import { NextRequest, NextResponse } from 'next/server'
import { resetAndSetupDatabase } from '../../../src/lib/db/reset-and-setup'
import { simpleSeedDatabase } from '../../../src/lib/db/simple-seed'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API: Database reset requested...')
    
    // Step 1: Reset and create database schema
    console.log('🔄 Resetting and creating database schema...')
    const schemaResult = await resetAndSetupDatabase()
    
    if (!schemaResult.success) {
      console.error('❌ API: Database schema reset failed:', schemaResult.message)
      return NextResponse.json({
        success: false,
        message: schemaResult.message,
        error: schemaResult.error,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    console.log('✅ API: Database schema reset successfully')

    // Wait a moment for the schema to be fully committed
    console.log('⏳ Waiting for schema to be committed...')
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Step 2: Seed database with data
    console.log('🔄 Seeding database with Ethiopian data...')
    const seedResult = await simpleSeedDatabase()
    
    if (!seedResult.success) {
      console.error('❌ API: Database seeding failed:', seedResult.message)
      return NextResponse.json({
        success: false,
        message: `Schema reset but seeding failed: ${seedResult.message}`,
        schemaSuccess: true,
        seedSuccess: false,
        error: seedResult.error,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    console.log('✅ API: Database seeded successfully')

    return NextResponse.json({
      success: true,
      message: 'Database reset and setup completed successfully',
      schemaSuccess: true,
      seedSuccess: true,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ API: Database reset failed:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Database reset failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Database reset API is ready. Use POST to reset the database.',
    timestamp: new Date().toISOString()
  })
}
