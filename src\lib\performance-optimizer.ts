/**
 * Performance optimization utilities for the EEU Transformer Management System
 */

import { cache } from './cache'

// Performance monitoring
interface PerformanceMetrics {
  componentRenderTime: number
  apiResponseTime: number
  bundleSize: number
  memoryUsage: number
  cacheHitRate: number
}

class PerformanceOptimizer {
  private metrics: PerformanceMetrics = {
    componentRenderTime: 0,
    apiResponseTime: 0,
    bundleSize: 0,
    memoryUsage: 0,
    cacheHitRate: 0
  }

  // Debounce function for search inputs
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(this, args), wait)
    }
  }

  // Throttle function for scroll events
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  // Memoization for expensive calculations
  memoize<T extends (...args: any[]) => any>(fn: T): T {
    const cache = new Map()
    return ((...args: any[]) => {
      const key = JSON.stringify(args)
      if (cache.has(key)) {
        return cache.get(key)
      }
      const result = fn(...args)
      cache.set(key, result)
      return result
    }) as T
  }

  // Virtual scrolling for large lists
  calculateVisibleItems(
    containerHeight: number,
    itemHeight: number,
    scrollTop: number,
    totalItems: number,
    overscan: number = 5
  ) {
    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(totalItems - 1, startIndex + visibleCount + overscan * 2)
    
    return {
      startIndex,
      endIndex,
      visibleCount,
      offsetY: startIndex * itemHeight
    }
  }

  // Image lazy loading
  createIntersectionObserver(
    callback: (entries: IntersectionObserverEntry[]) => void,
    options: IntersectionObserverInit = {}
  ): IntersectionObserver {
    const defaultOptions = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    }

    return new IntersectionObserver(callback, defaultOptions)
  }

  // Bundle size analysis
  analyzeBundleSize(): Promise<{ size: number; gzipSize: number }> {
    return new Promise((resolve) => {
      // Simulate bundle analysis
      const size = Math.random() * 1000000 // Random size in bytes
      const gzipSize = size * 0.3 // Approximate gzip compression
      
      this.metrics.bundleSize = size
      
      resolve({ size, gzipSize })
    })
  }

  // Memory usage monitoring
  getMemoryUsage(): MemoryInfo | null {
    if ('memory' in performance) {
      return (performance as any).memory
    }
    return null
  }

  // API response caching
  async cachedApiCall<T>(
    key: string,
    apiCall: () => Promise<T>,
    ttl: number = 5 * 60 * 1000 // 5 minutes default
  ): Promise<T> {
    const startTime = performance.now()
    
    // Check cache first
    const cached = cache.get<T>(key)
    if (cached) {
      this.updateCacheHitRate(true)
      return cached
    }

    // Make API call
    try {
      const result = await apiCall()
      cache.set(key, result, ttl)
      
      const endTime = performance.now()
      this.metrics.apiResponseTime = endTime - startTime
      this.updateCacheHitRate(false)
      
      return result
    } catch (error) {
      const endTime = performance.now()
      this.metrics.apiResponseTime = endTime - startTime
      this.updateCacheHitRate(false)
      throw error
    }
  }

  // Component render time tracking
  measureRenderTime<T>(
    componentName: string,
    renderFunction: () => T
  ): T {
    const startTime = performance.now()
    const result = renderFunction()
    const endTime = performance.now()
    
    const renderTime = endTime - startTime
    this.metrics.componentRenderTime = renderTime
    
    // Log slow renders
    if (renderTime > 16) { // More than one frame at 60fps
      console.warn(`Slow render detected for ${componentName}: ${renderTime.toFixed(2)}ms`)
    }
    
    return result
  }

  // Preload critical resources
  preloadResource(url: string, type: 'script' | 'style' | 'image' | 'fetch'): void {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = url
    
    switch (type) {
      case 'script':
        link.as = 'script'
        break
      case 'style':
        link.as = 'style'
        break
      case 'image':
        link.as = 'image'
        break
      case 'fetch':
        link.as = 'fetch'
        link.crossOrigin = 'anonymous'
        break
    }
    
    document.head.appendChild(link)
  }

  // Service Worker registration for caching
  async registerServiceWorker(swPath: string = '/sw.js'): Promise<ServiceWorkerRegistration | null> {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register(swPath)
        console.log('Service Worker registered successfully:', registration)
        return registration
      } catch (error) {
        console.error('Service Worker registration failed:', error)
        return null
      }
    }
    return null
  }

  // Critical CSS inlining
  inlineCriticalCSS(css: string): void {
    const style = document.createElement('style')
    style.textContent = css
    document.head.appendChild(style)
  }

  // Resource hints
  addResourceHints(urls: string[], type: 'dns-prefetch' | 'preconnect'): void {
    urls.forEach(url => {
      const link = document.createElement('link')
      link.rel = type
      link.href = url
      document.head.appendChild(link)
    })
  }

  // Performance metrics collection
  private updateCacheHitRate(hit: boolean): void {
    // Simple cache hit rate calculation
    const currentRate = this.metrics.cacheHitRate
    this.metrics.cacheHitRate = hit ? 
      Math.min(1, currentRate + 0.01) : 
      Math.max(0, currentRate - 0.01)
  }

  // Get current performance metrics
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  // Performance report
  generatePerformanceReport(): {
    metrics: PerformanceMetrics
    recommendations: string[]
    score: number
  } {
    const recommendations: string[] = []
    let score = 100

    // Analyze metrics and provide recommendations
    if (this.metrics.componentRenderTime > 16) {
      recommendations.push('Consider optimizing component renders - some components are taking longer than 16ms')
      score -= 10
    }

    if (this.metrics.apiResponseTime > 1000) {
      recommendations.push('API responses are slow - consider implementing caching or optimizing backend')
      score -= 15
    }

    if (this.metrics.bundleSize > 1000000) { // 1MB
      recommendations.push('Bundle size is large - consider code splitting and tree shaking')
      score -= 10
    }

    if (this.metrics.cacheHitRate < 0.7) {
      recommendations.push('Cache hit rate is low - review caching strategy')
      score -= 5
    }

    const memoryInfo = this.getMemoryUsage()
    if (memoryInfo && memoryInfo.usedJSHeapSize > 50000000) { // 50MB
      recommendations.push('High memory usage detected - check for memory leaks')
      score -= 10
    }

    return {
      metrics: this.metrics,
      recommendations,
      score: Math.max(0, score)
    }
  }

  // Cleanup function
  cleanup(): void {
    // Clear any timers, observers, etc.
    cache.clear()
  }
}

// Export singleton instance
export const performanceOptimizer = new PerformanceOptimizer()

// Export class for testing
export { PerformanceOptimizer }

// Utility functions
export const withPerformanceTracking = <T extends (...args: any[]) => any>(
  fn: T,
  name: string
): T => {
  return ((...args: any[]) => {
    return performanceOptimizer.measureRenderTime(name, () => fn(...args))
  }) as T
}

export const createOptimizedSelector = <T, R>(
  selector: (state: T) => R
): (state: T) => R => {
  return performanceOptimizer.memoize(selector)
}

// React hooks for performance optimization
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value)

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

export const useThrottle = <T>(value: T, limit: number): T => {
  const [throttledValue, setThrottledValue] = React.useState<T>(value)
  const lastRan = React.useRef(Date.now())

  React.useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value)
        lastRan.current = Date.now()
      }
    }, limit - (Date.now() - lastRan.current))

    return () => {
      clearTimeout(handler)
    }
  }, [value, limit])

  return throttledValue
}

// Import React for hooks
import React from 'react'
