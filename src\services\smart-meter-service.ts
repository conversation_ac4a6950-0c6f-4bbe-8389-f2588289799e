"use client"

import { SmartMeter, SmartMeterAlert, SmartMeterStatistics, SmartMeterUsageData } from "@/src/types/smart-meter"

// Generate random date within a range
const randomDate = (start: Date, end: Date): string => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString().split('T')[0]
}

// Generate random readings for the past 30 days
const generateReadings = (meterId: string, avgConsumption: number) => {
  const readings = []
  const now = new Date()
  
  for (let i = 30; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)
    
    // Morning reading
    readings.push({
      timestamp: `${date.toISOString().split('T')[0]}T08:00:00Z`,
      value: avgConsumption * 0.4 * (0.9 + Math.random() * 0.2),
      peak: false
    })
    
    // Evening peak reading
    readings.push({
      timestamp: `${date.toISOString().split('T')[0]}T19:00:00Z`,
      value: avgConsumption * 0.6 * (0.9 + Math.random() * 0.2),
      peak: true
    })
  }
  
  return readings
}

// Generate random alerts
const generateAlerts = (meterId: string, count: number): SmartMeterAlert[] => {
  const alertTypes: SmartMeterAlert["type"][] = ["Disconnection", "Tampering", "LowBattery", "CommunicationFailure", "HighConsumption", "LowConsumption"]
  const severities: SmartMeterAlert["severity"][] = ["Low", "Medium", "High", "Critical"]
  const statuses: SmartMeterAlert["status"][] = ["New", "Acknowledged", "Resolved", "Ignored"]
  
  const alerts: SmartMeterAlert[] = []
  
  for (let i = 0; i < count; i++) {
    const type = alertTypes[Math.floor(Math.random() * alertTypes.length)]
    const severity = severities[Math.floor(Math.random() * severities.length)]
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    
    alerts.push({
      id: `alert-${meterId}-${i}`,
      meterId,
      timestamp: randomDate(new Date(2023, 0, 1), new Date()),
      type,
      severity,
      message: `${type} detected on meter ${meterId}`,
      status,
      resolvedAt: status === "Resolved" ? randomDate(new Date(2023, 0, 1), new Date()) : undefined,
      resolvedBy: status === "Resolved" ? "System" : undefined,
      notes: Math.random() > 0.7 ? "Additional notes about this alert" : undefined
    })
  }
  
  return alerts
}

// Mock data for smart meters
const mockSmartMeters: SmartMeter[] = [
  {
    id: "SM-10245",
    serialNumber: "ETH-SM-10245",
    manufacturer: "Landis+Gyr",
    model: "E350",
    type: "Residential",
    installDate: "2024-01-15",
    lastReading: "2024-04-27T08:00:00Z",
    lastReadingValue: 14.2,
    lastCommunication: "2024-04-27T08:05:23Z",
    firmwareVersion: "3.2.1",
    status: "Connected",
    location: {
      latitude: 9.0222,
      longitude: 38.7468,
      region: "Addis Ababa",
      serviceCenter: "Bole",
      address: "Bole Road, Addis Ababa",
      installationSite: "Residential Building"
    },
    customer: {
      id: "CUST-5678",
      name: "Abebe Kebede",
      accountNumber: "ACC-12345",
      contactPhone: "+************",
      email: "<EMAIL>"
    },
    tariffPlan: "Residential Standard",
    billingCycle: "Monthly",
    prepaid: true,
    balance: 250.75,
    batteryLevel: 92,
    signalStrength: 85
  },
  {
    id: "SM-10872",
    serialNumber: "ETH-SM-10872",
    manufacturer: "Itron",
    model: "OpenWay Riva",
    type: "Commercial",
    installDate: "2024-03-22",
    lastReading: "2024-04-27T08:00:00Z",
    lastReadingValue: 45.8,
    lastCommunication: "2024-04-27T08:10:15Z",
    firmwareVersion: "2.5.0",
    status: "Connected",
    location: {
      latitude: 9.6000,
      longitude: 41.8500,
      region: "Dire Dawa",
      serviceCenter: "Central",
      address: "Main Street, Dire Dawa",
      installationSite: "Commercial Complex"
    },
    customer: {
      id: "CUST-8901",
      name: "Dire Dawa Shopping Center",
      accountNumber: "ACC-67890",
      contactPhone: "+************",
      email: "<EMAIL>"
    },
    tariffPlan: "Commercial Premium",
    billingCycle: "Monthly",
    prepaid: false,
    batteryLevel: 88,
    signalStrength: 90
  },
  {
    id: "SM-11105",
    serialNumber: "ETH-SM-11105",
    manufacturer: "Schneider Electric",
    model: "ION7400",
    type: "Residential",
    installDate: "2024-02-10",
    lastReading: "2024-04-26T08:00:00Z",
    lastReadingValue: 8.7,
    lastCommunication: "2024-04-26T08:15:42Z",
    firmwareVersion: "1.9.3",
    status: "Disconnected",
    location: {
      latitude: 11.6000,
      longitude: 37.3900,
      region: "Amhara",
      serviceCenter: "Bahir Dar",
      address: "Lakeside Area, Bahir Dar",
      installationSite: "Residential Apartment"
    },
    customer: {
      id: "CUST-2345",
      name: "Tigist Alemu",
      accountNumber: "ACC-34567",
      contactPhone: "+************",
      email: "<EMAIL>"
    },
    tariffPlan: "Residential Basic",
    billingCycle: "Monthly",
    prepaid: true,
    balance: 0,
    batteryLevel: 45,
    signalStrength: 20
  },
  {
    id: "SM-11267",
    serialNumber: "ETH-SM-11267",
    manufacturer: "Siemens",
    model: "AMIS",
    type: "Industrial",
    installDate: "2023-11-05",
    lastReading: "2024-04-27T08:00:00Z",
    lastReadingValue: 132.5,
    lastCommunication: "2024-04-27T08:02:10Z",
    firmwareVersion: "4.1.2",
    status: "Connected",
    location: {
      latitude: 8.9806,
      longitude: 38.7578,
      region: "Oromia",
      serviceCenter: "Adama",
      address: "Industrial Zone, Adama",
      installationSite: "Manufacturing Plant"
    },
    customer: {
      id: "CUST-7890",
      name: "Ethiopian Textile Factory",
      accountNumber: "ACC-78901",
      contactPhone: "+************",
      email: "<EMAIL>"
    },
    tariffPlan: "Industrial High Usage",
    billingCycle: "Monthly",
    prepaid: false,
    batteryLevel: 95,
    signalStrength: 88
  },
  {
    id: "SM-11389",
    serialNumber: "ETH-SM-11389",
    manufacturer: "Landis+Gyr",
    model: "E650",
    type: "Commercial",
    installDate: "2023-12-18",
    lastReading: "2024-04-27T08:00:00Z",
    lastReadingValue: 28.3,
    lastCommunication: "2024-04-27T08:08:55Z",
    firmwareVersion: "3.0.2",
    status: "Connected",
    location: {
      latitude: 7.0500,
      longitude: 38.4700,
      region: "SNNPR",
      serviceCenter: "Hawassa",
      address: "Lake Road, Hawassa",
      installationSite: "Hotel Complex"
    },
    customer: {
      id: "CUST-3456",
      name: "Hawassa Resort Hotel",
      accountNumber: "ACC-45678",
      contactPhone: "+************",
      email: "<EMAIL>"
    },
    tariffPlan: "Commercial Standard",
    billingCycle: "Monthly",
    prepaid: false,
    batteryLevel: 90,
    signalStrength: 82
  },
  {
    id: "SM-11452",
    serialNumber: "ETH-SM-11452",
    manufacturer: "Itron",
    model: "OpenWay Riva",
    type: "Residential",
    installDate: "2024-01-30",
    lastReading: "2024-04-25T08:00:00Z",
    lastReadingValue: 0,
    lastCommunication: "2024-04-25T08:20:30Z",
    firmwareVersion: "2.5.0",
    status: "Maintenance",
    location: {
      latitude: 13.4900,
      longitude: 39.4700,
      region: "Tigray",
      serviceCenter: "Mekelle",
      address: "Central District, Mekelle",
      installationSite: "Residential Building"
    },
    customer: {
      id: "CUST-9012",
      name: "Yohannes Tesfaye",
      accountNumber: "ACC-90123",
      contactPhone: "+************",
      email: "<EMAIL>"
    },
    tariffPlan: "Residential Standard",
    billingCycle: "Monthly",
    prepaid: true,
    balance: 150.25,
    batteryLevel: 75,
    signalStrength: 60
  },
  {
    id: "SM-11578",
    serialNumber: "ETH-SM-11578",
    manufacturer: "Schneider Electric",
    model: "ION8650",
    type: "Industrial",
    installDate: "2023-10-12",
    lastReading: "2024-04-27T08:00:00Z",
    lastReadingValue: 215.7,
    lastCommunication: "2024-04-27T08:01:45Z",
    firmwareVersion: "2.1.5",
    status: "Connected",
    location: {
      latitude: 9.3100,
      longitude: 42.1200,
      region: "Somali",
      serviceCenter: "Jijiga",
      address: "Industrial Area, Jijiga",
      installationSite: "Processing Plant"
    },
    customer: {
      id: "CUST-4567",
      name: "Eastern Food Processing",
      accountNumber: "ACC-56789",
      contactPhone: "+************",
      email: "<EMAIL>"
    },
    tariffPlan: "Industrial Standard",
    billingCycle: "Monthly",
    prepaid: false,
    batteryLevel: 92,
    signalStrength: 78
  },
  {
    id: "SM-11624",
    serialNumber: "ETH-SM-11624",
    manufacturer: "Siemens",
    model: "AMIS",
    type: "Residential",
    installDate: "2024-02-25",
    lastReading: "2024-04-27T08:00:00Z",
    lastReadingValue: 10.2,
    lastCommunication: "2024-04-27T08:12:20Z",
    firmwareVersion: "4.0.1",
    status: "Connected",
    location: {
      latitude: 9.0222,
      longitude: 38.7468,
      region: "Addis Ababa",
      serviceCenter: "Bole",
      address: "Airport Road, Addis Ababa",
      installationSite: "Residential Apartment"
    },
    customer: {
      id: "CUST-5678",
      name: "Meron Tadesse",
      accountNumber: "ACC-67890",
      contactPhone: "+************",
      email: "<EMAIL>"
    },
    tariffPlan: "Residential Premium",
    billingCycle: "Monthly",
    prepaid: true,
    balance: 320.50,
    batteryLevel: 88,
    signalStrength: 92
  },
  {
    id: "SM-11742",
    serialNumber: "ETH-SM-11742",
    manufacturer: "Landis+Gyr",
    model: "E350",
    type: "Commercial",
    installDate: "2023-12-05",
    lastReading: "2024-04-26T08:00:00Z",
    lastReadingValue: 0,
    lastCommunication: "2024-04-26T08:05:10Z",
    firmwareVersion: "3.2.1",
    status: "Tampered",
    location: {
      latitude: 11.8000,
      longitude: 41.7400,
      region: "Afar",
      serviceCenter: "Semera",
      address: "Main Street, Semera",
      installationSite: "Commercial Building"
    },
    customer: {
      id: "CUST-6789",
      name: "Afar Business Center",
      accountNumber: "ACC-78901",
      contactPhone: "+************",
      email: "<EMAIL>"
    },
    tariffPlan: "Commercial Basic",
    billingCycle: "Monthly",
    prepaid: false,
    batteryLevel: 65,
    signalStrength: 50
  },
  {
    id: "SM-11865",
    serialNumber: "ETH-SM-11865",
    manufacturer: "Itron",
    model: "OpenWay Riva",
    type: "Residential",
    installDate: "2024-01-20",
    lastReading: "2024-04-27T08:00:00Z",
    lastReadingValue: 12.8,
    lastCommunication: "2024-04-27T08:18:30Z",
    firmwareVersion: "2.5.0",
    status: "Connected",
    location: {
      latitude: 7.6800,
      longitude: 36.8300,
      region: "Oromia",
      serviceCenter: "Jimma",
      address: "Residential Area, Jimma",
      installationSite: "Residential Building"
    },
    customer: {
      id: "CUST-7890",
      name: "Dawit Bekele",
      accountNumber: "ACC-89012",
      contactPhone: "+************",
      email: "<EMAIL>"
    },
    tariffPlan: "Residential Standard",
    billingCycle: "Monthly",
    prepaid: true,
    balance: 180.75,
    batteryLevel: 90,
    signalStrength: 85
  }
]

// Add readings and alerts to each meter
mockSmartMeters.forEach(meter => {
  const avgConsumption = meter.type === "Residential" ? 15 : meter.type === "Commercial" ? 50 : 200
  meter.readings = generateReadings(meter.id, avgConsumption)
  
  const alertCount = Math.floor(Math.random() * 5) // 0-4 alerts per meter
  meter.alerts = generateAlerts(meter.id, alertCount)
})

// Mock usage data for charts
const mockUsageData: SmartMeterUsageData[] = [
  { timestamp: "2024-01", residential: 320, commercial: 580, industrial: 920 },
  { timestamp: "2024-02", residential: 340, commercial: 610, industrial: 880 },
  { timestamp: "2024-03", residential: 360, commercial: 590, industrial: 950 },
  { timestamp: "2024-04", residential: 380, commercial: 620, industrial: 980 },
  { timestamp: "2024-05", residential: 400, commercial: 650, industrial: 1020 },
  { timestamp: "2024-06", residential: 450, commercial: 680, industrial: 1050 }
]

// Mock daily usage data for the last 30 days
const mockDailyUsageData: SmartMeterUsageData[] = Array(30).fill(null).map((_, i) => {
  const date = new Date()
  date.setDate(date.getDate() - (29 - i))
  
  return {
    timestamp: date.toISOString().split('T')[0],
    residential: 300 + Math.random() * 100,
    commercial: 550 + Math.random() * 150,
    industrial: 850 + Math.random() * 200
  }
})

// Smart meter service class
class SmartMeterService {
  // Get all smart meters
  async getAllSmartMeters(): Promise<SmartMeter[]> {
    return [...mockSmartMeters]
  }
  
  // Get smart meter by ID
  async getSmartMeterById(id: string): Promise<SmartMeter | null> {
    const meter = mockSmartMeters.find(m => m.id === id)
    return meter ? { ...meter } : null
  }
  
  // Search smart meters by query
  async searchSmartMeters(query: string): Promise<SmartMeter[]> {
    if (!query) return [...mockSmartMeters]
    
    const lowerQuery = query.toLowerCase()
    return mockSmartMeters.filter(m => 
      m.id.toLowerCase().includes(lowerQuery) ||
      m.serialNumber.toLowerCase().includes(lowerQuery) ||
      m.customer.name.toLowerCase().includes(lowerQuery) ||
      m.location.address.toLowerCase().includes(lowerQuery) ||
      m.location.region.toLowerCase().includes(lowerQuery)
    )
  }
  
  // Filter smart meters by criteria
  async filterSmartMeters(criteria: {
    status?: string,
    type?: string,
    region?: string,
    manufacturer?: string
  }): Promise<SmartMeter[]> {
    let filtered = [...mockSmartMeters]
    
    if (criteria.status && criteria.status !== "all") {
      filtered = filtered.filter(m => m.status === criteria.status)
    }
    
    if (criteria.type && criteria.type !== "all") {
      filtered = filtered.filter(m => m.type === criteria.type)
    }
    
    if (criteria.region && criteria.region !== "all") {
      filtered = filtered.filter(m => m.location.region === criteria.region)
    }
    
    if (criteria.manufacturer && criteria.manufacturer !== "all") {
      filtered = filtered.filter(m => m.manufacturer === criteria.manufacturer)
    }
    
    return filtered
  }
  
  // Get smart meter statistics
  async getSmartMeterStatistics(): Promise<SmartMeterStatistics> {
    // Count meters by status
    const byStatus: Record<string, number> = {}
    mockSmartMeters.forEach(meter => {
      byStatus[meter.status] = (byStatus[meter.status] || 0) + 1
    })
    
    // Count meters by region
    const byRegion: Record<string, number> = {}
    mockSmartMeters.forEach(meter => {
      byRegion[meter.location.region] = (byRegion[meter.location.region] || 0) + 1
    })
    
    // Count meters by type
    const byType: Record<string, number> = {}
    mockSmartMeters.forEach(meter => {
      byType[meter.type] = (byType[meter.type] || 0) + 1
    })
    
    // Count meters by manufacturer
    const byManufacturer: Record<string, number> = {}
    mockSmartMeters.forEach(meter => {
      byManufacturer[meter.manufacturer] = (byManufacturer[meter.manufacturer] || 0) + 1
    })
    
    // Count alerts
    const alerts = {
      total: 0,
      byType: {} as Record<string, number>,
      bySeverity: {} as Record<string, number>,
      byStatus: {} as Record<string, number>
    }
    
    mockSmartMeters.forEach(meter => {
      if (meter.alerts) {
        alerts.total += meter.alerts.length
        
        meter.alerts.forEach(alert => {
          alerts.byType[alert.type] = (alerts.byType[alert.type] || 0) + 1
          alerts.bySeverity[alert.severity] = (alerts.bySeverity[alert.severity] || 0) + 1
          alerts.byStatus[alert.status] = (alerts.byStatus[alert.status] || 0) + 1
        })
      }
    })
    
    // Calculate consumption
    const consumption = {
      daily: 0,
      weekly: 0,
      monthly: 0,
      byRegion: {} as Record<string, number>,
      byType: {} as Record<string, number>
    }
    
    // Calculate average daily consumption
    mockSmartMeters.forEach(meter => {
      consumption.daily += meter.lastReadingValue
      
      // Aggregate by region
      consumption.byRegion[meter.location.region] = (consumption.byRegion[meter.location.region] || 0) + meter.lastReadingValue
      
      // Aggregate by type
      consumption.byType[meter.type] = (consumption.byType[meter.type] || 0) + meter.lastReadingValue
    })
    
    // Estimate weekly and monthly from daily
    consumption.weekly = consumption.daily * 7
    consumption.monthly = consumption.daily * 30
    
    return {
      total: mockSmartMeters.length,
      byStatus,
      byRegion,
      byType,
      byManufacturer,
      alerts,
      consumption
    }
  }
  
  // Get usage data for charts
  async getUsageData(timeframe: "daily" | "monthly" = "monthly"): Promise<SmartMeterUsageData[]> {
    return timeframe === "daily" ? [...mockDailyUsageData] : [...mockUsageData]
  }
  
  // Get all alerts
  async getAllAlerts(): Promise<SmartMeterAlert[]> {
    const allAlerts: SmartMeterAlert[] = []
    
    mockSmartMeters.forEach(meter => {
      if (meter.alerts) {
        allAlerts.push(...meter.alerts)
      }
    })
    
    // Sort by timestamp, newest first
    return allAlerts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  }
  
  // Get alerts by meter ID
  async getAlertsByMeterId(meterId: string): Promise<SmartMeterAlert[]> {
    const meter = mockSmartMeters.find(m => m.id === meterId)
    return meter?.alerts || []
  }
  
  // Filter alerts by criteria
  async filterAlerts(criteria: {
    severity?: string,
    type?: string,
    status?: string,
    meterId?: string
  }): Promise<SmartMeterAlert[]> {
    let allAlerts: SmartMeterAlert[] = []
    
    mockSmartMeters.forEach(meter => {
      if (meter.alerts) {
        allAlerts.push(...meter.alerts)
      }
    })
    
    if (criteria.meterId) {
      allAlerts = allAlerts.filter(alert => alert.meterId === criteria.meterId)
    }
    
    if (criteria.severity && criteria.severity !== "all") {
      allAlerts = allAlerts.filter(alert => alert.severity === criteria.severity)
    }
    
    if (criteria.type && criteria.type !== "all") {
      allAlerts = allAlerts.filter(alert => alert.type === criteria.type)
    }
    
    if (criteria.status && criteria.status !== "all") {
      allAlerts = allAlerts.filter(alert => alert.status === criteria.status)
    }
    
    // Sort by timestamp, newest first
    return allAlerts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  }
  
  // Update smart meter
  async updateSmartMeter(id: string, updates: Partial<SmartMeter>): Promise<SmartMeter | null> {
    const index = mockSmartMeters.findIndex(m => m.id === id)
    if (index === -1) return null
    
    // Update the meter
    mockSmartMeters[index] = {
      ...mockSmartMeters[index],
      ...updates
    }
    
    return { ...mockSmartMeters[index] }
  }
  
  // Add new smart meter
  async addSmartMeter(meter: Omit<SmartMeter, "id">): Promise<SmartMeter> {
    const newId = `SM-${10000 + mockSmartMeters.length}`
    
    const newMeter: SmartMeter = {
      id: newId,
      ...meter
    }
    
    mockSmartMeters.push(newMeter)
    return { ...newMeter }
  }
}

export const smartMeterService = new SmartMeterService()
