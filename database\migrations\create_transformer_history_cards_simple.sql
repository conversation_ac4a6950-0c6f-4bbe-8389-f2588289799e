-- Create transformer_history_cards table (simplified without foreign keys)
CREATE TABLE IF NOT EXISTS transformer_history_cards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transformer_id VARCHAR(50),
    card_no VARCHAR(50) NOT NULL UNIQUE,
    substation_name VA<PERSON>HAR(100) NOT NULL,
    feeder_name VA<PERSON>HAR(100),
    transformer_code VARCHAR(50) NOT NULL,
    kva_rating DECIMAL(10,2) NOT NULL,
    primary_voltage VARCHAR(20),
    region VARCHAR(50),
    district VARCHAR(100),
    specific_location TEXT,
    gps_location VARCHAR(50),
    manufacturer VARCHAR(100),
    year_of_manufacturing YEAR,
    serial_no VARCHAR(100),
    installation_date DATE,
    changing_date DATE,
    changing_reason TEXT,
    customer_type VARCHAR(50),
    customer_name VARCHAR(100),
    sub_city VARCHAR(100),
    kebele VARCHAR(100),
    construction_type VARCHAR(50),
    delivery_date DATE,
    responsible_person VARCHAR(100),
    signature VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    INDEX idx_transformer_id (transformer_id),
    INDEX idx_card_no (card_no),
    INDEX idx_region (region),
    INDEX idx_substation (substation_name)
);

-- Create transformer_inspections table
CREATE TABLE IF NOT EXISTS transformer_inspections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    history_card_id INT NOT NULL,
    inspection_date DATE NOT NULL,
    inspector_name VARCHAR(100),
    arrestor_status ENUM('OK', 'Faulty', 'N/A') DEFAULT 'N/A',
    dropout_fuse_status ENUM('OK', 'Faulty', 'N/A') DEFAULT 'N/A',
    bushing_status ENUM('OK', 'Faulty', 'N/A') DEFAULT 'N/A',
    ground_status ENUM('OK', 'Faulty', 'N/A') DEFAULT 'N/A',
    oil_level ENUM('Normal', 'Low', 'High', 'N/A') DEFAULT 'N/A',
    leakage_status ENUM('None', 'Minor', 'Major', 'N/A') DEFAULT 'N/A',
    cable_rating VARCHAR(50),
    transformer_load_percentage DECIMAL(5,2),
    remarks TEXT,
    inspector_signature VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_history_card_id (history_card_id),
    INDEX idx_inspection_date (inspection_date)
);

-- Create transformer_inspection_components table
CREATE TABLE IF NOT EXISTS transformer_inspection_components (
    id INT AUTO_INCREMENT PRIMARY KEY,
    history_card_id INT NOT NULL,
    component_type ENUM(
        'earth_resistance', 
        'insulation_resistance', 
        'oil_level', 
        'oil_temperature', 
        'unusual_noises', 
        'bushing_condition',
        'silica_gel_color',
        'lighting_arrestor',
        'pole_condition'
    ) NOT NULL,
    is_defective BOOLEAN DEFAULT FALSE,
    trafo_code VARCHAR(50),
    quantity INT DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_history_card_component (history_card_id, component_type)
);

-- Create transformer_megger_tests table
CREATE TABLE IF NOT EXISTS transformer_megger_tests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    history_card_id INT NOT NULL,
    test_date DATE NOT NULL,
    region VARCHAR(50),
    district VARCHAR(100),
    service_type ENUM('Residential', 'Commercial', 'Industrial', 'Agricultural'),
    work_order_no VARCHAR(100),
    specific_location TEXT,
    gps_location VARCHAR(50),
    manufacturer VARCHAR(100),
    voltage_level VARCHAR(20),
    capacity_kva DECIMAL(10,2),
    serial_no VARCHAR(100),
    
    -- Test Results
    rs_ohmic_value DECIMAL(10,3),
    ht_to_ground_value DECIMAL(10,3),
    lt_to_ground_value DECIMAL(10,3),
    oil_insulation_condition VARCHAR(100),
    
    -- Box measurements
    box_one_cable_value DECIMAL(10,3),
    box_one_fuse_rating VARCHAR(20),
    box_two_cable_value DECIMAL(10,3),
    box_two_fuse_rating VARCHAR(20),
    linear_rating VARCHAR(20),
    oil_level_status VARCHAR(50),
    ground_connection_value DECIMAL(10,3),
    silica_gel_condition VARCHAR(50),
    insulation_test_value DECIMAL(10,3),
    
    -- Approval chain
    inspected_by VARCHAR(100),
    inspected_date DATE,
    checked_by VARCHAR(100),
    checked_date DATE,
    approved_by VARCHAR(100),
    approved_date DATE,
    
    -- Results and conclusions
    test_result ENUM('Pass', 'Fail', 'Warning') DEFAULT 'Pass',
    remarks TEXT,
    conclusions TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_history_card_test (history_card_id),
    INDEX idx_test_date (test_date),
    INDEX idx_test_result (test_result)
);

-- Create transformer_history_attachments table for storing related documents
CREATE TABLE IF NOT EXISTS transformer_history_attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    history_card_id INT NOT NULL,
    attachment_type ENUM('photo', 'document', 'test_report', 'inspection_report') NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    description TEXT,
    uploaded_by INT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_history_card_attachment (history_card_id),
    INDEX idx_attachment_type (attachment_type)
);

-- Insert sample data for testing
INSERT INTO transformer_history_cards (
    transformer_id, card_no, substation_name, feeder_name, transformer_code, 
    kva_rating, primary_voltage, region, district, specific_location, 
    gps_location, manufacturer, year_of_manufacturing, serial_no, 
    installation_date, customer_type, construction_type, responsible_person
) VALUES 
('1', 'HC-001-2024', 'Addis Ababa Central', 'Feeder-A1', 'TF-AA-001', 
 100.00, '15kV/400V', 'Addis Ababa', 'Addis Ketema', 'Near Merkato Market', 
 '9.0192,38.7525', 'ABB', 2023, 'ABB-TF-2023-001', 
 '2023-06-15', 'Commercial', 'Pole Mounted', 'Engineer Alemayehu Tadesse'),

('2', 'HC-002-2024', 'Bahir Dar Substation', 'Feeder-B2', 'TF-BD-002', 
 250.00, '15kV/400V', 'Amhara', 'Bahir Dar', 'Industrial Zone', 
 '11.5942,37.3906', 'Siemens', 2022, 'SIE-TF-2022-045', 
 '2022-09-20', 'Industrial', 'Ground Mounted', 'Engineer Belay Worku'),

('3', 'HC-003-2024', 'Hawassa Distribution', 'Feeder-C3', 'TF-HW-003', 
 50.00, '15kV/400V', 'SNNP', 'Hawassa', 'Residential Area', 
 '7.0621,38.4759', 'Schneider Electric', 2024, 'SCH-TF-2024-012', 
 '2024-01-10', 'Residential', 'Pole Mounted', 'Engineer Chaltu Bekele');

-- Insert sample inspection records
INSERT INTO transformer_inspections (
    history_card_id, inspection_date, inspector_name, arrestor_status, 
    dropout_fuse_status, bushing_status, ground_status, oil_level, 
    leakage_status, cable_rating, transformer_load_percentage, remarks
) VALUES 
(1, '2024-01-15', 'Technician Dawit Mekonnen', 'OK', 'OK', 'OK', 'OK', 'Normal', 'None', '100A', 75.5, 'All components in good condition'),
(1, '2024-04-15', 'Technician Meron Tadesse', 'OK', 'Faulty', 'OK', 'OK', 'Low', 'Minor', '100A', 82.3, 'Dropout fuse needs replacement, minor oil leak detected'),
(2, '2024-02-10', 'Technician Getachew Alemu', 'OK', 'OK', 'OK', 'OK', 'Normal', 'None', '250A', 68.2, 'Routine inspection - all systems normal'),
(3, '2024-03-05', 'Technician Sara Haile', 'OK', 'OK', 'Faulty', 'OK', 'Normal', 'None', '50A', 45.8, 'Bushing shows signs of wear, schedule replacement');

-- Insert sample megger test results
INSERT INTO transformer_megger_tests (
    history_card_id, test_date, region, district, service_type, work_order_no,
    specific_location, manufacturer, voltage_level, capacity_kva, serial_no,
    rs_ohmic_value, ht_to_ground_value, lt_to_ground_value, oil_insulation_condition,
    inspected_by, test_result, remarks
) VALUES 
(1, '2024-01-20', 'Addis Ababa', 'Addis Ketema', 'Commercial', 'WO-2024-001',
 'Near Merkato Market', 'ABB', '15kV/400V', 100.00, 'ABB-TF-2023-001',
 500.250, 1000.500, 800.750, 'Good condition, clear oil',
 'Test Engineer Mulugeta Assefa', 'Pass', 'All insulation values within acceptable limits'),

(2, '2024-02-15', 'Amhara', 'Bahir Dar', 'Industrial', 'WO-2024-002',
 'Industrial Zone', 'Siemens', '15kV/400V', 250.00, 'SIE-TF-2022-045',
 450.125, 950.300, 750.600, 'Slight discoloration, acceptable',
 'Test Engineer Yohannes Bekele', 'Pass', 'Minor oil aging detected, monitor closely'),

(3, '2024-03-10', 'SNNP', 'Hawassa', 'Residential', 'WO-2024-003',
 'Residential Area', 'Schneider Electric', '15kV/400V', 50.00, 'SCH-TF-2024-012',
 600.800, 1200.900, 900.450, 'Excellent condition, new transformer',
 'Test Engineer Tigist Alemayehu', 'Pass', 'New installation - all parameters excellent');
