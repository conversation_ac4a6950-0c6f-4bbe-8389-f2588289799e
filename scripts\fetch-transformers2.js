// Fetch and display all data from app_transformers2
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    const [rows] = await connection.execute('SELECT * FROM app_transformers2');
    console.log('Transformers2:', rows);
  } catch (err) {
    console.error('❌ Error fetching app_transformers2:', err);
  } finally {
    await connection.end();
  }
})();
