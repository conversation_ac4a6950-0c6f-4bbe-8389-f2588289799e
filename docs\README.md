# 🔌 EEU Transformer Management System

> **Ethiopian Electric Utility - Digital Transformer Management System**  
> A modern, comprehensive solution for managing electrical transformers across Ethiopia's power grid.

## ✨ **Overview**

The EEU Transformer Management System is a state-of-the-art web application designed to streamline the management, monitoring, and maintenance of electrical transformers for the Ethiopian Electric Utility. Built with modern technologies and best practices, it provides real-time insights, predictive maintenance capabilities, and comprehensive reporting.

## 🎯 **Key Features**

### 📊 **Dashboard & Analytics**
- Real-time transformer status monitoring
- Interactive maps with geographic distribution
- Performance metrics and KPI tracking
- Customizable widgets and layouts

### 🔧 **Transformer Management**
- Complete transformer inventory management
- Detailed specifications and documentation
- Status tracking and health monitoring
- Location-based organization

### 🛠️ **Maintenance System**
- Scheduled maintenance planning
- Work order management
- Maintenance history tracking
- Predictive maintenance alerts

### 👥 **User Management**
- Role-based access control
- Regional and service center assignments
- Activity logging and audit trails
- Comprehensive permission system

### 📈 **Reporting & Analytics**
- Custom report generation
- PDF export capabilities
- Data visualization and charts
- Performance trend analysis

### 🗺️ **Geographic Features**
- Interactive transformer maps
- Location-based filtering
- Regional distribution views
- Service center management

## 🏗️ **Architecture**

### **Technology Stack**
- **Frontend**: Next.js 14 with App Router
- **UI Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with custom themes
- **Components**: Radix UI + shadcn/ui
- **Database**: SQLite with Better-SQLite3
- **Maps**: Leaflet with React-Leaflet
- **Charts**: Recharts for data visualization
- **Forms**: React Hook Form with Zod validation

### **Project Structure**
```
eeu-transformer-management/
├── 📁 app/                    # Next.js App Router pages
├── 📁 components/             # Reusable UI components
├── 📁 contexts/              # React contexts
├── 📁 hooks/                 # Custom React hooks
├── 📁 lib/                   # Core libraries and utilities
├── 📁 services/              # Business logic and API services
├── 📁 types/                 # TypeScript type definitions
├── 📁 data/                  # Static data files
├── 📁 public/                # Static assets
├── 📁 scripts/               # Build and utility scripts
└── 📁 src/                   # Modern organized source code
    ├── 📁 components/        # Reusable UI components
    ├── 📁 features/          # Feature-based modules
    ├── 📁 shared/            # Shared utilities and services
    └── 📁 styles/            # Global styles and themes
```

## 🚀 **Getting Started**

### **Prerequisites**
- Node.js 18+ 
- npm or pnpm
- Git

### **Installation**

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd eeu-transformer-management
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Initialize the database**
   ```bash
   npm run db:init
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open the application**
   ```
   http://localhost:3002
   ```

### **Default Login Credentials**
- **Email**: `<EMAIL>`
- **Password**: `admin123`

## 🛠️ **Development**

### **Available Scripts**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run type-check   # TypeScript type checking
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage
```

### **Database Management**
```bash
npm run db:init       # Initialize database with sample data
npm run db:init:force # Force reinitialize database
```

## 📱 **Features Overview**

### **Dashboard**
- **Real-time Metrics**: Live transformer status and performance data
- **Interactive Maps**: Geographic distribution of transformers
- **Quick Actions**: Fast access to common tasks
- **System Health**: Overall network health monitoring

### **Transformer Management**
- **Inventory**: Complete transformer database
- **Specifications**: Detailed technical information
- **Location Tracking**: GPS coordinates and addresses
- **Status Monitoring**: Real-time operational status

### **Maintenance**
- **Scheduling**: Plan and schedule maintenance activities
- **Work Orders**: Create and track maintenance tasks
- **History**: Complete maintenance history
- **Alerts**: Automated maintenance reminders

### **User Management**
- **Role-Based Access**: Granular permission control
- **Regional Assignment**: Users assigned to specific regions
- **Activity Logging**: Track user actions and changes
- **Profile Management**: User profile and preferences

### **Reports**
- **Custom Reports**: Build custom reports with filters
- **PDF Export**: Export reports to PDF format
- **Data Visualization**: Charts and graphs
- **Scheduled Reports**: Automated report generation

## 🎨 **Customization**

The system includes comprehensive customization options:

- **Themes**: Light and dark mode support
- **Branding**: Customizable logos and colors
- **Layout**: Configurable sidebar and header
- **Components**: Customizable UI components
- **Dashboard**: Configurable widgets and layouts

## 🔒 **Security**

- **Authentication**: Secure login system
- **Authorization**: Role-based access control
- **Data Protection**: Encrypted sensitive data
- **Audit Logging**: Complete activity tracking
- **Session Management**: Secure session handling

## 📊 **Performance**

- **Fast Loading**: Optimized for quick page loads
- **Responsive Design**: Works on all device sizes
- **Efficient Caching**: Smart data caching strategies
- **Code Splitting**: Lazy loading for better performance

## 🌍 **Localization**

- **Multi-language Support**: English and Amharic
- **Regional Settings**: Ethiopian-specific configurations
- **Date/Time Formats**: Local date and time formats
- **Currency**: Ethiopian Birr (ETB) support

## 📞 **Support**

For technical support or questions:

- **Email**: <EMAIL>
- **Phone**: +251-11-661-6000
- **Documentation**: Available in the app under Help section

## 👨‍💻 **Development Team**

**Lead Developer**: Worku Mesafint  
**Email**: <EMAIL>  
**GitHub**: [@workubest](https://github.com/workubest)

## 📄 **License**

This software is proprietary to the Ethiopian Electric Utility and is intended for internal use only. All rights reserved.

---

**© 2024 Ethiopian Electric Utility. All rights reserved.**
