"use client"

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON>2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>dingUp,
  Zap,
  Percent,
  Calendar,
  Map,
  Download,
  RefreshCw,
  Filter,
  ChevronDown,
  Search,
  Settings,
  FileText,
  Share2
} from 'lucide-react'
import { TransformerLayout } from '@/components/transformer/TransformerLayout'
import { ProtectedRoute } from "@/src/components/layout/protected-route"
import ProvidersWrapper from "@/components/providers-wrapper"
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/src/components/ui/tabs'

export default function TransformerAnalysisDashboardPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [timeRange, setTimeRange] = useState('month')
  const [regionFilter, setRegionFilter] = useState('all')
  const [analysisData, setAnalysisData] = useState({
    overview: {
      totalTransformers: 135,
      averageEfficiency: 87.5,
      averageAge: 4.2,
      averageHealthIndex: 78.3,
      totalFailures: 8,
      failureRate: 5.9
    },
    byRegion: [
      { name: 'Addis Ababa', count: 42, efficiency: 89.2, healthIndex: 82.1, failures: 2 },
      { name: 'Oromia', count: 35, efficiency: 86.8, healthIndex: 76.5, failures: 3 },
      { name: 'Amhara', count: 28, efficiency: 85.3, healthIndex: 75.2, failures: 2 },
      { name: 'SNNPR', count: 20, efficiency: 84.7, healthIndex: 74.8, failures: 1 },
      { name: 'Tigray', count: 10, efficiency: 88.1, healthIndex: 79.5, failures: 0 }
    ],
    byManufacturer: [
      { name: 'ABB', count: 48, efficiency: 89.5, healthIndex: 83.2, failures: 2 },
      { name: 'Siemens', count: 42, efficiency: 88.7, healthIndex: 81.5, failures: 2 },
      { name: 'Schneider Electric', count: 35, efficiency: 86.2, healthIndex: 77.8, failures: 3 },
      { name: 'General Electric', count: 10, efficiency: 85.8, healthIndex: 76.3, failures: 1 }
    ],
    byAge: [
      { range: '0-2 years', count: 35, efficiency: 91.2, healthIndex: 88.5, failures: 0 },
      { range: '2-5 years', count: 48, efficiency: 88.7, healthIndex: 82.3, failures: 2 },
      { range: '5-10 years', count: 32, efficiency: 85.4, healthIndex: 75.8, failures: 3 },
      { range: '10+ years', count: 20, efficiency: 82.1, healthIndex: 68.2, failures: 3 }
    ],
    failureCauses: [
      { cause: 'Overloading', count: 3, percentage: 37.5 },
      { cause: 'Oil Leakage', count: 2, percentage: 25 },
      { cause: 'Lightning Strike', count: 1, percentage: 12.5 },
      { cause: 'Bushing Failure', count: 1, percentage: 12.5 },
      { cause: 'Other', count: 1, percentage: 12.5 }
    ],
    trends: {
      efficiency: [
        { month: 'Jan', value: 86.2 },
        { month: 'Feb', value: 86.5 },
        { month: 'Mar', value: 86.8 },
        { month: 'Apr', value: 87.1 },
        { month: 'May', value: 87.3 },
        { month: 'Jun', value: 87.5 }
      ],
      healthIndex: [
        { month: 'Jan', value: 77.1 },
        { month: 'Feb', value: 77.5 },
        { month: 'Mar', value: 77.8 },
        { month: 'Apr', value: 78.0 },
        { month: 'May', value: 78.2 },
        { month: 'Jun', value: 78.3 }
      ],
      failures: [
        { month: 'Jan', value: 2 },
        { month: 'Feb', value: 1 },
        { month: 'Mar', value: 2 },
        { month: 'Apr', value: 0 },
        { month: 'May', value: 2 },
        { month: 'Jun', value: 1 }
      ]
    }
  })

  // Refresh data
  const refreshData = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }

  return (
    <ProvidersWrapper>
      <ProtectedRoute
        allowedRoles={[
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager"
        ]}
        requiredPermissions={[{ resource: "transformers", action: "read" }]}
      >
        <TransformerLayout>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Transformer Analytics</h1>
              <p className="text-gray-600 mt-1">Performance analysis and insights for transformer fleet</p>
            </div>

            <div className="flex items-center space-x-2">
              <div className="relative">
                <select
                  className="appearance-none bg-white border rounded-md px-3 py-1.5 pr-8 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value)}
                >
                  <option value="month">Last Month</option>
                  <option value="quarter">Last Quarter</option>
                  <option value="year">Last Year</option>
                  <option value="all">All Time</option>
                </select>
                <ChevronDown size={14} className="absolute right-2.5 top-2.5 text-gray-400" />
              </div>

              <div className="relative">
                <select
                  className="appearance-none bg-white border rounded-md px-3 py-1.5 pr-8 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                  value={regionFilter}
                  onChange={(e) => setRegionFilter(e.target.value)}
                >
                  <option value="all">All Regions</option>
                  <option value="Addis Ababa">Addis Ababa</option>
                  <option value="Oromia">Oromia</option>
                  <option value="Amhara">Amhara</option>
                  <option value="SNNPR">SNNPR</option>
                  <option value="Tigray">Tigray</option>
                </select>
                <ChevronDown size={14} className="absolute right-2.5 top-2.5 text-gray-400" />
              </div>

              <Button variant="outline" size="sm" onClick={refreshData} disabled={isLoading}>
                {isLoading ? (
                  <RefreshCw size={14} className="mr-1 animate-spin" />
                ) : (
                  <RefreshCw size={14} className="mr-1" />
                )}
                Refresh
              </Button>

              <Button variant="outline" size="sm">
                <Download size={14} className="mr-1" />
                Export
              </Button>
            </div>
          </div>

          {/* Stats cards */}
          <div className="grid grid-cols-6 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Total Transformers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-green-600">{analysisData.overview.totalTransformers}</div>
                  <Zap size={20} className="text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Avg. Efficiency</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-blue-600">{analysisData.overview.averageEfficiency}%</div>
                  <Percent size={20} className="text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Avg. Age</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-amber-600">{analysisData.overview.averageAge} years</div>
                  <Calendar size={20} className="text-amber-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Health Index</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-green-600">{analysisData.overview.averageHealthIndex}/100</div>
                  <TrendingUp size={20} className="text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Total Failures</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-red-600">{analysisData.overview.totalFailures}</div>
                  <AlertTriangleIcon size={20} className="text-red-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Failure Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-red-600">{analysisData.overview.failureRate}%</div>
                  <PercentageIcon size={20} className="text-red-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="overview" className="mb-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="regional">Regional Analysis</TabsTrigger>
              <TabsTrigger value="manufacturer">By Manufacturer</TabsTrigger>
              <TabsTrigger value="age">Age Analysis</TabsTrigger>
              <TabsTrigger value="failures">Failure Analysis</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-4">
              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Efficiency Trend</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 bg-gray-50 rounded flex items-center justify-center">
                      <div className="text-center">
                        <LineChart size={48} className="mx-auto text-gray-300 mb-2" />
                        <p className="text-gray-500">Chart visualization would appear here</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Health Index Trend</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 bg-gray-50 rounded flex items-center justify-center">
                      <div className="text-center">
                        <LineChart size={48} className="mx-auto text-gray-300 mb-2" />
                        <p className="text-gray-500">Chart visualization would appear here</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="regional" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Regional Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="h-64 bg-gray-50 rounded flex items-center justify-center">
                      <div className="text-center">
                        <PieChart size={48} className="mx-auto text-gray-300 mb-2" />
                        <p className="text-gray-500">Chart visualization would appear here</p>
                      </div>
                    </div>

                    <div>
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-2">Region</th>
                            <th className="text-right py-2">Count</th>
                            <th className="text-right py-2">Efficiency</th>
                            <th className="text-right py-2">Health</th>
                          </tr>
                        </thead>
                        <tbody>
                          {analysisData.byRegion.map(region => (
                            <tr key={region.name} className="border-b">
                              <td className="py-2">{region.name}</td>
                              <td className="text-right py-2">{region.count}</td>
                              <td className="text-right py-2">{region.efficiency}%</td>
                              <td className="text-right py-2">{region.healthIndex}/100</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="manufacturer" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Manufacturer Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="h-64 bg-gray-50 rounded flex items-center justify-center">
                      <div className="text-center">
                        <BarChart2 size={48} className="mx-auto text-gray-300 mb-2" />
                        <p className="text-gray-500">Chart visualization would appear here</p>
                      </div>
                    </div>

                    <div>
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-2">Manufacturer</th>
                            <th className="text-right py-2">Count</th>
                            <th className="text-right py-2">Efficiency</th>
                            <th className="text-right py-2">Failures</th>
                          </tr>
                        </thead>
                        <tbody>
                          {analysisData.byManufacturer.map(manufacturer => (
                            <tr key={manufacturer.name} className="border-b">
                              <td className="py-2">{manufacturer.name}</td>
                              <td className="text-right py-2">{manufacturer.count}</td>
                              <td className="text-right py-2">{manufacturer.efficiency}%</td>
                              <td className="text-right py-2">{manufacturer.failures}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="age" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Age Distribution Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="h-64 bg-gray-50 rounded flex items-center justify-center">
                      <div className="text-center">
                        <BarChart2 size={48} className="mx-auto text-gray-300 mb-2" />
                        <p className="text-gray-500">Chart visualization would appear here</p>
                      </div>
                    </div>

                    <div>
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-2">Age Range</th>
                            <th className="text-right py-2">Count</th>
                            <th className="text-right py-2">Efficiency</th>
                            <th className="text-right py-2">Health</th>
                          </tr>
                        </thead>
                        <tbody>
                          {analysisData.byAge.map(age => (
                            <tr key={age.range} className="border-b">
                              <td className="py-2">{age.range}</td>
                              <td className="text-right py-2">{age.count}</td>
                              <td className="text-right py-2">{age.efficiency}%</td>
                              <td className="text-right py-2">{age.healthIndex}/100</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="failures" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Failure Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="h-64 bg-gray-50 rounded flex items-center justify-center">
                      <div className="text-center">
                        <PieChart size={48} className="mx-auto text-gray-300 mb-2" />
                        <p className="text-gray-500">Chart visualization would appear here</p>
                      </div>
                    </div>

                    <div>
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-2">Failure Cause</th>
                            <th className="text-right py-2">Count</th>
                            <th className="text-right py-2">Percentage</th>
                          </tr>
                        </thead>
                        <tbody>
                          {analysisData.failureCauses.map(cause => (
                            <tr key={cause.cause} className="border-b">
                              <td className="py-2">{cause.cause}</td>
                              <td className="text-right py-2">{cause.count}</td>
                              <td className="text-right py-2">{cause.percentage}%</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Action buttons */}
          <div className="flex justify-end space-x-4">
            <Button variant="outline" className="flex items-center">
              <FileText size={16} className="mr-2" />
              Generate Report
            </Button>

            <Button variant="outline" className="flex items-center">
              <Share2 size={16} className="mr-2" />
              Share Analysis
            </Button>
          </div>
        </div>
        </TransformerLayout>
      </ProtectedRoute>
    </ProvidersWrapper>
  )
}

// Alert Triangle Icon
function AlertTriangleIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z" />
      <path d="M12 9v4" />
      <path d="M12 17h.01" />
    </svg>
  )
}

// Percentage Icon
function PercentageIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <line x1="19" x2="5" y1="5" y2="19" />
      <circle cx="6.5" cy="6.5" r="2.5" />
      <circle cx="17.5" cy="17.5" r="2.5" />
    </svg>
  )
}
