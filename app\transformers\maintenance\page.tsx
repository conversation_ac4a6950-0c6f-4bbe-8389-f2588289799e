"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Calendar,
  Clock,
  Wrench,
  AlertTriangle,
  CheckCircle,
  XCircle,
  User,
  MapPin,
  FileText,
  Plus,
  Edit,
  Trash2,
  Eye,
  Download,
  Upload,
  RefreshCw,
  Search,
  Filter,
  Settings,
  Bell,
  Target,
  TrendingUp,
  Activity,
  Zap,
  Thermometer,
  Gauge,
  BarChart3,
  PieChart,
  Users,
  Truck,
  Package,
  Star,
  Award
} from 'lucide-react'
import {
  <PERSON><PERSON>hart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  <PERSON><PERSON><PERSON> as RechartsP<PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { TransformerLayout } from '@/components/transformer/TransformerLayout'
import { ProtectedRoute } from "@/src/components/layout/protected-route"
import ProvidersWrapper from "@/components/providers-wrapper"

// Mock maintenance data
const mockMaintenanceSchedule = [
  {
    id: 'MAINT-001',
    transformerId: 'T-AA-001',
    transformerName: 'Lideta Primary Transformer',
    location: 'Lideta Substation, Addis Ababa',
    type: 'preventive',
    priority: 'medium',
    status: 'scheduled',
    scheduledDate: '2024-02-20',
    estimatedDuration: 8,
    assignedTechnician: 'John Doe',
    description: 'Routine oil analysis and cooling system inspection',
    lastMaintenance: '2023-08-20',
    nextDue: '2024-02-20',
    cost: 25000,
    parts: ['Oil Filter', 'Gaskets'],
    checklist: [
      { item: 'Oil level check', completed: false },
      { item: 'Temperature monitoring', completed: false },
      { item: 'Insulation resistance test', completed: false },
      { item: 'Cooling system inspection', completed: false }
    ]
  },
  {
    id: 'MAINT-002',
    transformerId: 'T-OR-045',
    transformerName: 'Sebeta Distribution Transformer',
    location: 'Sebeta Substation, Oromia',
    type: 'corrective',
    priority: 'high',
    status: 'in_progress',
    scheduledDate: '2024-02-15',
    estimatedDuration: 12,
    assignedTechnician: 'Jane Smith',
    description: 'High temperature alarm investigation and cooling system repair',
    lastMaintenance: '2023-11-15',
    nextDue: '2024-05-15',
    cost: 45000,
    parts: ['Cooling Fan', 'Temperature Sensor', 'Thermal Relay'],
    checklist: [
      { item: 'Temperature sensor calibration', completed: true },
      { item: 'Cooling fan replacement', completed: true },
      { item: 'Thermal protection testing', completed: false },
      { item: 'Load testing', completed: false }
    ]
  },
  {
    id: 'MAINT-003',
    transformerId: 'T-AM-023',
    transformerName: 'Bahir Dar Distribution Transformer',
    location: 'Bahir Dar Substation, Amhara',
    type: 'emergency',
    priority: 'critical',
    status: 'pending',
    scheduledDate: '2024-02-14',
    estimatedDuration: 16,
    assignedTechnician: 'Bob Johnson',
    description: 'Communication system failure and control panel replacement',
    lastMaintenance: '2023-09-10',
    nextDue: '2024-03-10',
    cost: 78000,
    parts: ['Control Panel', 'Communication Module', 'Wiring Harness'],
    checklist: [
      { item: 'System diagnostics', completed: false },
      { item: 'Control panel replacement', completed: false },
      { item: 'Communication system setup', completed: false },
      { item: 'Full system testing', completed: false }
    ]
  }
]

const maintenanceStats = {
  totalScheduled: 156,
  completed: 134,
  inProgress: 12,
  overdue: 10,
  avgCompletionTime: 6.8,
  costThisMonth: 2400000,
  preventiveRatio: 78,
  onTimeCompletion: 92.5
}

const maintenanceTypes = [
  { name: 'Preventive', value: 78, color: '#10b981' },
  { name: 'Corrective', value: 18, color: '#f59e0b' },
  { name: 'Emergency', value: 4, color: '#ef4444' }
]

const statusColors = {
  scheduled: 'bg-blue-100 text-blue-800 border-blue-200',
  in_progress: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  completed: 'bg-green-100 text-green-800 border-green-200',
  pending: 'bg-gray-100 text-gray-800 border-gray-200',
  overdue: 'bg-red-100 text-red-800 border-red-200'
}

const priorityColors = {
  low: 'bg-gray-100 text-gray-800 border-gray-200',
  medium: 'bg-blue-100 text-blue-800 border-blue-200',
  high: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  critical: 'bg-red-100 text-red-800 border-red-200'
}

const typeColors = {
  preventive: 'bg-green-100 text-green-800 border-green-200',
  corrective: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  emergency: 'bg-red-100 text-red-800 border-red-200'
}

export default function TransformerMaintenancePage() {
  const [maintenanceData, setMaintenanceData] = useState(mockMaintenanceSchedule)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedPriority, setSelectedPriority] = useState('all')
  const [selectedType, setSelectedType] = useState('all')

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  const filteredData = maintenanceData.filter(item => {
    const matchesSearch = item.transformerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.assignedTechnician.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || item.status === selectedStatus
    const matchesPriority = selectedPriority === 'all' || item.priority === selectedPriority
    const matchesType = selectedType === 'all' || item.type === selectedType

    return matchesSearch && matchesStatus && matchesPriority && matchesType
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading maintenance schedule...</p>
        </div>
      </div>
    )
  }

  return (
    <ProvidersWrapper>
      <ProtectedRoute
        allowedRoles={[
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician"
        ]}
      >
        <TransformerLayout>
          <div className="space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Maintenance Scheduling</h1>
                <p className="text-muted-foreground">
                  Comprehensive maintenance planning and scheduling for Ethiopian Electric Utility transformers
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  Calendar View
                </Button>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Schedule
                </Button>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Schedule Maintenance
                </Button>
              </div>
            </div>

            {/* Maintenance Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Scheduled</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{maintenanceStats.totalScheduled}</div>
                  <p className="text-xs text-muted-foreground">
                    {maintenanceStats.completed} completed this month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">In Progress</CardTitle>
                  <Activity className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">{maintenanceStats.inProgress}</div>
                  <p className="text-xs text-muted-foreground">
                    Currently active tasks
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Overdue</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">{maintenanceStats.overdue}</div>
                  <p className="text-xs text-muted-foreground">
                    Require immediate attention
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">On-time Rate</CardTitle>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{maintenanceStats.onTimeCompletion}%</div>
                  <p className="text-xs text-muted-foreground">
                    Completion performance
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Main Maintenance Interface */}
            <Tabs defaultValue="schedule" className="space-y-4">
              <TabsList>
                <TabsTrigger value="schedule">Maintenance Schedule</TabsTrigger>
                <TabsTrigger value="calendar">Calendar View</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
                <TabsTrigger value="planning">Planning</TabsTrigger>
              </TabsList>

              <TabsContent value="schedule" className="space-y-4">
                {/* Filters */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-4">
                      <div className="flex-1">
                        <Input
                          placeholder="Search maintenance tasks..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="max-w-sm"
                        />
                      </div>
                      <select
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Status</option>
                        <option value="scheduled">Scheduled</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="overdue">Overdue</option>
                      </select>
                      <select
                        value={selectedPriority}
                        onChange={(e) => setSelectedPriority(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Priority</option>
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="critical">Critical</option>
                      </select>
                      <select
                        value={selectedType}
                        onChange={(e) => setSelectedType(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Types</option>
                        <option value="preventive">Preventive</option>
                        <option value="corrective">Corrective</option>
                        <option value="emergency">Emergency</option>
                      </select>
                    </div>
                  </CardContent>
                </Card>

                {/* Maintenance Tasks List */}
                <div className="space-y-4">
                  {filteredData.map((task) => (
                    <Card key={task.id} className="hover:shadow-lg transition-shadow">
                      <CardContent className="pt-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold text-lg">{task.transformerName}</h3>
                              <Badge className={statusColors[task.status as keyof typeof statusColors]}>
                                {task.status.replace('_', ' ')}
                              </Badge>
                              <Badge className={priorityColors[task.priority as keyof typeof priorityColors]}>
                                {task.priority}
                              </Badge>
                              <Badge className={typeColors[task.type as keyof typeof typeColors]}>
                                {task.type}
                              </Badge>
                            </div>

                            <p className="text-muted-foreground mb-3">{task.description}</p>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                <span>{task.location}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>Due: {new Date(task.scheduledDate).toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>Duration: {task.estimatedDuration}h</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                <span>{task.assignedTechnician}</span>
                              </div>
                            </div>

                            {/* Progress Bar */}
                            <div className="mb-4">
                              <div className="flex justify-between text-xs mb-1">
                                <span>Progress</span>
                                <span>{Math.round((task.checklist.filter(item => item.completed).length / task.checklist.length) * 100)}%</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-blue-500 h-2 rounded-full"
                                  style={{ width: `${(task.checklist.filter(item => item.completed).length / task.checklist.length) * 100}%` }}
                                ></div>
                              </div>
                            </div>

                            {/* Parts Required */}
                            <div className="mb-4">
                              <Label className="text-sm font-medium">Parts Required:</Label>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {task.parts.map((part, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {part}
                                  </Badge>
                                ))}
                              </div>
                            </div>

                            {/* Cost Information */}
                            <div className="flex items-center gap-4 text-sm">
                              <div className="flex items-center gap-1">
                                <Package className="h-3 w-3" />
                                <span>Cost: {task.cost.toLocaleString()} ETB</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>Last: {new Date(task.lastMaintenance).toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Bell className="h-3 w-3" />
                                <span>Next Due: {new Date(task.nextDue).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 ml-4">
                            <Button size="sm" variant="outline">
                              <Eye className="h-3 w-3 mr-1" />
                              View
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            {task.status === 'scheduled' && (
                              <Button size="sm">
                                <Wrench className="h-3 w-3 mr-1" />
                                Start
                              </Button>
                            )}
                            {task.status === 'in_progress' && (
                              <Button size="sm" className="bg-green-600 hover:bg-green-700">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Complete
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="calendar" className="space-y-4">
                {/* Calendar View */}
                <Card>
                  <CardHeader>
                    <CardTitle>Maintenance Calendar</CardTitle>
                    <CardDescription>Visual calendar view of scheduled maintenance activities</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-7 gap-2 mb-4">
                      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                        <div key={day} className="text-center font-medium p-2 bg-gray-50 rounded">
                          {day}
                        </div>
                      ))}
                    </div>
                    <div className="grid grid-cols-7 gap-2">
                      {Array.from({ length: 35 }, (_, i) => {
                        const date = new Date(2024, 1, i - 6) // February 2024
                        const dayTasks = filteredData.filter(task =>
                          new Date(task.scheduledDate).toDateString() === date.toDateString()
                        )

                        return (
                          <div key={i} className="min-h-[100px] p-2 border rounded hover:bg-gray-50">
                            <div className="font-medium text-sm mb-1">
                              {date.getDate()}
                            </div>
                            {dayTasks.map(task => (
                              <div
                                key={task.id}
                                className={`text-xs p-1 mb-1 rounded ${
                                  task.priority === 'critical' ? 'bg-red-100 text-red-800' :
                                  task.priority === 'high' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-blue-100 text-blue-800'
                                }`}
                              >
                                {task.transformerId}
                              </div>
                            ))}
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="analytics" className="space-y-4">
                {/* Maintenance Analytics */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Maintenance Type Distribution</CardTitle>
                      <CardDescription>Breakdown of maintenance activities by type</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsPieChart>
                          <Pie
                            data={maintenanceTypes}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {maintenanceTypes.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Monthly Maintenance Trends</CardTitle>
                      <CardDescription>Maintenance activities over the past 12 months</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={Array.from({ length: 12 }, (_, i) => ({
                          month: new Date(2023, i).toLocaleDateString('en', { month: 'short' }),
                          preventive: Math.floor(Math.random() * 20) + 10,
                          corrective: Math.floor(Math.random() * 8) + 3,
                          emergency: Math.floor(Math.random() * 3) + 1
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="preventive" stackId="a" fill="#10b981" name="Preventive" />
                          <Bar dataKey="corrective" stackId="a" fill="#f59e0b" name="Corrective" />
                          <Bar dataKey="emergency" stackId="a" fill="#ef4444" name="Emergency" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Cost Analysis */}
                <Card>
                  <CardHeader>
                    <CardTitle>Maintenance Cost Analysis</CardTitle>
                    <CardDescription>Cost trends and budget tracking</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <RechartsLineChart data={Array.from({ length: 12 }, (_, i) => ({
                        month: new Date(2023, i).toLocaleDateString('en', { month: 'short' }),
                        actual: Math.floor(Math.random() * 500000) + 1500000,
                        budget: 2000000,
                        savings: Math.floor(Math.random() * 200000) + 100000
                      }))}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="actual" stroke="#3b82f6" name="Actual Cost (ETB)" />
                        <Line type="monotone" dataKey="budget" stroke="#ef4444" strokeDasharray="5 5" name="Budget (ETB)" />
                        <Line type="monotone" dataKey="savings" stroke="#10b981" name="Savings (ETB)" />
                      </RechartsLineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="planning" className="space-y-4">
                {/* Resource Planning Overview */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-blue-600">Available Technicians</p>
                          <p className="text-2xl font-bold text-blue-600">24</p>
                        </div>
                        <Users className="h-8 w-8 text-blue-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-green-600">Capacity Utilization</p>
                          <p className="text-2xl font-bold text-green-600">78%</p>
                        </div>
                        <BarChart3 className="h-8 w-8 text-green-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-purple-600">Planned Tasks</p>
                          <p className="text-2xl font-bold text-purple-600">89</p>
                        </div>
                        <Calendar className="h-8 w-8 text-purple-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-orange-600">Budget Remaining</p>
                          <p className="text-2xl font-bold text-orange-600">3.2M ETB</p>
                        </div>
                        <TrendingUp className="h-8 w-8 text-orange-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Predictive Maintenance Planning */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Predictive Maintenance Schedule</CardTitle>
                      <CardDescription>AI-recommended maintenance timeline for next 6 months</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={Array.from({ length: 6 }, (_, i) => ({
                          month: new Date(2024, i + 2).toLocaleDateString('en', { month: 'short' }),
                          predicted: Math.floor(Math.random() * 25) + 15,
                          scheduled: Math.floor(Math.random() * 20) + 10,
                          capacity: 35
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="predicted" fill="#3b82f6" name="AI Predicted" />
                          <Bar dataKey="scheduled" fill="#10b981" name="Currently Scheduled" />
                          <Bar dataKey="capacity" fill="#e5e7eb" name="Max Capacity" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Resource Allocation</CardTitle>
                      <CardDescription>Technician workload distribution</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsPieChart>
                          <Pie
                            data={[
                              { name: 'John Doe', value: 8, fill: '#3b82f6' },
                              { name: 'Jane Smith', value: 6, fill: '#10b981' },
                              { name: 'Bob Johnson', value: 5, fill: '#f59e0b' },
                              { name: 'Alice Brown', value: 4, fill: '#ef4444' },
                              { name: 'Others', value: 12, fill: '#8b5cf6' }
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, value }) => `${name}: ${value}`}
                            outerRadius={80}
                            dataKey="value"
                          />
                          <Tooltip />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Maintenance Planning Table */}
                <Card>
                  <CardHeader>
                    <CardTitle>Strategic Maintenance Planning</CardTitle>
                    <CardDescription>Long-term maintenance strategy and resource planning</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Upcoming Critical Maintenance */}
                      <div>
                        <h4 className="font-medium mb-3">Upcoming Critical Maintenance (Next 30 Days)</h4>
                        <div className="space-y-3">
                          {[
                            {
                              transformer: 'T-OR-045',
                              name: 'Sebeta Distribution Transformer',
                              dueDate: '2024-02-18',
                              priority: 'critical',
                              type: 'corrective',
                              estimatedCost: 45000,
                              riskLevel: 'high'
                            },
                            {
                              transformer: 'T-AM-023',
                              name: 'Bahir Dar Distribution Transformer',
                              dueDate: '2024-02-22',
                              priority: 'high',
                              type: 'preventive',
                              estimatedCost: 28000,
                              riskLevel: 'medium'
                            },
                            {
                              transformer: 'T-TI-012',
                              name: 'Mekelle Primary Transformer',
                              dueDate: '2024-02-25',
                              priority: 'medium',
                              type: 'preventive',
                              estimatedCost: 35000,
                              riskLevel: 'low'
                            }
                          ].map((item, index) => (
                            <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <span className="font-medium">{item.transformer}</span>
                                  <Badge className={priorityColors[item.priority as keyof typeof priorityColors]}>
                                    {item.priority}
                                  </Badge>
                                  <Badge className={typeColors[item.type as keyof typeof typeColors]}>
                                    {item.type}
                                  </Badge>
                                </div>
                                <p className="text-sm text-muted-foreground mb-2">{item.name}</p>
                                <div className="flex items-center gap-4 text-xs">
                                  <span>Due: {new Date(item.dueDate).toLocaleDateString()}</span>
                                  <span>Cost: {item.estimatedCost.toLocaleString()} ETB</span>
                                  <span className={`px-2 py-1 rounded ${
                                    item.riskLevel === 'high' ? 'bg-red-100 text-red-800' :
                                    item.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-green-100 text-green-800'
                                  }`}>
                                    {item.riskLevel} risk
                                  </span>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button size="sm" variant="outline">
                                  Schedule
                                </Button>
                                <Button size="sm">
                                  Optimize
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Resource Optimization Recommendations */}
                      <div>
                        <h4 className="font-medium mb-3">Resource Optimization Recommendations</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-blue-50 rounded-lg">
                            <h5 className="font-medium text-blue-900 mb-2">Workload Balancing</h5>
                            <p className="text-sm text-blue-700 mb-3">
                              Redistribute 3 tasks from John Doe to available technicians to optimize workload.
                            </p>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-blue-600">Efficiency gain: +15%</span>
                              <Button size="sm" variant="outline">Apply</Button>
                            </div>
                          </div>
                          <div className="p-4 bg-green-50 rounded-lg">
                            <h5 className="font-medium text-green-900 mb-2">Preventive Scheduling</h5>
                            <p className="text-sm text-green-700 mb-3">
                              Schedule 5 preventive tasks during low-demand periods to reduce emergency repairs.
                            </p>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-green-600">Cost savings: 180K ETB</span>
                              <Button size="sm" variant="outline">Schedule</Button>
                            </div>
                          </div>
                          <div className="p-4 bg-purple-50 rounded-lg">
                            <h5 className="font-medium text-purple-900 mb-2">Parts Optimization</h5>
                            <p className="text-sm text-purple-700 mb-3">
                              Bulk order common parts for Q2 maintenance to reduce costs by 12%.
                            </p>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-purple-600">Savings: 95K ETB</span>
                              <Button size="sm" variant="outline">Order</Button>
                            </div>
                          </div>
                          <div className="p-4 bg-orange-50 rounded-lg">
                            <h5 className="font-medium text-orange-900 mb-2">Training Recommendation</h5>
                            <p className="text-sm text-orange-700 mb-3">
                              Schedule advanced diagnostics training for 4 technicians to improve efficiency.
                            </p>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-orange-600">ROI: 240% in 6 months</span>
                              <Button size="sm" variant="outline">Plan</Button>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Budget Planning */}
                      <div>
                        <h4 className="font-medium mb-3">Budget Planning & Forecasting</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <Card>
                            <CardContent className="pt-4">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">2.4M ETB</div>
                                <p className="text-sm text-muted-foreground">Q1 Actual Spend</p>
                                <div className="text-xs text-green-600 mt-1">8% under budget</div>
                              </div>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardContent className="pt-4">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-purple-600">2.8M ETB</div>
                                <p className="text-sm text-muted-foreground">Q2 Forecast</p>
                                <div className="text-xs text-yellow-600 mt-1">5% over budget</div>
                              </div>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardContent className="pt-4">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">9.6M ETB</div>
                                <p className="text-sm text-muted-foreground">Annual Budget</p>
                                <div className="text-xs text-blue-600 mt-1">25% utilized</div>
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </TransformerLayout>
      </ProtectedRoute>
    </ProvidersWrapper>
  )
}
