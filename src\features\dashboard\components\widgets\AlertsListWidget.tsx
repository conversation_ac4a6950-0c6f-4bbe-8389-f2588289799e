"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Badge } from '@/src/components/ui/badge'
import { AlertTriangle, AlertCircle, Info, CheckCircle, Clock } from 'lucide-react'

interface Alert {
  id: string
  type: 'critical' | 'warning' | 'info' | 'success'
  title: string
  message: string
  transformerId?: string
  location: string
  timestamp: string
  isRead: boolean
  priority: 'low' | 'medium' | 'high' | 'critical'
}

export default function AlertsListWidget() {
  const [alerts, setAlerts] = useState<Alert[]>([
    {
      id: '1',
      type: 'critical',
      title: 'Transformer Overload',
      message: 'Transformer T-045 is operating at 95% capacity',
      transformerId: 'T-045',
      location: 'Oromia - Industrial Zone',
      timestamp: '2024-01-15T10:30:00',
      isRead: false,
      priority: 'critical'
    },
    {
      id: '2',
      type: 'warning',
      title: 'Maintenance Due',
      message: 'Routine maintenance scheduled for T-023',
      transformerId: 'T-023',
      location: 'Amhara - Residential Block',
      timestamp: '2024-01-15T09:15:00',
      isRead: false,
      priority: 'medium'
    },
    {
      id: '3',
      type: 'info',
      title: 'System Update',
      message: 'Monitoring system updated successfully',
      location: 'System Wide',
      timestamp: '2024-01-15T08:00:00',
      isRead: true,
      priority: 'low'
    },
    {
      id: '4',
      type: 'success',
      title: 'Maintenance Completed',
      message: 'Preventive maintenance completed for T-012',
      transformerId: 'T-012',
      location: 'Addis Ababa - Sector 2',
      timestamp: '2024-01-14T16:45:00',
      isRead: true,
      priority: 'low'
    }
  ])

  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Fetch alerts data
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // In a real implementation, this would fetch from the API
        await new Promise(resolve => setTimeout(resolve, 600))
      } catch (error) {
        console.error('Error fetching alerts:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-orange-500" />
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Info className="h-4 w-4 text-gray-500" />
    }
  }

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'critical':
        return 'bg-red-50 border-red-200'
      case 'warning':
        return 'bg-orange-50 border-orange-200'
      case 'info':
        return 'bg-blue-50 border-blue-200'
      case 'success':
        return 'bg-green-50 border-green-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-gray-100 text-gray-800'
      case 'medium':
        return 'bg-blue-100 text-blue-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'critical':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const markAsRead = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, isRead: true } : alert
    ))
  }

  const unreadCount = alerts.filter(alert => !alert.isRead).length

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Alerts</CardTitle>
          <CardDescription>Loading alerts...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-48">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Recent Alerts
          </div>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="rounded-full">
              {unreadCount}
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Latest system alerts and notifications
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {alerts.map((alert) => (
            <div
              key={alert.id}
              className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                getAlertColor(alert.type)
              } ${!alert.isRead ? 'border-l-4' : ''}`}
              onClick={() => markAsRead(alert.id)}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  {getAlertIcon(alert.type)}
                  <h4 className={`font-medium ${!alert.isRead ? 'font-semibold' : ''}`}>
                    {alert.title}
                  </h4>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={getPriorityColor(alert.priority)}>
                    {alert.priority}
                  </Badge>
                  {!alert.isRead && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  )}
                </div>
              </div>

              <p className="text-sm text-muted-foreground mb-2">
                {alert.message}
              </p>

              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center gap-4">
                  {alert.transformerId && (
                    <span>ID: {alert.transformerId}</span>
                  )}
                  <span>{alert.location}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>{formatTimestamp(alert.timestamp)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-4 gap-2 text-center">
            <div>
              <div className="text-lg font-semibold text-red-600">
                {alerts.filter(a => a.type === 'critical').length}
              </div>
              <div className="text-xs text-muted-foreground">Critical</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-orange-600">
                {alerts.filter(a => a.type === 'warning').length}
              </div>
              <div className="text-xs text-muted-foreground">Warning</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-blue-600">
                {alerts.filter(a => a.type === 'info').length}
              </div>
              <div className="text-xs text-muted-foreground">Info</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-green-600">
                {alerts.filter(a => a.type === 'success').length}
              </div>
              <div className="text-xs text-muted-foreground">Success</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
