"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON>alog<PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/src/components/ui/tabs"
import { Switch } from "@/src/components/ui/switch"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/src/components/ui/radio-group"
import { Slider } from "@/src/components/ui/slider"
import { useToast } from "@/src/components/ui/use-toast"
import { BarChart2, LineChart, PieChart, Activity, Download, Save, RefreshCw } from "lucide-react"

interface ChartSettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  chartType?: string
  onChartTypeChange?: (type: string) => void
  title?: string
  description?: string
}

export function ChartSettingsDialog({
  open,
  onOpenChange,
  chartType = "bar",
  onChartTypeChange,
  title = "Chart Settings",
  description = "Customize chart appearance and behavior"
}: ChartSettingsDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("appearance")
  
  // Chart appearance settings
  const [selectedChartType, setSelectedChartType] = useState(chartType)
  const [showLegend, setShowLegend] = useState(true)
  const [showGrid, setShowGrid] = useState(true)
  const [showLabels, setShowLabels] = useState(true)
  const [colorScheme, setColorScheme] = useState("default")
  
  // Data settings
  const [dataRange, setDataRange] = useState("30days")
  const [aggregation, setAggregation] = useState("daily")
  const [showTrend, setShowTrend] = useState(true)
  const [dataPoints, setDataPoints] = useState([50])
  
  // Export settings
  const [exportFormat, setExportFormat] = useState("png")
  const [exportResolution, setExportResolution] = useState("medium")
  
  const handleSave = () => {
    // Update chart type if provided
    if (onChartTypeChange && selectedChartType !== chartType) {
      onChartTypeChange(selectedChartType)
    }
    
    // In a real app, this would save all settings
    toast({
      title: "Chart Settings Saved",
      description: "Your chart customization settings have been applied."
    })
    
    onOpenChange(false)
  }
  
  const handleReset = () => {
    // Reset to defaults
    setSelectedChartType(chartType)
    setShowLegend(true)
    setShowGrid(true)
    setShowLabels(true)
    setColorScheme("default")
    setDataRange("30days")
    setAggregation("daily")
    setShowTrend(true)
    setDataPoints([50])
    setExportFormat("png")
    setExportResolution("medium")
    
    toast({
      title: "Settings Reset",
      description: "Chart settings have been reset to defaults."
    })
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-2">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="data">Data</TabsTrigger>
            <TabsTrigger value="export">Export</TabsTrigger>
          </TabsList>
          
          <TabsContent value="appearance" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-3">Chart Type</h4>
                <RadioGroup 
                  value={selectedChartType} 
                  onValueChange={setSelectedChartType}
                  className="grid grid-cols-3 gap-4"
                >
                  <div>
                    <RadioGroupItem 
                      value="bar" 
                      id="chart-bar" 
                      className="peer sr-only" 
                    />
                    <Label
                      htmlFor="chart-bar"
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <BarChart2 className="mb-2 h-6 w-6" />
                      Bar Chart
                    </Label>
                  </div>
                  <div>
                    <RadioGroupItem 
                      value="line" 
                      id="chart-line" 
                      className="peer sr-only" 
                    />
                    <Label
                      htmlFor="chart-line"
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <LineChart className="mb-2 h-6 w-6" />
                      Line Chart
                    </Label>
                  </div>
                  <div>
                    <RadioGroupItem 
                      value="pie" 
                      id="chart-pie" 
                      className="peer sr-only" 
                    />
                    <Label
                      htmlFor="chart-pie"
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <PieChart className="mb-2 h-6 w-6" />
                      Pie Chart
                    </Label>
                  </div>
                </RadioGroup>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-legend">Show Legend</Label>
                  <Switch 
                    id="show-legend" 
                    checked={showLegend} 
                    onCheckedChange={setShowLegend} 
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-grid">Show Grid Lines</Label>
                  <Switch 
                    id="show-grid" 
                    checked={showGrid} 
                    onCheckedChange={setShowGrid} 
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-labels">Show Data Labels</Label>
                  <Switch 
                    id="show-labels" 
                    checked={showLabels} 
                    onCheckedChange={setShowLabels} 
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="color-scheme">Color Scheme</Label>
                <Select value={colorScheme} onValueChange={setColorScheme}>
                  <SelectTrigger id="color-scheme">
                    <SelectValue placeholder="Select color scheme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default</SelectItem>
                    <SelectItem value="monochrome">Monochrome</SelectItem>
                    <SelectItem value="colorful">Colorful</SelectItem>
                    <SelectItem value="pastel">Pastel</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="data" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="data-range">Data Range</Label>
                <Select value={dataRange} onValueChange={setDataRange}>
                  <SelectTrigger id="data-range">
                    <SelectValue placeholder="Select data range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7days">Last 7 Days</SelectItem>
                    <SelectItem value="30days">Last 30 Days</SelectItem>
                    <SelectItem value="90days">Last 90 Days</SelectItem>
                    <SelectItem value="1year">Last Year</SelectItem>
                    <SelectItem value="all">All Time</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="aggregation">Data Aggregation</Label>
                <Select value={aggregation} onValueChange={setAggregation}>
                  <SelectTrigger id="aggregation">
                    <SelectValue placeholder="Select aggregation method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hourly">Hourly</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="show-trend">Show Trend Line</Label>
                <Switch 
                  id="show-trend" 
                  checked={showTrend} 
                  onCheckedChange={setShowTrend} 
                />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="data-points">Maximum Data Points</Label>
                  <span className="text-sm">{dataPoints[0]}</span>
                </div>
                <Slider
                  id="data-points"
                  min={10}
                  max={100}
                  step={5}
                  value={dataPoints}
                  onValueChange={setDataPoints}
                />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="export" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="export-format">Export Format</Label>
                <Select value={exportFormat} onValueChange={setExportFormat}>
                  <SelectTrigger id="export-format">
                    <SelectValue placeholder="Select export format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="png">PNG Image</SelectItem>
                    <SelectItem value="jpg">JPG Image</SelectItem>
                    <SelectItem value="svg">SVG Vector</SelectItem>
                    <SelectItem value="pdf">PDF Document</SelectItem>
                    <SelectItem value="csv">CSV Data</SelectItem>
                    <SelectItem value="excel">Excel Spreadsheet</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="export-resolution">Image Resolution</Label>
                <Select value={exportResolution} onValueChange={setExportResolution}>
                  <SelectTrigger id="export-resolution">
                    <SelectValue placeholder="Select resolution" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low (72 DPI)</SelectItem>
                    <SelectItem value="medium">Medium (150 DPI)</SelectItem>
                    <SelectItem value="high">High (300 DPI)</SelectItem>
                    <SelectItem value="ultra">Ultra HD (600 DPI)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => {
                  toast({
                    title: "Chart Exported",
                    description: `Chart has been exported as ${exportFormat.toUpperCase()}`
                  })
                }}
              >
                <Download className="mr-2 h-4 w-4" />
                Export Chart Now
              </Button>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="flex justify-between items-center mt-4">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
