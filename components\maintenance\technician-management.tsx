"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Textarea } from "@/src/components/ui/textarea"
import { Progress } from "@/src/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/src/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/src/components/ui/dialog"
import {
  Users,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  UserCheck,
  UserX,
  Calendar,
  Clock,
  MapPin,
  Phone,
  Mail,
  Award,
  Briefcase,
  Star,
  TrendingUp,
  Activity,
  CheckCircle,
  AlertTriangle,
  Download,
  RefreshCw,
  Settings,
  BarChart3,
  FileText,
  Wrench,
  Shield
} from 'lucide-react'
import { format } from 'date-fns'

interface Technician {
  id: string
  employeeId: string
  firstName: string
  lastName: string
  email: string
  phone: string
  avatar?: string
  position: string
  department: string
  location: string
  region: string
  status: 'active' | 'inactive' | 'on_leave' | 'training'
  hireDate: string
  experience: number // years
  certifications: {
    id: string
    name: string
    issuer: string
    issueDate: string
    expiryDate: string
    status: 'valid' | 'expired' | 'expiring_soon'
  }[]
  skills: {
    skill: string
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
    verified: boolean
  }[]
  workload: {
    current: number
    capacity: number
    availability: 'available' | 'busy' | 'overloaded' | 'unavailable'
  }
  performance: {
    completedTasks: number
    averageRating: number
    onTimeCompletion: number
    safetyScore: number
  }
  assignments: {
    id: string
    type: 'maintenance' | 'inspection' | 'emergency' | 'training'
    title: string
    transformerId?: string
    status: 'assigned' | 'in_progress' | 'completed' | 'cancelled'
    assignedDate: string
    dueDate: string
    priority: 'low' | 'medium' | 'high' | 'critical'
  }[]
  emergencyContact: {
    name: string
    relationship: string
    phone: string
  }
}

interface TechnicianManagementProps {
  className?: string
}

export function TechnicianManagement({ className }: TechnicianManagementProps) {
  const [technicians, setTechnicians] = useState<Technician[]>([])
  const [filteredTechnicians, setFilteredTechnicians] = useState<Technician[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [selectedTechnician, setSelectedTechnician] = useState<Technician | null>(null)
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    department: 'all',
    region: 'all',
    availability: 'all'
  })

  // Mock data for development
  const mockTechnicians: Technician[] = [
    {
      id: 'TECH-001',
      employeeId: 'EMP-001',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+251-911-123456',
      position: 'Senior Maintenance Engineer',
      department: 'Electrical Maintenance',
      location: 'Addis Ababa',
      region: 'Central',
      status: 'active',
      hireDate: '2020-03-15T00:00:00Z',
      experience: 8,
      certifications: [
        {
          id: 'CERT-001',
          name: 'High Voltage Safety Certification',
          issuer: 'Ethiopian Standards Agency',
          issueDate: '2023-01-15T00:00:00Z',
          expiryDate: '2025-01-15T00:00:00Z',
          status: 'valid'
        },
        {
          id: 'CERT-002',
          name: 'Transformer Maintenance Specialist',
          issuer: 'ABB Training Center',
          issueDate: '2022-06-10T00:00:00Z',
          expiryDate: '2024-06-10T00:00:00Z',
          status: 'expiring_soon'
        }
      ],
      skills: [
        { skill: 'High Voltage Systems', level: 'expert', verified: true },
        { skill: 'Transformer Maintenance', level: 'advanced', verified: true },
        { skill: 'Electrical Testing', level: 'advanced', verified: true },
        { skill: 'Safety Protocols', level: 'expert', verified: true }
      ],
      workload: {
        current: 6,
        capacity: 8,
        availability: 'available'
      },
      performance: {
        completedTasks: 145,
        averageRating: 4.8,
        onTimeCompletion: 95,
        safetyScore: 98
      },
      assignments: [
        {
          id: 'ASSIGN-001',
          type: 'maintenance',
          title: 'Transformer Oil Analysis',
          transformerId: 'T-AA-001',
          status: 'in_progress',
          assignedDate: '2024-02-10T09:00:00Z',
          dueDate: '2024-02-15T17:00:00Z',
          priority: 'medium'
        }
      ],
      emergencyContact: {
        name: 'Jane Doe',
        relationship: 'Spouse',
        phone: '+251-911-654321'
      }
    },
    {
      id: 'TECH-002',
      employeeId: 'EMP-002',
      firstName: 'Sarah',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '+251-911-234567',
      position: 'Maintenance Technician',
      department: 'Field Operations',
      location: 'Bahir Dar',
      region: 'Amhara',
      status: 'active',
      hireDate: '2021-07-20T00:00:00Z',
      experience: 5,
      certifications: [
        {
          id: 'CERT-003',
          name: 'Electrical Safety Training',
          issuer: 'Ethiopian Electric Utility',
          issueDate: '2023-03-01T00:00:00Z',
          expiryDate: '2024-03-01T00:00:00Z',
          status: 'expiring_soon'
        }
      ],
      skills: [
        { skill: 'Preventive Maintenance', level: 'advanced', verified: true },
        { skill: 'Equipment Inspection', level: 'intermediate', verified: true },
        { skill: 'Report Writing', level: 'advanced', verified: false }
      ],
      workload: {
        current: 8,
        capacity: 6,
        availability: 'overloaded'
      },
      performance: {
        completedTasks: 89,
        averageRating: 4.5,
        onTimeCompletion: 88,
        safetyScore: 96
      },
      assignments: [
        {
          id: 'ASSIGN-002',
          type: 'inspection',
          title: 'Routine Transformer Inspection',
          transformerId: 'T-AM-023',
          status: 'assigned',
          assignedDate: '2024-02-12T08:00:00Z',
          dueDate: '2024-02-18T16:00:00Z',
          priority: 'low'
        }
      ],
      emergencyContact: {
        name: 'Michael Smith',
        relationship: 'Brother',
        phone: '+251-911-345678'
      }
    },
    {
      id: 'TECH-003',
      employeeId: 'EMP-003',
      firstName: 'Ahmed',
      lastName: 'Hassan',
      email: '<EMAIL>',
      phone: '+251-911-345678',
      position: 'Emergency Response Specialist',
      department: 'Emergency Services',
      location: 'Dire Dawa',
      region: 'Eastern',
      status: 'on_leave',
      hireDate: '2019-11-10T00:00:00Z',
      experience: 12,
      certifications: [
        {
          id: 'CERT-004',
          name: 'Emergency Response Certification',
          issuer: 'National Emergency Management Agency',
          issueDate: '2023-05-15T00:00:00Z',
          expiryDate: '2026-05-15T00:00:00Z',
          status: 'valid'
        }
      ],
      skills: [
        { skill: 'Emergency Response', level: 'expert', verified: true },
        { skill: 'Crisis Management', level: 'advanced', verified: true },
        { skill: 'Team Leadership', level: 'advanced', verified: true }
      ],
      workload: {
        current: 0,
        capacity: 10,
        availability: 'unavailable'
      },
      performance: {
        completedTasks: 203,
        averageRating: 4.9,
        onTimeCompletion: 97,
        safetyScore: 99
      },
      assignments: [],
      emergencyContact: {
        name: 'Fatima Hassan',
        relationship: 'Wife',
        phone: '+251-911-456789'
      }
    }
  ]

  // Load technicians data
  const loadTechnicians = async () => {
    try {
      setIsLoading(true)
      // In a real app, this would fetch from the database
      // const data = await technicianService.getTechnicians()
      setTechnicians(mockTechnicians)
      setFilteredTechnicians(mockTechnicians)
    } catch (error) {
      console.error('Error loading technicians:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Apply filters
  const applyFilters = () => {
    let filtered = [...technicians]

    // Search filter
    if (filters.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(tech =>
        tech.firstName.toLowerCase().includes(search) ||
        tech.lastName.toLowerCase().includes(search) ||
        tech.employeeId.toLowerCase().includes(search) ||
        tech.email.toLowerCase().includes(search) ||
        tech.position.toLowerCase().includes(search)
      )
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(tech => tech.status === filters.status)
    }

    // Department filter
    if (filters.department !== 'all') {
      filtered = filtered.filter(tech => tech.department === filters.department)
    }

    // Region filter
    if (filters.region !== 'all') {
      filtered = filtered.filter(tech => tech.region === filters.region)
    }

    // Availability filter
    if (filters.availability !== 'all') {
      filtered = filtered.filter(tech => tech.workload.availability === filters.availability)
    }

    setFilteredTechnicians(filtered)
  }

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      case 'on_leave': return 'bg-yellow-100 text-yellow-800'
      case 'training': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get availability color
  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'available': return 'bg-green-100 text-green-800'
      case 'busy': return 'bg-yellow-100 text-yellow-800'
      case 'overloaded': return 'bg-red-100 text-red-800'
      case 'unavailable': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get skill level color
  const getSkillLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-red-100 text-red-800'
      case 'intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'advanced': return 'bg-blue-100 text-blue-800'
      case 'expert': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Calculate workload percentage
  const getWorkloadPercentage = (current: number, capacity: number) => {
    return Math.round((current / capacity) * 100)
  }

  // Handle technician actions
  const handleViewDetails = (technician: Technician) => {
    setSelectedTechnician(technician)
  }

  const handleAssignTask = (technicianId: string) => {
    // This would open an assignment dialog
    console.log('Assign task to technician:', technicianId)
  }

  const handleUpdateStatus = (technicianId: string, newStatus: string) => {
    setTechnicians(prev => prev.map(tech =>
      tech.id === technicianId
        ? { ...tech, status: newStatus as any }
        : tech
    ))
  }

  const handleAddTechnician = () => {
    setShowAddDialog(true)
  }

  useEffect(() => {
    loadTechnicians()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [filters, technicians])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Technician Management</h2>
          <p className="text-muted-foreground">
            Manage maintenance technicians, assignments, and performance tracking
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={loadTechnicians}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={handleAddTechnician}>
            <Plus className="h-4 w-4 mr-2" />
            Add Technician
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Technicians</p>
                <p className="text-2xl font-bold">{technicians.length}</p>
              </div>
              <Users className="h-8 w-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active</p>
                <p className="text-2xl font-bold text-green-600">
                  {technicians.filter(tech => tech.status === 'active').length}
                </p>
              </div>
              <UserCheck className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Available</p>
                <p className="text-2xl font-bold text-blue-600">
                  {technicians.filter(tech => tech.workload.availability === 'available').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Overloaded</p>
                <p className="text-2xl font-bold text-red-600">
                  {technicians.filter(tech => tech.workload.availability === 'overloaded').length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Performance</p>
                <p className="text-2xl font-bold text-green-600">
                  {(technicians.reduce((sum, tech) => sum + tech.performance.averageRating, 0) / technicians.length).toFixed(1)}
                </p>
              </div>
              <Star className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="technicians">Technicians</TabsTrigger>
          <TabsTrigger value="assignments">Assignments</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                {/* Search */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Search</label>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search technicians..."
                      value={filters.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>

                {/* Status Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Status</label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => handleFilterChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="on_leave">On Leave</SelectItem>
                      <SelectItem value="training">Training</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Department Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Department</label>
                  <Select
                    value={filters.department}
                    onValueChange={(value) => handleFilterChange('department', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All departments" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      <SelectItem value="Electrical Maintenance">Electrical Maintenance</SelectItem>
                      <SelectItem value="Field Operations">Field Operations</SelectItem>
                      <SelectItem value="Emergency Services">Emergency Services</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Region Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Region</label>
                  <Select
                    value={filters.region}
                    onValueChange={(value) => handleFilterChange('region', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All regions" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Regions</SelectItem>
                      <SelectItem value="Central">Central</SelectItem>
                      <SelectItem value="Amhara">Amhara</SelectItem>
                      <SelectItem value="Oromia">Oromia</SelectItem>
                      <SelectItem value="Eastern">Eastern</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Availability Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Availability</label>
                  <Select
                    value={filters.availability}
                    onValueChange={(value) => handleFilterChange('availability', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All availability" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Availability</SelectItem>
                      <SelectItem value="available">Available</SelectItem>
                      <SelectItem value="busy">Busy</SelectItem>
                      <SelectItem value="overloaded">Overloaded</SelectItem>
                      <SelectItem value="unavailable">Unavailable</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Results Summary */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>
              Showing {filteredTechnicians.length} of {technicians.length} technicians
            </span>
            {filters.search && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilterChange('search', '')}
              >
                Clear search
              </Button>
            )}
          </div>

          {/* Technicians Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredTechnicians.map((technician) => (
              <Card key={technician.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={technician.avatar} />
                        <AvatarFallback>
                          {technician.firstName[0]}{technician.lastName[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">
                          {technician.firstName} {technician.lastName}
                        </CardTitle>
                        <CardDescription>{technician.position}</CardDescription>
                        <div className="text-xs text-muted-foreground mt-1">
                          ID: {technician.employeeId}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1">
                      <Badge className={getStatusColor(technician.status)}>
                        {technician.status.replace('_', ' ').charAt(0).toUpperCase() +
                         technician.status.replace('_', ' ').slice(1)}
                      </Badge>
                      <Badge className={getAvailabilityColor(technician.workload.availability)}>
                        {technician.workload.availability.charAt(0).toUpperCase() +
                         technician.workload.availability.slice(1)}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Contact Info */}
                  <div className="grid grid-cols-1 gap-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="truncate">{technician.email}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{technician.phone}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{technician.location}, {technician.region}</span>
                    </div>
                  </div>

                  {/* Workload */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Workload</span>
                      <span className="font-medium">
                        {technician.workload.current}/{technician.workload.capacity}
                      </span>
                    </div>
                    <Progress
                      value={getWorkloadPercentage(technician.workload.current, technician.workload.capacity)}
                      className="h-2"
                    />
                  </div>

                  {/* Performance */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Rating</p>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        <span className="font-medium">{technician.performance.averageRating}</span>
                      </div>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Completed</p>
                      <p className="font-medium">{technician.performance.completedTasks}</p>
                    </div>
                  </div>

                  {/* Skills Preview */}
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">Top Skills</p>
                    <div className="flex flex-wrap gap-1">
                      {technician.skills.slice(0, 3).map((skill, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className={`text-xs ${getSkillLevelColor(skill.level)}`}
                        >
                          {skill.skill}
                        </Badge>
                      ))}
                      {technician.skills.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{technician.skills.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2 pt-2">
                    <Button size="sm" variant="outline" onClick={() => handleViewDetails(technician)}>
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                    {technician.workload.availability === 'available' && (
                      <Button size="sm" onClick={() => handleAssignTask(technician.id)}>
                        <Briefcase className="h-4 w-4 mr-2" />
                        Assign Task
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="technicians" className="space-y-4">
          {/* Technicians Table */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Technician</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Workload</TableHead>
                    <TableHead>Performance</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                          <span className="ml-2">Loading technicians...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredTechnicians.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                        No technicians found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredTechnicians.map((technician) => (
                      <TableRow key={technician.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={technician.avatar} />
                              <AvatarFallback className="text-xs">
                                {technician.firstName[0]}{technician.lastName[0]}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">
                                {technician.firstName} {technician.lastName}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {technician.employeeId}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{technician.position}</TableCell>
                        <TableCell>{technician.department}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium text-sm">{technician.location}</div>
                            <div className="text-xs text-muted-foreground">{technician.region}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(technician.status)}>
                            {technician.status.replace('_', ' ').charAt(0).toUpperCase() +
                             technician.status.replace('_', ' ').slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between text-sm">
                              <span>{technician.workload.current}/{technician.workload.capacity}</span>
                              <span className="text-muted-foreground">
                                {getWorkloadPercentage(technician.workload.current, technician.workload.capacity)}%
                              </span>
                            </div>
                            <Progress
                              value={getWorkloadPercentage(technician.workload.current, technician.workload.capacity)}
                              className="h-1"
                            />
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            <span className="font-medium">{technician.performance.averageRating}</span>
                            <span className="text-sm text-muted-foreground">
                              ({technician.performance.completedTasks})
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewDetails(technician)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Profile
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {technician.workload.availability === 'available' && (
                                <DropdownMenuItem onClick={() => handleAssignTask(technician.id)}>
                                  <Briefcase className="mr-2 h-4 w-4" />
                                  Assign Task
                                </DropdownMenuItem>
                              )}
                              {technician.status === 'active' && (
                                <DropdownMenuItem onClick={() => handleUpdateStatus(technician.id, 'inactive')}>
                                  <UserX className="mr-2 h-4 w-4" />
                                  Deactivate
                                </DropdownMenuItem>
                              )}
                              {technician.status === 'inactive' && (
                                <DropdownMenuItem onClick={() => handleUpdateStatus(technician.id, 'active')}>
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  Activate
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assignments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                Task Assignments
              </CardTitle>
              <CardDescription>
                Manage and track technician task assignments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Briefcase className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  Assignment Management
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Comprehensive task assignment system with workload balancing,
                  skill matching, and performance tracking.
                </p>
                <div className="text-sm text-gray-500">
                  Assignment management features coming soon
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Performance Analytics
              </CardTitle>
              <CardDescription>
                Technician performance metrics and analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <TrendingUp className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  Performance Dashboard
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Comprehensive performance analytics including completion rates,
                  quality metrics, safety scores, and skill development tracking.
                </p>
                <div className="text-sm text-gray-500">
                  Performance analytics features coming soon
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Technician Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Technician</DialogTitle>
            <DialogDescription>
              Add a new technician to the maintenance team.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">First Name</label>
                <Input placeholder="John" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Last Name</label>
                <Input placeholder="Doe" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Employee ID</label>
                <Input placeholder="EMP-XXX" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Email</label>
                <Input type="email" placeholder="<EMAIL>" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Phone</label>
                <Input placeholder="+251-911-XXXXXX" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Position</label>
                <Input placeholder="Maintenance Technician" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Department</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="electrical">Electrical Maintenance</SelectItem>
                    <SelectItem value="field">Field Operations</SelectItem>
                    <SelectItem value="emergency">Emergency Services</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Region</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="central">Central</SelectItem>
                    <SelectItem value="amhara">Amhara</SelectItem>
                    <SelectItem value="oromia">Oromia</SelectItem>
                    <SelectItem value="eastern">Eastern</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowAddDialog(false)}>
              Add Technician
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
