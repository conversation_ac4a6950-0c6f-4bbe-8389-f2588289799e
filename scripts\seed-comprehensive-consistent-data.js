/**
 * Comprehensive Consistent Data Seeding for EEU DTMS
 * This script creates realistic, consistent data across all tables
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function seedComprehensiveConsistentData() {
  let connection;
  
  try {
    console.log('🌱 SEEDING COMPREHENSIVE CONSISTENT DATA FOR EEU DTMS');
    console.log('=' .repeat(70));
    console.log('🏢 Ethiopian Electric Utility');
    console.log('🔌 Digital Transformer Management System');
    console.log('📅 Data Seeding Date:', new Date().toLocaleString());
    console.log('=' .repeat(70));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // Clear existing data safely
    console.log('\n🧹 Clearing existing data...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    await connection.execute('DELETE FROM app_alerts');
    await connection.execute('DELETE FROM app_maintenance_schedules');
    await connection.execute('DELETE FROM app_transformers');
    await connection.execute('ALTER TABLE app_transformers AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE app_maintenance_schedules AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE app_alerts AUTO_INCREMENT = 1');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    console.log('✅ Existing data cleared');

    // Get available regions
    const [regions] = await connection.execute('SELECT id, name, code FROM app_regions ORDER BY id');
    console.log(`📍 Found ${regions.length} regions available`);

    // 1. Seed Comprehensive Transformers (15 transformers across Ethiopia)
    console.log('\n⚡ SEEDING COMPREHENSIVE TRANSFORMERS');
    console.log('-' .repeat(50));
    
    const transformers = [
      // Addis Ababa Region (4 transformers)
      {
        serial_number: 'EEU-AA-001',
        name: 'Bole Main Distribution Transformer',
        type: 'distribution',
        capacity_kva: 1000.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'Siemens',
        model: 'GEAFOL Cast Resin',
        year_manufactured: 2020,
        installation_date: '2020-06-15',
        location_name: 'Bole Road, Near EEU Headquarters',
        latitude: 9.02220000,
        longitude: 38.74680000,
        region_id: regions.find(r => r.code === 'AA')?.id || 1,
        status: 'operational',
        efficiency_rating: 98.50,
        load_factor: 75.00,
        temperature: 65.00,
        oil_level: 95.00,
        last_maintenance: '2024-09-15',
        next_maintenance: '2024-12-15'
      },
      {
        serial_number: 'EEU-AA-002',
        name: 'Megenagna Distribution Transformer',
        type: 'distribution',
        capacity_kva: 500.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'ABB',
        model: 'UniGear ZS1',
        year_manufactured: 2019,
        installation_date: '2019-08-20',
        location_name: 'Megenagna, Addis Ababa',
        latitude: 9.02990000,
        longitude: 38.80790000,
        region_id: regions.find(r => r.code === 'AA')?.id || 1,
        status: 'warning',
        efficiency_rating: 97.80,
        load_factor: 88.00,
        temperature: 72.00,
        oil_level: 85.00,
        last_maintenance: '2024-08-10',
        next_maintenance: '2024-11-10'
      },
      {
        serial_number: 'EEU-AA-003',
        name: 'Bole Airport Transformer',
        type: 'distribution',
        capacity_kva: 1250.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'Siemens',
        model: 'GEAFOL',
        year_manufactured: 2022,
        installation_date: '2022-01-20',
        location_name: 'Bole International Airport',
        latitude: 8.98060000,
        longitude: 38.79920000,
        region_id: regions.find(r => r.code === 'AA')?.id || 1,
        status: 'operational',
        efficiency_rating: 99.50,
        load_factor: 70.00,
        temperature: 55.00,
        oil_level: 98.00,
        last_maintenance: '2024-10-01',
        next_maintenance: '2025-01-01'
      },
      {
        serial_number: 'EEU-AA-004',
        name: 'Merkato Commercial Transformer',
        type: 'distribution',
        capacity_kva: 630.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'ABB',
        model: 'UniGear ZS1',
        year_manufactured: 2020,
        installation_date: '2020-03-15',
        location_name: 'Merkato Commercial Area',
        latitude: 9.00840000,
        longitude: 38.75750000,
        region_id: regions.find(r => r.code === 'AA')?.id || 1,
        status: 'operational',
        efficiency_rating: 97.50,
        load_factor: 78.00,
        temperature: 66.00,
        oil_level: 91.00,
        last_maintenance: '2024-07-20',
        next_maintenance: '2024-10-20'
      },
      
      // Oromia Region (4 transformers)
      {
        serial_number: 'EEU-OR-001',
        name: 'Jimma Central Distribution Transformer',
        type: 'distribution',
        capacity_kva: 500.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'ABB',
        model: 'UniGear ZS1',
        year_manufactured: 2019,
        installation_date: '2019-08-20',
        location_name: 'Jimma City Center',
        latitude: 7.67810000,
        longitude: 36.83440000,
        region_id: regions.find(r => r.code === 'OR')?.id || 2,
        status: 'operational',
        efficiency_rating: 97.80,
        load_factor: 68.00,
        temperature: 62.00,
        oil_level: 92.00,
        last_maintenance: '2024-10-05',
        next_maintenance: '2025-01-05'
      },
      {
        serial_number: 'EEU-OR-002',
        name: 'Adama Industrial Transformer',
        type: 'distribution',
        capacity_kva: 800.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'Siemens',
        model: 'GEAFOL',
        year_manufactured: 2018,
        installation_date: '2018-11-12',
        location_name: 'Adama Industrial Park',
        latitude: 8.54000000,
        longitude: 39.26750000,
        region_id: regions.find(r => r.code === 'OR')?.id || 2,
        status: 'warning',
        efficiency_rating: 96.50,
        load_factor: 87.00,
        temperature: 74.00,
        oil_level: 88.00,
        last_maintenance: '2024-08-15',
        next_maintenance: '2024-11-15'
      },
      {
        serial_number: 'EEU-OR-003',
        name: 'Nekemte Distribution Transformer',
        type: 'distribution',
        capacity_kva: 400.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'Schneider Electric',
        model: 'Trihal',
        year_manufactured: 2019,
        installation_date: '2019-11-20',
        location_name: 'Nekemte City Center',
        latitude: 9.08330000,
        longitude: 36.55000000,
        region_id: regions.find(r => r.code === 'OR')?.id || 2,
        status: 'maintenance',
        efficiency_rating: 96.20,
        load_factor: 65.00,
        temperature: 58.00,
        oil_level: 83.00,
        last_maintenance: '2024-11-25',
        next_maintenance: '2024-12-25'
      },
      {
        serial_number: 'EEU-OR-004',
        name: 'Bishoftu Power Transformer',
        type: 'power',
        capacity_kva: 1500.00,
        voltage_primary: 132.00,
        voltage_secondary: 33.00,
        manufacturer: 'Siemens',
        model: 'GEAFOL',
        year_manufactured: 2021,
        installation_date: '2021-04-10',
        location_name: 'Bishoftu Industrial Zone',
        latitude: 8.75280000,
        longitude: 38.98330000,
        region_id: regions.find(r => r.code === 'OR')?.id || 2,
        status: 'operational',
        efficiency_rating: 99.10,
        load_factor: 72.00,
        temperature: 60.00,
        oil_level: 96.00,
        last_maintenance: '2024-09-10',
        next_maintenance: '2024-12-10'
      },
      
      // Amhara Region (3 transformers)
      {
        serial_number: 'EEU-AM-001',
        name: 'Bahir Dar Power Distribution Transformer',
        type: 'power',
        capacity_kva: 2000.00,
        voltage_primary: 132.00,
        voltage_secondary: 33.00,
        manufacturer: 'Schneider Electric',
        model: 'Trihal',
        year_manufactured: 2021,
        installation_date: '2021-03-10',
        location_name: 'Bahir Dar Industrial Zone',
        latitude: 11.59590000,
        longitude: 37.39060000,
        region_id: regions.find(r => r.code === 'AM')?.id || 3,
        status: 'operational',
        efficiency_rating: 99.20,
        load_factor: 82.00,
        temperature: 58.00,
        oil_level: 98.00,
        last_maintenance: '2024-11-20',
        next_maintenance: '2024-12-20'
      },
      {
        serial_number: 'EEU-AM-002',
        name: 'Gondar Distribution Transformer',
        type: 'distribution',
        capacity_kva: 315.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'ABB',
        model: 'UniGear ZS1',
        year_manufactured: 2019,
        installation_date: '2019-12-05',
        location_name: 'Gondar City Center',
        latitude: 12.60900000,
        longitude: 37.46470000,
        region_id: regions.find(r => r.code === 'AM')?.id || 3,
        status: 'operational',
        efficiency_rating: 97.00,
        load_factor: 72.00,
        temperature: 63.00,
        oil_level: 87.00,
        last_maintenance: '2024-08-05',
        next_maintenance: '2024-11-05'
      },
      {
        serial_number: 'EEU-AM-003',
        name: 'Debre Markos Transformer',
        type: 'distribution',
        capacity_kva: 315.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'Siemens',
        model: 'GEAFOL',
        year_manufactured: 2021,
        installation_date: '2021-05-10',
        location_name: 'Debre Markos Town',
        latitude: 10.35000000,
        longitude: 37.73330000,
        region_id: regions.find(r => r.code === 'AM')?.id || 3,
        status: 'operational',
        efficiency_rating: 98.10,
        load_factor: 69.00,
        temperature: 61.00,
        oil_level: 94.00,
        last_maintenance: '2024-09-10',
        next_maintenance: '2024-12-10'
      },
      
      // Tigray Region (2 transformers)
      {
        serial_number: 'EEU-TI-001',
        name: 'Mekelle Central Transformer',
        type: 'distribution',
        capacity_kva: 630.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'ABB',
        model: 'UniGear ZS1',
        year_manufactured: 2017,
        installation_date: '2017-05-08',
        location_name: 'Mekelle City Center',
        latitude: 13.49670000,
        longitude: 39.47530000,
        region_id: regions.find(r => r.code === 'TI')?.id || 4,
        status: 'critical',
        efficiency_rating: 94.20,
        load_factor: 95.00,
        temperature: 85.00,
        oil_level: 65.00,
        last_maintenance: '2024-07-15',
        next_maintenance: '2024-12-10'
      },
      {
        serial_number: 'EEU-TI-002',
        name: 'Adigrat Distribution Transformer',
        type: 'distribution',
        capacity_kva: 250.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'Schneider Electric',
        model: 'Trihal',
        year_manufactured: 2020,
        installation_date: '2020-08-15',
        location_name: 'Adigrat Town Center',
        latitude: 14.27000000,
        longitude: 39.46000000,
        region_id: regions.find(r => r.code === 'TI')?.id || 4,
        status: 'operational',
        efficiency_rating: 97.50,
        load_factor: 65.00,
        temperature: 59.00,
        oil_level: 89.00,
        last_maintenance: '2024-10-15',
        next_maintenance: '2025-01-15'
      },
      
      // SNNP Region (2 transformers)
      {
        serial_number: 'EEU-SN-001',
        name: 'Hawassa Distribution Transformer',
        type: 'distribution',
        capacity_kva: 400.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'Schneider Electric',
        model: 'Trihal',
        year_manufactured: 2020,
        installation_date: '2020-09-15',
        location_name: 'Hawassa City Center',
        latitude: 7.06210000,
        longitude: 38.47760000,
        region_id: regions.find(r => r.code === 'SN')?.id || 5,
        status: 'operational',
        efficiency_rating: 98.00,
        load_factor: 65.00,
        temperature: 60.00,
        oil_level: 90.00,
        last_maintenance: '2024-09-01',
        next_maintenance: '2024-12-01'
      },
      {
        serial_number: 'EEU-SN-002',
        name: 'Arba Minch Transformer',
        type: 'distribution',
        capacity_kva: 315.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'ABB',
        model: 'UniGear ZS1',
        year_manufactured: 2018,
        installation_date: '2018-07-20',
        location_name: 'Arba Minch Town',
        latitude: 6.03330000,
        longitude: 37.55000000,
        region_id: regions.find(r => r.code === 'SN')?.id || 5,
        status: 'warning',
        efficiency_rating: 96.80,
        load_factor: 84.00,
        temperature: 71.00,
        oil_level: 86.00,
        last_maintenance: '2024-06-20',
        next_maintenance: '2024-09-20'
      }
    ];

    // Insert transformers
    for (const transformer of transformers) {
      await connection.execute(`
        INSERT INTO app_transformers (
          serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
          manufacturer, model, year_manufactured, installation_date, location_name,
          latitude, longitude, region_id, status, efficiency_rating, load_factor,
          temperature, oil_level, last_maintenance, next_maintenance
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        transformer.serial_number, transformer.name, transformer.type, transformer.capacity_kva,
        transformer.voltage_primary, transformer.voltage_secondary, transformer.manufacturer,
        transformer.model, transformer.year_manufactured, transformer.installation_date,
        transformer.location_name, transformer.latitude, transformer.longitude,
        transformer.region_id, transformer.status, transformer.efficiency_rating,
        transformer.load_factor, transformer.temperature, transformer.oil_level,
        transformer.last_maintenance, transformer.next_maintenance
      ]);
    }
    console.log(`✅ Added ${transformers.length} transformers across ${regions.length} regions`);

    console.log('\n📊 TRANSFORMER DISTRIBUTION BY REGION:');
    const regionCounts = {};
    transformers.forEach(t => {
      const region = regions.find(r => r.id === t.region_id);
      if (region) {
        regionCounts[region.name] = (regionCounts[region.name] || 0) + 1;
      }
    });
    Object.entries(regionCounts).forEach(([region, count]) => {
      console.log(`  • ${region}: ${count} transformers`);
    });

    console.log('\n📈 TRANSFORMER STATUS DISTRIBUTION:');
    const statusCounts = {};
    transformers.forEach(t => {
      statusCounts[t.status] = (statusCounts[t.status] || 0) + 1;
    });
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  • ${status}: ${count} transformers`);
    });

    // 2. Seed Comprehensive Maintenance Schedules
    console.log('\n🔧 SEEDING COMPREHENSIVE MAINTENANCE SCHEDULES');
    console.log('-' .repeat(50));

    const maintenanceSchedules = [
      // Routine maintenance for all transformers
      { transformer_id: 1, type: 'routine', title: 'Monthly Visual Inspection - Bole Main', description: 'Regular monthly visual inspection including oil level, temperature readings, and connection integrity checks.', scheduled_date: '2024-12-30', estimated_duration: 2, priority: 'medium', status: 'scheduled' },
      { transformer_id: 2, type: 'preventive', title: 'Quarterly Maintenance - Megenagna', description: 'Comprehensive quarterly electrical testing including insulation resistance, turns ratio, and oil analysis.', scheduled_date: '2024-12-25', estimated_duration: 8, priority: 'high', status: 'in_progress' },
      { transformer_id: 3, type: 'routine', title: 'Monthly Inspection - Bole Airport', description: 'Critical infrastructure monthly inspection with enhanced security protocols.', scheduled_date: '2025-01-05', estimated_duration: 3, priority: 'high', status: 'scheduled' },
      { transformer_id: 4, type: 'routine', title: 'Weekly Check - Merkato Commercial', description: 'Weekly routine inspection for high-traffic commercial area transformer.', scheduled_date: '2024-12-28', estimated_duration: 1, priority: 'medium', status: 'scheduled' },

      // Oromia region maintenance
      { transformer_id: 5, type: 'routine', title: 'Monthly Inspection - Jimma Central', description: 'Monthly routine inspection and cleaning of transformer and surrounding area.', scheduled_date: '2025-01-10', estimated_duration: 2, priority: 'low', status: 'scheduled' },
      { transformer_id: 6, type: 'corrective', title: 'Temperature Investigation - Adama Industrial', description: 'Investigate high temperature readings and cooling system performance.', scheduled_date: '2024-12-20', estimated_duration: 6, priority: 'high', status: 'scheduled' },
      { transformer_id: 7, type: 'preventive', title: 'Oil Change - Nekemte', description: 'Complete oil change and filter replacement during maintenance window.', scheduled_date: '2024-12-26', estimated_duration: 12, priority: 'high', status: 'in_progress' },
      { transformer_id: 8, type: 'routine', title: 'Quarterly Check - Bishoftu Power', description: 'Quarterly inspection for power transformer with load testing.', scheduled_date: '2024-12-15', estimated_duration: 4, priority: 'medium', status: 'completed' },

      // Amhara region maintenance
      { transformer_id: 9, type: 'routine', title: 'Monthly Inspection - Bahir Dar Power', description: 'Monthly inspection of power distribution transformer.', scheduled_date: '2024-12-22', estimated_duration: 3, priority: 'medium', status: 'scheduled' },
      { transformer_id: 10, type: 'routine', title: 'Bi-weekly Check - Gondar', description: 'Bi-weekly routine maintenance check and cleaning.', scheduled_date: '2024-12-18', estimated_duration: 2, priority: 'low', status: 'completed' },
      { transformer_id: 11, type: 'routine', title: 'Monthly Check - Debre Markos', description: 'Monthly routine inspection and performance monitoring.', scheduled_date: '2024-12-12', estimated_duration: 2, priority: 'medium', status: 'completed' },

      // Tigray region maintenance
      { transformer_id: 12, type: 'emergency', title: 'Critical Temperature Issue - Mekelle', description: 'Emergency response to critical temperature alert and cooling system inspection.', scheduled_date: '2024-12-10', estimated_duration: 8, priority: 'critical', status: 'completed' },
      { transformer_id: 13, type: 'routine', title: 'Monthly Inspection - Adigrat', description: 'Monthly routine inspection and basic maintenance tasks.', scheduled_date: '2025-01-20', estimated_duration: 2, priority: 'medium', status: 'scheduled' },

      // SNNP region maintenance
      { transformer_id: 14, type: 'preventive', title: 'Annual Comprehensive Maintenance - Hawassa', description: 'Complete annual maintenance including oil change, gasket replacement, and full electrical testing.', scheduled_date: '2024-12-05', estimated_duration: 24, priority: 'high', status: 'completed' },
      { transformer_id: 15, type: 'corrective', title: 'Load Balancing - Arba Minch', description: 'Investigate and correct load balancing issues affecting performance.', scheduled_date: '2024-12-15', estimated_duration: 4, priority: 'medium', status: 'scheduled' }
    ];

    for (const schedule of maintenanceSchedules) {
      await connection.execute(`
        INSERT INTO app_maintenance_schedules (
          transformer_id, type, title, description, scheduled_date, estimated_duration, priority, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        schedule.transformer_id, schedule.type, schedule.title, schedule.description,
        schedule.scheduled_date, schedule.estimated_duration, schedule.priority, schedule.status
      ]);
    }
    console.log(`✅ Added ${maintenanceSchedules.length} maintenance schedules`);

    // 3. Seed Comprehensive Alerts
    console.log('\n🚨 SEEDING COMPREHENSIVE ALERTS');
    console.log('-' .repeat(50));

    const alerts = [
      // Temperature alerts
      { transformer_id: 2, title: 'High Temperature Alert - Megenagna', description: 'Transformer temperature has exceeded 70°C threshold. Immediate inspection recommended to prevent equipment damage.', severity: 'high', type: 'temperature', priority: 'high', status: 'active', is_resolved: false },
      { transformer_id: 6, title: 'High Temperature Warning - Adama Industrial', description: 'Transformer temperature approaching warning threshold at 74°C. Monitor closely.', severity: 'medium', type: 'temperature', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 12, title: 'Critical Temperature Alert - Mekelle', description: 'Transformer temperature reached critical level of 85°C. Emergency maintenance completed successfully.', severity: 'critical', type: 'temperature', priority: 'critical', status: 'resolved', is_resolved: true, resolved_at: '2024-12-10 14:30:00' },
      { transformer_id: 15, title: 'Temperature Warning - Arba Minch', description: 'Transformer temperature at 71°C, approaching warning threshold.', severity: 'medium', type: 'temperature', priority: 'medium', status: 'active', is_resolved: false },

      // Load alerts
      { transformer_id: 2, title: 'Overload Warning - Megenagna', description: 'Transformer is operating at 88% of capacity, exceeding the recommended 85% threshold for sustained operation.', severity: 'medium', type: 'load', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 6, title: 'High Load Factor - Adama Industrial', description: 'Industrial transformer operating at 87% capacity during peak hours. Consider load redistribution.', severity: 'medium', type: 'load', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 12, title: 'Critical Overload - Mekelle', description: 'Transformer operating at 95% capacity. Immediate load reduction required.', severity: 'high', type: 'load', priority: 'high', status: 'active', is_resolved: false },
      { transformer_id: 15, title: 'Load Monitoring - Arba Minch', description: 'Transformer load factor at 84%, approaching threshold. Monitor during peak hours.', severity: 'low', type: 'load', priority: 'low', status: 'active', is_resolved: false },

      // Maintenance alerts
      { transformer_id: 1, title: 'Routine Maintenance Due - Bole Main', description: 'Scheduled monthly maintenance is approaching. Please coordinate with maintenance team.', severity: 'low', type: 'maintenance', priority: 'low', status: 'active', is_resolved: false },
      { transformer_id: 12, title: 'Low Oil Level Warning - Mekelle', description: 'Transformer oil level has dropped to 65%, below the recommended minimum of 80%. Schedule oil top-up.', severity: 'medium', type: 'maintenance', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 4, title: 'Maintenance Overdue - Merkato', description: 'Scheduled maintenance is overdue by 5 days. Immediate attention required.', severity: 'high', type: 'maintenance', priority: 'high', status: 'active', is_resolved: false },
      { transformer_id: 7, title: 'Oil Change in Progress - Nekemte', description: 'Oil change maintenance currently in progress. Expected completion in 4 hours.', severity: 'low', type: 'maintenance', priority: 'low', status: 'investigating', is_resolved: false },

      // Voltage alerts
      { transformer_id: 9, title: 'Voltage Fluctuation - Bahir Dar', description: 'Primary voltage fluctuations detected. Monitoring for stability.', severity: 'medium', type: 'voltage', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 3, title: 'Voltage Stability - Bole Airport', description: 'Minor voltage variations detected during peak load. System stable.', severity: 'low', type: 'voltage', priority: 'low', status: 'monitoring', is_resolved: false },

      // Communication and system alerts
      { transformer_id: 14, title: 'Communication Test Alert - Hawassa', description: 'Testing alert system for critical infrastructure. System functioning normally.', severity: 'low', type: 'communication', priority: 'low', status: 'resolved', is_resolved: true, resolved_at: '2024-12-01 10:00:00' },
      { transformer_id: 8, title: 'System Health Check - Bishoftu', description: 'Automated system health check completed successfully. All parameters normal.', severity: 'low', type: 'communication', priority: 'low', status: 'resolved', is_resolved: true, resolved_at: '2024-12-15 09:00:00' },

      // Weather-related alerts
      { transformer_id: 5, title: 'Weather Monitoring - Jimma', description: 'Heavy rainfall expected. Monitor for potential electrical issues.', severity: 'low', type: 'weather', priority: 'low', status: 'active', is_resolved: false },
      { transformer_id: 13, title: 'Dust Storm Alert - Adigrat', description: 'Dust storm conditions may affect cooling efficiency. Monitor temperature closely.', severity: 'medium', type: 'weather', priority: 'medium', status: 'active', is_resolved: false }
    ];

    for (const alert of alerts) {
      await connection.execute(`
        INSERT INTO app_alerts (
          transformer_id, title, description, severity, type, priority, status, is_resolved, resolved_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        alert.transformer_id, alert.title, alert.description, alert.severity,
        alert.type, alert.priority, alert.status, alert.is_resolved,
        alert.resolved_at || null
      ]);
    }
    console.log(`✅ Added ${alerts.length} alerts across all severity levels`);

    // Final verification and summary
    const [finalTransformers] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    const [finalMaintenance] = await connection.execute('SELECT COUNT(*) as count FROM app_maintenance_schedules');
    const [finalAlerts] = await connection.execute('SELECT COUNT(*) as count FROM app_alerts');
    const [finalRegions] = await connection.execute('SELECT COUNT(*) as count FROM app_regions');

    console.log('\n' + '=' .repeat(70));
    console.log('🎉 COMPREHENSIVE CONSISTENT DATA SEEDING COMPLETED!');
    console.log('=' .repeat(70));

    console.log('\n📊 FINAL DATABASE SUMMARY:');
    console.log(`  ⚡ Transformers: ${finalTransformers[0].count} (across 5 Ethiopian regions)`);
    console.log(`  🔧 Maintenance Schedules: ${finalMaintenance[0].count} (various types and statuses)`);
    console.log(`  🚨 Alerts: ${finalAlerts[0].count} (multiple severity levels)`);
    console.log(`  🗺️  Regions: ${finalRegions[0].count} (Ethiopian administrative regions)`);

    // Show detailed status distribution
    const [statusStats] = await connection.execute(`
      SELECT
        status,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM app_transformers), 1) as percentage
      FROM app_transformers
      GROUP BY status
      ORDER BY count DESC
    `);

    console.log('\n📈 TRANSFORMER STATUS DISTRIBUTION:');
    statusStats.forEach(stat => {
      console.log(`  • ${stat.status}: ${stat.count} transformers (${stat.percentage}%)`);
    });

    // Show maintenance status distribution
    const [maintenanceStats] = await connection.execute(`
      SELECT
        status,
        COUNT(*) as count
      FROM app_maintenance_schedules
      GROUP BY status
      ORDER BY
        CASE status
          WHEN 'critical' THEN 1
          WHEN 'in_progress' THEN 2
          WHEN 'scheduled' THEN 3
          WHEN 'completed' THEN 4
        END
    `);

    console.log('\n🔧 MAINTENANCE STATUS DISTRIBUTION:');
    maintenanceStats.forEach(stat => {
      console.log(`  • ${stat.status}: ${stat.count} schedules`);
    });

    // Show alert severity distribution
    const [alertStats] = await connection.execute(`
      SELECT
        severity,
        COUNT(*) as total,
        SUM(CASE WHEN is_resolved = 0 OR is_resolved IS NULL THEN 1 ELSE 0 END) as active
      FROM app_alerts
      GROUP BY severity
      ORDER BY
        CASE severity
          WHEN 'critical' THEN 1
          WHEN 'high' THEN 2
          WHEN 'medium' THEN 3
          WHEN 'low' THEN 4
        END
    `);

    console.log('\n🚨 ALERT SEVERITY DISTRIBUTION:');
    alertStats.forEach(stat => {
      console.log(`  • ${stat.severity}: ${stat.active} active / ${stat.total} total`);
    });

    // Show regional distribution
    const [regionalStats] = await connection.execute(`
      SELECT
        r.name as region_name,
        r.code as region_code,
        COUNT(t.id) as transformer_count,
        SUM(CASE WHEN t.status = 'operational' THEN 1 ELSE 0 END) as operational_count
      FROM app_regions r
      LEFT JOIN app_transformers t ON r.id = t.region_id
      GROUP BY r.id, r.name, r.code
      HAVING transformer_count > 0
      ORDER BY transformer_count DESC
    `);

    console.log('\n🗺️  REGIONAL TRANSFORMER DISTRIBUTION:');
    regionalStats.forEach(region => {
      console.log(`  • ${region.region_name} (${region.region_code}): ${region.transformer_count} transformers, ${region.operational_count} operational`);
    });

    console.log('\n🎯 DASHBOARD NOW DISPLAYS:');
    console.log('  • 15 Real transformers across 5 Ethiopian regions');
    console.log('  • Comprehensive maintenance schedules with realistic timelines');
    console.log('  • Multi-level alert system with various severity types');
    console.log('  • Regional distribution showing actual Ethiopian locations');
    console.log('  • Performance metrics calculated from real transformer data');
    console.log('  • Interactive charts with meaningful data distributions');
    console.log('  • Status indicators reflecting actual operational states');
    console.log('  • Maintenance tracking with proper workflow statuses');

    console.log('\n📱 ACCESS YOUR ENHANCED DASHBOARD:');
    console.log('  🌐 URL: http://localhost:3002/dashboard');
    console.log('  👤 Login: <EMAIL> / password123');
    console.log('  📊 Data: Comprehensive and consistent across all components');
    console.log('  🔄 Updates: Real-time data refresh from populated database');

    console.log('\n🌟 DASHBOARD FEATURES NOW ENHANCED:');
    console.log('  ✅ Rich transformer data with realistic performance metrics');
    console.log('  ✅ Comprehensive maintenance scheduling across all regions');
    console.log('  ✅ Multi-type alert system (temperature, load, maintenance, voltage, weather)');
    console.log('  ✅ Regional distribution charts with meaningful data');
    console.log('  ✅ Status distribution pie charts with real percentages');
    console.log('  ✅ Performance analytics with actual efficiency ratings');
    console.log('  ✅ Interactive progress bars with real load factors');
    console.log('  ✅ Maintenance workflow tracking with proper statuses');

    console.log('\n🏆 MISSION ACCOMPLISHED:');
    console.log('  Your EEU DTMS now has comprehensive, consistent data');
    console.log('  that provides meaningful insights and realistic dashboard displays!');

    console.log('\n✅ Comprehensive consistent data seeded successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding comprehensive data:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { seedComprehensiveConsistentData };

// Run if called directly
if (require.main === module) {
  seedComprehensiveConsistentData();
}
