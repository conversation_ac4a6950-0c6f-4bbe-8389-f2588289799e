/**
 * Setup EEU Maintenance Schedules
 * This script creates maintenance schedule templates and recurring tasks
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

// EEU Maintenance Schedule Templates
const maintenanceTemplates = [
  // Routine Maintenance (Monthly)
  {
    type: 'routine',
    title: 'Monthly Visual Inspection',
    description: 'Visual inspection of transformer exterior, connections, and surrounding area. Check for oil leaks, unusual sounds, and proper ventilation.',
    frequency_days: 30,
    estimated_duration: 2,
    priority: 'medium',
    required_skills: ['visual_inspection', 'basic_electrical'],
    safety_requirements: ['safety_helmet', 'safety_vest', 'insulated_gloves']
  },
  
  // Preventive Maintenance (Quarterly)
  {
    type: 'preventive',
    title: 'Quarterly Electrical Testing',
    description: 'Comprehensive electrical testing including insulation resistance, turns ratio, and load tap changer operation. Oil sampling and analysis.',
    frequency_days: 90,
    estimated_duration: 8,
    priority: 'high',
    required_skills: ['electrical_testing', 'oil_analysis', 'advanced_diagnostics'],
    safety_requirements: ['safety_helmet', 'safety_vest', 'insulated_gloves', 'arc_flash_suit', 'lockout_tagout']
  },
  
  // Annual Maintenance
  {
    type: 'preventive',
    title: 'Annual Comprehensive Maintenance',
    description: 'Complete transformer maintenance including oil change, gasket replacement, cooling system service, and full electrical testing.',
    frequency_days: 365,
    estimated_duration: 24,
    priority: 'critical',
    required_skills: ['electrical_testing', 'mechanical_maintenance', 'oil_handling', 'cooling_systems'],
    safety_requirements: ['safety_helmet', 'safety_vest', 'insulated_gloves', 'arc_flash_suit', 'lockout_tagout', 'confined_space']
  },
  
  // Emergency Response
  {
    type: 'emergency',
    title: 'Emergency Response Protocol',
    description: 'Immediate response to transformer alarms, faults, or emergency conditions. Includes safety assessment and temporary measures.',
    frequency_days: 0, // On-demand
    estimated_duration: 4,
    priority: 'critical',
    required_skills: ['emergency_response', 'fault_diagnosis', 'safety_assessment'],
    safety_requirements: ['safety_helmet', 'safety_vest', 'insulated_gloves', 'emergency_kit']
  }
];

// Ethiopian seasonal considerations
const seasonalMaintenanceSchedules = [
  {
    season: 'dry_season',
    months: [10, 11, 12, 1, 2, 3], // October to March
    additional_tasks: [
      'Dust cleaning and inspection',
      'Cooling system efficiency check',
      'Increased load monitoring'
    ]
  },
  {
    season: 'rainy_season',
    months: [4, 5, 6, 7, 8, 9], // April to September
    additional_tasks: [
      'Moisture ingress inspection',
      'Drainage system check',
      'Lightning protection verification',
      'Grounding system testing'
    ]
  }
];

async function setupMaintenanceSchedules() {
  let connection;
  
  try {
    console.log('🔧 Setting up EEU Maintenance Schedules...');
    
    connection = await mysql.createConnection(config);
    
    // Create maintenance templates table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_maintenance_templates (
        id INT PRIMARY KEY AUTO_INCREMENT,
        type ENUM('routine', 'preventive', 'corrective', 'emergency', 'seasonal') NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        frequency_days INT DEFAULT 0,
        estimated_duration INT NOT NULL,
        priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        required_skills JSON,
        safety_requirements JSON,
        seasonal_months JSON,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Insert maintenance templates
    for (const template of maintenanceTemplates) {
      await connection.execute(`
        INSERT INTO app_maintenance_templates (
          type, title, description, frequency_days, estimated_duration,
          priority, required_skills, safety_requirements, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        template.type, template.title, template.description,
        template.frequency_days, template.estimated_duration, template.priority,
        JSON.stringify(template.required_skills),
        JSON.stringify(template.safety_requirements)
      ]);
    }

    // Create seasonal maintenance schedules
    for (const seasonal of seasonalMaintenanceSchedules) {
      await connection.execute(`
        INSERT INTO app_maintenance_templates (
          type, title, description, frequency_days, estimated_duration,
          priority, seasonal_months, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        'seasonal',
        `${seasonal.season.replace('_', ' ').toUpperCase()} Maintenance`,
        `Seasonal maintenance tasks for ${seasonal.season}: ${seasonal.additional_tasks.join(', ')}`,
        0, // Seasonal, not frequency-based
        6,
        'medium',
        JSON.stringify(seasonal.months)
      ]);
    }

    // Get all transformers and create initial schedules
    const [transformers] = await connection.execute('SELECT id, name FROM app_transformers');
    
    console.log(`📊 Creating schedules for ${transformers.length} transformers...`);
    
    for (const transformer of transformers) {
      // Create routine monthly schedule
      await connection.execute(`
        INSERT INTO app_maintenance_schedules (
          transformer_id, type, title, description, scheduled_date,
          estimated_duration, priority, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, DATE_ADD(CURDATE(), INTERVAL 7 DAY), ?, ?, ?, NOW(), NOW())
      `, [
        transformer.id, 'routine',
        `Monthly Inspection - ${transformer.name}`,
        'Scheduled monthly visual inspection and basic checks',
        2, 'medium', 'scheduled'
      ]);

      // Create quarterly preventive schedule
      await connection.execute(`
        INSERT INTO app_maintenance_schedules (
          transformer_id, type, title, description, scheduled_date,
          estimated_duration, priority, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, DATE_ADD(CURDATE(), INTERVAL 30 DAY), ?, ?, ?, NOW(), NOW())
      `, [
        transformer.id, 'preventive',
        `Quarterly Maintenance - ${transformer.name}`,
        'Comprehensive quarterly electrical testing and oil analysis',
        8, 'high', 'scheduled'
      ]);
    }

    // Show summary
    const [templateCount] = await connection.execute('SELECT COUNT(*) as count FROM app_maintenance_templates');
    const [scheduleCount] = await connection.execute('SELECT COUNT(*) as count FROM app_maintenance_schedules');
    const [upcomingCount] = await connection.execute(`
      SELECT COUNT(*) as count FROM app_maintenance_schedules 
      WHERE scheduled_date >= CURDATE() AND status = 'scheduled'
    `);
    
    console.log('\n📊 Maintenance Setup Summary:');
    console.log(`  - Templates Created: ${templateCount[0].count}`);
    console.log(`  - Schedules Created: ${scheduleCount[0].count}`);
    console.log(`  - Upcoming Tasks: ${upcomingCount[0].count}`);
    
    console.log('\n🔧 Maintenance Types:');
    console.log('  • Routine: Monthly visual inspections');
    console.log('  • Preventive: Quarterly electrical testing');
    console.log('  • Annual: Comprehensive maintenance');
    console.log('  • Emergency: On-demand response');
    console.log('  • Seasonal: Weather-specific tasks');
    
    console.log('\n📅 Ethiopian Seasonal Considerations:');
    console.log('  • Dry Season (Oct-Mar): Dust control, cooling efficiency');
    console.log('  • Rainy Season (Apr-Sep): Moisture protection, lightning safety');
    
  } catch (error) {
    console.error('❌ Error setting up maintenance schedules:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { setupMaintenanceSchedules, maintenanceTemplates };

// Run if called directly
if (require.main === module) {
  setupMaintenanceSchedules();
}
