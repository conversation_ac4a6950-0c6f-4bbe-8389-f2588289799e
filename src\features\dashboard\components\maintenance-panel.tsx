"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Progress } from "@/src/components/ui/progress"
import { 
  Wrench, 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  User,
  MapPin,
  Plus,
  Eye
} from "lucide-react"

export function MaintenancePanel() {
  const maintenanceTasks = [
    {
      id: 1,
      title: "Routine Inspection - T-001",
      type: "Preventive",
      priority: "High",
      assignee: "<PERSON>",
      location: "Addis Ababa - Bole",
      dueDate: "2024-01-15",
      progress: 75,
      status: "in-progress"
    },
    {
      id: 2,
      title: "Oil Analysis - T-045",
      type: "Diagnostic",
      priority: "Medium",
      assignee: "<PERSON>",
      location: "Dire Dawa - Industrial",
      dueDate: "2024-01-18",
      progress: 30,
      status: "scheduled"
    },
    {
      id: 3,
      title: "Cooling System Repair - T-023",
      type: "Corrective",
      priority: "Critical",
      assignee: "<PERSON>",
      location: "Bahir Dar - Central",
      dueDate: "2024-01-12",
      progress: 100,
      status: "completed"
    },
    {
      id: 4,
      title: "Bushing Replacement - T-067",
      type: "Corrective",
      priority: "High",
      assignee: "Sarah Wilson",
      location: "Mekelle - North",
      dueDate: "2024-01-20",
      progress: 0,
      status: "pending"
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge variant="outline" className="text-green-600">Completed</Badge>
      case "in-progress":
        return <Badge variant="default">In Progress</Badge>
      case "scheduled":
        return <Badge variant="secondary">Scheduled</Badge>
      case "pending":
        return <Badge variant="outline">Pending</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "Critical":
        return <Badge variant="destructive">Critical</Badge>
      case "High":
        return <Badge variant="destructive" className="bg-orange-500">High</Badge>
      case "Medium":
        return <Badge variant="secondary">Medium</Badge>
      case "Low":
        return <Badge variant="outline">Low</Badge>
      default:
        return <Badge variant="outline">Normal</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "in-progress":
        return <Clock className="h-4 w-4 text-blue-500" />
      case "scheduled":
        return <Calendar className="h-4 w-4 text-yellow-500" />
      case "pending":
        return <AlertCircle className="h-4 w-4 text-gray-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            Maintenance Tasks
          </div>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Task
          </Button>
        </CardTitle>
        <CardDescription>
          Scheduled and ongoing maintenance activities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {maintenanceTasks.map((task) => (
            <div key={task.id} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  {getStatusIcon(task.status)}
                  <h4 className="font-medium">{task.title}</h4>
                </div>
                <div className="flex items-center gap-2">
                  {getPriorityBadge(task.priority)}
                  {getStatusBadge(task.status)}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {task.assignee}
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {task.location}
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  Due: {task.dueDate}
                </div>
                <div className="text-xs">
                  Type: {task.type}
                </div>
              </div>
              
              {task.status !== "completed" && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Progress</span>
                    <span>{task.progress}%</span>
                  </div>
                  <Progress value={task.progress} className="h-2" />
                </div>
              )}
            </div>
          ))}
        </div>
        
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View All Tasks
            </Button>
            <div className="text-sm text-muted-foreground">
              {maintenanceTasks.filter(task => task.status === 'in-progress').length} active tasks
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
