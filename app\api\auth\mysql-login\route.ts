import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Mock user data for demonstration
const mockUsers = {
  "<EMAIL>": {
    id: "user-001",
    name: "Admin User",
    email: "<EMAIL>",
    role: "super_admin",
    organizationalLevel: "head_office",
    permissions: [
      { resource: "users", action: "create" },
      { resource: "users", action: "read" },
      { resource: "users", action: "update" },
      { resource: "users", action: "delete" },
      // Super admin has all permissions
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=AU",
    phone: "+251-911-123-456",
    department: "IT Administration",
    location: "Addis Ababa",
    bio: "System administrator responsible for managing the EEU Transformer Management System.",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "user-002",
    name: "Asset Manager",
    email: "<EMAIL>",
    role: "national_asset_manager",
    organizationalLevel: "head_office",
    permissions: [
      { resource: "transformers", action: "read" },
      { resource: "transformers", action: "update" },
      { resource: "transformers", action: "approve" },
      { resource: "reports", action: "export" },
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=AM",
    phone: "+251-911-234-567",
    department: "Asset Management",
    location: "Addis Ababa",
    bio: "National asset manager responsible for overseeing transformer assets across the country.",
    createdAt: "2023-01-15T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "user-003",
    name: "Regional Admin",
    email: "<EMAIL>",
    role: "regional_admin",
    organizationalLevel: "regional_office",
    regionId: "region-001", // Addis Ababa
    permissions: [
      { resource: "users", action: "create" },
      { resource: "users", action: "read" },
      { resource: "users", action: "update" },
      { resource: "transformers", action: "read" },
      { resource: "transformers", action: "update" },
      { resource: "maintenance", action: "assign" },
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=RA",
    phone: "+251-911-345-678",
    department: "Regional Administration",
    location: "Addis Ababa",
    bio: "Regional administrator for the Addis Ababa region, managing local operations and staff.",
    createdAt: "2023-02-01T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "user-004",
    name: "Service Center Manager",
    email: "<EMAIL>",
    role: "service_center_manager",
    organizationalLevel: "service_center",
    regionId: "region-001", // Addis Ababa
    serviceCenterId: "sc-001", // Bole Service Center
    permissions: [
      { resource: "transformers", action: "read" },
      { resource: "transformers", action: "update" },
      { resource: "maintenance", action: "assign" },
      { resource: "maintenance", action: "approve" },
      { resource: "reports", action: "export" },
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=SM",
    phone: "+251-911-456-789",
    department: "Service Operations",
    location: "Bole, Addis Ababa",
    bio: "Service center manager for the Bole district, overseeing local maintenance and service operations.",
    createdAt: "2023-02-15T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "user-005",
    name: "Field Technician",
    email: "<EMAIL>",
    role: "field_technician",
    organizationalLevel: "service_center",
    regionId: "region-001", // Addis Ababa
    serviceCenterId: "sc-001", // Bole Service Center
    permissions: [
      { resource: "transformers", action: "read" },
      { resource: "transformers", action: "update" },
      { resource: "maintenance", action: "update" },
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=FT",
    phone: "+251-911-567-890",
    department: "Field Operations",
    location: "Bole, Addis Ababa",
    bio: "Field technician responsible for on-site maintenance and repair of transformers.",
    createdAt: "2023-03-01T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "user-006",
    name: "Customer Service",
    email: "<EMAIL>",
    role: "customer_service_agent",
    organizationalLevel: "service_center",
    regionId: "region-001", // Addis Ababa
    serviceCenterId: "sc-001", // Bole Service Center
    permissions: [
      { resource: "outages", action: "read" },
      { resource: "customer_requests", action: "create" },
      { resource: "customer_requests", action: "read" },
      { resource: "customer_requests", action: "update" },
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=CS",
    phone: "+251-911-678-901",
    department: "Customer Support",
    location: "Bole, Addis Ababa",
    bio: "Customer service agent handling customer inquiries and service requests.",
    createdAt: "2023-03-15T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
};

export async function POST(request: NextRequest) {
  console.log('MySQL login API route called');

  try {
    // Log request headers
    console.log('Request headers:', Object.fromEntries(request.headers.entries()));

    const body = await request.json();
    console.log('Request body:', body);

    const { email, password } = body;
    console.log('MySQL login API called with email:', email);

    if (!email) {
      console.log('Email is required');
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Log available users
    console.log('Available users:', Object.keys(mockUsers));

    // For demo purposes, any password is valid
    const user = mockUsers[email];

    if (!user) {
      console.log('User not found for email:', email);
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    console.log('User found:', user);

    // Set auth cookie
    const cookieStore = cookies();
    cookieStore.set({
      name: 'eeu_user',
      value: 'true',
      httpOnly: true,
      path: '/',
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60 * 24 * 7, // 7 days
    });

    console.log('Auth cookie set');

    // Create response
    const response = NextResponse.json({
      user,
      message: 'Login successful'
    });

    // Log response
    console.log('Response created with status 200');

    return response;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'An error occurred during login' },
      { status: 500 }
    );
  }
}
