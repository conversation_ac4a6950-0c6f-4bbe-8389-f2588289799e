/**
 * Authentication Types
 * Type definitions for authentication, authorization, and user management
 */

import { BaseEntity, Permission, Role } from './common.types'

// User types
export interface User extends BaseEntity {
  email: string
  username?: string
  firstName: string
  lastName: string
  fullName: string
  avatar?: string
  phone?: string
  department?: string
  position?: string
  employeeId?: string
  status: UserStatus
  emailVerified: boolean
  phoneVerified: boolean
  lastLoginAt?: Date | string
  passwordChangedAt?: Date | string
  preferences: UserPreferences
  roles: UserRole[]
  permissions: Permission[]
  region?: string
  serviceCenter?: string
}

export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending_verification'

export interface UserRole {
  id: string
  roleId: string
  role: Role
  assignedAt: Date | string
  assignedBy: string
  expiresAt?: Date | string
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: 'en' | 'am'
  timezone: string
  dateFormat: string
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
    inApp: boolean
  }
  dashboard: {
    layout: 'grid' | 'list'
    widgets: string[]
    refreshInterval: number
  }
}

// Authentication types
export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
  captcha?: string
}

export interface RegisterData {
  email: string
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  phone?: string
  department?: string
  position?: string
  employeeId?: string
  region?: string
  serviceCenter?: string
  termsAccepted: boolean
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
  tokenType: 'Bearer'
}

export interface AuthSession {
  user: User
  tokens: AuthTokens
  isAuthenticated: boolean
  expiresAt: Date
}

// Password types
export interface PasswordChangeRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface PasswordResetRequest {
  email: string
  captcha?: string
}

export interface PasswordResetConfirm {
  token: string
  newPassword: string
  confirmPassword: string
}

export interface PasswordPolicy {
  minLength: number
  requireUppercase: boolean
  requireLowercase: boolean
  requireNumbers: boolean
  requireSpecialChars: boolean
  maxAge: number
  historyCount: number
}

// Role and Permission types
export interface RoleDefinition extends BaseEntity {
  name: string
  displayName: string
  description?: string
  hierarchy: number
  isSystem: boolean
  permissions: RolePermission[]
  userCount?: number
}

export interface RolePermission {
  id: string
  roleId: string
  permissionId: string
  permission: PermissionDefinition
  conditions?: Record<string, any>
  grantedAt: Date | string
  grantedBy: string
}

export interface PermissionDefinition extends BaseEntity {
  resource: string
  action: string
  displayName: string
  description?: string
  isSystem: boolean
  conditions?: PermissionCondition[]
}

export interface PermissionCondition {
  field: string
  operator: 'eq' | 'ne' | 'in' | 'nin' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains'
  value: any
  description?: string
}

// OAuth types
export interface OAuthProvider {
  id: string
  name: string
  displayName: string
  icon: string
  enabled: boolean
  clientId: string
  scopes: string[]
  redirectUri: string
}

export interface OAuthProfile {
  id: string
  email: string
  name: string
  firstName?: string
  lastName?: string
  avatar?: string
  provider: string
  providerId: string
}

// Security types
export interface SecurityEvent extends BaseEntity {
  userId?: string
  type: SecurityEventType
  description: string
  ipAddress: string
  userAgent: string
  location?: {
    country?: string
    region?: string
    city?: string
  }
  severity: 'low' | 'medium' | 'high' | 'critical'
  resolved: boolean
  resolvedAt?: Date | string
  resolvedBy?: string
}

export type SecurityEventType = 
  | 'login_success'
  | 'login_failure'
  | 'logout'
  | 'password_change'
  | 'password_reset'
  | 'account_locked'
  | 'account_unlocked'
  | 'permission_denied'
  | 'suspicious_activity'
  | 'data_breach_attempt'

export interface LoginAttempt {
  id: string
  email: string
  ipAddress: string
  userAgent: string
  success: boolean
  failureReason?: string
  timestamp: Date | string
  location?: {
    country?: string
    region?: string
    city?: string
  }
}

export interface AccountLockout {
  id: string
  userId: string
  reason: string
  lockedAt: Date | string
  expiresAt: Date | string
  unlocked: boolean
  unlockedAt?: Date | string
  unlockedBy?: string
}

// Session types
export interface SessionInfo {
  id: string
  userId: string
  ipAddress: string
  userAgent: string
  location?: {
    country?: string
    region?: string
    city?: string
  }
  createdAt: Date | string
  lastActivityAt: Date | string
  expiresAt: Date | string
  active: boolean
}

// Two-Factor Authentication types
export interface TwoFactorAuth {
  enabled: boolean
  method: 'sms' | 'email' | 'app'
  backupCodes: string[]
  lastUsedAt?: Date | string
}

export interface TwoFactorSetup {
  secret: string
  qrCode: string
  backupCodes: string[]
}

export interface TwoFactorVerification {
  code: string
  method: 'sms' | 'email' | 'app' | 'backup'
}

// API Key types
export interface ApiKey extends BaseEntity {
  name: string
  key: string
  userId: string
  permissions: Permission[]
  lastUsedAt?: Date | string
  expiresAt?: Date | string
  active: boolean
  ipWhitelist?: string[]
}

export interface ApiKeyRequest {
  name: string
  permissions: Permission[]
  expiresAt?: Date | string
  ipWhitelist?: string[]
}

// Auth context types
export interface AuthContextValue {
  user: User | null
  session: AuthSession | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>
  register: (data: RegisterData) => Promise<void>
  refreshToken: () => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<void>
  changePassword: (data: PasswordChangeRequest) => Promise<void>
  resetPassword: (data: PasswordResetRequest) => Promise<void>
  confirmPasswordReset: (data: PasswordResetConfirm) => Promise<void>
  hasPermission: (resource: string, action: string) => boolean
  hasRole: (roleName: string) => boolean
  checkPermissions: (permissions: Permission[]) => boolean
}

// Auth hook types
export interface UseAuthReturn extends AuthContextValue {}

export interface UsePermissionsReturn {
  hasPermission: (resource: string, action: string) => boolean
  hasRole: (roleName: string) => boolean
  checkPermissions: (permissions: Permission[]) => boolean
  userPermissions: Permission[]
  userRoles: Role[]
}

// Form validation types
export interface AuthFormErrors {
  email?: string
  password?: string
  confirmPassword?: string
  firstName?: string
  lastName?: string
  phone?: string
  currentPassword?: string
  newPassword?: string
  general?: string
}

export interface AuthFormState {
  values: Record<string, any>
  errors: AuthFormErrors
  touched: Record<string, boolean>
  isSubmitting: boolean
  isValid: boolean
}

// Utility types for auth
export type AuthAction = 
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: AuthSession }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'REFRESH_TOKEN_SUCCESS'; payload: AuthTokens }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'SET_LOADING'; payload: boolean }

export interface AuthState {
  user: User | null
  session: AuthSession | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

// Export utility functions
export const createInitialAuthState = (): AuthState => ({
  user: null,
  session: null,
  isAuthenticated: false,
  isLoading: true,
  error: null
})

export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return Date.now() >= payload.exp * 1000
  } catch {
    return true
  }
}

export const hasPermission = (
  userPermissions: Permission[],
  resource: string,
  action: string
): boolean => {
  return userPermissions.some(
    permission => permission.resource === resource && permission.action === action
  )
}

export const hasRole = (userRoles: Role[], roleName: string): boolean => {
  return userRoles.some(role => role.name === roleName)
}

export const checkPermissions = (
  userPermissions: Permission[],
  requiredPermissions: Permission[]
): boolean => {
  return requiredPermissions.every(required =>
    hasPermission(userPermissions, required.resource, required.action)
  )
}
