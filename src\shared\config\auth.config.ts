/**
 * Authentication Configuration
 * Configuration for authentication, authorization, and security
 */

export interface AuthConfig {
  providers: {
    local: boolean
    oauth: {
      google?: boolean
      microsoft?: boolean
      github?: boolean
    }
  }
  session: {
    strategy: 'jwt' | 'database'
    maxAge: number
    updateAge: number
    cookieName: string
    secure: boolean
    httpOnly: boolean
    sameSite: 'strict' | 'lax' | 'none'
  }
  jwt: {
    secret: string
    algorithm: string
    expiresIn: string
    refreshExpiresIn: string
    issuer: string
    audience: string
  }
  password: {
    minLength: number
    requireUppercase: boolean
    requireLowercase: boolean
    requireNumbers: boolean
    requireSpecialChars: boolean
    specialChars: string
    maxAge: number // Password expiry in days
    historyCount: number // Number of previous passwords to remember
  }
  security: {
    maxLoginAttempts: number
    lockoutDuration: number // in milliseconds
    rateLimiting: {
      windowMs: number
      maxRequests: number
    }
    csrfProtection: boolean
    corsOrigins: string[]
  }
  roles: {
    default: string
    hierarchy: Record<string, number>
  }
  permissions: {
    resources: string[]
    actions: string[]
  }
}

// Default authentication configuration
export const AUTH_CONFIG: AuthConfig = {
  providers: {
    local: true,
    oauth: {
      google: false,
      microsoft: false,
      github: false
    }
  },
  
  session: {
    strategy: 'jwt',
    maxAge: 8 * 60 * 60, // 8 hours in seconds
    updateAge: 24 * 60 * 60, // 24 hours in seconds
    cookieName: 'eeu_session',
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'strict'
  },
  
  jwt: {
    secret: process.env.JWT_SECRET || 'eeu-transformer-management-secret-key',
    algorithm: 'HS256',
    expiresIn: '8h',
    refreshExpiresIn: '7d',
    issuer: 'eeu-dtms',
    audience: 'eeu-users'
  },
  
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
    maxAge: 90, // 90 days
    historyCount: 5 // Remember last 5 passwords
  },
  
  security: {
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    rateLimiting: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100
    },
    csrfProtection: true,
    corsOrigins: [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'https://eeu-dtms.vercel.app'
    ]
  },
  
  roles: {
    default: 'field_technician',
    hierarchy: {
      super_admin: 100,
      national_asset_manager: 90,
      national_maintenance_manager: 85,
      regional_admin: 80,
      regional_asset_manager: 70,
      regional_maintenance_engineer: 60,
      service_center_manager: 50,
      field_technician: 40,
      viewer: 30,
      guest: 10
    }
  },
  
  permissions: {
    resources: [
      'dashboard',
      'transformers',
      'maintenance',
      'users',
      'roles',
      'reports',
      'alerts',
      'settings',
      'analytics',
      'exports',
      'imports',
      'notifications',
      'audit_logs',
      'system_health'
    ],
    actions: [
      'create',
      'read',
      'update',
      'delete',
      'export',
      'import',
      'approve',
      'reject',
      'assign',
      'unassign',
      'schedule',
      'cancel',
      'complete',
      'archive',
      'restore'
    ]
  }
}

// Role-based permissions matrix
export const ROLE_PERMISSIONS = {
  super_admin: {
    dashboard: ['read'],
    transformers: ['create', 'read', 'update', 'delete', 'export', 'import'],
    maintenance: ['create', 'read', 'update', 'delete', 'schedule', 'approve', 'assign'],
    users: ['create', 'read', 'update', 'delete', 'assign', 'unassign'],
    roles: ['create', 'read', 'update', 'delete'],
    reports: ['create', 'read', 'update', 'delete', 'export'],
    alerts: ['create', 'read', 'update', 'delete'],
    settings: ['read', 'update'],
    analytics: ['read', 'export'],
    exports: ['create', 'read'],
    imports: ['create', 'read'],
    notifications: ['create', 'read', 'update', 'delete'],
    audit_logs: ['read', 'export'],
    system_health: ['read']
  },
  
  national_asset_manager: {
    dashboard: ['read'],
    transformers: ['create', 'read', 'update', 'export'],
    maintenance: ['read', 'schedule', 'approve'],
    users: ['read'],
    reports: ['create', 'read', 'export'],
    alerts: ['read', 'update'],
    analytics: ['read', 'export'],
    exports: ['create', 'read'],
    notifications: ['read']
  },
  
  national_maintenance_manager: {
    dashboard: ['read'],
    transformers: ['read', 'update'],
    maintenance: ['create', 'read', 'update', 'schedule', 'approve', 'assign'],
    users: ['read'],
    reports: ['create', 'read', 'export'],
    alerts: ['create', 'read', 'update'],
    analytics: ['read'],
    exports: ['create', 'read'],
    notifications: ['read']
  },
  
  regional_admin: {
    dashboard: ['read'],
    transformers: ['create', 'read', 'update'],
    maintenance: ['create', 'read', 'update', 'schedule', 'assign'],
    users: ['read', 'update'],
    reports: ['create', 'read', 'export'],
    alerts: ['create', 'read', 'update'],
    analytics: ['read'],
    exports: ['create', 'read'],
    notifications: ['read']
  },
  
  regional_asset_manager: {
    dashboard: ['read'],
    transformers: ['read', 'update'],
    maintenance: ['read', 'schedule'],
    reports: ['create', 'read', 'export'],
    alerts: ['read', 'update'],
    analytics: ['read'],
    exports: ['create', 'read'],
    notifications: ['read']
  },
  
  regional_maintenance_engineer: {
    dashboard: ['read'],
    transformers: ['read', 'update'],
    maintenance: ['create', 'read', 'update', 'complete'],
    reports: ['create', 'read'],
    alerts: ['create', 'read', 'update'],
    notifications: ['read']
  },
  
  service_center_manager: {
    dashboard: ['read'],
    transformers: ['read', 'update'],
    maintenance: ['create', 'read', 'update', 'assign'],
    reports: ['create', 'read'],
    alerts: ['create', 'read'],
    notifications: ['read']
  },
  
  field_technician: {
    dashboard: ['read'],
    transformers: ['read'],
    maintenance: ['read', 'update', 'complete'],
    reports: ['create', 'read'],
    alerts: ['create', 'read'],
    notifications: ['read']
  },
  
  viewer: {
    dashboard: ['read'],
    transformers: ['read'],
    maintenance: ['read'],
    reports: ['read'],
    alerts: ['read'],
    notifications: ['read']
  },
  
  guest: {
    dashboard: ['read'],
    transformers: ['read'],
    reports: ['read']
  }
} as const

// OAuth provider configurations
export const OAUTH_PROVIDERS = {
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID || '',
    clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
    scope: ['openid', 'email', 'profile'],
    redirectUri: '/api/auth/callback/google'
  },
  
  microsoft: {
    clientId: process.env.MICROSOFT_CLIENT_ID || '',
    clientSecret: process.env.MICROSOFT_CLIENT_SECRET || '',
    scope: ['openid', 'email', 'profile'],
    redirectUri: '/api/auth/callback/microsoft'
  },
  
  github: {
    clientId: process.env.GITHUB_CLIENT_ID || '',
    clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
    scope: ['user:email'],
    redirectUri: '/api/auth/callback/github'
  }
}

// Password validation regex patterns
export const PASSWORD_PATTERNS = {
  uppercase: /[A-Z]/,
  lowercase: /[a-z]/,
  numbers: /[0-9]/,
  specialChars: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/,
  minLength: (length: number) => new RegExp(`^.{${length},}$`)
}

// Security headers configuration
export const SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
}

// Get authentication configuration based on environment
export const getAuthConfig = (): AuthConfig => {
  const environment = process.env.NODE_ENV || 'development'
  
  const config = { ...AUTH_CONFIG }
  
  if (environment === 'production') {
    config.session.secure = true
    config.security.corsOrigins = [
      'https://eeu-dtms.vercel.app',
      'https://eeu.gov.et'
    ]
  }
  
  if (environment === 'test') {
    config.session.maxAge = 60 // 1 minute for tests
    config.security.maxLoginAttempts = 3
    config.security.lockoutDuration = 5000 // 5 seconds
  }
  
  return config
}

// Validate authentication configuration
export const validateAuthConfig = (config: AuthConfig): boolean => {
  if (!config.jwt.secret || config.jwt.secret.length < 32) {
    throw new Error('JWT secret must be at least 32 characters long')
  }
  
  if (config.password.minLength < 8) {
    throw new Error('Minimum password length must be at least 8 characters')
  }
  
  if (config.security.maxLoginAttempts < 1) {
    throw new Error('Maximum login attempts must be at least 1')
  }
  
  return true
}

// Export default configuration
export default getAuthConfig()
