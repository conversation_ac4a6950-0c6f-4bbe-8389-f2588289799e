import { NextRequest, NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'eeu_dtms',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
}

// GET - Fetch transformer history cards
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const transformerId = searchParams.get('transformerId')
    const cardId = searchParams.get('cardId')

    const connection = await mysql.createConnection(dbConfig)

    let query = `
      SELECT 
        hc.*,
        t.serial_number as transformer_serial,
        t.manufacturer as transformer_manufacturer,
        t.capacity as transformer_capacity
      FROM transformer_history_cards hc
      LEFT JOIN transformers t ON hc.transformer_id = t.id
    `
    const params: any[] = []

    if (cardId) {
      query += ' WHERE hc.id = ?'
      params.push(cardId)
    } else if (transformerId) {
      query += ' WHERE hc.transformer_id = ?'
      params.push(transformerId)
    }

    query += ' ORDER BY hc.created_at DESC'

    const [historyCards] = await connection.execute(query, params)

    // Fetch related inspections and tests for each history card
    const enrichedCards = await Promise.all(
      (historyCards as any[]).map(async (card) => {
        // Fetch inspections
        const [inspections] = await connection.execute(
          'SELECT * FROM transformer_inspections WHERE history_card_id = ? ORDER BY inspection_date DESC',
          [card.id]
        )

        // Fetch inspection components
        const [components] = await connection.execute(
          'SELECT * FROM transformer_inspection_components WHERE history_card_id = ?',
          [card.id]
        )

        // Fetch megger tests
        const [meggerTests] = await connection.execute(
          'SELECT * FROM transformer_megger_tests WHERE history_card_id = ? ORDER BY test_date DESC',
          [card.id]
        )

        // Fetch attachments
        const [attachments] = await connection.execute(
          'SELECT * FROM transformer_history_attachments WHERE history_card_id = ? ORDER BY uploaded_at DESC',
          [card.id]
        )

        return {
          ...card,
          inspections,
          components,
          meggerTests,
          attachments
        }
      })
    )

    await connection.end()

    return NextResponse.json({
      success: true,
      historyCards: enrichedCards,
      count: enrichedCards.length
    })

  } catch (error) {
    console.error('❌ Error fetching history cards:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch history cards',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST - Create new transformer history card
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      transformerId,
      cardNo,
      substationName,
      feederName,
      transformerCode,
      kvaRating,
      primaryVoltage,
      region,
      district,
      specificLocation,
      gpsLocation,
      manufacturer,
      yearOfManufacturing,
      serialNo,
      installationDate,
      changingDate,
      changingReason,
      customerType,
      customerName,
      subCity,
      kebele,
      constructionType,
      deliveryDate,
      responsiblePerson,
      signature,
      inspections = [],
      testResults = {}
    } = body

    const connection = await mysql.createConnection(dbConfig)
    await connection.beginTransaction()

    try {
      // Insert main history card record
      const [historyCardResult] = await connection.execute(
        `INSERT INTO transformer_history_cards (
          transformer_id, card_no, substation_name, feeder_name, transformer_code,
          kva_rating, primary_voltage, region, district, specific_location,
          gps_location, manufacturer, year_of_manufacturing, serial_no,
          installation_date, changing_date, changing_reason, customer_type,
          customer_name, sub_city, kebele, construction_type, delivery_date,
          responsible_person, signature
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          transformerId, cardNo, substationName, feederName, transformerCode,
          kvaRating, primaryVoltage, region, district, specificLocation,
          gpsLocation, manufacturer, yearOfManufacturing, serialNo,
          installationDate, changingDate, changingReason, customerType,
          customerName, subCity, kebele, constructionType, deliveryDate,
          responsiblePerson, signature
        ]
      )

      const historyCardId = (historyCardResult as any).insertId

      // Insert inspection records
      if (inspections && inspections.length > 0) {
        for (const inspection of inspections) {
          await connection.execute(
            `INSERT INTO transformer_inspections (
              history_card_id, inspection_date, inspector_name, arrestor_status,
              dropout_fuse_status, bushing_status, ground_status, oil_level,
              leakage_status, cable_rating, transformer_load_percentage, remarks,
              inspector_signature
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              historyCardId, inspection.inspectionDate, inspection.inspector,
              inspection.arrestor, inspection.dropout, inspection.bushing,
              inspection.ground, inspection.oilLevel, inspection.leakage,
              inspection.cableRating, inspection.transformerLoad, inspection.remarks,
              inspection.signature
            ]
          )
        }
      }

      // Insert megger test results
      if (testResults && Object.keys(testResults).length > 0) {
        await connection.execute(
          `INSERT INTO transformer_megger_tests (
            history_card_id, test_date, region, district, service_type,
            work_order_no, specific_location, gps_location, manufacturer,
            voltage_level, capacity_kva, serial_no, rs_ohmic_value,
            ht_to_ground_value, lt_to_ground_value, oil_insulation_condition,
            inspected_by, checked_by, approved_by, remarks
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            historyCardId, testResults.date, testResults.region, testResults.district,
            testResults.serviceType, testResults.workOrder, testResults.specificLocation,
            testResults.gpsLocation, testResults.manufacturer, testResults.voltageLevel,
            testResults.capacity, testResults.serialNo, testResults.ohmicValue,
            testResults.htToGround, testResults.ltToGround, testResults.oilInsulation,
            testResults.inspectedBy, testResults.checkedBy, testResults.approvedBy,
            testResults.remarks
          ]
        )
      }

      await connection.commit()
      await connection.end()

      return NextResponse.json({
        success: true,
        message: 'History card created successfully',
        historyCardId
      })

    } catch (error) {
      await connection.rollback()
      await connection.end()
      throw error
    }

  } catch (error) {
    console.error('❌ Error creating history card:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create history card',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// PUT - Update transformer history card
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'History card ID is required' },
        { status: 400 }
      )
    }

    const connection = await mysql.createConnection(dbConfig)

    // Build dynamic update query
    const updateFields = Object.keys(updateData).filter(key => 
      !['inspections', 'testResults', 'components', 'attachments'].includes(key)
    )
    
    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid fields to update' },
        { status: 400 }
      )
    }

    const setClause = updateFields.map(field => `${field} = ?`).join(', ')
    const values = updateFields.map(field => updateData[field])
    values.push(id)

    await connection.execute(
      `UPDATE transformer_history_cards SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      values
    )

    await connection.end()

    return NextResponse.json({
      success: true,
      message: 'History card updated successfully'
    })

  } catch (error) {
    console.error('❌ Error updating history card:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update history card',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// DELETE - Delete transformer history card
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'History card ID is required' },
        { status: 400 }
      )
    }

    const connection = await mysql.createConnection(dbConfig)

    // Delete history card (cascading deletes will handle related records)
    const [result] = await connection.execute(
      'DELETE FROM transformer_history_cards WHERE id = ?',
      [id]
    )

    await connection.end()

    if ((result as any).affectedRows === 0) {
      return NextResponse.json(
        { success: false, error: 'History card not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'History card deleted successfully'
    })

  } catch (error) {
    console.error('❌ Error deleting history card:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete history card',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
