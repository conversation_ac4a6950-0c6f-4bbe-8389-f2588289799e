/**
 * Comprehensive Data Seeding for EEU DTMS
 * Seeds all tables with realistic Ethiopian Electric Utility data
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function seedComprehensiveData() {
  let connection;
  
  try {
    console.log('🌱 SEEDING COMPREHENSIVE DATA FOR EEU DTMS');
    console.log('=' .repeat(60));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // Clear existing data
    console.log('\n🧹 Clearing existing data...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    
    const tables = [
      'app_notifications', 'app_outages', 'app_performance_metrics', 'app_weather_data',
      'app_alerts', 'app_maintenance_schedules', 'app_transformers', 
      'app_service_centers', 'app_users', 'app_regions', 'app_system_settings'
    ];
    
    for (const table of tables) {
      await connection.execute(`DELETE FROM ${table}`);
      await connection.execute(`ALTER TABLE ${table} AUTO_INCREMENT = 1`);
    }
    
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    console.log('✅ Existing data cleared');

    // 1. Seed Regions
    console.log('\n🗺️  Seeding Ethiopian Regions...');
    const regions = [
      { name: 'Addis Ababa', code: 'AA', population: 3500000, area_km2: 527.0, capital_city: 'Addis Ababa', coordinates: JSON.stringify({lat: 9.0222, lng: 38.7468}) },
      { name: 'Oromia', code: 'OR', population: 37000000, area_km2: 353006.81, capital_city: 'Adama', coordinates: JSON.stringify({lat: 8.5400, lng: 39.2675}) },
      { name: 'Amhara', code: 'AM', population: 21000000, area_km2: 154708.96, capital_city: 'Bahir Dar', coordinates: JSON.stringify({lat: 11.5959, lng: 37.3906}) },
      { name: 'Tigray', code: 'TI', population: 5500000, area_km2: 50078.64, capital_city: 'Mekelle', coordinates: JSON.stringify({lat: 13.4967, lng: 39.4753}) },
      { name: 'SNNP', code: 'SN', population: 20000000, area_km2: 105887.18, capital_city: 'Hawassa', coordinates: JSON.stringify({lat: 7.0621, lng: 38.4776}) },
      { name: 'Somali', code: 'SO', population: 5500000, area_km2: 279252.0, capital_city: 'Jijiga', coordinates: JSON.stringify({lat: 9.3500, lng: 42.8000}) },
      { name: 'Afar', code: 'AF', population: 1800000, area_km2: 96707.0, capital_city: 'Semera', coordinates: JSON.stringify({lat: 11.7943, lng: 41.0058}) },
      { name: 'Benishangul-Gumuz', code: 'BG', population: 1100000, area_km2: 50699.0, capital_city: 'Assosa', coordinates: JSON.stringify({lat: 10.0667, lng: 34.5333}) },
      { name: 'Gambela', code: 'GA', population: 435000, area_km2: 25802.0, capital_city: 'Gambela', coordinates: JSON.stringify({lat: 8.2500, lng: 34.5833}) },
      { name: 'Harari', code: 'HA', population: 250000, area_km2: 311.25, capital_city: 'Harar', coordinates: JSON.stringify({lat: 9.3100, lng: 42.1200}) },
      { name: 'Dire Dawa', code: 'DD', population: 500000, area_km2: 1213.0, capital_city: 'Dire Dawa', coordinates: JSON.stringify({lat: 9.5900, lng: 41.8500}) }
    ];

    for (const region of regions) {
      await connection.execute(`
        INSERT INTO app_regions (name, code, population, area_km2, capital_city, coordinates)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [region.name, region.code, region.population, region.area_km2, region.capital_city, region.coordinates]);
    }
    console.log(`✅ Seeded ${regions.length} regions`);

    // 2. Seed Service Centers
    console.log('\n🏢 Seeding Service Centers...');
    const serviceCenters = [
      { name: 'Addis Ababa Main Service Center', code: 'AA-MAIN', region_id: 1, address: 'Bole Road, Addis Ababa', latitude: 9.0222, longitude: 38.7468, phone: '+251-11-123-4567', email: '<EMAIL>' },
      { name: 'Addis Ababa East Service Center', code: 'AA-EAST', region_id: 1, address: 'Megenagna, Addis Ababa', latitude: 9.0299, longitude: 38.8079, phone: '+251-11-123-4568', email: '<EMAIL>' },
      { name: 'Adama Service Center', code: 'OR-ADAMA', region_id: 2, address: 'Adama City Center', latitude: 8.5400, longitude: 39.2675, phone: '+251-22-123-4567', email: '<EMAIL>' },
      { name: 'Jimma Service Center', code: 'OR-JIMMA', region_id: 2, address: 'Jimma City Center', latitude: 7.6781, longitude: 36.8344, phone: '+251-47-123-4567', email: '<EMAIL>' },
      { name: 'Bahir Dar Service Center', code: 'AM-BAHIR', region_id: 3, address: 'Bahir Dar City Center', latitude: 11.5959, longitude: 37.3906, phone: '+251-58-123-4567', email: '<EMAIL>' },
      { name: 'Gondar Service Center', code: 'AM-GONDAR', region_id: 3, address: 'Gondar City Center', latitude: 12.6090, longitude: 37.4647, phone: '+251-58-123-4568', email: '<EMAIL>' },
      { name: 'Mekelle Service Center', code: 'TI-MEKELLE', region_id: 4, address: 'Mekelle City Center', latitude: 13.4967, longitude: 39.4753, phone: '+251-34-123-4567', email: '<EMAIL>' },
      { name: 'Hawassa Service Center', code: 'SN-HAWASSA', region_id: 5, address: 'Hawassa City Center', latitude: 7.0621, longitude: 38.4776, phone: '+251-46-123-4567', email: '<EMAIL>' }
    ];

    for (const center of serviceCenters) {
      await connection.execute(`
        INSERT INTO app_service_centers (name, code, region_id, address, latitude, longitude, phone, email, capacity)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [center.name, center.code, center.region_id, center.address, center.latitude, center.longitude, center.phone, center.email, 100]);
    }
    console.log(`✅ Seeded ${serviceCenters.length} service centers`);

    // 3. Seed Users
    console.log('\n👥 Seeding Users...');
    const users = [
      { email: '<EMAIL>', name: 'System Administrator', role: 'super_admin', department: 'IT', region_id: 1, phone: '+251-911-000-001' },
      { email: '<EMAIL>', name: 'Ato Belete Molla', role: 'super_admin', department: 'Executive', region_id: 1, phone: '+251-911-000-002' },
      { email: '<EMAIL>', name: 'Eng. Alemayehu Worku', role: 'national_asset_manager', department: 'Asset Management', region_id: 1, phone: '+251-911-000-003' },
      { email: '<EMAIL>', name: 'Eng. Tigist Mengistu', role: 'national_maintenance_manager', department: 'Maintenance', region_id: 1, phone: '+251-911-000-004' },
      { email: '<EMAIL>', name: 'W/ro Hanan Ahmed', role: 'regional_admin', department: 'Regional Operations', region_id: 1, phone: '+251-911-000-010' },
      { email: '<EMAIL>', name: 'Ato Tadesse Bekele', role: 'regional_admin', department: 'Regional Operations', region_id: 2, phone: '+251-911-000-011' },
      { email: '<EMAIL>', name: 'Ato Mulugeta Haile', role: 'regional_admin', department: 'Regional Operations', region_id: 3, phone: '+251-911-000-012' },
      { email: '<EMAIL>', name: 'Ato Dawit Tesfaye', role: 'field_technician', department: 'Field Operations', region_id: 1, service_center_id: 1, phone: '+251-911-000-020' },
      { email: '<EMAIL>', name: 'Ato Mohammed Ali', role: 'field_technician', department: 'Field Operations', region_id: 2, service_center_id: 4, phone: '+251-911-000-021' },
      { email: '<EMAIL>', name: 'Ato Yohannes Alemu', role: 'field_technician', department: 'Field Operations', region_id: 3, service_center_id: 5, phone: '+251-911-000-022' }
    ];

    for (const user of users) {
      const preferences = JSON.stringify({
        theme: 'light',
        language: 'en',
        notifications: true,
        dashboardLayout: 'default'
      });
      
      await connection.execute(`
        INSERT INTO app_users (email, name, role, department, region_id, service_center_id, phone, preferences, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [user.email, user.name, user.role, user.department, user.region_id, user.service_center_id || null, user.phone, preferences, true]);
    }
    console.log(`✅ Seeded ${users.length} users`);

    // 4. Seed Transformers
    console.log('\n⚡ Seeding Transformers...');
    const transformers = [
      { serial_number: 'EEU-AA-001', name: 'Bole Main Distribution Transformer', type: 'distribution', capacity_kva: 1000, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2020, installation_date: '2020-06-15', location_name: 'Bole Road, Near EEU Headquarters', latitude: 9.0222, longitude: 38.7468, region_id: 1, service_center_id: 1, status: 'operational', efficiency_rating: 98.5, load_factor: 75.0, temperature: 65.0, oil_level: 95.0, health_index: 92.0, customer_type: 'commercial' },
      { serial_number: 'EEU-AA-002', name: 'Megenagna Distribution Transformer', type: 'distribution', capacity_kva: 500, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2019, installation_date: '2019-08-20', location_name: 'Megenagna, Addis Ababa', latitude: 9.0299, longitude: 38.8079, region_id: 1, service_center_id: 2, status: 'warning', efficiency_rating: 97.8, load_factor: 88.0, temperature: 72.0, oil_level: 85.0, health_index: 78.0, customer_type: 'residential' },
      { serial_number: 'EEU-OR-001', name: 'Jimma Central Distribution Transformer', type: 'distribution', capacity_kva: 500, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2019, installation_date: '2019-08-20', location_name: 'Jimma City Center, Main Square', latitude: 7.6781, longitude: 36.8344, region_id: 2, service_center_id: 4, status: 'operational', efficiency_rating: 97.8, load_factor: 68.0, temperature: 62.0, oil_level: 92.0, health_index: 85.0, customer_type: 'commercial' },
      { serial_number: 'EEU-AM-001', name: 'Bahir Dar Power Distribution Transformer', type: 'power', capacity_kva: 2000, voltage_primary: 132, voltage_secondary: 33, manufacturer: 'Schneider Electric', model: 'Trihal', year_manufactured: 2021, installation_date: '2021-03-10', location_name: 'Bahir Dar Industrial Zone', latitude: 11.5959, longitude: 37.3906, region_id: 3, service_center_id: 5, status: 'maintenance', efficiency_rating: 99.2, load_factor: 45.0, temperature: 58.0, oil_level: 98.0, health_index: 95.0, customer_type: 'industrial' },
      { serial_number: 'EEU-OR-002', name: 'Adama Industrial Transformer', type: 'distribution', capacity_kva: 800, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2018, installation_date: '2018-11-12', location_name: 'Adama Industrial Park', latitude: 8.5400, longitude: 39.2675, region_id: 2, service_center_id: 3, status: 'operational', efficiency_rating: 96.5, load_factor: 82.0, temperature: 68.0, oil_level: 88.0, health_index: 80.0, customer_type: 'industrial' },
      { serial_number: 'EEU-TI-001', name: 'Mekelle Central Transformer', type: 'distribution', capacity_kva: 630, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2017, installation_date: '2017-05-08', location_name: 'Mekelle City Center', latitude: 13.4967, longitude: 39.4753, region_id: 4, service_center_id: 7, status: 'critical', efficiency_rating: 94.2, load_factor: 95.0, temperature: 85.0, oil_level: 65.0, health_index: 45.0, customer_type: 'residential' },
      { serial_number: 'EEU-SN-001', name: 'Hawassa Distribution Transformer', type: 'distribution', capacity_kva: 400, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Schneider Electric', model: 'Trihal', year_manufactured: 2020, installation_date: '2020-09-15', location_name: 'Hawassa City Center', latitude: 7.0621, longitude: 38.4776, region_id: 5, service_center_id: 8, status: 'operational', efficiency_rating: 98.0, load_factor: 65.0, temperature: 60.0, oil_level: 90.0, health_index: 88.0, customer_type: 'commercial' },
      { serial_number: 'EEU-AA-003', name: 'Bole Airport Transformer', type: 'distribution', capacity_kva: 1250, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2022, installation_date: '2022-01-20', location_name: 'Bole International Airport', latitude: 8.9806, longitude: 38.7992, region_id: 1, service_center_id: 1, status: 'operational', efficiency_rating: 99.5, load_factor: 70.0, temperature: 55.0, oil_level: 98.0, health_index: 98.0, customer_type: 'commercial', criticality: 'critical' }
    ];

    for (const transformer of transformers) {
      await connection.execute(`
        INSERT INTO app_transformers (
          serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
          manufacturer, model, year_manufactured, installation_date, location_name,
          latitude, longitude, region_id, service_center_id, status, efficiency_rating,
          load_factor, temperature, oil_level, health_index, customer_type, criticality
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        transformer.serial_number, transformer.name, transformer.type, transformer.capacity_kva,
        transformer.voltage_primary, transformer.voltage_secondary, transformer.manufacturer,
        transformer.model, transformer.year_manufactured, transformer.installation_date,
        transformer.location_name, transformer.latitude, transformer.longitude,
        transformer.region_id, transformer.service_center_id, transformer.status,
        transformer.efficiency_rating, transformer.load_factor, transformer.temperature,
        transformer.oil_level, transformer.health_index, transformer.customer_type,
        transformer.criticality || 'medium'
      ]);
    }
    console.log(`✅ Seeded ${transformers.length} transformers`);

    // 5. Seed Maintenance Schedules
    console.log('\n🔧 Seeding Maintenance Schedules...');
    const maintenanceSchedules = [
      { transformer_id: 1, type: 'routine', title: 'Monthly Visual Inspection - Bole Main', description: 'Regular monthly visual inspection and basic checks', scheduled_date: '2024-12-30', estimated_duration: 2, priority: 'medium', status: 'scheduled', technician_id: 8 },
      { transformer_id: 2, type: 'preventive', title: 'Quarterly Maintenance - Megenagna', description: 'Comprehensive quarterly electrical testing and oil analysis', scheduled_date: '2024-12-25', estimated_duration: 8, priority: 'high', status: 'in_progress', technician_id: 8, supervisor_id: 5 },
      { transformer_id: 3, type: 'routine', title: 'Monthly Inspection - Jimma Central', description: 'Monthly routine inspection and cleaning', scheduled_date: '2024-12-20', estimated_duration: 2, priority: 'low', status: 'completed', technician_id: 9, completion_date: '2024-12-20', actual_duration: 2 },
      { transformer_id: 4, type: 'corrective', title: 'Oil Level Restoration - Bahir Dar', description: 'Restore oil level and check for leaks', scheduled_date: '2024-12-15', estimated_duration: 4, priority: 'high', status: 'scheduled', technician_id: 10, supervisor_id: 7 },
      { transformer_id: 6, type: 'emergency', title: 'Critical Temperature Issue - Mekelle', description: 'Emergency response to critical temperature alert', scheduled_date: '2024-12-10', estimated_duration: 6, priority: 'critical', status: 'completed', technician_id: 10, completion_date: '2024-12-10', actual_duration: 8 },
      { transformer_id: 5, type: 'preventive', title: 'Annual Comprehensive Maintenance - Adama', description: 'Complete annual maintenance including oil change and testing', scheduled_date: '2025-01-15', estimated_duration: 24, priority: 'high', status: 'scheduled', technician_id: 9, supervisor_id: 6 }
    ];

    for (const schedule of maintenanceSchedules) {
      await connection.execute(`
        INSERT INTO app_maintenance_schedules (
          transformer_id, type, title, description, scheduled_date, estimated_duration,
          priority, status, technician_id, supervisor_id, completion_date, actual_duration
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        schedule.transformer_id, schedule.type, schedule.title, schedule.description,
        schedule.scheduled_date, schedule.estimated_duration, schedule.priority,
        schedule.status, schedule.technician_id, schedule.supervisor_id || null,
        schedule.completion_date || null, schedule.actual_duration || null
      ]);
    }
    console.log(`✅ Seeded ${maintenanceSchedules.length} maintenance schedules`);

    // 6. Seed Alerts
    console.log('\n🚨 Seeding Alerts...');
    const alerts = [
      { transformer_id: 2, title: 'High Temperature Alert', description: 'Transformer temperature has exceeded 70°C. Immediate inspection recommended.', severity: 'high', type: 'temperature', priority: 'high', threshold_value: 70.0, actual_value: 72.0, created_by: 8 },
      { transformer_id: 2, title: 'Overload Warning', description: 'Transformer is operating at 88% of capacity, exceeding the recommended 85% threshold.', severity: 'medium', type: 'load', priority: 'medium', threshold_value: 85.0, actual_value: 88.0, created_by: 8 },
      { transformer_id: 6, title: 'Critical Temperature Alert', description: 'Transformer temperature at critical level - immediate action required', severity: 'critical', type: 'temperature', priority: 'critical', threshold_value: 80.0, actual_value: 85.0, created_by: 10, assigned_to: 10, status: 'resolved', resolved_at: '2024-12-10 14:30:00', resolved_by: 10, is_resolved: true },
      { transformer_id: 6, title: 'Low Oil Level Warning', description: 'Transformer oil level below recommended minimum', severity: 'medium', type: 'maintenance', priority: 'medium', threshold_value: 80.0, actual_value: 65.0, created_by: 10, assigned_to: 10 },
      { transformer_id: 5, title: 'High Load Factor Alert', description: 'Transformer operating at high load factor, monitoring required', severity: 'medium', type: 'load', priority: 'medium', threshold_value: 80.0, actual_value: 82.0, created_by: 9 },
      { transformer_id: 8, title: 'Communication Test Alert', description: 'Testing alert system for critical infrastructure', severity: 'low', type: 'communication', priority: 'low', created_by: 1, status: 'resolved', resolved_at: '2024-12-01 10:00:00', resolved_by: 1, is_resolved: true }
    ];

    for (const alert of alerts) {
      await connection.execute(`
        INSERT INTO app_alerts (
          transformer_id, title, description, severity, type, priority, threshold_value,
          actual_value, created_by, assigned_to, status, resolved_at, resolved_by, is_resolved
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        alert.transformer_id, alert.title, alert.description, alert.severity,
        alert.type, alert.priority, alert.threshold_value || null, alert.actual_value || null,
        alert.created_by, alert.assigned_to || null, alert.status || 'active',
        alert.resolved_at || null, alert.resolved_by || null, alert.is_resolved || false
      ]);
    }
    console.log(`✅ Seeded ${alerts.length} alerts`);

    console.log('\n✅ Comprehensive data seeding completed!');
    console.log('📊 Summary:');
    console.log(`  • Regions: ${regions.length}`);
    console.log(`  • Service Centers: ${serviceCenters.length}`);
    console.log(`  • Users: ${users.length}`);
    console.log(`  • Transformers: ${transformers.length}`);
    console.log(`  • Maintenance Schedules: ${maintenanceSchedules.length}`);
    console.log(`  • Alerts: ${alerts.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding data:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Export for use in other scripts
module.exports = { seedComprehensiveData };

// Run if called directly
if (require.main === module) {
  seedComprehensiveData();
}
