/**
 * MySQL Server-side Service
 *
 * This service handles MySQL operations on the server side only.
 * It should only be imported in API routes or server components.
 */

import mysql from 'mysql2/promise';

// Database configuration
const DB_CONFIG = {
  host: process.env.DB_HOST || process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || process.env.MYSQL_PORT || '3306'),
  user: process.env.DB_USER || process.env.MYSQL_USER || 'root',
  password: process.env.DB_PASSWORD || process.env.MYSQL_PASSWORD || '',
  database: process.env.DB_NAME || process.env.MYSQL_DATABASE || 'dtms_eeu_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// Connection pool
let pool: mysql.Pool | null = null;

/**
 * Initialize MySQL connection pool
 */
function initializePool(): mysql.Pool {
  if (!pool) {
    pool = mysql.createPool(DB_CONFIG);
  }
  return pool;
}

/**
 * Execute a MySQL query
 */
async function executeQuery<T = any>(query: string, params: any[] = []): Promise<T> {
  const connection = initializePool();

  try {
    const [results] = await connection.execute(query, params);
    return results as T;
  } catch (error) {
    console.error('❌ MySQL query error:', error);
    console.error('Query:', query.substring(0, 100) + '...');
    throw error;
  }
}

export class MySQLServerService {

  /**
   * Test connection
   */
  static async testConnection(): Promise<boolean> {
    try {
      await executeQuery('SELECT 1 as test');
      return true;
    } catch (error) {
      console.error('MySQL connection test failed:', error);
      return false;
    }
  }

  /**
   * Get all transformers
   */
  static async getTransformers() {
    try {
      const transformers = await executeQuery<any[]>(`
        SELECT
          t.*,
          r.name as region_name,
          r.code as region_code,
          sc.name as service_center_name
        FROM app_transformers t
        LEFT JOIN app_regions r ON t.region_id = r.id
        LEFT JOIN app_service_centers sc ON t.service_center_id = sc.id
        ORDER BY t.created_at DESC
      `);

      return transformers.map(t => ({
        id: t.id,
        createdAt: t.created_at,
        updatedAt: t.updated_at || t.created_at,
        serialNumber: t.serial_number,
        name: t.name,
        status: t.status,
        type: t.type,
        manufacturer: t.manufacturer,
        model: t.model,
        manufactureDate: t.year_manufactured,
        installationDate: t.installation_date,
        lastMaintenanceDate: t.last_maintenance,
        nextMaintenanceDate: t.next_maintenance,
        capacity: t.capacity_kva,
        capacity_kva: t.capacity_kva,
        voltage: {
          primary: t.voltage_primary,
          secondary: t.voltage_secondary
        },
        voltage_primary: t.voltage_primary,
        voltage_secondary: t.voltage_secondary,
        regionId: t.region_id,
        serviceCenterId: t.service_center_id,
        location: {
          address: t.location_name,
          coordinates: {
            lat: parseFloat(t.latitude) || 0,
            lng: parseFloat(t.longitude) || 0
          }
        },
        location_name: t.location_name,
        latitude: t.latitude,
        longitude: t.longitude,
        metrics: {
          temperature: parseFloat(t.temperature) || 0,
          loadPercentage: parseFloat(t.load_factor) || 0,
          oilLevel: parseFloat(t.oil_level) || 0,
          healthIndex: parseFloat(t.efficiency_rating) || 0
        },
        temperature: t.temperature,
        load_factor: t.load_factor,
        oil_level: t.oil_level,
        efficiency_rating: t.efficiency_rating,
        tags: [],
        regionName: t.region_name,
        serviceCenterName: t.service_center_name
      }));
    } catch (error) {
      console.error('Error fetching transformers:', error);
      return [];
    }
  }

  /**
   * Get transformer statistics
   */
  static async getTransformerStatistics() {
    try {
      const transformers = await this.getTransformers();

      // Calculate statistics
      const total = transformers.length;

      // Count by status
      const byStatus = transformers.reduce((acc, transformer) => {
        acc[transformer.status] = (acc[transformer.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Count by region
      const byRegion = transformers.reduce((acc, transformer) => {
        if (transformer.regionName) {
          acc[transformer.regionName] = (acc[transformer.regionName] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);

      // Get alerts statistics
      const alerts = await executeQuery<any[]>(`
        SELECT severity, is_resolved, COUNT(*) as count
        FROM app_alerts
        GROUP BY severity, is_resolved
      `);

      const alertStats = {
        critical: 0,
        warning: 0,
        info: 0,
        total: 0
      };

      alerts.forEach(alert => {
        if (!alert.is_resolved) {
          if (alert.severity === 'critical') alertStats.critical += alert.count;
          else if (alert.severity === 'high') alertStats.warning += alert.count;
          else alertStats.info += alert.count;
          alertStats.total += alert.count;
        }
      });

      // Mock maintenance statistics
      const maintenance = {
        scheduled: Math.floor(total * 0.15),
        overdue: Math.floor(total * 0.05),
        completed: Math.floor(total * 0.8),
        total: Math.floor(total * 1.0)
      };

      return {
        total,
        byStatus,
        byRegion,
        maintenance,
        alerts: alertStats
      };
    } catch (error) {
      console.error('Error calculating transformer statistics:', error);
      return {
        total: 0,
        byStatus: {},
        byRegion: {},
        maintenance: { scheduled: 0, overdue: 0, completed: 0, total: 0 },
        alerts: { critical: 0, warning: 0, info: 0, total: 0 }
      };
    }
  }

  /**
   * Get all alerts
   */
  static async getAlerts() {
    try {
      const alerts = await executeQuery<any[]>(`
        SELECT
          a.*,
          t.serial_number as transformer_serial,
          t.name as transformer_name
        FROM app_alerts a
        LEFT JOIN app_transformers t ON a.transformer_id = t.id
        ORDER BY a.created_at DESC
      `);

      return alerts.map(a => ({
        id: a.id,
        createdAt: a.created_at,
        updatedAt: a.updated_at || a.created_at,
        transformerId: a.transformer_id,
        type: a.type,
        severity: a.severity,
        title: a.title,
        message: a.message,
        isResolved: Boolean(a.is_resolved),
        resolvedAt: a.resolved_at,
        resolvedBy: a.resolved_by,
        resolutionNotes: a.resolution_notes,
        transformerSerial: a.transformer_serial,
        transformerName: a.transformer_name
      }));
    } catch (error) {
      console.error('Error fetching alerts:', error);
      return [];
    }
  }

  /**
   * Get recent alerts
   */
  static async getRecentAlerts(limit: number = 10) {
    try {
      const alerts = await this.getAlerts();
      return alerts.slice(0, limit);
    } catch (error) {
      console.error('Error fetching recent alerts:', error);
      return [];
    }
  }

  /**
   * Get alerts with filters
   */
  static async getAlerts(filters: any = {}) {
    try {
      let query = `
        SELECT
          a.*,
          t.serial_number as transformer_serial,
          t.name as transformer_name,
          r.name as region_name,
          sc.name as service_center_name
        FROM app_alerts a
        LEFT JOIN app_transformers t ON a.transformer_id = t.id
        LEFT JOIN app_regions r ON t.region_id = r.id
        LEFT JOIN app_service_centers sc ON t.service_center_id = sc.id
        WHERE 1=1
      `;

      const params: any[] = [];

      if (filters.severity) {
        query += ' AND a.severity = ?';
        params.push(filters.severity);
      }

      if (filters.status !== undefined) {
        if (filters.status === 'resolved') {
          query += ' AND a.is_resolved = 1';
        } else if (filters.status === 'unresolved') {
          query += ' AND a.is_resolved = 0';
        }
      }

      query += ' ORDER BY a.created_at DESC';

      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(filters.limit);
      }

      if (filters.offset) {
        query += ' OFFSET ?';
        params.push(filters.offset);
      }

      const alerts = await executeQuery<any[]>(query, params);

      return alerts.map(a => ({
        id: a.id,
        createdAt: a.created_at,
        updatedAt: a.updated_at,
        transformerId: a.transformer_id,
        type: a.type,
        severity: a.severity,
        priority: a.priority,
        title: a.title,
        description: a.description,
        status: a.status,
        isResolved: Boolean(a.is_resolved),
        createdBy: a.created_by,
        assignedTo: a.assigned_to,
        resolvedBy: a.resolved_by,
        resolvedAt: a.resolved_at,
        transformerSerial: a.transformer_serial,
        transformerName: a.transformer_name,
        regionName: a.region_name,
        serviceCenterName: a.service_center_name
      }));
    } catch (error) {
      console.error('Error fetching alerts:', error);
      return [];
    }
  }

  /**
   * Get maintenance schedules with filters
   */
  static async getMaintenanceSchedules(filters: any = {}) {
    try {
      let query = `
        SELECT
          ms.*,
          t.serial_number as transformer_serial,
          t.name as transformer_name,
          u1.first_name as technician_first_name,
          u1.last_name as technician_last_name,
          u2.first_name as supervisor_first_name,
          u2.last_name as supervisor_last_name
        FROM app_maintenance_schedules ms
        LEFT JOIN app_transformers t ON ms.transformer_id = t.id
        LEFT JOIN app_users u1 ON ms.technician_id = u1.id
        LEFT JOIN app_users u2 ON ms.supervisor_id = u2.id
        WHERE 1=1
      `;

      const params: any[] = [];

      if (filters.status) {
        query += ' AND ms.status = ?';
        params.push(filters.status);
      }

      if (filters.priority) {
        query += ' AND ms.priority = ?';
        params.push(filters.priority);
      }

      if (filters.type) {
        query += ' AND ms.type = ?';
        params.push(filters.type);
      }

      query += ' ORDER BY ms.scheduled_date DESC';

      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(filters.limit);
      }

      if (filters.offset) {
        query += ' OFFSET ?';
        params.push(filters.offset);
      }

      const schedules = await executeQuery<any[]>(query, params);

      return schedules.map(ms => ({
        id: ms.id,
        createdAt: ms.created_at,
        updatedAt: ms.updated_at,
        transformerId: ms.transformer_id,
        type: ms.type,
        status: ms.status,
        priority: ms.priority,
        title: ms.title,
        description: ms.description,
        scheduledDate: ms.scheduled_date,
        scheduledTime: ms.scheduled_time,
        estimatedDuration: ms.estimated_duration,
        actualStartTime: ms.actual_start_time,
        actualEndTime: ms.actual_end_time,
        technicianId: ms.technician_id,
        supervisorId: ms.supervisor_id,
        teamMembers: ms.team_members ? JSON.parse(ms.team_members) : [],
        requiredParts: ms.required_parts ? JSON.parse(ms.required_parts) : [],
        requiredTools: ms.required_tools ? JSON.parse(ms.required_tools) : [],
        safetyRequirements: ms.safety_requirements ? JSON.parse(ms.safety_requirements) : [],
        workInstructions: ms.work_instructions,
        completionNotes: ms.completion_notes,
        qualityCheckPassed: ms.quality_check_passed,
        cost: ms.cost,
        downtimeMinutes: ms.downtime_minutes,
        createdBy: ms.created_by,
        transformerSerial: ms.transformer_serial,
        transformerName: ms.transformer_name,
        technicianName: ms.technician_first_name && ms.technician_last_name
          ? `${ms.technician_first_name} ${ms.technician_last_name}` : null,
        supervisorName: ms.supervisor_first_name && ms.supervisor_last_name
          ? `${ms.supervisor_first_name} ${ms.supervisor_last_name}` : null
      }));
    } catch (error) {
      console.error('Error fetching maintenance schedules:', error);
      return [];
    }
  }

  /**
   * Get notifications with filters
   */
  static async getNotifications(filters: any = {}) {
    try {
      let query = `
        SELECT
          n.*,
          u.first_name as sender_first_name,
          u.last_name as sender_last_name
        FROM app_notifications n
        LEFT JOIN app_users u ON n.sender_id = u.id
        WHERE 1=1
      `;

      const params: any[] = [];

      if (filters.userId) {
        query += ' AND n.recipient_id = ?';
        params.push(filters.userId);
      }

      if (filters.isRead !== undefined) {
        query += ' AND n.is_read = ?';
        params.push(filters.isRead ? 1 : 0);
      }

      if (filters.priority) {
        query += ' AND n.priority = ?';
        params.push(filters.priority);
      }

      query += ' ORDER BY n.created_at DESC';

      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(filters.limit);
      }

      if (filters.offset) {
        query += ' OFFSET ?';
        params.push(filters.offset);
      }

      const notifications = await executeQuery<any[]>(query, params);

      return notifications.map(n => ({
        id: n.id,
        recipientId: n.recipient_id,
        type: n.type,
        title: n.title,
        message: n.message,
        isRead: Boolean(n.is_read),
        readAt: n.read_at,
        senderId: n.sender_id,
        createdAt: n.created_at,
        senderName: n.sender_first_name && n.sender_last_name
          ? `${n.sender_first_name} ${n.sender_last_name}` : null
      }));
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  /**
   * Get dashboard data
   */
  static async getDashboardData() {
    try {
      const transformerStatistics = await this.getTransformerStatistics();
      const recentAlerts = await this.getAlerts({ limit: 5 });
      const upcomingMaintenance = await this.getMaintenanceSchedules({
        status: 'scheduled',
        limit: 5
      });
      const recentNotifications = await this.getNotifications({ limit: 5 });

      return {
        transformerStatistics,
        recentAlerts,
        upcomingMaintenance,
        recentNotifications,
        activeOutages: [], // Mock for now
        weatherAlerts: [], // Mock for now
        isLoading: false
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      return {
        transformerStatistics: {
          total: 0,
          byStatus: {},
          byRegion: {},
          maintenance: { scheduled: 0, overdue: 0, completed: 0, total: 0 },
          alerts: { critical: 0, warning: 0, info: 0, total: 0 }
        },
        recentAlerts: [],
        upcomingMaintenance: [],
        recentNotifications: [],
        activeOutages: [],
        weatherAlerts: [],
        isLoading: false
      };
    }
  }

  /**
   * Get recent activities for dashboard
   */
  static async getRecentActivities(limit: number = 10) {
    try {
      const query = `
        SELECT
          'alert' as type,
          id,
          title as description,
          severity as priority,
          created_at,
          transformer_id as related_id
        FROM app_alerts
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)

        UNION ALL

        SELECT
          'maintenance' as type,
          id,
          CONCAT('Maintenance: ', type) as description,
          priority,
          created_at,
          transformer_id as related_id
        FROM app_maintenance_schedules
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)

        ORDER BY created_at DESC
        LIMIT ?
      `;

      const activities = await executeQuery(query, [limit]);

      return activities.map((activity: any) => ({
        id: activity.id,
        type: activity.type,
        description: activity.description,
        priority: activity.priority,
        timestamp: activity.created_at,
        relatedId: activity.related_id
      }));
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      return [];
    }
  }

  /**
   * Get performance metrics
   */
  static async getPerformanceMetrics() {
    try {
      const query = `
        SELECT
          metric_type,
          metric_name,
          AVG(value) as avg_value,
          MIN(value) as min_value,
          MAX(value) as max_value,
          COUNT(*) as data_points
        FROM app_performance_metrics
        WHERE period_start >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY metric_type, metric_name
        ORDER BY metric_type, metric_name
      `;

      const metrics = await executeQuery(query);

      return metrics.map((metric: any) => ({
        type: metric.metric_type,
        name: metric.metric_name,
        average: parseFloat(metric.avg_value),
        minimum: parseFloat(metric.min_value),
        maximum: parseFloat(metric.max_value),
        dataPoints: metric.data_points
      }));
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
      return [];
    }
  }
}

export default MySQLServerService;
