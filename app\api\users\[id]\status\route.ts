import { NextRequest, NextResponse } from 'next/server'

// Mock users data - in a real app this would be from database
const mockUsers = [
  {
    id: 1,
    employeeId: 'EEU001',
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    status: 'active'
  },
  {
    id: 2,
    employeeId: 'EEU002',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    status: 'active'
  }
]

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = parseInt(params.id)
    const body = await request.json()
    const { status } = body

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      )
    }

    if (!status || !['active', 'inactive', 'suspended'].includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status. Must be active, inactive, or suspended' },
        { status: 400 }
      )
    }

    const userIndex = mockUsers.findIndex(u => u.id === userId)

    if (userIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Update user status
    mockUsers[userIndex].status = status

    // Log the status change (in a real app, this would be saved to audit log)
    console.log(`User ${userId} status changed to ${status} at ${new Date().toISOString()}`)

    return NextResponse.json({
      success: true,
      data: { 
        userId,
        status,
        updatedAt: new Date().toISOString()
      },
      message: `User status updated to ${status} successfully`
    })
  } catch (error) {
    console.error('Error updating user status:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update user status' },
      { status: 500 }
    )
  }
}
