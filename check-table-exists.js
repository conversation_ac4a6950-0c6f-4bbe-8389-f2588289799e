const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

async function checkTableExists(tableName) {
  const connection = await mysql.createConnection(config);
  const [rows] = await connection.execute(
    "SELECT TABLE_NAME, TABLE_TYPE FROM information_schema.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?",
    ['dtms_eeu_db', tableName]
  );
  if (rows.length > 0) {
    console.log(`${tableName} exists as a ${rows[0].TABLE_TYPE}`);
  } else {
    console.log(`${tableName} does NOT exist`);
  }
  await connection.end();
}

const tableName = process.argv[2] || 'app_transformers';
checkTableExists(tableName);
