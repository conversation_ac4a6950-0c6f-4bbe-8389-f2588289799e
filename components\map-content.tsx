"use client"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { UnifiedMap } from "@/components/unified-map"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Button } from "@/src/components/ui/button"
import {
  Layers,
  Filter,
  MapPin,
  Download,
  Search,
  Ruler,
  BarChart,
  Locate,
  Mountain,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap
} from "lucide-react"
import { useToast } from "@/src/components/ui/use-toast"
import { transformerService } from "@/src/services/transformer-service"
import { MapLocation, mapService } from "@/src/services/map-service"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/src/components/ui/dialog"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Switch } from "@/src/components/ui/switch"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/src/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/src/components/ui/dropdown-menu"

export function MapContent() {
  const [selectedRegion, setSelectedRegion] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [transformerLocations, setTransformerLocations] = useState<MapLocation[]>([])
  const [filteredLocations, setFilteredLocations] = useState<MapLocation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [mapStyle, setMapStyle] = useState<'light' | 'dark' | 'satellite' | 'streets' | 'terrain'>('light')
  const [clustered, setClustered] = useState(true)
  const [showHeatmap, setShowHeatmap] = useState(false)
  const [showFindNearestDialog, setShowFindNearestDialog] = useState(false)
  const [nearestAddress, setNearestAddress] = useState("")
  const [nearestRadius, setNearestRadius] = useState("5")
  const [nearestResults, setNearestResults] = useState<MapLocation[]>([])
  const [showLayersDialog, setShowLayersDialog] = useState(false)
  const [layerSettings, setLayerSettings] = useState({
    operational: true,
    maintenance: true,
    critical: true,
    burnt: true,
    offline: true,
    showLabels: true, // Enable labels by default for better visibility
    showServiceCenters: true,
    showRegionBoundaries: true // Enable region boundaries for better context
  })
  const [mapStats, setMapStats] = useState({
    total: 0,
    operational: 0,
    maintenance: 0,
    critical: 0,
    burnt: 0,
    offline: 0
  })

  const { toast } = useToast()

  // Load transformer locations
  useEffect(() => {
    const loadTransformerLocations = async () => {
      try {
        setIsLoading(true)
        // Get all transformers first
        const transformers = await transformerService.getAllTransformers()
        // Then convert them to map locations using the standardized method
        const locations = transformers.map(transformer => mapService.transformerToMapLocation(transformer))
        setTransformerLocations(locations)
        setFilteredLocations(locations)

        // Calculate stats
        const stats = {
          total: locations.length,
          operational: locations.filter(loc => loc.status.toLowerCase() === 'operational').length,
          maintenance: locations.filter(loc => loc.status.toLowerCase() === 'maintenance').length,
          critical: locations.filter(loc => loc.status.toLowerCase() === 'critical').length,
          burnt: locations.filter(loc => loc.status.toLowerCase() === 'burnt').length,
          offline: locations.filter(loc => loc.status.toLowerCase() === 'offline').length
        }
        setMapStats(stats)
      } catch (error) {
        console.error("Error loading transformer locations:", error)
        toast({
          title: "Error",
          description: "Failed to load transformer locations. Please try again.",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadTransformerLocations()
  }, [toast])

  // Apply filters when region or status changes
  useEffect(() => {
    let filtered = [...transformerLocations]

    // Apply region filter
    if (selectedRegion !== 'all') {
      filtered = filtered.filter(loc => {
        // Make sure we have location data and region
        if (!loc.data?.location?.region) return false;

        // Case-insensitive comparison
        return loc.data.location.region.toLowerCase() === selectedRegion.toLowerCase();
      })
    }

    // Apply status filter
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(loc =>
        loc.status.toLowerCase() === selectedStatus.toLowerCase()
      )
    }

    // Apply layer visibility settings
    filtered = filtered.filter(loc => {
      const status = loc.status.toLowerCase()
      if (status === 'operational' && !layerSettings.operational) return false
      if (status === 'maintenance' && !layerSettings.maintenance) return false
      if (status === 'critical' && !layerSettings.critical) return false
      if (status === 'burnt' && !layerSettings.burnt) return false
      if (status === 'offline' && !layerSettings.offline) return false
      return true
    })

    setFilteredLocations(filtered)

    // Update stats for filtered view
    const stats = {
      total: filtered.length,
      operational: filtered.filter(loc => loc.status.toLowerCase() === 'operational').length,
      maintenance: filtered.filter(loc => loc.status.toLowerCase() === 'maintenance').length,
      critical: filtered.filter(loc => loc.status.toLowerCase() === 'critical').length,
      burnt: filtered.filter(loc => loc.status.toLowerCase() === 'burnt').length,
      offline: filtered.filter(loc => loc.status.toLowerCase() === 'offline').length
    }
    setMapStats(stats)
  }, [selectedRegion, selectedStatus, transformerLocations, layerSettings])

  // Handle find nearest transformers
  const handleFindNearest = async () => {
    try {
      if (!nearestAddress) {
        toast({
          title: "Input Required",
          description: "Please enter an address or coordinates",
          variant: "destructive"
        })
        return
      }

      // For demo purposes, we'll use a simple approach
      // In a real app, you would use a geocoding service to convert address to coordinates

      // Parse coordinates if entered directly (lat,lng format)
      let latitude = 9.0222 // Default to Addis Ababa
      let longitude = 38.7468

      const coordsMatch = nearestAddress.match(/(-?\d+\.\d+),\s*(-?\d+\.\d+)/)
      if (coordsMatch) {
        latitude = parseFloat(coordsMatch[1])
        longitude = parseFloat(coordsMatch[2])
      } else {
        // Simulate geocoding for demo purposes
        // In a real app, you would use a geocoding service
        toast({
          title: "Geocoding",
          description: "Converting address to coordinates...",
        })

        // Simulate delay
        await new Promise(resolve => setTimeout(resolve, 1000))

        // For demo, just use random coordinates near Addis Ababa
        latitude = 9.0222 + (Math.random() - 0.5) * 0.1
        longitude = 38.7468 + (Math.random() - 0.5) * 0.1
      }

      // Find nearby transformers
      const radius = parseFloat(nearestRadius)
      const nearby = await transformerService.getNearbyTransformers(latitude, longitude, radius)

      // Convert to map locations using the standardized method
      const nearbyLocations = nearby.map(transformer =>
        mapService.transformerToMapLocation(transformer)
      )

      setNearestResults(nearbyLocations)

      toast({
        title: "Search Complete",
        description: `Found ${nearbyLocations.length} transformers within ${radius} km`,
      })

      // Close dialog and update map to show results
      setShowFindNearestDialog(false)
      setFilteredLocations(nearbyLocations)
    } catch (error) {
      console.error("Error finding nearest transformers:", error)
      toast({
        title: "Error",
        description: "Failed to find nearest transformers. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Handle layer settings change
  const handleLayerSettingChange = (setting: keyof typeof layerSettings) => {
    setLayerSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }))
  }

  // Reset filters
  const resetFilters = () => {
    setSelectedRegion('all')
    setSelectedStatus('all')
    setLayerSettings({
      operational: true,
      maintenance: true,
      critical: true,
      burnt: true,
      offline: true,
      showLabels: true,
      showServiceCenters: true,
      showRegionBoundaries: true
    })
    // Ensure all transformers are displayed
    setFilteredLocations(transformerLocations)

    // Show a toast notification to inform the user
    toast({
      title: "Filters Reset",
      description: `Showing all ${transformerLocations.length} transformers`,
    })
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Transformer Network Map</h1>
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={() => setShowLayersDialog(true)}>
                  <Layers className="mr-2 h-4 w-4" />
                  Toggle Layers
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                Show or hide map layers
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={() => setShowFindNearestDialog(true)}>
                  <MapPin className="mr-2 h-4 w-4" />
                  Find Nearest
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                Find transformers near a location
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={showHeatmap ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowHeatmap(!showHeatmap)}
                >
                  <BarChart className="mr-2 h-4 w-4" />
                  Heatmap
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                Toggle heatmap view
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="default"
                  size="sm"
                  className="bg-primary text-primary-foreground hover:bg-primary/90"
                  onClick={() => {
                    resetFilters();
                    // Force refresh to ensure all transformers are displayed
                    const loadAllTransformers = async () => {
                      setIsLoading(true);
                      try {
                        const transformers = await transformerService.getAllTransformers();
                        const locations = transformers.map(transformer =>
                          mapService.transformerToMapLocation(transformer)
                        );
                        setTransformerLocations(locations);
                        setFilteredLocations(locations);

                        // Disable clustering to show all individual transformers
                        setClustered(false);

                        toast({
                          title: "All Transformers Loaded",
                          description: `Showing all ${locations.length} transformers on the map`,
                        });
                      } catch (error) {
                        console.error("Error loading all transformers:", error);
                      } finally {
                        setIsLoading(false);
                      }
                    };
                    loadAllTransformers();
                  }}
                >
                  <Zap className="mr-2 h-4 w-4" />
                  Show All Transformers ({transformerLocations.length})
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                Show all 32 transformers on the map (disables clustering)
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <div className="md:col-span-3">
          <Card className="h-[calc(100vh-13rem)]">
            <CardHeader className="pb-3">
              <CardTitle>Geographic View</CardTitle>
              <CardDescription>Interactive map of transformer locations and status</CardDescription>
            </CardHeader>
            <CardContent className="p-0 h-[calc(100%-5rem)] relative">
              {isLoading && (
                <div className="absolute inset-0 bg-background/80 flex items-center justify-center z-50">
                  <div className="text-center">
                    <div className="mb-2 h-8 w-8 animate-spin rounded-full border-4 border-teal-600 border-t-transparent mx-auto"></div>
                    <p className="text-sm text-muted-foreground">Loading transformer data...</p>
                  </div>
                </div>
              )}
              <UnifiedMap
                locations={filteredLocations}
                height="100%"
                clustered={clustered}
                mapStyle={mapStyle}
                showControls={true}
                showLegend={true}
                showFilters={true}
                interactive={true}
                allowFullscreen={true}
                showHeatmap={showHeatmap}
                showSearch={true}
                showExport={true}
                showMeasurement={true}
              />
            </CardContent>
          </Card>
        </div>
        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Filters</CardTitle>
              <CardDescription>Filter transformers on the map</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Region</label>
                <Select value={selectedRegion} onValueChange={setSelectedRegion}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Regions</SelectItem>
                    <SelectItem value="Addis Ababa">Addis Ababa</SelectItem>
                    <SelectItem value="Amhara">Amhara</SelectItem>
                    <SelectItem value="Oromia">Oromia</SelectItem>
                    <SelectItem value="SNNPR">SNNPR</SelectItem>
                    <SelectItem value="Tigray">Tigray</SelectItem>
                    <SelectItem value="Somali">Somali</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="operational">Operational</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="burnt">Burnt</SelectItem>
                    <SelectItem value="offline">Offline</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-2">
                <Button className="flex-1" onClick={() => {
                  // Apply filters
                  let filtered = [...transformerLocations]

                  // Apply region filter
                  if (selectedRegion !== 'all') {
                    filtered = filtered.filter(loc => {
                      // Make sure we have location data and region
                      if (!loc.data?.location?.region) return false;

                      // Case-insensitive comparison
                      return loc.data.location.region.toLowerCase() === selectedRegion.toLowerCase();
                    })
                  }

                  // Apply status filter
                  if (selectedStatus !== 'all') {
                    filtered = filtered.filter(loc =>
                      loc.status.toLowerCase() === selectedStatus.toLowerCase()
                    )
                  }

                  setFilteredLocations(filtered)

                  toast({
                    title: "Filters Applied",
                    description: `Showing ${filtered.length} transformers`,
                  })
                }}>
                  <Filter className="mr-2 h-4 w-4" />
                  Apply Filters
                </Button>
                <Button variant="outline" onClick={resetFilters}>
                  Reset
                </Button>
              </div>

              <div className="space-y-2 pt-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="cluster-mode" className="text-sm">Cluster Mode</Label>
                  <Switch
                    id="cluster-mode"
                    checked={clustered}
                    onCheckedChange={setClustered}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Map Statistics</CardTitle>
              <CardDescription>Current view statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Total Transformers:</span>
                  <span className="text-sm font-medium">{mapStats.total.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Operational:</span>
                  <span className="text-sm font-medium text-green-600">{mapStats.operational.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Maintenance:</span>
                  <span className="text-sm font-medium text-yellow-600">{mapStats.maintenance.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Critical:</span>
                  <span className="text-sm font-medium text-red-600">{mapStats.critical.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Burnt:</span>
                  <span className="text-sm font-medium text-orange-600">{mapStats.burnt.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Offline:</span>
                  <span className="text-sm font-medium text-gray-600">{mapStats.offline.toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Find Nearest Dialog */}
      <Dialog open={showFindNearestDialog} onOpenChange={setShowFindNearestDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Find Nearest Transformers</DialogTitle>
            <DialogDescription>
              Enter an address or coordinates to find transformers nearby.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="address">Address or Coordinates</Label>
              <Input
                id="address"
                placeholder="Address or lat,lng (e.g., 9.0222, 38.7468)"
                value={nearestAddress}
                onChange={(e) => setNearestAddress(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                Enter an address or coordinates in the format "latitude, longitude"
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="radius">Search Radius (km)</Label>
              <Input
                id="radius"
                type="number"
                min="1"
                max="50"
                value={nearestRadius}
                onChange={(e) => setNearestRadius(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowFindNearestDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleFindNearest}>
              Search
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Toggle Layers Dialog */}
      <Dialog open={showLayersDialog} onOpenChange={setShowLayersDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Map Layers</DialogTitle>
            <DialogDescription>
              Configure which layers are visible on the map.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Transformer Status</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="layer-operational"
                    checked={layerSettings.operational}
                    onCheckedChange={() => handleLayerSettingChange('operational')}
                  />
                  <Label htmlFor="layer-operational" className="flex items-center">
                    <div className="flex h-4 w-4 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20 mr-2">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    </div>
                    Operational
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="layer-maintenance"
                    checked={layerSettings.maintenance}
                    onCheckedChange={() => handleLayerSettingChange('maintenance')}
                  />
                  <Label htmlFor="layer-maintenance" className="flex items-center">
                    <div className="flex h-4 w-4 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/20 mr-2">
                      <Clock className="h-3 w-3 text-yellow-600" />
                    </div>
                    Maintenance
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="layer-critical"
                    checked={layerSettings.critical}
                    onCheckedChange={() => handleLayerSettingChange('critical')}
                  />
                  <Label htmlFor="layer-critical" className="flex items-center">
                    <div className="flex h-4 w-4 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20 mr-2">
                      <AlertTriangle className="h-3 w-3 text-red-600" />
                    </div>
                    Critical
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="layer-burnt"
                    checked={layerSettings.burnt}
                    onCheckedChange={() => handleLayerSettingChange('burnt')}
                  />
                  <Label htmlFor="layer-burnt" className="flex items-center">
                    <div className="flex h-4 w-4 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20 mr-2">
                      <Zap className="h-3 w-3 text-orange-600" />
                    </div>
                    Burnt
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="layer-offline"
                    checked={layerSettings.offline}
                    onCheckedChange={() => handleLayerSettingChange('offline')}
                  />
                  <Label htmlFor="layer-offline" className="flex items-center">
                    <div className="flex h-4 w-4 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-900/20 mr-2">
                      <div className="h-2 w-2 rounded-full bg-gray-600"></div>
                    </div>
                    Offline
                  </Label>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-sm font-medium">Additional Layers</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="layer-labels"
                    checked={layerSettings.showLabels}
                    onCheckedChange={() => handleLayerSettingChange('showLabels')}
                  />
                  <Label htmlFor="layer-labels">Show Labels</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="layer-service-centers"
                    checked={layerSettings.showServiceCenters}
                    onCheckedChange={() => handleLayerSettingChange('showServiceCenters')}
                  />
                  <Label htmlFor="layer-service-centers">Service Centers</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="layer-region-boundaries"
                    checked={layerSettings.showRegionBoundaries}
                    onCheckedChange={() => handleLayerSettingChange('showRegionBoundaries')}
                  />
                  <Label htmlFor="layer-region-boundaries">Region Boundaries</Label>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-sm font-medium">Map Style</h3>
              <div className="grid grid-cols-3 gap-2">
                <Button
                  variant={mapStyle === 'light' ? "default" : "outline"}
                  size="sm"
                  onClick={() => setMapStyle('light')}
                  className="w-full"
                >
                  Light
                </Button>
                <Button
                  variant={mapStyle === 'dark' ? "default" : "outline"}
                  size="sm"
                  onClick={() => setMapStyle('dark')}
                  className="w-full"
                >
                  Dark
                </Button>
                <Button
                  variant={mapStyle === 'satellite' ? "default" : "outline"}
                  size="sm"
                  onClick={() => setMapStyle('satellite')}
                  className="w-full"
                >
                  Satellite
                </Button>
                <Button
                  variant={mapStyle === 'streets' ? "default" : "outline"}
                  size="sm"
                  onClick={() => setMapStyle('streets')}
                  className="w-full"
                >
                  Streets
                </Button>
                <Button
                  variant={mapStyle === 'terrain' ? "default" : "outline"}
                  size="sm"
                  onClick={() => setMapStyle('terrain')}
                  className="w-full"
                >
                  Terrain
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowLayersDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
