import { NextRequest, NextResponse } from 'next/server'
import { recreateTablesOnly } from '../../../src/lib/db/recreate-tables'
import { simpleSeedDatabase } from '../../../src/lib/db/simple-seed'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API: Table recreation requested...')
    
    // Step 1: Recreate tables
    console.log('🔄 Recreating database tables...')
    const tableResult = await recreateTablesOnly()
    
    if (!tableResult.success) {
      console.error('❌ API: Table recreation failed:', tableResult.message)
      return NextResponse.json({
        success: false,
        message: tableResult.message,
        error: tableResult.error,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    console.log('✅ API: Tables recreated successfully')

    // Wait a moment for the tables to be fully committed
    console.log('⏳ Waiting for tables to be committed...')
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Step 2: Seed database with data
    console.log('🔄 Seeding database with Ethiopian data...')
    const seedResult = await simpleSeedDatabase()
    
    if (!seedResult.success) {
      console.error('❌ API: Database seeding failed:', seedResult.message)
      return NextResponse.json({
        success: false,
        message: `Tables recreated but seeding failed: ${seedResult.message}`,
        tablesSuccess: true,
        seedSuccess: false,
        error: seedResult.error,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    console.log('✅ API: Database seeded successfully')

    return NextResponse.json({
      success: true,
      message: 'Tables recreated and database seeded successfully',
      tablesSuccess: true,
      seedSuccess: true,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ API: Table recreation failed:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Table recreation failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Table recreation API is ready. Use POST to recreate tables.',
    timestamp: new Date().toISOString()
  })
}
