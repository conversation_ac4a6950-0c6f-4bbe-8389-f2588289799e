import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') || ''
    const priority = searchParams.get('priority') || ''
    const dateRange = searchParams.get('dateRange') || ''
    const search = searchParams.get('search') || ''
    const offset = (page - 1) * limit

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    // Build WHERE clause
    let whereConditions = []
    let queryParams = []

    if (status) {
      whereConditions.push('ms.status = ?')
      queryParams.push(status)
    }

    if (priority) {
      whereConditions.push('ms.priority = ?')
      queryParams.push(priority)
    }

    if (search) {
      whereConditions.push('(t.name LIKE ? OR ms.description LIKE ? OR ms.maintenance_type LIKE ?)')
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`)
    }

    // Date range filtering
    if (dateRange) {
      const today = new Date()
      switch (dateRange) {
        case 'today':
          whereConditions.push('DATE(ms.scheduled_date) = CURDATE()')
          break
        case 'week':
          whereConditions.push('ms.scheduled_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)')
          break
        case 'month':
          whereConditions.push('ms.scheduled_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)')
          break
        case 'overdue':
          whereConditions.push('ms.scheduled_date < CURDATE() AND ms.status != "completed"')
          break
      }
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : ''

    // Get scheduled maintenance tasks
    const tasks = await executeQuery(`
      SELECT 
        ms.*,
        t.name as transformer_name,
        t.serial_number,
        t.location as transformer_location,
        t.status as transformer_status,
        r.name as region_name,
        sc.name as service_center_name,
        u.first_name as technician_first_name,
        u.last_name as technician_last_name,
        u.phone as technician_phone,
        DATEDIFF(ms.scheduled_date, CURDATE()) as days_until_due,
        CASE 
          WHEN ms.scheduled_date < CURDATE() AND ms.status != 'completed' THEN 'overdue'
          WHEN DATEDIFF(ms.scheduled_date, CURDATE()) <= 3 THEN 'urgent'
          WHEN DATEDIFF(ms.scheduled_date, CURDATE()) <= 7 THEN 'upcoming'
          ELSE 'scheduled'
        END as urgency_status
      FROM app_maintenance_schedules ms
      LEFT JOIN app_transformers t ON ms.transformer_id = t.id
      LEFT JOIN app_regions r ON t.region_id = r.id
      LEFT JOIN app_service_centers sc ON t.service_center_id = sc.id
      LEFT JOIN app_users u ON ms.assigned_technician_id = u.id
      ${whereClause}
      ORDER BY 
        CASE ms.priority 
          WHEN 'critical' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'medium' THEN 3 
          WHEN 'low' THEN 4 
        END,
        ms.scheduled_date ASC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset])

    // Get total count
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total
      FROM app_maintenance_schedules ms
      LEFT JOIN app_transformers t ON ms.transformer_id = t.id
      ${whereClause}
    `, queryParams)

    const total = totalResult[0]?.total || 0

    // Get summary statistics
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_tasks,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tasks,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tasks,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
        SUM(CASE WHEN scheduled_date < CURDATE() AND status != 'completed' THEN 1 ELSE 0 END) as overdue_tasks,
        SUM(CASE WHEN priority = 'critical' THEN 1 ELSE 0 END) as critical_tasks,
        AVG(CASE WHEN status = 'completed' THEN DATEDIFF(completed_date, scheduled_date) END) as avg_completion_delay
      FROM app_maintenance_schedules ms
      LEFT JOIN app_transformers t ON ms.transformer_id = t.id
      ${whereClause}
    `, queryParams)

    // Get upcoming tasks (next 7 days)
    const upcomingTasks = await executeQuery(`
      SELECT 
        ms.id,
        ms.scheduled_date,
        ms.maintenance_type,
        ms.priority,
        t.name as transformer_name,
        t.location
      FROM app_maintenance_schedules ms
      LEFT JOIN app_transformers t ON ms.transformer_id = t.id
      WHERE ms.scheduled_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        AND ms.status = 'pending'
      ORDER BY ms.scheduled_date ASC
      LIMIT 10
    `)

    return NextResponse.json({
      success: true,
      data: {
        tasks: tasks.map((task: any) => ({
          ...task,
          estimated_duration: task.estimated_duration || '2-4 hours',
          required_parts: task.required_parts ? JSON.parse(task.required_parts) : [],
          technician_name: task.technician_first_name && task.technician_last_name ? 
            `${task.technician_first_name} ${task.technician_last_name}` : 'Unassigned'
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        statistics: {
          ...stats[0],
          avg_completion_delay: Math.round(stats[0]?.avg_completion_delay || 0)
        },
        upcoming: upcomingTasks
      }
    })

  } catch (error) {
    console.error('❌ Error fetching scheduled tasks:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch scheduled tasks',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, taskId, data } = body

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    switch (action) {
      case 'create_task':
        const newTaskId = await executeQuery(`
          INSERT INTO app_maintenance_schedules 
          (transformer_id, scheduled_date, maintenance_type, priority, description, 
           assigned_technician_id, estimated_duration, required_parts, status, created_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
        `, [
          data.transformerId,
          data.scheduledDate,
          data.maintenanceType,
          data.priority,
          data.description,
          data.assignedTechnicianId,
          data.estimatedDuration,
          JSON.stringify(data.requiredParts || [])
        ])
        
        return NextResponse.json({
          success: true,
          taskId: newTaskId.insertId,
          message: 'Maintenance task created successfully'
        })

      case 'update_status':
        const updateData: any = {
          status: data.status,
          updated_at: new Date()
        }

        if (data.status === 'completed') {
          updateData.completed_date = new Date()
          updateData.technician_notes = data.technicianNotes || ''
          updateData.actual_duration = data.actualDuration || null
        } else if (data.status === 'in_progress') {
          updateData.started_date = new Date()
        }

        const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ')
        const updateValues = Object.values(updateData)

        await executeQuery(`
          UPDATE app_maintenance_schedules 
          SET ${updateFields}
          WHERE id = ?
        `, [...updateValues, taskId])
        break

      case 'assign_technician':
        await executeQuery(`
          UPDATE app_maintenance_schedules 
          SET assigned_technician_id = ?, updated_at = NOW()
          WHERE id = ?
        `, [data.technicianId, taskId])
        break

      case 'reschedule':
        await executeQuery(`
          UPDATE app_maintenance_schedules 
          SET scheduled_date = ?, updated_at = NOW()
          WHERE id = ?
        `, [data.newDate, taskId])
        break

      case 'bulk_assign':
        const { taskIds, technicianId } = data
        await executeQuery(`
          UPDATE app_maintenance_schedules 
          SET assigned_technician_id = ?, updated_at = NOW()
          WHERE id IN (${taskIds.map(() => '?').join(',')})
        `, [technicianId, ...taskIds])
        break

      case 'generate_schedule':
        // Auto-generate maintenance schedule based on transformer age and last maintenance
        const autoTasks = await executeQuery(`
          INSERT INTO app_maintenance_schedules 
          (transformer_id, scheduled_date, maintenance_type, priority, description, status, created_at)
          SELECT 
            t.id,
            DATE_ADD(CURDATE(), INTERVAL 
              CASE 
                WHEN DATEDIFF(CURDATE(), t.installation_date) > 3650 THEN 30  -- Old transformers: monthly
                WHEN DATEDIFF(CURDATE(), t.installation_date) > 1825 THEN 60  -- Medium age: bi-monthly
                ELSE 90  -- New transformers: quarterly
              END DAY
            ) as scheduled_date,
            'preventive' as maintenance_type,
            CASE 
              WHEN t.status = 'faulty' THEN 'critical'
              WHEN t.load_percentage > 90 THEN 'high'
              ELSE 'medium'
            END as priority,
            CONCAT('Scheduled preventive maintenance for ', t.name) as description,
            'pending' as status,
            NOW() as created_at
          FROM app_transformers t
          LEFT JOIN app_maintenance_schedules ms ON t.id = ms.transformer_id 
            AND ms.scheduled_date > CURDATE() 
            AND ms.status IN ('pending', 'in_progress')
          WHERE ms.id IS NULL  -- No future maintenance scheduled
            AND t.status != 'decommissioned'
        `)

        return NextResponse.json({
          success: true,
          tasksCreated: autoTasks.affectedRows,
          message: `${autoTasks.affectedRows} maintenance tasks auto-generated`
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: 'Action completed successfully'
    })

  } catch (error) {
    console.error('❌ Error processing scheduled tasks action:', error)
    return NextResponse.json(
      {
        error: 'Failed to process action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
