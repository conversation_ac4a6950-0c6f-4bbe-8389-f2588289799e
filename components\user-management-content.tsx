"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Download, Search, Filter, Edit, Trash, UserPlus, Shield, MapPin } from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar"
import { useAuth } from "@/src/features/auth/context/auth-context"
import { UserDialog } from "@/components/user-dialog"
import { useToast } from "@/src/components/ui/use-toast"
import type { User, User<PERSON>ole } from "@/src/types/auth"

export function UserManagementContent() {
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const { user: currentUser } = useAuth()
  const { toast } = useToast()

  // Mock users data - in a real app, this would come from an API
  const users = [
    {
      id: "user-001",
      name: "Abebe Kebede",
      email: "<EMAIL>",
      role: "super_admin" as UserRole,
      organizationalLevel: "head_office",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-25T10:24:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=AK",
    },
    {
      id: "user-002",
      name: "Tigist Haile",
      email: "<EMAIL>",
      role: "national_asset_manager" as UserRole,
      organizationalLevel: "head_office",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-26T09:15:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=TH",
    },
    {
      id: "user-003",
      name: "Dawit Mengistu",
      email: "<EMAIL>",
      role: "regional_admin" as UserRole,
      organizationalLevel: "regional_office",
      regionId: "region-001",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-26T14:30:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=DM",
    },
    {
      id: "user-004",
      name: "Hiwot Tadesse",
      email: "<EMAIL>",
      role: "regional_maintenance_engineer" as UserRole,
      organizationalLevel: "regional_office",
      regionId: "region-001",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-25T16:45:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=HT",
    },
    {
      id: "user-005",
      name: "Solomon Bekele",
      email: "<EMAIL>",
      role: "service_center_manager" as UserRole,
      organizationalLevel: "service_center",
      regionId: "region-001",
      serviceCenterId: "sc-001",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-24T11:20:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=SB",
    },
    {
      id: "user-006",
      name: "Meron Alemu",
      email: "<EMAIL>",
      role: "field_technician" as UserRole,
      organizationalLevel: "service_center",
      regionId: "region-001",
      serviceCenterId: "sc-001",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-26T08:10:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=MA",
    },
    {
      id: "user-007",
      name: "Yonas Girma",
      email: "<EMAIL>",
      role: "customer_service_agent" as UserRole,
      organizationalLevel: "service_center",
      regionId: "region-001",
      serviceCenterId: "sc-001",
      permissions: [],
      isActive: false,
      lastLogin: "2023-04-20T09:30:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=YG",
    },
    {
      id: "user-008",
      name: "Bethlehem Tadesse",
      email: "<EMAIL>",
      role: "audit_compliance_officer" as UserRole,
      organizationalLevel: "head_office",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-25T13:15:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=BT",
    },
  ]

  // Filter users based on search query and filters
  const filteredUsers = users.filter((user) => {
    // Search filter
    const matchesSearch =
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.role.toLowerCase().includes(searchQuery.toLowerCase())

    // Role filter
    const matchesRole = roleFilter === "all" || user.role === roleFilter

    // Status filter
    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "active" && user.isActive) ||
      (statusFilter === "inactive" && !user.isActive)

    // Access control - only show users the current user has permission to manage
    let hasAccess = false
    if (currentUser?.role === "super_admin") {
      hasAccess = true
    } else if (currentUser?.role === "regional_admin" && currentUser?.regionId) {
      hasAccess = user.regionId === currentUser.regionId
    } else if (currentUser?.role === "service_center_manager" && currentUser?.serviceCenterId) {
      hasAccess = user.serviceCenterId === currentUser.serviceCenterId
    }

    return matchesSearch && matchesRole && matchesStatus && hasAccess
  })

  const handleAddUser = () => {
    setSelectedUser(null)
    setIsDialogOpen(true)
  }

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setIsDialogOpen(true)
  }

  const handleDeleteUser = (user: User) => {
    // In a real app, this would call an API to delete the user
    toast({
      title: "User deleted",
      description: `${user.name} has been deleted.`,
    })
  }

  const handleUserSave = (user: User) => {
    // In a real app, this would call an API to save the user
    toast({
      title: selectedUser ? "User updated" : "User created",
      description: `${user.name} has been ${selectedUser ? "updated" : "created"}.`,
    })
    setIsDialogOpen(false)
  }

  const getRoleDisplay = (role: UserRole) => {
    return role
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  }

  const getOrganizationalLevelDisplay = (level: string) => {
    return level
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">User Management</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button size="sm" onClick={handleAddUser}>
            <UserPlus className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Users</CardTitle>
          <CardDescription>Manage system users, roles, and permissions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search users..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-[160px]">
                  <Shield className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="super_admin">Super Admin</SelectItem>
                  <SelectItem value="national_asset_manager">National Asset Manager</SelectItem>
                  <SelectItem value="national_maintenance_manager">National Maintenance Manager</SelectItem>
                  <SelectItem value="regional_admin">Regional Admin</SelectItem>
                  <SelectItem value="regional_asset_manager">Regional Asset Manager</SelectItem>
                  <SelectItem value="regional_maintenance_engineer">Regional Maintenance Engineer</SelectItem>
                  <SelectItem value="service_center_manager">Service Center Manager</SelectItem>
                  <SelectItem value="field_technician">Field Technician</SelectItem>
                  <SelectItem value="customer_service_agent">Customer Service Agent</SelectItem>
                  <SelectItem value="audit_compliance_officer">Audit & Compliance Officer</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[160px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Organizational Level</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No users found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                            <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-xs text-muted-foreground">{user.email}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-teal-600" />
                          <span>{getRoleDisplay(user.role)}</span>
                        </div>
                      </TableCell>
                      <TableCell>{getOrganizationalLevelDisplay(user.organizationalLevel)}</TableCell>
                      <TableCell>
                        {user.regionId && (
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-teal-600" />
                            <span>
                              {user.regionId === "region-001" ? "Addis Ababa" : user.regionId}
                              {user.serviceCenterId &&
                                ` - ${user.serviceCenterId === "sc-001" ? "Bole" : user.serviceCenterId}`}
                            </span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={user.isActive ? "default" : "outline"}
                          className={user.isActive ? "bg-green-500 hover:bg-green-600" : ""}
                        >
                          {user.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(user.lastLogin).toLocaleDateString("en-US", {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                        })}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleEditUser(user)}>
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleDeleteUser(user)}>
                            <Trash className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <UserDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        user={selectedUser}
        onSave={handleUserSave}
        currentUserRole={currentUser?.role}
        currentUserRegionId={currentUser?.regionId}
        currentUserServiceCenterId={currentUser?.serviceCenterId}
      />
    </div>
  )
}
