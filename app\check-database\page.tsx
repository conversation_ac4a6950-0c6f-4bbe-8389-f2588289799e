'use client'

import { useState, useEffect } from 'react'

interface TableStatus {
  exists: boolean
  rowCount: number
}

interface DatabaseStatus {
  databaseExists: boolean
  tables: Record<string, TableStatus>
  hasData: boolean
  message: string
}

export default function CheckDatabasePage() {
  const [status, setStatus] = useState<DatabaseStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const checkDatabase = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/check-database')
      const data = await response.json()

      if (data.success) {
        setStatus(data.status)
      } else {
        setError(data.error || 'Failed to check database')
      }
    } catch (err) {
      setError('Failed to connect to API')
      console.error('Database check error:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    checkDatabase()
  }, [])

  const expectedTables = [
    'app_regions',
    'app_service_centers',
    'app_users',
    'app_transformers',
    'app_alerts',
    'app_maintenance_schedules',
    'app_notifications',
    'app_performance_metrics',
    'app_weather_data'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <h1 className="text-3xl font-bold text-gray-900">🗄️ Database Status Check</h1>
          </div>
          <p className="text-gray-600">Check the current status of the dtms_eeu_db database and tables</p>
        </div>

        <div className="grid gap-6">
          {/* Database Status Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                🗄️ Database Status
              </h2>
              <button
                onClick={checkDatabase}
                disabled={loading}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
              >
                {loading ? '🔄 Checking...' : '🔄 Refresh'}
              </button>
            </div>
            <div>
              {loading && (
                <div className="flex items-center gap-2 text-blue-600">
                  🔄 Checking database status...
                </div>
              )}

              {error && (
                <div className="flex items-center gap-2 text-red-600">
                  ❌ {error}
                </div>
              )}

              {status && (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    {status.databaseExists ? '✅' : '❌'}
                    <span className="font-medium">
                      Database: {status.databaseExists ? 'EXISTS' : 'NOT FOUND'}
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    {status.hasData ? '✅' : '⚠️'}
                    <span className="font-medium">
                      Data: {status.hasData ? 'HAS DATA' : 'EMPTY'}
                    </span>
                  </div>

                  <p className="text-gray-600">{status.message}</p>
                </div>
              )}
            </div>
          </div>

          {/* Tables Status Card */}
          {status && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold flex items-center gap-2 mb-4">
                📊 Tables Status
              </h2>
              <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                {expectedTables.map((tableName) => {
                  const tableStatus = status.tables[tableName]
                  const exists = tableStatus?.exists || false
                  const rowCount = tableStatus?.rowCount || 0

                  return (
                    <div
                      key={tableName}
                      className={`p-3 rounded-lg border ${
                        exists
                          ? rowCount > 0
                            ? 'border-green-200 bg-green-50'
                            : 'border-yellow-200 bg-yellow-50'
                          : 'border-red-200 bg-red-50'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-sm">{tableName}</span>
                        {exists ? '✅' : '❌'}
                      </div>

                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded text-xs ${
                          exists ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {exists ? 'EXISTS' : 'MISSING'}
                        </span>

                        {exists && (
                          <span className={`px-2 py-1 rounded text-xs ${
                            rowCount > 0 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {rowCount} rows
                          </span>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* Summary Card */}
          {status && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">📈 Summary</h2>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {Object.keys(status.tables).length}
                  </div>
                  <div className="text-sm text-gray-600">Expected Tables</div>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {Object.values(status.tables).filter(t => t.exists).length}
                  </div>
                  <div className="text-sm text-gray-600">Existing Tables</div>
                </div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {Object.values(status.tables).reduce((sum, t) => sum + (t.rowCount || 0), 0)}
                  </div>
                  <div className="text-sm text-gray-600">Total Rows</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
