"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Badge } from '@/src/components/ui/badge'
import { Calendar, Clock, MapPin, User } from 'lucide-react'

interface MaintenanceTask {
  id: string
  transformerId: string
  transformerName: string
  type: 'routine' | 'preventive' | 'corrective' | 'emergency'
  priority: 'low' | 'medium' | 'high' | 'critical'
  scheduledDate: string
  estimatedDuration: number
  technician: string
  location: string
  status: 'scheduled' | 'in-progress' | 'completed' | 'overdue'
}

export default function MaintenanceScheduleWidget() {
  const [tasks, setTasks] = useState<MaintenanceTask[]>([
    {
      id: '1',
      transformerId: 'T-001',
      transformerName: 'Main Distribution Transformer',
      type: 'routine',
      priority: 'medium',
      scheduledDate: '2024-01-15T09:00:00',
      estimatedDuration: 120,
      technician: '<PERSON>',
      location: 'Addis Ababa - Sector 1',
      status: 'scheduled'
    },
    {
      id: '2',
      transformerId: 'T-045',
      transformerName: 'Industrial Zone Transformer',
      type: 'preventive',
      priority: 'high',
      scheduledDate: '2024-01-15T14:00:00',
      estimatedDuration: 180,
      technician: '<PERSON>',
      location: 'Oromia - Industrial Zone',
      status: 'in-progress'
    },
    {
      id: '3',
      transformerId: 'T-023',
      transformerName: 'Residential Area Transformer',
      type: 'corrective',
      priority: 'critical',
      scheduledDate: '2024-01-14T16:00:00',
      estimatedDuration: 240,
      technician: 'Mike Johnson',
      location: 'Amhara - Residential Block',
      status: 'overdue'
    }
  ])

  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Fetch maintenance schedule data
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // In a real implementation, this would fetch from the API
        await new Promise(resolve => setTimeout(resolve, 800))
      } catch (error) {
        console.error('Error fetching maintenance schedule:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-gray-100 text-gray-800'
      case 'medium':
        return 'bg-blue-100 text-blue-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'critical':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800'
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}h ${mins}m`
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Maintenance Schedule</CardTitle>
          <CardDescription>Loading maintenance schedule...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-48">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Maintenance Schedule
        </CardTitle>
        <CardDescription>
          Upcoming and ongoing maintenance tasks
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {tasks.map((task) => (
            <div key={task.id} className="border rounded-lg p-4 space-y-3">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium">{task.transformerName}</h4>
                  <p className="text-sm text-muted-foreground">ID: {task.transformerId}</p>
                </div>
                <div className="flex gap-2">
                  <Badge variant="outline" className={getPriorityColor(task.priority)}>
                    {task.priority}
                  </Badge>
                  <Badge variant="outline" className={getStatusColor(task.status)}>
                    {task.status}
                  </Badge>
                </div>
              </div>

              {/* Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDate(task.scheduledDate)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDuration(task.estimatedDuration)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span>{task.technician}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{task.location}</span>
                </div>
              </div>

              {/* Type */}
              <div>
                <Badge variant="secondary" className="capitalize">
                  {task.type} maintenance
                </Badge>
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-blue-600">
                {tasks.filter(t => t.status === 'scheduled').length}
              </div>
              <div className="text-sm text-muted-foreground">Scheduled</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-yellow-600">
                {tasks.filter(t => t.status === 'in-progress').length}
              </div>
              <div className="text-sm text-muted-foreground">In Progress</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-red-600">
                {tasks.filter(t => t.status === 'overdue').length}
              </div>
              <div className="text-sm text-muted-foreground">Overdue</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
