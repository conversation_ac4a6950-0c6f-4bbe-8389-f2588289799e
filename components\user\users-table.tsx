"use client"

import { useState, use<PERSON><PERSON>back, memo } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Button } from "@/src/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar"
import {
  Edit, Trash, MoreHorizontal, UserCheck, UserX, Mail,
  KeyRound, Shield, Copy
} from "lucide-react"
import { useToast } from "@/src/components/ui/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/src/components/ui/tooltip"
import { StatusBadge } from "./status-badge"
import { User } from "@/src/types/user-management"
import { useUserManagement } from "@/src/contexts/user-context"
import { UserDeleteDialog } from "./user-delete-dialog"
import { EnhancedUserDialog } from "./enhanced-user-dialog"

interface UsersTableProps {
  role?: string
}

function UsersTableComponent({ role }: UsersTableProps) {
  const { toast } = useToast()
  const {
    filteredUsers,
    setSelectedUser,
    selectedUser,
    updateUser,
    deleteUser,
    toggleUserStatus
  } = useUserManagement()

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  // Filter users by role if specified
  const displayUsers = role
    ? filteredUsers.filter(user => user.role === role)
    : filteredUsers

  const handleEditUser = useCallback((user: User) => {
    setSelectedUser(user)
    setIsEditDialogOpen(true)
  }, [setSelectedUser])

  const handleDeleteUser = useCallback((user: User) => {
    setSelectedUser(user)
    setIsDeleteDialogOpen(true)
  }, [setSelectedUser])

  const handleUserSave = useCallback((userData: any) => {
    updateUser(userData)
    setIsEditDialogOpen(false)
  }, [updateUser])

  const confirmDeleteUser = useCallback(() => {
    if (selectedUser) {
      deleteUser(selectedUser.id)
      setIsDeleteDialogOpen(false)
    }
  }, [selectedUser, deleteUser])

  const handleToggleUserStatus = useCallback((userId: string) => {
    toggleUserStatus(userId)
  }, [toggleUserStatus])

  const sendPasswordReset = useCallback((user: User) => {
    toast({
      title: "Password Reset Sent",
      description: `A password reset link has been sent to ${user.email}.`,
    })
  }, [toast])

  const copyEmail = useCallback((email: string) => {
    navigator.clipboard.writeText(email)
    toast({
      title: "Email Copied",
      description: "Email address has been copied to clipboard.",
    })
  }, [toast])

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>User</TableHead>
            <TableHead>Employee ID</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Department</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Last Active</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {displayUsers.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                No users found.
              </TableCell>
            </TableRow>
          ) : (
            displayUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={user.avatar || `/placeholder.svg?height=32&width=32&text=${user.name.charAt(0)}`}
                        alt={user.name}
                      />
                      <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <span className="font-mono text-sm bg-muted px-1.5 py-0.5 rounded">{user.employeeId}</span>
                  </div>
                </TableCell>
                <TableCell>{user.role}</TableCell>
                <TableCell>{user.department}</TableCell>
                <TableCell>
                  <StatusBadge status={user.status} />
                </TableCell>
                <TableCell>{user.lastActive}</TableCell>
                <TableCell className="text-right">
                  <TooltipProvider>
                    <div className="flex justify-end gap-1">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditUser(user)}
                            aria-label="Edit user"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Edit user</p>
                        </TooltipContent>
                      </Tooltip>

                      <DropdownMenu>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                aria-label="More options"
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>More options</p>
                          </TooltipContent>
                        </Tooltip>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>User Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleEditUser(user)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit User
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleUserStatus(user.id)}>
                            {user.status === "Active" ? (
                              <>
                                <UserX className="mr-2 h-4 w-4" />
                                Deactivate User
                              </>
                            ) : (
                              <>
                                <UserCheck className="mr-2 h-4 w-4" />
                                Activate User
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => sendPasswordReset(user)}>
                            <KeyRound className="mr-2 h-4 w-4" />
                            Reset Password
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => copyEmail(user.email)}>
                            <Copy className="mr-2 h-4 w-4" />
                            Copy Email
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600 focus:text-red-600"
                            onClick={() => handleDeleteUser(user)}
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TooltipProvider>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {/* Edit User Dialog */}
      {selectedUser && (
        <EnhancedUserDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          user={selectedUser}
          onSave={handleUserSave}
        />
      )}

      {/* Delete User Dialog */}
      {selectedUser && (
        <UserDeleteDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          userName={selectedUser.name}
          onDelete={confirmDeleteUser}
        />
      )}
    </>
  )
}

// Memoize the component to prevent unnecessary re-renders
export const UsersTable = memo(UsersTableComponent)
