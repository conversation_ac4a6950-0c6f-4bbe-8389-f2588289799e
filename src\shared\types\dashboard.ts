// Dashboard Types
export interface Widget {
  id: string
  type: 'transformer-status' | 'maintenance-schedule' | 'alerts-list' | 'outage-map' | 'weather-impact'
  position: {
    x: number
    y: number
    w: number
    h: number
  }
  isVisible: boolean
  settings?: Record<string, any>
}

export interface DashboardLayout {
  widgets: Widget[]
  settings: {
    autoRefresh: boolean
    refreshInterval: number
    theme: 'light' | 'dark'
  }
}

// Transformer Status Types
export interface TransformerStatus {
  id: string
  name: string
  status: 'operational' | 'maintenance' | 'warning' | 'critical'
  location: string
  region: string
  lastUpdated: string
  capacity: number
  currentLoad: number
  efficiency: number
}

export interface TransformerStatistics {
  total: number
  byStatus: {
    operational: number
    maintenance: number
    warning: number
    critical: number
  }
  byRegion: Array<{
    name: string
    count: number
    operational: number
    maintenance: number
    warning: number
    critical: number
  }>
  trends: {
    daily: number[]
    weekly: number[]
    monthly: number[]
  }
}

// Maintenance Types
export interface MaintenanceTask {
  id: string
  transformerId: string
  transformerName: string
  type: 'routine' | 'preventive' | 'corrective' | 'emergency'
  priority: 'low' | 'medium' | 'high' | 'critical'
  scheduledDate: string
  estimatedDuration: number
  technician: string
  location: string
  status: 'scheduled' | 'in-progress' | 'completed' | 'overdue'
  description?: string
  notes?: string
}

// Alert Types
export interface Alert {
  id: string
  type: 'critical' | 'warning' | 'info' | 'success'
  title: string
  message: string
  transformerId?: string
  location: string
  timestamp: string
  isRead: boolean
  priority: 'low' | 'medium' | 'high' | 'critical'
  category: 'system' | 'maintenance' | 'performance' | 'weather' | 'security'
}

// Outage Types
export interface OutageData {
  id: string
  location: string
  coordinates: [number, number]
  affectedTransformers: number
  affectedCustomers: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  startTime: string
  estimatedRestoration: string
  status: 'active' | 'investigating' | 'repairing' | 'resolved'
  cause?: string
  description?: string
}

// Weather Types
export interface WeatherData {
  location: string
  temperature: number
  humidity: number
  windSpeed: number
  condition: 'sunny' | 'cloudy' | 'rainy' | 'stormy'
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  transformersAtRisk: number
  recommendations: string[]
  forecast?: {
    next24h: string
    next48h: string
    next72h: string
  }
}

export interface WeatherImpact {
  region: string
  weather: WeatherData
  alerts: string[]
  riskAssessment: {
    equipmentRisk: 'low' | 'medium' | 'high' | 'critical'
    operationalRisk: 'low' | 'medium' | 'high' | 'critical'
    customerImpact: 'low' | 'medium' | 'high' | 'critical'
  }
}

// Dashboard Data Types
export interface DashboardData {
  transformerStats: TransformerStatistics
  maintenanceTasks: MaintenanceTask[]
  alerts: Alert[]
  outages: OutageData[]
  weatherImpacts: WeatherImpact[]
  systemHealth: {
    overallStatus: 'healthy' | 'warning' | 'critical'
    uptime: number
    performance: number
    lastUpdated: string
  }
}

// API Response Types
export interface DashboardApiResponse {
  success: boolean
  data: DashboardData
  timestamp: string
  error?: string
}

// Widget Configuration Types
export interface WidgetConfig {
  id: string
  title: string
  description: string
  component: string
  defaultSize: {
    w: number
    h: number
  }
  minSize: {
    w: number
    h: number
  }
  maxSize?: {
    w: number
    h: number
  }
  settings: {
    [key: string]: {
      type: 'boolean' | 'string' | 'number' | 'select'
      label: string
      defaultValue: any
      options?: any[]
    }
  }
}

// User Preferences Types
export interface DashboardPreferences {
  userId: string
  layout: DashboardLayout
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
  refreshSettings: {
    autoRefresh: boolean
    interval: number
  }
  displaySettings: {
    theme: 'light' | 'dark' | 'auto'
    density: 'compact' | 'normal' | 'comfortable'
    animations: boolean
  }
}

// Filter and Search Types
export interface DashboardFilters {
  region?: string[]
  status?: string[]
  priority?: string[]
  dateRange?: {
    start: string
    end: string
  }
  transformerType?: string[]
}

export interface SearchParams {
  query: string
  filters: DashboardFilters
  sortBy: string
  sortOrder: 'asc' | 'desc'
  page: number
  limit: number
}

// Export all types
export type {
  Widget,
  DashboardLayout,
  TransformerStatus,
  TransformerStatistics,
  MaintenanceTask,
  Alert,
  OutageData,
  WeatherData,
  WeatherImpact,
  DashboardData,
  DashboardApiResponse,
  WidgetConfig,
  DashboardPreferences,
  DashboardFilters,
  SearchParams
}
