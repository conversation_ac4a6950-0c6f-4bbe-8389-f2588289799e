/**
 * Maintenance repository
 * 
 * This class provides specialized methods for working with maintenance record entities.
 */

import { BaseRepository } from './base-repository';
import { MaintenanceRecord, MaintenanceStatus, MaintenanceType } from '../schema';

export class MaintenanceRepository extends BaseRepository<MaintenanceRecord> {
  constructor() {
    super('maintenanceRecords');
  }
  
  /**
   * Find maintenance records by transformer
   */
  findByTransformer(transformerId: string): MaintenanceRecord[] {
    return this.find({ transformerId });
  }
  
  /**
   * Find maintenance records by status
   */
  findByStatus(status: MaintenanceStatus | MaintenanceStatus[]): MaintenanceRecord[] {
    if (Array.isArray(status)) {
      return this.find({}).filter(record => status.includes(record.status));
    }
    return this.find({ status });
  }
  
  /**
   * Find maintenance records by type
   */
  findByType(type: MaintenanceType | MaintenanceType[]): MaintenanceRecord[] {
    if (Array.isArray(type)) {
      return this.find({}).filter(record => type.includes(record.type));
    }
    return this.find({ type });
  }
  
  /**
   * Find maintenance records by assigned user
   */
  findByAssignedUser(userId: string): MaintenanceRecord[] {
    return this.find({ assignedTo: userId });
  }
  
  /**
   * Find maintenance records by reported user
   */
  findByReportedUser(userId: string): MaintenanceRecord[] {
    return this.find({ reportedBy: userId });
  }
  
  /**
   * Find maintenance records by priority
   */
  findByPriority(priority: 'low' | 'medium' | 'high' | 'critical'): MaintenanceRecord[] {
    return this.find({ priority });
  }
  
  /**
   * Find maintenance records by date range
   */
  findByScheduledDateRange(startDate: Date, endDate: Date): MaintenanceRecord[] {
    return this.find({}).filter(record => {
      const scheduledDate = new Date(record.scheduledDate);
      return scheduledDate >= startDate && scheduledDate <= endDate;
    });
  }
  
  /**
   * Find overdue maintenance records
   */
  findOverdue(): MaintenanceRecord[] {
    const now = new Date();
    return this.find({}).filter(record => {
      return (
        record.status !== 'completed' &&
        record.status !== 'cancelled' &&
        new Date(record.scheduledDate) < now
      );
    });
  }
  
  /**
   * Find upcoming maintenance records
   */
  findUpcoming(days: number = 7): MaintenanceRecord[] {
    const now = new Date();
    const futureDate = new Date(now);
    futureDate.setDate(futureDate.getDate() + days);
    
    return this.find({}).filter(record => {
      const scheduledDate = new Date(record.scheduledDate);
      return (
        (record.status === 'scheduled' || record.status === 'in-progress') &&
        scheduledDate >= now &&
        scheduledDate <= futureDate
      );
    });
  }
  
  /**
   * Update maintenance record status
   */
  updateStatus(id: string, status: MaintenanceStatus): MaintenanceRecord | null {
    const updates: Partial<MaintenanceRecord> = { status };
    
    // Add completed date if status is completed
    if (status === 'completed') {
      updates.completedDate = new Date().toISOString();
    }
    
    return this.update(id, updates);
  }
  
  /**
   * Get maintenance statistics
   */
  getStatistics() {
    const records = this.getAll();
    
    // Count by status
    const statusCounts = records.reduce((counts, record) => {
      counts[record.status] = (counts[record.status] || 0) + 1;
      return counts;
    }, {} as Record<MaintenanceStatus, number>);
    
    // Count by type
    const typeCounts = records.reduce((counts, record) => {
      counts[record.type] = (counts[record.type] || 0) + 1;
      return counts;
    }, {} as Record<MaintenanceType, number>);
    
    // Count by priority
    const priorityCounts = records.reduce((counts, record) => {
      counts[record.priority] = (counts[record.priority] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);
    
    // Calculate average duration for completed maintenance
    const completedRecords = records.filter(
      record => record.status === 'completed' && record.actualDuration !== undefined
    );
    
    const averageDuration = completedRecords.length > 0
      ? completedRecords.reduce((sum, record) => sum + (record.actualDuration || 0), 0) / completedRecords.length
      : 0;
    
    return {
      total: records.length,
      byStatus: statusCounts,
      byType: typeCounts,
      byPriority: priorityCounts,
      averageDuration,
      overdue: this.findOverdue().length,
      upcoming: this.findUpcoming().length
    };
  }
}

// Export a singleton instance
export const maintenanceRepository = new MaintenanceRepository();
