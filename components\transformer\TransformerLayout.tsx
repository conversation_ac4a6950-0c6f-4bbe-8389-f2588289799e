"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"

interface TransformerLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
}

export function TransformerLayout({ 
  children, 
  title = "Transformer Management",
  description = "Manage and monitor transformer operations"
}: TransformerLayoutProps) {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        {description && (
          <p className="text-muted-foreground">{description}</p>
        )}
      </div>
      
      <div className="space-y-6">
        {children}
      </div>
    </div>
  )
}

export default TransformerLayout
