import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const transformerId = searchParams.get('transformerId')
    const type = searchParams.get('type') || 'all'
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = (page - 1) * limit

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    let whereConditions = []
    let queryParams = []

    if (transformerId) {
      whereConditions.push('transformer_id = ?')
      queryParams.push(transformerId)
    }

    if (startDate) {
      whereConditions.push('created_at >= ?')
      queryParams.push(startDate)
    }

    if (endDate) {
      whereConditions.push('created_at <= ?')
      queryParams.push(endDate)
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : ''

    // Get maintenance history
    const maintenanceHistory = type === 'all' || type === 'maintenance' ? await executeQuery(`
      SELECT 
        'maintenance' as record_type,
        ms.id,
        ms.transformer_id,
        ms.scheduled_date,
        ms.completed_date,
        ms.maintenance_type,
        ms.status,
        ms.priority,
        ms.description,
        ms.technician_notes,
        ms.cost,
        ms.created_at,
        t.name as transformer_name,
        t.serial_number,
        u.first_name as technician_first_name,
        u.last_name as technician_last_name
      FROM app_maintenance_schedules ms
      LEFT JOIN app_transformers t ON ms.transformer_id = t.id
      LEFT JOIN app_users u ON ms.assigned_technician_id = u.id
      ${whereClause}
      ORDER BY ms.created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]) : []

    // Get alert history
    const alertHistory = type === 'all' || type === 'alerts' ? await executeQuery(`
      SELECT 
        'alert' as record_type,
        a.id,
        a.transformer_id,
        a.title,
        a.description,
        a.severity,
        a.status,
        a.resolved_at,
        a.resolution_notes,
        a.created_at,
        t.name as transformer_name,
        t.serial_number
      FROM app_alerts a
      LEFT JOIN app_transformers t ON a.transformer_id = t.id
      ${whereClause}
      ORDER BY a.created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]) : []

    // Get inspection records
    const inspectionHistory = type === 'all' || type === 'inspections' ? await executeQuery(`
      SELECT 
        'inspection' as record_type,
        i.id,
        i.transformer_id,
        i.inspection_date,
        i.inspection_type,
        i.findings,
        i.recommendations,
        i.inspector_notes,
        i.status,
        i.created_at,
        t.name as transformer_name,
        t.serial_number,
        u.first_name as inspector_first_name,
        u.last_name as inspector_last_name
      FROM app_transformer_inspections i
      LEFT JOIN app_transformers t ON i.transformer_id = t.id
      LEFT JOIN app_users u ON i.inspector_id = u.id
      ${whereClause}
      ORDER BY i.created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]) : []

    // Get performance history
    const performanceHistory = type === 'all' || type === 'performance' ? await executeQuery(`
      SELECT 
        'performance' as record_type,
        pm.id,
        pm.transformer_id,
        pm.uptime_percentage,
        pm.efficiency_rating,
        pm.load_factor,
        pm.temperature,
        pm.voltage_regulation,
        pm.recorded_at as created_at,
        t.name as transformer_name,
        t.serial_number
      FROM app_performance_metrics pm
      LEFT JOIN app_transformers t ON pm.transformer_id = t.id
      ${whereClause}
      ORDER BY pm.recorded_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]) : []

    // Combine and sort all records
    const allRecords = [
      ...maintenanceHistory,
      ...alertHistory,
      ...inspectionHistory,
      ...performanceHistory
    ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

    // Get summary statistics
    const summaryStats = await executeQuery(`
      SELECT 
        (SELECT COUNT(*) FROM app_maintenance_schedules ${whereClause}) as total_maintenance,
        (SELECT COUNT(*) FROM app_alerts ${whereClause}) as total_alerts,
        (SELECT COUNT(*) FROM app_transformer_inspections ${whereClause}) as total_inspections,
        (SELECT COUNT(*) FROM app_performance_metrics ${whereClause}) as total_performance_records
    `, queryParams)

    // Get transformer details if specific transformer requested
    let transformerDetails = null
    if (transformerId) {
      const transformer = await executeQuery(`
        SELECT 
          t.*,
          r.name as region_name,
          sc.name as service_center_name
        FROM app_transformers t
        LEFT JOIN app_regions r ON t.region_id = r.id
        LEFT JOIN app_service_centers sc ON t.service_center_id = sc.id
        WHERE t.id = ?
      `, [transformerId])
      
      transformerDetails = transformer[0] || null
    }

    return NextResponse.json({
      success: true,
      data: {
        records: allRecords,
        transformer: transformerDetails,
        summary: summaryStats[0],
        pagination: {
          page,
          limit,
          total: allRecords.length
        }
      }
    })

  } catch (error) {
    console.error('❌ Error fetching transformer history:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch transformer history',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, transformerId, data } = body

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    switch (action) {
      case 'add_inspection':
        await executeQuery(`
          INSERT INTO app_transformer_inspections 
          (transformer_id, inspection_date, inspection_type, findings, recommendations, inspector_notes, inspector_id, status, created_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, 'completed', NOW())
        `, [
          transformerId,
          data.inspectionDate,
          data.inspectionType,
          data.findings,
          data.recommendations,
          data.inspectorNotes,
          data.inspectorId
        ])
        break

      case 'add_performance_record':
        await executeQuery(`
          INSERT INTO app_performance_metrics 
          (transformer_id, uptime_percentage, efficiency_rating, load_factor, temperature, voltage_regulation, recorded_at)
          VALUES (?, ?, ?, ?, ?, ?, NOW())
        `, [
          transformerId,
          data.uptimePercentage,
          data.efficiencyRating,
          data.loadFactor,
          data.temperature,
          data.voltageRegulation
        ])
        break

      case 'export_history':
        const exportData = await executeQuery(`
          SELECT 
            'maintenance' as type,
            ms.scheduled_date as date,
            ms.maintenance_type as details,
            ms.status,
            ms.cost,
            t.name as transformer_name
          FROM app_maintenance_schedules ms
          LEFT JOIN app_transformers t ON ms.transformer_id = t.id
          WHERE ms.transformer_id = ?
          
          UNION ALL
          
          SELECT 
            'alert' as type,
            a.created_at as date,
            CONCAT(a.title, ': ', a.description) as details,
            a.status,
            NULL as cost,
            t.name as transformer_name
          FROM app_alerts a
          LEFT JOIN app_transformers t ON a.transformer_id = t.id
          WHERE a.transformer_id = ?
          
          ORDER BY date DESC
        `, [transformerId, transformerId])

        return NextResponse.json({
          success: true,
          data: exportData,
          message: 'History export generated successfully'
        })

      case 'generate_report':
        const reportData = await executeQuery(`
          SELECT 
            t.name,
            t.serial_number,
            COUNT(DISTINCT ms.id) as maintenance_count,
            COUNT(DISTINCT a.id) as alert_count,
            COUNT(DISTINCT i.id) as inspection_count,
            AVG(pm.efficiency_rating) as avg_efficiency
          FROM app_transformers t
          LEFT JOIN app_maintenance_schedules ms ON t.id = ms.transformer_id
          LEFT JOIN app_alerts a ON t.id = a.transformer_id
          LEFT JOIN app_transformer_inspections i ON t.id = i.transformer_id
          LEFT JOIN app_performance_metrics pm ON t.id = pm.transformer_id
          WHERE t.id = ?
          GROUP BY t.id, t.name, t.serial_number
        `, [transformerId])

        return NextResponse.json({
          success: true,
          report: reportData[0],
          message: 'History report generated successfully'
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: 'Action completed successfully'
    })

  } catch (error) {
    console.error('❌ Error processing history action:', error)
    return NextResponse.json(
      {
        error: 'Failed to process action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
