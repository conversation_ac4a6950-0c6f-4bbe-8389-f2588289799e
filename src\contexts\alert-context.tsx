"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { useToast } from "@/src/components/ui/use-toast"

// Define alert types
export type AlertSeverity = "Critical" | "High" | "Medium" | "Low"
export type AlertStatus = "Active" | "Acknowledged" | "In Progress" | "Resolved"
export type AlertType = "Temperature" | "Oil Level" | "Load" | "Connection" | "Voltage" | "Physical" | "Other"

export interface Alert {
  id: string
  transformer: string
  location: string
  type: AlertType
  severity: AlertSeverity
  timestamp: string
  status: AlertStatus
  message: string
  assignedTo?: string
  notes?: string[]
  readBy?: string[]
  acknowledgedBy?: string
  acknowledgedAt?: string
  resolvedBy?: string
  resolvedAt?: string
  createdBy?: string
}

export interface AlertFilters {
  search: string
  severity: string
  type: string
  status: string
  dateRange: {
    from?: Date
    to?: Date
  }
  location: string
}

interface AlertCounts {
  critical: number
  high: number
  medium: number
  low: number
  total: number
  active: number
  acknowledged: number
  inProgress: number
  resolved: number
}

interface AlertContextType {
  alerts: Alert[]
  filteredAlerts: Alert[]
  alertCounts: AlertCounts
  filters: AlertFilters
  activeTab: string
  setActiveTab: (tab: string) => void
  updateFilter: (key: keyof AlertFilters, value: any) => void
  resetFilters: () => void
  addAlert: (alert: Omit<Alert, "id">) => void
  updateAlert: (id: string, updates: Partial<Alert>) => void
  deleteAlert: (id: string) => void
  acknowledgeAlert: (id: string, userId: string) => void
  resolveAlert: (id: string, userId: string, notes?: string) => void
  markAsRead: (id: string, userId: string) => void
  markAllAsRead: (userId: string) => void
  assignAlert: (id: string, userId: string) => void
  exportAlerts: (format: "csv" | "pdf" | "excel") => void
  configureAlertSettings: () => void
}

const AlertContext = createContext<AlertContextType | undefined>(undefined)

// Sample data for alerts
const sampleAlerts: Alert[] = [
  {
    id: "ALT-1024",
    transformer: "tr-001",
    location: "West District",
    type: "Temperature",
    severity: "High",
    timestamp: "Apr 27, 2025 10:24 AM",
    status: "Active",
    message: "Temperature exceeding normal operating range",
    notes: ["Initial alert triggered by automated system"],
    readBy: []
  },
  {
    id: "ALT-1023",
    transformer: "tr-002",
    location: "Central Area",
    type: "Oil Level",
    severity: "Critical",
    timestamp: "Apr 27, 2025 09:15 AM",
    status: "Active",
    message: "Oil level critically low, immediate action required",
    notes: ["Alert requires immediate attention", "Maintenance team notified"],
    readBy: []
  },
  {
    id: "ALT-1022",
    transformer: "tr-003",
    location: "East District",
    type: "Load",
    severity: "Medium",
    timestamp: "Apr 27, 2025 08:30 AM",
    status: "Acknowledged",
    message: "Load exceeding 85% of rated capacity",
    acknowledgedBy: "John Doe",
    acknowledgedAt: "Apr 27, 2025 08:45 AM",
    notes: ["Monitoring situation", "May require load balancing"],
    readBy: ["John Doe"]
  },
  {
    id: "ALT-1021",
    transformer: "tr-004",
    location: "North Substation",
    type: "Connection",
    severity: "Low",
    timestamp: "Apr 26, 2025 04:45 PM",
    status: "In Progress",
    message: "Communication interruption detected",
    acknowledgedBy: "Jane Smith",
    acknowledgedAt: "Apr 26, 2025 05:00 PM",
    assignedTo: "Tech Team Alpha",
    notes: ["Investigating connection issues", "Possible network outage in the area"],
    readBy: ["Jane Smith", "Tech Team Alpha"]
  },
  {
    id: "ALT-1020",
    transformer: "tr-005",
    location: "South Region",
    type: "Temperature",
    severity: "Medium",
    timestamp: "Apr 26, 2025 02:30 PM",
    status: "Resolved",
    message: "Temperature fluctuation detected",
    acknowledgedBy: "Mike Johnson",
    acknowledgedAt: "Apr 26, 2025 02:45 PM",
    resolvedBy: "Mike Johnson",
    resolvedAt: "Apr 26, 2025 03:30 PM",
    notes: ["Temperature stabilized after cooling system reset", "No further action required"],
    readBy: ["Mike Johnson", "System Admin"]
  },
  {
    id: "ALT-1019",
    transformer: "tr-006",
    location: "West District",
    type: "Voltage",
    severity: "High",
    timestamp: "Apr 25, 2025 11:20 AM",
    status: "Resolved",
    message: "Voltage spike detected",
    acknowledgedBy: "Sarah Lee",
    acknowledgedAt: "Apr 25, 2025 11:30 AM",
    resolvedBy: "Technical Team",
    resolvedAt: "Apr 25, 2025 01:15 PM",
    notes: ["Voltage regulator adjusted", "System stabilized"],
    readBy: ["Sarah Lee", "Technical Team", "System Admin"]
  },
  {
    id: "ALT-1018",
    transformer: "tr-007",
    location: "Central Area",
    type: "Physical",
    severity: "Critical",
    timestamp: "Apr 24, 2025 09:10 AM",
    status: "Resolved",
    message: "Physical damage reported - possible tampering",
    acknowledgedBy: "Security Team",
    acknowledgedAt: "Apr 24, 2025 09:20 AM",
    resolvedBy: "Maintenance Crew",
    resolvedAt: "Apr 24, 2025 02:00 PM",
    notes: ["Security footage reviewed", "Damage repaired", "Additional security measures implemented"],
    readBy: ["Security Team", "Maintenance Crew", "System Admin"]
  }
]

// Default filters
const defaultFilters: AlertFilters = {
  search: "",
  severity: "all",
  type: "all",
  status: "all",
  dateRange: {},
  location: "all"
}

export function AlertProvider({ children }: { children: ReactNode }) {
  const [alerts, setAlerts] = useState<Alert[]>(sampleAlerts)
  const [filters, setFilters] = useState<AlertFilters>(defaultFilters)
  const [activeTab, setActiveTab] = useState("active")
  const { toast } = useToast()

  // Apply filters to get filtered alerts
  const filteredAlerts = alerts.filter(alert => {
    // Filter by search query
    if (filters.search && !alert.transformer.toLowerCase().includes(filters.search.toLowerCase()) &&
        !alert.message.toLowerCase().includes(filters.search.toLowerCase()) &&
        !alert.location.toLowerCase().includes(filters.search.toLowerCase()) &&
        !alert.id.toLowerCase().includes(filters.search.toLowerCase())) {
      return false
    }

    // Filter by severity
    if (filters.severity !== "all" && alert.severity.toLowerCase() !== filters.severity.toLowerCase()) {
      return false
    }

    // Filter by type
    if (filters.type !== "all" && alert.type.toLowerCase() !== filters.type.toLowerCase()) {
      return false
    }

    // Filter by status
    if (filters.status !== "all" && alert.status.toLowerCase() !== filters.status.toLowerCase()) {
      return false
    }

    // Filter by location
    if (filters.location !== "all" && alert.location !== filters.location) {
      return false
    }

    // Filter by active tab
    if (activeTab === "active" && alert.status === "Resolved") {
      return false
    }
    if (activeTab === "resolved" && alert.status !== "Resolved") {
      return false
    }
    // "all" tab shows everything

    return true
  })

  // Calculate alert counts
  const alertCounts: AlertCounts = alerts.reduce((counts, alert) => {
    // Count by severity
    if (alert.severity === "Critical") counts.critical++
    else if (alert.severity === "High") counts.high++
    else if (alert.severity === "Medium") counts.medium++
    else if (alert.severity === "Low") counts.low++

    // Count by status
    if (alert.status === "Active") counts.active++
    else if (alert.status === "Acknowledged") counts.acknowledged++
    else if (alert.status === "In Progress") counts.inProgress++
    else if (alert.status === "Resolved") counts.resolved++

    counts.total++
    return counts
  }, {
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    total: 0,
    active: 0,
    acknowledged: 0,
    inProgress: 0,
    resolved: 0
  })

  // Update a specific filter
  const updateFilter = (key: keyof AlertFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // Reset all filters to default
  const resetFilters = () => {
    setFilters(defaultFilters)
  }

  // Add a new alert
  const addAlert = async (alert: Omit<Alert, "id">) => {
    const newAlert: Alert = {
      ...alert,
      id: `ALT-${Math.floor(1000 + Math.random() * 9000)}`,
      readBy: []
    }
    setAlerts(prev => [newAlert, ...prev])

    // Import the alert service dynamically to avoid circular dependencies
    const { alertService } = await import('@/src/services/alert-service')

    // Update transformer status based on alert severity
    if (alert.transformer) {
      await alertService.updateTransformerStatusFromAlert(alert.transformer, alert.severity)
    }

    toast({
      title: "Alert Created",
      description: `Alert ${newAlert.id} has been created successfully.`
    })
  }

  // Update an existing alert
  const updateAlert = (id: string, updates: Partial<Alert>) => {
    setAlerts(prev =>
      prev.map(alert =>
        alert.id === id ? { ...alert, ...updates } : alert
      )
    )
    toast({
      title: "Alert Updated",
      description: `Alert ${id} has been updated successfully.`
    })
  }

  // Delete an alert
  const deleteAlert = (id: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== id))
    toast({
      title: "Alert Deleted",
      description: `Alert ${id} has been deleted.`
    })
  }

  // Acknowledge an alert
  const acknowledgeAlert = (id: string, userId: string) => {
    updateAlert(id, {
      status: "Acknowledged",
      acknowledgedBy: userId,
      acknowledgedAt: new Date().toLocaleString(),
      readBy: [...(alerts.find(a => a.id === id)?.readBy || []), userId]
    })
    toast({
      title: "Alert Acknowledged",
      description: `Alert ${id} has been acknowledged.`
    })
  }

  // Resolve an alert
  const resolveAlert = async (id: string, userId: string, notes?: string) => {
    const alert = alerts.find(a => a.id === id)
    if (!alert) return

    const updatedNotes = notes
      ? [...(alert.notes || []), notes]
      : alert.notes

    updateAlert(id, {
      status: "Resolved",
      resolvedBy: userId,
      resolvedAt: new Date().toLocaleString(),
      notes: updatedNotes,
      readBy: [...(alert.readBy || []), userId]
    })

    // Import the alert service dynamically to avoid circular dependencies
    const { alertService } = await import('@/src/services/alert-service')

    // Update transformer status when alert is resolved
    if (alert.transformer) {
      await alertService.updateTransformerStatusOnAlertResolution(alert.transformer)
    }

    toast({
      title: "Alert Resolved",
      description: `Alert ${id} has been resolved.`
    })
  }

  // Mark an alert as read
  const markAsRead = (id: string, userId: string) => {
    const alert = alerts.find(a => a.id === id)
    if (!alert) return

    if (!alert.readBy?.includes(userId)) {
      updateAlert(id, {
        readBy: [...(alert.readBy || []), userId]
      })
    }
  }

  // Mark all alerts as read
  const markAllAsRead = (userId: string) => {
    setAlerts(prev =>
      prev.map(alert => {
        if (!alert.readBy?.includes(userId)) {
          return {
            ...alert,
            readBy: [...(alert.readBy || []), userId]
          }
        }
        return alert
      })
    )
    toast({
      title: "All Alerts Marked as Read",
      description: "All alerts have been marked as read."
    })
  }

  // Assign an alert to a user
  const assignAlert = (id: string, userId: string) => {
    const alert = alerts.find(a => a.id === id)
    if (!alert) return

    updateAlert(id, {
      assignedTo: userId,
      status: alert.status === "Active" ? "In Progress" : alert.status
    })
    toast({
      title: "Alert Assigned",
      description: `Alert ${id} has been assigned.`
    })
  }

  // Export alerts
  const exportAlerts = (format: "csv" | "pdf" | "excel") => {
    // In a real app, this would generate and download the file
    toast({
      title: "Alerts Exported",
      description: `Alerts have been exported in ${format.toUpperCase()} format.`
    })
  }

  // Configure alert settings
  const configureAlertSettings = () => {
    // In a real app, this would open a settings dialog
    toast({
      title: "Alert Configuration",
      description: "Alert configuration would open here."
    })
  }

  return (
    <AlertContext.Provider
      value={{
        alerts,
        filteredAlerts,
        alertCounts,
        filters,
        activeTab,
        setActiveTab,
        updateFilter,
        resetFilters,
        addAlert,
        updateAlert,
        deleteAlert,
        acknowledgeAlert,
        resolveAlert,
        markAsRead,
        markAllAsRead,
        assignAlert,
        exportAlerts,
        configureAlertSettings
      }}
    >
      {children}
    </AlertContext.Provider>
  )
}

export function useAlerts() {
  const context = useContext(AlertContext)
  if (context === undefined) {
    throw new Error("useAlerts must be used within an AlertProvider")
  }
  return context
}
