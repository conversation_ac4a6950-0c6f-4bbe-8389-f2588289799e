"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/src/components/ui/radio-group"
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar"
import type { User, UserRole } from "@/src/types/auth"

interface AssignUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: User
  onComplete: (user: User, regionId?: string, serviceCenterId?: string) => void
  currentUserRole?: UserRole
  currentUserRegionId?: string
  currentUserServiceCenterId?: string
}

export function AssignUserDialog({
  open,
  onOpenChange,
  user,
  onComplete,
  currentUserRole,
  currentUserRegionId,
  currentUserServiceCenterId,
}: AssignUserDialogProps) {
  const [assignmentType, setAssignmentType] = useState<"head_office" | "regional_office" | "service_center">(
    user.organizationalLevel,
  )
  const [selectedRegionId, setSelectedRegionId] = useState<string>(user.regionId || "")
  const [selectedServiceCenterId, setSelectedServiceCenterId] = useState<string>(user.serviceCenterId || "")

  // Reset form when dialog opens or user changes
  useEffect(() => {
    setAssignmentType(user.organizationalLevel)
    setSelectedRegionId(user.regionId || "")
    setSelectedServiceCenterId(user.serviceCenterId || "")
  }, [user, open])

  const handleSubmit = () => {
    // Create updated user object
    const updatedUser: User = {
      ...user,
      organizationalLevel: assignmentType,
      regionId: assignmentType === "head_office" ? undefined : selectedRegionId,
      serviceCenterId: assignmentType === "service_center" ? selectedServiceCenterId : undefined,
    }

    onComplete(
      updatedUser,
      assignmentType === "head_office" ? undefined : selectedRegionId,
      assignmentType === "service_center" ? selectedServiceCenterId : undefined,
    )
  }

  // Mock region data - in a real app, this would come from a context or API
  const regions = [
    { id: "region-001", name: "Addis Ababa" },
    { id: "region-002", name: "Oromia" },
    { id: "region-003", name: "Amhara" },
    { id: "region-004", name: "Tigray" },
    { id: "region-005", name: "SNNPR" },
    { id: "region-006", name: "Sidama" },
  ]

  // Filter regions based on current user's role and region
  const availableRegions = regions.filter((region) => {
    if (currentUserRole === "super_admin") return true
    if (currentUserRole === "regional_admin" && currentUserRegionId) {
      return region.id === currentUserRegionId
    }
    if (currentUserRole === "service_center_manager" && currentUserRegionId) {
      return region.id === currentUserRegionId
    }
    return false
  })

  // Mock service center data - in a real app, this would come from a context or API
  const serviceCenters = [
    { id: "sc-001", name: "Bole", regionId: "region-001" },
    { id: "sc-002", name: "Kirkos", regionId: "region-001" },
    { id: "sc-003", name: "Arada", regionId: "region-001" },
    { id: "sc-004", name: "Yeka", regionId: "region-001" },
    { id: "sc-005", name: "Akaki", regionId: "region-001" },
    { id: "sc-006", name: "Adama", regionId: "region-002" },
    { id: "sc-007", name: "Bishoftu", regionId: "region-002" },
    { id: "sc-008", name: "Bahir Dar", regionId: "region-003" },
    { id: "sc-009", name: "Gondar", regionId: "region-003" },
    { id: "sc-010", name: "Mekelle", regionId: "region-004" },
  ]

  // Filter service centers based on selected region and current user's role and service center
  const availableServiceCenters = serviceCenters.filter((sc) => {
    if (sc.regionId !== selectedRegionId) return false

    if (currentUserRole === "super_admin" || currentUserRole === "regional_admin") return true
    if (currentUserRole === "service_center_manager" && currentUserServiceCenterId) {
      return sc.id === currentUserServiceCenterId
    }
    return false
  })

  // Check if the user's role is compatible with the selected organizational level
  const isRoleCompatible = () => {
    const headOfficeRoles = [
      "super_admin",
      "national_asset_manager",
      "national_maintenance_manager",
      "audit_compliance_officer",
    ]
    const regionalOfficeRoles = ["regional_admin", "regional_asset_manager", "regional_maintenance_engineer"]
    const serviceCenterRoles = ["service_center_manager", "field_technician", "customer_service_agent"]

    if (assignmentType === "head_office" && !headOfficeRoles.includes(user.role)) {
      return false
    }
    if (assignmentType === "regional_office" && !regionalOfficeRoles.includes(user.role)) {
      return false
    }
    if (assignmentType === "service_center" && !serviceCenterRoles.includes(user.role)) {
      return false
    }

    return true
  }

  // Get warning message if role is not compatible with organizational level
  const getWarningMessage = () => {
    if (!isRoleCompatible()) {
      return `Warning: The user's role (${user.role}) is not typically assigned to ${assignmentType.replace(
        "_",
        " ",
      )}. Consider updating the user's role.`
    }
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Assign User</DialogTitle>
          <DialogDescription>Assign this user to a region or service center based on their role.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex items-center gap-3 mb-4">
            <Avatar className="h-10 w-10">
              <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
              <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium">{user.name}</p>
              <p className="text-sm text-muted-foreground">{user.email}</p>
              <p className="text-sm text-muted-foreground">
                Role:{" "}
                {user.role
                  .split("_")
                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(" ")}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Organizational Level</Label>
            <RadioGroup
              value={assignmentType}
              onValueChange={(value: "head_office" | "regional_office" | "service_center") => setAssignmentType(value)}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="head_office" id="head_office" />
                <Label htmlFor="head_office" className="cursor-pointer">
                  Head Office
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="regional_office" id="regional_office" />
                <Label htmlFor="regional_office" className="cursor-pointer">
                  Regional Office
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="service_center" id="service_center" />
                <Label htmlFor="service_center" className="cursor-pointer">
                  Service Center
                </Label>
              </div>
            </RadioGroup>
          </div>

          {assignmentType !== "head_office" && (
            <div className="space-y-2">
              <Label htmlFor="region">Region</Label>
              <Select value={selectedRegionId} onValueChange={setSelectedRegionId}>
                <SelectTrigger id="region">
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  {availableRegions.map((region) => (
                    <SelectItem key={region.id} value={region.id}>
                      {region.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {assignmentType === "service_center" && selectedRegionId && (
            <div className="space-y-2">
              <Label htmlFor="serviceCenter">Service Center</Label>
              <Select value={selectedServiceCenterId} onValueChange={setSelectedServiceCenterId}>
                <SelectTrigger id="serviceCenter">
                  <SelectValue placeholder="Select service center" />
                </SelectTrigger>
                <SelectContent>
                  {availableServiceCenters.map((sc) => (
                    <SelectItem key={sc.id} value={sc.id}>
                      {sc.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {getWarningMessage() && (
            <div className="text-sm text-amber-600 bg-amber-50 p-2 rounded-md border border-amber-200">
              {getWarningMessage()}
            </div>
          )}
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={
              (assignmentType !== "head_office" && !selectedRegionId) ||
              (assignmentType === "service_center" && !selectedServiceCenterId)
            }
          >
            Assign User
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
