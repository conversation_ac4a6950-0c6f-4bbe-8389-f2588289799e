/**
 * Reset and Seed Database for EEU DTMS
 * This script safely resets the database and seeds it with comprehensive data
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function resetAndSeedDatabase() {
  let connection;
  
  try {
    console.log('🔄 RESETTING AND SEEDING EEU DTMS DATABASE');
    console.log('=' .repeat(60));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // Step 1: Clear existing data safely
    console.log('\n🧹 Clearing existing data...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    
    const tables = [
      'app_notifications', 'app_outages', 'app_performance_metrics', 'app_weather_data',
      'app_system_settings', 'app_alerts', 'app_maintenance_schedules', 'app_transformers', 
      'app_service_centers', 'app_users'
    ];
    
    for (const table of tables) {
      try {
        await connection.execute(`DELETE FROM ${table}`);
        await connection.execute(`ALTER TABLE ${table} AUTO_INCREMENT = 1`);
        console.log(`  ✅ Cleared ${table}`);
      } catch (error) {
        console.log(`  ⚠️  Table ${table} not found or already empty`);
      }
    }
    
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    console.log('✅ Database cleared successfully');

    // Step 2: Seed comprehensive data
    console.log('\n🌱 SEEDING COMPREHENSIVE DATA');
    console.log('-' .repeat(40));
    
    // Seed Regions (using existing table structure)
    console.log('🗺️  Seeding Ethiopian Regions...');
    const regions = [
      { name: 'Addis Ababa', code: 'AA', population: 3500000, area_km2: 527.0 },
      { name: 'Oromia', code: 'OR', population: 37000000, area_km2: 353006.81 },
      { name: 'Amhara', code: 'AM', population: 21000000, area_km2: 154708.96 },
      { name: 'Tigray', code: 'TI', population: 5500000, area_km2: 50078.64 },
      { name: 'SNNP', code: 'SN', population: 20000000, area_km2: 105887.18 },
      { name: 'Somali', code: 'SO', population: 5500000, area_km2: 279252.0 },
      { name: 'Afar', code: 'AF', population: 1800000, area_km2: 96707.0 },
      { name: 'Benishangul-Gumuz', code: 'BG', population: 1100000, area_km2: 50699.0 }
    ];

    for (const region of regions) {
      await connection.execute(`
        INSERT INTO app_regions (name, code, population, area_km2)
        VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        population = VALUES(population), area_km2 = VALUES(area_km2)
      `, [region.name, region.code, region.population, region.area_km2]);
    }
    console.log(`✅ Seeded ${regions.length} regions`);

    // Seed Transformers (using existing table structure)
    console.log('⚡ Seeding Transformers...');
    const transformers = [
      { serial_number: 'EEU-AA-001', name: 'Bole Main Distribution Transformer', type: 'distribution', capacity_kva: 1000, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2020, installation_date: '2020-06-15', location_name: 'Bole Road, Near EEU Headquarters', latitude: 9.0222, longitude: 38.7468, region_id: 1, status: 'operational', efficiency_rating: 98.5, load_factor: 75.0, temperature: 65.0, oil_level: 95.0, health_index: 92.0 },
      { serial_number: 'EEU-AA-002', name: 'Megenagna Distribution Transformer', type: 'distribution', capacity_kva: 500, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2019, installation_date: '2019-08-20', location_name: 'Megenagna, Addis Ababa', latitude: 9.0299, longitude: 38.8079, region_id: 1, status: 'warning', efficiency_rating: 97.8, load_factor: 88.0, temperature: 72.0, oil_level: 85.0, health_index: 78.0 },
      { serial_number: 'EEU-OR-001', name: 'Jimma Central Distribution Transformer', type: 'distribution', capacity_kva: 500, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2019, installation_date: '2019-08-20', location_name: 'Jimma City Center', latitude: 7.6781, longitude: 36.8344, region_id: 2, status: 'operational', efficiency_rating: 97.8, load_factor: 68.0, temperature: 62.0, oil_level: 92.0, health_index: 85.0 },
      { serial_number: 'EEU-AM-001', name: 'Bahir Dar Power Distribution Transformer', type: 'power', capacity_kva: 2000, voltage_primary: 132, voltage_secondary: 33, manufacturer: 'Schneider Electric', model: 'Trihal', year_manufactured: 2021, installation_date: '2021-03-10', location_name: 'Bahir Dar Industrial Zone', latitude: 11.5959, longitude: 37.3906, region_id: 3, status: 'maintenance', efficiency_rating: 99.2, load_factor: 45.0, temperature: 58.0, oil_level: 98.0, health_index: 95.0 },
      { serial_number: 'EEU-OR-002', name: 'Adama Industrial Transformer', type: 'distribution', capacity_kva: 800, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2018, installation_date: '2018-11-12', location_name: 'Adama Industrial Park', latitude: 8.5400, longitude: 39.2675, region_id: 2, status: 'operational', efficiency_rating: 96.5, load_factor: 82.0, temperature: 68.0, oil_level: 88.0, health_index: 80.0 },
      { serial_number: 'EEU-TI-001', name: 'Mekelle Central Transformer', type: 'distribution', capacity_kva: 630, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2017, installation_date: '2017-05-08', location_name: 'Mekelle City Center', latitude: 13.4967, longitude: 39.4753, region_id: 4, status: 'critical', efficiency_rating: 94.2, load_factor: 95.0, temperature: 85.0, oil_level: 65.0, health_index: 45.0 },
      { serial_number: 'EEU-SN-001', name: 'Hawassa Distribution Transformer', type: 'distribution', capacity_kva: 400, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Schneider Electric', model: 'Trihal', year_manufactured: 2020, installation_date: '2020-09-15', location_name: 'Hawassa City Center', latitude: 7.0621, longitude: 38.4776, region_id: 5, status: 'operational', efficiency_rating: 98.0, load_factor: 65.0, temperature: 60.0, oil_level: 90.0, health_index: 88.0 },
      { serial_number: 'EEU-AA-003', name: 'Bole Airport Transformer', type: 'distribution', capacity_kva: 1250, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2022, installation_date: '2022-01-20', location_name: 'Bole International Airport', latitude: 8.9806, longitude: 38.7992, region_id: 1, status: 'operational', efficiency_rating: 99.5, load_factor: 70.0, temperature: 55.0, oil_level: 98.0, health_index: 98.0 },
      { serial_number: 'EEU-AM-002', name: 'Gondar Distribution Transformer', type: 'distribution', capacity_kva: 315, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2019, installation_date: '2019-12-05', location_name: 'Gondar City Center', latitude: 12.6090, longitude: 37.4647, region_id: 3, status: 'operational', efficiency_rating: 97.0, load_factor: 72.0, temperature: 63.0, oil_level: 87.0, health_index: 82.0 },
      { serial_number: 'EEU-SO-001', name: 'Jijiga Main Transformer', type: 'distribution', capacity_kva: 250, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Schneider Electric', model: 'Trihal', year_manufactured: 2021, installation_date: '2021-07-18', location_name: 'Jijiga City Center', latitude: 9.3500, longitude: 42.8000, region_id: 6, status: 'warning', efficiency_rating: 96.8, load_factor: 89.0, temperature: 74.0, oil_level: 82.0, health_index: 75.0 }
    ];

    for (const transformer of transformers) {
      await connection.execute(`
        INSERT INTO app_transformers (
          serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
          manufacturer, model, year_manufactured, installation_date, location_name,
          latitude, longitude, region_id, status, efficiency_rating, load_factor,
          temperature, oil_level
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        transformer.serial_number, transformer.name, transformer.type, transformer.capacity_kva,
        transformer.voltage_primary, transformer.voltage_secondary, transformer.manufacturer,
        transformer.model, transformer.year_manufactured, transformer.installation_date,
        transformer.location_name, transformer.latitude, transformer.longitude,
        transformer.region_id, transformer.status, transformer.efficiency_rating,
        transformer.load_factor, transformer.temperature, transformer.oil_level
      ]);
    }
    console.log(`✅ Seeded ${transformers.length} transformers`);

    // Seed Maintenance Schedules
    console.log('🔧 Seeding Maintenance Schedules...');
    const maintenanceSchedules = [
      { transformer_id: 1, type: 'routine', title: 'Monthly Visual Inspection - Bole Main', description: 'Regular monthly visual inspection and basic checks', scheduled_date: '2024-12-30', estimated_duration: 2, priority: 'medium', status: 'scheduled' },
      { transformer_id: 2, type: 'preventive', title: 'Quarterly Maintenance - Megenagna', description: 'Comprehensive quarterly electrical testing and oil analysis', scheduled_date: '2024-12-25', estimated_duration: 8, priority: 'high', status: 'in_progress' },
      { transformer_id: 3, type: 'routine', title: 'Monthly Inspection - Jimma Central', description: 'Monthly routine inspection and cleaning', scheduled_date: '2024-12-20', estimated_duration: 2, priority: 'low', status: 'completed' },
      { transformer_id: 4, type: 'corrective', title: 'Oil Level Restoration - Bahir Dar', description: 'Restore oil level and check for leaks', scheduled_date: '2024-12-15', estimated_duration: 4, priority: 'high', status: 'scheduled' },
      { transformer_id: 6, type: 'emergency', title: 'Critical Temperature Issue - Mekelle', description: 'Emergency response to critical temperature alert', scheduled_date: '2024-12-10', estimated_duration: 6, priority: 'critical', status: 'completed' },
      { transformer_id: 5, type: 'preventive', title: 'Annual Comprehensive Maintenance - Adama', description: 'Complete annual maintenance including oil change and testing', scheduled_date: '2025-01-15', estimated_duration: 24, priority: 'high', status: 'scheduled' },
      { transformer_id: 7, type: 'routine', title: 'Monthly Check - Hawassa', description: 'Regular monthly maintenance check', scheduled_date: '2024-12-28', estimated_duration: 3, priority: 'medium', status: 'scheduled' },
      { transformer_id: 8, type: 'preventive', title: 'Airport Transformer Maintenance', description: 'Critical infrastructure maintenance for airport transformer', scheduled_date: '2025-01-05', estimated_duration: 12, priority: 'critical', status: 'scheduled' }
    ];

    for (const schedule of maintenanceSchedules) {
      await connection.execute(`
        INSERT INTO app_maintenance_schedules (
          transformer_id, type, title, description, scheduled_date, estimated_duration, priority, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        schedule.transformer_id, schedule.type, schedule.title, schedule.description,
        schedule.scheduled_date, schedule.estimated_duration, schedule.priority, schedule.status
      ]);
    }
    console.log(`✅ Seeded ${maintenanceSchedules.length} maintenance schedules`);

    // Seed Alerts
    console.log('🚨 Seeding Alerts...');
    const alerts = [
      { transformer_id: 2, title: 'High Temperature Alert', description: 'Transformer temperature has exceeded 70°C. Immediate inspection recommended.', severity: 'high', type: 'temperature', priority: 'high', is_resolved: false },
      { transformer_id: 2, title: 'Overload Warning', description: 'Transformer is operating at 88% of capacity, exceeding the recommended 85% threshold.', severity: 'medium', type: 'load', priority: 'medium', is_resolved: false },
      { transformer_id: 6, title: 'Critical Temperature Alert', description: 'Transformer temperature at critical level - immediate action required', severity: 'critical', type: 'temperature', priority: 'critical', is_resolved: true },
      { transformer_id: 6, title: 'Low Oil Level Warning', description: 'Transformer oil level below recommended minimum', severity: 'medium', type: 'maintenance', priority: 'medium', is_resolved: false },
      { transformer_id: 5, title: 'High Load Factor Alert', description: 'Transformer operating at high load factor, monitoring required', severity: 'medium', type: 'load', priority: 'medium', is_resolved: false },
      { transformer_id: 10, title: 'Temperature Warning - Jijiga', description: 'Transformer temperature approaching warning threshold', severity: 'medium', type: 'temperature', priority: 'medium', is_resolved: false },
      { transformer_id: 8, title: 'Communication Test Alert', description: 'Testing alert system for critical infrastructure', severity: 'low', type: 'communication', priority: 'low', is_resolved: true },
      { transformer_id: 9, title: 'Routine Maintenance Due', description: 'Scheduled maintenance approaching for Gondar transformer', severity: 'low', type: 'maintenance', priority: 'low', is_resolved: false }
    ];

    for (const alert of alerts) {
      await connection.execute(`
        INSERT INTO app_alerts (
          transformer_id, title, description, severity, type, priority, is_resolved
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        alert.transformer_id, alert.title, alert.description, alert.severity,
        alert.type, alert.priority, alert.is_resolved
      ]);
    }
    console.log(`✅ Seeded ${alerts.length} alerts`);

    // Final summary
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 DATABASE RESET AND SEEDING COMPLETED!');
    console.log('=' .repeat(60));
    
    console.log('\n📊 DATABASE SUMMARY:');
    console.log(`  ✅ ${regions.length} Ethiopian Regions`);
    console.log(`  ✅ ${transformers.length} Transformers (Various Types & Statuses)`);
    console.log(`  ✅ ${maintenanceSchedules.length} Maintenance Schedules`);
    console.log(`  ✅ ${alerts.length} Alerts (Various Severities)`);
    
    console.log('\n🎯 DASHBOARD STATUS:');
    console.log('  • Operational Transformers: 7');
    console.log('  • Warning Status: 2');
    console.log('  • Maintenance Status: 1');
    console.log('  • Critical Status: 1');
    console.log('  • Active Alerts: 6');
    console.log('  • Resolved Alerts: 2');
    console.log('  • Scheduled Maintenance: 5');
    console.log('  • In Progress: 1');
    console.log('  • Completed: 2');
    
    console.log('\n🌟 Your EEU DTMS database is now fully populated!');
    console.log('🔗 Access dashboard at: http://localhost:3002');
    
  } catch (error) {
    console.error('❌ Error resetting and seeding database:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Export for use in other scripts
module.exports = { resetAndSeedDatabase };

// Run if called directly
if (require.main === module) {
  resetAndSeedDatabase();
}
