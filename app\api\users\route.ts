import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'

// Simple password hashing function (for development)
function hashPassword(password: string): string {
  return crypto.createHash('sha256').update(password + 'eeu-salt').digest('hex')
}

// Mock data for development
const mockUsers = [
  {
    id: 1,
    employeeId: 'EEU001',
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+251-911-123456',
    role: 'regional_admin',
    department: 'Operations',
    region: 'Addis Ababa',
    serviceCenter: 'Central Service Center',
    status: 'active',
    lastLogin: '2024-01-20T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    hireDate: '2023-06-15',
    supervisor: '<PERSON>',
    permissions: ['read_transformers', 'write_transformers', 'read_maintenance', 'write_maintenance']
  },
  {
    id: 2,
    employeeId: 'EEU002',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+251-911-234567',
    role: 'field_technician',
    department: 'Maintenance',
    region: 'Oromia',
    serviceCenter: 'Oromia Service Center',
    status: 'active',
    lastLogin: '2024-01-19T14:15:00Z',
    createdAt: '2024-01-02T00:00:00Z',
    hireDate: '2023-08-20',
    supervisor: '<PERSON> <PERSON>',
    permissions: ['read_transformers', 'read_maintenance', 'write_maintenance']
  },
  {
    id: 3,
    employeeId: 'EEU003',
    firstName: 'Ahmed',
    lastName: 'Hassan',
    email: '<EMAIL>',
    phone: '+251-911-345678',
    role: 'national_asset_manager',
    department: 'Asset Management',
    region: 'National',
    status: 'active',
    lastLogin: '2024-01-21T09:00:00Z',
    createdAt: '2024-01-03T00:00:00Z',
    hireDate: '2022-03-10',
    permissions: ['read_transformers', 'write_transformers', 'read_maintenance', 'write_maintenance', 'read_reports', 'write_reports']
  },
  {
    id: 4,
    employeeId: 'EEU004',
    firstName: 'Sarah',
    lastName: 'Wilson',
    email: '<EMAIL>',
    phone: '+251-911-456789',
    role: 'customer_service_agent',
    department: 'Customer Service',
    region: 'Amhara',
    status: 'inactive',
    lastLogin: '2024-01-10T16:45:00Z',
    createdAt: '2024-01-04T00:00:00Z',
    hireDate: '2023-11-05',
    supervisor: 'Ahmed Hassan',
    permissions: ['read_transformers', 'read_alerts']
  },
  {
    id: 5,
    employeeId: 'EEU005',
    firstName: 'Michael',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+251-911-567890',
    role: 'super_admin',
    department: 'IT Administration',
    region: 'National',
    status: 'active',
    lastLogin: '2024-01-21T11:20:00Z',
    createdAt: '2024-01-05T00:00:00Z',
    hireDate: '2021-01-15',
    permissions: ['*'] // All permissions
  }
]

const mockRoles = [
  {
    id: 1,
    name: 'super_admin',
    displayName: 'Super Administrator',
    description: 'Full system access with all permissions',
    level: 10,
    permissions: ['*']
  },
  {
    id: 2,
    name: 'national_asset_manager',
    displayName: 'National Asset Manager',
    description: 'National level asset management and oversight',
    level: 8,
    permissions: ['read_transformers', 'write_transformers', 'read_maintenance', 'write_maintenance', 'read_reports', 'write_reports', 'read_analytics']
  },
  {
    id: 3,
    name: 'regional_admin',
    displayName: 'Regional Administrator',
    description: 'Regional level administration and management',
    level: 6,
    permissions: ['read_transformers', 'write_transformers', 'read_maintenance', 'write_maintenance', 'read_reports', 'read_users']
  },
  {
    id: 4,
    name: 'field_technician',
    displayName: 'Field Technician',
    description: 'Field operations and maintenance tasks',
    level: 4,
    permissions: ['read_transformers', 'read_maintenance', 'write_maintenance', 'read_alerts']
  },
  {
    id: 5,
    name: 'customer_service_agent',
    displayName: 'Customer Service Agent',
    description: 'Customer support and basic system access',
    level: 2,
    permissions: ['read_transformers', 'read_alerts', 'read_reports']
  }
]

const mockDepartments = [
  {
    id: 1,
    name: 'Operations',
    code: 'OPS',
    description: 'Operational management and oversight',
    head: 'John Doe',
    budget: 5000000
  },
  {
    id: 2,
    name: 'Maintenance',
    code: 'MAINT',
    description: 'Equipment maintenance and repair',
    head: 'Jane Smith',
    budget: 3000000
  },
  {
    id: 3,
    name: 'Asset Management',
    code: 'ASSET',
    description: 'Asset tracking and lifecycle management',
    head: 'Ahmed Hassan',
    budget: 2000000
  },
  {
    id: 4,
    name: 'Customer Service',
    code: 'CS',
    description: 'Customer support and relations',
    head: 'Sarah Wilson',
    budget: 1500000
  },
  {
    id: 5,
    name: 'IT Administration',
    code: 'IT',
    description: 'Information technology and systems',
    head: 'Michael Johnson',
    budget: 2500000
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const role = searchParams.get('role') || ''
    const status = searchParams.get('status') || ''
    const department = searchParams.get('department') || ''

    // Filter users based on query parameters
    let filteredUsers = mockUsers

    if (search) {
      filteredUsers = filteredUsers.filter(user =>
        user.firstName.toLowerCase().includes(search.toLowerCase()) ||
        user.lastName.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase()) ||
        user.employeeId.toLowerCase().includes(search.toLowerCase())
      )
    }

    if (role && role !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.role === role)
    }

    if (status && status !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.status === status)
    }

    if (department && department !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.department === department)
    }

    // Pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex)

    // Remove sensitive information
    const safeUsers = paginatedUsers.map(user => {
      const { ...safeUser } = user
      return safeUser
    })

    return NextResponse.json({
      success: true,
      data: {
        users: safeUsers,
        roles: mockRoles,
        departments: mockDepartments,
        pagination: {
          page,
          limit,
          total: filteredUsers.length,
          totalPages: Math.ceil(filteredUsers.length / limit)
        }
      }
    })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      employeeId,
      firstName,
      lastName,
      email,
      phone,
      role,
      department,
      region,
      serviceCenter,
      supervisor,
      hireDate,
      password
    } = body

    // Validate required fields
    if (!employeeId || !firstName || !lastName || !email || !role || !department || !password) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = mockUsers.find(user =>
      user.email === email || user.employeeId === employeeId
    )

    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'User with this email or employee ID already exists' },
        { status: 409 }
      )
    }

    // Hash password
    const hashedPassword = hashPassword(password)

    // Create new user
    const newUser = {
      id: Math.max(...mockUsers.map(u => u.id)) + 1,
      employeeId,
      firstName,
      lastName,
      email,
      phone: phone || '',
      role,
      department,
      region: region || '',
      serviceCenter: serviceCenter || '',
      supervisor: supervisor || '',
      hireDate: hireDate || new Date().toISOString().split('T')[0],
      status: 'active' as const,
      createdAt: new Date().toISOString(),
      lastLogin: undefined,
      permissions: mockRoles.find(r => r.name === role)?.permissions || []
    }

    // In a real application, save to database
    mockUsers.push(newUser)

    // Remove sensitive information from response
    const { ...safeUser } = newUser

    return NextResponse.json({
      success: true,
      data: { user: safeUser },
      message: 'User created successfully'
    })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create user' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      )
    }

    const userIndex = mockUsers.findIndex(user => user.id === id)
    if (userIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Update user
    mockUsers[userIndex] = {
      ...mockUsers[userIndex],
      ...updateData,
      id // Ensure ID doesn't change
    }

    // Remove sensitive information from response
    const { ...safeUser } = mockUsers[userIndex]

    return NextResponse.json({
      success: true,
      data: { user: safeUser },
      message: 'User updated successfully'
    })
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update user' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = parseInt(searchParams.get('id') || '0')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      )
    }

    const userIndex = mockUsers.findIndex(user => user.id === id)
    if (userIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Remove user
    mockUsers.splice(userIndex, 1)

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete user' },
      { status: 500 }
    )
  }
}
