/**
 * Transformer repository
 * 
 * This class provides specialized methods for working with transformer entities.
 */

import { BaseRepository } from './base-repository';
import { Transformer, TransformerStatus } from '../schema';

export class TransformerRepository extends BaseRepository<Transformer> {
  constructor() {
    super('transformers');
  }
  
  /**
   * Find transformers by region
   */
  findByRegion(regionId: string): Transformer[] {
    return this.find({ regionId });
  }
  
  /**
   * Find transformers by service center
   */
  findByServiceCenter(serviceCenterId: string): Transformer[] {
    return this.find({ serviceCenterId });
  }
  
  /**
   * Find transformers by status
   */
  findByStatus(status: TransformerStatus | TransformerStatus[]): Transformer[] {
    if (Array.isArray(status)) {
      return this.find({}).filter(transformer => status.includes(transformer.status));
    }
    return this.find({ status });
  }
  
  /**
   * Find transformers by capacity range
   */
  findByCapacityRange(minCapacity: number, maxCapacity: number): Transformer[] {
    return this.find({}).filter(
      transformer => transformer.capacity >= minCapacity && transformer.capacity <= maxCapacity
    );
  }
  
  /**
   * Find transformers by tags
   */
  findByTags(tags: string[]): Transformer[] {
    return this.find({}).filter(
      transformer => tags.every(tag => transformer.tags.includes(tag))
    );
  }
  
  /**
   * Find transformers that need maintenance
   */
  findNeedingMaintenance(): Transformer[] {
    const now = new Date();
    return this.find({}).filter(transformer => {
      // Check if next maintenance date is in the past
      if (transformer.nextMaintenanceDate && new Date(transformer.nextMaintenanceDate) <= now) {
        return true;
      }
      
      // Check if health index is below threshold
      if (transformer.metrics.healthIndex < 50) {
        return true;
      }
      
      // Check if status indicates maintenance needed
      return ['warning', 'critical'].includes(transformer.status);
    });
  }
  
  /**
   * Find transformers by search term (across multiple fields)
   */
  search(term: string): Transformer[] {
    const searchTerm = term.toLowerCase();
    return this.find({}).filter(transformer => {
      return (
        transformer.name.toLowerCase().includes(searchTerm) ||
        transformer.serialNumber.toLowerCase().includes(searchTerm) ||
        transformer.type.toLowerCase().includes(searchTerm) ||
        transformer.manufacturer.toLowerCase().includes(searchTerm) ||
        transformer.model.toLowerCase().includes(searchTerm) ||
        transformer.location.address.toLowerCase().includes(searchTerm) ||
        transformer.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    });
  }
  
  /**
   * Get transformer statistics
   */
  getStatistics() {
    const transformers = this.getAll();
    
    // Count by status
    const statusCounts = transformers.reduce((counts, transformer) => {
      counts[transformer.status] = (counts[transformer.status] || 0) + 1;
      return counts;
    }, {} as Record<TransformerStatus, number>);
    
    // Count by region
    const regionCounts = transformers.reduce((counts, transformer) => {
      counts[transformer.regionId] = (counts[transformer.regionId] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);
    
    // Count by capacity
    const capacityCounts = transformers.reduce((counts, transformer) => {
      const capacityKey = `${transformer.capacity}kVA`;
      counts[capacityKey] = (counts[capacityKey] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);
    
    // Calculate average health index
    const averageHealthIndex = transformers.reduce(
      (sum, transformer) => sum + transformer.metrics.healthIndex, 
      0
    ) / (transformers.length || 1);
    
    return {
      total: transformers.length,
      byStatus: statusCounts,
      byRegion: regionCounts,
      byCapacity: capacityCounts,
      averageHealthIndex
    };
  }
}

// Export a singleton instance
export const transformerRepository = new TransformerRepository();
