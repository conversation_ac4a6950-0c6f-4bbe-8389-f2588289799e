import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    const dataDir = path.join(process.cwd(), 'data');
    const filePath = path.join(dataDir, 'regions.json');
    
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ error: 'Data not found' }, { status: 404 });
    }
    
    const data = fs.readFileSync(filePath, 'utf8');
    const regions = JSON.parse(data);
    
    return NextResponse.json(regions);
  } catch (error) {
    console.error('Error fetching regions:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
