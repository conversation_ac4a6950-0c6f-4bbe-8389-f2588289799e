import { transformerService } from './transformer-service';
import { supabaseTransformerService } from './supabase-transformer-service';
import { userService } from './user-service';
import { supabaseUserService } from './supabase-user-service';
import { serviceService } from './service-service';
import { supabaseServiceService } from './supabase-service-service';
// Import other services as needed

// Determine if we should use Supabase or mock data
const useSupabase = process.env.NEXT_PUBLIC_USE_SUPABASE === 'true';

/**
 * Service factory that returns either the mock service or the Supabase service
 * based on the NEXT_PUBLIC_USE_SUPABASE environment variable
 */
export const serviceFactory = {
  /**
   * Get the transformer service
   */
  getTransformerService() {
    return useSupabase ? supabaseTransformerService : transformerService;
  },

  /**
   * Get the user service
   */
  getUserService() {
    return useSupabase ? supabaseUserService : userService;
  },

  /**
   * Get the service center service
   */
  getServiceService() {
    return useSupabase ? supabaseServiceService : serviceService;
  },

  // Add other services as needed
};
