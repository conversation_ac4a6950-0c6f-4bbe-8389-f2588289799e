/**
 * Common Types
 * Shared type definitions used across the application
 */

// Base entity interface
export interface BaseEntity {
  id: string
  createdAt: Date | string
  updatedAt: Date | string
  deletedAt?: Date | string | null
  createdBy?: string
  updatedBy?: string
}

// Pagination types
export interface PaginationParams {
  page?: number
  limit?: number
  offset?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationMeta {
  total: number
  page: number
  limit: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  meta: PaginationMeta
}

// Filter types
export interface FilterParams {
  search?: string
  status?: string | string[]
  dateFrom?: string
  dateTo?: string
  [key: string]: any
}

export interface SortParams {
  field: string
  direction: 'asc' | 'desc'
}

// API response types
export interface ApiSuccessResponse<T = any> {
  success: true
  data: T
  message?: string
  meta?: PaginationMeta
}

export interface ApiErrorResponse {
  success: false
  error: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse

// Form types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'date' | 'file'
  required?: boolean
  placeholder?: string
  options?: { label: string; value: string | number }[]
  validation?: {
    min?: number
    max?: number
    pattern?: RegExp
    custom?: (value: any) => boolean | string
  }
}

export interface FormState {
  values: Record<string, any>
  errors: Record<string, string>
  touched: Record<string, boolean>
  isSubmitting: boolean
  isValid: boolean
}

// Status types
export type Status = 'active' | 'inactive' | 'pending' | 'suspended' | 'archived'

export type Priority = 'low' | 'medium' | 'high' | 'critical'

export type AlertType = 'success' | 'error' | 'warning' | 'info'

// Location types
export interface Coordinates {
  latitude: number
  longitude: number
}

export interface Address {
  street?: string
  city?: string
  region: string
  country?: string
  postalCode?: string
}

export interface Location extends Coordinates {
  address?: Address
  region: string
  serviceCenter?: string
  installationSite?: string
}

// File types
export interface FileInfo {
  id: string
  name: string
  size: number
  type: string
  url: string
  uploadedAt: Date | string
  uploadedBy: string
}

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

// Notification types
export interface Notification {
  id: string
  type: AlertType
  title: string
  message: string
  read: boolean
  createdAt: Date | string
  expiresAt?: Date | string
  actions?: NotificationAction[]
}

export interface NotificationAction {
  label: string
  action: string
  variant?: 'primary' | 'secondary' | 'danger'
}

// Audit log types
export interface AuditLog extends BaseEntity {
  userId: string
  action: string
  resource: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

// Search types
export interface SearchResult<T = any> {
  item: T
  score: number
  highlights?: Record<string, string[]>
}

export interface SearchResponse<T = any> {
  results: SearchResult<T>[]
  total: number
  query: string
  took: number
}

// Chart data types
export interface ChartDataPoint {
  x: string | number | Date
  y: number
  label?: string
  color?: string
}

export interface ChartSeries {
  name: string
  data: ChartDataPoint[]
  color?: string
  type?: 'line' | 'bar' | 'area' | 'pie'
}

export interface ChartConfig {
  title?: string
  subtitle?: string
  xAxis?: {
    title?: string
    type?: 'category' | 'datetime' | 'numeric'
  }
  yAxis?: {
    title?: string
    min?: number
    max?: number
  }
  legend?: {
    show?: boolean
    position?: 'top' | 'bottom' | 'left' | 'right'
  }
  colors?: string[]
  responsive?: boolean
  animations?: boolean
}

// Table types
export interface TableColumn<T = any> {
  key: keyof T | string
  title: string
  sortable?: boolean
  filterable?: boolean
  width?: number | string
  align?: 'left' | 'center' | 'right'
  render?: (value: any, record: T, index: number) => React.ReactNode
}

export interface TableProps<T = any> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: PaginationParams & PaginationMeta
  selection?: {
    selectedKeys: string[]
    onSelectionChange: (keys: string[]) => void
  }
  sorting?: {
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  }
  filtering?: {
    filters: FilterParams
    onFilterChange: (filters: FilterParams) => void
  }
}

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system'

export interface ThemeConfig {
  mode: ThemeMode
  primaryColor: string
  accentColor: string
  borderRadius: number
  fontSize: 'small' | 'medium' | 'large'
}

// Language types
export type Language = 'en' | 'am' // English, Amharic

export interface LocaleConfig {
  language: Language
  dateFormat: string
  timeFormat: string
  numberFormat: string
  currency: string
}

// Permission types
export interface Permission {
  resource: string
  action: string
  conditions?: Record<string, any>
}

export interface Role {
  id: string
  name: string
  description?: string
  permissions: Permission[]
  hierarchy: number
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type KeyOf<T> = keyof T

export type ValueOf<T> = T[keyof T]

// Event types
export interface CustomEvent<T = any> {
  type: string
  payload: T
  timestamp: Date
  source?: string
}

// Error types
export interface AppError {
  code: string
  message: string
  details?: any
  stack?: string
  timestamp: Date
}

// Loading states
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

export interface AsyncState<T = any> {
  data: T | null
  loading: boolean
  error: AppError | null
  lastUpdated?: Date
}

// Export utility functions for type checking
export const isApiSuccessResponse = <T>(response: ApiResponse<T>): response is ApiSuccessResponse<T> => {
  return response.success === true
}

export const isApiErrorResponse = (response: ApiResponse): response is ApiErrorResponse => {
  return response.success === false
}

export const createAsyncState = <T>(initialData: T | null = null): AsyncState<T> => ({
  data: initialData,
  loading: false,
  error: null
})

export const createPaginationMeta = (
  total: number,
  page: number,
  limit: number
): PaginationMeta => ({
  total,
  page,
  limit,
  totalPages: Math.ceil(total / limit),
  hasNext: page * limit < total,
  hasPrev: page > 1
})
