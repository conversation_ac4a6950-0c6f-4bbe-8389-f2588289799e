/**
 * Setup EEU Alert System
 * This script configures alert thresholds and notification rules for EEU operations
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

// EEU Alert Configuration Templates
const alertConfigurations = [
  // Temperature Alerts
  {
    name: 'High Temperature Warning',
    type: 'temperature',
    condition: 'greater_than',
    threshold_value: 70.0,
    severity: 'medium',
    priority: 'medium',
    description: 'Transformer temperature exceeds normal operating range',
    action_required: 'Monitor closely, check cooling system',
    escalation_time: 30, // minutes
    auto_resolve: false
  },
  {
    name: 'Critical Temperature Alert',
    type: 'temperature',
    condition: 'greater_than',
    threshold_value: 85.0,
    severity: 'critical',
    priority: 'critical',
    description: 'Transformer temperature at critical level - immediate action required',
    action_required: 'Immediate inspection, consider load reduction or shutdown',
    escalation_time: 5, // minutes
    auto_resolve: false
  },
  
  // Load Alerts
  {
    name: 'High Load Warning',
    type: 'load',
    condition: 'greater_than',
    threshold_value: 85.0,
    severity: 'medium',
    priority: 'medium',
    description: 'Transformer load exceeds recommended operating level',
    action_required: 'Monitor load patterns, consider load balancing',
    escalation_time: 60, // minutes
    auto_resolve: true
  },
  {
    name: 'Overload Alert',
    type: 'load',
    condition: 'greater_than',
    threshold_value: 95.0,
    severity: 'high',
    priority: 'high',
    description: 'Transformer overload condition detected',
    action_required: 'Immediate load reduction required',
    escalation_time: 15, // minutes
    auto_resolve: false
  },
  
  // Oil Level Alerts
  {
    name: 'Low Oil Level Warning',
    type: 'oil_level',
    condition: 'less_than',
    threshold_value: 80.0,
    severity: 'medium',
    priority: 'medium',
    description: 'Transformer oil level below recommended minimum',
    action_required: 'Schedule oil level inspection and top-up',
    escalation_time: 120, // minutes
    auto_resolve: false
  },
  {
    name: 'Critical Oil Level Alert',
    type: 'oil_level',
    condition: 'less_than',
    threshold_value: 60.0,
    severity: 'critical',
    priority: 'critical',
    description: 'Transformer oil level critically low',
    action_required: 'Immediate oil level restoration required',
    escalation_time: 30, // minutes
    auto_resolve: false
  },
  
  // Maintenance Alerts
  {
    name: 'Maintenance Due Warning',
    type: 'maintenance',
    condition: 'days_overdue',
    threshold_value: 7.0,
    severity: 'medium',
    priority: 'medium',
    description: 'Scheduled maintenance is overdue',
    action_required: 'Schedule maintenance as soon as possible',
    escalation_time: 1440, // 24 hours
    auto_resolve: true
  },
  {
    name: 'Critical Maintenance Overdue',
    type: 'maintenance',
    condition: 'days_overdue',
    threshold_value: 30.0,
    severity: 'high',
    priority: 'high',
    description: 'Critical maintenance significantly overdue',
    action_required: 'Immediate maintenance scheduling required',
    escalation_time: 480, // 8 hours
    auto_resolve: false
  },
  
  // Communication Alerts
  {
    name: 'Communication Loss',
    type: 'communication',
    condition: 'no_data',
    threshold_value: 15.0, // minutes
    severity: 'high',
    priority: 'high',
    description: 'Lost communication with transformer monitoring system',
    action_required: 'Check communication equipment and connections',
    escalation_time: 30, // minutes
    auto_resolve: true
  }
];

// Notification rules for different user roles
const notificationRules = [
  {
    role: 'field_technician',
    alert_types: ['temperature', 'load', 'oil_level'],
    severity_levels: ['medium', 'high', 'critical'],
    notification_methods: ['email', 'sms'],
    immediate_notification: ['critical']
  },
  {
    role: 'maintenance_manager',
    alert_types: ['maintenance', 'temperature', 'load'],
    severity_levels: ['medium', 'high', 'critical'],
    notification_methods: ['email', 'dashboard'],
    immediate_notification: ['high', 'critical']
  },
  {
    role: 'regional_manager',
    alert_types: ['all'],
    severity_levels: ['high', 'critical'],
    notification_methods: ['email', 'dashboard'],
    immediate_notification: ['critical']
  },
  {
    role: 'operations_director',
    alert_types: ['all'],
    severity_levels: ['critical'],
    notification_methods: ['email', 'sms', 'dashboard'],
    immediate_notification: ['critical']
  }
];

async function setupAlertSystem() {
  let connection;
  
  try {
    console.log('🚨 Setting up EEU Alert System...');
    
    connection = await mysql.createConnection(config);
    
    // Create alert configurations table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_alert_configurations (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(200) NOT NULL,
        type ENUM('temperature', 'load', 'oil_level', 'voltage', 'maintenance', 
                  'communication', 'weather', 'security') NOT NULL,
        condition ENUM('greater_than', 'less_than', 'equals', 'not_equals', 
                      'days_overdue', 'no_data') NOT NULL,
        threshold_value DECIMAL(10,2) NOT NULL,
        severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        description TEXT,
        action_required TEXT,
        escalation_time INT DEFAULT 60,
        auto_resolve BOOLEAN DEFAULT false,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create notification rules table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_notification_rules (
        id INT PRIMARY KEY AUTO_INCREMENT,
        role VARCHAR(50) NOT NULL,
        alert_types JSON,
        severity_levels JSON,
        notification_methods JSON,
        immediate_notification JSON,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Insert alert configurations
    for (const config of alertConfigurations) {
      await connection.execute(`
        INSERT INTO app_alert_configurations (
          name, type, condition, threshold_value, severity, priority,
          description, action_required, escalation_time, auto_resolve,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        config.name, config.type, config.condition, config.threshold_value,
        config.severity, config.priority, config.description,
        config.action_required, config.escalation_time, config.auto_resolve
      ]);
    }

    // Insert notification rules
    for (const rule of notificationRules) {
      await connection.execute(`
        INSERT INTO app_notification_rules (
          role, alert_types, severity_levels, notification_methods,
          immediate_notification, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        rule.role,
        JSON.stringify(rule.alert_types),
        JSON.stringify(rule.severity_levels),
        JSON.stringify(rule.notification_methods),
        JSON.stringify(rule.immediate_notification)
      ]);
    }

    // Show summary
    const [configCount] = await connection.execute('SELECT COUNT(*) as count FROM app_alert_configurations');
    const [ruleCount] = await connection.execute('SELECT COUNT(*) as count FROM app_notification_rules');
    const [typeCount] = await connection.execute(`
      SELECT type, COUNT(*) as count 
      FROM app_alert_configurations 
      GROUP BY type 
      ORDER BY type
    `);
    
    console.log('\n📊 Alert System Setup Summary:');
    console.log(`  - Alert Configurations: ${configCount[0].count}`);
    console.log(`  - Notification Rules: ${ruleCount[0].count}`);
    
    console.log('\n🚨 Alert Types Configured:');
    typeCount.forEach(type => {
      console.log(`  • ${type.type}: ${type.count} configurations`);
    });
    
    console.log('\n📱 Notification Methods:');
    console.log('  • Email notifications for all severity levels');
    console.log('  • SMS for critical alerts');
    console.log('  • Dashboard notifications for real-time monitoring');
    
    console.log('\n⚡ Ethiopian Grid Considerations:');
    console.log('  • Temperature thresholds adjusted for tropical climate');
    console.log('  • Load monitoring for peak demand periods');
    console.log('  • Maintenance alerts for rainy season preparation');
    console.log('  • Communication backup for remote locations');
    
    console.log('\n🔧 Customization Options:');
    console.log('  • Adjust thresholds based on transformer type and location');
    console.log('  • Configure regional-specific alert rules');
    console.log('  • Set up escalation chains for different scenarios');
    console.log('  • Enable/disable alerts based on operational needs');
    
  } catch (error) {
    console.error('❌ Error setting up alert system:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { setupAlertSystem, alertConfigurations, notificationRules };

// Run if called directly
if (require.main === module) {
  setupAlertSystem();
}
