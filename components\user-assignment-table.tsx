"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar"
import { MapPin, Building, Shield } from "lucide-react"
import { AssignUserDialog } from "@/components/assign-user-dialog"
import type { User, UserRole } from "@/src/types/auth"

interface UserAssignmentTableProps {
  users: User[]
  onUserAssigned: (user: User, regionId?: string, serviceCenterId?: string) => void
  currentUserRole?: UserRole
  currentUserRegionId?: string
  currentUserServiceCenterId?: string
}

export function UserAssignmentTable({
  users,
  onUserAssigned,
  currentUserRole,
  currentUserRegionId,
  currentUserServiceCenterId,
}: UserAssignmentTableProps) {
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const handleAssignUser = (user: User) => {
    setSelectedUser(user)
    setIsDialogOpen(true)
  }

  const handleAssignmentComplete = (user: User, regionId?: string, serviceCenterId?: string) => {
    onUserAssigned(user, regionId, serviceCenterId)
    setIsDialogOpen(false)
  }

  // Check if current user can assign the given user
  const canAssignUser = (user: User) => {
    if (!currentUserRole) return false

    // Super admin can assign anyone
    if (currentUserRole === "super_admin") return true

    // Regional admin can assign users in their region or unassigned users
    if (currentUserRole === "regional_admin" && currentUserRegionId) {
      return (
        user.regionId === currentUserRegionId ||
        !user.regionId ||
        (user.organizationalLevel === "service_center" && !user.serviceCenterId)
      )
    }

    // Service center manager can only assign users in their service center
    if (currentUserRole === "service_center_manager" && currentUserServiceCenterId) {
      return user.serviceCenterId === currentUserServiceCenterId || !user.serviceCenterId
    }

    return false
  }

  const getRoleDisplay = (role: UserRole) => {
    return role
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  }

  const getOrganizationalLevelDisplay = (level: string) => {
    return level
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  }

  const getRegionName = (regionId?: string) => {
    if (!regionId) return "Not Assigned"

    // Mock region data - in a real app, this would come from a context or API
    const regions = {
      "region-001": "Addis Ababa",
      "region-002": "Oromia",
      "region-003": "Amhara",
      "region-004": "Tigray",
      "region-005": "SNNPR",
      "region-006": "Sidama",
    }

    return regions[regionId as keyof typeof regions] || regionId
  }

  const getServiceCenterName = (serviceCenterId?: string) => {
    if (!serviceCenterId) return "Not Assigned"

    // Mock service center data - in a real app, this would come from a context or API
    const serviceCenters = {
      "sc-001": "Bole",
      "sc-002": "Kirkos",
      "sc-003": "Arada",
      "sc-004": "Yeka",
      "sc-005": "Akaki",
    }

    return serviceCenters[serviceCenterId as keyof typeof serviceCenters] || serviceCenterId
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Organizational Level</TableHead>
              <TableHead>Region</TableHead>
              <TableHead>Service Center</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No users found.
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                        <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-teal-600" />
                      <span>{getRoleDisplay(user.role)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{getOrganizationalLevelDisplay(user.organizationalLevel)}</Badge>
                  </TableCell>
                  <TableCell>
                    {user.organizationalLevel !== "head_office" ? (
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-teal-600" />
                        <span>{getRegionName(user.regionId)}</span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">N/A</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {user.organizationalLevel === "service_center" ? (
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-teal-600" />
                        <span>{getServiceCenterName(user.serviceCenterId)}</span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">N/A</span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAssignUser(user)}
                      disabled={!canAssignUser(user)}
                    >
                      Assign
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {selectedUser && (
        <AssignUserDialog
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          user={selectedUser}
          onComplete={handleAssignmentComplete}
          currentUserRole={currentUserRole}
          currentUserRegionId={currentUserRegionId}
          currentUserServiceCenterId={currentUserServiceCenterId}
        />
      )}
    </>
  )
}
