import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') || ''
    const priority = searchParams.get('priority') || ''
    const type = searchParams.get('type') || ''
    const search = searchParams.get('search') || ''
    const offset = (page - 1) * limit

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    // Build WHERE clause
    let whereConditions = []
    let queryParams = []

    if (status) {
      whereConditions.push('wo.status = ?')
      queryParams.push(status)
    }

    if (priority) {
      whereConditions.push('wo.priority = ?')
      queryParams.push(priority)
    }

    if (type) {
      whereConditions.push('wo.work_order_type = ?')
      queryParams.push(type)
    }

    if (search) {
      whereConditions.push('(wo.title LIKE ? OR wo.description LIKE ? OR t.name LIKE ?)')
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`)
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : ''

    // Get work orders
    const workOrders = await executeQuery(`
      SELECT 
        wo.*,
        t.name as transformer_name,
        t.serial_number,
        t.location as transformer_location,
        t.status as transformer_status,
        r.name as region_name,
        sc.name as service_center_name,
        u.first_name as technician_first_name,
        u.last_name as technician_last_name,
        u.phone as technician_phone,
        creator.first_name as creator_first_name,
        creator.last_name as creator_last_name,
        DATEDIFF(wo.due_date, CURDATE()) as days_until_due,
        CASE 
          WHEN wo.due_date < CURDATE() AND wo.status NOT IN ('completed', 'cancelled') THEN 'overdue'
          WHEN DATEDIFF(wo.due_date, CURDATE()) <= 1 THEN 'urgent'
          WHEN DATEDIFF(wo.due_date, CURDATE()) <= 3 THEN 'upcoming'
          ELSE 'scheduled'
        END as urgency_status
      FROM app_work_orders wo
      LEFT JOIN app_transformers t ON wo.transformer_id = t.id
      LEFT JOIN app_regions r ON t.region_id = r.id
      LEFT JOIN app_service_centers sc ON t.service_center_id = sc.id
      LEFT JOIN app_users u ON wo.assigned_technician_id = u.id
      LEFT JOIN app_users creator ON wo.created_by = creator.id
      ${whereClause}
      ORDER BY 
        CASE wo.priority 
          WHEN 'critical' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'medium' THEN 3 
          WHEN 'low' THEN 4 
        END,
        wo.due_date ASC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset])

    // Get total count
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total
      FROM app_work_orders wo
      LEFT JOIN app_transformers t ON wo.transformer_id = t.id
      ${whereClause}
    `, queryParams)

    const total = totalResult[0]?.total || 0

    // Get summary statistics
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_orders,
        SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open_orders,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_orders,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
        SUM(CASE WHEN due_date < CURDATE() AND status NOT IN ('completed', 'cancelled') THEN 1 ELSE 0 END) as overdue_orders,
        SUM(CASE WHEN priority = 'critical' THEN 1 ELSE 0 END) as critical_orders,
        AVG(CASE WHEN status = 'completed' THEN DATEDIFF(completed_date, created_at) END) as avg_completion_days,
        SUM(estimated_cost) as total_estimated_cost,
        SUM(actual_cost) as total_actual_cost
      FROM app_work_orders wo
      LEFT JOIN app_transformers t ON wo.transformer_id = t.id
      ${whereClause}
    `, queryParams)

    // Get recent activity
    const recentActivity = await executeQuery(`
      SELECT 
        wo.id,
        wo.title,
        wo.status,
        wo.updated_at,
        t.name as transformer_name,
        u.first_name as technician_first_name,
        u.last_name as technician_last_name
      FROM app_work_orders wo
      LEFT JOIN app_transformers t ON wo.transformer_id = t.id
      LEFT JOIN app_users u ON wo.assigned_technician_id = u.id
      WHERE wo.updated_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      ORDER BY wo.updated_at DESC
      LIMIT 10
    `)

    return NextResponse.json({
      success: true,
      data: {
        workOrders: workOrders.map((order: any) => ({
          ...order,
          technician_name: order.technician_first_name && order.technician_last_name ? 
            `${order.technician_first_name} ${order.technician_last_name}` : 'Unassigned',
          creator_name: order.creator_first_name && order.creator_last_name ? 
            `${order.creator_first_name} ${order.creator_last_name}` : 'System',
          parts_required: order.parts_required ? JSON.parse(order.parts_required) : [],
          attachments: order.attachments ? JSON.parse(order.attachments) : []
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        statistics: {
          ...stats[0],
          avg_completion_days: Math.round(stats[0]?.avg_completion_days || 0),
          total_estimated_cost: Math.round(stats[0]?.total_estimated_cost || 0),
          total_actual_cost: Math.round(stats[0]?.total_actual_cost || 0)
        },
        recentActivity
      }
    })

  } catch (error) {
    console.error('❌ Error fetching work orders:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch work orders',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, workOrderId, data } = body

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    switch (action) {
      case 'create_order':
        const newOrderId = await executeQuery(`
          INSERT INTO app_work_orders 
          (title, description, transformer_id, work_order_type, priority, due_date, 
           assigned_technician_id, estimated_cost, parts_required, created_by, status, created_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'open', NOW())
        `, [
          data.title,
          data.description,
          data.transformerId,
          data.workOrderType,
          data.priority,
          data.dueDate,
          data.assignedTechnicianId,
          data.estimatedCost,
          JSON.stringify(data.partsRequired || []),
          data.createdBy
        ])
        
        return NextResponse.json({
          success: true,
          workOrderId: newOrderId.insertId,
          message: 'Work order created successfully'
        })

      case 'update_status':
        const updateData: any = {
          status: data.status,
          updated_at: new Date()
        }

        if (data.status === 'completed') {
          updateData.completed_date = new Date()
          updateData.completion_notes = data.completionNotes || ''
          updateData.actual_cost = data.actualCost || null
        } else if (data.status === 'in_progress') {
          updateData.started_date = new Date()
        }

        const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ')
        const updateValues = Object.values(updateData)

        await executeQuery(`
          UPDATE app_work_orders 
          SET ${updateFields}
          WHERE id = ?
        `, [...updateValues, workOrderId])
        break

      case 'assign_technician':
        await executeQuery(`
          UPDATE app_work_orders 
          SET assigned_technician_id = ?, updated_at = NOW()
          WHERE id = ?
        `, [data.technicianId, workOrderId])
        break

      case 'update_priority':
        await executeQuery(`
          UPDATE app_work_orders 
          SET priority = ?, updated_at = NOW()
          WHERE id = ?
        `, [data.priority, workOrderId])
        break

      case 'add_comment':
        await executeQuery(`
          INSERT INTO app_work_order_comments 
          (work_order_id, user_id, comment, created_at)
          VALUES (?, ?, ?, NOW())
        `, [workOrderId, data.userId, data.comment])
        break

      case 'upload_attachment':
        await executeQuery(`
          INSERT INTO app_work_order_attachments 
          (work_order_id, filename, file_path, uploaded_by, created_at)
          VALUES (?, ?, ?, ?, NOW())
        `, [workOrderId, data.filename, data.filePath, data.uploadedBy])
        break

      case 'bulk_update':
        const { workOrderIds, bulkAction, bulkData } = data
        
        if (bulkAction === 'assign_technician') {
          await executeQuery(`
            UPDATE app_work_orders 
            SET assigned_technician_id = ?, updated_at = NOW()
            WHERE id IN (${workOrderIds.map(() => '?').join(',')})
          `, [bulkData.technicianId, ...workOrderIds])
        } else if (bulkAction === 'update_priority') {
          await executeQuery(`
            UPDATE app_work_orders 
            SET priority = ?, updated_at = NOW()
            WHERE id IN (${workOrderIds.map(() => '?').join(',')})
          `, [bulkData.priority, ...workOrderIds])
        }
        break

      case 'generate_from_alert':
        // Create work order from alert
        const alertData = await executeQuery(`
          SELECT * FROM app_alerts WHERE id = ?
        `, [data.alertId])

        if (alertData.length > 0) {
          const alert = alertData[0]
          const newOrderFromAlert = await executeQuery(`
            INSERT INTO app_work_orders 
            (title, description, transformer_id, work_order_type, priority, due_date, 
             created_by, status, created_at)
            VALUES (?, ?, ?, 'corrective', ?, DATE_ADD(CURDATE(), INTERVAL 3 DAY), ?, 'open', NOW())
          `, [
            `Alert Response: ${alert.title}`,
            `Work order generated from alert: ${alert.description}`,
            alert.transformer_id,
            alert.severity === 'critical' ? 'critical' : 'high',
            data.createdBy
          ])

          // Update alert to reference work order
          await executeQuery(`
            UPDATE app_alerts 
            SET work_order_id = ?, updated_at = NOW()
            WHERE id = ?
          `, [newOrderFromAlert.insertId, data.alertId])

          return NextResponse.json({
            success: true,
            workOrderId: newOrderFromAlert.insertId,
            message: 'Work order created from alert successfully'
          })
        }
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: 'Action completed successfully'
    })

  } catch (error) {
    console.error('❌ Error processing work order action:', error)
    return NextResponse.json(
      {
        error: 'Failed to process action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
