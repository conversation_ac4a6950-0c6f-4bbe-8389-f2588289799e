/**
 * Add Maintenance Schedules and Alerts
 * This script adds comprehensive maintenance and alert data
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function addMaintenanceAndAlerts() {
  let connection;
  
  try {
    console.log('🔧 ADDING MAINTENANCE SCHEDULES AND ALERTS');
    console.log('=' .repeat(60));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // Get existing transformers
    const [transformers] = await connection.execute('SELECT id, name FROM app_transformers ORDER BY id');
    console.log(`📊 Found ${transformers.length} transformers to add maintenance and alerts for`);

    // 1. Add Comprehensive Maintenance Schedules
    console.log('\n🔧 ADDING COMPREHENSIVE MAINTENANCE SCHEDULES');
    console.log('-' .repeat(50));
    
    const maintenanceSchedules = [
      // Addis Ababa transformers (IDs 1-4)
      { transformer_id: 1, type: 'routine', title: 'Monthly Visual Inspection - Bole Main', description: 'Regular monthly visual inspection including oil level, temperature readings, and connection integrity checks.', scheduled_date: '2024-12-30', estimated_duration: 2, priority: 'medium', status: 'scheduled' },
      { transformer_id: 2, type: 'preventive', title: 'Quarterly Maintenance - Megenagna', description: 'Comprehensive quarterly electrical testing including insulation resistance, turns ratio, and oil analysis.', scheduled_date: '2024-12-25', estimated_duration: 8, priority: 'high', status: 'in_progress' },
      { transformer_id: 3, type: 'routine', title: 'Monthly Inspection - Bole Airport', description: 'Critical infrastructure monthly inspection with enhanced security protocols.', scheduled_date: '2025-01-05', estimated_duration: 3, priority: 'high', status: 'scheduled' },
      { transformer_id: 4, type: 'routine', title: 'Weekly Check - Merkato Commercial', description: 'Weekly routine inspection for high-traffic commercial area transformer.', scheduled_date: '2024-12-28', estimated_duration: 1, priority: 'medium', status: 'scheduled' },
      
      // Oromia transformers (IDs 5-8)
      { transformer_id: 5, type: 'routine', title: 'Monthly Inspection - Jimma Central', description: 'Monthly routine inspection and cleaning of transformer and surrounding area.', scheduled_date: '2025-01-10', estimated_duration: 2, priority: 'low', status: 'scheduled' },
      { transformer_id: 6, type: 'corrective', title: 'Temperature Investigation - Adama Industrial', description: 'Investigate high temperature readings and cooling system performance.', scheduled_date: '2024-12-20', estimated_duration: 6, priority: 'high', status: 'scheduled' },
      { transformer_id: 7, type: 'preventive', title: 'Oil Change - Nekemte', description: 'Complete oil change and filter replacement during maintenance window.', scheduled_date: '2024-12-26', estimated_duration: 12, priority: 'high', status: 'in_progress' },
      { transformer_id: 8, type: 'routine', title: 'Quarterly Check - Bishoftu Power', description: 'Quarterly inspection for power transformer with load testing.', scheduled_date: '2024-12-15', estimated_duration: 4, priority: 'medium', status: 'completed' },
      
      // Amhara transformers (IDs 9-11)
      { transformer_id: 9, type: 'routine', title: 'Monthly Inspection - Bahir Dar Power', description: 'Monthly inspection of power distribution transformer.', scheduled_date: '2024-12-22', estimated_duration: 3, priority: 'medium', status: 'scheduled' },
      { transformer_id: 10, type: 'routine', title: 'Bi-weekly Check - Gondar', description: 'Bi-weekly routine maintenance check and cleaning.', scheduled_date: '2024-12-18', estimated_duration: 2, priority: 'low', status: 'completed' },
      { transformer_id: 11, type: 'routine', title: 'Monthly Check - Debre Markos', description: 'Monthly routine inspection and performance monitoring.', scheduled_date: '2024-12-12', estimated_duration: 2, priority: 'medium', status: 'completed' },
      
      // Tigray transformers (IDs 12-13)
      { transformer_id: 12, type: 'emergency', title: 'Critical Temperature Issue - Mekelle', description: 'Emergency response to critical temperature alert and cooling system inspection.', scheduled_date: '2024-12-10', estimated_duration: 8, priority: 'critical', status: 'completed' },
      { transformer_id: 13, type: 'routine', title: 'Monthly Inspection - Adigrat', description: 'Monthly routine inspection and basic maintenance tasks.', scheduled_date: '2025-01-20', estimated_duration: 2, priority: 'medium', status: 'scheduled' },
      
      // SNNP transformers (IDs 14-15)
      { transformer_id: 14, type: 'preventive', title: 'Annual Comprehensive Maintenance - Hawassa', description: 'Complete annual maintenance including oil change, gasket replacement, and full electrical testing.', scheduled_date: '2024-12-05', estimated_duration: 24, priority: 'high', status: 'completed' },
      { transformer_id: 15, type: 'corrective', title: 'Load Balancing - Arba Minch', description: 'Investigate and correct load balancing issues affecting performance.', scheduled_date: '2024-12-15', estimated_duration: 4, priority: 'medium', status: 'scheduled' }
    ];

    let maintenanceCount = 0;
    for (const schedule of maintenanceSchedules) {
      try {
        await connection.execute(`
          INSERT INTO app_maintenance_schedules (
            transformer_id, type, title, description, scheduled_date, estimated_duration, priority, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          schedule.transformer_id, schedule.type, schedule.title, schedule.description,
          schedule.scheduled_date, schedule.estimated_duration, schedule.priority, schedule.status
        ]);
        maintenanceCount++;
        console.log(`  ✅ Added: ${schedule.title}`);
      } catch (error) {
        console.log(`  ⚠️  Skipped: ${schedule.title} (${error.message})`);
      }
    }
    console.log(`✅ Successfully added ${maintenanceCount} maintenance schedules`);

    // 2. Add Comprehensive Alerts
    console.log('\n🚨 ADDING COMPREHENSIVE ALERTS');
    console.log('-' .repeat(50));
    
    const alerts = [
      // Temperature alerts
      { transformer_id: 2, title: 'High Temperature Alert - Megenagna', description: 'Transformer temperature has exceeded 70°C threshold. Immediate inspection recommended to prevent equipment damage.', severity: 'high', type: 'temperature', priority: 'high', status: 'active', is_resolved: false },
      { transformer_id: 6, title: 'High Temperature Warning - Adama Industrial', description: 'Transformer temperature approaching warning threshold at 74°C. Monitor closely.', severity: 'medium', type: 'temperature', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 12, title: 'Critical Temperature Alert - Mekelle', description: 'Transformer temperature reached critical level of 85°C. Emergency maintenance completed successfully.', severity: 'critical', type: 'temperature', priority: 'critical', status: 'resolved', is_resolved: true, resolved_at: '2024-12-10 14:30:00' },
      { transformer_id: 15, title: 'Temperature Warning - Arba Minch', description: 'Transformer temperature at 71°C, approaching warning threshold.', severity: 'medium', type: 'temperature', priority: 'medium', status: 'active', is_resolved: false },
      
      // Load alerts
      { transformer_id: 2, title: 'Overload Warning - Megenagna', description: 'Transformer is operating at 88% of capacity, exceeding the recommended 85% threshold for sustained operation.', severity: 'medium', type: 'load', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 6, title: 'High Load Factor - Adama Industrial', description: 'Industrial transformer operating at 87% capacity during peak hours. Consider load redistribution.', severity: 'medium', type: 'load', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 12, title: 'Critical Overload - Mekelle', description: 'Transformer operating at 95% capacity. Immediate load reduction required.', severity: 'high', type: 'load', priority: 'high', status: 'active', is_resolved: false },
      { transformer_id: 15, title: 'Load Monitoring - Arba Minch', description: 'Transformer load factor at 84%, approaching threshold. Monitor during peak hours.', severity: 'low', type: 'load', priority: 'low', status: 'active', is_resolved: false },
      
      // Maintenance alerts
      { transformer_id: 1, title: 'Routine Maintenance Due - Bole Main', description: 'Scheduled monthly maintenance is approaching. Please coordinate with maintenance team.', severity: 'low', type: 'maintenance', priority: 'low', status: 'active', is_resolved: false },
      { transformer_id: 12, title: 'Low Oil Level Warning - Mekelle', description: 'Transformer oil level has dropped to 65%, below the recommended minimum of 80%. Schedule oil top-up.', severity: 'medium', type: 'maintenance', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 4, title: 'Maintenance Overdue - Merkato', description: 'Scheduled maintenance is overdue by 5 days. Immediate attention required.', severity: 'high', type: 'maintenance', priority: 'high', status: 'active', is_resolved: false },
      { transformer_id: 7, title: 'Oil Change in Progress - Nekemte', description: 'Oil change maintenance currently in progress. Expected completion in 4 hours.', severity: 'low', type: 'maintenance', priority: 'low', status: 'investigating', is_resolved: false },
      
      // Voltage alerts
      { transformer_id: 9, title: 'Voltage Fluctuation - Bahir Dar', description: 'Primary voltage fluctuations detected. Monitoring for stability.', severity: 'medium', type: 'voltage', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 3, title: 'Voltage Stability - Bole Airport', description: 'Minor voltage variations detected during peak load. System stable.', severity: 'low', type: 'voltage', priority: 'low', status: 'monitoring', is_resolved: false },
      
      // Communication and system alerts
      { transformer_id: 14, title: 'Communication Test Alert - Hawassa', description: 'Testing alert system for critical infrastructure. System functioning normally.', severity: 'low', type: 'communication', priority: 'low', status: 'resolved', is_resolved: true, resolved_at: '2024-12-01 10:00:00' },
      { transformer_id: 8, title: 'System Health Check - Bishoftu', description: 'Automated system health check completed successfully. All parameters normal.', severity: 'low', type: 'communication', priority: 'low', status: 'resolved', is_resolved: true, resolved_at: '2024-12-15 09:00:00' },
      
      // Weather-related alerts
      { transformer_id: 5, title: 'Weather Monitoring - Jimma', description: 'Heavy rainfall expected. Monitor for potential electrical issues.', severity: 'low', type: 'weather', priority: 'low', status: 'active', is_resolved: false },
      { transformer_id: 13, title: 'Dust Storm Alert - Adigrat', description: 'Dust storm conditions may affect cooling efficiency. Monitor temperature closely.', severity: 'medium', type: 'weather', priority: 'medium', status: 'active', is_resolved: false }
    ];

    let alertCount = 0;
    for (const alert of alerts) {
      try {
        await connection.execute(`
          INSERT INTO app_alerts (
            transformer_id, title, description, severity, type, priority, status, is_resolved, resolved_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          alert.transformer_id, alert.title, alert.description, alert.severity,
          alert.type, alert.priority, alert.status, alert.is_resolved,
          alert.resolved_at || null
        ]);
        alertCount++;
        console.log(`  ✅ Added: ${alert.title}`);
      } catch (error) {
        console.log(`  ⚠️  Skipped: ${alert.title} (${error.message})`);
      }
    }
    console.log(`✅ Successfully added ${alertCount} alerts`);

    // Final verification
    const [finalTransformers] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    const [finalMaintenance] = await connection.execute('SELECT COUNT(*) as count FROM app_maintenance_schedules');
    const [finalAlerts] = await connection.execute('SELECT COUNT(*) as count FROM app_alerts');

    console.log('\n' + '=' .repeat(60));
    console.log('🎉 MAINTENANCE AND ALERTS ADDITION COMPLETED!');
    console.log('=' .repeat(60));
    
    console.log('\n📊 FINAL DATABASE SUMMARY:');
    console.log(`  ⚡ Transformers: ${finalTransformers[0].count}`);
    console.log(`  🔧 Maintenance Schedules: ${finalMaintenance[0].count}`);
    console.log(`  🚨 Alerts: ${finalAlerts[0].count}`);
    
    // Show maintenance status distribution
    const [maintenanceStats] = await connection.execute(`
      SELECT status, COUNT(*) as count
      FROM app_maintenance_schedules 
      GROUP BY status 
      ORDER BY 
        CASE status 
          WHEN 'critical' THEN 1 
          WHEN 'in_progress' THEN 2 
          WHEN 'scheduled' THEN 3 
          WHEN 'completed' THEN 4 
        END
    `);
    
    console.log('\n🔧 MAINTENANCE STATUS DISTRIBUTION:');
    maintenanceStats.forEach(stat => {
      console.log(`  • ${stat.status}: ${stat.count} schedules`);
    });
    
    // Show alert severity distribution
    const [alertStats] = await connection.execute(`
      SELECT 
        severity,
        COUNT(*) as total,
        SUM(CASE WHEN is_resolved = 0 OR is_resolved IS NULL THEN 1 ELSE 0 END) as active
      FROM app_alerts 
      GROUP BY severity 
      ORDER BY 
        CASE severity 
          WHEN 'critical' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'medium' THEN 3 
          WHEN 'low' THEN 4 
        END
    `);
    
    console.log('\n🚨 ALERT SEVERITY DISTRIBUTION:');
    alertStats.forEach(stat => {
      console.log(`  • ${stat.severity}: ${stat.active} active / ${stat.total} total`);
    });
    
    console.log('\n🎯 DASHBOARD NOW DISPLAYS:');
    console.log('  • 15 Real transformers across 5 Ethiopian regions');
    console.log('  • Comprehensive maintenance schedules with realistic timelines');
    console.log('  • Multi-level alert system with various severity types');
    console.log('  • Regional distribution showing actual Ethiopian locations');
    console.log('  • Performance metrics calculated from real transformer data');
    console.log('  • Interactive charts with meaningful data distributions');
    
    console.log('\n🌟 Your dashboard is now fully populated with consistent data!');
    console.log('🔗 Refresh your browser at: http://localhost:3002/dashboard');
    
  } catch (error) {
    console.error('❌ Error adding maintenance and alerts:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { addMaintenanceAndAlerts };

// Run if called directly
if (require.main === module) {
  addMaintenanceAndAlerts();
}
