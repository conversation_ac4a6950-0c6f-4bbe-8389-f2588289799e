"use client"

import { useState, useRef } from "react"
import { Search, Filter, Plus, Download, Upload, FileText, Bar<PERSON>hart3, Flame } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Input } from "@/src/components/ui/input"
import { Button } from "@/src/components/ui/button"
import { TransformersList } from "@/components/transformers-list"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import Link from "next/link"
import { AddTransformerDialog } from "@/components/add-transformer-dialog"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/src/components/ui/dialog"
import { useToast } from "@/src/components/ui/use-toast"
import { serviceFactory } from "@/src/services/service-factory"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/src/components/ui/tabs"
import { TransformerStatusChart } from "@/components/transformer-status-chart"

export function TransformersContent() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [regionFilter, setRegionFilter] = useState("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isStatsDialogOpen, setIsStatsDialogOpen] = useState(false)
  const [importData, setImportData] = useState("")
  const [isImporting, setIsImporting] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  // Handle export to CSV
  const handleExport = async () => {
    try {
      const transformerService = serviceFactory.getTransformerService()
      const csvData = await transformerService.exportTransformersToCSV()

      // Create a blob and download link
      const blob = new Blob([csvData], { type: 'text/csv' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'transformers.csv'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Export successful",
        description: "Transformer data has been exported to CSV.",
      })
    } catch (error) {
      toast({
        title: "Export failed",
        description: "There was an error exporting the transformer data.",
        variant: "destructive",
      })
    }
  }

  // Handle file selection for import
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      if (event.target?.result) {
        setImportData(event.target.result as string)
      }
    }
    reader.readAsText(file)
  }

  // Handle import from CSV
  const handleImport = async () => {
    if (!importData) {
      toast({
        title: "Import failed",
        description: "No data to import. Please select a CSV file.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsImporting(true)
      const transformerService = serviceFactory.getTransformerService()
      const importedCount = await transformerService.importTransformersFromCSV(importData)

      toast({
        title: "Import successful",
        description: `${importedCount} transformers have been imported.`,
      })

      // Reset form and close dialog
      setImportData("")
      setIsImportDialogOpen(false)

      // Trigger refresh of transformer list
      setRefreshTrigger(prev => prev + 1)

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    } catch (error) {
      toast({
        title: "Import failed",
        description: "There was an error importing the transformer data.",
        variant: "destructive",
      })
    } finally {
      setIsImporting(false)
    }
  }

  // Handle transformer added
  const handleTransformerAdded = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Transformer Inventory</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsStatsDialogOpen(true)}>
            <BarChart3 className="mr-2 h-4 w-4" />
            Statistics
          </Button>
          <Link href="/transformers/burnt-report">
            <Button variant="outline" size="sm">
              <Flame className="mr-2 h-4 w-4 text-red-500" />
              Burnt Report
            </Button>
          </Link>
          <Button variant="outline" size="sm" onClick={() => setIsImportDialogOpen(true)}>
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button size="sm" onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Transformer
          </Button>
        </div>
      </div>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Transformers</CardTitle>
          <CardDescription>Manage your transformer inventory, view details, and track status.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search transformers..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[160px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="operational">Operational</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="burnt">Burnt</SelectItem>
                </SelectContent>
              </Select>
              <Select value={regionFilter} onValueChange={setRegionFilter}>
                <SelectTrigger className="w-[160px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Regions</SelectItem>
                  <SelectItem value="addis_ababa">Addis Ababa</SelectItem>
                  <SelectItem value="oromia">Oromia</SelectItem>
                  <SelectItem value="amhara">Amhara</SelectItem>
                  <SelectItem value="tigray">Tigray</SelectItem>
                  <SelectItem value="sidama">Sidama</SelectItem>
                  <SelectItem value="snnpr">SNNPR</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="mt-4">
            <TransformersList
              searchQuery={searchQuery}
              statusFilter={statusFilter}
              regionFilter={regionFilter}
              refreshTrigger={refreshTrigger}
            />
          </div>
        </CardContent>
      </Card>

      {/* Add Transformer Dialog */}
      <AddTransformerDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onTransformerAdded={handleTransformerAdded}
      />

      {/* Import Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Import Transformers</DialogTitle>
            <DialogDescription>
              Import transformer data from a CSV file. The file should have the following columns: Serial Number,
              Manufacturer, Model, Type, Capacity, Installation Date, Status, Region, Service Center.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="flex flex-col gap-2">
              <label htmlFor="csv-file" className="text-sm font-medium">
                Select CSV File
              </label>
              <Input
                id="csv-file"
                type="file"
                accept=".csv"
                ref={fileInputRef}
                onChange={handleFileSelect}
              />
              <p className="text-xs text-muted-foreground mt-1">
                The first row should be a header row with column names.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleImport} disabled={!importData || isImporting}>
              {isImporting ? "Importing..." : "Import"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Statistics Dialog */}
      <Dialog open={isStatsDialogOpen} onOpenChange={setIsStatsDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Transformer Statistics</DialogTitle>
            <DialogDescription>
              View statistics and distribution of transformers across the network.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            <Tabs defaultValue="status" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="status">Status Distribution</TabsTrigger>
                <TabsTrigger value="type">Type Distribution</TabsTrigger>
                <TabsTrigger value="region">Regional Distribution</TabsTrigger>
              </TabsList>
              <TabsContent value="status" className="space-y-4">
                <div className="h-[300px]">
                  <TransformerStatusChart />
                </div>
              </TabsContent>
              <TabsContent value="type" className="space-y-4">
                <div className="h-[300px] flex items-center justify-center">
                  <p className="text-muted-foreground">Type distribution chart will be available in the next update.</p>
                </div>
              </TabsContent>
              <TabsContent value="region" className="space-y-4">
                <div className="h-[300px] flex items-center justify-center">
                  <p className="text-muted-foreground">Regional distribution chart will be available in the next update.</p>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
