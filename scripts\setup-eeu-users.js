/**
 * Setup EEU User Accounts
 * This script creates user accounts for different roles in the organization
 */

const mysql = require('mysql2/promise');
const crypto = require('crypto');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

// Helper functions for password hashing
function generateSalt() {
  return crypto.randomBytes(16).toString('hex');
}

function hashPassword(password, salt) {
  return crypto.createHash('sha256').update(password + salt).digest('hex');
}

// EEU User Templates
const eeuUserTemplates = [
  // Management Level
  {
    email: '<EMAIL>',
    name: '<PERSON><PERSON>ete <PERSON>',
    role: 'ceo',
    department: 'Executive',
    region: 'National',
    phone: '+************',
    is_active: true
  },
  {
    email: '<EMAIL>',
    name: 'Ato Girma Bekele',
    role: 'operations_director',
    department: 'Operations',
    region: 'National',
    phone: '+251911000002',
    is_active: true
  },
  
  // Regional Managers
  {
    email: '<EMAIL>',
    name: 'W/ro Hanan Ahmed',
    role: 'regional_manager',
    department: 'Regional Operations',
    region: 'Addis Ababa',
    phone: '+251911000010',
    is_active: true
  },
  {
    email: '<EMAIL>',
    name: 'Ato Tadesse Bekele',
    role: 'regional_manager',
    department: 'Regional Operations',
    region: 'Oromia',
    phone: '+251911000011',
    is_active: true
  },
  {
    email: '<EMAIL>',
    name: 'Ato Mulugeta Haile',
    role: 'regional_manager',
    department: 'Regional Operations',
    region: 'Amhara',
    phone: '+251911000012',
    is_active: true
  },
  
  // Technical Staff
  {
    email: '<EMAIL>',
    name: 'Dr. Alemayehu Worku',
    role: 'chief_engineer',
    department: 'Engineering',
    region: 'National',
    phone: '+251911000020',
    is_active: true
  },
  {
    email: '<EMAIL>',
    name: 'Eng. Tigist Mengistu',
    role: 'maintenance_manager',
    department: 'Maintenance',
    region: 'National',
    phone: '+251911000021',
    is_active: true
  },
  
  // Field Technicians
  {
    email: '<EMAIL>',
    name: 'Ato Dawit Tesfaye',
    role: 'field_technician',
    department: 'Field Operations',
    region: 'Addis Ababa',
    phone: '+251911000030',
    is_active: true
  },
  {
    email: '<EMAIL>',
    name: 'Ato Mohammed Ali',
    role: 'field_technician',
    department: 'Field Operations',
    region: 'Oromia',
    phone: '+251911000031',
    is_active: true
  },
  {
    email: '<EMAIL>',
    name: 'Ato Yohannes Alemu',
    role: 'field_technician',
    department: 'Field Operations',
    region: 'Amhara',
    phone: '+251911000032',
    is_active: true
  },
  
  // System Administrators
  {
    email: '<EMAIL>',
    name: 'Ato Biniam Tekle',
    role: 'system_admin',
    department: 'IT',
    region: 'National',
    phone: '+251911000040',
    is_active: true
  }
];

async function setupUserTables() {
  let connection;
  
  try {
    connection = await mysql.createConnection(config);
    
    // Create users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        email VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        role ENUM('ceo', 'operations_director', 'regional_manager', 'chief_engineer', 
                  'maintenance_manager', 'field_technician', 'system_admin', 'super_admin') NOT NULL,
        department VARCHAR(100),
        region VARCHAR(100),
        phone VARCHAR(20),
        is_active BOOLEAN DEFAULT true,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create user authentication table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_user_auth (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        salt VARCHAR(255) NOT NULL,
        reset_token VARCHAR(255) NULL,
        reset_token_expires TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES app_users(id) ON DELETE CASCADE
      )
    `);

    console.log('✅ User tables created successfully');
    return connection;
    
  } catch (error) {
    console.error('❌ Error setting up user tables:', error);
    if (connection) await connection.end();
    throw error;
  }
}

async function createEEUUsers() {
  let connection;
  
  try {
    console.log('👥 Setting up EEU User Accounts...');
    
    connection = await setupUserTables();
    
    const defaultPassword = 'EEU2024!'; // Change this in production
    console.log(`🔐 Default password for all users: ${defaultPassword}`);
    console.log('⚠️  IMPORTANT: Change passwords after first login!');
    
    for (const userTemplate of eeuUserTemplates) {
      // Create user
      const [userResult] = await connection.execute(`
        INSERT INTO app_users (email, name, role, department, region, phone, is_active, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        name = VALUES(name), role = VALUES(role), department = VALUES(department),
        region = VALUES(region), phone = VALUES(phone), updated_at = NOW()
      `, [
        userTemplate.email, userTemplate.name, userTemplate.role,
        userTemplate.department, userTemplate.region, userTemplate.phone,
        userTemplate.is_active
      ]);

      // Get user ID
      const [userRows] = await connection.execute('SELECT id FROM app_users WHERE email = ?', [userTemplate.email]);
      const userId = userRows[0].id;

      // Create authentication record
      const salt = generateSalt();
      const passwordHash = hashPassword(defaultPassword, salt);

      await connection.execute(`
        INSERT INTO app_user_auth (user_id, password_hash, salt, created_at, updated_at)
        VALUES (?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        password_hash = VALUES(password_hash), salt = VALUES(salt), updated_at = NOW()
      `, [userId, passwordHash, salt]);
    }

    // Show summary
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM app_users');
    const [roleCount] = await connection.execute(`
      SELECT role, COUNT(*) as count 
      FROM app_users 
      GROUP BY role 
      ORDER BY role
    `);
    
    console.log('\n📊 User Account Summary:');
    console.log(`  - Total Users: ${userCount[0].count}`);
    console.log('  - By Role:');
    roleCount.forEach(role => {
      console.log(`    • ${role.role}: ${role.count} users`);
    });
    
    console.log('\n👤 Sample Login Credentials:');
    console.log('  CEO: <EMAIL>');
    console.log('  Operations Director: <EMAIL>');
    console.log('  Addis Manager: <EMAIL>');
    console.log('  Chief Engineer: <EMAIL>');
    console.log(`  Password for all: ${defaultPassword}`);
    
    console.log('\n🔒 Security Recommendations:');
    console.log('  1. Change all default passwords immediately');
    console.log('  2. Implement password complexity requirements');
    console.log('  3. Enable two-factor authentication');
    console.log('  4. Regular password rotation policy');
    
  } catch (error) {
    console.error('❌ Error creating EEU users:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { createEEUUsers, eeuUserTemplates };

// Run if called directly
if (require.main === module) {
  createEEUUsers();
}
