"use client"

import type React from "react"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Textarea } from "@/src/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useToast } from "@/src/components/ui/use-toast"
import { CalendarIcon } from "lucide-react"
import { Calendar } from "@/src/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/src/lib/utils"
import type { Transformer } from "@/src/types/transformer"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/src/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/src/components/ui/radio-group"

interface AddInspectionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  transformer?: Transformer
}

export function AddInspectionDialog({ open, onOpenChange, transformer }: AddInspectionDialogProps) {
  const [inspectionType, setInspectionType] = useState("")
  const [inspectionDate, setInspectionDate] = useState<Date | undefined>(new Date())
  const [inspector, setInspector] = useState("")
  const [findings, setFindings] = useState("")
  const [nextInspectionDate, setNextInspectionDate] = useState<Date | undefined>(new Date())
  const [result, setResult] = useState("pass")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Component condition states
  const [bushingsCondition, setBushingsCondition] = useState("good")
  const [oilLevelCondition, setOilLevelCondition] = useState("normal")
  const [leakageCondition, setLeakageCondition] = useState("none")
  const [poleStatusCondition, setPoleStatusCondition] = useState("good")

  // Component status states
  const [lArresterStatus, setLArresterStatus] = useState("good")
  const [linksStatus, setLinksStatus] = useState("good")
  const [dropOutFuseStatus, setDropOutFuseStatus] = useState("good")
  const [groundStatus, setGroundStatus] = useState("good")

  // Transformer load states
  const [loadR, setLoadR] = useState("")
  const [loadS, setLoadS] = useState("")
  const [loadT, setLoadT] = useState("")
  const [loadN, setLoadN] = useState("")

  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // In a real application, this would be an API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Inspection record added successfully",
        description: `Inspection for transformer ${transformer?.serialNumber} has been recorded.`,
      })

      // Reset form
      setInspectionType("")
      setInspectionDate(new Date())
      setInspector("")
      setFindings("")
      setNextInspectionDate(new Date())
      setResult("pass")
      setBushingsCondition("good")
      setOilLevelCondition("normal")
      setLeakageCondition("none")
      setPoleStatusCondition("good")
      setLArresterStatus("good")
      setLinksStatus("good")
      setDropOutFuseStatus("good")
      setGroundStatus("good")
      setLoadR("")
      setLoadS("")
      setLoadT("")
      setLoadN("")
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "Error adding inspection record",
        description: "There was an error adding the inspection record. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Inspection Record</DialogTitle>
          <DialogDescription>
            Add a new inspection record for transformer {transformer?.serialNumber || ""}. Fill out the form below with
            all inspection details.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="inspectionType">Inspection Type</Label>
                <Select value={inspectionType} onValueChange={setInspectionType} required>
                  <SelectTrigger id="inspectionType">
                    <SelectValue placeholder="Select inspection type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="visual">Visual Inspection</SelectItem>
                    <SelectItem value="thermal">Thermal Imaging</SelectItem>
                    <SelectItem value="insulation">Insulation Test</SelectItem>
                    <SelectItem value="acoustic">Acoustic Emission</SelectItem>
                    <SelectItem value="comprehensive">Comprehensive Inspection</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="inspector">Inspector Name</Label>
                <Input
                  id="inspector"
                  value={inspector}
                  onChange={(e) => setInspector(e.target.value)}
                  placeholder="Name of the inspector"
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="inspectionDate">Inspection Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !inspectionDate && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {inspectionDate ? format(inspectionDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={inspectionDate} onSelect={setInspectionDate} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="nextInspectionDate">Next Inspection Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !nextInspectionDate && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {nextInspectionDate ? format(nextInspectionDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={nextInspectionDate}
                      onSelect={setNextInspectionDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Components Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label>Lightning Arrester</Label>
                      <RadioGroup
                        value={lArresterStatus}
                        onValueChange={setLArresterStatus}
                        className="flex gap-4 mt-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="good" id="la-good" />
                          <Label htmlFor="la-good" className="font-normal">
                            Good
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="bad" id="la-bad" />
                          <Label htmlFor="la-bad" className="font-normal">
                            Bad
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="na" id="la-na" />
                          <Label htmlFor="la-na" className="font-normal">
                            N/A
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div>
                      <Label>Links</Label>
                      <RadioGroup value={linksStatus} onValueChange={setLinksStatus} className="flex gap-4 mt-2">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="good" id="links-good" />
                          <Label htmlFor="links-good" className="font-normal">
                            Good
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="bad" id="links-bad" />
                          <Label htmlFor="links-bad" className="font-normal">
                            Bad
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="na" id="links-na" />
                          <Label htmlFor="links-na" className="font-normal">
                            N/A
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div>
                      <Label>Drop Out Fuse</Label>
                      <RadioGroup
                        value={dropOutFuseStatus}
                        onValueChange={setDropOutFuseStatus}
                        className="flex gap-4 mt-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="good" id="fuse-good" />
                          <Label htmlFor="fuse-good" className="font-normal">
                            Good
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="bad" id="fuse-bad" />
                          <Label htmlFor="fuse-bad" className="font-normal">
                            Bad
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="na" id="fuse-na" />
                          <Label htmlFor="fuse-na" className="font-normal">
                            N/A
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div>
                      <Label>Ground</Label>
                      <RadioGroup value={groundStatus} onValueChange={setGroundStatus} className="flex gap-4 mt-2">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="good" id="ground-good" />
                          <Label htmlFor="ground-good" className="font-normal">
                            Good
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="bad" id="ground-bad" />
                          <Label htmlFor="ground-bad" className="font-normal">
                            Bad
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="na" id="ground-na" />
                          <Label htmlFor="ground-na" className="font-normal">
                            N/A
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Conditions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label>Bushings</Label>
                      <RadioGroup
                        value={bushingsCondition}
                        onValueChange={setBushingsCondition}
                        className="flex gap-4 mt-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="good" id="bushings-good" />
                          <Label htmlFor="bushings-good" className="font-normal">
                            Good
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="bad" id="bushings-bad" />
                          <Label htmlFor="bushings-bad" className="font-normal">
                            Bad
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="na" id="bushings-na" />
                          <Label htmlFor="bushings-na" className="font-normal">
                            N/A
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div>
                      <Label>Oil Level</Label>
                      <RadioGroup
                        value={oilLevelCondition}
                        onValueChange={setOilLevelCondition}
                        className="flex gap-4 mt-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="normal" id="oil-normal" />
                          <Label htmlFor="oil-normal" className="font-normal">
                            Normal
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="low" id="oil-low" />
                          <Label htmlFor="oil-low" className="font-normal">
                            Low
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="na" id="oil-na" />
                          <Label htmlFor="oil-na" className="font-normal">
                            N/A
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div>
                      <Label>Leakage</Label>
                      <RadioGroup
                        value={leakageCondition}
                        onValueChange={setLeakageCondition}
                        className="flex gap-4 mt-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="none" id="leakage-none" />
                          <Label htmlFor="leakage-none" className="font-normal">
                            None
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="minor" id="leakage-minor" />
                          <Label htmlFor="leakage-minor" className="font-normal">
                            Minor
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="major" id="leakage-major" />
                          <Label htmlFor="leakage-major" className="font-normal">
                            Major
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div>
                      <Label>Pole Status</Label>
                      <RadioGroup
                        value={poleStatusCondition}
                        onValueChange={setPoleStatusCondition}
                        className="flex gap-4 mt-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="good" id="pole-good" />
                          <Label htmlFor="pole-good" className="font-normal">
                            Good
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="bad" id="pole-bad" />
                          <Label htmlFor="pole-bad" className="font-normal">
                            Bad
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="na" id="pole-na" />
                          <Label htmlFor="pole-na" className="font-normal">
                            N/A
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Transformer Load (A)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor="load-r">R Phase</Label>
                    <Input
                      id="load-r"
                      type="number"
                      value={loadR}
                      onChange={(e) => setLoadR(e.target.value)}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="load-s">S Phase</Label>
                    <Input
                      id="load-s"
                      type="number"
                      value={loadS}
                      onChange={(e) => setLoadS(e.target.value)}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="load-t">T Phase</Label>
                    <Input
                      id="load-t"
                      type="number"
                      value={loadT}
                      onChange={(e) => setLoadT(e.target.value)}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="load-n">N Neutral</Label>
                    <Input
                      id="load-n"
                      type="number"
                      value={loadN}
                      onChange={(e) => setLoadN(e.target.value)}
                      placeholder="0"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="space-y-2">
              <Label htmlFor="findings">Findings & Remarks</Label>
              <Textarea
                id="findings"
                placeholder="Describe your findings and any relevant remarks"
                value={findings}
                onChange={(e) => setFindings(e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Inspection Result</Label>
              <RadioGroup value={result} onValueChange={setResult} className="flex gap-4 mt-2">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="pass" id="result-pass" />
                  <Label htmlFor="result-pass" className="font-normal">
                    Pass
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="pass_with_notes" id="result-pass-notes" />
                  <Label htmlFor="result-pass-notes" className="font-normal">
                    Pass with Notes
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="fail" id="result-fail" />
                  <Label htmlFor="result-fail" className="font-normal">
                    Fail
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : "Add Inspection Record"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
