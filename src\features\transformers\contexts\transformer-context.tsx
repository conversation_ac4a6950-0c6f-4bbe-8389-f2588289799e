"use client"

import { create<PERSON>ontext, useContext, useState, useEffect, ReactNode } from "react"
import { transformerService } from "@/src/services/transformer-service"
import { mapService } from "@/src/services/map-service"
import { maintenanceService } from "@/src/services/maintenance-service"
import type { Transformer } from "@/src/types/transformer"
import type { MapLocation } from "@/src/services/map-service"
import type { ExtendedMaintenanceRecord } from "@/src/services/maintenance-service"
import { useToast } from "@/src/components/ui/use-toast"

interface TransformerContextType {
  // Transformer data
  transformers: Transformer[]
  filteredTransformers: Transformer[]
  selectedTransformer: Transformer | null
  transformerLocations: MapLocation[]
  transformerStatistics: any
  maintenanceRecords: ExtendedMaintenanceRecord[]

  // Loading states
  isLoading: boolean
  isMapLoading: boolean

  // Filters
  filters: {
    status: string[]
    region: string[]
    serviceCenter: string[]
    capacity: [number, number] | null
    search: string
  }

  // Filter options (for dropdowns)
  filterOptions: {
    regions: string[]
    serviceCenters: string[]
    statuses: string[]
  }

  // Actions
  setSelectedTransformer: (transformer: Transformer | null) => void
  updateFilter: (key: string, value: any) => void
  resetFilters: () => void
  refreshData: () => void
  getTransformerById: (id: string) => Promise<Transformer | null>
  getMaintenanceRecordsByTransformerId: (id: string) => ExtendedMaintenanceRecord[]
}

const defaultFilters = {
  status: [],
  region: [],
  serviceCenter: [],
  capacity: null as [number, number] | null,
  search: ""
}

const TransformerContext = createContext<TransformerContextType | undefined>(undefined)

export function TransformerProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast()

  // State
  const [transformers, setTransformers] = useState<Transformer[]>([])
  const [filteredTransformers, setFilteredTransformers] = useState<Transformer[]>([])
  const [selectedTransformer, setSelectedTransformer] = useState<Transformer | null>(null)
  const [transformerLocations, setTransformerLocations] = useState<MapLocation[]>([])
  const [transformerStatistics, setTransformerStatistics] = useState<any>({
    total: 0,
    byStatus: {
      operational: 0,
      warning: 0,
      maintenance: 0,
      critical: 0,
      burnt: 0
    },
    byRegion: {},
    byType: {},
    byManufacturer: {},
    alerts: {
      critical: 0,
      warning: 0,
      info: 0,
      total: 0
    },
    maintenance: {
      scheduled: 0,
      overdue: 0,
      completed: 0,
      total: 0
    }
  })
  const [maintenanceRecords, setMaintenanceRecords] = useState<ExtendedMaintenanceRecord[]>([])

  // Loading states
  const [isLoading, setIsLoading] = useState(true)
  const [isMapLoading, setIsMapLoading] = useState(true)

  // Filters
  const [filters, setFilters] = useState(defaultFilters)

  // Filter options
  const [filterOptions, setFilterOptions] = useState({
    regions: [] as string[],
    serviceCenters: [] as string[],
    statuses: [] as string[]
  })

  // Load initial data
  useEffect(() => {
    loadInitialData()
  }, [])

  // Apply filters when they change
  useEffect(() => {
    applyFilters()
  }, [filters, transformers])

  // Load all initial data
  const loadInitialData = async () => {
    setIsLoading(true)
    setIsMapLoading(true)

    try {
      // Load transformers
      const transformerData = await transformerService.getAllTransformers()
      setTransformers(transformerData)
      setFilteredTransformers(transformerData)

      // Load map locations
      const locations = await mapService.getTransformerLocations()
      setTransformerLocations(locations)

      // Load maintenance records
      const maintenance = maintenanceService.getAllMaintenanceRecords()
      setMaintenanceRecords(maintenance)

      // Load transformer statistics
      const stats = await transformerService.getTransformerStatistics()
      setTransformerStatistics(stats)

      // Extract filter options
      const regions = [...new Set(transformerData.map(t => t.location.region))]
      const serviceCenters = [...new Set(transformerData.map(t => t.location.serviceCenter).filter(Boolean))]
      const statuses = [...new Set(transformerData.map(t => t.status))]

      setFilterOptions({
        regions,
        serviceCenters: serviceCenters as string[],
        statuses
      })
    } catch (error) {
      console.error("Error loading transformer data:", error)
      toast({
        title: "Error",
        description: "Failed to load transformer data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
      setIsMapLoading(false)
    }
  }

  // Apply filters to transformers
  const applyFilters = async () => {
    if (transformers.length === 0) return

    let filtered = [...transformers]

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(t =>
        filters.status.some(s => s.toLowerCase() === t.status.toLowerCase())
      )
    }

    // Apply region filter
    if (filters.region.length > 0) {
      filtered = filtered.filter(t =>
        filters.region.some(r => r.toLowerCase() === t.location.region.toLowerCase())
      )
    }

    // Apply service center filter
    if (filters.serviceCenter.length > 0) {
      filtered = filtered.filter(t =>
        t.location.serviceCenter &&
        filters.serviceCenter.some(sc => sc.toLowerCase() === t.location.serviceCenter!.toLowerCase())
      )
    }

    // Apply capacity filter
    if (filters.capacity) {
      const [min, max] = filters.capacity
      filtered = filtered.filter(t => {
        const capacity = parseFloat(t.capacity)
        return capacity >= min && capacity <= max
      })
    }

    // Apply search filter
    if (filters.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(t =>
        t.serialNumber.toLowerCase().includes(search) ||
        t.manufacturer.toLowerCase().includes(search) ||
        t.model.toLowerCase().includes(search) ||
        t.location.region.toLowerCase().includes(search) ||
        (t.location.serviceCenter && t.location.serviceCenter.toLowerCase().includes(search))
      )
    }

    setFilteredTransformers(filtered)

    // Update map locations based on filtered transformers
    setIsMapLoading(true)
    try {
      const locations = await mapService.getFilteredTransformerLocations({
        status: filters.status,
        region: filters.region,
        serviceCenter: filters.serviceCenter,
        capacity: filters.capacity || undefined,
        search: filters.search
      })
      setTransformerLocations(locations)
    } catch (error) {
      console.error("Error updating map locations:", error)
    } finally {
      setIsMapLoading(false)
    }
  }

  // Update a specific filter
  const updateFilter = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))

    // Log filter update for debugging
    console.log(`Filter updated: ${key}`, value)

    // Force immediate filter application
    setTimeout(() => applyFilters(), 0)
  }

  // Reset all filters
  const resetFilters = () => {
    setFilters(defaultFilters)

    // Force immediate filter application
    setTimeout(() => applyFilters(), 0)

    console.log("All filters reset")
  }

  // Refresh all data
  const refreshData = () => {
    loadInitialData()
  }

  // Get transformer by ID
  const getTransformerById = async (id: string): Promise<Transformer | null> => {
    try {
      return await transformerService.getTransformerById(id)
    } catch (error) {
      console.error(`Error fetching transformer ${id}:`, error)
      return null
    }
  }

  // Get maintenance records by transformer ID
  const getMaintenanceRecordsByTransformerId = (id: string): ExtendedMaintenanceRecord[] => {
    return maintenanceRecords.filter(record => record.transformerId === id)
  }

  const value = {
    transformers,
    filteredTransformers,
    selectedTransformer,
    transformerLocations,
    transformerStatistics,
    maintenanceRecords,
    isLoading,
    isMapLoading,
    filters,
    filterOptions,
    setSelectedTransformer,
    updateFilter,
    resetFilters,
    refreshData,
    getTransformerById,
    getMaintenanceRecordsByTransformerId
  }

  return (
    <TransformerContext.Provider value={value}>
      {children}
    </TransformerContext.Provider>
  )
}

export function useTransformers() {
  const context = useContext(TransformerContext)
  if (context === undefined) {
    throw new Error("useTransformers must be used within a TransformerProvider")
  }
  return context
}
