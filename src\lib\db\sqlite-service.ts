/**
 * SQLite Database Service
 * 
 * This service provides a high-level API for interacting with the SQLite database.
 * It uses the better-sqlite3 package to perform operations on the database.
 */

import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Database file path
const DB_PATH = path.join(process.cwd(), 'data', 'eeu-transformer.db');

// Ensure the data directory exists
const dataDir = path.join(process.cwd(), 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Create a database instance
let db: Database.Database;

try {
  db = new Database(DB_PATH);
  db.pragma('journal_mode = WAL');
  db.pragma('foreign_keys = ON');
  console.log('SQLite database connected successfully');
} catch (error) {
  console.error('Error connecting to SQLite database:', error);
  throw error;
}

/**
 * Initialize the database schema
 */
export function initializeSchema(): void {
  try {
    const schemaPath = path.join(process.cwd(), 'scripts', 'sqlite-schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Split the schema into individual statements
    const statements = schema
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);
    
    // Execute each statement
    for (const statement of statements) {
      db.exec(statement + ';');
    }
    
    console.log('SQLite schema initialized successfully');
  } catch (error) {
    console.error('Error initializing SQLite schema:', error);
    throw error;
  }
}

/**
 * Transformer Service
 */
export const transformerService = {
  /**
   * Get all transformers
   */
  getAllTransformers() {
    try {
      const stmt = db.prepare(`
        SELECT * FROM transformers
      `);
      
      const transformers = stmt.all();
      
      // Convert the flat structure to the nested structure expected by the frontend
      return transformers.map((transformer: any) => ({
        id: transformer.id,
        serialNumber: transformer.serial_number,
        manufacturer: transformer.manufacturer,
        model: transformer.model,
        type: transformer.type,
        capacity: transformer.capacity,
        voltageRating: transformer.voltage_rating,
        installationDate: transformer.installation_date,
        lastMaintenanceDate: transformer.last_maintenance_date,
        lastInspectionDate: transformer.last_inspection_date,
        status: transformer.status,
        location: {
          latitude: transformer.location_latitude,
          longitude: transformer.location_longitude,
          region: transformer.location_region,
          serviceCenter: transformer.location_service_center,
          address: transformer.location_address,
          installationSite: transformer.location_installation_site
        },
        manufacturingYear: transformer.manufacturing_year,
        primaryVoltage: transformer.primary_voltage,
        secondaryVoltage: transformer.secondary_voltage,
        connectionType: transformer.connection_type,
        phaseCount: transformer.phase_count,
        impedance: transformer.impedance,
        oilType: transformer.oil_type,
        coolingType: transformer.cooling_type,
        weight: transformer.weight,
        dimensions: transformer.dimensions,
        temperatureRise: transformer.temperature_rise,
        noiseLevel: transformer.noise_level,
        feederName: transformer.feeder_name,
        substationName: transformer.substation_name
      }));
    } catch (error) {
      console.error('Error getting all transformers:', error);
      return [];
    }
  },
  
  /**
   * Get a transformer by ID
   */
  getTransformerById(id: string) {
    try {
      const stmt = db.prepare(`
        SELECT * FROM transformers WHERE id = ?
      `);
      
      const transformer = stmt.get(id);
      
      if (!transformer) {
        return null;
      }
      
      // Convert the flat structure to the nested structure expected by the frontend
      return {
        id: transformer.id,
        serialNumber: transformer.serial_number,
        manufacturer: transformer.manufacturer,
        model: transformer.model,
        type: transformer.type,
        capacity: transformer.capacity,
        voltageRating: transformer.voltage_rating,
        installationDate: transformer.installation_date,
        lastMaintenanceDate: transformer.last_maintenance_date,
        lastInspectionDate: transformer.last_inspection_date,
        status: transformer.status,
        location: {
          latitude: transformer.location_latitude,
          longitude: transformer.location_longitude,
          region: transformer.location_region,
          serviceCenter: transformer.location_service_center,
          address: transformer.location_address,
          installationSite: transformer.location_installation_site
        },
        manufacturingYear: transformer.manufacturing_year,
        primaryVoltage: transformer.primary_voltage,
        secondaryVoltage: transformer.secondary_voltage,
        connectionType: transformer.connection_type,
        phaseCount: transformer.phase_count,
        impedance: transformer.impedance,
        oilType: transformer.oil_type,
        coolingType: transformer.cooling_type,
        weight: transformer.weight,
        dimensions: transformer.dimensions,
        temperatureRise: transformer.temperature_rise,
        noiseLevel: transformer.noise_level,
        feederName: transformer.feeder_name,
        substationName: transformer.substation_name
      };
    } catch (error) {
      console.error(`Error getting transformer with ID ${id}:`, error);
      return null;
    }
  },
  
  /**
   * Add a new transformer
   */
  addTransformer(transformer: any) {
    try {
      const id = transformer.id || uuidv4();
      
      const stmt = db.prepare(`
        INSERT INTO transformers (
          id, serial_number, manufacturer, model, type, capacity, voltage_rating,
          installation_date, last_maintenance_date, last_inspection_date, status,
          location_latitude, location_longitude, location_region, location_service_center,
          location_address, location_installation_site, manufacturing_year, primary_voltage,
          secondary_voltage, connection_type, phase_count, impedance, oil_type, cooling_type,
          weight, dimensions, temperature_rise, noise_level, feeder_name, substation_name
        ) VALUES (
          ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )
      `);
      
      stmt.run(
        id,
        transformer.serialNumber,
        transformer.manufacturer,
        transformer.model,
        transformer.type,
        transformer.capacity,
        transformer.voltageRating,
        transformer.installationDate,
        transformer.lastMaintenanceDate,
        transformer.lastInspectionDate,
        transformer.status,
        transformer.location?.latitude,
        transformer.location?.longitude,
        transformer.location?.region,
        transformer.location?.serviceCenter,
        transformer.location?.address,
        transformer.location?.installationSite,
        transformer.manufacturingYear,
        transformer.primaryVoltage,
        transformer.secondaryVoltage,
        transformer.connectionType,
        transformer.phaseCount,
        transformer.impedance,
        transformer.oilType,
        transformer.coolingType,
        transformer.weight,
        transformer.dimensions,
        transformer.temperatureRise,
        transformer.noiseLevel,
        transformer.feederName,
        transformer.substationName
      );
      
      return { ...transformer, id };
    } catch (error) {
      console.error('Error adding transformer:', error);
      return null;
    }
  },
  
  /**
   * Update a transformer
   */
  updateTransformer(id: string, transformer: any) {
    try {
      const stmt = db.prepare(`
        UPDATE transformers SET
          serial_number = ?,
          manufacturer = ?,
          model = ?,
          type = ?,
          capacity = ?,
          voltage_rating = ?,
          installation_date = ?,
          last_maintenance_date = ?,
          last_inspection_date = ?,
          status = ?,
          location_latitude = ?,
          location_longitude = ?,
          location_region = ?,
          location_service_center = ?,
          location_address = ?,
          location_installation_site = ?,
          manufacturing_year = ?,
          primary_voltage = ?,
          secondary_voltage = ?,
          connection_type = ?,
          phase_count = ?,
          impedance = ?,
          oil_type = ?,
          cooling_type = ?,
          weight = ?,
          dimensions = ?,
          temperature_rise = ?,
          noise_level = ?,
          feeder_name = ?,
          substation_name = ?,
          updated_at = datetime('now')
        WHERE id = ?
      `);
      
      stmt.run(
        transformer.serialNumber,
        transformer.manufacturer,
        transformer.model,
        transformer.type,
        transformer.capacity,
        transformer.voltageRating,
        transformer.installationDate,
        transformer.lastMaintenanceDate,
        transformer.lastInspectionDate,
        transformer.status,
        transformer.location?.latitude,
        transformer.location?.longitude,
        transformer.location?.region,
        transformer.location?.serviceCenter,
        transformer.location?.address,
        transformer.location?.installationSite,
        transformer.manufacturingYear,
        transformer.primaryVoltage,
        transformer.secondaryVoltage,
        transformer.connectionType,
        transformer.phaseCount,
        transformer.impedance,
        transformer.oilType,
        transformer.coolingType,
        transformer.weight,
        transformer.dimensions,
        transformer.temperatureRise,
        transformer.noiseLevel,
        transformer.feederName,
        transformer.substationName,
        id
      );
      
      return { ...transformer, id };
    } catch (error) {
      console.error(`Error updating transformer with ID ${id}:`, error);
      return null;
    }
  },
  
  /**
   * Delete a transformer
   */
  deleteTransformer(id: string) {
    try {
      const stmt = db.prepare(`
        DELETE FROM transformers WHERE id = ?
      `);
      
      stmt.run(id);
      
      return true;
    } catch (error) {
      console.error(`Error deleting transformer with ID ${id}:`, error);
      return false;
    }
  }
};

/**
 * Region Service
 */
export const regionService = {
  /**
   * Get all regions
   */
  getAllRegions() {
    try {
      const stmt = db.prepare(`
        SELECT * FROM regions
      `);
      
      return stmt.all();
    } catch (error) {
      console.error('Error getting all regions:', error);
      return [];
    }
  },
  
  /**
   * Get a region by ID
   */
  getRegionById(id: string) {
    try {
      const stmt = db.prepare(`
        SELECT * FROM regions WHERE id = ?
      `);
      
      return stmt.get(id);
    } catch (error) {
      console.error(`Error getting region with ID ${id}:`, error);
      return null;
    }
  },
  
  /**
   * Add a new region
   */
  addRegion(region: any) {
    try {
      const id = region.id || uuidv4();
      
      const stmt = db.prepare(`
        INSERT INTO regions (
          id, name, code, latitude, longitude
        ) VALUES (?, ?, ?, ?, ?)
      `);
      
      stmt.run(
        id,
        region.name,
        region.code,
        region.latitude,
        region.longitude
      );
      
      return { ...region, id };
    } catch (error) {
      console.error('Error adding region:', error);
      return null;
    }
  }
};

/**
 * Service Center Service
 */
export const serviceCenterService = {
  /**
   * Get all service centers
   */
  getAllServiceCenters() {
    try {
      const stmt = db.prepare(`
        SELECT * FROM service_centers
      `);
      
      const serviceCenters = stmt.all();
      
      // Convert the flat structure to the nested structure expected by the frontend
      return serviceCenters.map((serviceCenter: any) => ({
        id: serviceCenter.id,
        name: serviceCenter.name,
        region: serviceCenter.region,
        address: serviceCenter.address,
        contactPerson: serviceCenter.contact_person,
        phone: serviceCenter.phone,
        email: serviceCenter.email,
        location: {
          latitude: serviceCenter.latitude,
          longitude: serviceCenter.longitude
        }
      }));
    } catch (error) {
      console.error('Error getting all service centers:', error);
      return [];
    }
  },
  
  /**
   * Get a service center by ID
   */
  getServiceCenterById(id: string) {
    try {
      const stmt = db.prepare(`
        SELECT * FROM service_centers WHERE id = ?
      `);
      
      const serviceCenter = stmt.get(id);
      
      if (!serviceCenter) {
        return null;
      }
      
      // Convert the flat structure to the nested structure expected by the frontend
      return {
        id: serviceCenter.id,
        name: serviceCenter.name,
        region: serviceCenter.region,
        address: serviceCenter.address,
        contactPerson: serviceCenter.contact_person,
        phone: serviceCenter.phone,
        email: serviceCenter.email,
        location: {
          latitude: serviceCenter.latitude,
          longitude: serviceCenter.longitude
        }
      };
    } catch (error) {
      console.error(`Error getting service center with ID ${id}:`, error);
      return null;
    }
  },
  
  /**
   * Get service centers by region
   */
  getServiceCentersByRegion(region: string) {
    try {
      const stmt = db.prepare(`
        SELECT * FROM service_centers WHERE region = ?
      `);
      
      const serviceCenters = stmt.all(region);
      
      // Convert the flat structure to the nested structure expected by the frontend
      return serviceCenters.map((serviceCenter: any) => ({
        id: serviceCenter.id,
        name: serviceCenter.name,
        region: serviceCenter.region,
        address: serviceCenter.address,
        contactPerson: serviceCenter.contact_person,
        phone: serviceCenter.phone,
        email: serviceCenter.email,
        location: {
          latitude: serviceCenter.latitude,
          longitude: serviceCenter.longitude
        }
      }));
    } catch (error) {
      console.error(`Error getting service centers for region ${region}:`, error);
      return [];
    }
  },
  
  /**
   * Add a new service center
   */
  addServiceCenter(serviceCenter: any) {
    try {
      const id = serviceCenter.id || uuidv4();
      
      const stmt = db.prepare(`
        INSERT INTO service_centers (
          id, name, region, address, contact_person, phone, email, latitude, longitude
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      stmt.run(
        id,
        serviceCenter.name,
        serviceCenter.region,
        serviceCenter.address,
        serviceCenter.contactPerson,
        serviceCenter.phone,
        serviceCenter.email,
        serviceCenter.location?.latitude,
        serviceCenter.location?.longitude
      );
      
      return { ...serviceCenter, id };
    } catch (error) {
      console.error('Error adding service center:', error);
      return null;
    }
  }
};

/**
 * SQLite Database Service
 */
export const sqliteService = {
  transformers: transformerService,
  regions: regionService,
  serviceCenters: serviceCenterService,
  
  /**
   * Initialize the database
   */
  initialize() {
    initializeSchema();
  }
};

export default sqliteService;
