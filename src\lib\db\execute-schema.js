const mysql = require('mysql2');
const fs = require('fs');
const path = require('path');

// Database connection configuration
const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '', // Replace with your MySQL root password
  multipleStatements: true,
});

// Path to the SQL script
const sqlFilePath = path.join(__dirname, 'create-tables.sql');

// Read the SQL script
fs.readFile(sqlFilePath, 'utf8', (err, sql) => {
  if (err) {
    console.error('Error reading the SQL file:', err);
    return;
  }

  // Execute the SQL script
  connection.query(sql, (error, results) => {
    if (error) {
      console.error('Error executing the SQL script:', error);
    } else {
      console.log('Database schema executed successfully.');
    }

    // Close the connection
    connection.end();
  });
});
