// Verify that data in app_*2 tables matches the original app_* tables (row counts and sample data)
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    // Get all app_* and app_*2 tables
    const [tables] = await connection.execute("SHOW TABLES");
    const tableNames = tables.map(row => Object.values(row)[0]);
    const appTables = tableNames.filter(name => name.startsWith('app_') && !name.endsWith('2'));
    const app2Tables = tableNames.filter(name => name.startsWith('app_') && name.endsWith('2'));
    for (const from of appTables) {
      const to = from + '2';
      if (app2Tables.includes(to)) {
        // Count rows in both tables
        const [[{count: fromCount}]] = await connection.execute(`SELECT COUNT(*) as count FROM \
          \
          \`${from}\`
        `);
        const [[{count: toCount}]] = await connection.execute(`SELECT COUNT(*) as count FROM \
          \`${to}\`
        `);
        console.log(`Table: ${from} -> ${to}`);
        console.log(`  Original: ${fromCount} rows, Migrated: ${toCount} rows`);
        // Show up to 2 sample rows from each
        const [fromRows] = await connection.execute(`SELECT * FROM ${from} LIMIT 2`);
        const [toRows] = await connection.execute(`SELECT * FROM ${to} LIMIT 2`);
        console.log(`  Sample from ${from}:`, fromRows);
        console.log(`  Sample from ${to}:`, toRows);
      }
    }
  } catch (err) {
    console.error('❌ Error verifying migration:', err);
  } finally {
    await connection.end();
  }
})();
