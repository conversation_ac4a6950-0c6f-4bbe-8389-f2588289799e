/**
 * User repository
 * 
 * This class provides specialized methods for working with user entities.
 */

import { BaseRepository } from './base-repository';
import { User, UserRole } from '../schema';

export class UserRepository extends BaseRepository<User> {
  constructor() {
    super('users');
  }
  
  /**
   * Find a user by email
   */
  findByEmail(email: string): User | null {
    return this.findOne({ email });
  }
  
  /**
   * Find users by role
   */
  findByRole(role: UserRole | UserRole[]): User[] {
    if (Array.isArray(role)) {
      return this.find({}).filter(user => role.includes(user.role));
    }
    return this.find({ role });
  }
  
  /**
   * Find users by region
   */
  findByRegion(region: string): User[] {
    return this.find({ region });
  }
  
  /**
   * Find users by service center
   */
  findByServiceCenter(serviceCenter: string): User[] {
    return this.find({ serviceCenter });
  }
  
  /**
   * Find active users
   */
  findActive(): User[] {
    return this.find({ isActive: true });
  }
  
  /**
   * Find inactive users
   */
  findInactive(): User[] {
    return this.find({ isActive: false });
  }
  
  /**
   * Search users by name or email
   */
  search(term: string): User[] {
    const searchTerm = term.toLowerCase();
    return this.find({}).filter(user => {
      return (
        user.name.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm)
      );
    });
  }
  
  /**
   * Get user statistics
   */
  getStatistics() {
    const users = this.getAll();
    
    // Count by role
    const roleCounts = users.reduce((counts, user) => {
      counts[user.role] = (counts[user.role] || 0) + 1;
      return counts;
    }, {} as Record<UserRole, number>);
    
    // Count by region
    const regionCounts = users.reduce((counts, user) => {
      if (user.region) {
        counts[user.region] = (counts[user.region] || 0) + 1;
      }
      return counts;
    }, {} as Record<string, number>);
    
    // Count active vs inactive
    const activeCount = users.filter(user => user.isActive).length;
    const inactiveCount = users.length - activeCount;
    
    return {
      total: users.length,
      active: activeCount,
      inactive: inactiveCount,
      byRole: roleCounts,
      byRegion: regionCounts
    };
  }
}

// Export a singleton instance
export const userRepository = new UserRepository();
