"use client"

import { useState, useEffect, useCallback } from 'react'

export interface FilterOptions {
  // Geographic Filters
  regions: string[]
  serviceCenters: string[]
  
  // Transformer Filters
  transformerTypes: string[]
  transformerStatuses: string[]
  manufacturers: string[]
  capacityRange: [number, number]
  efficiencyRange: [number, number]
  temperatureRange: [number, number]
  loadFactorRange: [number, number]
  
  // Date Filters
  dateRange: {
    from: Date | null
    to: Date | null
  }
  
  // Maintenance Filters
  maintenanceTypes: string[]
  maintenanceStatuses: string[]
  maintenancePriorities: string[]
  
  // Alert Filters
  alertSeverities: string[]
  alertTypes: string[]
  alertStatuses: string[]
  
  // Asset Filters
  criticalities: string[]
  customerTypes: string[]
  assetValueRange: [number, number]
  
  // Search
  searchQuery: string
}

export interface FilteredData {
  transformers: any[]
  alerts: any[]
  maintenance: any[]
  performance: any[]
  regions: any[]
  serviceCenters: any[]
  summary: {
    totalTransformers: number
    operationalCount: number
    warningCount: number
    criticalCount: number
    maintenanceCount: number
    avgEfficiency: number
    avgLoadFactor: number
    avgTemperature: number
    totalAssetValue: number
    activeAlerts: number
    pendingMaintenance: number
  }
}

const defaultFilters: FilterOptions = {
  regions: [],
  serviceCenters: [],
  transformerTypes: [],
  transformerStatuses: [],
  manufacturers: [],
  capacityRange: [0, 2000],
  efficiencyRange: [90, 100],
  temperatureRange: [0, 100],
  loadFactorRange: [0, 100],
  dateRange: { from: null, to: null },
  maintenanceTypes: [],
  maintenanceStatuses: [],
  maintenancePriorities: [],
  alertSeverities: [],
  alertTypes: [],
  alertStatuses: [],
  criticalities: [],
  customerTypes: [],
  assetValueRange: [0, 500000],
  searchQuery: ''
}

export function useDashboardFilters() {
  const [filters, setFilters] = useState<FilterOptions>(defaultFilters)
  const [filteredData, setFilteredData] = useState<FilteredData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Apply filters to data
  const applyFilters = useCallback(async (filterOptions: FilterOptions) => {
    setIsLoading(true)
    setError(null)

    try {
      // Build query parameters
      const queryParams = new URLSearchParams()

      // Geographic filters
      if (filterOptions.regions.length > 0) {
        queryParams.append('regions', filterOptions.regions.join(','))
      }
      if (filterOptions.serviceCenters.length > 0) {
        queryParams.append('serviceCenters', filterOptions.serviceCenters.join(','))
      }

      // Transformer filters
      if (filterOptions.transformerTypes.length > 0) {
        queryParams.append('types', filterOptions.transformerTypes.join(','))
      }
      if (filterOptions.transformerStatuses.length > 0) {
        queryParams.append('statuses', filterOptions.transformerStatuses.join(','))
      }
      if (filterOptions.manufacturers.length > 0) {
        queryParams.append('manufacturers', filterOptions.manufacturers.join(','))
      }

      // Range filters
      queryParams.append('capacityMin', filterOptions.capacityRange[0].toString())
      queryParams.append('capacityMax', filterOptions.capacityRange[1].toString())
      queryParams.append('efficiencyMin', filterOptions.efficiencyRange[0].toString())
      queryParams.append('efficiencyMax', filterOptions.efficiencyRange[1].toString())
      queryParams.append('temperatureMin', filterOptions.temperatureRange[0].toString())
      queryParams.append('temperatureMax', filterOptions.temperatureRange[1].toString())
      queryParams.append('loadFactorMin', filterOptions.loadFactorRange[0].toString())
      queryParams.append('loadFactorMax', filterOptions.loadFactorRange[1].toString())
      queryParams.append('assetValueMin', filterOptions.assetValueRange[0].toString())
      queryParams.append('assetValueMax', filterOptions.assetValueRange[1].toString())

      // Date filters
      if (filterOptions.dateRange.from) {
        queryParams.append('dateFrom', filterOptions.dateRange.from.toISOString())
      }
      if (filterOptions.dateRange.to) {
        queryParams.append('dateTo', filterOptions.dateRange.to.toISOString())
      }

      // Maintenance filters
      if (filterOptions.maintenanceTypes.length > 0) {
        queryParams.append('maintenanceTypes', filterOptions.maintenanceTypes.join(','))
      }
      if (filterOptions.maintenanceStatuses.length > 0) {
        queryParams.append('maintenanceStatuses', filterOptions.maintenanceStatuses.join(','))
      }
      if (filterOptions.maintenancePriorities.length > 0) {
        queryParams.append('maintenancePriorities', filterOptions.maintenancePriorities.join(','))
      }

      // Alert filters
      if (filterOptions.alertSeverities.length > 0) {
        queryParams.append('alertSeverities', filterOptions.alertSeverities.join(','))
      }
      if (filterOptions.alertTypes.length > 0) {
        queryParams.append('alertTypes', filterOptions.alertTypes.join(','))
      }
      if (filterOptions.alertStatuses.length > 0) {
        queryParams.append('alertStatuses', filterOptions.alertStatuses.join(','))
      }

      // Asset filters
      if (filterOptions.criticalities.length > 0) {
        queryParams.append('criticalities', filterOptions.criticalities.join(','))
      }
      if (filterOptions.customerTypes.length > 0) {
        queryParams.append('customerTypes', filterOptions.customerTypes.join(','))
      }

      // Search
      if (filterOptions.searchQuery.trim()) {
        queryParams.append('search', filterOptions.searchQuery.trim())
      }

      // Make API call to filtered data endpoint
      const response = await fetch(`/api/dashboard/filtered-data?${queryParams.toString()}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch filtered data: ${response.statusText}`)
      }

      const data = await response.json()
      setFilteredData(data)

    } catch (err) {
      console.error('Error applying filters:', err)
      setError(err instanceof Error ? err.message : 'Failed to apply filters')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Update filters
  const updateFilters = useCallback((newFilters: FilterOptions) => {
    setFilters(newFilters)
    applyFilters(newFilters)
  }, [applyFilters])

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters(defaultFilters)
    applyFilters(defaultFilters)
  }, [applyFilters])

  // Get active filter count
  const getActiveFilterCount = useCallback((filterOptions: FilterOptions) => {
    let count = 0
    if (filterOptions.regions.length > 0) count++
    if (filterOptions.serviceCenters.length > 0) count++
    if (filterOptions.transformerTypes.length > 0) count++
    if (filterOptions.transformerStatuses.length > 0) count++
    if (filterOptions.manufacturers.length > 0) count++
    if (filterOptions.maintenanceTypes.length > 0) count++
    if (filterOptions.maintenanceStatuses.length > 0) count++
    if (filterOptions.maintenancePriorities.length > 0) count++
    if (filterOptions.alertSeverities.length > 0) count++
    if (filterOptions.alertTypes.length > 0) count++
    if (filterOptions.alertStatuses.length > 0) count++
    if (filterOptions.criticalities.length > 0) count++
    if (filterOptions.customerTypes.length > 0) count++
    if (filterOptions.searchQuery.trim() !== '') count++
    if (filterOptions.dateRange.from || filterOptions.dateRange.to) count++
    
    // Check if ranges are different from defaults
    if (filterOptions.capacityRange[0] !== 0 || filterOptions.capacityRange[1] !== 2000) count++
    if (filterOptions.efficiencyRange[0] !== 90 || filterOptions.efficiencyRange[1] !== 100) count++
    if (filterOptions.temperatureRange[0] !== 0 || filterOptions.temperatureRange[1] !== 100) count++
    if (filterOptions.loadFactorRange[0] !== 0 || filterOptions.loadFactorRange[1] !== 100) count++
    if (filterOptions.assetValueRange[0] !== 0 || filterOptions.assetValueRange[1] !== 500000) count++
    
    return count
  }, [])

  // Export filters to URL or local storage
  const exportFilters = useCallback(() => {
    return JSON.stringify(filters)
  }, [filters])

  // Import filters from URL or local storage
  const importFilters = useCallback((filtersJson: string) => {
    try {
      const importedFilters = JSON.parse(filtersJson)
      setFilters({ ...defaultFilters, ...importedFilters })
      applyFilters({ ...defaultFilters, ...importedFilters })
    } catch (err) {
      console.error('Error importing filters:', err)
      setError('Failed to import filters')
    }
  }, [applyFilters])

  // Save filters to localStorage
  const saveFiltersToStorage = useCallback(() => {
    try {
      localStorage.setItem('eeu-dtms-filters', JSON.stringify(filters))
    } catch (err) {
      console.error('Error saving filters to storage:', err)
    }
  }, [filters])

  // Load filters from localStorage
  const loadFiltersFromStorage = useCallback(() => {
    try {
      const savedFilters = localStorage.getItem('eeu-dtms-filters')
      if (savedFilters) {
        const parsedFilters = JSON.parse(savedFilters)
        setFilters({ ...defaultFilters, ...parsedFilters })
        applyFilters({ ...defaultFilters, ...parsedFilters })
      }
    } catch (err) {
      console.error('Error loading filters from storage:', err)
    }
  }, [applyFilters])

  // Initialize with default data on mount
  useEffect(() => {
    applyFilters(defaultFilters)
  }, [applyFilters])

  return {
    filters,
    filteredData,
    isLoading,
    error,
    updateFilters,
    resetFilters,
    applyFilters,
    getActiveFilterCount: () => getActiveFilterCount(filters),
    exportFilters,
    importFilters,
    saveFiltersToStorage,
    loadFiltersFromStorage
  }
}
