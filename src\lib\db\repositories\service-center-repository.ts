/**
 * Service Center repository
 * 
 * This class provides specialized methods for working with service center entities.
 */

import { BaseRepository } from './base-repository';
import { ServiceCenter } from '../schema';

export class ServiceCenterRepository extends BaseRepository<ServiceCenter> {
  constructor() {
    super('serviceCenters');
  }
  
  /**
   * Find service centers by region
   */
  findByRegion(regionId: string): ServiceCenter[] {
    return this.find({ regionId });
  }
  
  /**
   * Find a service center by code
   */
  findByCode(code: string): ServiceCenter | null {
    return this.findOne({ code });
  }
  
  /**
   * Get service centers with transformer counts
   */
  getServiceCentersWithTransformerCounts(): ServiceCenter[] {
    return this.getAll();
  }
  
  /**
   * Find service centers by manager name
   */
  findByManager(managerName: string): ServiceCenter[] {
    const serviceCenters = this.find({});
    return serviceCenters.filter(serviceCenter => 
      serviceCenter.manager && 
      serviceCenter.manager.toLowerCase().includes(managerName.toLowerCase())
    );
  }
  
  /**
   * Search service centers by name or address
   */
  search(term: string): ServiceCenter[] {
    const searchTerm = term.toLowerCase();
    return this.find({}).filter(serviceCenter => {
      return (
        serviceCenter.name.toLowerCase().includes(searchTerm) ||
        serviceCenter.address.toLowerCase().includes(searchTerm) ||
        (serviceCenter.email && serviceCenter.email.toLowerCase().includes(searchTerm)) ||
        (serviceCenter.manager && serviceCenter.manager.toLowerCase().includes(searchTerm))
      );
    });
  }
}

// Export a singleton instance
export const serviceCenterRepository = new ServiceCenterRepository();
