"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { 
  Search, 
  Filter, 
  X, 
  RefreshCw, 
  UserPlus, 
  Shield, 
  Users, 
  CheckCircle2,
  <PERSON>ert<PERSON>riangle
} from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useToast } from "@/src/components/ui/use-toast"
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/src/components/ui/tooltip"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/src/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import type { RoleDefinition } from "@/src/types/auth"

// Mock user data for role assignment
interface User {
  id: string
  name: string
  email: string
  role: string
  organizationalLevel: string
  department?: string
}

interface RoleAssignmentProps {
  roles: RoleDefinition[]
}

export function RoleAssignment({ roles }: RoleAssignmentProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState<string>("all")
  const [levelFilter, setLevelFilter] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(false)
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [selectedRole, setSelectedRole] = useState<string>("")
  const { toast } = useToast()

  // Mock users data
  const users: User[] = [
    {
      id: "user-001",
      name: "Abebe Kebede",
      email: "<EMAIL>",
      role: "Super Admin",
      organizationalLevel: "head_office",
      department: "IT"
    },
    {
      id: "user-002",
      name: "Tigist Haile",
      email: "<EMAIL>",
      role: "National Asset Manager",
      organizationalLevel: "head_office",
      department: "Asset Management"
    },
    {
      id: "user-003",
      name: "Dawit Tadesse",
      email: "<EMAIL>",
      role: "Regional Admin",
      organizationalLevel: "regional_office",
      department: "Administration"
    },
    {
      id: "user-004",
      name: "Hiwot Gebre",
      email: "<EMAIL>",
      role: "Field Technician",
      organizationalLevel: "service_center",
      department: "Maintenance"
    },
    {
      id: "user-005",
      name: "Solomon Bekele",
      email: "<EMAIL>",
      role: "Customer Service Agent",
      organizationalLevel: "service_center",
      department: "Customer Service"
    },
    {
      id: "user-006",
      name: "Meron Alemu",
      email: "<EMAIL>",
      role: "Regional Maintenance Engineer",
      organizationalLevel: "regional_office",
      department: "Maintenance"
    },
    {
      id: "user-007",
      name: "Yonas Tesfaye",
      email: "<EMAIL>",
      role: "Service Center Manager",
      organizationalLevel: "service_center",
      department: "Management"
    },
  ]

  // Get unique departments for filtering
  const departments = Array.from(new Set(users.map(user => user.department))).filter(Boolean) as string[]

  // Filter users based on search query and filters
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.role.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesLevel = levelFilter === "all" || user.organizationalLevel === levelFilter
    
    const matchesDepartment = departmentFilter === "all" || user.department === departmentFilter

    return matchesSearch && matchesLevel && matchesDepartment
  })

  const handleClearFilters = () => {
    setSearchQuery("")
    setLevelFilter("all")
    setDepartmentFilter("all")
  }

  const handleRefresh = () => {
    setIsLoading(true)
    
    // In a real app, this would fetch fresh data from the API
    setTimeout(() => {
      toast({
        title: "User data refreshed",
        description: "User data has been refreshed successfully.",
      })
      setIsLoading(false)
    }, 500)
  }

  const handleAssignClick = (user: User) => {
    setSelectedUser(user)
    setSelectedRole("")
    setIsAssignDialogOpen(true)
  }

  const handleAssignRole = () => {
    if (!selectedUser || !selectedRole) {
      toast({
        title: "Error",
        description: "Please select a role to assign.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    
    // In a real app, this would call an API to assign the role
    setTimeout(() => {
      const role = roles.find(r => r.id === selectedRole)
      
      toast({
        title: "Role assigned",
        description: `${role?.name} role has been assigned to ${selectedUser.name}.`,
      })
      
      setIsLoading(false)
      setIsAssignDialogOpen(false)
    }, 500)
  }

  return (
    <TooltipProvider>
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <UserPlus className="mr-2 h-5 w-5 text-muted-foreground" />
                Role Assignment
              </CardTitle>
              <CardDescription>Assign roles to users in the system</CardDescription>
            </div>
            <Badge variant="outline" className="ml-2">
              {filteredUsers.length} of {users.length} users
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search users..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full rounded-l-none"
                  onClick={() => setSearchQuery("")}
                  aria-label="Clear search"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              <Select value={levelFilter} onValueChange={setLevelFilter}>
                <SelectTrigger className="w-[180px]">
                  <Users className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Organizational Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="head_office">Head Office</SelectItem>
                  <SelectItem value="regional_office">Regional Office</SelectItem>
                  <SelectItem value="service_center">Service Center</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {departments.map(dept => (
                    <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {(searchQuery || levelFilter !== "all" || departmentFilter !== "all") && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={handleClearFilters}>
                      <X className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Clear all filters</p>
                  </TooltipContent>
                </Tooltip>
              )}
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleRefresh} disabled={isLoading}>
                    <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Refresh user data</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Current Role</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Level</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No users found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
                          {user.role}
                        </Badge>
                      </TableCell>
                      <TableCell>{user.department}</TableCell>
                      <TableCell>
                        {user.organizationalLevel === "head_office"
                          ? "Head Office"
                          : user.organizationalLevel === "regional_office"
                            ? "Regional Office"
                            : "Service Center"}
                      </TableCell>
                      <TableCell className="text-right">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 gap-1"
                              onClick={() => handleAssignClick(user)}
                              disabled={isLoading}
                            >
                              <Shield className="h-4 w-4" />
                              Assign Role
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Assign a new role to this user</p>
                          </TooltipContent>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Role Assignment Dialog */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              Assign Role
            </DialogTitle>
            <DialogDescription>
              {selectedUser && `Assign a new role to ${selectedUser.name}`}
            </DialogDescription>
          </DialogHeader>
          
          {selectedUser && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Current Role:</span>
                  <Badge variant="outline" className="bg-blue-50 text-blue-700">
                    {selectedUser.role}
                  </Badge>
                </div>
                
                <div className="space-y-1">
                  <label className="text-sm font-medium">Select New Role:</label>
                  <Select value={selectedRole} onValueChange={setSelectedRole}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.map(role => (
                        <SelectItem key={role.id} value={role.id}>
                          {role.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {selectedRole && (
                  <div className="mt-4 rounded-md border p-3">
                    <div className="text-sm font-medium">Role Details:</div>
                    <div className="mt-2 space-y-2 text-sm">
                      {roles.find(r => r.id === selectedRole)?.description}
                      <div className="flex items-center gap-2">
                        <span>Permissions:</span>
                        <Badge variant="outline">
                          {roles.find(r => r.id === selectedRole)?.permissions.length} permissions
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleAssignRole} 
              disabled={!selectedRole || isLoading}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Assigning...
                </>
              ) : (
                <>
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  Assign Role
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  )
}
