import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'

export interface User {
  id: string
  email: string
  name: string
  role: string
  region?: string
  permissions?: string[]
  avatar?: string
  lastLogin?: Date
  isActive?: boolean
}

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  name: string
  email: string
  password: string
  confirmPassword: string
  role?: string
  region?: string
}

export function useAuth() {
  const router = useRouter()
  
  const [state, setState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    error: null
  })

  // Mock user for development
  const mockUser: User = {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'System Administrator',
    role: 'super_admin',
    region: 'addis-ababa',
    permissions: ['read', 'write', 'delete', 'admin'],
    avatar: '/avatars/admin.jpg',
    lastLogin: new Date(),
    isActive: true
  }

  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      try {
        // Check for stored auth token
        const token = localStorage.getItem('auth_token')
        const storedUser = localStorage.getItem('auth_user')
        
        if (token && storedUser) {
          // Validate token with server
          const response = await fetch('/api/auth/validate', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
          
          if (response.ok) {
            const userData = JSON.parse(storedUser)
            setState({
              user: userData,
              isLoading: false,
              isAuthenticated: true,
              error: null
            })
          } else {
            // Token invalid, clear storage
            localStorage.removeItem('auth_token')
            localStorage.removeItem('auth_user')
            setState({
              user: null,
              isLoading: false,
              isAuthenticated: false,
              error: null
            })
          }
        } else {
          // No stored auth, use mock user for development
          setState({
            user: mockUser,
            isLoading: false,
            isAuthenticated: true,
            error: null
          })
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
        // Fallback to mock user for development
        setState({
          user: mockUser,
          isLoading: false,
          isAuthenticated: true,
          error: null
        })
      }
    }

    initAuth()
  }, [])

  const login = useCallback(async (credentials: LoginCredentials) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
      })
      
      const result = await response.json()
      
      if (result.success) {
        const { user, token } = result.data
        
        // Store auth data
        localStorage.setItem('auth_token', token)
        localStorage.setItem('auth_user', JSON.stringify(user))
        
        setState({
          user,
          isLoading: false,
          isAuthenticated: true,
          error: null
        })
        
        return { success: true }
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.message || 'Login failed'
        }))
        return { success: false, error: result.message }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed'
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
      return { success: false, error: errorMessage }
    }
  }, [])

  const register = useCallback(async (data: RegisterData) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })
      
      const result = await response.json()
      
      if (result.success) {
        return { success: true }
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.message || 'Registration failed'
        }))
        return { success: false, error: result.message }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed'
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
      return { success: false, error: errorMessage }
    }
  }, [])

  const logout = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true }))
    
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear local storage
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_user')
      
      setState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        error: null
      })
      
      router.push('/login')
    }
  }, [router])

  const updateUser = useCallback((updates: Partial<User>) => {
    setState(prev => {
      if (!prev.user) return prev
      
      const updatedUser = { ...prev.user, ...updates }
      localStorage.setItem('auth_user', JSON.stringify(updatedUser))
      
      return {
        ...prev,
        user: updatedUser
      }
    })
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  const hasPermission = useCallback((permission: string) => {
    return state.user?.permissions?.includes(permission) || false
  }, [state.user])

  const hasRole = useCallback((role: string | string[]) => {
    if (!state.user?.role) return false
    
    if (Array.isArray(role)) {
      return role.includes(state.user.role)
    }
    
    return state.user.role === role
  }, [state.user])

  const isAdmin = useCallback(() => {
    return hasRole(['super_admin', 'admin'])
  }, [hasRole])

  return {
    ...state,
    login,
    register,
    logout,
    updateUser,
    clearError,
    hasPermission,
    hasRole,
    isAdmin
  }
}
