/**
 * MySQL Transformers API Route
 *
 * This API route handles transformer data requests from MySQL database.
 */

import { NextRequest, NextResponse } from 'next/server';
import { MySQLServerService } from '@/src/lib/mysql-server';
import { getPool } from '@/src/lib/mysql-connection';

export async function GET(request: NextRequest) {
  try {
    console.log('⚡ API: Fetching transformers from MySQL...');

    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();

    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        {
          error: 'MySQL connection failed',
          fallback: true
        },
        { status: 503 }
      );
    }

    // Get transformers data and statistics
    const transformers = await MySQLServerService.getTransformers();

    // Calculate statistics from the transformer data
    const statistics = {
      total: transformers.length,
      byStatus: transformers.reduce((acc: any, t: any) => {
        acc[t.status] = (acc[t.status] || 0) + 1;
        return acc;
      }, {}),
      byRegion: transformers.reduce((acc: any, t: any) => {
        acc[t.regionName] = (acc[t.regionName] || 0) + 1;
        return acc;
      }, {}),
      alerts: {
        critical: transformers.filter((t: any) => t.status === 'critical').length,
        warning: transformers.filter((t: any) => t.status === 'warning').length,
        info: 0,
        total: transformers.filter((t: any) => ['critical', 'warning'].includes(t.status)).length
      },
      maintenance: {
        scheduled: transformers.filter((t: any) => t.status === 'maintenance').length,
        overdue: 0, // Would need maintenance schedule data
        completed: 0, // Would need maintenance history data
        total: transformers.filter((t: any) => t.status === 'maintenance').length
      }
    };

    console.log(`✅ API: ${transformers.length} transformers fetched successfully from MySQL`);

    return NextResponse.json({
      success: true,
      data: {
        transformers,
        statistics
      },
      count: transformers.length,
      source: 'mysql'
    });

  } catch (error) {
    console.error('❌ API: Error fetching transformers:', error);

    return NextResponse.json(
      {
        error: 'Failed to fetch transformers',
        message: error instanceof Error ? error.message : 'Unknown error',
        fallback: true
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('⚡ API: Creating new transformer in MySQL...');

    const body = await request.json();
    const pool = getPool();

    // Generate unique serial number if not provided
    const serialNumber = body.serialNumber || `ETH-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // Insert new transformer
    const [result] = await pool.execute(`
      INSERT INTO app_transformers (
        serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
        manufacturer, model, year_manufactured, installation_date, location_name,
        latitude, longitude, region_id, service_center_id, status,
        efficiency_rating, load_factor, temperature, oil_level,
        last_maintenance, next_maintenance, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      serialNumber,
      body.name || `${body.manufacturer || 'Unknown'} Transformer`,
      body.type || 'distribution',
      body.capacity || body.capacityKva || 500,
      body.voltagePrimary || body.voltage_primary || 11.0,
      body.voltageSecondary || body.voltage_secondary || 0.4,
      body.manufacturer || 'Unknown',
      body.model || 'Standard',
      body.yearManufactured || body.year_manufactured || new Date().getFullYear(),
      body.installationDate || body.installation_date || new Date().toISOString().split('T')[0],
      body.locationName || body.location_name || body.location || 'Unknown Location',
      body.latitude || 0,
      body.longitude || 0,
      body.regionId || body.region_id || 1,
      body.serviceCenterId || body.service_center_id || 1,
      body.status || 'operational',
      body.efficiencyRating || body.efficiency_rating || 95.0,
      body.loadFactor || body.load_factor || 75.0,
      body.temperature || 35.0,
      body.oilLevel || body.oil_level || 90.0,
      body.lastMaintenance || body.last_maintenance || null,
      body.nextMaintenance || body.next_maintenance || null
    ]);

    const insertId = (result as any).insertId;

    console.log(`✅ API: Transformer created successfully with ID ${insertId}`);

    return NextResponse.json({
      success: true,
      data: {
        id: insertId,
        serialNumber: serialNumber,
        message: 'Transformer created successfully'
      },
      timestamp: new Date().toISOString()
    }, { status: 201 });

  } catch (error) {
    console.error('❌ API: Error creating transformer:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Database error',
        message: 'Failed to create transformer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
