/**
 * String Utilities
 * Common string manipulation and formatting functions
 */

// String formatting functions
export const capitalize = (str: string): string => {
  if (!str) return ''
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

export const capitalizeWords = (str: string): string => {
  if (!str) return ''
  return str.split(' ').map(word => capitalize(word)).join(' ')
}

export const camelCase = (str: string): string => {
  if (!str) return ''
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase()
    })
    .replace(/\s+/g, '')
}

export const pascalCase = (str: string): string => {
  if (!str) return ''
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, word => word.toUpperCase())
    .replace(/\s+/g, '')
}

export const kebabCase = (str: string): string => {
  if (!str) return ''
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase()
}

export const snakeCase = (str: string): string => {
  if (!str) return ''
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase()
}

export const constantCase = (str: string): string => {
  if (!str) return ''
  return snakeCase(str).toUpperCase()
}

// String validation functions
export const isEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const isPhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s\-\(\)]+$/
  return phoneRegex.test(phone)
}

export const isUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export const isNumeric = (str: string): boolean => {
  return !isNaN(Number(str)) && !isNaN(parseFloat(str))
}

export const isAlphabetic = (str: string): boolean => {
  return /^[a-zA-Z]+$/.test(str)
}

export const isAlphanumeric = (str: string): boolean => {
  return /^[a-zA-Z0-9]+$/.test(str)
}

export const isEmpty = (str: string | null | undefined): boolean => {
  return !str || str.trim().length === 0
}

export const isNotEmpty = (str: string | null | undefined): boolean => {
  return !isEmpty(str)
}

// String manipulation functions
export const truncate = (str: string, length: number, suffix: string = '...'): string => {
  if (!str || str.length <= length) return str
  return str.substring(0, length - suffix.length) + suffix
}

export const truncateWords = (str: string, wordCount: number, suffix: string = '...'): string => {
  if (!str) return ''
  const words = str.split(' ')
  if (words.length <= wordCount) return str
  return words.slice(0, wordCount).join(' ') + suffix
}

export const stripHtml = (html: string): string => {
  if (!html) return ''
  return html.replace(/<[^>]*>/g, '')
}

export const escapeHtml = (str: string): string => {
  if (!str) return ''
  const div = document.createElement('div')
  div.textContent = str
  return div.innerHTML
}

export const unescapeHtml = (str: string): string => {
  if (!str) return ''
  const div = document.createElement('div')
  div.innerHTML = str
  return div.textContent || div.innerText || ''
}

export const removeAccents = (str: string): string => {
  if (!str) return ''
  return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '')
}

export const slugify = (str: string): string => {
  if (!str) return ''
  return removeAccents(str)
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

// String search and replace functions
export const highlightText = (text: string, query: string, className: string = 'highlight'): string => {
  if (!text || !query) return text
  
  const regex = new RegExp(`(${escapeRegex(query)})`, 'gi')
  return text.replace(regex, `<span class="${className}">$1</span>`)
}

export const escapeRegex = (str: string): string => {
  if (!str) return ''
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

export const replaceAll = (str: string, search: string, replace: string): string => {
  if (!str) return ''
  return str.split(search).join(replace)
}

export const removeExtraSpaces = (str: string): string => {
  if (!str) return ''
  return str.replace(/\s+/g, ' ').trim()
}

// String generation functions
export const generateId = (length: number = 8): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export const generateUuid = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

export const generateSlug = (str: string, maxLength: number = 50): string => {
  const slug = slugify(str)
  return truncate(slug, maxLength, '')
}

// String parsing functions
export const parseInitials = (name: string): string => {
  if (!name) return ''
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .substring(0, 3)
}

export const parseFullName = (fullName: string): { firstName: string; lastName: string } => {
  if (!fullName) return { firstName: '', lastName: '' }
  
  const parts = fullName.trim().split(' ')
  const firstName = parts[0] || ''
  const lastName = parts.slice(1).join(' ') || ''
  
  return { firstName, lastName }
}

export const formatFullName = (firstName: string, lastName: string): string => {
  const first = firstName?.trim() || ''
  const last = lastName?.trim() || ''
  
  if (!first && !last) return ''
  if (!first) return last
  if (!last) return first
  
  return `${first} ${last}`
}

// String comparison functions
export const similarity = (str1: string, str2: string): number => {
  if (!str1 || !str2) return 0
  if (str1 === str2) return 1
  
  const longer = str1.length > str2.length ? str1 : str2
  const shorter = str1.length > str2.length ? str2 : str1
  
  if (longer.length === 0) return 1
  
  const distance = levenshteinDistance(longer, shorter)
  return (longer.length - distance) / longer.length
}

export const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = []
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i]
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1]
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        )
      }
    }
  }
  
  return matrix[str2.length][str1.length]
}

export const fuzzySearch = (query: string, items: string[], threshold: number = 0.3): string[] => {
  if (!query) return items
  
  return items
    .map(item => ({ item, score: similarity(query.toLowerCase(), item.toLowerCase()) }))
    .filter(({ score }) => score >= threshold)
    .sort((a, b) => b.score - a.score)
    .map(({ item }) => item)
}

// String formatting for display
export const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

export const formatNumber = (num: number, locale: string = 'en-US'): string => {
  return new Intl.NumberFormat(locale).format(num)
}

export const formatCurrency = (amount: number, currency: string = 'ETB', locale: string = 'en-US'): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency
  }).format(amount)
}

export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`
}

// String masking functions
export const maskEmail = (email: string): string => {
  if (!email || !isEmail(email)) return email
  
  const [username, domain] = email.split('@')
  const maskedUsername = username.length > 2 
    ? username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1)
    : username
  
  return `${maskedUsername}@${domain}`
}

export const maskPhone = (phone: string): string => {
  if (!phone) return phone
  
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length < 4) return phone
  
  const masked = cleaned.slice(0, -4).replace(/\d/g, '*') + cleaned.slice(-4)
  return masked
}

export const maskCreditCard = (cardNumber: string): string => {
  if (!cardNumber) return cardNumber
  
  const cleaned = cardNumber.replace(/\D/g, '')
  if (cleaned.length < 4) return cardNumber
  
  return '*'.repeat(cleaned.length - 4) + cleaned.slice(-4)
}

// Export all utilities
export default {
  capitalize,
  capitalizeWords,
  camelCase,
  pascalCase,
  kebabCase,
  snakeCase,
  constantCase,
  isEmail,
  isPhone,
  isUrl,
  isNumeric,
  isAlphabetic,
  isAlphanumeric,
  isEmpty,
  isNotEmpty,
  truncate,
  truncateWords,
  stripHtml,
  escapeHtml,
  unescapeHtml,
  removeAccents,
  slugify,
  highlightText,
  escapeRegex,
  replaceAll,
  removeExtraSpaces,
  generateId,
  generateUuid,
  generateSlug,
  parseInitials,
  parseFullName,
  formatFullName,
  similarity,
  levenshteinDistance,
  fuzzySearch,
  formatBytes,
  formatNumber,
  formatCurrency,
  formatPercentage,
  maskEmail,
  maskPhone,
  maskCreditCard
}
