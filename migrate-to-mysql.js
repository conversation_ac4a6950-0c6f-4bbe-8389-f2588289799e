/**
 * MySQL Migration Script (JavaScript version)
 * 
 * This script migrates all data from the JSON database to MySQL dtms_eeu_db database.
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db',
  multipleStatements: true
};

let connection;

/**
 * Initialize MySQL connection
 */
async function initConnection() {
  try {
    connection = await mysql.createConnection(config);
    console.log('✅ MySQL connection established');
  } catch (error) {
    console.error('❌ Failed to connect to MySQL:', error.message);
    throw error;
  }
}

/**
 * Execute MySQL query
 */
async function executeQuery(query, params = []) {
  try {
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('❌ Query error:', error.message);
    console.error('Query:', query.substring(0, 100) + '...');
    throw error;
  }
}

/**
 * Initialize MySQL schema
 */
async function initializeSchema() {
  console.log('🔄 Initializing MySQL schema...');
  
  try {
    // Read schema file
    const schemaPath = path.join(__dirname, 'lib', 'mysql-schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Execute schema
    await connection.query(schema);
    console.log('✅ MySQL schema initialized');
    
  } catch (error) {
    console.error('❌ Schema initialization failed:', error.message);
    throw error;
  }
}

/**
 * Load JSON data
 */
function loadJsonData() {
  console.log('📂 Loading JSON database data...');
  
  const dataDir = path.join(__dirname, 'data');
  const data = {};
  
  try {
    // Load all JSON files
    const files = ['users.json', 'regions.json', 'service-centers.json', 'transformers.json', 
                   'maintenance-records.json', 'alerts.json', 'outages.json', 'weather-alerts.json'];
    
    files.forEach(file => {
      const filePath = path.join(dataDir, file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        const key = file.replace('.json', '').replace('-', '_');
        data[key] = JSON.parse(content);
        console.log(`   Loaded ${data[key].length} records from ${file}`);
      } else {
        console.log(`   File ${file} not found, skipping...`);
        data[file.replace('.json', '').replace('-', '_')] = [];
      }
    });
    
    return data;
    
  } catch (error) {
    console.error('❌ Failed to load JSON data:', error.message);
    throw error;
  }
}

/**
 * Migrate users
 */
async function migrateUsers(users) {
  console.log(`🔄 Migrating ${users.length} users...`);
  
  for (const user of users) {
    await executeQuery(`
      INSERT INTO users (
        id, created_at, updated_at, email, password_hash, salt,
        first_name, last_name, role, is_active, last_login,
        region_id, service_center_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        updated_at = VALUES(updated_at),
        first_name = VALUES(first_name),
        last_name = VALUES(last_name),
        role = VALUES(role),
        is_active = VALUES(is_active)
    `, [
      user.id,
      user.createdAt,
      user.updatedAt,
      user.email,
      user.passwordHash,
      user.salt,
      user.firstName,
      user.lastName,
      user.role,
      user.isActive,
      user.lastLogin || null,
      user.regionId || null,
      user.serviceCenterId || null
    ]);
  }
  
  console.log('✅ Users migrated');
}

/**
 * Migrate regions
 */
async function migrateRegions(regions) {
  console.log(`🔄 Migrating ${regions.length} regions...`);
  
  for (const region of regions) {
    await executeQuery(`
      INSERT INTO regions (
        id, created_at, updated_at, name, code, description,
        coordinates_lat, coordinates_lng, population, area_km2
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        updated_at = VALUES(updated_at),
        name = VALUES(name),
        description = VALUES(description),
        coordinates_lat = VALUES(coordinates_lat),
        coordinates_lng = VALUES(coordinates_lng)
    `, [
      region.id,
      region.createdAt,
      region.updatedAt,
      region.name,
      region.code,
      region.description || null,
      region.coordinates.lat,
      region.coordinates.lng,
      region.population || 0,
      region.areaKm2 || 0
    ]);
  }
  
  console.log('✅ Regions migrated');
}

/**
 * Migrate service centers
 */
async function migrateServiceCenters(serviceCenters) {
  console.log(`🔄 Migrating ${serviceCenters.length} service centers...`);
  
  for (const center of serviceCenters) {
    await executeQuery(`
      INSERT INTO service_centers (
        id, created_at, updated_at, name, code, region_id,
        address, coordinates_lat, coordinates_lng, contact_phone,
        contact_email, manager_name, capacity
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        updated_at = VALUES(updated_at),
        name = VALUES(name),
        address = VALUES(address),
        coordinates_lat = VALUES(coordinates_lat),
        coordinates_lng = VALUES(coordinates_lng)
    `, [
      center.id,
      center.createdAt,
      center.updatedAt,
      center.name,
      center.code,
      center.regionId,
      center.address || null,
      center.coordinates.lat,
      center.coordinates.lng,
      center.contactPhone || null,
      center.contactEmail || null,
      center.managerName || null,
      center.capacity || 0
    ]);
  }
  
  console.log('✅ Service centers migrated');
}

/**
 * Migrate transformers
 */
async function migrateTransformers(transformers) {
  console.log(`🔄 Migrating ${transformers.length} transformers...`);
  
  for (const transformer of transformers) {
    await executeQuery(`
      INSERT INTO transformers (
        id, created_at, updated_at, serial_number, name, status,
        type, manufacturer, model, manufacture_date, installation_date,
        last_maintenance_date, next_maintenance_date, capacity,
        voltage_primary, voltage_secondary, region_id, service_center_id,
        location_address, location_lat, location_lng, temperature,
        load_percentage, oil_level, health_index, tags
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        updated_at = VALUES(updated_at),
        status = VALUES(status),
        temperature = VALUES(temperature),
        load_percentage = VALUES(load_percentage),
        oil_level = VALUES(oil_level),
        health_index = VALUES(health_index)
    `, [
      transformer.id,
      transformer.createdAt,
      transformer.updatedAt,
      transformer.serialNumber,
      transformer.name,
      transformer.status,
      transformer.type,
      transformer.manufacturer,
      transformer.model,
      transformer.manufactureDate || null,
      transformer.installationDate || null,
      transformer.lastMaintenanceDate || null,
      transformer.nextMaintenanceDate || null,
      transformer.capacity,
      transformer.voltage.primary,
      transformer.voltage.secondary,
      transformer.regionId,
      transformer.serviceCenterId,
      transformer.location.address || null,
      transformer.location.coordinates.lat,
      transformer.location.coordinates.lng,
      transformer.metrics.temperature,
      transformer.metrics.loadPercentage,
      transformer.metrics.oilLevel,
      transformer.metrics.healthIndex,
      JSON.stringify(transformer.tags || [])
    ]);
  }
  
  console.log('✅ Transformers migrated');
}

/**
 * Main migration function
 */
async function runMigration() {
  console.log('🚀 Starting MySQL Migration to dtms_eeu_db');
  console.log('============================================');
  
  const startTime = Date.now();
  
  try {
    // Step 1: Initialize connection
    await initConnection();
    
    // Step 2: Initialize schema
    await initializeSchema();
    
    // Step 3: Load JSON data
    const data = loadJsonData();
    
    // Step 4: Migrate data
    console.log('📦 Starting data migration...');
    
    await migrateUsers(data.users || []);
    await migrateRegions(data.regions || []);
    await migrateServiceCenters(data.service_centers || []);
    await migrateTransformers(data.transformers || []);
    
    // Step 5: Verify migration
    console.log('🔍 Verifying migration...');
    
    const counts = {
      users: (await executeQuery('SELECT COUNT(*) as count FROM users'))[0].count,
      regions: (await executeQuery('SELECT COUNT(*) as count FROM regions'))[0].count,
      service_centers: (await executeQuery('SELECT COUNT(*) as count FROM service_centers'))[0].count,
      transformers: (await executeQuery('SELECT COUNT(*) as count FROM transformers'))[0].count
    };
    
    console.log('📊 Migration Results:');
    console.log(`   Users: ${counts.users}`);
    console.log(`   Regions: ${counts.regions}`);
    console.log(`   Service Centers: ${counts.service_centers}`);
    console.log(`   Transformers: ${counts.transformers}`);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`⏱️  Migration completed in ${duration.toFixed(2)} seconds`);
    console.log('🎉 Migration successful!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run migration
runMigration().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
