-- Seed data for Ethiopian Electric Utility Transformer Management System
USE dtms_eeu_db;

-- Insert Ethiopian Regions
INSERT INTO app_regions (name, code, population, area_km2) VALUES
('Addis Ababa', 'AA', 3500000, 527.00),
('Oromia', 'OR', 37000000, 353006.81),
('Amhara', 'AM', 21000000, 154708.96),
('Southern Nations, Nationalities, and Peoples', 'SNNPR', 20000000, 105887.18),
('Tigray', 'TI', 5500000, 50078.64),
('Somali', 'SO', 5500000, 279252.00),
('Afar', 'AF', 1800000, 96707.00),
('Benishangul-Gumuz', 'BG', 1100000, 50699.00),
('Gambela', 'GA', 450000, 29782.82),
('<PERSON><PERSON>', 'HA', 250000, 311.25),
('<PERSON><PERSON> Dawa', 'DD', 500000, 1213.20);

-- Insert Service Centers
INSERT INTO app_service_centers (name, code, region_id, address, phone, email, manager_name) VALUES
-- Addis Ababa Service Centers
('Addis Ababa Central', 'AAC001', 1, 'Bole Road, Addis Ababa', '+251-11-123-4567', '<EMAIL>', 'Alemayehu Tadesse'),
('Addis Ababa North', 'AAN002', 1, 'Piassa, Addis Ababa', '+251-11-234-5678', '<EMAIL>', 'Meron Bekele'),
('Addis Ababa South', 'AAS003', 1, 'Akaki, Addis Ababa', '+251-11-345-6789', '<EMAIL>', 'Dawit Haile'),

-- Oromia Service Centers
('Adama Service Center', 'OR001', 2, 'Adama City, Oromia', '+251-22-112-3456', '<EMAIL>', 'Tigist Worku'),
('Jimma Service Center', 'OR002', 2, 'Jimma City, Oromia', '+251-47-111-2345', '<EMAIL>', 'Bekele Megersa'),
('Nekemte Service Center', 'OR003', 2, 'Nekemte City, Oromia', '+251-57-661-1234', '<EMAIL>', 'Chaltu Daba'),

-- Amhara Service Centers
('Bahir Dar Service Center', 'AM001', 3, 'Bahir Dar City, Amhara', '+251-58-220-1234', '<EMAIL>', 'Yohannes Alemu'),
('Gondar Service Center', 'AM002', 3, 'Gondar City, Amhara', '+251-58-111-2345', '<EMAIL>', 'Almaz Tesfaye'),
('Dessie Service Center', 'AM003', 3, 'Dessie City, Amhara', '+251-33-111-2345', '<EMAIL>', 'Getachew Assefa'),

-- SNNPR Service Centers
('Hawassa Service Center', 'SN001', 4, 'Hawassa City, SNNPR', '+251-46-220-1234', '<EMAIL>', 'Mulugeta Kebede'),
('Arba Minch Service Center', 'SN002', 4, 'Arba Minch, SNNPR', '+251-46-881-1234', '<EMAIL>', 'Hanna Girma'),

-- Other Regions
('Mekelle Service Center', 'TI001', 5, 'Mekelle City, Tigray', '+251-34-440-1234', '<EMAIL>', 'Gebrehiwot Tekle'),
('Jijiga Service Center', 'SO001', 6, 'Jijiga City, Somali', '+251-25-552-1234', '<EMAIL>', 'Abdullahi Hassan'),
('Semera Service Center', 'AF001', 7, 'Semera City, Afar', '+251-33-660-1234', '<EMAIL>', 'Ali Ahmed'),
('Assosa Service Center', 'BG001', 8, 'Assosa City, Benishangul-Gumuz', '+251-57-775-1234', '<EMAIL>', 'Berhanu Mulugeta'),
('Gambela Service Center', 'GA001', 9, 'Gambela City, Gambela', '+251-47-551-1234', '<EMAIL>', 'Ojulu Obang'),
('Harar Service Center', 'HA001', 10, 'Harar City, Harari', '+251-25-666-1234', '<EMAIL>', 'Abdulkadir Mohammed'),
('Dire Dawa Service Center', 'DD001', 11, 'Dire Dawa City', '+251-25-111-2345', '<EMAIL>', 'Yusuf Ibrahim');

-- Insert Users (Technicians and Staff)
INSERT INTO app_users (username, email, first_name, last_name, phone, role, region_id, service_center_id, is_active) VALUES
-- Super Admin
('admin', '<EMAIL>', 'System', 'Administrator', '+251-11-000-0000', 'super_admin', 1, 1, TRUE),

-- National Level Staff
('nmanager', '<EMAIL>', 'Abebe', 'Kebede', '+251-11-111-1111', 'national_asset_manager', 1, 1, TRUE),
('nmaintenance', '<EMAIL>', 'Almaz', 'Tesfaye', '+251-11-222-2222', 'national_maintenance_manager', 1, 1, TRUE),

-- Regional Admins
('aa_admin', '<EMAIL>', 'Alemayehu', 'Tadesse', '+251-11-123-4567', 'regional_admin', 1, 1, TRUE),
('or_admin', '<EMAIL>', 'Bekele', 'Megersa', '+251-22-112-3456', 'regional_admin', 2, 4, TRUE),
('am_admin', '<EMAIL>', 'Yohannes', 'Alemu', '+251-58-220-1234', 'regional_admin', 3, 7, TRUE),

-- Field Technicians
('tech001', '<EMAIL>', 'Alemayehu', 'Tadesse', '+251-91-123-4567', 'field_technician', 1, 1, TRUE),
('tech002', '<EMAIL>', 'Meron', 'Bekele', '+251-91-234-5678', 'field_technician', 2, 4, TRUE),
('tech003', '<EMAIL>', 'Dawit', 'Haile', '+251-91-345-6789', 'field_technician', 3, 7, FALSE),
('tech004', '<EMAIL>', 'Tigist', 'Worku', '+251-91-456-7890', 'field_technician', 4, 10, TRUE),
('tech005', '<EMAIL>', 'Gebrehiwot', 'Tekle', '+251-91-567-8901', 'field_technician', 5, 12, TRUE),
('tech006', '<EMAIL>', 'Abdullahi', 'Hassan', '+251-91-678-9012', 'field_technician', 6, 13, TRUE),

-- Service Center Managers
('sc_manager001', '<EMAIL>', 'Mulugeta', 'Kebede', '+251-46-220-1234', 'service_center_manager', 4, 10, TRUE),
('sc_manager002', '<EMAIL>', 'Hanna', 'Girma', '+251-46-881-1234', 'service_center_manager', 4, 11, TRUE),

-- Maintenance Engineers
('maint001', '<EMAIL>', 'Getachew', 'Assefa', '+251-33-111-2345', 'regional_maintenance_engineer', 3, 9, TRUE),
('maint002', '<EMAIL>', 'Chaltu', 'Daba', '+251-57-661-1234', 'regional_maintenance_engineer', 2, 6, TRUE);

-- Insert Transformers (Sample data for different regions)
INSERT INTO app_transformers (serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
                             manufacturer, model, year_manufactured, installation_date, location_name,
                             latitude, longitude, region_id, service_center_id, status, efficiency_rating,
                             load_factor, temperature, oil_level, last_maintenance, next_maintenance) VALUES

-- Addis Ababa Transformers
('T-AA-001', 'Bole International Airport T1', 'power', 2500.00, 15000.00, 400.00, 'ABB', 'ONAN-2500', 2020, '2020-03-15', 'Bole International Airport', 8.9806, 38.7992, 1, 1, 'operational', 96.2, 85.5, 65.2, 85.0, '2024-01-10', '2024-07-10'),
('T-AA-002', 'Merkato Commercial District T1', 'distribution', 1000.00, 15000.00, 400.00, 'Siemens', 'ONAN-1000', 2019, '2019-08-20', 'Merkato Commercial Area', 9.0192, 38.7441, 1, 1, 'operational', 94.8, 92.3, 68.5, 82.0, '2023-12-15', '2024-06-15'),
('T-AA-003', 'Piassa Residential T1', 'distribution', 630.00, 15000.00, 400.00, 'Schneider', 'ONAN-630', 2021, '2021-05-10', 'Piassa Residential Area', 9.0370, 38.7578, 1, 2, 'warning', 93.5, 78.2, 72.8, 75.0, '2023-11-20', '2024-05-20'),
('T-AA-004', 'Akaki Industrial T1', 'power', 1600.00, 15000.00, 400.00, 'ABB', 'ONAN-1600', 2018, '2018-12-05', 'Akaki Industrial Zone', 8.8667, 38.7667, 1, 3, 'operational', 95.1, 88.7, 66.3, 88.0, '2024-01-05', '2024-07-05'),

-- Oromia Transformers
('T-OR-001', 'Adama City Center T1', 'power', 2000.00, 15000.00, 400.00, 'ABB', 'ONAN-2000', 2020, '2020-06-12', 'Adama City Center', 8.5400, 39.2675, 2, 4, 'operational', 95.8, 87.2, 67.1, 86.0, '2024-01-08', '2024-07-08'),
('T-OR-002', 'Jimma University T1', 'distribution', 1250.00, 15000.00, 400.00, 'Siemens', 'ONAN-1250', 2019, '2019-09-18', 'Jimma University Campus', 7.6667, 36.8333, 2, 5, 'operational', 94.2, 76.8, 69.4, 83.0, '2023-12-20', '2024-06-20'),
('T-OR-003', 'Nekemte Hospital T1', 'distribution', 800.00, 15000.00, 400.00, 'Schneider', 'ONAN-800', 2021, '2021-03-25', 'Nekemte General Hospital', 9.0833, 36.5500, 2, 6, 'maintenance', 92.8, 65.4, 58.2, 90.0, '2024-01-12', '2024-07-12'),

-- Amhara Transformers
('T-AM-001', 'Bahir Dar University T1', 'power', 1800.00, 15000.00, 400.00, 'ABB', 'ONAN-1800', 2020, '2020-04-08', 'Bahir Dar University', 11.5933, 37.3617, 3, 7, 'operational', 96.0, 82.1, 64.7, 87.0, '2024-01-15', '2024-07-15'),
('T-AM-002', 'Gondar Castle Area T1', 'distribution', 1000.00, 15000.00, 400.00, 'Siemens', 'ONAN-1000', 2018, '2018-11-30', 'Gondar Historical Area', 12.6000, 37.4667, 3, 8, 'warning', 93.2, 89.5, 71.3, 78.0, '2023-11-25', '2024-05-25'),
('T-AM-003', 'Dessie Commercial T1', 'distribution', 630.00, 15000.00, 400.00, 'Schneider', 'ONAN-630', 2021, '2021-07-14', 'Dessie Commercial District', 11.1167, 39.6333, 3, 9, 'critical', 89.5, 95.2, 78.9, 65.0, '2023-10-10', '2024-04-10');

-- Insert more transformers for other regions (continuing the pattern)
INSERT INTO app_transformers (serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
                             manufacturer, model, year_manufactured, installation_date, location_name,
                             latitude, longitude, region_id, service_center_id, status, efficiency_rating,
                             load_factor, temperature, oil_level, last_maintenance, next_maintenance) VALUES

-- SNNPR Transformers
('T-SN-001', 'Hawassa Lake Resort T1', 'power', 1500.00, 15000.00, 400.00, 'ABB', 'ONAN-1500', 2020, '2020-02-20', 'Hawassa Lake Resort Area', 7.0500, 38.4667, 4, 10, 'operational', 95.5, 84.3, 66.8, 85.0, '2024-01-18', '2024-07-18'),
('T-SN-002', 'Arba Minch University T1', 'distribution', 1000.00, 15000.00, 400.00, 'Siemens', 'ONAN-1000', 2019, '2019-10-12', 'Arba Minch University', 6.0333, 37.5500, 4, 11, 'operational', 94.7, 79.6, 68.2, 84.0, '2023-12-22', '2024-06-22'),

-- Tigray Transformers
('T-TI-001', 'Mekelle University T1', 'power', 2000.00, 15000.00, 400.00, 'ABB', 'ONAN-2000', 2019, '2019-05-15', 'Mekelle University Campus', 13.4967, 39.4733, 5, 12, 'operational', 95.2, 81.7, 67.5, 86.0, '2024-01-20', '2024-07-20'),

-- Somali Transformers
('T-SO-001', 'Jijiga Airport T1', 'distribution', 800.00, 15000.00, 400.00, 'Schneider', 'ONAN-800', 2020, '2020-08-30', 'Jijiga Airport', 9.3333, 42.9167, 6, 13, 'warning', 92.1, 73.4, 70.1, 80.0, '2023-11-30', '2024-05-30');

-- Insert Alerts
INSERT INTO app_alerts (transformer_id, title, description, severity, type, status, priority, created_by, assigned_to) VALUES
(3, 'High Temperature Alert', 'Transformer T-AA-003 temperature exceeding safe limits (72.8°C)', 'high', 'temperature', 'active', 'high', 6, 6),
(9, 'Critical Load Imbalance', 'Transformer T-AM-003 showing critical load imbalance and overheating', 'critical', 'load', 'investigating', 'critical', 6, 10),
(2, 'Voltage Fluctuation Detected', 'Irregular voltage patterns detected in Merkato area', 'medium', 'voltage', 'monitoring', 'medium', 6, 6),
(13, 'Communication Loss', 'Lost communication with remote monitoring station at Jijiga Airport', 'high', 'communication', 'active', 'high', 6, 13),
(7, 'Scheduled Maintenance Due', 'Routine maintenance scheduled for Gondar Castle Area transformer', 'low', 'maintenance', 'resolved', 'low', 10, 10),
(5, 'Oil Level Warning', 'Oil level below recommended threshold in Adama City Center transformer', 'medium', 'maintenance', 'monitoring', 'medium', 7, 7),
(1, 'Weather Risk Alert', 'High wind conditions may affect Bole Airport transformer operations', 'medium', 'weather', 'monitoring', 'medium', 6, 6);

-- Insert Maintenance Schedules
INSERT INTO app_maintenance_schedules (transformer_id, type, title, description, scheduled_date, estimated_duration, priority, status, technician_id, supervisor_id, cost_estimate) VALUES
(3, 'emergency', 'Emergency Temperature Control Repair', 'Urgent repair needed for cooling system malfunction', '2024-01-15', 4, 'critical', 'scheduled', 6, 4, 15000.00),
(2, 'routine', 'Oil Analysis and Change', 'Routine oil quality analysis and replacement if needed', '2024-01-16', 6, 'medium', 'scheduled', 7, 4, 8000.00),
(6, 'preventive', 'Cooling System Inspection', 'Preventive inspection of cooling system components', '2024-01-17', 3, 'medium', 'scheduled', 8, 15, 5000.00),
(4, 'scheduled', 'Bushing Replacement', 'Scheduled replacement of high voltage bushings', '2024-01-18', 8, 'high', 'scheduled', 9, 4, 25000.00),
(1, 'routine', 'Annual Inspection', 'Comprehensive annual inspection and testing', '2024-01-20', 12, 'medium', 'scheduled', 6, 4, 12000.00),
(9, 'corrective', 'Load Balancing Correction', 'Corrective maintenance for load imbalance issues', '2024-01-19', 6, 'critical', 'in_progress', 10, 15, 18000.00),
(5, 'preventive', 'Transformer Oil Top-up', 'Preventive oil level maintenance', '2024-01-22', 2, 'low', 'scheduled', 7, 4, 3000.00);

-- Insert Notifications
INSERT INTO app_notifications (title, message, type, recipient_id, sender_id) VALUES
('Critical Alert: Transformer T-AM-003', 'Immediate attention required for critical temperature and load issues', 'error', 10, 1),
('Maintenance Scheduled', 'Emergency repair scheduled for T-AA-003 on January 15th', 'warning', 6, 4),
('System Update', 'Dashboard system has been updated with new features', 'info', 4, 1),
('Weather Warning', 'High wind conditions expected in Addis Ababa region', 'warning', 6, 1),
('Maintenance Completed', 'Routine maintenance completed successfully on T-SN-002', 'success', 4, 11),
('New Alert Generated', 'Communication loss detected at Jijiga Airport transformer', 'error', 13, 1),
('Performance Report', 'Monthly performance report is now available for download', 'info', 4, 1);

-- Insert Performance Metrics (Sample recent data)
INSERT INTO app_performance_metrics (transformer_id, metric_type, value, unit, recorded_at) VALUES
-- Power Generation Data (Last 24 hours)
(1, 'power_generation', 1850.0, 'MW', '2024-01-14 00:00:00'),
(1, 'power_generation', 2100.0, 'MW', '2024-01-14 06:00:00'),
(1, 'power_generation', 2847.0, 'MW', '2024-01-14 12:00:00'),
(1, 'power_generation', 2650.0, 'MW', '2024-01-14 18:00:00'),
(1, 'power_generation', 1950.0, 'MW', '2024-01-14 24:00:00'),

-- Efficiency Data (Last week)
(1, 'efficiency', 94.2, '%', '2024-01-08 12:00:00'),
(1, 'efficiency', 95.1, '%', '2024-01-09 12:00:00'),
(1, 'efficiency', 93.8, '%', '2024-01-10 12:00:00'),
(1, 'efficiency', 94.7, '%', '2024-01-11 12:00:00'),
(1, 'efficiency', 95.3, '%', '2024-01-12 12:00:00'),
(1, 'efficiency', 94.1, '%', '2024-01-13 12:00:00'),
(1, 'efficiency', 93.9, '%', '2024-01-14 12:00:00'),

-- Temperature monitoring
(3, 'temperature', 72.8, '°C', '2024-01-14 14:30:00'),
(9, 'temperature', 78.9, '°C', '2024-01-14 14:30:00'),
(2, 'temperature', 68.5, '°C', '2024-01-14 14:30:00'),

-- Load Factor monitoring
(1, 'load_factor', 85.5, '%', '2024-01-14 14:30:00'),
(2, 'load_factor', 92.3, '%', '2024-01-14 14:30:00'),
(9, 'load_factor', 95.2, '%', '2024-01-14 14:30:00');

-- Insert Weather Data
INSERT INTO app_weather_data (region_id, temperature, humidity, wind_speed, weather_condition, risk_level, recorded_at) VALUES
(1, 24.0, 65.0, 12.0, 'partly-cloudy', 'low', '2024-01-14 14:00:00'),
(2, 26.5, 58.0, 8.0, 'sunny', 'low', '2024-01-14 14:00:00'),
(3, 22.0, 72.0, 15.0, 'cloudy', 'medium', '2024-01-14 14:00:00'),
(4, 28.0, 55.0, 6.0, 'sunny', 'low', '2024-01-14 14:00:00'),
(5, 20.0, 68.0, 18.0, 'windy', 'medium', '2024-01-14 14:00:00'),
(6, 32.0, 45.0, 22.0, 'hot', 'high', '2024-01-14 14:00:00');
