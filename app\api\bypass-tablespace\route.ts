import { NextRequest, NextResponse } from 'next/server'
import { bypassTablespaceIssue } from '../../../src/lib/db/bypass-tablespace'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API: Tablespace bypass requested...')
    
    // Step 1: Create tables with new names to bypass tablespace issues
    console.log('🔄 Creating tables with new names to bypass tablespace issues...')
    const bypassResult = await bypassTablespaceIssue()
    
    if (!bypassResult.success) {
      console.error('❌ API: Tablespace bypass failed:', bypassResult.message)
      return NextResponse.json({
        success: false,
        message: bypassResult.message,
        error: bypassResult.error,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    console.log('✅ API: Tablespace bypass completed successfully')

    // Wait a moment for the tables to be fully committed
    console.log('⏳ Waiting for tables to be committed...')
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Step 2: Now we need to create a custom seeding function for the new table names
    console.log('🔄 Seeding database with Ethiopian data using new table names...')
    
    // For now, let's just return success - we'll create a custom seeder next
    console.log('✅ API: Tables created successfully, ready for seeding')

    return NextResponse.json({
      success: true,
      message: 'Tablespace bypass completed successfully - tables created with new names and views',
      bypassSuccess: true,
      tableNames: bypassResult.tableNames,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ API: Tablespace bypass failed:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Tablespace bypass failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Tablespace bypass API is ready. Use POST to create tables with new names.',
    timestamp: new Date().toISOString()
  })
}
