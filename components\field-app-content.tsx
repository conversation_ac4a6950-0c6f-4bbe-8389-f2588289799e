"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Smartphone, Download, QrCode, Wifi, CloudOff } from "lucide-react"

export function FieldAppContent() {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Field Technician Mobile App</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <QrCode className="mr-2 h-4 w-4" />
            QR Code
          </Button>
          <Button size="sm">
            <Download className="mr-2 h-4 w-4" />
            Download App
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>EEU Field App</CardTitle>
            <CardDescription>Mobile application for field technicians</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center text-center">
            <div className="relative h-64 w-32 mb-4 border-8 border-gray-800 rounded-3xl overflow-hidden">
              <div className="absolute inset-0 bg-[url('/placeholder.svg?height=300&width=150&text=EEU+Field+App')] bg-cover bg-center"></div>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">Ethiopia Electric Utility Field App</h3>
              <p className="text-sm text-muted-foreground">Version 2.0.4</p>
              <div className="flex justify-center gap-2">
                <Button variant="outline" size="sm">
                  Android
                </Button>
                <Button variant="outline" size="sm">
                  iOS
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Key Features</CardTitle>
            <CardDescription>Capabilities for field technicians</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-4">
              <li className="flex items-start gap-3">
                <div className="mt-0.5 rounded-full bg-teal-100 p-1 dark:bg-teal-900/20">
                  <Smartphone className="h-4 w-4 text-teal-600" />
                </div>
                <div>
                  <p className="font-medium">Offline Mode</p>
                  <p className="text-sm text-muted-foreground">
                    Work in areas with poor connectivity with full offline capabilities
                  </p>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <div className="mt-0.5 rounded-full bg-teal-100 p-1 dark:bg-teal-900/20">
                  <CloudOff className="h-4 w-4 text-teal-600" />
                </div>
                <div>
                  <p className="font-medium">Data Synchronization</p>
                  <p className="text-sm text-muted-foreground">Automatically sync data when connectivity is restored</p>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <div className="mt-0.5 rounded-full bg-teal-100 p-1 dark:bg-teal-900/20">
                  <Wifi className="h-4 w-4 text-teal-600" />
                </div>
                <div>
                  <p className="font-medium">Real-time Updates</p>
                  <p className="text-sm text-muted-foreground">Receive real-time alerts and maintenance assignments</p>
                </div>
              </li>
              <li className="flex items-start gap-3">
                <div className="mt-0.5 rounded-full bg-teal-100 p-1 dark:bg-teal-900/20">
                  <QrCode className="h-4 w-4 text-teal-600" />
                </div>
                <div>
                  <p className="font-medium">QR Code Scanning</p>
                  <p className="text-sm text-muted-foreground">
                    Quickly identify transformers and equipment with QR code scanning
                  </p>
                </div>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage Guide</TabsTrigger>
          <TabsTrigger value="deployment">Deployment</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>App Overview</CardTitle>
              <CardDescription>Key information about the field technician app</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                The Ethiopia Electric Utility Field App is designed specifically for technicians working in the field
                across Ethiopia. It provides essential tools for transformer maintenance, inspection, and repair while
                functioning in areas with limited connectivity.
              </p>
              <p>
                With support for Amharic, English, and other local languages, the app ensures all technicians can work
                efficiently regardless of language preference. The offline-first approach allows technicians to complete
                their work even in remote areas with poor network coverage.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Supported Devices</h3>
                  <ul className="text-sm space-y-1">
                    <li>• Android 8.0 and above</li>
                    <li>• iOS 12.0 and above</li>
                    <li>• Minimum 2GB RAM</li>
                    <li>• 500MB storage space</li>
                  </ul>
                </div>
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Languages</h3>
                  <ul className="text-sm space-y-1">
                    <li>• English</li>
                    <li>• አማርኛ (Amharic)</li>
                    <li>• ትግርኛ (Tigrinya)</li>
                    <li>• Afaan Oromoo</li>
                  </ul>
                </div>
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Data Usage</h3>
                  <ul className="text-sm space-y-1">
                    <li>• Initial download: 45MB</li>
                    <li>• Daily sync: ~5MB</li>
                    <li>• Offline storage: Up to 200MB</li>
                    <li>• Compressed image uploads</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage Guide</CardTitle>
              <CardDescription>How to use the field technician app</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-12">Usage guide content would be displayed here</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="deployment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Deployment Status</CardTitle>
              <CardDescription>Current deployment status across regions</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-12">Deployment status would be displayed here</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
