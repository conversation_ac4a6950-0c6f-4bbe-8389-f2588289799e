/**
 * Database Configuration
 * Configuration for different database providers and environments
 */

export interface DatabaseConfig {
  provider: 'mysql' | 'sqlite' | 'supabase'
  connection: {
    host?: string
    port?: number
    database: string
    username?: string
    password?: string
    url?: string
    ssl?: boolean
  }
  pool?: {
    min: number
    max: number
    acquireTimeoutMillis: number
    createTimeoutMillis: number
    destroyTimeoutMillis: number
    idleTimeoutMillis: number
    reapIntervalMillis: number
    createRetryIntervalMillis: number
  }
  migrations?: {
    directory: string
    tableName: string
  }
  seeds?: {
    directory: string
  }
  debug?: boolean
}

// MySQL Configuration
export const MYSQL_CONFIG: DatabaseConfig = {
  provider: 'mysql',
  connection: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    database: process.env.DB_NAME || 'eeu_transformer_db',
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true'
  },
  pool: {
    min: 2,
    max: 10,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 100
  },
  migrations: {
    directory: './src/shared/lib/database/migrations',
    tableName: 'knex_migrations'
  },
  seeds: {
    directory: './src/shared/lib/database/seeds'
  },
  debug: process.env.NODE_ENV === 'development'
}

// SQLite Configuration (for development/testing)
export const SQLITE_CONFIG: DatabaseConfig = {
  provider: 'sqlite',
  connection: {
    database: process.env.SQLITE_DB_PATH || './data/eeu_transformer.db'
  },
  migrations: {
    directory: './src/shared/lib/database/migrations',
    tableName: 'knex_migrations'
  },
  seeds: {
    directory: './src/shared/lib/database/seeds'
  },
  debug: process.env.NODE_ENV === 'development'
}

// Supabase Configuration
export const SUPABASE_CONFIG: DatabaseConfig = {
  provider: 'supabase',
  connection: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    password: process.env.SUPABASE_ANON_KEY || '', // Using password field for anon key
    database: 'postgres' // Supabase uses PostgreSQL
  },
  debug: process.env.NODE_ENV === 'development'
}

// Database Schema Configuration
export const SCHEMA_CONFIG = {
  tables: {
    // Core tables
    users: 'users',
    roles: 'roles',
    permissions: 'permissions',
    user_roles: 'user_roles',
    role_permissions: 'role_permissions',
    
    // Transformer tables
    transformers: 'transformers',
    transformer_locations: 'transformer_locations',
    transformer_specifications: 'transformer_specifications',
    transformer_health: 'transformer_health',
    
    // Maintenance tables
    maintenance_records: 'maintenance_records',
    maintenance_schedules: 'maintenance_schedules',
    maintenance_teams: 'maintenance_teams',
    spare_parts: 'spare_parts',
    
    // Monitoring tables
    abnormality_reports: 'abnormality_reports',
    megger_tests: 'megger_tests',
    temperature_readings: 'temperature_readings',
    load_measurements: 'load_measurements',
    
    // Administrative tables
    service_centers: 'service_centers',
    regions: 'regions',
    switchgear_teams: 'switchgear_teams',
    
    // System tables
    audit_logs: 'audit_logs',
    notifications: 'notifications',
    system_settings: 'system_settings',
    file_uploads: 'file_uploads'
  },
  
  // Column naming conventions
  columns: {
    id: 'id',
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
    createdBy: 'created_by',
    updatedBy: 'updated_by'
  },
  
  // Index naming conventions
  indexes: {
    primary: 'pk',
    foreign: 'fk',
    unique: 'uk',
    index: 'idx'
  }
}

// Connection Pool Configuration
export const POOL_CONFIG = {
  development: {
    min: 1,
    max: 5
  },
  test: {
    min: 1,
    max: 2
  },
  production: {
    min: 5,
    max: 20
  }
}

// Migration Configuration
export const MIGRATION_CONFIG = {
  directory: './src/shared/lib/database/migrations',
  extension: 'ts',
  loadExtensions: ['.ts'],
  tableName: 'knex_migrations',
  schemaName: 'public',
  disableTransactions: false
}

// Seed Configuration
export const SEED_CONFIG = {
  directory: './src/shared/lib/database/seeds',
  loadExtensions: ['.ts'],
  specific: process.env.SEED_FILE || undefined
}

// Get database configuration based on environment and provider
export const getDatabaseConfig = (): DatabaseConfig => {
  const provider = (process.env.DB_PROVIDER as DatabaseConfig['provider']) || 'sqlite'
  const environment = process.env.NODE_ENV || 'development'
  
  let config: DatabaseConfig
  
  switch (provider) {
    case 'mysql':
      config = MYSQL_CONFIG
      break
    case 'supabase':
      config = SUPABASE_CONFIG
      break
    case 'sqlite':
    default:
      config = SQLITE_CONFIG
      break
  }
  
  // Apply environment-specific pool settings
  if (config.pool && POOL_CONFIG[environment as keyof typeof POOL_CONFIG]) {
    config.pool = {
      ...config.pool,
      ...POOL_CONFIG[environment as keyof typeof POOL_CONFIG]
    }
  }
  
  return config
}

// Database connection string builder
export const buildConnectionString = (config: DatabaseConfig): string => {
  const { connection } = config
  
  switch (config.provider) {
    case 'mysql':
      return `mysql://${connection.username}:${connection.password}@${connection.host}:${connection.port}/${connection.database}`
    
    case 'sqlite':
      return `sqlite:${connection.database}`
    
    case 'supabase':
      return connection.url || ''
    
    default:
      throw new Error(`Unsupported database provider: ${config.provider}`)
  }
}

// Validate database configuration
export const validateDatabaseConfig = (config: DatabaseConfig): boolean => {
  if (!config.provider) {
    throw new Error('Database provider is required')
  }
  
  if (!config.connection) {
    throw new Error('Database connection configuration is required')
  }
  
  switch (config.provider) {
    case 'mysql':
      if (!config.connection.host || !config.connection.database) {
        throw new Error('MySQL requires host and database')
      }
      break
    
    case 'sqlite':
      if (!config.connection.database) {
        throw new Error('SQLite requires database path')
      }
      break
    
    case 'supabase':
      if (!config.connection.url) {
        throw new Error('Supabase requires URL')
      }
      break
  }
  
  return true
}

// Export default configuration
export default getDatabaseConfig()
