// Seed demo data into app_regions2 and app_service_centers2
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

const regions = [
  { name: 'Addis Ababa', code: 'AA', population: 5000000, area_km2: 527 },
  { name: 'Oromia', code: 'OR', population: 35000000, area_km2: 353632 },
  { name: '<PERSON><PERSON>', code: 'AM', population: 23000000, area_km2: 154709 }
];

const serviceCenters = [
  { name: 'Bole Center', code: 'BOL', region_code: 'AA', address: 'Bole, Addis Ababa', phone: '011-1234567', email: '<EMAIL>', manager_name: '<PERSON><PERSON><PERSON>' },
  { name: 'Adama Center', code: 'ADM', region_code: 'OR', address: 'Adama, Oromia', phone: '022-7654321', email: '<EMAIL>', manager_name: '<PERSON><PERSON><PERSON> Bekele' }
];

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    // Insert regions
    for (const r of regions) {
      await connection.execute(
        'INSERT INTO app_regions2 (name, code, population, area_km2) VALUES (?, ?, ?, ?)',
        [r.name, r.code, r.population, r.area_km2]
      );
    }
    // Get region ids
    const [regionRows] = await connection.execute('SELECT id, code FROM app_regions2');
    const regionMap = {};
    for (const row of regionRows) regionMap[row.code] = row.id;
    // Insert service centers
    for (const sc of serviceCenters) {
      await connection.execute(
        'INSERT INTO app_service_centers2 (name, code, region_id, address, phone, email, manager_name) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [sc.name, sc.code, regionMap[sc.region_code], sc.address, sc.phone, sc.email, sc.manager_name]
      );
    }
    console.log('✅ Seeded app_regions2 and app_service_centers2');
  } catch (err) {
    console.error('❌ Error seeding tables:', err);
  } finally {
    await connection.end();
  }
})();
