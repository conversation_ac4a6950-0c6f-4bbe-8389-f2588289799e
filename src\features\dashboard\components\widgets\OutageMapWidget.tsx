"use client"

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Badge } from '@/src/components/ui/badge'
import { MapPin, Zap, AlertTriangle, Users } from 'lucide-react'

interface OutageData {
  id: string
  location: string
  coordinates: [number, number]
  affectedTransformers: number
  affectedCustomers: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  startTime: string
  estimatedRestoration: string
  status: 'active' | 'investigating' | 'repairing' | 'resolved'
}

export default function OutageMapWidget() {
  const [outages, setOutages] = useState<OutageData[]>([
    {
      id: '1',
      location: 'Addis Ababa - Bole District',
      coordinates: [9.0192, 38.7525],
      affectedTransformers: 3,
      affectedCustomers: 1250,
      severity: 'high',
      startTime: '2024-01-15T08:30:00',
      estimatedRestoration: '2024-01-15T14:00:00',
      status: 'repairing'
    },
    {
      id: '2',
      location: 'Oromia - Adama City',
      coordinates: [8.5400, 39.2675],
      affectedTransformers: 1,
      affectedCustomers: 450,
      severity: 'medium',
      startTime: '2024-01-15T10:15:00',
      estimatedRestoration: '2024-01-15T16:30:00',
      status: 'investigating'
    },
    {
      id: '3',
      location: 'Amhara - Bahir Dar',
      coordinates: [11.5942, 37.3615],
      affectedTransformers: 2,
      affectedCustomers: 800,
      severity: 'critical',
      startTime: '2024-01-15T07:45:00',
      estimatedRestoration: '2024-01-15T18:00:00',
      status: 'active'
    }
  ])

  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Fetch outage data
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // In a real implementation, this would fetch from the API
        await new Promise(resolve => setTimeout(resolve, 700))
      } catch (error) {
        console.error('Error fetching outage data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'critical':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-red-100 text-red-800'
      case 'investigating':
        return 'bg-yellow-100 text-yellow-800'
      case 'repairing':
        return 'bg-blue-100 text-blue-800'
      case 'resolved':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatTime = (timeString: string) => {
    const date = new Date(timeString)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const calculateDuration = (startTime: string) => {
    const start = new Date(startTime)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - start.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m`
    } else {
      const hours = Math.floor(diffInMinutes / 60)
      const minutes = diffInMinutes % 60
      return `${hours}h ${minutes}m`
    }
  }

  const totalAffectedCustomers = outages.reduce((sum, outage) => sum + outage.affectedCustomers, 0)
  const totalAffectedTransformers = outages.reduce((sum, outage) => sum + outage.affectedTransformers, 0)

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Outage Map</CardTitle>
          <CardDescription>Loading outage data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-48">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Outage Map
        </CardTitle>
        <CardDescription>
          Current power outages across all regions
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Summary Stats */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{outages.length}</div>
            <div className="text-sm text-muted-foreground">Active Outages</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{totalAffectedCustomers.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Affected Customers</div>
          </div>
        </div>

        {/* Map Placeholder */}
        <div className="bg-gray-100 rounded-lg h-48 flex items-center justify-center mb-6">
          <div className="text-center">
            <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">Interactive map would be displayed here</p>
            <p className="text-sm text-gray-400">Showing {outages.length} outage locations</p>
          </div>
        </div>

        {/* Outage List */}
        <div className="space-y-3">
          {outages.map((outage) => (
            <div key={outage.id} className="border rounded-lg p-3">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h4 className="font-medium">{outage.location}</h4>
                  <p className="text-sm text-muted-foreground">
                    Duration: {calculateDuration(outage.startTime)}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Badge variant="outline" className={getSeverityColor(outage.severity)}>
                    {outage.severity}
                  </Badge>
                  <Badge variant="outline" className={getStatusColor(outage.status)}>
                    {outage.status}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-muted-foreground" />
                  <span>{outage.affectedTransformers} transformers</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{outage.affectedCustomers.toLocaleString()} customers</span>
                </div>
              </div>

              <div className="mt-2 text-xs text-muted-foreground">
                <div>Started: {formatTime(outage.startTime)}</div>
                <div>Est. Restoration: {formatTime(outage.estimatedRestoration)}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Status Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-4 gap-2 text-center">
            <div>
              <div className="text-lg font-semibold text-red-600">
                {outages.filter(o => o.status === 'active').length}
              </div>
              <div className="text-xs text-muted-foreground">Active</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-yellow-600">
                {outages.filter(o => o.status === 'investigating').length}
              </div>
              <div className="text-xs text-muted-foreground">Investigating</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-blue-600">
                {outages.filter(o => o.status === 'repairing').length}
              </div>
              <div className="text-xs text-muted-foreground">Repairing</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-green-600">
                {outages.filter(o => o.status === 'resolved').length}
              </div>
              <div className="text-xs text-muted-foreground">Resolved</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
