/**
 * Clean Transformers API Route
 * Simplified API with proper error handling and validation
 */

import { NextRequest, NextResponse } from 'next/server'
import { getTransformers, getDashboardSummary } from '@/src/services/transformer.service'
import { FilterOptions } from '@/src/types'

/**
 * GET /api/transformers
 * Fetch transformers with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse pagination parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100) // Max 100 items
    
    // Parse filter parameters
    const filters: FilterOptions = {
      regions: searchParams.get('regions')?.split(',').filter(Boolean),
      serviceCenters: searchParams.get('serviceCenters')?.split(',').filter(Boolean),
      types: searchParams.get('types')?.split(',').filter(Boolean) as any,
      statuses: searchParams.get('statuses')?.split(',').filter(Boolean) as any,
      manufacturers: searchParams.get('manufacturers')?.split(',').filter(Boolean),
      criticalities: searchParams.get('criticalities')?.split(',').filter(Boolean) as any,
      customerTypes: searchParams.get('customerTypes')?.split(',').filter(Boolean) as any,
      search: searchParams.get('search') || undefined,
    }
    
    // Parse range filters
    const capacityMin = parseFloat(searchParams.get('capacityMin') || '0')
    const capacityMax = parseFloat(searchParams.get('capacityMax') || '2000')
    if (capacityMin >= 0 && capacityMax > capacityMin) {
      filters.capacityRange = [capacityMin, capacityMax]
    }
    
    const efficiencyMin = parseFloat(searchParams.get('efficiencyMin') || '0')
    const efficiencyMax = parseFloat(searchParams.get('efficiencyMax') || '100')
    if (efficiencyMin >= 0 && efficiencyMax > efficiencyMin) {
      filters.efficiencyRange = [efficiencyMin, efficiencyMax]
    }
    
    const temperatureMin = parseFloat(searchParams.get('temperatureMin') || '0')
    const temperatureMax = parseFloat(searchParams.get('temperatureMax') || '100')
    if (temperatureMin >= 0 && temperatureMax > temperatureMin) {
      filters.temperatureRange = [temperatureMin, temperatureMax]
    }
    
    const loadFactorMin = parseFloat(searchParams.get('loadFactorMin') || '0')
    const loadFactorMax = parseFloat(searchParams.get('loadFactorMax') || '100')
    if (loadFactorMin >= 0 && loadFactorMax > loadFactorMin) {
      filters.loadFactorRange = [loadFactorMin, loadFactorMax]
    }
    
    const assetValueMin = parseFloat(searchParams.get('assetValueMin') || '0')
    const assetValueMax = parseFloat(searchParams.get('assetValueMax') || '500000')
    if (assetValueMin >= 0 && assetValueMax > assetValueMin) {
      filters.assetValueRange = [assetValueMin, assetValueMax]
    }
    
    // Parse date filters
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    if (dateFrom && dateTo) {
      filters.dateRange = [new Date(dateFrom), new Date(dateTo)]
    }
    
    // Fetch data using service layer
    const result = await getTransformers(filters, page, limit)
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error('Error in transformers API:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch transformers',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/transformers
 * Create a new transformer (if needed)
 */
export async function POST(request: NextRequest) {
  try {
    // Implementation for creating transformers
    return NextResponse.json(
      { 
        success: false, 
        error: 'Not implemented',
        message: 'Transformer creation not yet implemented'
      },
      { status: 501 }
    )
  } catch (error) {
    console.error('Error creating transformer:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create transformer',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
