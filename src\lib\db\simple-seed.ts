import mysql from 'mysql2/promise'

// Database configuration
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: 'dtms_eeu_db',
  multipleStatements: true
}

export async function simpleSeedDatabase() {
  let connection: mysql.Connection | null = null

  try {
    console.log('🔄 Connecting to database for seeding...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Verify tables exist before seeding
    console.log('🔍 Verifying tables exist...')
    const [tables] = await connection.query('SHOW TABLES')
    const tableList = (tables as any[]).map(row => Object.values(row)[0])
    console.log('📋 Available tables:', tableList)

    const requiredTables = ['app_regions', 'app_service_centers', 'app_users', 'app_transformers']
    const missingTables = requiredTables.filter(table => !tableList.includes(table))

    if (missingTables.length > 0) {
      throw new Error(`Missing required tables: ${missingTables.join(', ')}`)
    }
    console.log('✅ All required tables exist')

    // Check if data already exists
    const [existingRegions] = await connection.query('SELECT COUNT(*) as count FROM app_regions')
    const regionCount = (existingRegions as any)[0].count

    if (regionCount > 0) {
      console.log('⚠️ Data already exists, skipping seed...')
      return {
        success: true,
        message: 'Database already contains data'
      }
    }

    console.log('🔄 Seeding Ethiopian regions...')

    // Insert Ethiopian regions
    const regions = [
      { name: 'Addis Ababa', code: 'AA', population: 3500000, area_km2: 527.0 },
      { name: 'Oromia', code: 'OR', population: 37000000, area_km2: 353006.81 },
      { name: 'Amhara', code: 'AM', population: 21000000, area_km2: 154708.96 },
      { name: 'SNNPR', code: 'SN', population: 20000000, area_km2: 105887.18 },
      { name: 'Tigray', code: 'TI', population: 5500000, area_km2: 50078.64 },
      { name: 'Somali', code: 'SO', population: 5500000, area_km2: 279252.0 },
      { name: 'Afar', code: 'AF', population: 1800000, area_km2: 96707.0 },
      { name: 'Benishangul-Gumuz', code: 'BG', population: 1100000, area_km2: 50699.0 },
      { name: 'Gambela', code: 'GA', population: 435000, area_km2: 29782.82 },
      { name: 'Harari', code: 'HA', population: 250000, area_km2: 311.25 },
      { name: 'Dire Dawa', code: 'DD', population: 500000, area_km2: 1213.20 }
    ]

    for (const region of regions) {
      await connection.query(
        'INSERT INTO app_regions (name, code, population, area_km2) VALUES (?, ?, ?, ?)',
        [region.name, region.code, region.population, region.area_km2]
      )
    }
    console.log('✅ Regions seeded successfully')

    console.log('🔄 Seeding service centers...')

    // Insert service centers
    const serviceCenters = [
      { name: 'Addis Ababa Central', code: 'AAC001', region_id: 1, address: 'Bole Road, Addis Ababa', phone: '+251-11-123-4567', email: '<EMAIL>', manager_name: 'Alemayehu Tadesse' },
      { name: 'Adama Service Center', code: 'OR001', region_id: 2, address: 'Adama, Oromia', phone: '+251-22-123-4567', email: '<EMAIL>', manager_name: 'Meron Bekele' },
      { name: 'Bahir Dar Service Center', code: 'AM001', region_id: 3, address: 'Bahir Dar, Amhara', phone: '+251-58-123-4567', email: '<EMAIL>', manager_name: 'Dawit Haile' },
      { name: 'Hawassa Service Center', code: 'SN001', region_id: 4, address: 'Hawassa, SNNPR', phone: '+251-46-123-4567', email: '<EMAIL>', manager_name: 'Tigist Worku' },
      { name: 'Mekelle Service Center', code: 'TI001', region_id: 5, address: 'Mekelle, Tigray', phone: '+251-34-123-4567', email: '<EMAIL>', manager_name: 'Gebrehiwot Tekle' },
      { name: 'Jijiga Service Center', code: 'SO001', region_id: 6, address: 'Jijiga, Somali', phone: '+251-25-123-4567', email: '<EMAIL>', manager_name: 'Ahmed Hassan' }
    ]

    for (const center of serviceCenters) {
      await connection.query(
        'INSERT INTO app_service_centers (name, code, region_id, address, phone, email, manager_name) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [center.name, center.code, center.region_id, center.address, center.phone, center.email, center.manager_name]
      )
    }
    console.log('✅ Service centers seeded successfully')

    console.log('🔄 Seeding users...')

    // Insert users
    const users = [
      { username: 'alemayehu.tadesse', email: '<EMAIL>', first_name: 'Alemayehu', last_name: 'Tadesse', phone: '+251-911-123456', role: 'service_center_manager', region_id: 1, service_center_id: 1, is_active: true },
      { username: 'meron.bekele', email: '<EMAIL>', first_name: 'Meron', last_name: 'Bekele', phone: '+251-911-234567', role: 'field_technician', region_id: 2, service_center_id: 2, is_active: true },
      { username: 'dawit.haile', email: '<EMAIL>', first_name: 'Dawit', last_name: 'Haile', phone: '+251-911-345678', role: 'field_technician', region_id: 3, service_center_id: 3, is_active: false },
      { username: 'tigist.worku', email: '<EMAIL>', first_name: 'Tigist', last_name: 'Worku', phone: '+251-911-456789', role: 'regional_maintenance_engineer', region_id: 4, service_center_id: 4, is_active: true },
      { username: 'gebrehiwot.tekle', email: '<EMAIL>', first_name: 'Gebrehiwot', last_name: 'Tekle', phone: '+251-911-567890', role: 'field_technician', region_id: 5, service_center_id: 5, is_active: true },
      { username: 'ahmed.hassan', email: '<EMAIL>', first_name: 'Ahmed', last_name: 'Hassan', phone: '+251-911-678901', role: 'service_center_manager', region_id: 6, service_center_id: 6, is_active: true }
    ]

    for (const user of users) {
      await connection.query(
        'INSERT INTO app_users (username, email, first_name, last_name, phone, role, region_id, service_center_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [user.username, user.email, user.first_name, user.last_name, user.phone, user.role, user.region_id, user.service_center_id, user.is_active]
      )
    }
    console.log('✅ Users seeded successfully')

    console.log('🔄 Seeding transformers...')

    // Insert sample transformers
    const transformers = [
      { serial_number: 'EEU-T-001', name: 'Bole Transformer 1', type: 'distribution', capacity_kva: 500.0, voltage_primary: 15000.0, voltage_secondary: 400.0, manufacturer: 'ABB', model: 'DT-500', year_manufactured: 2020, installation_date: '2020-03-15', location_name: 'Bole District, Addis Ababa', latitude: 8.9806, longitude: 38.7578, region_id: 1, service_center_id: 1, status: 'operational', efficiency_rating: 96.5, load_factor: 85.2, temperature: 45.5, oil_level: 95.0, last_maintenance: '2024-01-01', next_maintenance: '2024-07-01' },
      { serial_number: 'EEU-T-002', name: 'Adama Main Transformer', type: 'power', capacity_kva: 2500.0, voltage_primary: 33000.0, voltage_secondary: 15000.0, manufacturer: 'Siemens', model: 'PT-2500', year_manufactured: 2019, installation_date: '2019-08-20', location_name: 'Adama Industrial Zone', latitude: 8.5400, longitude: 39.2675, region_id: 2, service_center_id: 2, status: 'warning', efficiency_rating: 94.2, load_factor: 92.1, temperature: 65.2, oil_level: 88.0, last_maintenance: '2023-12-15', next_maintenance: '2024-06-15' },
      { serial_number: 'EEU-T-003', name: 'Bahir Dar University Transformer', type: 'distribution', capacity_kva: 1000.0, voltage_primary: 15000.0, voltage_secondary: 400.0, manufacturer: 'Schneider Electric', model: 'DT-1000', year_manufactured: 2021, installation_date: '2021-05-10', location_name: 'Bahir Dar University Campus', latitude: 11.5942, longitude: 37.3914, region_id: 3, service_center_id: 3, status: 'maintenance', efficiency_rating: 95.8, load_factor: 78.5, temperature: 42.0, oil_level: 92.0, last_maintenance: '2024-01-10', next_maintenance: '2024-07-10' }
    ]

    for (const transformer of transformers) {
      await connection.query(
        `INSERT INTO app_transformers (serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
         manufacturer, model, year_manufactured, installation_date, location_name, latitude, longitude,
         region_id, service_center_id, status, efficiency_rating, load_factor, temperature, oil_level,
         last_maintenance, next_maintenance) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [transformer.serial_number, transformer.name, transformer.type, transformer.capacity_kva,
         transformer.voltage_primary, transformer.voltage_secondary, transformer.manufacturer,
         transformer.model, transformer.year_manufactured, transformer.installation_date,
         transformer.location_name, transformer.latitude, transformer.longitude, transformer.region_id,
         transformer.service_center_id, transformer.status, transformer.efficiency_rating,
         transformer.load_factor, transformer.temperature, transformer.oil_level,
         transformer.last_maintenance, transformer.next_maintenance]
      )
    }
    console.log('✅ Transformers seeded successfully')

    return {
      success: true,
      message: 'Database seeded successfully with Ethiopian transformer data'
    }

  } catch (error) {
    console.error('❌ Database seeding failed:', error)
    return {
      success: false,
      message: `Database seeding failed: ${error}`,
      error
    }
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}
