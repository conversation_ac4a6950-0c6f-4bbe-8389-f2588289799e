import { NextRequest, NextResponse } from 'next/server';
import { migrateAll } from '../../../../scripts/migrate-all';

/**
 * API route to run all database migrations
 * 
 * This is a protected route that should only be accessible to administrators.
 * In a production environment, this should be secured with proper authentication.
 */
export async function POST(req: NextRequest) {
  try {
    // In a real implementation, check for admin authentication
    // For now, we'll use a simple API key check
    const authHeader = req.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const apiKey = authHeader.split(' ')[1];
    
    // Simple API key check (in production, use a secure method)
    if (apiKey !== 'eeu-admin-migration-key') {
      return NextResponse.json(
        { error: 'Invalid API key' },
        { status: 401 }
      );
    }
    
    // Run all migrations
    await migrateAll();
    
    return NextResponse.json(
      { success: true, message: 'All migrations completed successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error running migrations:', error);
    
    return NextResponse.json(
      { error: 'Failed to run migrations', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
