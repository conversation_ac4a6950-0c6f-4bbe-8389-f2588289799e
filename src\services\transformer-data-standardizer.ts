/**
 * Transformer Data Standardizer
 * Ensures consistent data format across all components (dashboard, list, detail, map)
 */

export interface StandardizedTransformer {
  id: string
  serialNumber: string
  name: string
  manufacturer: string
  model: string
  type: string
  capacity: number
  voltagePrimary: number
  voltageSecondary: number
  status: 'operational' | 'warning' | 'maintenance' | 'critical' | 'offline' | 'burnt'
  installationDate: string
  location: {
    name: string
    region: string
    district: string
    subCity: string
    kebele: string
    address: string
    coordinates: {
      lat: number
      lng: number
    }
  }
  lastMaintenance?: string
  nextMaintenance?: string
  temperature?: number
  loadFactor?: number
  healthIndex?: number
}

export class TransformerDataStandardizer {
  /**
   * Standardize transformer data from any source format
   */
  static standardize(rawData: any): StandardizedTransformer {
    const id = this.extractId(rawData)
    const serialNumber = this.extractSerialNumber(rawData)
    
    return {
      id,
      serialNumber,
      name: this.extractName(rawData),
      manufacturer: this.extractManufacturer(rawData),
      model: this.extractModel(rawData),
      type: this.extractType(rawData),
      capacity: this.extractCapacity(rawData),
      voltagePrimary: this.extractVoltagePrimary(rawData),
      voltageSecondary: this.extractVoltageSecondary(rawData),
      status: this.extractStatus(rawData),
      installationDate: this.extractInstallationDate(rawData),
      location: this.extractLocation(rawData),
      lastMaintenance: this.extractLastMaintenance(rawData),
      nextMaintenance: this.extractNextMaintenance(rawData),
      temperature: this.extractTemperature(rawData),
      loadFactor: this.extractLoadFactor(rawData),
      healthIndex: this.extractHealthIndex(rawData)
    }
  }

  /**
   * Generate consistent transformer code
   */
  static generateTransformerCode(id: string, serialNumber?: string): string {
    if (serialNumber && !serialNumber.startsWith('DT-')) {
      return `DT-${serialNumber}`
    }
    return serialNumber || `DT-${id.toString().padStart(6, '0')}`
  }

  /**
   * Standardize status values
   */
  static standardizeStatus(status: string): StandardizedTransformer['status'] {
    const statusLower = status?.toLowerCase() || 'operational'
    
    switch (statusLower) {
      case 'operational':
      case 'active':
      case 'online':
        return 'operational'
      case 'warning':
      case 'alert':
        return 'warning'
      case 'maintenance':
      case 'under_maintenance':
        return 'maintenance'
      case 'critical':
      case 'failed':
        return 'critical'
      case 'offline':
      case 'inactive':
        return 'offline'
      case 'burnt':
      case 'burned':
        return 'burnt'
      default:
        return 'operational'
    }
  }

  /**
   * Extract and standardize location data
   */
  private static extractLocation(rawData: any): StandardizedTransformer['location'] {
    // Handle different location formats
    const location = rawData.location || {}
    const locationName = rawData.location_name || location.name || location.address || 'Unknown Location'
    
    // Extract coordinates
    const lat = this.safeParseFloat(
      rawData.latitude || 
      rawData.lat || 
      location.latitude || 
      location.coordinates?.lat || 
      location.coordinates?.latitude ||
      9.005401 // Default Addis Ababa
    )
    
    const lng = this.safeParseFloat(
      rawData.longitude || 
      rawData.lng || 
      location.longitude || 
      location.coordinates?.lng || 
      location.coordinates?.longitude ||
      38.763611 // Default Addis Ababa
    )

    // Extract region information
    const region = rawData.region_name || location.region || 'Addis Ababa'
    const district = this.extractDistrict(locationName, region)
    
    return {
      name: locationName,
      region,
      district,
      subCity: location.subCity || 'Bole',
      kebele: location.kebele || '05',
      address: location.address || locationName,
      coordinates: { lat, lng }
    }
  }

  // Helper extraction methods
  private static extractId(rawData: any): string {
    return rawData.id?.toString() || rawData._id?.toString() || 'unknown'
  }

  private static extractSerialNumber(rawData: any): string {
    const serial = rawData.serialNumber || rawData.serial_number || rawData.code
    return this.generateTransformerCode(this.extractId(rawData), serial)
  }

  private static extractName(rawData: any): string {
    return rawData.name || `${this.extractManufacturer(rawData)} ${this.extractModel(rawData)} Transformer`
  }

  private static extractManufacturer(rawData: any): string {
    return rawData.manufacturer || 'Unknown'
  }

  private static extractModel(rawData: any): string {
    return rawData.model || 'Unknown'
  }

  private static extractType(rawData: any): string {
    return rawData.type || 'Distribution'
  }

  private static extractCapacity(rawData: any): number {
    return this.safeParseFloat(
      rawData.capacity || 
      rawData.capacity_kva || 
      rawData.ratingKVA || 
      rawData.kvaRating ||
      500
    )
  }

  private static extractVoltagePrimary(rawData: any): number {
    return this.safeParseFloat(
      rawData.voltagePrimary || 
      rawData.voltage_primary || 
      rawData.voltage?.primary ||
      15000
    )
  }

  private static extractVoltageSecondary(rawData: any): number {
    return this.safeParseFloat(
      rawData.voltageSecondary || 
      rawData.voltage_secondary || 
      rawData.voltage?.secondary ||
      400
    )
  }

  private static extractStatus(rawData: any): StandardizedTransformer['status'] {
    return this.standardizeStatus(rawData.status || 'operational')
  }

  private static extractInstallationDate(rawData: any): string {
    return rawData.installationDate || rawData.installation_date || '2023-01-01'
  }

  private static extractLastMaintenance(rawData: any): string | undefined {
    return rawData.lastMaintenance || rawData.last_maintenance_date || rawData.lastMaintenanceDate
  }

  private static extractNextMaintenance(rawData: any): string | undefined {
    return rawData.nextMaintenance || rawData.next_maintenance_date || rawData.nextMaintenanceDate
  }

  private static extractTemperature(rawData: any): number | undefined {
    const temp = rawData.temperature || rawData.metrics?.temperature
    return temp ? this.safeParseFloat(temp) : undefined
  }

  private static extractLoadFactor(rawData: any): number | undefined {
    const load = rawData.loadFactor || rawData.load_factor || rawData.metrics?.loadPercentage
    return load ? this.safeParseFloat(load) : undefined
  }

  private static extractHealthIndex(rawData: any): number | undefined {
    const health = rawData.healthIndex || rawData.health_index || rawData.metrics?.healthIndex
    return health ? this.safeParseFloat(health) : undefined
  }

  private static extractDistrict(locationName: string, region: string): string {
    // Extract district from location name or use default based on region
    if (locationName.includes('North')) return 'North District'
    if (locationName.includes('South')) return 'South District'
    if (locationName.includes('East')) return 'East District'
    if (locationName.includes('West')) return 'West District'
    if (locationName.includes('Central')) return 'Central District'
    
    // Default based on region
    switch (region) {
      case 'Addis Ababa': return 'North District'
      case 'Oromia': return 'Central District'
      case 'Amhara': return 'North District'
      default: return 'Central District'
    }
  }

  private static safeParseFloat(value: any, defaultValue: number = 0): number {
    if (typeof value === 'number') return value
    if (typeof value === 'string') {
      const parsed = parseFloat(value)
      return isNaN(parsed) ? defaultValue : parsed
    }
    return defaultValue
  }

  /**
   * Format transformer for display in different contexts
   */
  static formatForDisplay(transformer: StandardizedTransformer, context: 'dashboard' | 'list' | 'detail' | 'map') {
    const base = {
      id: transformer.id,
      serialNumber: transformer.serialNumber,
      name: transformer.name,
      status: transformer.status,
      location: transformer.location.name,
      capacity: transformer.capacity
    }

    switch (context) {
      case 'dashboard':
        return {
          ...base,
          region: transformer.location.region,
          lastMaintenance: transformer.lastMaintenance,
          healthIndex: transformer.healthIndex || 85
        }
      
      case 'list':
        return {
          ...base,
          code: transformer.serialNumber,
          kvaRating: transformer.capacity,
          installation: transformer.installationDate,
          district: transformer.location.district
        }
      
      case 'detail':
        return transformer
      
      case 'map':
        return {
          id: transformer.id,
          title: `${transformer.manufacturer} ${transformer.model}`,
          description: `${transformer.capacity} kVA, ${transformer.location.region}`,
          latitude: transformer.location.coordinates.lat,
          longitude: transformer.location.coordinates.lng,
          status: transformer.status,
          data: transformer
        }
      
      default:
        return transformer
    }
  }
}
