/**
 * Simple Data Addition for EEU DTMS
 * Adds sample data to existing tables without conflicts
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function addSampleDataSimple() {
  let connection;
  
  try {
    console.log('🌱 ADDING SAMPLE DATA TO EEU DTMS');
    console.log('=' .repeat(50));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // Check existing data first
    const [existingTransformers] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    const [existingRegions] = await connection.execute('SELECT COUNT(*) as count FROM app_regions');
    
    console.log(`📊 Current data: ${existingTransformers[0].count} transformers, ${existingRegions[0].count} regions`);
    
    // Add more transformers if we have less than 8
    if (existingTransformers[0].count < 8) {
      console.log('\n⚡ Adding more transformers...');
      
      const newTransformers = [
        { serial_number: 'EEU-AA-004', name: 'Merkato Distribution Transformer', type: 'distribution', capacity_kva: 630, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2020, installation_date: '2020-03-15', location_name: 'Merkato Commercial Area', latitude: 9.0084, longitude: 38.7575, region_id: 1, status: 'operational', efficiency_rating: 97.5, load_factor: 78.0, temperature: 66.0, oil_level: 91.0 },
        { serial_number: 'EEU-OR-003', name: 'Nekemte Distribution Transformer', type: 'distribution', capacity_kva: 400, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Schneider Electric', model: 'Trihal', year_manufactured: 2019, installation_date: '2019-11-20', location_name: 'Nekemte City Center', latitude: 9.0833, longitude: 36.5500, region_id: 2, status: 'warning', efficiency_rating: 96.2, load_factor: 87.0, temperature: 73.0, oil_level: 83.0 },
        { serial_number: 'EEU-AM-003', name: 'Debre Markos Transformer', type: 'distribution', capacity_kva: 315, voltage_primary: 33, voltage_secondary: 0.4, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2021, installation_date: '2021-05-10', location_name: 'Debre Markos Town', latitude: 10.3500, longitude: 37.7333, region_id: 3, status: 'operational', efficiency_rating: 98.1, load_factor: 69.0, temperature: 61.0, oil_level: 94.0 }
      ];
      
      for (const transformer of newTransformers) {
        try {
          await connection.execute(`
            INSERT INTO app_transformers (
              serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
              manufacturer, model, year_manufactured, installation_date, location_name,
              latitude, longitude, region_id, status, efficiency_rating, load_factor,
              temperature, oil_level
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            transformer.serial_number, transformer.name, transformer.type, transformer.capacity_kva,
            transformer.voltage_primary, transformer.voltage_secondary, transformer.manufacturer,
            transformer.model, transformer.year_manufactured, transformer.installation_date,
            transformer.location_name, transformer.latitude, transformer.longitude,
            transformer.region_id, transformer.status, transformer.efficiency_rating,
            transformer.load_factor, transformer.temperature, transformer.oil_level
          ]);
          console.log(`  ✅ Added ${transformer.name}`);
        } catch (error) {
          console.log(`  ⚠️  Skipped ${transformer.name} (already exists or constraint issue)`);
        }
      }
    }
    
    // Add maintenance schedules
    console.log('\n🔧 Adding maintenance schedules...');
    const maintenanceSchedules = [
      { transformer_id: 1, type: 'routine', title: 'Monthly Visual Inspection', description: 'Regular monthly visual inspection and basic checks', scheduled_date: '2024-12-30', estimated_duration: 2, priority: 'medium', status: 'scheduled' },
      { transformer_id: 2, type: 'preventive', title: 'Quarterly Maintenance', description: 'Comprehensive quarterly electrical testing and oil analysis', scheduled_date: '2024-12-25', estimated_duration: 8, priority: 'high', status: 'in_progress' },
      { transformer_id: 1, type: 'routine', title: 'Weekly Check', description: 'Weekly routine inspection', scheduled_date: '2024-12-20', estimated_duration: 1, priority: 'low', status: 'completed' },
      { transformer_id: 2, type: 'corrective', title: 'Temperature Issue Investigation', description: 'Investigate high temperature readings', scheduled_date: '2024-12-15', estimated_duration: 4, priority: 'high', status: 'scheduled' }
    ];
    
    for (const schedule of maintenanceSchedules) {
      try {
        await connection.execute(`
          INSERT INTO app_maintenance_schedules (
            transformer_id, type, title, description, scheduled_date, estimated_duration, priority, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          schedule.transformer_id, schedule.type, schedule.title, schedule.description,
          schedule.scheduled_date, schedule.estimated_duration, schedule.priority, schedule.status
        ]);
        console.log(`  ✅ Added maintenance: ${schedule.title}`);
      } catch (error) {
        console.log(`  ⚠️  Skipped maintenance: ${schedule.title} (constraint issue)`);
      }
    }
    
    // Add alerts
    console.log('\n🚨 Adding alerts...');
    const alerts = [
      { transformer_id: 2, title: 'High Temperature Alert', description: 'Transformer temperature has exceeded 70°C. Immediate inspection recommended.', severity: 'high', type: 'temperature', priority: 'high', is_resolved: false },
      { transformer_id: 2, title: 'Overload Warning', description: 'Transformer is operating at high capacity, monitoring required.', severity: 'medium', type: 'load', priority: 'medium', is_resolved: false },
      { transformer_id: 1, title: 'Routine Maintenance Due', description: 'Scheduled maintenance approaching', severity: 'low', type: 'maintenance', priority: 'low', is_resolved: false },
      { transformer_id: 1, title: 'Communication Test', description: 'Testing alert system', severity: 'low', type: 'communication', priority: 'low', is_resolved: true }
    ];
    
    for (const alert of alerts) {
      try {
        await connection.execute(`
          INSERT INTO app_alerts (
            transformer_id, title, description, severity, type, priority, is_resolved
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          alert.transformer_id, alert.title, alert.description, alert.severity,
          alert.type, alert.priority, alert.is_resolved
        ]);
        console.log(`  ✅ Added alert: ${alert.title}`);
      } catch (error) {
        console.log(`  ⚠️  Skipped alert: ${alert.title} (constraint issue)`);
      }
    }
    
    // Check final counts
    const [finalTransformers] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    const [finalMaintenance] = await connection.execute('SELECT COUNT(*) as count FROM app_maintenance_schedules');
    const [finalAlerts] = await connection.execute('SELECT COUNT(*) as count FROM app_alerts');
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎉 SAMPLE DATA ADDITION COMPLETED!');
    console.log('=' .repeat(50));
    
    console.log('\n📊 FINAL DATABASE SUMMARY:');
    console.log(`  ⚡ Transformers: ${finalTransformers[0].count}`);
    console.log(`  🔧 Maintenance Schedules: ${finalMaintenance[0].count}`);
    console.log(`  🚨 Alerts: ${finalAlerts[0].count}`);
    console.log(`  🗺️  Regions: ${existingRegions[0].count}`);
    
    // Show transformer status distribution
    const [statusDistribution] = await connection.execute(`
      SELECT status, COUNT(*) as count 
      FROM app_transformers 
      GROUP BY status 
      ORDER BY count DESC
    `);
    
    console.log('\n📈 TRANSFORMER STATUS DISTRIBUTION:');
    statusDistribution.forEach(status => {
      console.log(`  • ${status.status}: ${status.count} transformers`);
    });
    
    // Show alert severity distribution
    const [alertSeverity] = await connection.execute(`
      SELECT severity, COUNT(*) as count 
      FROM app_alerts 
      WHERE is_resolved = 0 OR is_resolved IS NULL
      GROUP BY severity 
      ORDER BY 
        CASE severity 
          WHEN 'critical' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'medium' THEN 3 
          WHEN 'low' THEN 4 
        END
    `);
    
    console.log('\n🚨 ACTIVE ALERTS BY SEVERITY:');
    if (alertSeverity.length > 0) {
      alertSeverity.forEach(alert => {
        console.log(`  • ${alert.severity}: ${alert.count} alerts`);
      });
    } else {
      console.log('  • No active alerts');
    }
    
    console.log('\n🎯 DASHBOARD READY FOR:');
    console.log('  • Real-time transformer monitoring');
    console.log('  • Maintenance scheduling and tracking');
    console.log('  • Alert management and escalation');
    console.log('  • Performance analytics and reporting');
    
    console.log('\n🌟 Your EEU DTMS database is now populated with sample data!');
    console.log('🔗 Access dashboard at: http://localhost:3002');
    console.log('👤 Login: <EMAIL> / password123');
    
  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { addSampleDataSimple };

// Run if called directly
if (require.main === module) {
  addSampleDataSimple();
}
