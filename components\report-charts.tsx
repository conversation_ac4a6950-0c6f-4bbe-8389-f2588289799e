"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/src/components/ui/tabs"
import { Button } from "@/src/components/ui/button"
import { Download, Maximize2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Activity, Grid3X3 } from "lucide-react"
import { useState } from "react"
import { useReports, ChartType } from "@/contexts/reports-context"

// Enhanced sample data for comprehensive transformer reports
const performanceData = [
  {
    date: "Jan 2023",
    "Efficiency": 92.4,
    "Load Factor": 78.2,
    "Power Quality": 95.1,
  },
  {
    date: "Feb 2023",
    "Efficiency": 91.7,
    "Load Factor": 76.8,
    "Power Quality": 94.5,
  },
  {
    date: "Mar 2023",
    "Efficiency": 93.1,
    "Load Factor": 79.5,
    "Power Quality": 96.2,
  },
  {
    date: "Apr 2023",
    "Efficiency": 94.2,
    "Load Factor": 81.3,
    "Power Quality": 97.0,
  },
  {
    date: "May 2023",
    "Efficiency": 93.8,
    "Load Factor": 80.7,
    "Power Quality": 96.5,
  },
  {
    date: "Jun 2023",
    "Efficiency": 92.9,
    "Load Factor": 79.1,
    "Power Quality": 95.8,
  },
  {
    date: "Jul 2023",
    "Efficiency": 91.5,
    "Load Factor": 77.2,
    "Power Quality": 94.3,
  },
  {
    date: "Aug 2023",
    "Efficiency": 92.3,
    "Load Factor": 78.6,
    "Power Quality": 95.2,
  },
  {
    date: "Sep 2023",
    "Efficiency": 93.6,
    "Load Factor": 80.4,
    "Power Quality": 96.7,
  },
  {
    date: "Oct 2023",
    "Efficiency": 94.5,
    "Load Factor": 82.1,
    "Power Quality": 97.3,
  },
  {
    date: "Nov 2023",
    "Efficiency": 94.1,
    "Load Factor": 81.5,
    "Power Quality": 96.9,
  },
  {
    date: "Dec 2023",
    "Efficiency": 93.2,
    "Load Factor": 79.8,
    "Power Quality": 95.6,
  },
]

// Comprehensive transformer distribution data
const voltageDistributionData = [
  { name: "11kV", count: 485, percentage: 38.9 },
  { name: "15kV", count: 312, percentage: 25.0 },
  { name: "33kV", count: 198, percentage: 15.9 },
  { name: "66kV", count: 156, percentage: 12.5 },
  { name: "132kV", count: 97, percentage: 7.7 }
]

const kvaRatingData = [
  { name: "25-100 kVA", count: 425, percentage: 34.1 },
  { name: "100-315 kVA", count: 368, percentage: 29.5 },
  { name: "315-630 kVA", count: 245, percentage: 19.6 },
  { name: "630-1000 kVA", count: 132, percentage: 10.6 },
  { name: "1000+ kVA", count: 78, percentage: 6.2 }
]

const customerTypeData = [
  { name: "Residential", count: 542, percentage: 43.4 },
  { name: "Commercial", count: 298, percentage: 23.9 },
  { name: "Industrial", count: 187, percentage: 15.0 },
  { name: "Government", count: 134, percentage: 10.7 },
  { name: "Healthcare", count: 87, percentage: 7.0 }
]

const regionDistributionData = [
  { name: "Addis Ababa", count: 245, percentage: 19.6 },
  { name: "Oromia", count: 198, percentage: 15.9 },
  { name: "Amhara", count: 178, percentage: 14.3 },
  { name: "SNNPR", count: 156, percentage: 12.5 },
  { name: "Tigray", count: 142, percentage: 11.4 },
  { name: "Somali", count: 124, percentage: 9.9 },
  { name: "Afar", count: 98, percentage: 7.8 },
  { name: "Benishangul", count: 87, percentage: 7.0 },
  { name: "Gambela", count: 20, percentage: 1.6 }
]

const serviceCenterData = [
  { name: "Bole Service Center", region: "Addis Ababa", count: 89, percentage: 7.1 },
  { name: "Kirkos Service Center", region: "Addis Ababa", count: 76, percentage: 6.1 },
  { name: "Bahir Dar Service Center", region: "Amhara", count: 68, percentage: 5.4 },
  { name: "Hawassa Service Center", region: "SNNPR", count: 62, percentage: 5.0 },
  { name: "Mekelle Service Center", region: "Tigray", count: 58, percentage: 4.6 },
  { name: "Dire Dawa Service Center", region: "Dire Dawa", count: 54, percentage: 4.3 },
  { name: "Jimma Service Center", region: "Oromia", count: 51, percentage: 4.1 },
  { name: "Gondar Service Center", region: "Amhara", count: 47, percentage: 3.8 },
  { name: "Adama Service Center", region: "Oromia", count: 43, percentage: 3.4 },
  { name: "Dessie Service Center", region: "Amhara", count: 39, percentage: 3.1 }
]

const feederData = [
  { name: "Bole Feeder 1", serviceCenter: "Bole Service Center", count: 28, voltage: "11kV" },
  { name: "Kirkos Feeder 2", serviceCenter: "Kirkos Service Center", count: 25, voltage: "15kV" },
  { name: "Bahir Dar Feeder 1", serviceCenter: "Bahir Dar Service Center", count: 23, voltage: "33kV" },
  { name: "Hawassa Feeder 3", serviceCenter: "Hawassa Service Center", count: 21, voltage: "11kV" },
  { name: "Mekelle Feeder 1", serviceCenter: "Mekelle Service Center", count: 19, voltage: "66kV" },
  { name: "Dire Dawa Feeder 2", serviceCenter: "Dire Dawa Service Center", count: 18, voltage: "15kV" },
  { name: "Jimma Feeder 1", serviceCenter: "Jimma Service Center", count: 17, voltage: "33kV" },
  { name: "Gondar Feeder 3", serviceCenter: "Gondar Service Center", count: 16, voltage: "11kV" },
  { name: "Adama Feeder 2", serviceCenter: "Adama Service Center", count: 15, voltage: "66kV" },
  { name: "Dessie Feeder 1", serviceCenter: "Dessie Service Center", count: 14, voltage: "15kV" }
]

const substationData = [
  { name: "Bole Substation", region: "Addis Ababa", count: 45, voltage: "132kV", capacity: "150 MVA" },
  { name: "Kirkos Substation", region: "Addis Ababa", count: 38, voltage: "66kV", capacity: "100 MVA" },
  { name: "Bahir Dar Substation", region: "Amhara", count: 35, voltage: "132kV", capacity: "120 MVA" },
  { name: "Hawassa Substation", region: "SNNPR", count: 32, voltage: "66kV", capacity: "80 MVA" },
  { name: "Mekelle Substation", region: "Tigray", count: 29, voltage: "132kV", capacity: "100 MVA" },
  { name: "Dire Dawa Substation", region: "Dire Dawa", count: 27, voltage: "66kV", capacity: "75 MVA" },
  { name: "Jimma Substation", region: "Oromia", count: 25, voltage: "66kV", capacity: "60 MVA" },
  { name: "Gondar Substation", region: "Amhara", count: 23, voltage: "66kV", capacity: "50 MVA" },
  { name: "Adama Substation", region: "Oromia", count: 21, voltage: "132kV", capacity: "90 MVA" },
  { name: "Dessie Substation", region: "Amhara", count: 19, voltage: "66kV", capacity: "45 MVA" }
]

const maintenanceData = [
  {
    date: "Jan 2023",
    "Preventive": 24,
    "Corrective": 8,
    "Predictive": 12,
  },
  {
    date: "Feb 2023",
    "Preventive": 22,
    "Corrective": 10,
    "Predictive": 14,
  },
  {
    date: "Mar 2023",
    "Preventive": 28,
    "Corrective": 6,
    "Predictive": 16,
  },
  {
    date: "Apr 2023",
    "Preventive": 26,
    "Corrective": 7,
    "Predictive": 15,
  },
  {
    date: "May 2023",
    "Preventive": 30,
    "Corrective": 5,
    "Predictive": 18,
  },
  {
    date: "Jun 2023",
    "Preventive": 25,
    "Corrective": 9,
    "Predictive": 14,
  },
  {
    date: "Jul 2023",
    "Preventive": 23,
    "Corrective": 11,
    "Predictive": 13,
  },
  {
    date: "Aug 2023",
    "Preventive": 27,
    "Corrective": 8,
    "Predictive": 16,
  },
  {
    date: "Sep 2023",
    "Preventive": 29,
    "Corrective": 6,
    "Predictive": 17,
  },
  {
    date: "Oct 2023",
    "Preventive": 31,
    "Corrective": 4,
    "Predictive": 19,
  },
  {
    date: "Nov 2023",
    "Preventive": 28,
    "Corrective": 7,
    "Predictive": 16,
  },
  {
    date: "Dec 2023",
    "Preventive": 26,
    "Corrective": 9,
    "Predictive": 15,
  },
]

const failureData = [
  {
    name: "Winding Failure",
    value: 28,
  },
  {
    name: "Bushing Failure",
    value: 22,
  },
  {
    name: "Oil Leakage",
    value: 18,
  },
  {
    name: "Core Failure",
    value: 12,
  },
  {
    name: "Tap Changer Issues",
    value: 10,
  },
  {
    name: "Other",
    value: 10,
  },
]

const regionData = [
  {
    name: "Addis Ababa",
    "Transformers": 245,
  },
  {
    name: "Dire Dawa",
    "Transformers": 178,
  },
  {
    name: "Bahir Dar",
    "Transformers": 156,
  },
  {
    name: "Hawassa",
    "Transformers": 142,
  },
  {
    name: "Mekelle",
    "Transformers": 132,
  },
  {
    name: "Gondar",
    "Transformers": 124,
  },
  {
    name: "Jimma",
    "Transformers": 98,
  },
]

const ageData = [
  {
    name: "0-5 years",
    value: 35,
  },
  {
    name: "6-10 years",
    value: 25,
  },
  {
    name: "11-15 years",
    value: 20,
  },
  {
    name: "16-20 years",
    value: 12,
  },
  {
    name: "21+ years",
    value: 8,
  },
]

interface ChartCardProps {
  title: string
  description: string
  chartType: ChartType
  data: any[]
  index: string
  categories?: string[]
  colors?: string[]
  valueFormatter?: (value: number) => string
}

function ChartCard({
  title,
  description,
  chartType,
  data,
  index,
  categories,
  colors = ["indigo", "cyan", "amber", "emerald", "rose", "slate"],
  valueFormatter = (value) => `${value}`
}: ChartCardProps) {
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = () => {
    setIsRefreshing(true)
    setTimeout(() => setIsRefreshing(false), 1000)
  }

  const renderChart = () => {
    // Since we're not using a chart library, we'll display placeholder icons
    // In a real implementation, you would integrate a chart library like Chart.js, Recharts, or Tremor
    const chartHeight = "h-72 mt-4"

    switch (chartType) {
      case "bar":
        return (
          <div className={`flex flex-col items-center justify-center ${chartHeight} bg-muted/30 rounded-lg`}>
            <BarChart3 className="h-16 w-16 text-primary/50 mb-4" />
            <p className="text-lg font-medium">Bar Chart</p>
            <p className="text-sm text-muted-foreground">Data visualization would appear here</p>
          </div>
        )
      case "line":
        return (
          <div className={`flex flex-col items-center justify-center ${chartHeight} bg-muted/30 rounded-lg`}>
            <LineChart className="h-16 w-16 text-primary/50 mb-4" />
            <p className="text-lg font-medium">Line Chart</p>
            <p className="text-sm text-muted-foreground">Data visualization would appear here</p>
          </div>
        )
      case "area":
        return (
          <div className={`flex flex-col items-center justify-center ${chartHeight} bg-muted/30 rounded-lg`}>
            <Activity className="h-16 w-16 text-primary/50 mb-4" />
            <p className="text-lg font-medium">Area Chart</p>
            <p className="text-sm text-muted-foreground">Data visualization would appear here</p>
          </div>
        )
      case "pie":
        return (
          <div className={`flex flex-col items-center justify-center ${chartHeight} bg-muted/30 rounded-lg`}>
            <PieChart className="h-16 w-16 text-primary/50 mb-4" />
            <p className="text-lg font-medium">Pie Chart</p>
            <p className="text-sm text-muted-foreground">Data visualization would appear here</p>
          </div>
        )
      case "donut":
        return (
          <div className={`flex flex-col items-center justify-center ${chartHeight} bg-muted/30 rounded-lg`}>
            <PieChart className="h-16 w-16 text-primary/50 mb-4" />
            <p className="text-lg font-medium">Donut Chart</p>
            <p className="text-sm text-muted-foreground">Data visualization would appear here</p>
          </div>
        )
      case "heatmap":
        return (
          <div className={`flex flex-col items-center justify-center ${chartHeight} bg-muted/30 rounded-lg`}>
            <Grid3X3 className="h-16 w-16 text-primary/50 mb-4" />
            <p className="text-lg font-medium">Heatmap</p>
            <p className="text-sm text-muted-foreground">Data visualization would appear here</p>
          </div>
        )
      default:
        return (
          <div className={`flex flex-col items-center justify-center ${chartHeight} bg-muted/30 rounded-lg`}>
            <BarChart3 className="h-16 w-16 text-primary/50 mb-4" />
            <p className="text-lg font-medium">Chart</p>
            <p className="text-sm text-muted-foreground">Data visualization would appear here</p>
          </div>
        )
    }
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="icon" onClick={handleRefresh}>
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
            </Button>
            <Button variant="outline" size="icon">
              <Maximize2 className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>{renderChart()}</CardContent>
    </Card>
  )
}

export function PerformanceCharts() {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <ChartCard
        title="Transformer Efficiency Trends"
        description="Monthly efficiency metrics over the past year"
        chartType="line"
        data={performanceData}
        index="date"
        categories={["Efficiency", "Power Quality"]}
        valueFormatter={(value) => `${value}%`}
      />
      <ChartCard
        title="Load Factor Analysis"
        description="Monthly load factor trends"
        chartType="area"
        data={performanceData}
        index="date"
        categories={["Load Factor"]}
        valueFormatter={(value) => `${value}%`}
      />
      <ChartCard
        title="Transformer Distribution by Region"
        description="Number of transformers by region"
        chartType="bar"
        data={regionData}
        index="name"
        categories={["Transformers"]}
      />
      <ChartCard
        title="Transformer Age Distribution"
        description="Percentage of transformers by age group"
        chartType="donut"
        data={ageData}
        index="name"
        valueFormatter={(value) => `${value}%`}
      />
    </div>
  )
}

export function MaintenanceCharts() {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <ChartCard
        title="Maintenance Activities"
        description="Monthly maintenance activities by type"
        chartType="bar"
        data={maintenanceData}
        index="date"
        categories={["Preventive", "Corrective", "Predictive"]}
      />
      <ChartCard
        title="Failure Analysis"
        description="Distribution of failure causes"
        chartType="pie"
        data={failureData}
        index="name"
        valueFormatter={(value) => `${value}%`}
      />
      <ChartCard
        title="Maintenance Trends"
        description="Monthly maintenance trends by type"
        chartType="line"
        data={maintenanceData}
        index="date"
        categories={["Preventive", "Corrective", "Predictive"]}
      />
      <ChartCard
        title="Maintenance vs. Efficiency"
        description="Correlation between maintenance and efficiency"
        chartType="area"
        data={performanceData}
        index="date"
        categories={["Efficiency"]}
        valueFormatter={(value) => `${value}%`}
      />
    </div>
  )
}

export function OverviewCharts() {
  return (
    <div className="space-y-6">
      {/* Comprehensive Transformer Distribution Reports */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <ChartCard
          title="Transformers by Voltage Level"
          description="Distribution of transformers by voltage rating"
          chartType="pie"
          data={voltageDistributionData}
          index="name"
          valueFormatter={(value) => `${value} units`}
        />
        <ChartCard
          title="Transformers by kVA Rating"
          description="Distribution of transformers by capacity"
          chartType="donut"
          data={kvaRatingData}
          index="name"
          valueFormatter={(value) => `${value} units`}
        />
        <ChartCard
          title="Transformers by Customer Type"
          description="Distribution by customer category"
          chartType="pie"
          data={customerTypeData}
          index="name"
          valueFormatter={(value) => `${value} units`}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <ChartCard
          title="Transformers by Region"
          description="Regional distribution of transformers"
          chartType="bar"
          data={regionDistributionData}
          index="name"
          categories={["count"]}
          valueFormatter={(value) => `${value} units`}
        />
        <ChartCard
          title="Top Service Centers"
          description="Service centers with most transformers"
          chartType="bar"
          data={serviceCenterData.slice(0, 8)}
          index="name"
          categories={["count"]}
          valueFormatter={(value) => `${value} units`}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <ChartCard
          title="Top Feeders"
          description="Feeders with most transformers"
          chartType="bar"
          data={feederData}
          index="name"
          categories={["count"]}
          valueFormatter={(value) => `${value} units`}
        />
        <ChartCard
          title="Top Substations"
          description="Substations with most transformers"
          chartType="bar"
          data={substationData}
          index="name"
          categories={["count"]}
          valueFormatter={(value) => `${value} units`}
        />
      </div>

      {/* Performance and Maintenance Overview */}
      <div className="grid gap-4 md:grid-cols-2">
        <ChartCard
          title="Transformer Efficiency Trends"
          description="Monthly efficiency performance"
          chartType="line"
          data={performanceData}
          index="date"
          categories={["Efficiency"]}
          valueFormatter={(value) => `${value}%`}
        />
        <ChartCard
          title="Maintenance Activities"
          description="Monthly maintenance by type"
          chartType="bar"
          data={maintenanceData}
          index="date"
          categories={["Preventive", "Corrective", "Predictive"]}
        />
      </div>
    </div>
  )
}

// New specialized chart components for comprehensive transformer analysis
export function TransformerDistributionCharts() {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <ChartCard
          title="Voltage Level Distribution"
          description="Number of transformers by voltage rating"
          chartType="pie"
          data={voltageDistributionData}
          index="name"
          valueFormatter={(value) => `${value} units`}
        />
        <ChartCard
          title="kVA Rating Distribution"
          description="Transformer capacity distribution"
          chartType="donut"
          data={kvaRatingData}
          index="name"
          valueFormatter={(value) => `${value} units`}
        />
        <ChartCard
          title="Customer Type Distribution"
          description="Transformers by customer category"
          chartType="pie"
          data={customerTypeData}
          index="name"
          valueFormatter={(value) => `${value} units`}
        />
      </div>
    </div>
  )
}

export function RegionalAnalysisCharts() {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        <ChartCard
          title="Regional Distribution"
          description="Transformers across Ethiopian regions"
          chartType="bar"
          data={regionDistributionData}
          index="name"
          categories={["count"]}
          valueFormatter={(value) => `${value} units`}
        />
        <ChartCard
          title="Service Center Analysis"
          description="Top performing service centers"
          chartType="bar"
          data={serviceCenterData}
          index="name"
          categories={["count"]}
          valueFormatter={(value) => `${value} units`}
        />
      </div>
    </div>
  )
}

export function InfrastructureAnalysisCharts() {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        <ChartCard
          title="Feeder Analysis"
          description="Transformers by feeder distribution"
          chartType="bar"
          data={feederData}
          index="name"
          categories={["count"]}
          valueFormatter={(value) => `${value} units`}
        />
        <ChartCard
          title="Substation Analysis"
          description="Transformers by substation"
          chartType="bar"
          data={substationData}
          index="name"
          categories={["count"]}
          valueFormatter={(value) => `${value} units`}
        />
      </div>
    </div>
  )
}

export function ComprehensiveReportCharts() {
  return (
    <div className="space-y-8">
      {/* Summary Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">1,248</div>
              <div className="text-sm text-muted-foreground">Total Transformers</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">9</div>
              <div className="text-sm text-muted-foreground">Regions Covered</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">25</div>
              <div className="text-sm text-muted-foreground">Service Centers</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600">5</div>
              <div className="text-sm text-muted-foreground">Voltage Levels</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Voltage and Capacity Analysis */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Voltage and Capacity Analysis</h3>
        <div className="grid gap-4 md:grid-cols-2">
          <ChartCard
            title="Voltage Level Distribution"
            description="Breakdown by voltage rating (kV)"
            chartType="pie"
            data={voltageDistributionData}
            index="name"
            valueFormatter={(value) => `${value} units`}
          />
          <ChartCard
            title="kVA Rating Distribution"
            description="Breakdown by transformer capacity"
            chartType="donut"
            data={kvaRatingData}
            index="name"
            valueFormatter={(value) => `${value} units`}
          />
        </div>
      </div>

      {/* Customer and Regional Analysis */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Customer and Regional Analysis</h3>
        <div className="grid gap-4 md:grid-cols-2">
          <ChartCard
            title="Customer Type Distribution"
            description="Transformers by customer category"
            chartType="pie"
            data={customerTypeData}
            index="name"
            valueFormatter={(value) => `${value} units`}
          />
          <ChartCard
            title="Regional Distribution"
            description="Transformers across Ethiopian regions"
            chartType="bar"
            data={regionDistributionData}
            index="name"
            categories={["count"]}
            valueFormatter={(value) => `${value} units`}
          />
        </div>
      </div>

      {/* Service Center Analysis */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Service Center Analysis</h3>
        <ChartCard
          title="Service Center Performance"
          description="Top 10 service centers by transformer count"
          chartType="bar"
          data={serviceCenterData}
          index="name"
          categories={["count"]}
          valueFormatter={(value) => `${value} units`}
        />
      </div>

      {/* Infrastructure Analysis */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Infrastructure Analysis</h3>
        <div className="grid gap-4 md:grid-cols-2">
          <ChartCard
            title="Feeder Distribution"
            description="Top 10 feeders by transformer count"
            chartType="bar"
            data={feederData}
            index="name"
            categories={["count"]}
            valueFormatter={(value) => `${value} units`}
          />
          <ChartCard
            title="Substation Distribution"
            description="Top 10 substations by transformer count"
            chartType="bar"
            data={substationData}
            index="name"
            categories={["count"]}
            valueFormatter={(value) => `${value} units`}
          />
        </div>
      </div>
    </div>
  )
}

export function CustomCharts({ chartType = "bar", data = performanceData, index = "date", categories = ["Efficiency", "Load Factor", "Power Quality"] }) {
  return (
    <ChartCard
      title="Custom Report"
      description="Generated based on selected metrics"
      chartType={chartType as ChartType}
      data={data}
      index={index}
      categories={categories}
      valueFormatter={(value) => typeof value === "number" && value > 1 && value < 100 ? `${value}%` : `${value}`}
    />
  )
}
