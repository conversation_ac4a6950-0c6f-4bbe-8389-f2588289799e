"use client"

import { MaintenanceRecord } from '@/src/lib/db/schema'

export interface MaintenanceFilters {
  status?: string[]
  priority?: string[]
  type?: string[]
  assignedTo?: string
  transformerId?: string
  dateFrom?: string
  dateTo?: string
  search?: string
}

export interface MaintenanceStatistics {
  total: number
  byStatus: {
    scheduled: number
    in_progress: number
    completed: number
    cancelled: number
    overdue: number
  }
  byPriority: {
    low: number
    medium: number
    high: number
    critical: number
  }
  byType: {
    preventive: number
    corrective: number
    emergency: number
    inspection: number
  }
  averageDuration: number
  upcomingCount: number
  overdueCount: number
}

export interface CreateMaintenanceRequest {
  transformerId: string
  type: string
  title: string
  description: string
  scheduledDate: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  assignedTo: string
  estimatedDuration: number
  notes?: string
}

export interface UpdateMaintenanceRequest {
  id: string
  status?: string
  completedDate?: string
  actualDuration?: number
  notes?: string
  cost?: number
}

class MaintenanceDatabaseService {
  private baseUrl = '/api/mysql/maintenance'

  /**
   * Get all maintenance records with optional filtering
   */
  async getMaintenanceRecords(filters?: MaintenanceFilters): Promise<MaintenanceRecord[]> {
    try {
      const params = new URLSearchParams()
      
      if (filters) {
        if (filters.status?.length) {
          filters.status.forEach(status => params.append('status', status))
        }
        if (filters.priority?.length) {
          filters.priority.forEach(priority => params.append('priority', priority))
        }
        if (filters.type?.length) {
          filters.type.forEach(type => params.append('type', type))
        }
        if (filters.assignedTo) params.append('assignedTo', filters.assignedTo)
        if (filters.transformerId) params.append('transformerId', filters.transformerId)
        if (filters.dateFrom) params.append('dateFrom', filters.dateFrom)
        if (filters.dateTo) params.append('dateTo', filters.dateTo)
        if (filters.search) params.append('search', filters.search)
      }

      const response = await fetch(`${this.baseUrl}?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch maintenance records: ${response.statusText}`)
      }

      const data = await response.json()
      return data.success ? data.data : []
    } catch (error) {
      console.error('Error fetching maintenance records:', error)
      return this.getMockMaintenanceRecords()
    }
  }

  /**
   * Get maintenance record by ID
   */
  async getMaintenanceRecord(id: string): Promise<MaintenanceRecord | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch maintenance record: ${response.statusText}`)
      }

      const data = await response.json()
      return data.success ? data.data : null
    } catch (error) {
      console.error('Error fetching maintenance record:', error)
      return null
    }
  }

  /**
   * Create new maintenance record
   */
  async createMaintenanceRecord(maintenance: CreateMaintenanceRequest): Promise<MaintenanceRecord | null> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(maintenance),
      })

      if (!response.ok) {
        throw new Error(`Failed to create maintenance record: ${response.statusText}`)
      }

      const data = await response.json()
      return data.success ? data.data : null
    } catch (error) {
      console.error('Error creating maintenance record:', error)
      throw error
    }
  }

  /**
   * Update maintenance record
   */
  async updateMaintenanceRecord(update: UpdateMaintenanceRequest): Promise<MaintenanceRecord | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${update.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(update),
      })

      if (!response.ok) {
        throw new Error(`Failed to update maintenance record: ${response.statusText}`)
      }

      const data = await response.json()
      return data.success ? data.data : null
    } catch (error) {
      console.error('Error updating maintenance record:', error)
      throw error
    }
  }

  /**
   * Delete maintenance record
   */
  async deleteMaintenanceRecord(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error(`Failed to delete maintenance record: ${response.statusText}`)
      }

      const data = await response.json()
      return data.success
    } catch (error) {
      console.error('Error deleting maintenance record:', error)
      return false
    }
  }

  /**
   * Get maintenance statistics
   */
  async getMaintenanceStatistics(): Promise<MaintenanceStatistics> {
    try {
      const response = await fetch(`${this.baseUrl}/statistics`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch maintenance statistics: ${response.statusText}`)
      }

      const data = await response.json()
      return data.success ? data.data : this.getMockStatistics()
    } catch (error) {
      console.error('Error fetching maintenance statistics:', error)
      return this.getMockStatistics()
    }
  }

  /**
   * Get upcoming maintenance records
   */
  async getUpcomingMaintenance(days: number = 7): Promise<MaintenanceRecord[]> {
    const endDate = new Date()
    endDate.setDate(endDate.getDate() + days)
    
    return this.getMaintenanceRecords({
      status: ['scheduled'],
      dateTo: endDate.toISOString().split('T')[0]
    })
  }

  /**
   * Get overdue maintenance records
   */
  async getOverdueMaintenance(): Promise<MaintenanceRecord[]> {
    const today = new Date().toISOString().split('T')[0]
    
    return this.getMaintenanceRecords({
      status: ['scheduled'],
      dateTo: today
    })
  }

  /**
   * Complete maintenance record
   */
  async completeMaintenance(id: string, actualDuration?: number, notes?: string, cost?: number): Promise<MaintenanceRecord | null> {
    return this.updateMaintenanceRecord({
      id,
      status: 'completed',
      completedDate: new Date().toISOString(),
      actualDuration,
      notes,
      cost
    })
  }

  /**
   * Cancel maintenance record
   */
  async cancelMaintenance(id: string, reason?: string): Promise<MaintenanceRecord | null> {
    return this.updateMaintenanceRecord({
      id,
      status: 'cancelled',
      notes: reason
    })
  }

  /**
   * Get maintenance records by transformer ID
   */
  async getMaintenanceByTransformer(transformerId: string): Promise<MaintenanceRecord[]> {
    return this.getMaintenanceRecords({ transformerId })
  }

  /**
   * Mock data for development/fallback
   */
  private getMockMaintenanceRecords(): MaintenanceRecord[] {
    return [
      {
        id: '1',
        transformerId: 'transformer-001',
        type: 'preventive',
        status: 'scheduled',
        title: 'Routine Inspection',
        description: 'Monthly routine inspection and testing',
        scheduledDate: '2024-02-15T09:00:00Z',
        assignedTo: 'tech-001',
        reportedBy: 'admin-001',
        priority: 'medium',
        estimatedDuration: 4,
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z'
      },
      {
        id: '2',
        transformerId: 'transformer-002',
        type: 'corrective',
        status: 'in_progress',
        title: 'Oil Leak Repair',
        description: 'Repair oil leak in main tank',
        scheduledDate: '2024-02-10T08:00:00Z',
        assignedTo: 'tech-002',
        reportedBy: 'admin-001',
        priority: 'high',
        estimatedDuration: 8,
        createdAt: '2024-02-08T14:00:00Z',
        updatedAt: '2024-02-10T08:00:00Z'
      }
    ]
  }

  /**
   * Mock statistics for development/fallback
   */
  private getMockStatistics(): MaintenanceStatistics {
    return {
      total: 150,
      byStatus: {
        scheduled: 45,
        in_progress: 12,
        completed: 85,
        cancelled: 8,
        overdue: 5
      },
      byPriority: {
        low: 30,
        medium: 75,
        high: 35,
        critical: 10
      },
      byType: {
        preventive: 90,
        corrective: 40,
        emergency: 15,
        inspection: 5
      },
      averageDuration: 6.5,
      upcomingCount: 25,
      overdueCount: 5
    }
  }
}

export const maintenanceDatabaseService = new MaintenanceDatabaseService()
