// Migrate data from original app_* tables to app_*2 workaround tables (only matching columns)
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

const migrations = [
  {
    from: 'app_regions',
    to: 'app_regions2',
    columns: ['id', 'name', 'code', 'population', 'area_km2', 'created_at', 'updated_at']
  },
  {
    from: 'app_service_centers',
    to: 'app_service_centers2',
    columns: ['id', 'name', 'code', 'region_id', 'address', 'phone', 'email', 'manager_name', 'created_at', 'updated_at']
  },
  {
    from: 'app_transformers',
    to: 'app_transformers2',
    columns: ['id', 'serial_number', 'name', 'type', 'capacity_kva', 'voltage_primary', 'voltage_secondary', 'manufacturer', 'model', 'year_manufactured', 'installation_date', 'location_name', 'latitude', 'longitude', 'region_id', 'service_center_id', 'status', 'efficiency_rating', 'load_factor', 'temperature', 'oil_level', 'last_maintenance', 'next_maintenance', 'created_at', 'updated_at']
  },
  {
    from: 'app_alerts',
    to: 'app_alerts2',
    columns: ['id', 'transformer_id', 'title', 'description', 'severity', 'type', 'status', 'priority', 'created_by', 'assigned_to', 'resolved_at', 'resolved_by', 'is_resolved', 'created_at']
  },
  {
    from: 'app_maintenance_schedules',
    to: 'app_maintenance_schedules2',
    columns: ['id', 'transformer_id', 'type', 'title', 'description', 'scheduled_date', 'estimated_duration', 'completion_days', 'priority', 'status', 'technician_id', 'supervisor_id', 'created_at', 'updated_at']
  },
  {
    from: 'app_notifications',
    to: 'app_notifications2',
    columns: ['id', 'title', 'message', 'type', 'recipient_id', 'sender_id', 'is_read', 'read_at', 'created_at']
  },
  {
    from: 'app_performance_metrics',
    to: 'app_performance_metrics2',
    columns: ['id', 'transformer_id', 'metric_type', 'value', 'unit', 'recorded_at']
  },
  {
    from: 'app_weather_data',
    to: 'app_weather_data2',
    columns: ['id', 'region_id', 'temperature', 'humidity', 'wind_speed', 'weather_condition', 'risk_level', 'recorded_at']
  },
  {
    from: 'app_sensor_readings',
    to: 'app_sensor_readings2',
    columns: ['id', 'transformer_id', 'sensor_type', 'reading_value', 'recorded_at']
  }
];

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    for (const m of migrations) {
      const [rows] = await connection.execute(`SELECT ${m.columns.join(', ')} FROM ${m.from}`);
      if (rows.length === 0) {
        console.log(`No data to migrate from ${m.from}`);
        continue;
      }
      // Clear destination table first
      await connection.execute(`DELETE FROM ${m.to}`);
      for (const row of rows) {
        const values = m.columns.map(col => row[col]);
        const placeholders = m.columns.map(() => '?').join(', ');
        await connection.execute(`INSERT INTO ${m.to} (${m.columns.join(', ')}) VALUES (${placeholders})`, values);
      }
      console.log(`✅ Migrated ${rows.length} rows from ${m.from} to ${m.to}`);
    }
  } catch (err) {
    console.error('❌ Error during migration:', err);
  } finally {
    await connection.end();
  }
})();
