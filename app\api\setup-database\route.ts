import { NextRequest, NextResponse } from 'next/server'
import { simpleSetupDatabase } from '../../../src/lib/db/simple-setup'
import { simpleSeedDatabase } from '../../../src/lib/db/simple-seed'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 API: Database setup API is ready...')

    return NextResponse.json({
      success: true,
      message: 'Database setup API is ready',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ API: Database status check failed:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to check database status',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    console.log(`🔄 API: Database ${action} requested...`)

    let result

    switch (action) {
      case 'setup':
        // First create the schema
        console.log('🔄 Creating database schema...')
        const setupResult = await simpleSetupDatabase()

        if (!setupResult.success) {
          console.error('❌ API: Database schema creation failed:', setupResult.message)
          return NextResponse.json({
            success: false,
            error: setupResult.message,
            timestamp: new Date().toISOString()
          }, { status: 500 })
        }

        // Wait a moment for the schema to be fully committed
        console.log('⏳ Waiting for schema to be committed...')
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Then seed the data
        console.log('🔄 Seeding database with Ethiopian data...')
        const seedResult = await simpleSeedDatabase()

        if (!seedResult.success) {
          console.error('❌ API: Database seeding failed:', seedResult.message)
          return NextResponse.json({
            success: false,
            error: seedResult.message,
            timestamp: new Date().toISOString()
          }, { status: 500 })
        }

        result = {
          success: true,
          message: 'Database setup and seeding completed successfully',
          setupResult,
          seedResult
        }
        break

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use "setup"',
          timestamp: new Date().toISOString()
        }, { status: 400 })
    }

    if (result.success) {
      console.log(`✅ API: Database ${action} completed successfully`)

      return NextResponse.json({
        success: true,
        message: result.message,
        setupResult: result.setupResult,
        seedResult: result.seedResult,
        action,
        timestamp: new Date().toISOString()
      })
    } else {
      console.error(`❌ API: Database ${action} failed:`, result.error)

      return NextResponse.json({
        success: false,
        error: result.message,
        action,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

  } catch (error) {
    console.error('❌ API: Database setup/reset failed:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to process database operation',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
