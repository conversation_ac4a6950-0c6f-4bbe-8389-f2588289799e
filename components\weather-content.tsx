"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { CloudLightning, Download } from "lucide-react"
import { WeatherImpactWidget } from "@/components/weather-impact-widget"
import { WeatherSeverityLegend } from "@/components/weather-severity-legend"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"

export function WeatherContent() {
  const [lastRefreshed, setLastRefreshed] = useState<Date>(new Date())

  const handleRefresh = () => {
    // This will be called by the WeatherImpactWidget when it refreshes data
    setLastRefreshed(new Date())
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Weather Impact Analysis</h1>
        <div className="flex items-center gap-2">
          <p className="text-sm text-muted-foreground hidden md:block">
            Last updated: {lastRefreshed.toLocaleTimeString()}
          </p>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button size="sm">
            <CloudLightning className="mr-2 h-4 w-4" />
            Weather Alerts
          </Button>
        </div>
      </div>

      <Tabs defaultValue="dashboard" className="space-y-4">
        <TabsList>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="legend">Severity Legend</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          <Card>
            <CardHeader>
              <CardTitle>Weather Impact on Transformer Network</CardTitle>
              <CardDescription>
                Analysis of weather conditions and their impact on the transformer network
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WeatherImpactWidget />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="legend">
          <WeatherSeverityLegend />
        </TabsContent>
      </Tabs>
    </div>
  )
}
