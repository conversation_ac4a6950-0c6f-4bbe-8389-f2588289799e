"use client"

import React, { useState, useCallback, useRef } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  MoreVertical,
  Download,
  Share2,
  Maximize2,
  Minimize2,
  RefreshCw,
  Settings,
  Eye,
  EyeOff,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Palette,
  Grid,
  BarChart3,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  AreaChart as AreaChartIcon,
  TrendingUp,
  Filter,
  Calendar,
  Clock
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  Di<PERSON><PERSON>itle,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog"
import { toast } from 'sonner'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export interface ChartData {
  id: string
  title: string
  subtitle?: string
  data: any[]
  type: 'line' | 'bar' | 'area' | 'pie' | 'scatter' | 'radar'
  xAxisKey?: string
  yAxisKey?: string
  dataKeys: string[]
  colors?: string[]
  showGrid?: boolean
  showLegend?: boolean
  showTooltip?: boolean
  showZoom?: boolean
  showBrush?: boolean
  annotations?: Array<{
    x?: number
    y?: number
    text: string
    color?: string
  }>
  lastUpdated?: Date
  refreshInterval?: number
  isRealTime?: boolean
}

interface ChartSettings {
  showGrid: boolean
  showLegend: boolean
  showTooltip: boolean
  showAnimations: boolean
  theme: 'light' | 'dark'
  colorScheme: 'default' | 'blue' | 'green' | 'purple' | 'orange'
  chartType: 'line' | 'bar' | 'area' | 'pie'
  timeRange: '1h' | '6h' | '24h' | '7d' | '30d'
}

interface AdvancedChartContainerProps {
  data: ChartData
  onExport?: (id: string, format: 'png' | 'pdf' | 'svg' | 'csv') => void
  onShare?: (id: string) => void
  onRefresh?: (id: string) => void
  onSettingsChange?: (id: string, settings: Partial<ChartSettings>) => void
  onDataFilter?: (id: string, filters: any) => void
  className?: string
  height?: number
  fullWidth?: boolean
  showActions?: boolean
  showSettings?: boolean
  interactive?: boolean
}

const DEFAULT_SETTINGS: ChartSettings = {
  showGrid: true,
  showLegend: true,
  showTooltip: true,
  showAnimations: true,
  theme: 'light',
  colorScheme: 'default',
  chartType: 'line',
  timeRange: '24h'
}

export function AdvancedChartContainer({
  data,
  onExport,
  onShare,
  onRefresh,
  onSettingsChange,
  onDataFilter,
  className = '',
  height = 400,
  fullWidth = false,
  showActions = true,
  showSettings = true,
  interactive = true
}: AdvancedChartContainerProps) {
  const [settings, setSettings] = useState<ChartSettings>(DEFAULT_SETTINGS)
  const [isExpanded, setIsExpanded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(1)
  const [selectedDataKeys, setSelectedDataKeys] = useState<string[]>(data.dataKeys)
  const [showAnnotations, setShowAnnotations] = useState(true)
  
  const chartRef = useRef<HTMLDivElement>(null)

  const updateSettings = useCallback((newSettings: Partial<ChartSettings>) => {
    const updated = { ...settings, ...newSettings }
    setSettings(updated)
    onSettingsChange?.(data.id, newSettings)
    toast.success('Chart settings updated')
  }, [settings, data.id, onSettingsChange])

  const handleExport = useCallback(async (format: 'png' | 'pdf' | 'svg' | 'csv') => {
    if (!chartRef.current) return

    setIsLoading(true)
    try {
      switch (format) {
        case 'png':
          const canvas = await html2canvas(chartRef.current, {
            backgroundColor: '#ffffff',
            scale: 2
          })
          const link = document.createElement('a')
          link.download = `${data.title.replace(/\s+/g, '-').toLowerCase()}-chart.png`
          link.href = canvas.toDataURL()
          link.click()
          break

        case 'pdf':
          const pdfCanvas = await html2canvas(chartRef.current, {
            backgroundColor: '#ffffff',
            scale: 2
          })
          const pdf = new jsPDF('landscape')
          const imgData = pdfCanvas.toDataURL('image/png')
          const imgWidth = 280
          const imgHeight = (pdfCanvas.height * imgWidth) / pdfCanvas.width
          pdf.addImage(imgData, 'PNG', 10, 10, imgWidth, imgHeight)
          pdf.save(`${data.title.replace(/\s+/g, '-').toLowerCase()}-chart.pdf`)
          break

        case 'csv':
          const csvContent = [
            data.dataKeys.join(','),
            ...data.data.map(row => 
              data.dataKeys.map(key => row[key] || '').join(',')
            )
          ].join('\n')
          
          const csvBlob = new Blob([csvContent], { type: 'text/csv' })
          const csvUrl = URL.createObjectURL(csvBlob)
          const csvLink = document.createElement('a')
          csvLink.href = csvUrl
          csvLink.download = `${data.title.replace(/\s+/g, '-').toLowerCase()}-data.csv`
          csvLink.click()
          break

        default:
          toast.error('Export format not supported')
          return
      }

      onExport?.(data.id, format)
      toast.success(`Chart exported as ${format.toUpperCase()}`)
    } catch (error) {
      toast.error('Export failed')
    } finally {
      setIsLoading(false)
    }
  }, [data, onExport])

  const handleShare = useCallback(async () => {
    try {
      const shareData = {
        title: data.title,
        text: `Check out this ${data.title} chart`,
        url: window.location.href
      }

      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        await navigator.clipboard.writeText(window.location.href)
        toast.success('Chart link copied to clipboard')
      }

      onShare?.(data.id)
    } catch (error) {
      toast.error('Sharing failed')
    }
  }, [data, onShare])

  const handleRefresh = useCallback(() => {
    setIsLoading(true)
    onRefresh?.(data.id)
    setTimeout(() => setIsLoading(false), 1000)
    toast.success('Chart data refreshed')
  }, [data.id, onRefresh])

  const handleZoom = useCallback((direction: 'in' | 'out' | 'reset') => {
    switch (direction) {
      case 'in':
        setZoomLevel(prev => Math.min(prev * 1.2, 3))
        break
      case 'out':
        setZoomLevel(prev => Math.max(prev / 1.2, 0.5))
        break
      case 'reset':
        setZoomLevel(1)
        break
    }
  }, [])

  const toggleDataKey = useCallback((key: string) => {
    setSelectedDataKeys(prev => 
      prev.includes(key) 
        ? prev.filter(k => k !== key)
        : [...prev, key]
    )
  }, [])

  const getColorScheme = () => {
    switch (settings.colorScheme) {
      case 'blue': return ['#3b82f6', '#1d4ed8', '#1e40af', '#1e3a8a']
      case 'green': return ['#10b981', '#059669', '#047857', '#065f46']
      case 'purple': return ['#8b5cf6', '#7c3aed', '#6d28d9', '#5b21b6']
      case 'orange': return ['#f59e0b', '#d97706', '#b45309', '#92400e']
      default: return ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']
    }
  }

  return (
    <Card className={`${fullWidth ? 'col-span-full' : ''} border-0 shadow-xl bg-white/95 backdrop-blur-sm hover:shadow-2xl transition-all duration-500 ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            <div className="flex items-center gap-2">
              <CardTitle className="text-xl font-bold text-gray-900">{data.title}</CardTitle>
              
              {data.isRealTime && (
                <Badge variant="secondary" className="bg-green-100 text-green-700">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                  Live
                </Badge>
              )}
              
              {isLoading && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                  <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                  Updating
                </Badge>
              )}
            </div>
            
            {data.subtitle && (
              <p className="text-sm text-gray-500">{data.subtitle}</p>
            )}
            
            {data.lastUpdated && (
              <div className="flex items-center gap-1 text-xs text-gray-400">
                <Clock className="h-3 w-3" />
                <span>Updated {data.lastUpdated.toLocaleTimeString()}</span>
              </div>
            )}
          </div>
          
          {showActions && (
            <div className="flex items-center gap-2">
              {/* Zoom Controls */}
              {interactive && (
                <div className="flex items-center gap-1">
                  <Button variant="ghost" size="sm" onClick={() => handleZoom('out')}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => handleZoom('reset')}>
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => handleZoom('in')}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </div>
              )}

              {/* Settings Menu */}
              {showSettings && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>Chart Settings</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuCheckboxItem
                      checked={settings.showGrid}
                      onCheckedChange={(checked) => updateSettings({ showGrid: checked })}
                    >
                      <Grid className="mr-2 h-4 w-4" />
                      Show Grid
                    </DropdownMenuCheckboxItem>
                    
                    <DropdownMenuCheckboxItem
                      checked={settings.showLegend}
                      onCheckedChange={(checked) => updateSettings({ showLegend: checked })}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      Show Legend
                    </DropdownMenuCheckboxItem>
                    
                    <DropdownMenuCheckboxItem
                      checked={settings.showAnimations}
                      onCheckedChange={(checked) => updateSettings({ showAnimations: checked })}
                    >
                      <TrendingUp className="mr-2 h-4 w-4" />
                      Animations
                    </DropdownMenuCheckboxItem>
                    
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuLabel>Chart Type</DropdownMenuLabel>
                    <DropdownMenuRadioGroup
                      value={settings.chartType}
                      onValueChange={(value) => updateSettings({ chartType: value as any })}
                    >
                      <DropdownMenuRadioItem value="line">
                        <LineChart className="mr-2 h-4 w-4" />
                        Line Chart
                      </DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="bar">
                        <BarChart3 className="mr-2 h-4 w-4" />
                        Bar Chart
                      </DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="area">
                        <AreaChartIcon className="mr-2 h-4 w-4" />
                        Area Chart
                      </DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="pie">
                        <PieChart className="mr-2 h-4 w-4" />
                        Pie Chart
                      </DropdownMenuRadioItem>
                    </DropdownMenuRadioGroup>
                    
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuLabel>Color Scheme</DropdownMenuLabel>
                    <DropdownMenuRadioGroup
                      value={settings.colorScheme}
                      onValueChange={(value) => updateSettings({ colorScheme: value as any })}
                    >
                      <DropdownMenuRadioItem value="default">Default</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="blue">Blue</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="green">Green</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="purple">Purple</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="orange">Orange</DropdownMenuRadioItem>
                    </DropdownMenuRadioGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {/* Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuItem onClick={handleRefresh}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh Data
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem onClick={() => setIsExpanded(true)}>
                    <Maximize2 className="mr-2 h-4 w-4" />
                    Full Screen
                  </DropdownMenuItem>
                  
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuItem onClick={handleShare}>
                    <Share2 className="mr-2 h-4 w-4" />
                    Share Chart
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem onClick={() => handleExport('png')}>
                    <Download className="mr-2 h-4 w-4" />
                    Export as PNG
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem onClick={() => handleExport('pdf')}>
                    <Download className="mr-2 h-4 w-4" />
                    Export as PDF
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem onClick={() => handleExport('csv')}>
                    <Download className="mr-2 h-4 w-4" />
                    Export Data (CSV)
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>

        {/* Data Series Toggle */}
        {data.dataKeys.length > 1 && (
          <div className="flex flex-wrap gap-2 mt-3">
            {data.dataKeys.map((key, index) => (
              <Button
                key={key}
                variant={selectedDataKeys.includes(key) ? "default" : "outline"}
                size="sm"
                onClick={() => toggleDataKey(key)}
                className="h-7 text-xs"
                style={{
                  backgroundColor: selectedDataKeys.includes(key) ? getColorScheme()[index] : undefined,
                  borderColor: getColorScheme()[index]
                }}
              >
                <div 
                  className="w-2 h-2 rounded-full mr-2"
                  style={{ backgroundColor: getColorScheme()[index] }}
                ></div>
                {key}
              </Button>
            ))}
          </div>
        )}
      </CardHeader>
      
      <CardContent className="pt-0">
        <div 
          ref={chartRef}
          className="relative"
          style={{ 
            height: `${height}px`,
            transform: `scale(${zoomLevel})`,
            transformOrigin: 'top left',
            transition: 'transform 0.3s ease'
          }}
        >
          {/* Chart component would be rendered here */}
          <div className="w-full h-full bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="text-4xl mb-2">{data.type === 'line' ? '📈' : data.type === 'bar' ? '📊' : data.type === 'pie' ? '🥧' : '📉'}</div>
              <p className="text-gray-600">Chart Component</p>
              <p className="text-sm text-gray-400">({data.data.length} data points)</p>
            </div>
          </div>

          {/* Annotations */}
          {showAnnotations && data.annotations && data.annotations.map((annotation, index) => (
            <div
              key={index}
              className="absolute bg-white border border-gray-300 rounded px-2 py-1 text-xs shadow-lg"
              style={{
                left: `${(annotation.x || 0) * 100}%`,
                top: `${(annotation.y || 0) * 100}%`,
                transform: 'translate(-50%, -100%)'
              }}
            >
              {annotation.text}
            </div>
          ))}
        </div>

        {/* Chart Statistics */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="font-semibold text-gray-900">{data.data.length}</div>
            <div className="text-gray-500">Data Points</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-gray-900">{selectedDataKeys.length}</div>
            <div className="text-gray-500">Series</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-gray-900">{(zoomLevel * 100).toFixed(0)}%</div>
            <div className="text-gray-500">Zoom</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-gray-900">{settings.chartType}</div>
            <div className="text-gray-500">Type</div>
          </div>
        </div>
      </CardContent>

      {/* Full Screen Dialog */}
      <Dialog open={isExpanded} onOpenChange={setIsExpanded}>
        <DialogContent className="max-w-7xl h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {data.title} - Full Screen View
            </DialogTitle>
            <DialogDescription>
              {data.subtitle || 'Detailed chart analysis'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 overflow-hidden">
            <div className="w-full h-full bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="text-6xl mb-4">{data.type === 'line' ? '📈' : data.type === 'bar' ? '📊' : data.type === 'pie' ? '🥧' : '📉'}</div>
                <p className="text-xl text-gray-600">Full Screen Chart</p>
                <p className="text-gray-400">Enhanced view with all features</p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
