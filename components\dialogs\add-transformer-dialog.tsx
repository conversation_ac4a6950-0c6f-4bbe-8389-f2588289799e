"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/src/components/ui/form"
import { Input } from "@/src/components/ui/input"
import { Button } from "@/src/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Textarea } from "@/src/components/ui/textarea"
import { useToast } from "@/src/components/ui/use-toast"
import { Loader2, Zap } from "lucide-react"

const transformerSchema = z.object({
  name: z.string().min(1, "Name is required"),
  serialNumber: z.string().min(1, "Serial number is required"),
  manufacturer: z.string().min(1, "Manufacturer is required"),
  model: z.string().min(1, "Model is required"),
  type: z.string().min(1, "Type is required"),
  capacity: z.number().min(1, "Capacity must be greater than 0"),
  voltagePrimary: z.number().min(1, "Primary voltage is required"),
  voltageSecondary: z.number().min(1, "Secondary voltage is required"),
  region: z.string().min(1, "Region is required"),
  serviceCenter: z.string().min(1, "Service center is required"),
  location: z.string().min(1, "Location is required"),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  installationDate: z.string().min(1, "Installation date is required"),
  specifications: z.string().optional(),
  notes: z.string().optional(),
})

type TransformerFormData = z.infer<typeof transformerSchema>

interface AddTransformerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function AddTransformerDialog({ open, onOpenChange, onSuccess }: AddTransformerDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const form = useForm<TransformerFormData>({
    resolver: zodResolver(transformerSchema),
    defaultValues: {
      name: "",
      serialNumber: "",
      manufacturer: "",
      model: "",
      type: "",
      capacity: 0,
      voltagePrimary: 0,
      voltageSecondary: 0,
      region: "",
      serviceCenter: "",
      location: "",
      installationDate: "",
      specifications: "",
      notes: "",
    },
  })

  const onSubmit = async (data: TransformerFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/mysql/dashboard/actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'addTransformer',
          payload: data,
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Transformer Added",
          description: "New transformer has been successfully added to the system.",
        })
        form.reset()
        onOpenChange(false)
        onSuccess()
      } else {
        throw new Error(result.message || 'Failed to add transformer')
      }
    } catch (error) {
      console.error('Error adding transformer:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add transformer",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Zap className="h-5 w-5 mr-2" />
            Add New Transformer
          </DialogTitle>
          <DialogDescription>
            Enter the details for the new transformer to add it to the system.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Transformer Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Addis Ababa Main Transformer" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="serialNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Serial Number</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., TRF-2024-001" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="manufacturer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Manufacturer</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select manufacturer" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ABB">ABB</SelectItem>
                          <SelectItem value="Siemens">Siemens</SelectItem>
                          <SelectItem value="Schneider Electric">Schneider Electric</SelectItem>
                          <SelectItem value="GE">General Electric</SelectItem>
                          <SelectItem value="TBEA">TBEA</SelectItem>
                          <SelectItem value="Hyundai">Hyundai</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Model</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., PowerMax 500" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Distribution">Distribution</SelectItem>
                          <SelectItem value="Power">Power</SelectItem>
                          <SelectItem value="Sub-transmission">Sub-transmission</SelectItem>
                          <SelectItem value="Transmission">Transmission</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="capacity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Capacity (kVA)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="e.g., 500"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="voltagePrimary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Primary Voltage (V)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="e.g., 15000"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="voltageSecondary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Secondary Voltage (V)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="e.g., 400"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="region"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Region</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select region" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="addis">Addis Ababa</SelectItem>
                          <SelectItem value="oromia">Oromia</SelectItem>
                          <SelectItem value="amhara">Amhara</SelectItem>
                          <SelectItem value="tigray">Tigray</SelectItem>
                          <SelectItem value="snnpr">SNNPR</SelectItem>
                          <SelectItem value="afar">Afar</SelectItem>
                          <SelectItem value="somali">Somali</SelectItem>
                          <SelectItem value="benishangul">Benishangul-Gumuz</SelectItem>
                          <SelectItem value="gambela">Gambela</SelectItem>
                          <SelectItem value="harari">Harari</SelectItem>
                          <SelectItem value="dire_dawa">Dire Dawa</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="serviceCenter"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Service Center</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Addis Ababa Service Center" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location Address</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Bole Subcity, Addis Ababa" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="installationDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Installation Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="latitude"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Latitude (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="any"
                        placeholder="e.g., 9.0192"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="longitude"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Longitude (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="any"
                        placeholder="e.g., 38.7525"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="specifications"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Technical Specifications (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter technical specifications, cooling type, protection class, etc."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Additional technical details about the transformer
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional notes or comments"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Add Transformer
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
