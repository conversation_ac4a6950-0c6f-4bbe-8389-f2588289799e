/**
 * Database initialization script
 *
 * This script initializes the JSON database with seed data.
 * It can be run manually or as part of the application startup process.
 */

import { ensureDbExists, readDb, backupDb, writeDb } from './db-utils';
import { generateSeedData } from './seed-data';

// Initialize the database with seed data
export function initializeDb(seedData: Partial<any>): void {
  ensureDbExists();
  const db = readDb();

  // Merge seed data with existing data
  Object.keys(seedData).forEach(key => {
    if (key === '_metadata') return; // Skip metadata

    const collection = key as keyof Omit<typeof db, '_metadata'>;
    if (Array.isArray(seedData[collection]) && seedData[collection]!.length > 0) {
      // Only add entities that don't already exist (by ID)
      const existingIds = new Set((db[collection] as any[]).map(item => item.id));
      const newEntities = (seedData[collection] as any[]).filter(
        item => !existingIds.has(item.id)
      );

      (db[collection] as any[]).push(...newEntities);
    }
  });

  writeDb(db);
}

export async function initializeDatabase(force: boolean = false): Promise<void> {
  console.log('Initializing database...');

  // Ensure the database exists
  ensureDbExists();

  // Read the current database
  const db = readDb();

  // Check if the database is empty or force initialization is requested
  const isEmpty = Object.keys(db).every(key => {
    if (key === '_metadata') return true;
    return Array.isArray(db[key as keyof typeof db]) && (db[key as keyof typeof db] as any[]).length === 0;
  });

  if (isEmpty || force) {
    if (!isEmpty && force) {
      console.log('Force initialization requested. Creating backup of existing database...');
      const backupKey = backupDb();
      console.log(`Backup created with key ${backupKey}`);
    }

    console.log('Generating seed data...');
    const seedData = generateSeedData();

    console.log('Initializing database with seed data...');
    initializeDb(seedData);

    console.log('Database initialized successfully.');
  } else {
    console.log('Database already contains data. Skipping initialization.');
    console.log(`Current record counts: ${JSON.stringify(db._metadata.recordCounts)}`);
  }
}
