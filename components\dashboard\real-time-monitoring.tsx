"use client"

import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Switch } from "@/src/components/ui/switch"
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer 
} from 'recharts'
import { 
  Activity, 
  Wifi, 
  WifiOff, 
  Zap, 
  Thermometer, 
  Gauge,
  AlertCircle,
  CheckCircle,
  Pause,
  Play,
  Settings
} from 'lucide-react'

interface RealTimeData {
  timestamp: string
  voltage: number
  current: number
  power: number
  temperature: number
  frequency: number
  efficiency: number
}

interface TransformerStatus {
  id: string
  name: string
  status: 'online' | 'offline' | 'warning' | 'critical'
  lastUpdate: string
  metrics: {
    voltage: number
    current: number
    temperature: number
    load: number
  }
}

export function RealTimeMonitoring() {
  const [isConnected, setIsConnected] = useState(false)
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [data, setData] = useState<RealTimeData[]>([])
  const [transformers, setTransformers] = useState<TransformerStatus[]>([])
  const [selectedMetric, setSelectedMetric] = useState<keyof RealTimeData>('voltage')
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const maxDataPoints = 50

  useEffect(() => {
    // Simulate initial connection
    setTimeout(() => {
      setIsConnected(true)
      loadInitialData()
    }, 1000)

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  const loadInitialData = () => {
    const initialData: RealTimeData[] = Array.from({ length: 20 }, (_, i) => ({
      timestamp: new Date(Date.now() - (19 - i) * 5000).toISOString(),
      voltage: 11000 + Math.random() * 200 - 100,
      current: 50 + Math.random() * 20,
      power: 500 + Math.random() * 100,
      temperature: 45 + Math.random() * 10,
      frequency: 50 + Math.random() * 0.2 - 0.1,
      efficiency: 94 + Math.random() * 4
    }))

    const initialTransformers: TransformerStatus[] = [
      {
        id: 'T001',
        name: 'Main Distribution T-001',
        status: 'online',
        lastUpdate: new Date().toISOString(),
        metrics: { voltage: 11050, current: 65, temperature: 47, load: 78 }
      },
      {
        id: 'T002',
        name: 'Secondary T-002',
        status: 'warning',
        lastUpdate: new Date().toISOString(),
        metrics: { voltage: 10950, current: 45, temperature: 52, load: 65 }
      },
      {
        id: 'T003',
        name: 'Backup T-003',
        status: 'online',
        lastUpdate: new Date().toISOString(),
        metrics: { voltage: 11100, current: 38, temperature: 43, load: 45 }
      },
      {
        id: 'T004',
        name: 'Emergency T-004',
        status: 'critical',
        lastUpdate: new Date().toISOString(),
        metrics: { voltage: 10800, current: 85, temperature: 58, load: 95 }
      }
    ]

    setData(initialData)
    setTransformers(initialTransformers)
  }

  const startMonitoring = () => {
    setIsMonitoring(true)
    intervalRef.current = setInterval(() => {
      const newDataPoint: RealTimeData = {
        timestamp: new Date().toISOString(),
        voltage: 11000 + Math.random() * 200 - 100,
        current: 50 + Math.random() * 20,
        power: 500 + Math.random() * 100,
        temperature: 45 + Math.random() * 10,
        frequency: 50 + Math.random() * 0.2 - 0.1,
        efficiency: 94 + Math.random() * 4
      }

      setData(prevData => {
        const newData = [...prevData, newDataPoint]
        return newData.slice(-maxDataPoints)
      })

      // Update transformer statuses randomly
      setTransformers(prevTransformers => 
        prevTransformers.map(transformer => ({
          ...transformer,
          lastUpdate: new Date().toISOString(),
          metrics: {
            voltage: transformer.metrics.voltage + (Math.random() - 0.5) * 20,
            current: Math.max(0, transformer.metrics.current + (Math.random() - 0.5) * 5),
            temperature: Math.max(20, transformer.metrics.temperature + (Math.random() - 0.5) * 2),
            load: Math.max(0, Math.min(100, transformer.metrics.load + (Math.random() - 0.5) * 10))
          }
        }))
      )
    }, 2000) // Update every 2 seconds
  }

  const stopMonitoring = () => {
    setIsMonitoring(false)
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }

  const getStatusColor = (status: TransformerStatus['status']) => {
    switch (status) {
      case 'online': return 'text-green-500'
      case 'warning': return 'text-yellow-500'
      case 'critical': return 'text-red-500'
      case 'offline': return 'text-gray-500'
      default: return 'text-gray-500'
    }
  }

  const getStatusIcon = (status: TransformerStatus['status']) => {
    switch (status) {
      case 'online': return <CheckCircle className="h-4 w-4" />
      case 'warning': return <AlertCircle className="h-4 w-4" />
      case 'critical': return <AlertCircle className="h-4 w-4" />
      case 'offline': return <WifiOff className="h-4 w-4" />
      default: return <WifiOff className="h-4 w-4" />
    }
  }

  const formatValue = (value: number, metric: keyof RealTimeData) => {
    switch (metric) {
      case 'voltage': return `${value.toFixed(0)}V`
      case 'current': return `${value.toFixed(1)}A`
      case 'power': return `${value.toFixed(0)}kW`
      case 'temperature': return `${value.toFixed(1)}°C`
      case 'frequency': return `${value.toFixed(2)}Hz`
      case 'efficiency': return `${value.toFixed(1)}%`
      default: return value.toFixed(2)
    }
  }

  const currentValue = data.length > 0 ? data[data.length - 1][selectedMetric] : 0

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Real-Time Monitoring
              </CardTitle>
              <CardDescription>
                Live transformer performance data and system status
              </CardDescription>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                {isConnected ? (
                  <Wifi className="h-4 w-4 text-green-500" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  checked={isMonitoring}
                  onCheckedChange={isMonitoring ? stopMonitoring : startMonitoring}
                  disabled={!isConnected}
                />
                <span className="text-sm">
                  {isMonitoring ? 'Monitoring' : 'Paused'}
                </span>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Real-time Chart */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Live Metrics</CardTitle>
              <select 
                value={selectedMetric} 
                onChange={(e) => setSelectedMetric(e.target.value as keyof RealTimeData)}
                className="px-3 py-1 border rounded-md text-sm"
              >
                <option value="voltage">Voltage</option>
                <option value="current">Current</option>
                <option value="power">Power</option>
                <option value="temperature">Temperature</option>
                <option value="frequency">Frequency</option>
                <option value="efficiency">Efficiency</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold">
                {formatValue(currentValue, selectedMetric)}
              </span>
              {isMonitoring && (
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              )}
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="timestamp" 
                  tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleString()}
                  formatter={(value: number) => [formatValue(value, selectedMetric), selectedMetric]}
                />
                <Line 
                  type="monotone" 
                  dataKey={selectedMetric} 
                  stroke="#8884d8" 
                  strokeWidth={2}
                  dot={false}
                  isAnimationActive={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Transformer Status */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Transformer Status</CardTitle>
            <CardDescription>
              Current status of all monitored transformers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {transformers.map((transformer) => (
                <div key={transformer.id} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className={getStatusColor(transformer.status)}>
                        {getStatusIcon(transformer.status)}
                      </div>
                      <span className="font-medium">{transformer.name}</span>
                    </div>
                    <Badge 
                      variant={transformer.status === 'online' ? 'default' : 
                               transformer.status === 'warning' ? 'secondary' : 'destructive'}
                    >
                      {transformer.status}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex items-center gap-1">
                      <Zap className="h-3 w-3" />
                      <span>{transformer.metrics.voltage.toFixed(0)}V</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Gauge className="h-3 w-3" />
                      <span>{transformer.metrics.load.toFixed(0)}%</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Activity className="h-3 w-3" />
                      <span>{transformer.metrics.current.toFixed(1)}A</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Thermometer className="h-3 w-3" />
                      <span>{transformer.metrics.temperature.toFixed(1)}°C</span>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground mt-2">
                    Last update: {new Date(transformer.lastUpdate).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">Online</span>
            </div>
            <p className="text-2xl font-bold">
              {transformers.filter(t => t.status === 'online').length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium">Warning</span>
            </div>
            <p className="text-2xl font-bold">
              {transformers.filter(t => t.status === 'warning').length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <span className="text-sm font-medium">Critical</span>
            </div>
            <p className="text-2xl font-bold">
              {transformers.filter(t => t.status === 'critical').length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <WifiOff className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium">Offline</span>
            </div>
            <p className="text-2xl font-bold">
              {transformers.filter(t => t.status === 'offline').length}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
