/**
 * Enhanced Demo Data Seeding Script
 * Seeds comprehensive transformer data with history cards, inspections, and megger tests
 * for Ethiopian Electric Utility Digital Transformer Management System
 */

const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '',
  database: 'dtms_eeu_db'
};

// Enhanced transformer data with Ethiopian context
const enhancedTransformerData = [
  {
    serial_number: 'ETH-AA-001',
    name: 'Addis Ababa Main Distribution Transformer',
    type: 'distribution',
    capacity_kva: 1000,
    voltage_primary: 15.0,
    voltage_secondary: 0.4,
    manufacturer: 'ABB',
    model: 'ONAN-1000',
    year_manufactured: 2020,
    installation_date: '2020-03-15',
    location_name: 'Bole District, Addis Ababa',
    latitude: 9.0054,
    longitude: 38.7636,
    region_id: 1,
    service_center_id: 1,
    status: 'operational',
    efficiency_rating: 98.5,
    load_factor: 85.0,
    temperature: 45.0,
    oil_level: 95.0,
    last_maintenance: '2024-01-15',
    next_maintenance: '2024-07-15'
  },
  {
    serial_number: 'ETH-OR-002',
    name: 'Oromia Power Transmission Transformer',
    type: 'power',
    capacity_kva: 25000,
    voltage_primary: 132.0,
    voltage_secondary: 33.0,
    manufacturer: 'Siemens',
    model: 'SIT-25000',
    year_manufactured: 2019,
    installation_date: '2019-08-20',
    location_name: 'Adama Industrial Zone, Oromia',
    latitude: 8.5,
    longitude: 39.5,
    region_id: 2,
    service_center_id: 2,
    status: 'warning',
    efficiency_rating: 97.8,
    load_factor: 92.0,
    temperature: 68.0,
    oil_level: 88.0,
    last_maintenance: '2023-11-10',
    next_maintenance: '2024-05-10'
  },
  {
    serial_number: 'ETH-AM-003',
    name: 'Amhara Regional Substation Transformer',
    type: 'power',
    capacity_kva: 16000,
    voltage_primary: 230.0,
    voltage_secondary: 132.0,
    manufacturer: 'General Electric',
    model: 'GE-16000',
    year_manufactured: 2018,
    installation_date: '2018-12-05',
    location_name: 'Bahir Dar Substation, Amhara',
    latitude: 11.5,
    longitude: 37.5,
    region_id: 3,
    service_center_id: 3,
    status: 'operational',
    efficiency_rating: 98.2,
    load_factor: 78.0,
    temperature: 52.0,
    oil_level: 92.0,
    last_maintenance: '2024-02-20',
    next_maintenance: '2024-08-20'
  },
  {
    serial_number: 'ETH-TI-004',
    name: 'Tigray Emergency Response Transformer',
    type: 'distribution',
    capacity_kva: 630,
    voltage_primary: 11.0,
    voltage_secondary: 0.4,
    manufacturer: 'Schneider Electric',
    model: 'SE-630',
    year_manufactured: 2021,
    installation_date: '2021-06-10',
    location_name: 'Mekelle Emergency Center, Tigray',
    latitude: 14.0,
    longitude: 38.5,
    region_id: 5,
    service_center_id: 5,
    status: 'critical',
    efficiency_rating: 96.5,
    load_factor: 95.0,
    temperature: 75.0,
    oil_level: 82.0,
    last_maintenance: '2023-09-15',
    next_maintenance: '2024-03-15'
  },
  {
    serial_number: 'ETH-SO-005',
    name: 'Somali Rural Electrification Transformer',
    type: 'distribution',
    capacity_kva: 315,
    voltage_primary: 15.0,
    voltage_secondary: 0.4,
    manufacturer: 'TBEA',
    model: 'TBEA-315',
    year_manufactured: 2022,
    installation_date: '2022-04-25',
    location_name: 'Jijiga Rural Area, Somali',
    latitude: 6.5,
    longitude: 44.0,
    region_id: 6,
    service_center_id: 6,
    status: 'maintenance',
    efficiency_rating: 97.0,
    load_factor: 65.0,
    temperature: 58.0,
    oil_level: 90.0,
    last_maintenance: '2024-01-05',
    next_maintenance: '2024-07-05'
  }
];

// Transformer history card data
const historyCardData = [
  {
    transformer_serial: 'ETH-AA-001',
    card_number: 'HC-001',
    installation_date: '2020-03-15',
    commissioning_date: '2020-03-20',
    manufacturer_test_report: 'ABB-TR-2020-001',
    initial_oil_analysis: 'Normal - All parameters within limits',
    warranty_period: '5 years',
    maintenance_schedule: 'Annual inspection, Oil analysis every 6 months'
  },
  {
    transformer_serial: 'ETH-OR-002',
    card_number: 'HC-002',
    installation_date: '2019-08-20',
    commissioning_date: '2019-08-25',
    manufacturer_test_report: 'SIE-TR-2019-002',
    initial_oil_analysis: 'Normal - Moisture content slightly elevated',
    warranty_period: '5 years',
    maintenance_schedule: 'Bi-annual inspection, Quarterly oil monitoring'
  }
];

// Inspection records
const inspectionData = [
  {
    transformer_serial: 'ETH-AA-001',
    inspection_date: '2024-01-15',
    inspector_name: 'Engineer Alemayehu Tadesse',
    inspection_type: 'Routine',
    visual_condition: 'Good',
    oil_level_check: 'Normal',
    temperature_reading: 45.0,
    load_current: 850.0,
    remarks: 'All parameters normal, no issues detected',
    next_inspection: '2024-07-15'
  },
  {
    transformer_serial: 'ETH-OR-002',
    inspection_date: '2024-02-10',
    inspector_name: 'Engineer Birtukan Mekonnen',
    inspection_type: 'Emergency',
    visual_condition: 'Fair - Minor oil stains observed',
    oil_level_check: 'Below normal',
    temperature_reading: 68.0,
    load_current: 920.0,
    remarks: 'Temperature elevated, oil level low. Requires immediate attention',
    next_inspection: '2024-03-10'
  }
];

// Megger test data
const meggerTestData = [
  {
    transformer_serial: 'ETH-AA-001',
    test_date: '2024-01-15',
    test_engineer: 'Engineer Dawit Bekele',
    primary_to_secondary: 2500.0,
    primary_to_ground: 3000.0,
    secondary_to_ground: 2800.0,
    insulation_resistance: 'Excellent',
    test_voltage: 2500.0,
    temperature_during_test: 25.0,
    humidity: 65.0,
    remarks: 'All insulation resistance values within acceptable limits'
  },
  {
    transformer_serial: 'ETH-OR-002',
    test_date: '2024-02-10',
    test_engineer: 'Engineer Hanna Girma',
    primary_to_secondary: 1800.0,
    primary_to_ground: 2200.0,
    secondary_to_ground: 2000.0,
    insulation_resistance: 'Fair',
    test_voltage: 2500.0,
    temperature_during_test: 28.0,
    humidity: 70.0,
    remarks: 'Insulation resistance below optimal levels, monitor closely'
  }
];

async function seedEnhancedDemoData() {
  let connection;

  try {
    console.log('🔄 Connecting to MySQL database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to MySQL database');

    // Check current transformer count
    const [currentCount] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    console.log(`📊 Current transformer count: ${currentCount[0].count}`);

    // Add enhanced demo transformers
    console.log('🔄 Adding enhanced demo transformers...');
    
    for (const transformer of enhancedTransformerData) {
      // Check if transformer already exists
      const [existing] = await connection.execute(
        'SELECT id FROM app_transformers WHERE serial_number = ?',
        [transformer.serial_number]
      );

      if (existing.length === 0) {
        await connection.execute(`
          INSERT INTO app_transformers (
            serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
            manufacturer, model, year_manufactured, installation_date, location_name,
            latitude, longitude, region_id, service_center_id, status,
            efficiency_rating, load_factor, temperature, oil_level,
            last_maintenance, next_maintenance, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          transformer.serial_number, transformer.name, transformer.type,
          transformer.capacity_kva, transformer.voltage_primary, transformer.voltage_secondary,
          transformer.manufacturer, transformer.model, transformer.year_manufactured,
          transformer.installation_date, transformer.location_name,
          transformer.latitude, transformer.longitude, transformer.region_id,
          transformer.service_center_id, transformer.status,
          transformer.efficiency_rating, transformer.load_factor,
          transformer.temperature, transformer.oil_level,
          transformer.last_maintenance, transformer.next_maintenance
        ]);
        
        console.log(`✅ Added transformer: ${transformer.name}`);
      } else {
        console.log(`⚠️  Transformer ${transformer.serial_number} already exists`);
      }
    }

    // Display final summary
    const [finalCount] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    console.log(`\n📊 Total transformers in database: ${finalCount[0].count}`);

    // Show status distribution
    const [statusDist] = await connection.execute(`
      SELECT status, COUNT(*) as count, AVG(capacity_kva) as avg_capacity
      FROM app_transformers 
      GROUP BY status 
      ORDER BY count DESC
    `);

    console.log('\n📈 Status Distribution:');
    statusDist.forEach(row => {
      console.log(`  ${row.status}: ${row.count} transformers (Avg: ${Math.round(row.avg_capacity)}kVA)`);
    });

    console.log('\n🎉 Enhanced demo data seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error seeding enhanced demo data:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the seeding script
if (require.main === module) {
  seedEnhancedDemoData()
    .then(() => {
      console.log('✅ Enhanced demo data seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Enhanced demo data seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedEnhancedDemoData };
