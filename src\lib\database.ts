/**
 * Clean Database Connection Layer
 * Simplified MySQL connection with connection pooling and error handling
 */

import mysql from 'mysql2/promise'

// Database configuration with environment variables
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  charset: 'utf8mb4',
  acquireTimeout: 60000,
  timeout: 60000,
}

// Single connection pool instance
let pool: mysql.Pool | null = null

/**
 * Get or create database connection pool
 * This ensures we reuse connections efficiently
 */
function getPool(): mysql.Pool {
  if (!pool) {
    pool = mysql.createPool(dbConfig)
  }
  return pool
}

/**
 * Execute a database query with parameters
 * @param query - SQL query string
 * @param params - Query parameters (optional)
 * @returns Query results
 */
export async function executeQuery<T = any>(
  query: string, 
  params: any[] = []
): Promise<T[]> {
  try {
    const connection = getPool()
    const [results] = await connection.execute(query, params)
    return results as T[]
  } catch (error) {
    console.error('Database query error:', error)
    console.error('Query:', query)
    console.error('Params:', params)
    throw new Error(`Database query failed: ${error}`)
  }
}

/**
 * Get a single database connection for transactions
 * Remember to release the connection when done!
 */
export async function getConnection(): Promise<mysql.PoolConnection> {
  try {
    const connection = getPool()
    return await connection.getConnection()
  } catch (error) {
    console.error('Database connection error:', error)
    throw new Error(`Failed to get database connection: ${error}`)
  }
}

/**
 * Execute multiple queries in a transaction
 * Automatically handles commit/rollback
 */
export async function executeTransaction(
  queries: Array<{ query: string; params?: any[] }>
): Promise<any[]> {
  const connection = await getConnection()
  
  try {
    await connection.beginTransaction()
    
    const results = []
    for (const { query, params = [] } of queries) {
      const [result] = await connection.execute(query, params)
      results.push(result)
    }
    
    await connection.commit()
    return results
  } catch (error) {
    await connection.rollback()
    throw error
  } finally {
    connection.release()
  }
}

/**
 * Test database connection
 */
export async function testConnection(): Promise<boolean> {
  try {
    await executeQuery('SELECT 1 as test')
    return true
  } catch (error) {
    console.error('Database connection test failed:', error)
    return false
  }
}

/**
 * Close all database connections
 * Call this when shutting down the application
 */
export async function closeConnections(): Promise<void> {
  if (pool) {
    await pool.end()
    pool = null
  }
}

/**
 * Get database statistics for monitoring
 */
export async function getDatabaseStats(): Promise<{
  totalConnections: number
  activeConnections: number
  idleConnections: number
}> {
  const connection = getPool()
  
  return {
    totalConnections: connection.config.connectionLimit || 0,
    activeConnections: 0, // Would need to implement connection tracking
    idleConnections: 0,   // Would need to implement connection tracking
  }
}

// Export the pool for advanced usage if needed
export { getPool }
