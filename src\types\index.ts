/**
 * TypeScript Type Definitions
 * Centralized type definitions for the entire application
 */

// Base types
export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

// Transformer related types
export interface Transformer extends BaseEntity {
  name: string
  serialNumber: string
  type: TransformerType
  status: TransformerStatus
  location: TransformerLocation
  specifications: TransformerSpecs
  performance: PerformanceMetrics
  regionId: string
  serviceCenterId: string
  manufacturer: string
  model: string
  installationDate: Date
  lastMaintenanceDate?: Date
  nextMaintenanceDate?: Date
  assetValue: number
  criticality: CriticalityLevel
  customerType: CustomerType
}

export type TransformerType = 
  | 'distribution' 
  | 'power' 
  | 'instrument' 
  | 'auto'

export type TransformerStatus = 
  | 'operational' 
  | 'warning' 
  | 'critical' 
  | 'maintenance' 
  | 'offline' 
  | 'burnt'

export type CriticalityLevel = 
  | 'low' 
  | 'medium' 
  | 'high' 
  | 'critical'

export type CustomerType = 
  | 'residential' 
  | 'commercial' 
  | 'industrial' 
  | 'government'

export interface TransformerLocation {
  name: string
  address: string
  coordinates: {
    latitude: number
    longitude: number
  }
  region: string
  serviceCenter: string
}

export interface TransformerSpecs {
  capacityKva: number
  voltageRating: {
    primary: number
    secondary: number
  }
  phases: number
  frequency: number
  coolingType: string
  impedance: number
}

export interface PerformanceMetrics {
  efficiencyRating: number
  loadFactor: number
  temperature: number
  vibration: number
  oilLevel: number
  powerFactor: number
  harmonicDistortion: number
}

// Region and Service Center types
export interface Region extends BaseEntity {
  name: string
  code: string
  description?: string
  transformerCount?: number
}

export interface ServiceCenter extends BaseEntity {
  name: string
  code: string
  regionId: string
  address: string
  contactInfo: {
    phone: string
    email: string
    manager: string
  }
  transformerCount?: number
}

// Alert types
export interface Alert extends BaseEntity {
  transformerId: string
  type: AlertType
  severity: AlertSeverity
  title: string
  message: string
  status: AlertStatus
  acknowledgedBy?: string
  acknowledgedAt?: Date
  resolvedBy?: string
  resolvedAt?: Date
  metadata?: Record<string, any>
}

export type AlertType = 
  | 'temperature' 
  | 'voltage' 
  | 'load' 
  | 'maintenance' 
  | 'communication' 
  | 'weather' 
  | 'security'

export type AlertSeverity = 
  | 'low' 
  | 'medium' 
  | 'high' 
  | 'critical'

export type AlertStatus = 
  | 'active' 
  | 'acknowledged' 
  | 'resolved' 
  | 'dismissed'

// Maintenance types
export interface MaintenanceSchedule extends BaseEntity {
  transformerId: string
  type: MaintenanceType
  status: MaintenanceStatus
  priority: Priority
  scheduledDate: Date
  completedDate?: Date
  estimatedDuration: number
  actualDuration?: number
  description: string
  technician?: string
  notes?: string
  cost?: number
}

export type MaintenanceType = 
  | 'preventive' 
  | 'corrective' 
  | 'emergency' 
  | 'inspection'

export type MaintenanceStatus = 
  | 'scheduled' 
  | 'in_progress' 
  | 'completed' 
  | 'cancelled' 
  | 'overdue'

export type Priority = 
  | 'low' 
  | 'medium' 
  | 'high' 
  | 'critical'

// User types
export interface User extends BaseEntity {
  firstName: string
  lastName: string
  email: string
  phone?: string
  role: UserRole
  department: string
  regionName?: string
  status: UserStatus
  lastLoginAt?: Date
}

export type UserRole = 
  | 'super_admin' 
  | 'national_asset_manager' 
  | 'regional_admin' 
  | 'field_technician' 
  | 'audit_compliance_officer'

export type UserStatus = 
  | 'active' 
  | 'inactive' 
  | 'suspended'

// Dashboard and Analytics types
export interface DashboardSummary {
  totalTransformers: number
  operationalCount: number
  warningCount: number
  criticalCount: number
  maintenanceCount: number
  avgEfficiency: number
  avgLoadFactor: number
  avgTemperature: number
  totalAssetValue: number
  activeAlerts: number
  pendingMaintenance: number
}

export interface FilterOptions {
  regions?: string[]
  serviceCenters?: string[]
  types?: TransformerType[]
  statuses?: TransformerStatus[]
  manufacturers?: string[]
  criticalities?: CriticalityLevel[]
  customerTypes?: CustomerType[]
  capacityRange?: [number, number]
  efficiencyRange?: [number, number]
  temperatureRange?: [number, number]
  loadFactorRange?: [number, number]
  assetValueRange?: [number, number]
  dateRange?: [Date, Date]
  search?: string
}

export interface DashboardData {
  transformers: Transformer[]
  alerts: Alert[]
  maintenance: MaintenanceSchedule[]
  regions: Region[]
  serviceCenters: ServiceCenter[]
  summary: DashboardSummary
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Chart and visualization types
export interface ChartDataPoint {
  name: string
  value: number
  color?: string
  [key: string]: any
}

export interface TimeSeriesDataPoint {
  timestamp: Date
  value: number
  label?: string
}

// Performance monitoring types
export interface PerformanceMetric {
  transformerId: string
  recordedAt: Date
  metrics: PerformanceMetrics
}

// Export utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
