"use client"

import { useEffect, useState } from "react"
import { MapLocation } from "@/src/services/map-service"
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Badge } from "@/src/components/ui/badge"
import { But<PERSON> } from "@/src/components/ui/button"
import { MapPin, Eye, Satellite } from "lucide-react"

// Simple satellite map view component
const SatelliteMapView = ({
  locations,
  height = "500px",
  onMarkerClick
}: {
  locations: MapLocation[]
  height?: string
  onMarkerClick?: (location: MapLocation) => void
}) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'operational': return '#22c55e' // green-500
      case 'warning': return '#f97316' // orange-500
      case 'maintenance': return '#f59e0b' // amber-500
      case 'critical': return '#ef4444' // red-500
      case 'burnt': return '#7c3aed' // purple-600
      case 'offline': return '#6b7280' // gray-500
      default: return '#6b7280' // gray-500
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'operational': return 'bg-green-100 text-green-800 border-green-200'
      case 'warning': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'maintenance': return 'bg-amber-100 text-amber-800 border-amber-200'
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'burnt': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'offline': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const statusCounts = locations.reduce((acc, location) => {
    acc[location.status] = (acc[location.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return (
    <div style={{ height }} className="w-full">
      <Card className="h-full">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2">
            <Satellite className="h-5 w-5" />
            Satellite View - Transformer Locations ({locations.length} total)
          </CardTitle>

          {/* Status Summary */}
          <div className="flex flex-wrap gap-2">
            {Object.entries(statusCounts).map(([status, count]) => (
              <Badge key={status} className={getStatusBadgeColor(status)}>
                {status}: {count}
              </Badge>
            ))}
          </div>
        </CardHeader>

        <CardContent className="h-full overflow-y-auto">
          <div className="space-y-3">
            {locations.map((location) => (
              <Card key={location.id} className="p-4 hover:shadow-md transition-shadow border-l-4"
                    style={{ borderLeftColor: getStatusColor(location.status) }}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <div
                        className="w-4 h-4 rounded-full border-2 border-white shadow-sm"
                        style={{ backgroundColor: getStatusColor(location.status) }}
                      />
                      <h3 className="font-semibold text-lg">{location.title}</h3>
                      <Badge className={getStatusBadgeColor(location.status)}>
                        {location.status}
                      </Badge>
                    </div>

                    <p className="text-gray-600 mb-2">{location.description}</p>

                    <div className="text-sm text-gray-500">
                      <div className="flex items-center gap-1 mb-1">
                        <MapPin className="h-3 w-3" />
                        Coordinates: {location.latitude.toFixed(4)}, {location.longitude.toFixed(4)}
                      </div>
                      {location.data && (
                        <div className="mt-2 space-y-1">
                          <div>🆔 ID: {location.data.id}</div>
                          {location.data.serialNumber && (
                            <div>🔢 Serial: {location.data.serialNumber}</div>
                          )}
                          {location.data.capacity && (
                            <div>⚡ Capacity: {location.data.capacity} kVA</div>
                          )}
                          {location.data.manufacturer && (
                            <div>🏭 Manufacturer: {location.data.manufacturer}</div>
                          )}
                          {location.data.model && (
                            <div>📋 Model: {location.data.model}</div>
                          )}
                          {location.data.location && (
                            <div>📍 Location: {location.data.location.name}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="ml-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onMarkerClick && onMarkerClick(location)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {locations.length === 0 && (
            <div className="text-center py-12">
              <Satellite className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No transformer locations found</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

interface LeafletMapProps {
  locations: MapLocation[]
  height?: string
  onMarkerClick?: (location: MapLocation) => void
  clustered?: boolean
}

export function LeafletMap({
  locations,
  height = "500px",
  onMarkerClick,
  clustered = true
}: LeafletMapProps) {
  // Use the simple satellite view component instead of complex Leaflet
  return (
    <SatelliteMapView
      locations={locations}
      height={height}
      onMarkerClick={onMarkerClick}
    />
  )
}
