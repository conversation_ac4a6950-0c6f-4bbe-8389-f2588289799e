"use client"

import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { 
  Upload, Download, Plus, RefreshCw
} from "lucide-react"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/src/components/ui/tooltip"

interface UserActionButtonsProps {
  onAddUser: () => void
  onImport: () => void
  onExport: () => void
  onRefresh: () => void
}

export function UserActionButtons({ 
  onAddUser, 
  onImport, 
  onExport, 
  onRefresh 
}: UserActionButtonsProps) {
  return (
    <div className="flex items-center gap-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="outline" size="sm" onClick={onImport}>
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Import users from CSV or Excel</p>
          </TooltipContent>
        </Tooltip>
        
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="outline" size="sm" onClick={onExport}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Export users to file</p>
          </TooltipContent>
        </Tooltip>
        
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="outline" size="sm" onClick={onRefresh}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Refresh user list</p>
          </TooltipContent>
        </Tooltip>
        
        <Tooltip>
          <TooltipTrigger asChild>
            <Button size="sm" onClick={onAddUser}>
              <Plus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Create a new user</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  )
}
