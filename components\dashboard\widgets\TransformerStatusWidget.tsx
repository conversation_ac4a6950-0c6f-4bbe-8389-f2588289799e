"use client"

import { useState, useEffect } from 'react'
import { Zap, CheckCircle, AlertTriangle, AlertCircle, BarChart2 } from 'lucide-react'
import { TransformerStatusWidget as TransformerStatusWidgetType } from '../../../types/dashboard-widgets'

interface TransformerStatusWidgetProps {
  widget: TransformerStatusWidgetType
  isEditing: boolean
}

export default function TransformerStatusWidget({ widget, isEditing }: TransformerStatusWidgetProps) {
  const [data, setData] = useState({
    total: 135,
    byStatus: {
      operational: 119,
      maintenance: 8,
      warning: 5,
      critical: 3
    },
    byRegion: [
      { name: 'Addis Ababa', count: 42, operational: 38, maintenance: 2, warning: 1, critical: 1 },
      { name: 'Oromia', count: 35, operational: 31, maintenance: 2, warning: 1, critical: 1 },
      { name: '<PERSON><PERSON>', count: 28, operational: 25, maintenance: 1, warning: 2, critical: 0 },
      { name: 'SNNPR', count: 20, operational: 17, maintenance: 2, warning: 0, critical: 1 },
      { name: 'Tigray', count: 10, operational: 8, maintenance: 1, warning: 1, critical: 0 }
    ],
    change: {
      total: 5,
      operational: 3,
      maintenance: 0,
      warning: 1,
      critical: 1
    }
  })
  
  const [isLoading, setIsLoading] = useState(false)
  const [displayMode, setDisplayMode] = useState<'cards' | 'chart'>(
    widget.settings?.displayType || 'cards'
  )
  
  // Fetch data
  useEffect(() => {
    // In a real implementation, this would fetch from the API
    // For now, we're using mock data initialized above
  }, [widget.id])
  
  // Refresh data
  const refreshData = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }
  
  // Toggle display mode
  const toggleDisplayMode = () => {
    setDisplayMode(prev => prev === 'cards' ? 'chart' : 'cards')
  }
  
  // Render status cards
  const renderStatusCards = () => (
    <div className="grid grid-cols-2 gap-3">
      <div className="bg-gray-50 rounded-lg p-3">
        <div className="flex items-center space-x-2 mb-1">
          <Zap size={16} className="text-green-600" />
          <span className="text-sm font-medium">Total</span>
        </div>
        <div className="flex items-baseline">
          <span className="text-2xl font-bold">{data.total}</span>
          <span className="ml-2 text-xs text-green-600 bg-green-50 rounded-full px-1.5 py-0.5">
            +{data.change.total}
          </span>
        </div>
      </div>
      
      <div className="bg-gray-50 rounded-lg p-3">
        <div className="flex items-center space-x-2 mb-1">
          <CheckCircle size={16} className="text-green-600" />
          <span className="text-sm font-medium">Operational</span>
        </div>
        <div className="flex items-baseline">
          <span className="text-2xl font-bold">{data.byStatus.operational}</span>
          <span className="ml-2 text-xs text-green-600 bg-green-50 rounded-full px-1.5 py-0.5">
            +{data.change.operational}
          </span>
        </div>
      </div>
      
      <div className="bg-gray-50 rounded-lg p-3">
        <div className="flex items-center space-x-2 mb-1">
          <AlertCircle size={16} className="text-orange-500" />
          <span className="text-sm font-medium">Warning</span>
        </div>
        <div className="flex items-baseline">
          <span className="text-2xl font-bold">{data.byStatus.warning}</span>
          <span className="ml-2 text-xs text-orange-600 bg-orange-50 rounded-full px-1.5 py-0.5">
            +{data.change.warning}
          </span>
        </div>
      </div>
      
      <div className="bg-gray-50 rounded-lg p-3">
        <div className="flex items-center space-x-2 mb-1">
          <AlertTriangle size={16} className="text-red-500" />
          <span className="text-sm font-medium">Critical</span>
        </div>
        <div className="flex items-baseline">
          <span className="text-2xl font-bold">{data.byStatus.critical}</span>
          <span className="ml-2 text-xs text-red-600 bg-red-50 rounded-full px-1.5 py-0.5">
            +{data.change.critical}
          </span>
        </div>
      </div>
    </div>
  )
  
  // Render chart
  const renderChart = () => (
    <div className="h-48 flex items-center justify-center bg-gray-50 rounded-lg">
      <div className="text-center">
        <BarChart2 size={32} className="mx-auto text-gray-300 mb-2" />
        <p className="text-gray-500 text-sm">Chart visualization would appear here</p>
      </div>
    </div>
  )
  
  // Render region table
  const renderRegionTable = () => (
    <div className="mt-4">
      <h4 className="text-sm font-medium mb-2">By Region</h4>
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="bg-gray-50">
              <th className="px-2 py-1 text-left font-medium">Region</th>
              <th className="px-2 py-1 text-right font-medium">Total</th>
              <th className="px-2 py-1 text-right font-medium">Operational</th>
              <th className="px-2 py-1 text-right font-medium">Issues</th>
            </tr>
          </thead>
          <tbody>
            {data.byRegion.map(region => (
              <tr key={region.name} className="border-t">
                <td className="px-2 py-1">{region.name}</td>
                <td className="px-2 py-1 text-right">{region.count}</td>
                <td className="px-2 py-1 text-right text-green-600">{region.operational}</td>
                <td className="px-2 py-1 text-right text-orange-500">
                  {region.warning + region.critical}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
  
  return (
    <div>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center space-x-2">
          <button
            className={`p-1 rounded ${displayMode === 'cards' ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setDisplayMode('cards')}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2" />
              <rect x="3" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2" />
              <rect x="14" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2" />
              <rect x="14" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2" />
            </svg>
          </button>
          
          <button
            className={`p-1 rounded ${displayMode === 'chart' ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setDisplayMode('chart')}
          >
            <BarChart2 size={16} />
          </button>
        </div>
        
        <div className="text-xs text-gray-500">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex items-center justify-center h-48">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-green-500"></div>
        </div>
      ) : (
        <>
          {displayMode === 'cards' ? renderStatusCards() : renderChart()}
          
          {widget.settings?.showByRegion && renderRegionTable()}
        </>
      )}
    </div>
  )
}
