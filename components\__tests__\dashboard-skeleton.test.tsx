import React from 'react'
import { render } from '@testing-library/react'
import { DashboardSkeleton } from '@/components/dashboard-skeleton'

describe('DashboardSkeleton', () => {
  it('renders the skeleton loading state', () => {
    const { container } = render(<DashboardSkeleton />)
    
    // Check for the main container
    expect(container.firstChild).toHaveClass('flex', 'flex-col', 'gap-4')
    
    // Check for the header skeleton
    const headerSkeletons = container.querySelectorAll('.h-8.w-48.mb-2, .h-4.w-64')
    expect(headerSkeletons.length).toBe(2)
    
    // Check for the stat card skeletons
    const statCardSkeletons = container.querySelectorAll('.grid.gap-4.md\\:grid-cols-2.lg\\:grid-cols-4 .h-28')
    expect(statCardSkeletons.length).toBe(4)
    
    // Check for the tab skeleton
    const tabSkeleton = container.querySelector('.h-10.w-full.mt-4.mb-2')
    expect(tabSkeleton).toBeInTheDocument()
    
    // Check for the main content skeletons
    const mainContentSkeletons = container.querySelectorAll('.lg\\:col-span-4.h-80, .lg\\:col-span-3.h-80')
    expect(mainContentSkeletons.length).toBe(2)
    
    // Check for the bottom card skeletons
    const bottomCardSkeletons = container.querySelectorAll('.grid.gap-4.md\\:grid-cols-2.lg\\:grid-cols-3 .h-64')
    expect(bottomCardSkeletons.length).toBe(3)
  })
})
