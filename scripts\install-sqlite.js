/**
 * Install SQLite Script
 * 
 * This script installs the better-sqlite3 package.
 */

const { execSync } = require('child_process');

// Install better-sqlite3
console.log('Installing better-sqlite3...');
try {
  execSync('npm install better-sqlite3@9.4.3', { stdio: 'inherit' });
  console.log('better-sqlite3 installed successfully.');
} catch (error) {
  console.error('Error installing better-sqlite3:', error);
  process.exit(1);
}
