import { Transformer, TransformerLocation } from "@/src/types/transformer";

/**
 * Utility functions for validating transformer data
 */

/**
 * Check if a transformer object has all required fields
 */
export function isValidTransformer(transformer: any): transformer is Transformer {
  if (!transformer) return false;
  
  return (
    typeof transformer.id === 'string' &&
    typeof transformer.serialNumber === 'string' &&
    typeof transformer.manufacturer === 'string' &&
    typeof transformer.model === 'string' &&
    typeof transformer.type === 'string' &&
    typeof transformer.capacity === 'string' &&
    typeof transformer.installationDate === 'string' &&
    typeof transformer.lastMaintenanceDate === 'string' &&
    typeof transformer.lastInspectionDate === 'string' &&
    typeof transformer.status === 'string' &&
    isValidTransformerLocation(transformer.location)
  );
}

/**
 * Check if a transformer location object has all required fields
 */
export function isValidTransformerLocation(location: any): location is TransformerLocation {
  if (!location) return false;
  
  return (
    typeof location.latitude === 'string' &&
    typeof location.longitude === 'string' &&
    typeof location.region === 'string'
  );
}

/**
 * Sanitize a transformer object by providing default values for missing fields
 */
export function sanitizeTransformer(transformer: Partial<Transformer>): Transformer {
  if (!transformer) {
    throw new Error("Cannot sanitize null or undefined transformer");
  }
  
  return {
    id: transformer.id || `tr-${Date.now()}`,
    serialNumber: transformer.serialNumber || "Unknown",
    manufacturer: transformer.manufacturer || "Unknown",
    model: transformer.model || "Unknown",
    type: transformer.type || "Distribution",
    capacity: transformer.capacity || "0",
    voltageRating: transformer.voltageRating || "15/0.4 kV",
    installationDate: transformer.installationDate || new Date().toISOString().split('T')[0],
    lastMaintenanceDate: transformer.lastMaintenanceDate || "N/A",
    lastInspectionDate: transformer.lastInspectionDate || "N/A",
    status: (transformer.status as any) || "Operational",
    location: sanitizeTransformerLocation(transformer.location),
    maintenanceHistory: transformer.maintenanceHistory || [],
    inspectionRecords: transformer.inspectionRecords || [],
    testResults: transformer.testResults || [],
  };
}

/**
 * Sanitize a transformer location object by providing default values for missing fields
 */
export function sanitizeTransformerLocation(location?: Partial<TransformerLocation>): TransformerLocation {
  if (!location) {
    return {
      latitude: "9.0222",
      longitude: "38.7468",
      region: "Addis Ababa",
      serviceCenter: "Bole",
      address: "Unknown",
      installationSite: "Unknown"
    };
  }
  
  return {
    latitude: location.latitude || "9.0222",
    longitude: location.longitude || "38.7468",
    region: location.region || "Addis Ababa",
    serviceCenter: location.serviceCenter || "Bole",
    address: location.address || "Unknown",
    installationSite: location.installationSite || "Unknown"
  };
}

/**
 * Parse a string to a number safely
 */
export function safeParseFloat(value: string | undefined | null, defaultValue: number = 0): number {
  if (!value) return defaultValue;
  
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Format a capacity value with kVA unit
 */
export function formatCapacity(capacity: string | undefined | null): string {
  if (!capacity) return "Unknown kVA";
  
  return `${capacity} kVA`;
}
