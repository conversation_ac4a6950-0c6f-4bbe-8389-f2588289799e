/**
 * Seed All Dashboard Component Data for EEU DTMS
 * This script populates all 18 dashboard tables with comprehensive data
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function seedAllDashboardData() {
  let connection;
  
  try {
    console.log('🌱 SEEDING ALL DASHBOARD COMPONENT DATA');
    console.log('=' .repeat(70));
    console.log('🏢 Ethiopian Electric Utility');
    console.log('🔌 Digital Transformer Management System');
    console.log('📅 Data Seeding Date:', new Date().toLocaleString());
    console.log('=' .repeat(70));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // 1. Seed Regions
    console.log('\n🗺️  SEEDING ETHIOPIAN REGIONS');
    console.log('-' .repeat(40));
    
    const regions = [
      { name: 'Addis Ababa', code: 'AA', population: 3500000, area_km2: 527.0, capital_city: 'Addis Ababa', coordinates: JSON.stringify({lat: 9.0222, lng: 38.7468}), weather_station_id: 'ETH-AA-001', grid_connection_status: 'connected' },
      { name: 'Oromia', code: 'OR', population: 37000000, area_km2: 353006.81, capital_city: 'Adama', coordinates: JSON.stringify({lat: 8.5400, lng: 39.2675}), weather_station_id: 'ETH-OR-001', grid_connection_status: 'connected' },
      { name: 'Amhara', code: 'AM', population: 21000000, area_km2: 154708.96, capital_city: 'Bahir Dar', coordinates: JSON.stringify({lat: 11.5959, lng: 37.3906}), weather_station_id: 'ETH-AM-001', grid_connection_status: 'connected' },
      { name: 'Tigray', code: 'TI', population: 5500000, area_km2: 50078.64, capital_city: 'Mekelle', coordinates: JSON.stringify({lat: 13.4967, lng: 39.4753}), weather_station_id: 'ETH-TI-001', grid_connection_status: 'partial' },
      { name: 'SNNP', code: 'SN', population: 20000000, area_km2: 105887.18, capital_city: 'Hawassa', coordinates: JSON.stringify({lat: 7.0621, lng: 38.4776}), weather_station_id: 'ETH-SN-001', grid_connection_status: 'connected' },
      { name: 'Somali', code: 'SO', population: 5500000, area_km2: 279252.0, capital_city: 'Jijiga', coordinates: JSON.stringify({lat: 9.3500, lng: 42.8000}), weather_station_id: 'ETH-SO-001', grid_connection_status: 'partial' },
      { name: 'Afar', code: 'AF', population: 1800000, area_km2: 96707.0, capital_city: 'Semera', coordinates: JSON.stringify({lat: 11.7943, lng: 41.0058}), weather_station_id: 'ETH-AF-001', grid_connection_status: 'partial' },
      { name: 'Benishangul-Gumuz', code: 'BG', population: 1100000, area_km2: 50699.0, capital_city: 'Assosa', coordinates: JSON.stringify({lat: 10.0667, lng: 34.5333}), weather_station_id: 'ETH-BG-001', grid_connection_status: 'connected' }
    ];

    for (const region of regions) {
      await connection.execute(`
        INSERT INTO dtms_regions (name, code, population, area_km2, capital_city, coordinates, weather_station_id, grid_connection_status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        population = VALUES(population), area_km2 = VALUES(area_km2), capital_city = VALUES(capital_city),
        coordinates = VALUES(coordinates), weather_station_id = VALUES(weather_station_id), grid_connection_status = VALUES(grid_connection_status)
      `, [region.name, region.code, region.population, region.area_km2, region.capital_city, region.coordinates, region.weather_station_id, region.grid_connection_status]);
    }
    console.log(`✅ Seeded ${regions.length} Ethiopian regions`);

    // 2. Seed Users
    console.log('\n👥 SEEDING SYSTEM USERS');
    console.log('-' .repeat(40));
    
    const users = [
      { email: '<EMAIL>', password_hash: '$2b$10$example_hash_admin', name: 'System Administrator', role: 'super_admin', department: 'IT', region_id: 1, phone: '+251-911-000-001', is_active: true, preferences: JSON.stringify({theme: 'light', language: 'en', notifications: true}), permissions: JSON.stringify({all: true}) },
      { email: '<EMAIL>', password_hash: '$2b$10$example_hash_ceo', name: 'Ato Belete Molla', role: 'super_admin', department: 'Executive', region_id: 1, phone: '+251-911-000-002', is_active: true, preferences: JSON.stringify({theme: 'light', language: 'en', notifications: true}), permissions: JSON.stringify({executive: true}) },
      { email: '<EMAIL>', password_hash: '$2b$10$example_hash_asset', name: 'Eng. Alemayehu Worku', role: 'national_asset_manager', department: 'Asset Management', region_id: 1, phone: '+251-911-000-003', is_active: true, preferences: JSON.stringify({theme: 'light', language: 'en', notifications: true}), permissions: JSON.stringify({assets: true, reports: true}) },
      { email: '<EMAIL>', password_hash: '$2b$10$example_hash_maint', name: 'Eng. Tigist Mengistu', role: 'national_maintenance_manager', department: 'Maintenance', region_id: 1, phone: '+251-911-000-004', is_active: true, preferences: JSON.stringify({theme: 'light', language: 'en', notifications: true}), permissions: JSON.stringify({maintenance: true, work_orders: true}) },
      { email: '<EMAIL>', password_hash: '$2b$10$example_hash_addis', name: 'W/ro Hanan Ahmed', role: 'regional_admin', department: 'Regional Operations', region_id: 1, phone: '+251-911-000-010', is_active: true, preferences: JSON.stringify({theme: 'light', language: 'en', notifications: true}), permissions: JSON.stringify({regional: true}) },
      { email: '<EMAIL>', password_hash: '$2b$10$example_hash_oromia', name: 'Ato Tadesse Bekele', role: 'regional_admin', department: 'Regional Operations', region_id: 2, phone: '+251-911-000-011', is_active: true, preferences: JSON.stringify({theme: 'light', language: 'en', notifications: true}), permissions: JSON.stringify({regional: true}) },
      { email: '<EMAIL>', password_hash: '$2b$10$example_hash_tech1', name: 'Ato Dawit Tesfaye', role: 'field_technician', department: 'Field Operations', region_id: 1, phone: '+251-911-000-020', is_active: true, preferences: JSON.stringify({theme: 'light', language: 'en', notifications: true}), permissions: JSON.stringify({field: true}) },
      { email: '<EMAIL>', password_hash: '$2b$10$example_hash_tech2', name: 'Ato Mohammed Ali', role: 'field_technician', department: 'Field Operations', region_id: 2, phone: '+251-911-000-021', is_active: true, preferences: JSON.stringify({theme: 'light', language: 'en', notifications: true}), permissions: JSON.stringify({field: true}) }
    ];

    for (const user of users) {
      await connection.execute(`
        INSERT INTO dtms_users (email, password_hash, name, role, department, region_id, phone, is_active, preferences, permissions)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        name = VALUES(name), role = VALUES(role), department = VALUES(department), 
        region_id = VALUES(region_id), phone = VALUES(phone), is_active = VALUES(is_active),
        preferences = VALUES(preferences), permissions = VALUES(permissions)
      `, [user.email, user.password_hash, user.name, user.role, user.department, user.region_id, user.phone, user.is_active, user.preferences, user.permissions]);
    }
    console.log(`✅ Seeded ${users.length} system users`);

    // 3. Seed Service Centers
    console.log('\n🏢 SEEDING SERVICE CENTERS');
    console.log('-' .repeat(40));
    
    const serviceCenters = [
      { name: 'Addis Ababa Main Service Center', code: 'AA-MAIN', region_id: 1, address: 'Bole Road, Addis Ababa', latitude: 9.0222, longitude: 38.7468, phone: '+251-11-123-4567', email: '<EMAIL>', manager_id: 5, capacity: 100, status: 'active', operating_hours: JSON.stringify({weekdays: '08:00-17:00', weekends: '08:00-12:00'}), emergency_contact: '+251-911-000-100' },
      { name: 'Oromia Regional Service Center', code: 'OR-MAIN', region_id: 2, address: 'Adama City Center', latitude: 8.5400, longitude: 39.2675, phone: '+251-22-123-4567', email: '<EMAIL>', manager_id: 6, capacity: 80, status: 'active', operating_hours: JSON.stringify({weekdays: '08:00-17:00', weekends: '08:00-12:00'}), emergency_contact: '+251-911-000-101' },
      { name: 'Amhara Regional Service Center', code: 'AM-MAIN', region_id: 3, address: 'Bahir Dar City Center', latitude: 11.5959, longitude: 37.3906, phone: '+251-58-123-4567', email: '<EMAIL>', capacity: 60, status: 'active', operating_hours: JSON.stringify({weekdays: '08:00-17:00', weekends: '08:00-12:00'}), emergency_contact: '+251-911-000-102' },
      { name: 'Tigray Regional Service Center', code: 'TI-MAIN', region_id: 4, address: 'Mekelle City Center', latitude: 13.4967, longitude: 39.4753, phone: '+251-34-123-4567', email: '<EMAIL>', capacity: 40, status: 'active', operating_hours: JSON.stringify({weekdays: '08:00-17:00', weekends: '08:00-12:00'}), emergency_contact: '+251-911-000-103' },
      { name: 'SNNP Regional Service Center', code: 'SN-MAIN', region_id: 5, address: 'Hawassa City Center', latitude: 7.0621, longitude: 38.4776, phone: '+251-46-123-4567', email: '<EMAIL>', capacity: 50, status: 'active', operating_hours: JSON.stringify({weekdays: '08:00-17:00', weekends: '08:00-12:00'}), emergency_contact: '+251-911-000-104' }
    ];

    for (const center of serviceCenters) {
      await connection.execute(`
        INSERT INTO dtms_service_centers (name, code, region_id, address, latitude, longitude, phone, email, manager_id, capacity, status, operating_hours, emergency_contact)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        name = VALUES(name), address = VALUES(address), latitude = VALUES(latitude), longitude = VALUES(longitude),
        phone = VALUES(phone), email = VALUES(email), manager_id = VALUES(manager_id), capacity = VALUES(capacity),
        status = VALUES(status), operating_hours = VALUES(operating_hours), emergency_contact = VALUES(emergency_contact)
      `, [center.name, center.code, center.region_id, center.address, center.latitude, center.longitude, center.phone, center.email, center.manager_id || null, center.capacity, center.status, center.operating_hours, center.emergency_contact]);
    }
    console.log(`✅ Seeded ${serviceCenters.length} service centers`);

    // 4. Seed System Settings
    console.log('\n⚙️  SEEDING SYSTEM SETTINGS');
    console.log('-' .repeat(40));
    
    const systemSettings = [
      { setting_key: 'temperature_threshold_warning', setting_value: '70', data_type: 'number', category: 'alerts', description: 'Temperature threshold for warning alerts (°C)', is_public: true, is_editable: true, validation_rules: JSON.stringify({min: 50, max: 100}), updated_by: 1 },
      { setting_key: 'temperature_threshold_critical', setting_value: '85', data_type: 'number', category: 'alerts', description: 'Temperature threshold for critical alerts (°C)', is_public: true, is_editable: true, validation_rules: JSON.stringify({min: 70, max: 120}), updated_by: 1 },
      { setting_key: 'load_factor_threshold', setting_value: '85', data_type: 'number', category: 'alerts', description: 'Load factor threshold for alerts (%)', is_public: true, is_editable: true, validation_rules: JSON.stringify({min: 50, max: 100}), updated_by: 1 },
      { setting_key: 'maintenance_reminder_days', setting_value: '7', data_type: 'number', category: 'maintenance', description: 'Days before maintenance to send reminder', is_public: true, is_editable: true, validation_rules: JSON.stringify({min: 1, max: 30}), updated_by: 1 },
      { setting_key: 'system_timezone', setting_value: 'Africa/Addis_Ababa', data_type: 'string', category: 'system', description: 'System timezone', is_public: true, is_editable: true, validation_rules: JSON.stringify({type: 'timezone'}), updated_by: 1 },
      { setting_key: 'dashboard_refresh_interval', setting_value: '300', data_type: 'number', category: 'dashboard', description: 'Dashboard auto-refresh interval (seconds)', is_public: true, is_editable: true, validation_rules: JSON.stringify({min: 30, max: 3600}), updated_by: 1 },
      { setting_key: 'email_notifications_enabled', setting_value: 'true', data_type: 'boolean', category: 'notifications', description: 'Enable email notifications', is_public: true, is_editable: true, validation_rules: JSON.stringify({type: 'boolean'}), updated_by: 1 },
      { setting_key: 'sms_notifications_enabled', setting_value: 'false', data_type: 'boolean', category: 'notifications', description: 'Enable SMS notifications', is_public: true, is_editable: true, validation_rules: JSON.stringify({type: 'boolean'}), updated_by: 1 },
      { setting_key: 'company_name', setting_value: 'Ethiopian Electric Utility', data_type: 'string', category: 'company', description: 'Company name', is_public: true, is_editable: false, validation_rules: JSON.stringify({type: 'string'}), updated_by: 1 },
      { setting_key: 'system_version', setting_value: '1.0.0', data_type: 'string', category: 'system', description: 'System version', is_public: true, is_editable: false, validation_rules: JSON.stringify({type: 'version'}), updated_by: 1 }
    ];

    for (const setting of systemSettings) {
      await connection.execute(`
        INSERT INTO dtms_system_settings (setting_key, setting_value, data_type, category, description, is_public, is_editable, validation_rules, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        setting_value = VALUES(setting_value), data_type = VALUES(data_type), category = VALUES(category),
        description = VALUES(description), is_public = VALUES(is_public), is_editable = VALUES(is_editable),
        validation_rules = VALUES(validation_rules), updated_by = VALUES(updated_by)
      `, [setting.setting_key, setting.setting_value, setting.data_type, setting.category, setting.description, setting.is_public, setting.is_editable, setting.validation_rules, setting.updated_by]);
    }
    console.log(`✅ Seeded ${systemSettings.length} system settings`);

    console.log('\n✅ Core dashboard data seeded successfully!');
    console.log('📊 Ready to seed transformers and operational data...');
    
  } catch (error) {
    console.error('❌ Error seeding dashboard data:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Export for use in other scripts
module.exports = { seedAllDashboardData };

// Run if called directly
if (require.main === module) {
  seedAllDashboardData();
}
