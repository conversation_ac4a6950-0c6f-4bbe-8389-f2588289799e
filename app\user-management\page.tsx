import { MainLayout } from "@/src/components/layout/main-layout"
import { UserManagementContent } from "@/components/user-management-content"

export default function UserManagementPage() {
  return (
    <MainLayout
      allowedRoles={["super_admin", "regional_admin", "service_center_manager"]}
      requiredPermissions={[{ resource: "users", action: "read" }]}
    >
      <UserManagementContent />
    </MainLayout>
  )
}
