"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { 
  FileText, 
  Download, 
  Calendar, 
  BarChart3, 
  TrendingUp,
  Clock,
  Eye,
  Plus,
  Filter
} from "lucide-react"

export function ReportsPanel() {
  const reports = [
    {
      id: 1,
      title: "Monthly Performance Report",
      description: "Comprehensive system performance analysis",
      type: "Performance",
      period: "December 2024",
      status: "ready",
      size: "2.4 MB",
      lastGenerated: "2024-01-10"
    },
    {
      id: 2,
      title: "Maintenance Summary",
      description: "Completed and scheduled maintenance activities",
      type: "Maintenance",
      period: "Q4 2024",
      status: "generating",
      size: "1.8 MB",
      lastGenerated: "2024-01-08"
    },
    {
      id: 3,
      title: "Asset Health Report",
      description: "Transformer condition and health metrics",
      type: "Asset Health",
      period: "December 2024",
      status: "ready",
      size: "3.2 MB",
      lastGenerated: "2024-01-09"
    },
    {
      id: 4,
      title: "Energy Efficiency Analysis",
      description: "System efficiency and loss analysis",
      type: "Efficiency",
      period: "Annual 2024",
      status: "scheduled",
      size: "4.1 MB",
      lastGenerated: "2024-01-05"
    }
  ]

  const quickReports = [
    { name: "Daily Summary", icon: Calendar, description: "Today's operations" },
    { name: "Performance Metrics", icon: BarChart3, description: "Key performance indicators" },
    { name: "Alert Summary", icon: TrendingUp, description: "Recent alerts and issues" },
    { name: "Maintenance Log", icon: Clock, description: "Recent maintenance activities" }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge variant="outline" className="text-green-600">Ready</Badge>
      case "generating":
        return <Badge variant="default">Generating</Badge>
      case "scheduled":
        return <Badge variant="secondary">Scheduled</Badge>
      case "error":
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Reports & Analytics
          </div>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Report
          </Button>
        </CardTitle>
        <CardDescription>
          Generate and download system reports
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Quick Reports */}
        <div className="mb-6">
          <h4 className="font-medium mb-3">Quick Reports</h4>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
            {quickReports.map((report, index) => {
              const IconComponent = report.icon
              return (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-3 flex flex-col items-center gap-2"
                >
                  <IconComponent className="h-5 w-5" />
                  <div className="text-center">
                    <div className="text-sm font-medium">{report.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {report.description}
                    </div>
                  </div>
                </Button>
              )
            })}
          </div>
        </div>

        {/* Recent Reports */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Recent Reports</h4>
            <Button variant="ghost" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
          
          {reports.map((report) => (
            <div key={report.id} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-start justify-between">
                <div>
                  <h5 className="font-medium">{report.title}</h5>
                  <p className="text-sm text-muted-foreground">
                    {report.description}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(report.status)}
                  {report.status === "ready" && (
                    <Button size="sm" variant="outline">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-muted-foreground">
                <div>
                  <span className="font-medium">Type:</span> {report.type}
                </div>
                <div>
                  <span className="font-medium">Period:</span> {report.period}
                </div>
                <div>
                  <span className="font-medium">Size:</span> {report.size}
                </div>
                <div>
                  <span className="font-medium">Generated:</span> {report.lastGenerated}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View All Reports
            </Button>
            <div className="text-sm text-muted-foreground">
              {reports.filter(report => report.status === 'ready').length} reports ready
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
