"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/src/components/ui/form"
import { Input } from "@/src/components/ui/input"
import { Button } from "@/src/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Textarea } from "@/src/components/ui/textarea"
import { useToast } from "@/src/components/ui/use-toast"
import { Loader2, Wrench, Calendar, Clock } from "lucide-react"

const maintenanceSchema = z.object({
  transformerId: z.string().min(1, "Transformer is required"),
  type: z.string().min(1, "Maintenance type is required"),
  priority: z.string().min(1, "Priority is required"),
  scheduledDate: z.string().min(1, "Scheduled date is required"),
  scheduledTime: z.string().min(1, "Scheduled time is required"),
  estimatedDuration: z.number().min(1, "Estimated duration is required"),
  assignedTeam: z.string().min(1, "Assigned team is required"),
  description: z.string().min(1, "Description is required"),
  requiredParts: z.string().optional(),
  safetyRequirements: z.string().optional(),
  notes: z.string().optional(),
})

type MaintenanceFormData = z.infer<typeof maintenanceSchema>

interface ScheduleMaintenanceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function ScheduleMaintenanceDialog({ open, onOpenChange, onSuccess }: ScheduleMaintenanceDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const form = useForm<MaintenanceFormData>({
    resolver: zodResolver(maintenanceSchema),
    defaultValues: {
      transformerId: "",
      type: "",
      priority: "",
      scheduledDate: "",
      scheduledTime: "",
      estimatedDuration: 0,
      assignedTeam: "",
      description: "",
      requiredParts: "",
      safetyRequirements: "",
      notes: "",
    },
  })

  const onSubmit = async (data: MaintenanceFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/mysql/dashboard/actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'scheduleMaintenance',
          payload: data,
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Maintenance Scheduled",
          description: "Maintenance has been successfully scheduled.",
        })
        form.reset()
        onOpenChange(false)
        onSuccess()
      } else {
        throw new Error(result.message || 'Failed to schedule maintenance')
      }
    } catch (error) {
      console.error('Error scheduling maintenance:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to schedule maintenance",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Wrench className="h-5 w-5 mr-2" />
            Schedule Maintenance
          </DialogTitle>
          <DialogDescription>
            Schedule maintenance work for a transformer in the system.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="transformerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Transformer</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select transformer" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="transformer-001">Addis Ababa Main Transformer</SelectItem>
                          <SelectItem value="transformer-002">Bahir Dar Distribution Center</SelectItem>
                          <SelectItem value="transformer-003">Hawassa Power Station</SelectItem>
                          <SelectItem value="transformer-004">Mekelle Regional Hub</SelectItem>
                          <SelectItem value="transformer-005">Jimma Service Center</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maintenance Type</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="preventive">Preventive Maintenance</SelectItem>
                          <SelectItem value="corrective">Corrective Maintenance</SelectItem>
                          <SelectItem value="emergency">Emergency Repair</SelectItem>
                          <SelectItem value="inspection">Routine Inspection</SelectItem>
                          <SelectItem value="testing">Performance Testing</SelectItem>
                          <SelectItem value="upgrade">System Upgrade</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="critical">Critical</SelectItem>
                          <SelectItem value="emergency">Emergency</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="assignedTeam"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assigned Team</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select team" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="team-alpha">Team Alpha (Addis Ababa)</SelectItem>
                          <SelectItem value="team-beta">Team Beta (Oromia)</SelectItem>
                          <SelectItem value="team-gamma">Team Gamma (Amhara)</SelectItem>
                          <SelectItem value="team-delta">Team Delta (SNNPR)</SelectItem>
                          <SelectItem value="team-epsilon">Team Epsilon (Tigray)</SelectItem>
                          <SelectItem value="emergency-team">Emergency Response Team</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="scheduledDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Scheduled Date
                    </FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="scheduledTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      Scheduled Time
                    </FormLabel>
                    <FormControl>
                      <Input type="time" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estimatedDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Duration (hours)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.5"
                        min="0.5"
                        max="24"
                        placeholder="e.g., 4"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Estimated time to complete the maintenance work
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the maintenance work to be performed..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Detailed description of the maintenance activities
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="requiredParts"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Required Parts (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="List any spare parts or materials needed..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Parts and materials required for the maintenance
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="safetyRequirements"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Safety Requirements (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Specify safety procedures and requirements..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Safety procedures and protective equipment required
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional notes or special instructions..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Schedule Maintenance
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
