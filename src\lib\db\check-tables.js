const mysql = require('mysql2');

// Database connection configuration
const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '', // Replace with your MySQL root password
  database: 'dtms_eeu_db',
});

// Query to check existing tables
const query = `SHOW TABLES;`;

connection.query(query, (error, results) => {
  if (error) {
    console.error('Error fetching tables:', error);
  } else {
    console.log('Existing tables in the database:');
    results.forEach((row) => {
      console.log(Object.values(row)[0]);
    });
  }

  // Close the connection
  connection.end();
});
