/**
 * Database loader utility for EEU Transformer Management System
 */

interface DbService {
  getTransformers: (filters?: any) => Promise<any[]>
  getTransformerById: (id: string) => Promise<any>
  createTransformer: (data: any) => Promise<any>
  updateTransformer: (id: string, data: any) => Promise<any>
  deleteTransformer: (id: string) => Promise<boolean>
  getTransformerStatistics: () => Promise<any>
}

// Mock database service for development
class MockDbService implements DbService {
  async getTransformers(filters?: any): Promise<any[]> {
    // Return mock transformer data
    return Array.from({ length: 50 }, (_, i) => ({
      id: `T${String(i + 1).padStart(3, '0')}`,
      name: `Transformer ${String(i + 1).padStart(3, '0')}`,
      type: ['Distribution', 'Power', 'Instrument'][i % 3],
      capacity: ['100 kVA', '250 kVA', '500 kVA', '1000 kVA'][i % 4],
      voltage: ['11/0.4 kV', '33/11 kV', '132/33 kV'][i % 3],
      status: ['Operational', 'Warning', 'Critical', 'Maintenance', 'Offline'][i % 5],
      location: {
        region: ['Addis Ababa', 'Oromia', 'Amhara', 'Tigray', 'SNNPR'][i % 5],
        serviceCenter: `Service Center ${i % 10 + 1}`,
        address: `Address ${i + 1}`,
        coordinates: { lat: 9.0192 + (i * 0.01), lng: 38.7525 + (i * 0.01) }
      },
      specifications: {
        manufacturer: ['ABB', 'Siemens', 'Schneider', 'GE'][i % 4],
        model: `Model-${i + 1}`,
        serialNumber: `SN${String(i + 1).padStart(6, '0')}`,
        yearInstalled: 2015 + (i % 8),
        lastMaintenance: new Date(Date.now() - (i * 30 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
        nextMaintenance: new Date(Date.now() + ((30 - i) * 24 * 60 * 60 * 1000)).toISOString().split('T')[0]
      },
      performance: {
        efficiency: 90 + (i % 10),
        loadFactor: 0.6 + (i % 4) * 0.1,
        temperature: 35 + (i % 20),
        vibration: ['Normal', 'Elevated', 'High'][i % 3]
      },
      lastUpdated: new Date(Date.now() - (i * 60 * 60 * 1000)).toISOString()
    }))
  }

  async getTransformerById(id: string): Promise<any> {
    const transformers = await this.getTransformers()
    return transformers.find(t => t.id === id) || null
  }

  async createTransformer(data: any): Promise<any> {
    return {
      id: `T${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  async updateTransformer(id: string, data: any): Promise<any> {
    const existing = await this.getTransformerById(id)
    if (!existing) {
      throw new Error('Transformer not found')
    }
    return {
      ...existing,
      ...data,
      updatedAt: new Date().toISOString()
    }
  }

  async deleteTransformer(id: string): Promise<boolean> {
    const existing = await this.getTransformerById(id)
    return !!existing
  }

  async getTransformerStatistics(): Promise<any> {
    return {
      total: 150,
      byStatus: {
        operational: 120,
        warning: 15,
        critical: 8,
        maintenance: 5,
        offline: 2
      },
      byRegion: {
        'Addis Ababa': 50,
        'Oromia': 40,
        'Amhara': 30,
        'Tigray': 20,
        'SNNPR': 10
      },
      byType: {
        'Distribution': 80,
        'Power': 50,
        'Instrument': 20
      },
      performance: {
        avgEfficiency: 94.2,
        avgLoadFactor: 0.75,
        avgTemperature: 42.5
      }
    }
  }
}

// Singleton instance
let dbServiceInstance: DbService | null = null

export function getDbService(): DbService {
  if (!dbServiceInstance) {
    // In a real application, this would connect to the actual database
    // For now, we use the mock service
    dbServiceInstance = new MockDbService()
  }
  return dbServiceInstance
}

// Export types
export type { DbService }
