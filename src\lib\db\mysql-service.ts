/**
 * MySQL Database Service
 *
 * This service provides a high-level API for interacting with the MySQL database.
 * It implements the repository pattern for each entity.
 * This service is only available on the server side.
 */

import { v4 as uuidv4 } from 'uuid';
import { executeQuery, initializeSchema as initMySQLSchema, isServer } from '../mysql';
import {
  User,
  Region,
  ServiceCenter,
  Transformer,
  MaintenanceRecord,
  Alert,
  Outage,
  WeatherAlert
} from './schema';

/**
 * User repository for MySQL
 */
export const userRepository = {
  /**
   * Get all users
   */
  async getAll(): Promise<User[]> {
    const users = await executeQuery<any[]>('SELECT * FROM users');
    return users.map(mapUserFromDB);
  },

  /**
   * Get a user by ID
   */
  async getById(id: string): Promise<User | null> {
    const users = await executeQuery<any[]>('SELECT * FROM users WHERE id = ?', [id]);
    return users.length > 0 ? mapUserFromDB(users[0]) : null;
  },

  /**
   * Get a user by email
   */
  async getByEmail(email: string): Promise<User | null> {
    const users = await executeQuery<any[]>('SELECT * FROM users WHERE email = ?', [email]);
    return users.length > 0 ? mapUserFromDB(users[0]) : null;
  },

  /**
   * Create a new user
   */
  async create(user: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const id = uuidv4();
    const now = new Date().toISOString();

    await executeQuery(
      `INSERT INTO users (
        id, email, name, role, region_id, service_center_id, is_active, avatar, preferences
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        user.email,
        user.name,
        user.role,
        user.region,
        user.serviceCenter,
        user.isActive ? 1 : 0,
        user.avatar || null,
        user.preferences ? JSON.stringify(user.preferences) : null
      ]
    );

    return {
      id,
      email: user.email,
      name: user.name,
      role: user.role,
      region: user.region,
      serviceCenter: user.serviceCenter,
      isActive: user.isActive,
      avatar: user.avatar,
      preferences: user.preferences,
      createdAt: now,
      updatedAt: now
    };
  },

  /**
   * Update a user
   */
  async update(id: string, user: Partial<User>): Promise<User | null> {
    const currentUser = await this.getById(id);
    if (!currentUser) return null;

    const updates: string[] = [];
    const values: any[] = [];

    if (user.email !== undefined) {
      updates.push('email = ?');
      values.push(user.email);
    }

    if (user.name !== undefined) {
      updates.push('name = ?');
      values.push(user.name);
    }

    if (user.role !== undefined) {
      updates.push('role = ?');
      values.push(user.role);
    }

    if (user.region !== undefined) {
      updates.push('region_id = ?');
      values.push(user.region);
    }

    if (user.serviceCenter !== undefined) {
      updates.push('service_center_id = ?');
      values.push(user.serviceCenter);
    }

    if (user.isActive !== undefined) {
      updates.push('is_active = ?');
      values.push(user.isActive ? 1 : 0);
    }

    if (user.avatar !== undefined) {
      updates.push('avatar = ?');
      values.push(user.avatar);
    }

    if (user.preferences !== undefined) {
      updates.push('preferences = ?');
      values.push(JSON.stringify(user.preferences));
    }

    if (user.lastLogin !== undefined) {
      updates.push('last_login = ?');
      values.push(user.lastLogin);
    }

    if (updates.length === 0) return currentUser;

    values.push(id);

    await executeQuery(
      `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    return this.getById(id);
  },

  /**
   * Delete a user
   */
  async delete(id: string): Promise<boolean> {
    const result = await executeQuery<any>(
      'DELETE FROM users WHERE id = ?',
      [id]
    );

    return result.affectedRows > 0;
  },

  /**
   * Search users by name or email
   */
  async search(term: string): Promise<User[]> {
    const users = await executeQuery<any[]>(
      'SELECT * FROM users WHERE name LIKE ? OR email LIKE ?',
      [`%${term}%`, `%${term}%`]
    );

    return users.map(mapUserFromDB);
  }
};

/**
 * Region repository for MySQL
 */
export const regionRepository = {
  /**
   * Get all regions
   */
  async getAll(): Promise<Region[]> {
    const regions = await executeQuery<any[]>('SELECT * FROM regions');
    return regions.map(mapRegionFromDB);
  },

  /**
   * Get a region by ID
   */
  async getById(id: string): Promise<Region | null> {
    const regions = await executeQuery<any[]>('SELECT * FROM regions WHERE id = ?', [id]);
    return regions.length > 0 ? mapRegionFromDB(regions[0]) : null;
  },

  /**
   * Create a new region
   */
  async create(region: Omit<Region, 'id' | 'createdAt' | 'updatedAt'>): Promise<Region> {
    const id = uuidv4();
    const now = new Date().toISOString();

    await executeQuery(
      `INSERT INTO regions (
        id, name, code, coordinates, service_centers, transformers
      ) VALUES (?, ?, ?, ?, ?, ?)`,
      [
        id,
        region.name,
        region.code,
        JSON.stringify(region.coordinates),
        region.serviceCenters || 0,
        region.transformers || 0
      ]
    );

    return {
      id,
      name: region.name,
      code: region.code,
      coordinates: region.coordinates,
      serviceCenters: region.serviceCenters || 0,
      transformers: region.transformers || 0,
      createdAt: now,
      updatedAt: now
    };
  },

  /**
   * Update a region
   */
  async update(id: string, region: Partial<Region>): Promise<Region | null> {
    const currentRegion = await this.getById(id);
    if (!currentRegion) return null;

    const updates: string[] = [];
    const values: any[] = [];

    if (region.name !== undefined) {
      updates.push('name = ?');
      values.push(region.name);
    }

    if (region.code !== undefined) {
      updates.push('code = ?');
      values.push(region.code);
    }

    if (region.coordinates !== undefined) {
      updates.push('coordinates = ?');
      values.push(JSON.stringify(region.coordinates));
    }

    if (region.serviceCenters !== undefined) {
      updates.push('service_centers = ?');
      values.push(region.serviceCenters);
    }

    if (region.transformers !== undefined) {
      updates.push('transformers = ?');
      values.push(region.transformers);
    }

    if (updates.length === 0) return currentRegion;

    values.push(id);

    await executeQuery(
      `UPDATE regions SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    return this.getById(id);
  },

  /**
   * Delete a region
   */
  async delete(id: string): Promise<boolean> {
    const result = await executeQuery<any>(
      'DELETE FROM regions WHERE id = ?',
      [id]
    );

    return result.affectedRows > 0;
  },

  /**
   * Search regions by name
   */
  async searchByName(name: string): Promise<Region[]> {
    const regions = await executeQuery<any[]>(
      'SELECT * FROM regions WHERE name LIKE ?',
      [`%${name}%`]
    );

    return regions.map(mapRegionFromDB);
  }
};

// Helper functions to map database records to entities
function mapUserFromDB(dbUser: any): User {
  return {
    id: dbUser.id,
    email: dbUser.email,
    name: dbUser.name,
    role: dbUser.role as any,
    region: dbUser.region_id,
    serviceCenter: dbUser.service_center_id,
    isActive: Boolean(dbUser.is_active),
    lastLogin: dbUser.last_login,
    avatar: dbUser.avatar,
    preferences: dbUser.preferences ? JSON.parse(dbUser.preferences) : undefined,
    createdAt: dbUser.created_at.toISOString(),
    updatedAt: dbUser.updated_at.toISOString()
  };
}

function mapRegionFromDB(dbRegion: any): Region {
  return {
    id: dbRegion.id,
    name: dbRegion.name,
    code: dbRegion.code,
    coordinates: dbRegion.coordinates ? JSON.parse(dbRegion.coordinates) : { lat: 0, lng: 0 },
    serviceCenters: dbRegion.service_centers,
    transformers: dbRegion.transformers,
    createdAt: dbRegion.created_at.toISOString(),
    updatedAt: dbRegion.updated_at.toISOString()
  };
}

function mapTransformerFromDB(dbTransformer: any): Transformer {
  return {
    id: dbTransformer.id,
    serialNumber: dbTransformer.serial_number,
    name: dbTransformer.name,
    status: dbTransformer.status as any,
    type: dbTransformer.type,
    manufacturer: dbTransformer.manufacturer,
    model: dbTransformer.model,
    manufactureDate: dbTransformer.manufacture_date,
    installationDate: dbTransformer.installation_date,
    lastMaintenanceDate: dbTransformer.last_maintenance_date,
    nextMaintenanceDate: dbTransformer.next_maintenance_date,
    capacity: parseFloat(dbTransformer.capacity) || 0,
    voltage: {
      primary: parseFloat(dbTransformer.primary_voltage) || 0,
      secondary: parseFloat(dbTransformer.secondary_voltage) || 0
    },
    regionId: dbTransformer.region_id,
    serviceCenterId: dbTransformer.service_center_id,
    location: {
      address: dbTransformer.address,
      coordinates: {
        lat: parseFloat(dbTransformer.latitude) || 0,
        lng: parseFloat(dbTransformer.longitude) || 0
      }
    },
    metrics: {
      temperature: parseFloat(dbTransformer.temperature) || 0,
      loadPercentage: parseFloat(dbTransformer.load_percentage) || 0,
      oilLevel: parseFloat(dbTransformer.oil_level) || 0,
      healthIndex: parseFloat(dbTransformer.health_index) || 0
    },
    tags: dbTransformer.tags ? JSON.parse(dbTransformer.tags) : [],
    createdAt: dbTransformer.created_at.toISOString(),
    updatedAt: dbTransformer.updated_at.toISOString()
  };
}

function mapServiceCenterFromDB(dbServiceCenter: any): ServiceCenter {
  return {
    id: dbServiceCenter.id,
    name: dbServiceCenter.name,
    regionId: dbServiceCenter.region_id,
    address: dbServiceCenter.address,
    contactPerson: dbServiceCenter.contact_person,
    phone: dbServiceCenter.phone,
    email: dbServiceCenter.email,
    location: {
      latitude: parseFloat(dbServiceCenter.latitude) || 0,
      longitude: parseFloat(dbServiceCenter.longitude) || 0
    },
    createdAt: dbServiceCenter.created_at.toISOString(),
    updatedAt: dbServiceCenter.updated_at.toISOString()
  };
}

/**
 * Transformer repository for MySQL
 */
export const transformerRepository = {
  /**
   * Get all transformers
   */
  async getAll(): Promise<Transformer[]> {
    const transformers = await executeQuery<any[]>('SELECT * FROM transformers');
    return transformers.map(mapTransformerFromDB);
  },

  /**
   * Get a transformer by ID
   */
  async getById(id: string): Promise<Transformer | null> {
    const transformers = await executeQuery<any[]>('SELECT * FROM transformers WHERE id = ?', [id]);
    return transformers.length > 0 ? mapTransformerFromDB(transformers[0]) : null;
  },

  /**
   * Get transformers by region ID
   */
  async getByRegion(regionId: string): Promise<Transformer[]> {
    const transformers = await executeQuery<any[]>('SELECT * FROM transformers WHERE region_id = ?', [regionId]);
    return transformers.map(mapTransformerFromDB);
  },

  /**
   * Get transformers by service center ID
   */
  async getByServiceCenter(serviceCenterId: string): Promise<Transformer[]> {
    const transformers = await executeQuery<any[]>('SELECT * FROM transformers WHERE service_center_id = ?', [serviceCenterId]);
    return transformers.map(mapTransformerFromDB);
  },

  /**
   * Get transformers by status
   */
  async getByStatus(status: string): Promise<Transformer[]> {
    const transformers = await executeQuery<any[]>('SELECT * FROM transformers WHERE status = ?', [status]);
    return transformers.map(mapTransformerFromDB);
  },

  /**
   * Create a new transformer
   */
  async create(transformer: Omit<Transformer, 'id' | 'createdAt' | 'updatedAt'>): Promise<Transformer> {
    const id = uuidv4();
    const now = new Date().toISOString();

    await executeQuery(
      `INSERT INTO transformers (
        id, serial_number, name, status, type, manufacturer, model,
        manufacture_date, installation_date, last_maintenance_date, next_maintenance_date,
        capacity, primary_voltage, secondary_voltage, region_id, service_center_id,
        address, latitude, longitude, temperature, load_percentage, oil_level, health_index, tags
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        transformer.serialNumber,
        transformer.name,
        transformer.status,
        transformer.type,
        transformer.manufacturer,
        transformer.model,
        transformer.manufactureDate,
        transformer.installationDate,
        transformer.lastMaintenanceDate,
        transformer.nextMaintenanceDate,
        transformer.capacity,
        transformer.voltage?.primary,
        transformer.voltage?.secondary,
        transformer.regionId,
        transformer.serviceCenterId,
        transformer.location?.address,
        transformer.location?.coordinates?.lat,
        transformer.location?.coordinates?.lng,
        transformer.metrics?.temperature,
        transformer.metrics?.loadPercentage,
        transformer.metrics?.oilLevel,
        transformer.metrics?.healthIndex,
        JSON.stringify(transformer.tags || [])
      ]
    );

    // Update region transformer count
    await executeQuery(
      'UPDATE regions SET transformers = transformers + 1 WHERE id = ?',
      [transformer.regionId]
    );

    return {
      id,
      serialNumber: transformer.serialNumber,
      name: transformer.name,
      status: transformer.status,
      type: transformer.type,
      manufacturer: transformer.manufacturer,
      model: transformer.model,
      manufactureDate: transformer.manufactureDate,
      installationDate: transformer.installationDate,
      lastMaintenanceDate: transformer.lastMaintenanceDate,
      nextMaintenanceDate: transformer.nextMaintenanceDate,
      capacity: transformer.capacity,
      voltage: transformer.voltage,
      regionId: transformer.regionId,
      serviceCenterId: transformer.serviceCenterId,
      location: transformer.location,
      metrics: transformer.metrics,
      tags: transformer.tags || [],
      createdAt: now,
      updatedAt: now
    };
  },

  /**
   * Update a transformer
   */
  async update(id: string, transformer: Partial<Transformer>): Promise<Transformer | null> {
    const currentTransformer = await this.getById(id);
    if (!currentTransformer) return null;

    const updates: string[] = [];
    const values: any[] = [];

    if (transformer.serialNumber !== undefined) {
      updates.push('serial_number = ?');
      values.push(transformer.serialNumber);
    }

    if (transformer.name !== undefined) {
      updates.push('name = ?');
      values.push(transformer.name);
    }

    if (transformer.status !== undefined) {
      updates.push('status = ?');
      values.push(transformer.status);
    }

    if (transformer.type !== undefined) {
      updates.push('type = ?');
      values.push(transformer.type);
    }

    if (transformer.manufacturer !== undefined) {
      updates.push('manufacturer = ?');
      values.push(transformer.manufacturer);
    }

    if (transformer.model !== undefined) {
      updates.push('model = ?');
      values.push(transformer.model);
    }

    if (transformer.manufactureDate !== undefined) {
      updates.push('manufacture_date = ?');
      values.push(transformer.manufactureDate);
    }

    if (transformer.installationDate !== undefined) {
      updates.push('installation_date = ?');
      values.push(transformer.installationDate);
    }

    if (transformer.lastMaintenanceDate !== undefined) {
      updates.push('last_maintenance_date = ?');
      values.push(transformer.lastMaintenanceDate);
    }

    if (transformer.nextMaintenanceDate !== undefined) {
      updates.push('next_maintenance_date = ?');
      values.push(transformer.nextMaintenanceDate);
    }

    if (transformer.capacity !== undefined) {
      updates.push('capacity = ?');
      values.push(transformer.capacity);
    }

    if (transformer.voltage?.primary !== undefined) {
      updates.push('primary_voltage = ?');
      values.push(transformer.voltage.primary);
    }

    if (transformer.voltage?.secondary !== undefined) {
      updates.push('secondary_voltage = ?');
      values.push(transformer.voltage.secondary);
    }

    if (transformer.regionId !== undefined && transformer.regionId !== currentTransformer.regionId) {
      updates.push('region_id = ?');
      values.push(transformer.regionId);

      // Update region transformer counts
      await executeQuery(
        'UPDATE regions SET transformers = transformers - 1 WHERE id = ?',
        [currentTransformer.regionId]
      );

      await executeQuery(
        'UPDATE regions SET transformers = transformers + 1 WHERE id = ?',
        [transformer.regionId]
      );
    }

    if (transformer.serviceCenterId !== undefined) {
      updates.push('service_center_id = ?');
      values.push(transformer.serviceCenterId);
    }

    if (transformer.location?.address !== undefined) {
      updates.push('address = ?');
      values.push(transformer.location.address);
    }

    if (transformer.location?.coordinates?.lat !== undefined) {
      updates.push('latitude = ?');
      values.push(transformer.location.coordinates.lat);
    }

    if (transformer.location?.coordinates?.lng !== undefined) {
      updates.push('longitude = ?');
      values.push(transformer.location.coordinates.lng);
    }

    if (transformer.metrics?.temperature !== undefined) {
      updates.push('temperature = ?');
      values.push(transformer.metrics.temperature);
    }

    if (transformer.metrics?.loadPercentage !== undefined) {
      updates.push('load_percentage = ?');
      values.push(transformer.metrics.loadPercentage);
    }

    if (transformer.metrics?.oilLevel !== undefined) {
      updates.push('oil_level = ?');
      values.push(transformer.metrics.oilLevel);
    }

    if (transformer.metrics?.healthIndex !== undefined) {
      updates.push('health_index = ?');
      values.push(transformer.metrics.healthIndex);
    }

    if (transformer.tags !== undefined) {
      updates.push('tags = ?');
      values.push(JSON.stringify(transformer.tags));
    }

    if (updates.length === 0) return currentTransformer;

    values.push(id);

    await executeQuery(
      `UPDATE transformers SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    return this.getById(id);
  },

  /**
   * Delete a transformer
   */
  async delete(id: string): Promise<boolean> {
    const transformer = await this.getById(id);
    if (!transformer) return false;

    // Update region transformer count
    await executeQuery(
      'UPDATE regions SET transformers = transformers - 1 WHERE id = ?',
      [transformer.regionId]
    );

    const result = await executeQuery<any>(
      'DELETE FROM transformers WHERE id = ?',
      [id]
    );

    return result.affectedRows > 0;
  },

  /**
   * Search transformers
   */
  async search(term: string): Promise<Transformer[]> {
    const transformers = await executeQuery<any[]>(
      `SELECT * FROM transformers
       WHERE serial_number LIKE ?
       OR name LIKE ?
       OR manufacturer LIKE ?
       OR model LIKE ?`,
      [`%${term}%`, `%${term}%`, `%${term}%`, `%${term}%`]
    );

    return transformers.map(mapTransformerFromDB);
  }
};

/**
 * Service Center repository for MySQL
 */
export const serviceCenterRepository = {
  /**
   * Get all service centers
   */
  async getAll(): Promise<ServiceCenter[]> {
    const serviceCenters = await executeQuery<any[]>('SELECT * FROM service_centers');
    return serviceCenters.map(mapServiceCenterFromDB);
  },

  /**
   * Get a service center by ID
   */
  async getById(id: string): Promise<ServiceCenter | null> {
    const serviceCenters = await executeQuery<any[]>('SELECT * FROM service_centers WHERE id = ?', [id]);
    return serviceCenters.length > 0 ? mapServiceCenterFromDB(serviceCenters[0]) : null;
  },

  /**
   * Get service centers by region ID
   */
  async getByRegion(regionId: string): Promise<ServiceCenter[]> {
    const serviceCenters = await executeQuery<any[]>('SELECT * FROM service_centers WHERE region_id = ?', [regionId]);
    return serviceCenters.map(mapServiceCenterFromDB);
  },

  /**
   * Create a new service center
   */
  async create(serviceCenter: Omit<ServiceCenter, 'id' | 'createdAt' | 'updatedAt'>): Promise<ServiceCenter> {
    const id = uuidv4();
    const now = new Date().toISOString();

    await executeQuery(
      `INSERT INTO service_centers (
        id, name, region_id, address, contact_person, phone, email, latitude, longitude
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        serviceCenter.name,
        serviceCenter.regionId,
        serviceCenter.address,
        serviceCenter.contactPerson,
        serviceCenter.phone,
        serviceCenter.email,
        serviceCenter.location?.latitude,
        serviceCenter.location?.longitude
      ]
    );

    // Update region service center count
    await executeQuery(
      'UPDATE regions SET service_centers = service_centers + 1 WHERE id = ?',
      [serviceCenter.regionId]
    );

    return {
      id,
      name: serviceCenter.name,
      regionId: serviceCenter.regionId,
      address: serviceCenter.address,
      contactPerson: serviceCenter.contactPerson,
      phone: serviceCenter.phone,
      email: serviceCenter.email,
      location: serviceCenter.location,
      createdAt: now,
      updatedAt: now
    };
  },

  /**
   * Update a service center
   */
  async update(id: string, serviceCenter: Partial<ServiceCenter>): Promise<ServiceCenter | null> {
    const currentServiceCenter = await this.getById(id);
    if (!currentServiceCenter) return null;

    const updates: string[] = [];
    const values: any[] = [];

    if (serviceCenter.name !== undefined) {
      updates.push('name = ?');
      values.push(serviceCenter.name);
    }

    if (serviceCenter.regionId !== undefined && serviceCenter.regionId !== currentServiceCenter.regionId) {
      updates.push('region_id = ?');
      values.push(serviceCenter.regionId);

      // Update region service center counts
      await executeQuery(
        'UPDATE regions SET service_centers = service_centers - 1 WHERE id = ?',
        [currentServiceCenter.regionId]
      );

      await executeQuery(
        'UPDATE regions SET service_centers = service_centers + 1 WHERE id = ?',
        [serviceCenter.regionId]
      );
    }

    if (serviceCenter.address !== undefined) {
      updates.push('address = ?');
      values.push(serviceCenter.address);
    }

    if (serviceCenter.contactPerson !== undefined) {
      updates.push('contact_person = ?');
      values.push(serviceCenter.contactPerson);
    }

    if (serviceCenter.phone !== undefined) {
      updates.push('phone = ?');
      values.push(serviceCenter.phone);
    }

    if (serviceCenter.email !== undefined) {
      updates.push('email = ?');
      values.push(serviceCenter.email);
    }

    if (serviceCenter.location?.latitude !== undefined) {
      updates.push('latitude = ?');
      values.push(serviceCenter.location.latitude);
    }

    if (serviceCenter.location?.longitude !== undefined) {
      updates.push('longitude = ?');
      values.push(serviceCenter.location.longitude);
    }

    if (updates.length === 0) return currentServiceCenter;

    values.push(id);

    await executeQuery(
      `UPDATE service_centers SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    return this.getById(id);
  },

  /**
   * Delete a service center
   */
  async delete(id: string): Promise<boolean> {
    const serviceCenter = await this.getById(id);
    if (!serviceCenter) return false;

    // Update region service center count
    await executeQuery(
      'UPDATE regions SET service_centers = service_centers - 1 WHERE id = ?',
      [serviceCenter.regionId]
    );

    const result = await executeQuery<any>(
      'DELETE FROM service_centers WHERE id = ?',
      [id]
    );

    return result.affectedRows > 0;
  },

  /**
   * Search service centers
   */
  async search(term: string): Promise<ServiceCenter[]> {
    const serviceCenters = await executeQuery<any[]>(
      `SELECT * FROM service_centers
       WHERE name LIKE ?
       OR address LIKE ?
       OR contact_person LIKE ?
       OR email LIKE ?`,
      [`%${term}%`, `%${term}%`, `%${term}%`, `%${term}%`]
    );

    return serviceCenters.map(mapServiceCenterFromDB);
  }
};

/**
 * MySQL Database Service
 */
export class MySQLDatabaseService {
  // Repositories
  users = userRepository;
  regions = regionRepository;
  transformers = transformerRepository;
  serviceCenters = serviceCenterRepository;

  /**
   * Initialize the database
   */
  async initialize(force: boolean = false): Promise<void> {
    try {
      console.log('Initializing MySQL database...');
      await initMySQLSchema(force);
      console.log('MySQL database initialized successfully');

      // Seed initial data if needed
      await this.seedInitialData();
    } catch (error) {
      console.error('Error initializing MySQL database:', error);
      throw error;
    }
  }

  /**
   * Seed initial data
   */
  private async seedInitialData(): Promise<void> {
    try {
      // Check if we already have users
      const users = await userRepository.getAll();
      if (users.length > 0) {
        console.log('Database already has data, skipping seed');
        return;
      }

      console.log('Seeding initial data...');

      // Create admin user
      await userRepository.create({
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'super_admin',
        isActive: true
      });

      // Create regions
      await regionRepository.create({
        name: 'Addis Ababa',
        code: 'addis',
        serviceCenters: 4,
        transformers: 10,
        coordinates: { lat: 9.0222, lng: 38.7468 }
      });

      await regionRepository.create({
        name: 'Oromia',
        code: 'oromia',
        serviceCenters: 8,
        transformers: 25,
        coordinates: { lat: 8.9806, lng: 39.3772 }
      });

      console.log('Initial data seeded successfully');
    } catch (error) {
      console.error('Error seeding initial data:', error);
    }
  }

  /**
   * Get database metadata
   */
  async getMetadata() {
    const [userCount] = await executeQuery<any[]>('SELECT COUNT(*) as count FROM users');
    const [regionCount] = await executeQuery<any[]>('SELECT COUNT(*) as count FROM regions');
    const [serviceCenterCount] = await executeQuery<any[]>('SELECT COUNT(*) as count FROM service_centers');
    const [transformerCount] = await executeQuery<any[]>('SELECT COUNT(*) as count FROM transformers');

    return {
      version: '1.0.0',
      lastUpdated: new Date().toISOString(),
      recordCounts: {
        users: userCount.count,
        regions: regionCount.count,
        serviceCenters: serviceCenterCount.count,
        transformers: transformerCount.count,
        maintenanceRecords: 0, // Add these when implemented
        alerts: 0,
        outages: 0,
        weatherAlerts: 0
      }
    };
  }
}

// Export a singleton instance
export const mysqlService = new MySQLDatabaseService();
