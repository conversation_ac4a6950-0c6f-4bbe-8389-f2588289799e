"use client"

import { useState, useEffect } from "react"

// Define types for our weather data
export interface WeatherData {
  current: {
    main: {
      temp: number
      feels_like: number
      humidity: number
      pressure: number
    }
    weather: Array<{
      id: number
      main: string
      description: string
      icon: string
    }>
    wind: {
      speed: number
      deg: number
    }
    rain?: {
      "1h"?: number
      "3h"?: number
    }
    clouds: {
      all: number
    }
    dt: number
    name: string
  }
  forecast: {
    list: Array<{
      dt: number
      main: {
        temp: number
        feels_like: number
        humidity: number
        pressure: number
      }
      weather: Array<{
        id: number
        main: string
        description: string
        icon: string
      }>
      wind: {
        speed: number
        deg: number
      }
      rain?: {
        "3h"?: number
      }
      clouds: {
        all: number
      }
      dt_txt: string
    }>
    city: {
      name: string
      country: string
    }
  }
  timestamp: string
}

// Map region codes to city names for the API
const regionToCityMap: Record<string, string> = {
  "addis-ababa": "Addis Ababa,et",
  "dire-dawa": "Dire Dawa,et",
  "bahir-dar": "Bahir Dar,et",
  hawassa: "Hawassa,et",
  mekelle: "Mekelle,et",
  jimma: "Jimma,et",
  gondar: "Gondar,et",
  adama: "Adama,et",
  bishoftu: "Bishoftu,et",
  dessie: "Dessie,et",
  jijiga: "Jijiga,et",
  harar: "Harar,et",
  sodo: "Sodo,et",
  "arba-minch": "Arba Minch,et",
  "debre-markos": "Debre Markos,et",
  "debre-berhan": "Debre Berhan,et",
  nekemte: "Nekemte,et",
  // Default to Addis Ababa for regions without specific cities
  oromia: "Adama,et",
  amhara: "Bahir Dar,et",
  tigray: "Mekelle,et",
  afar: "Semera,et",
  somali: "Jijiga,et",
  snnpr: "Hawassa,et",
  "benishangul-gumuz": "Asosa,et",
  gambela: "Gambela,et",
  harari: "Harar,et",
  sidama: "Hawassa,et",
  "south-west": "Bonga,et",
}

export function useWeatherData(region: string) {
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchWeatherData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Get the city name for the API request
        const location = regionToCityMap[region] || "Addis Ababa,et"

        // Check if we have cached data that's less than 30 minutes old
        const cachedData = localStorage.getItem(`weather_${region}`)
        if (cachedData) {
          const parsedData = JSON.parse(cachedData)
          const cacheTime = new Date(parsedData.timestamp).getTime()
          const currentTime = new Date().getTime()

          // If cache is less than 30 minutes old, use it
          if (currentTime - cacheTime < 30 * 60 * 1000) {
            setWeatherData(parsedData)
            setIsLoading(false)
            return
          }
        }

        // Fetch fresh data
        const response = await fetch(`/api/weather?location=${encodeURIComponent(location)}`)

        if (!response.ok) {
          throw new Error(`Failed to fetch weather data: ${response.statusText}`)
        }

        const data = await response.json()

        // Cache the data
        localStorage.setItem(`weather_${region}`, JSON.stringify(data))

        setWeatherData(data)
      } catch (err) {
        console.error("Error fetching weather data:", err)
        setError(err instanceof Error ? err.message : "Failed to fetch weather data")
      } finally {
        setIsLoading(false)
      }
    }

    fetchWeatherData()

    // Set up a refresh interval (every 30 minutes)
    const intervalId = setInterval(fetchWeatherData, 30 * 60 * 1000)

    return () => clearInterval(intervalId)
  }, [region])

  // Function to manually refresh the data
  const refreshData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const location = regionToCityMap[region] || "Addis Ababa,et"
      const response = await fetch(`/api/weather?location=${encodeURIComponent(location)}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch weather data: ${response.statusText}`)
      }

      const data = await response.json()
      localStorage.setItem(`weather_${region}`, JSON.stringify(data))
      setWeatherData(data)
    } catch (err) {
      console.error("Error refreshing weather data:", err)
      setError(err instanceof Error ? err.message : "Failed to refresh weather data")
    } finally {
      setIsLoading(false)
    }
  }

  return { weatherData, isLoading, error, refreshData }
}
