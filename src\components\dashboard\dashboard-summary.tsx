/**
 * Dashboard Summary Component
 * Clean, reusable summary cards for dashboard metrics
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Zap, 
  Activity, 
  AlertTriangle, 
  Wrench, 
  DollarSign,
  TrendingUp,
  TrendingDown
} from 'lucide-react'
import { DashboardSummary } from '@/src/types'

interface DashboardSummaryProps {
  summary: DashboardSummary
  isLoading?: boolean
  className?: string
}

/**
 * Summary card component for individual metrics
 */
interface SummaryCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon: React.ReactNode
  trend?: 'up' | 'down' | 'stable'
  trendValue?: string
  color: 'blue' | 'green' | 'yellow' | 'red' | 'purple'
  isLoading?: boolean
}

function SummaryCard({ 
  title, 
  value, 
  subtitle, 
  icon, 
  trend, 
  trendValue, 
  color,
  isLoading = false 
}: SummaryCardProps) {
  const colorClasses = {
    blue: 'from-blue-500 to-indigo-600',
    green: 'from-emerald-500 to-teal-500',
    yellow: 'from-amber-500 to-orange-500',
    red: 'from-red-500 to-pink-500',
    purple: 'from-purple-500 to-indigo-500'
  }

  const getTrendIcon = () => {
    if (trend === 'up') return <TrendingUp className="h-4 w-4 text-green-600" />
    if (trend === 'down') return <TrendingDown className="h-4 w-4 text-red-600" />
    return null
  }

  if (isLoading) {
    return (
      <Card className="relative overflow-hidden border-0 shadow-lg bg-white/95 backdrop-blur-sm">
        <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${colorClasses[color]}`}></div>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
          <div className={`p-3 rounded-xl bg-gradient-to-br ${colorClasses[color]} text-white shadow-lg animate-pulse`}>
            {icon}
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
          <div className="h-4 bg-gray-100 rounded animate-pulse w-3/4"></div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-500 border-0 bg-white/95 backdrop-blur-sm">
      <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${colorClasses[color]}`}></div>
      
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
        <div className={`p-3 rounded-xl bg-gradient-to-br ${colorClasses[color]} text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}>
          {icon}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="text-3xl font-bold text-gray-900 mb-1">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>
        
        {subtitle && (
          <p className="text-sm text-gray-500 mb-2">{subtitle}</p>
        )}
        
        {trend && trendValue && (
          <div className="flex items-center gap-1">
            {getTrendIcon()}
            <span className={`text-sm font-medium ${
              trend === 'up' ? 'text-green-600' : 
              trend === 'down' ? 'text-red-600' : 'text-gray-600'
            }`}>
              {trendValue}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * Main dashboard summary component
 */
export function DashboardSummaryCards({ summary, isLoading = false, className = '' }: DashboardSummaryProps) {
  // Calculate percentages and trends
  const operationalPercentage = summary.totalTransformers > 0 
    ? ((summary.operationalCount / summary.totalTransformers) * 100).toFixed(1)
    : '0'
    
  const criticalPercentage = summary.totalTransformers > 0
    ? ((summary.criticalCount / summary.totalTransformers) * 100).toFixed(1)
    : '0'

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {/* Total Transformers */}
      <SummaryCard
        title="Total Transformers"
        value={summary.totalTransformers}
        subtitle={`${summary.operationalCount} operational`}
        icon={<Zap className="h-5 w-5" />}
        color="blue"
        isLoading={isLoading}
      />

      {/* System Availability */}
      <SummaryCard
        title="System Availability"
        value={`${operationalPercentage}%`}
        subtitle={`${summary.criticalCount} critical issues`}
        icon={<Activity className="h-5 w-5" />}
        trend={parseFloat(operationalPercentage) > 95 ? 'up' : 'down'}
        trendValue={`${operationalPercentage}% uptime`}
        color="green"
        isLoading={isLoading}
      />

      {/* Average Efficiency */}
      <SummaryCard
        title="Average Efficiency"
        value={`${summary.avgEfficiency.toFixed(1)}%`}
        subtitle={`Load factor: ${summary.avgLoadFactor.toFixed(1)}%`}
        icon={<TrendingUp className="h-5 w-5" />}
        trend={summary.avgEfficiency > 90 ? 'up' : 'down'}
        trendValue={`${summary.avgEfficiency > 90 ? 'Excellent' : 'Needs attention'}`}
        color="purple"
        isLoading={isLoading}
      />

      {/* Total Asset Value */}
      <SummaryCard
        title="Total Asset Value"
        value={`$${(summary.totalAssetValue / 1000000).toFixed(1)}M`}
        subtitle={`${summary.activeAlerts} active alerts`}
        icon={<DollarSign className="h-5 w-5" />}
        color="yellow"
        isLoading={isLoading}
      />

      {/* Critical Alerts */}
      {summary.criticalCount > 0 && (
        <SummaryCard
          title="Critical Issues"
          value={summary.criticalCount}
          subtitle={`${criticalPercentage}% of total transformers`}
          icon={<AlertTriangle className="h-5 w-5" />}
          trend="down"
          trendValue="Requires immediate attention"
          color="red"
          isLoading={isLoading}
        />
      )}

      {/* Pending Maintenance */}
      {summary.pendingMaintenance > 0 && (
        <SummaryCard
          title="Pending Maintenance"
          value={summary.pendingMaintenance}
          subtitle="Scheduled maintenance tasks"
          icon={<Wrench className="h-5 w-5" />}
          color="blue"
          isLoading={isLoading}
        />
      )}
    </div>
  )
}
