/**
 * MySQL Statistics API Route
 * 
 * This API route handles transformer statistics requests from MySQL database.
 */

import { NextRequest, NextResponse } from 'next/server';
import { MySQLServerService } from '@/src/lib/mysql-server';

export async function GET(request: NextRequest) {
  try {
    console.log('📈 API: Fetching transformer statistics from MySQL...');
    
    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();
    
    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        { 
          error: 'MySQL connection failed',
          fallback: true 
        },
        { status: 503 }
      );
    }
    
    // Get statistics data
    const statistics = await MySQLServerService.getTransformerStatistics();
    
    console.log('✅ API: Transformer statistics fetched successfully from MySQL');
    
    return NextResponse.json({
      success: true,
      data: statistics,
      source: 'mysql'
    });
    
  } catch (error) {
    console.error('❌ API: Error fetching transformer statistics:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch transformer statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
        fallback: true
      },
      { status: 500 }
    );
  }
}
