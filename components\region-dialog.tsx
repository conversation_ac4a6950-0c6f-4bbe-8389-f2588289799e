"use client"

import type React from "react"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Switch } from "@/src/components/ui/switch"

interface RegionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  region: any | null
  onSave: (region: any) => void
}

export function RegionDialog({ open, onOpenChange, region, onSave }: RegionDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    status: true,
  })

  // Reset form when dialog opens or region changes
  useEffect(() => {
    if (region) {
      setFormData({
        name: region.name || "",
        code: region.code || "",
        status: region.status === "active",
      })
    } else {
      setFormData({
        name: "",
        code: "",
        status: true,
      })
    }
  }, [region, open])

  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave({
      id: region?.id || `region-${Date.now()}`,
      name: formData.name,
      code: formData.code,
      status: formData.status ? "active" : "inactive",
      serviceCenters: region?.serviceCenters || 0,
      transformers: region?.transformers || 0,
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>{region ? "Edit Region" : "Add New Region"}</DialogTitle>
            <DialogDescription>
              {region ? "Update region details and status." : "Create a new region for the Ethiopia Electric Utility."}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Region Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">Region Code</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => handleChange("code", e.target.value)}
                  required
                  maxLength={3}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="status"
                checked={formData.status}
                onCheckedChange={(checked) => handleChange("status", checked)}
              />
              <Label htmlFor="status">Active Region</Label>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">{region ? "Update Region" : "Create Region"}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
