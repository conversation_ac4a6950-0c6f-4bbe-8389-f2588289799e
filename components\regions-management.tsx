"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Download, Search, Edit, Trash, Plus, MapPin, ChevronDown, ChevronRight } from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { useAuth } from "@/src/features/auth/context/auth-context"
import { useToast } from "@/src/components/ui/use-toast"
import { RegionDialog } from "@/components/region-dialog"
import { RegionServiceCenters } from "@/components/region-service-centers"
import type { Region } from "@/src/types/auth"

export function RegionsManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedRegion, setSelectedRegion] = useState<Region | null>(null)
  const [expandedRegions, setExpandedRegions] = useState<string[]>([])
  const { user: currentUser } = useAuth()
  const { toast } = useToast()

  // Mock regions data - in a real app, this would come from an API
  const regions = [
    {
      id: "region-001",
      name: "Addis Ababa",
      code: "AA",
      serviceCenters: 5,
      transformers: 1245,
      status: "active",
    },
    {
      id: "region-002",
      name: "Oromia",
      code: "OR",
      serviceCenters: 12,
      transformers: 987,
      status: "active",
    },
    {
      id: "region-003",
      name: "Amhara",
      code: "AM",
      serviceCenters: 8,
      transformers: 756,
      status: "active",
    },
    {
      id: "region-004",
      name: "Tigray",
      code: "TI",
      serviceCenters: 4,
      transformers: 321,
      status: "inactive",
    },
    {
      id: "region-005",
      name: "SNNPR",
      code: "SN",
      serviceCenters: 7,
      transformers: 543,
      status: "active",
    },
    {
      id: "region-006",
      name: "Sidama",
      code: "SI",
      serviceCenters: 3,
      transformers: 234,
      status: "active",
    },
    {
      id: "region-007",
      name: "Afar",
      code: "AF",
      serviceCenters: 2,
      transformers: 98,
      status: "active",
    },
  ]

  // Filter regions based on search query
  const filteredRegions = regions.filter((region) => {
    const matchesSearch =
      region.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      region.code.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesSearch
  })

  const handleAddRegion = () => {
    setSelectedRegion(null)
    setIsDialogOpen(true)
  }

  const handleEditRegion = (region: any) => {
    setSelectedRegion(region)
    setIsDialogOpen(true)
  }

  const handleDeleteRegion = (region: any) => {
    // In a real app, this would call an API to delete the region
    toast({
      title: "Region deleted",
      description: `${region.name} has been deleted.`,
    })
  }

  const handleRegionSave = (region: any) => {
    // In a real app, this would call an API to save the region
    toast({
      title: selectedRegion ? "Region updated" : "Region created",
      description: `${region.name} has been ${selectedRegion ? "updated" : "created"}.`,
    })
    setIsDialogOpen(false)
  }

  const toggleRegionExpand = (regionId: string) => {
    setExpandedRegions((prev) => {
      if (prev.includes(regionId)) {
        return prev.filter((id) => id !== regionId)
      } else {
        return [...prev, regionId]
      }
    })
  }

  const handleServiceCenterUpdated = () => {
    // In a real app, this would refresh the regions data
    toast({
      title: "Service Center updated",
      description: "The service center has been updated successfully.",
    })
  }

  // Check if user has permission to manage regions
  const canManageRegions = currentUser?.role === "super_admin" || currentUser?.role === "national_asset_manager"

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Region Management</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          {canManageRegions && (
            <Button size="sm" onClick={handleAddRegion}>
              <Plus className="mr-2 h-4 w-4" />
              Add Region
            </Button>
          )}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Regions</CardTitle>
          <CardDescription>Manage regions, service centers, and regional assets</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search regions..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Region</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Service Centers</TableHead>
                  <TableHead>Transformers</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRegions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No regions found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRegions.map((region) => (
                    <React.Fragment key={region.id}>
                      <TableRow>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 p-0"
                              onClick={() => toggleRegionExpand(region.id)}
                            >
                              {expandedRegions.includes(region.id) ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </Button>
                            <MapPin className="h-4 w-4 text-teal-600" />
                            <span className="font-medium">{region.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{region.code}</TableCell>
                        <TableCell>
                          <Button
                            variant="link"
                            className="p-0 h-auto font-normal"
                            onClick={() => toggleRegionExpand(region.id)}
                          >
                            {region.serviceCenters}
                          </Button>
                        </TableCell>
                        <TableCell>{region.transformers}</TableCell>
                        <TableCell>
                          <Badge
                            variant={region.status === "active" ? "default" : "outline"}
                            className={region.status === "active" ? "bg-green-500 hover:bg-green-600" : ""}
                          >
                            {region.status === "active" ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="icon" onClick={() => handleEditRegion(region)}>
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit</span>
                            </Button>
                            {canManageRegions && (
                              <Button variant="ghost" size="icon" onClick={() => handleDeleteRegion(region)}>
                                <Trash className="h-4 w-4" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                      {expandedRegions.includes(region.id) && (
                        <TableRow>
                          <TableCell colSpan={6} className="p-0 border-t-0">
                            <div className="pl-10 pr-4 pb-4">
                              <RegionServiceCenters
                                regionId={region.id}
                                regionName={region.name}
                                onServiceCenterUpdated={handleServiceCenterUpdated}
                              />
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <RegionDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        region={selectedRegion}
        onSave={handleRegionSave}
      />
    </div>
  )
}
