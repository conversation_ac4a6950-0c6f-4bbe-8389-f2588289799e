"use client"

import type React from "react"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/src/components/ui/dialog"
import { Loader2, ChevronLeft, ChevronRight, Download } from "lucide-react"
import type { Transformer, InspectionRecord, MaintenanceRecord, TestResult } from "@/src/types/transformer"
import { jsPDF } from "jspdf"
// Import jspdf-autotable directly with require to avoid bundling issues
const autoTable = require('jspdf-autotable')
import { toast } from "@/src/components/ui/use-toast"

interface PDFPreviewDialogProps {
  transformer: Transformer
  inspections: InspectionRecord[]
  maintenances: MaintenanceRecord[]
  testResults: TestResult[]
  children: React.ReactNode
}

export function PDFPreviewDialog({
  transformer,
  inspections,
  maintenances,
  testResults,
  children,
}: PDFPreviewDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [pdfDataUrl, setPdfDataUrl] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const pdfDocRef = useRef<jsPDF | null>(null)

  const generatePDF = async () => {
    setIsGenerating(true)

    try {
      // Create a new PDF document
      const doc = new jsPDF()
      const pageWidth = doc.internal.pageSize.getWidth()

      // Add header
      doc.setFontSize(18)
      doc.setTextColor(0, 0, 0)
      doc.text("Ethiopia Electric Utility", pageWidth / 2, 15, { align: "center" })
      doc.setFontSize(14)
      doc.text("Transformer Details Report", pageWidth / 2, 22, { align: "center" })
      doc.setFontSize(12)
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, pageWidth / 2, 28, { align: "center" })

      // Add transformer basic info
      doc.setFontSize(14)
      doc.text(`Transformer: ${transformer.transformerCode}`, 14, 40)
      doc.setFontSize(10)
      doc.text(`Status: ${transformer.status}`, 14, 46)
      doc.text(`Location: ${transformer.location.specificLocation}`, 14, 52)
      doc.text(`Substation: ${transformer.substationName} / Feeder: ${transformer.feederName}`, 14, 58)

      // Add technical specifications
      doc.setFontSize(12)
      doc.text("Technical Specifications", 14, 70)

      const techSpecsData = [
        ["Card No.", transformer.cardNo],
        ["KVA Rating", transformer.kvaRating],
        ["Primary Service Voltage", transformer.primaryServiceVoltage],
        ["Manufacturer", transformer.manufacturer],
        ["Manufacturing Year", transformer.manufacturingYear],
        ["Serial Number", transformer.serialNumber],
        ["Tag Number", transformer.tagNumber],
        ["Installation Date", new Date(transformer.installationDate).toLocaleDateString()],
        ["Construction Type", transformer.constructionType],
        ["Customer Type", transformer.customerType],
      ]

      if (transformer.lastChangingDate) {
        techSpecsData.push(
          ["Last Changing Date", new Date(transformer.lastChangingDate).toLocaleDateString()],
          ["Changing Reason", transformer.changingReason || "Not specified"],
        )
      }

      autoTable(doc, {
        startY: 75,
        head: [["Property", "Value"]],
        body: techSpecsData,
        theme: "striped",
        headStyles: { fillColor: [66, 66, 66] },
      })

      // Add location details
      let currentY = (doc as any).lastAutoTable.finalY + 15
      doc.setFontSize(12)
      doc.text("Location Details", 14, currentY)

      const locationData = [
        [
          "GPS Coordinates",
          `${transformer.location.gpsCoordinates.latitude.toFixed(6)}, ${transformer.location.gpsCoordinates.longitude.toFixed(
            6,
          )}`,
        ],
        ["Region", transformer.location.region],
        ["District", transformer.location.district],
      ]

      if (transformer.location.subCity) {
        locationData.push(["Sub City", transformer.location.subCity])
      }

      if (transformer.location.kebele) {
        locationData.push(["Kebele", transformer.location.kebele])
      }

      locationData.push(["Specific Location", transformer.location.specificLocation])

      autoTable(doc, {
        startY: currentY + 5,
        head: [["Property", "Value"]],
        body: locationData,
        theme: "striped",
        headStyles: { fillColor: [66, 66, 66] },
      })

      // Add maintenance records
      currentY = (doc as any).lastAutoTable.finalY + 15
      if (currentY > 250) {
        doc.addPage()
        currentY = 20
      }

      doc.setFontSize(12)
      doc.text("Maintenance Records", 14, currentY)

      if (maintenances.length > 0) {
        const maintenanceData = maintenances.map((maintenance) => [
          new Date(maintenance.date).toLocaleDateString(),
          maintenance.description,
          maintenance.type,
          maintenance.status,
          maintenance.technician,
          maintenance.notes || "",
        ])

        autoTable(doc, {
          startY: currentY + 5,
          head: [["Date", "Description", "Type", "Status", "Technician", "Notes"]],
          body: maintenanceData,
          theme: "striped",
          headStyles: { fillColor: [66, 66, 66] },
        })
      } else {
        doc.setFontSize(10)
        doc.text("No maintenance records found", 14, currentY + 10)
      }

      // Add inspection records
      currentY = (doc as any).lastAutoTable ? (doc as any).lastAutoTable.finalY + 15 : currentY + 20
      if (currentY > 250) {
        doc.addPage()
        currentY = 20
      }

      doc.setFontSize(12)
      doc.text("Inspection Records", 14, currentY)

      if (inspections.length > 0) {
        const inspectionData = inspections.map((inspection) => [
          new Date(inspection.date).toLocaleDateString(),
          inspection.inspectorName,
          `L. Arrester: ${inspection.components.lArrester}, Links: ${inspection.components.links}, Fuse: ${inspection.components.dropOutFuse}, Ground: ${inspection.components.ground}`,
          `Bushings: ${inspection.conditions.bushings}, Oil: ${inspection.conditions.oilLevel}, Leakage: ${inspection.conditions.leakage}`,
          inspection.remarks || "",
        ])

        autoTable(doc, {
          startY: currentY + 5,
          head: [["Date", "Inspector", "Components", "Conditions", "Remarks"]],
          body: inspectionData,
          theme: "striped",
          headStyles: { fillColor: [66, 66, 66] },
        })
      } else {
        doc.setFontSize(10)
        doc.text("No inspection records found", 14, currentY + 10)
      }

      // Add test results
      currentY = (doc as any).lastAutoTable ? (doc as any).lastAutoTable.finalY + 15 : currentY + 20
      if (currentY > 250) {
        doc.addPage()
        currentY = 20
      }

      doc.setFontSize(12)
      doc.text("Test Results", 14, currentY)

      if (testResults.length > 0) {
        const testResultsData = testResults.map((test) => [
          new Date(test.date).toLocaleDateString(),
          `R-S: ${test.meggerResults.rToS}, S-T: ${test.meggerResults.sToT}, T-R: ${test.meggerResults.tToR}, HT-G: ${test.meggerResults.htToGround}, LT-G: ${test.meggerResults.ltToGround}`,
          `${test.oilInsulation} kV / ${test.oilLevel}`,
          test.conclusion,
        ])

        autoTable(doc, {
          startY: currentY + 5,
          head: [["Date", "Megger Results (MΩ)", "Oil Condition", "Conclusion"]],
          body: testResultsData,
          theme: "striped",
          headStyles: { fillColor: [66, 66, 66] },
        })
      } else {
        doc.setFontSize(10)
        doc.text("No test results found", 14, currentY + 10)
      }

      // Add footer
      const pageCount = doc.getNumberOfPages()
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i)
        doc.setFontSize(8)
        doc.text(
          `Ethiopia Electric Utility - Distribution Transformer Management System - Page ${i} of ${pageCount}`,
          pageWidth / 2,
          doc.internal.pageSize.getHeight() - 10,
          { align: "center" },
        )
      }

      // Store the PDF document for later download
      pdfDocRef.current = doc
      setTotalPages(pageCount)
      setCurrentPage(1)

      // Convert to data URL for preview
      const dataUrl = doc.output("datauristring")
      setPdfDataUrl(dataUrl)
    } catch (error) {
      console.error("Error generating PDF preview:", error)
      toast({
        title: "Error",
        description: "Failed to generate PDF preview. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const handleDownload = () => {
    if (pdfDocRef.current) {
      pdfDocRef.current.save(`Transformer_${transformer.transformerCode}_Report.pdf`)
      toast({
        title: "PDF Downloaded",
        description: "The transformer report has been successfully downloaded.",
      })
    }
  }

  const handleDialogChange = (open: boolean) => {
    setIsOpen(open)
    if (open) {
      generatePDF()
    } else {
      setPdfDataUrl(null)
      setCurrentPage(1)
      setTotalPages(1)
      pdfDocRef.current = null
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Transformer Report Preview</DialogTitle>
          <DialogDescription>
            Preview the transformer report before downloading. Use the navigation buttons to view all pages.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-auto border rounded-md bg-white">
          {isGenerating ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Generating preview...</span>
            </div>
          ) : pdfDataUrl ? (
            <iframe src={pdfDataUrl} className="w-full h-full" title="PDF Preview" />
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">No preview available</div>
          )}
        </div>

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage <= 1 || isGenerating}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage >= totalPages || isGenerating}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <Button onClick={handleDownload} disabled={!pdfDataUrl || isGenerating}>
            <Download className="h-4 w-4 mr-1" /> Download PDF
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
