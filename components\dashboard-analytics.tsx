'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/src/components/ui/tabs'
import { Progress } from '@/src/components/ui/progress'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Download,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Activity,
  MapPin,
  Users,
  Settings
} from 'lucide-react'
import { toast } from 'sonner'

interface AnalyticsData {
  overview: {
    totalTransformers: number
    operationalTransformers: number
    averageLoad: number
    averageEfficiency: number
    systemUptime: number
    efficiencyTrend: string
  }
  maintenance: {
    totalSchedules: number
    completedTasks: number
    pendingTasks: number
    inProgressTasks: number
    averageCompletionDays: number
    efficiency: number
  }
  alerts: {
    totalAlerts: number
    criticalAlerts: number
    highAlerts: number
    mediumAlerts: number
    lowAlerts: number
    resolvedAlerts: number
    resolutionRate: number
  }
  performance: {
    averageUptime: number
    averageEfficiency: number
    averageLoadFactor: number
    monitoredTransformers: number
  }
  regional: Array<{
    name: string
    code: string
    transformerCount: number
    averageLoad: number
    operationalCount: number
    operationalPercentage: number
  }>
  trends: {
    daily: Array<{
      date: string
      alerts: number
      criticalPercentage: number
    }>
  }
  summary: {
    systemHealth: number
    maintenanceBacklog: number
    criticalIssues: number
    overallEfficiency: number
  }
}

export default function DashboardAnalytics() {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const fetchAnalytics = async () => {
    try {
      setRefreshing(true)
      const response = await fetch('/api/dashboard/analytics')
      const result = await response.json()

      if (result.success) {
        setData(result.data)
      } else {
        toast.error('Failed to fetch analytics data')
      }
    } catch (error) {
      console.error('Error fetching analytics:', error)
      toast.error('Error loading analytics data')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = async () => {
    await fetchAnalytics()
    toast.success('Analytics data refreshed')
  }

  const handleGenerateReport = async () => {
    try {
      const response = await fetch('/api/dashboard/analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'generate_report' })
      })

      const result = await response.json()
      if (result.success) {
        toast.success('Analytics report generated successfully')
      }
    } catch (error) {
      toast.error('Failed to generate report')
    }
  }

  const handleRefreshMetrics = async () => {
    try {
      const response = await fetch('/api/dashboard/analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'refresh_metrics' })
      })

      const result = await response.json()
      if (result.success) {
        toast.success('Metrics refreshed successfully')
        await fetchAnalytics()
      }
    } catch (error) {
      toast.error('Failed to refresh metrics')
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No analytics data available</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard Analytics</h1>
          <p className="text-muted-foreground">
            Comprehensive system performance and operational insights
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleGenerateReport}
          >
            <Download className="mr-2 h-4 w-4" />
            Generate Report
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshMetrics}
          >
            <Activity className="mr-2 h-4 w-4" />
            Refresh Metrics
          </Button>
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.systemHealth}%</div>
            <Progress value={data.summary.systemHealth} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              {data.overview.operationalTransformers} of {data.overview.totalTransformers} operational
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overall Efficiency</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.overallEfficiency}%</div>
            <Progress value={data.summary.overallEfficiency} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              Trend: {data.overview.efficiencyTrend}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.criticalIssues}</div>
            <Badge variant={data.summary.criticalIssues > 0 ? "destructive" : "secondary"} className="mt-2">
              {data.summary.criticalIssues > 0 ? 'Attention Required' : 'All Clear'}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              {data.alerts.totalAlerts} total alerts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance Backlog</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.maintenanceBacklog}</div>
            <Badge variant={data.summary.maintenanceBacklog > 10 ? "destructive" : "secondary"} className="mt-2">
              {data.summary.maintenanceBacklog > 10 ? 'High' : 'Normal'}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              {data.maintenance.efficiency}% completion rate
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="regional">Regional</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Transformer Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Operational</span>
                  <Badge variant="secondary">{data.overview.operationalTransformers}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Average Load</span>
                  <Badge variant="outline">{data.overview.averageLoad}%</Badge>
                </div>
                <div className="flex justify-between">
                  <span>System Uptime</span>
                  <Badge variant="secondary">{data.overview.systemUptime}%</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Average Uptime</span>
                  <Badge variant="secondary">{data.performance.averageUptime}%</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Load Factor</span>
                  <Badge variant="outline">{data.performance.averageLoadFactor}%</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Monitored Units</span>
                  <Badge variant="secondary">{data.performance.monitoredTransformers}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Maintenance Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Completed Tasks</span>
                  <Badge variant="secondary">{data.maintenance.completedTasks}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Pending Tasks</span>
                  <Badge variant="outline">{data.maintenance.pendingTasks}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Avg. Completion</span>
                  <Badge variant="secondary">{data.maintenance.averageCompletionDays} days</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="regional" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Regional Distribution
              </CardTitle>
              <CardDescription>
                Transformer distribution and performance by region
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.regional.map((region) => (
                  <div key={region.code} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{region.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {region.transformerCount} transformers
                      </p>
                    </div>
                    <div className="text-right space-y-1">
                      <Badge variant="secondary">
                        {region.operationalPercentage}% operational
                      </Badge>
                      <p className="text-sm text-muted-foreground">
                        Avg Load: {region.averageLoad}%
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Average Uptime</span>
                    <span>{data.performance.averageUptime}%</span>
                  </div>
                  <Progress value={data.performance.averageUptime} />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Average Efficiency</span>
                    <span>{data.performance.averageEfficiency}%</span>
                  </div>
                  <Progress value={data.performance.averageEfficiency} />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Load Factor</span>
                    <span>{data.performance.averageLoadFactor}%</span>
                  </div>
                  <Progress value={data.performance.averageLoadFactor} />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>Monitored Transformers</span>
                    <Badge variant="secondary">{data.performance.monitoredTransformers}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>System Health</span>
                    <Badge variant={data.summary.systemHealth > 90 ? "secondary" : "destructive"}>
                      {data.summary.systemHealth}%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Overall Efficiency</span>
                    <Badge variant={data.summary.overallEfficiency > 85 ? "secondary" : "outline"}>
                      {data.summary.overallEfficiency}%
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Total Schedules</span>
                  <Badge variant="secondary">{data.maintenance.totalSchedules}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Completed</span>
                  <Badge variant="secondary">{data.maintenance.completedTasks}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>In Progress</span>
                  <Badge variant="outline">{data.maintenance.inProgressTasks}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Pending</span>
                  <Badge variant={data.maintenance.pendingTasks > 10 ? "destructive" : "outline"}>
                    {data.maintenance.pendingTasks}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Efficiency Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Completion Rate</span>
                    <span>{data.maintenance.efficiency}%</span>
                  </div>
                  <Progress value={data.maintenance.efficiency} />
                </div>
                <div className="flex justify-between">
                  <span>Avg. Completion Time</span>
                  <Badge variant="outline">{data.maintenance.averageCompletionDays} days</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Alert Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Critical</span>
                  <Badge variant="destructive">{data.alerts.criticalAlerts}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>High</span>
                  <Badge variant="outline">{data.alerts.highAlerts}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Medium</span>
                  <Badge variant="secondary">{data.alerts.mediumAlerts}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Low</span>
                  <Badge variant="outline">{data.alerts.lowAlerts}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Resolution Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Resolution Rate</span>
                    <span>{data.alerts.resolutionRate}%</span>
                  </div>
                  <Progress value={data.alerts.resolutionRate} />
                </div>
                <div className="flex justify-between">
                  <span>Total Alerts</span>
                  <Badge variant="secondary">{data.alerts.totalAlerts}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Resolved</span>
                  <Badge variant="secondary">{data.alerts.resolvedAlerts}</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
