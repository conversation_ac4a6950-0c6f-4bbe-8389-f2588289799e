"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { But<PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/src/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/src/components/ui/dropdown-menu"
import { 
  Users, 
  UserPlus, 
  Search, 
  Filter, 
  Download, 
  Upload, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  Shield,
  Clock,
  Activity,
  Settings,
  RefreshCw,
  Mail,
  Phone,
  MapPin,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'

interface User {
  id: string
  name: string
  email: string
  role: string
  region: string
  serviceCenter: string
  status: 'Active' | 'Inactive' | 'Suspended'
  lastLogin: string
  createdAt: string
  permissions: string[]
  avatar?: string
  phone?: string
  department: string
  manager?: string
}

interface UserActivity {
  id: string
  userId: string
  action: string
  timestamp: string
  details: string
  ipAddress: string
}

export function AdvancedUserManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [activities, setActivities] = useState<UserActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [activeTab, setActiveTab] = useState('users')

  useEffect(() => {
    loadUsers()
    loadActivities()
  }, [])

  const loadUsers = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockUsers: User[] = [
        {
          id: '1',
          name: 'Abebe Kebede',
          email: '<EMAIL>',
          role: 'super_admin',
          region: 'National',
          serviceCenter: 'Head Office',
          status: 'Active',
          lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          createdAt: '2023-01-15',
          permissions: ['read', 'write', 'delete', 'admin'],
          phone: '+251911123456',
          department: 'IT Administration',
          manager: 'System Administrator'
        },
        {
          id: '2',
          name: 'Tigist Haile',
          email: '<EMAIL>',
          role: 'regional_admin',
          region: 'Addis Ababa',
          serviceCenter: 'Central Service Center',
          status: 'Active',
          lastLogin: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          createdAt: '2023-02-20',
          permissions: ['read', 'write'],
          phone: '+251911234567',
          department: 'Regional Operations',
          manager: 'Abebe Kebede'
        },
        {
          id: '3',
          name: 'Dawit Tadesse',
          email: '<EMAIL>',
          role: 'field_technician',
          region: 'Oromia',
          serviceCenter: 'Adama Service Center',
          status: 'Active',
          lastLogin: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          createdAt: '2023-03-10',
          permissions: ['read'],
          phone: '+251911345678',
          department: 'Field Operations',
          manager: 'Tigist Haile'
        },
        {
          id: '4',
          name: 'Meron Assefa',
          email: '<EMAIL>',
          role: 'maintenance_engineer',
          region: 'Amhara',
          serviceCenter: 'Bahir Dar Service Center',
          status: 'Inactive',
          lastLogin: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: '2023-04-05',
          permissions: ['read', 'write'],
          phone: '+251911456789',
          department: 'Maintenance',
          manager: 'Tigist Haile'
        },
        {
          id: '5',
          name: 'Yohannes Gebre',
          email: '<EMAIL>',
          role: 'customer_service',
          region: 'Tigray',
          serviceCenter: 'Mekelle Service Center',
          status: 'Suspended',
          lastLogin: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: '2023-05-12',
          permissions: ['read'],
          phone: '+251911567890',
          department: 'Customer Service',
          manager: 'Tigist Haile'
        }
      ]
      
      setUsers(mockUsers)
    } catch (error) {
      console.error('Failed to load users:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadActivities = async () => {
    try {
      const mockActivities: UserActivity[] = [
        {
          id: '1',
          userId: '1',
          action: 'Login',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          details: 'Successful login from dashboard',
          ipAddress: '*************'
        },
        {
          id: '2',
          userId: '2',
          action: 'Update Transformer',
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
          details: 'Updated transformer T-001 status',
          ipAddress: '*************'
        },
        {
          id: '3',
          userId: '3',
          action: 'Create Maintenance Record',
          timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
          details: 'Created maintenance record for T-045',
          ipAddress: '*************'
        }
      ]
      
      setActivities(mockActivities)
    } catch (error) {
      console.error('Failed to load activities:', error)
    }
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' || 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesRole = roleFilter === 'all' || user.role === roleFilter
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter

    return matchesSearch && matchesRole && matchesStatus
  })

  const getRoleDisplayName = (role: string) => {
    const roleMap: Record<string, string> = {
      'super_admin': 'Super Admin',
      'regional_admin': 'Regional Admin',
      'field_technician': 'Field Technician',
      'maintenance_engineer': 'Maintenance Engineer',
      'customer_service': 'Customer Service'
    }
    return roleMap[role] || role
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800 border-green-200'
      case 'Inactive': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'Suspended': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Active': return <CheckCircle className="h-3 w-3" />
      case 'Inactive': return <XCircle className="h-3 w-3" />
      case 'Suspended': return <AlertCircle className="h-3 w-3" />
      default: return <CheckCircle className="h-3 w-3" />
    }
  }

  const formatLastLogin = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Management
              </CardTitle>
              <CardDescription>
                Manage users, roles, and permissions across the EEU system
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-1" />
                Import Users
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
              <Button size="sm">
                <UserPlus className="h-4 w-4 mr-1" />
                Add User
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
              <TabsTrigger value="activity">Activity Log</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="users" className="space-y-4">
              {/* Search and Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <select 
                  value={roleFilter} 
                  onChange={(e) => setRoleFilter(e.target.value)}
                  className="px-3 py-2 border rounded-md text-sm"
                >
                  <option value="all">All Roles</option>
                  <option value="super_admin">Super Admin</option>
                  <option value="regional_admin">Regional Admin</option>
                  <option value="field_technician">Field Technician</option>
                  <option value="maintenance_engineer">Maintenance Engineer</option>
                  <option value="customer_service">Customer Service</option>
                </select>

                <select 
                  value={statusFilter} 
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border rounded-md text-sm"
                >
                  <option value="all">All Status</option>
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                  <option value="Suspended">Suspended</option>
                </select>

                <Button variant="outline" size="sm" onClick={loadUsers}>
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                </Button>
              </div>

              {/* Users Table */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Region</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Login</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                          Loading users...
                        </TableCell>
                      </TableRow>
                    ) : filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                          No users found matching your criteria
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={user.avatar} />
                                <AvatarFallback>
                                  {user.name.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">{user.name}</div>
                                <div className="text-sm text-muted-foreground flex items-center gap-1">
                                  <Mail className="h-3 w-3" />
                                  {user.email}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {getRoleDisplayName(user.role)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3 text-muted-foreground" />
                              {user.region}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={`${getStatusColor(user.status)} flex items-center gap-1 w-fit`}>
                              {getStatusIcon(user.status)}
                              {user.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Clock className="h-3 w-3" />
                              {formatLastLogin(user.lastLogin)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Profile
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit User
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Shield className="h-4 w-4 mr-2" />
                                  Manage Permissions
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Settings className="h-4 w-4 mr-2" />
                                  Reset Password
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-red-600">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Deactivate User
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="roles" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { role: 'super_admin', count: 1, permissions: ['Full System Access'] },
                  { role: 'regional_admin', count: 5, permissions: ['Regional Management', 'User Management'] },
                  { role: 'field_technician', count: 25, permissions: ['Field Operations', 'Maintenance Records'] },
                  { role: 'maintenance_engineer', count: 15, permissions: ['Maintenance Planning', 'Technical Reports'] },
                  { role: 'customer_service', count: 10, permissions: ['Customer Support', 'Incident Management'] }
                ].map((roleInfo) => (
                  <Card key={roleInfo.role}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">{getRoleDisplayName(roleInfo.role)}</CardTitle>
                      <CardDescription>{roleInfo.count} users</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {roleInfo.permissions.map((permission) => (
                          <Badge key={permission} variant="outline" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                      </div>
                      <Button variant="outline" size="sm" className="w-full mt-3">
                        <Settings className="h-4 w-4 mr-1" />
                        Manage Role
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="activity" className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Action</TableHead>
                      <TableHead>Details</TableHead>
                      <TableHead>Timestamp</TableHead>
                      <TableHead>IP Address</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {activities.map((activity) => {
                      const user = users.find(u => u.id === activity.userId)
                      return (
                        <TableRow key={activity.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarFallback className="text-xs">
                                  {user?.name.split(' ').map(n => n[0]).join('') || 'U'}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm">{user?.name || 'Unknown User'}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{activity.action}</Badge>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {activity.details}
                          </TableCell>
                          <TableCell className="text-sm">
                            {new Date(activity.timestamp).toLocaleString()}
                          </TableCell>
                          <TableCell className="text-sm font-mono">
                            {activity.ipAddress}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                        <p className="text-2xl font-bold">{users.length}</p>
                      </div>
                      <Users className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                        <p className="text-2xl font-bold">{users.filter(u => u.status === 'Active').length}</p>
                      </div>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Online Now</p>
                        <p className="text-2xl font-bold">
                          {users.filter(u => {
                            const lastLogin = new Date(u.lastLogin)
                            const now = new Date()
                            return (now.getTime() - lastLogin.getTime()) < (60 * 60 * 1000) // 1 hour
                          }).length}
                        </p>
                      </div>
                      <Activity className="h-4 w-4 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Suspended</p>
                        <p className="text-2xl font-bold">{users.filter(u => u.status === 'Suspended').length}</p>
                      </div>
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
