/**
 * Region Service
 * Data access layer for region operations
 */

import { executeQuery } from '@/src/lib/database'
import { Region } from '@/src/types'

/**
 * Get all regions with transformer counts
 */
export async function getRegions(): Promise<Region[]> {
  try {
    const query = `
      SELECT 
        r.*,
        COUNT(t.id) as transformer_count
      FROM dtms_regions r
      LEFT JOIN dtms_transformers t ON r.id = t.region_id
      GROUP BY r.id
      ORDER BY r.name
    `
    
    const regions = await executeQuery<Region>(query)
    return regions
  } catch (error) {
    console.error('Error fetching regions:', error)
    throw new Error('Failed to fetch regions')
  }
}

/**
 * Get region by ID
 */
export async function getRegionById(id: string): Promise<Region | null> {
  try {
    const query = `
      SELECT 
        r.*,
        COUNT(t.id) as transformer_count
      FROM dtms_regions r
      LEFT JOIN dtms_transformers t ON r.id = t.region_id
      WHERE r.id = ?
      GROUP BY r.id
    `
    
    const regions = await executeQuery<Region>(query, [id])
    return regions[0] || null
  } catch (error) {
    console.error('Error fetching region:', error)
    throw new Error('Failed to fetch region')
  }
}
