"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from "@/src/components/ui/dialog"
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger
} from "@/src/components/ui/dropdown-menu"
import {
  Eye, MoreHorizontal, Edit, Trash, PowerOff, Power,
  BarChart3, AlertTriangle, Download, Wifi, WifiOff
} from "lucide-react"
import { SmartMeter } from "@/src/types/smart-meter"
import { useToast } from "@/src/components/ui/use-toast"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/src/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Label } from "@/src/components/ui/label"
import { Progress } from "@/src/components/ui/progress"

interface SmartMetersTableProps {
  searchQuery: string
  meters?: SmartMeter[]
}

export function SmartMetersTable({ searchQuery, meters = [] }: SmartMetersTableProps) {
  const { toast } = useToast()
  const [selectedMeter, setSelectedMeter] = useState<SmartMeter | null>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [powerDialogOpen, setPowerDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Use mock data if no meters are provided
  const mockMeters = [
    {
      id: "SM-10245",
      serialNumber: "ETH-SM-10245",
      manufacturer: "Landis+Gyr",
      model: "E350",
      type: "Residential",
      installDate: "2024-01-15",
      lastReading: "2024-04-27T08:00:00Z",
      lastReadingValue: 14.2,
      lastCommunication: "2024-04-27T08:05:23Z",
      firmwareVersion: "3.2.1",
      status: "Connected",
      location: {
        latitude: 9.0222,
        longitude: 38.7468,
        region: "Addis Ababa",
        serviceCenter: "Bole",
        address: "Bole Road, Addis Ababa",
        installationSite: "Residential Building"
      },
      customer: {
        id: "CUST-5678",
        name: "Abebe Kebede",
        accountNumber: "ACC-12345",
        contactPhone: "+************",
        email: "<EMAIL>"
      },
      tariffPlan: "Residential Standard",
      billingCycle: "Monthly",
      prepaid: true,
      balance: 250.75,
      batteryLevel: 92,
      signalStrength: 85
    },
    {
      id: "SM-10872",
      serialNumber: "ETH-SM-10872",
      manufacturer: "Itron",
      model: "OpenWay Riva",
      type: "Commercial",
      installDate: "2024-03-22",
      lastReading: "2024-04-27T08:00:00Z",
      lastReadingValue: 45.8,
      lastCommunication: "2024-04-27T08:10:15Z",
      firmwareVersion: "2.5.0",
      status: "Connected",
      location: {
        latitude: 9.6000,
        longitude: 41.8500,
        region: "Dire Dawa",
        serviceCenter: "Central",
        address: "Main Street, Dire Dawa",
        installationSite: "Commercial Complex"
      },
      customer: {
        id: "CUST-8901",
        name: "Dire Dawa Shopping Center",
        accountNumber: "ACC-67890",
        contactPhone: "+************",
        email: "<EMAIL>"
      },
      tariffPlan: "Commercial Premium",
      billingCycle: "Monthly",
      prepaid: false,
      batteryLevel: 88,
      signalStrength: 90
    },
    {
      id: "SM-11105",
      serialNumber: "ETH-SM-11105",
      manufacturer: "Schneider Electric",
      model: "ION7400",
      type: "Residential",
      installDate: "2024-02-10",
      lastReading: "2024-04-26T08:00:00Z",
      lastReadingValue: 8.7,
      lastCommunication: "2024-04-26T08:15:42Z",
      firmwareVersion: "1.9.3",
      status: "Disconnected",
      location: {
        latitude: 11.6000,
        longitude: 37.3900,
        region: "Amhara",
        serviceCenter: "Bahir Dar",
        address: "Lakeside Area, Bahir Dar",
        installationSite: "Residential Apartment"
      },
      customer: {
        id: "CUST-2345",
        name: "Tigist Alemu",
        accountNumber: "ACC-34567",
        contactPhone: "+************",
        email: "<EMAIL>"
      },
      tariffPlan: "Residential Basic",
      billingCycle: "Monthly",
      prepaid: true,
      balance: 0,
      batteryLevel: 45,
      signalStrength: 20
    },
    {
      id: "SM-11267",
      serialNumber: "ETH-SM-11267",
      manufacturer: "Siemens",
      model: "AMIS",
      type: "Industrial",
      installDate: "2023-11-05",
      lastReading: "2024-04-27T08:00:00Z",
      lastReadingValue: 132.5,
      lastCommunication: "2024-04-27T08:02:10Z",
      firmwareVersion: "4.1.2",
      status: "Connected",
      location: {
        latitude: 8.9806,
        longitude: 38.7578,
        region: "Oromia",
        serviceCenter: "Adama",
        address: "Industrial Zone, Adama",
        installationSite: "Manufacturing Plant"
      },
      customer: {
        id: "CUST-7890",
        name: "Ethiopian Textile Factory",
        accountNumber: "ACC-78901",
        contactPhone: "+************",
        email: "<EMAIL>"
      },
      tariffPlan: "Industrial High Usage",
      billingCycle: "Monthly",
      prepaid: false,
      batteryLevel: 95,
      signalStrength: 88
    },
    {
      id: "SM-11452",
      serialNumber: "ETH-SM-11452",
      manufacturer: "Itron",
      model: "OpenWay Riva",
      type: "Residential",
      installDate: "2024-01-30",
      lastReading: "2024-04-25T08:00:00Z",
      lastReadingValue: 0,
      lastCommunication: "2024-04-25T08:20:30Z",
      firmwareVersion: "2.5.0",
      status: "Maintenance",
      location: {
        latitude: 13.4900,
        longitude: 39.4700,
        region: "Tigray",
        serviceCenter: "Mekelle",
        address: "Central District, Mekelle",
        installationSite: "Residential Building"
      },
      customer: {
        id: "CUST-9012",
        name: "Yohannes Tesfaye",
        accountNumber: "ACC-90123",
        contactPhone: "+************",
        email: "<EMAIL>"
      },
      tariffPlan: "Residential Standard",
      billingCycle: "Monthly",
      prepaid: true,
      balance: 150.25,
      batteryLevel: 75,
      signalStrength: 60
    }
  ]

  const displayMeters = meters.length > 0 ? meters : mockMeters

  const filteredMeters = displayMeters.filter(
    (meter) =>
      meter.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      meter.location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
      meter.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      meter.type.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Handle view meter
  const handleViewMeter = (meter: SmartMeter) => {
    setSelectedMeter(meter)
    setViewDialogOpen(true)
  }

  // Handle delete meter
  const handleDeleteMeter = (meter: SmartMeter) => {
    setSelectedMeter(meter)
    setDeleteDialogOpen(true)
  }

  // Handle power action (connect/disconnect)
  const handlePowerAction = (meter: SmartMeter) => {
    setSelectedMeter(meter)
    setPowerDialogOpen(true)
  }

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedMeter) return

    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      setDeleteDialogOpen(false)

      toast({
        title: "Smart meter deleted",
        description: `Smart meter ${selectedMeter.id} has been deleted.`,
      })
    }, 1500)
  }

  // Confirm power action
  const confirmPowerAction = async () => {
    if (!selectedMeter) return

    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      setPowerDialogOpen(false)

      const action = selectedMeter.status === "Connected" ? "disconnected" : "connected"

      toast({
        title: `Smart meter ${action}`,
        description: `Smart meter ${selectedMeter.id} has been ${action}.`,
      })
    }, 1500)
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Connected":
        return "bg-green-500 hover:bg-green-600"
      case "Disconnected":
        return "bg-yellow-500 hover:bg-yellow-600"
      case "Maintenance":
        return "bg-blue-500 hover:bg-blue-600"
      case "Tampered":
        return "bg-red-500 hover:bg-red-600"
      default:
        return "bg-gray-500 hover:bg-gray-600"
    }
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Last Reading</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredMeters.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                No smart meters found.
              </TableCell>
            </TableRow>
          ) : (
            filteredMeters.map((meter) => (
              <TableRow key={meter.id}>
                <TableCell className="font-medium">{meter.id}</TableCell>
                <TableCell>{meter.location.address}</TableCell>
                <TableCell>{meter.customer.name}</TableCell>
                <TableCell>{meter.type}</TableCell>
                <TableCell>{meter.lastReadingValue} kWh</TableCell>
                <TableCell>
                  <Badge className={getStatusColor(meter.status)}>
                    {meter.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleViewMeter(meter)}
                    >
                      <Eye className="h-4 w-4" />
                      <span className="sr-only">View</span>
                    </Button>

                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handlePowerAction(meter)}
                    >
                      {meter.status === "Connected" ? (
                        <PowerOff className="h-4 w-4" />
                      ) : (
                        <Power className="h-4 w-4" />
                      )}
                      <span className="sr-only">
                        {meter.status === "Connected" ? "Disconnect" : "Connect"}
                      </span>
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">More options</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleViewMeter(meter)}>
                          <Eye className="mr-2 h-4 w-4" />
                          <span>View Details</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          <span>Edit</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <BarChart3 className="mr-2 h-4 w-4" />
                          <span>View Usage</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <AlertTriangle className="mr-2 h-4 w-4" />
                          <span>View Alerts</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Download className="mr-2 h-4 w-4" />
                          <span>Export Data</span>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handlePowerAction(meter)}>
                          {meter.status === "Connected" ? (
                            <>
                              <WifiOff className="mr-2 h-4 w-4 text-yellow-500" />
                              <span>Disconnect</span>
                            </>
                          ) : (
                            <>
                              <Wifi className="mr-2 h-4 w-4 text-green-500" />
                              <span>Connect</span>
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteMeter(meter)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash className="mr-2 h-4 w-4" />
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {/* View Meter Dialog */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          {selectedMeter && (
            <>
              <DialogHeader>
                <DialogTitle>Smart Meter Details</DialogTitle>
                <DialogDescription>
                  Detailed information about smart meter {selectedMeter.id}
                </DialogDescription>
              </DialogHeader>

              <Tabs defaultValue="details" className="mt-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="customer">Customer</TabsTrigger>
                  <TabsTrigger value="technical">Technical</TabsTrigger>
                </TabsList>

                <TabsContent value="details" className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-muted-foreground">ID</Label>
                      <p className="font-medium">{selectedMeter.id}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Serial Number</Label>
                      <p className="font-medium">{selectedMeter.serialNumber}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Manufacturer</Label>
                      <p className="font-medium">{selectedMeter.manufacturer}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Model</Label>
                      <p className="font-medium">{selectedMeter.model}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Type</Label>
                      <p className="font-medium">{selectedMeter.type}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Status</Label>
                      <p>
                        <Badge className={getStatusColor(selectedMeter.status)}>
                          {selectedMeter.status}
                        </Badge>
                      </p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Installation Date</Label>
                      <p className="font-medium">{selectedMeter.installDate}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Last Reading</Label>
                      <p className="font-medium">{selectedMeter.lastReadingValue} kWh on {formatDate(selectedMeter.lastReading)}</p>
                    </div>
                  </div>

                  <div className="mt-4">
                    <Label className="text-muted-foreground">Location</Label>
                    <p className="font-medium">{selectedMeter.location.address}</p>
                    <p className="text-sm text-muted-foreground">
                      {selectedMeter.location.region}, {selectedMeter.location.serviceCenter}
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="customer" className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-muted-foreground">Customer Name</Label>
                      <p className="font-medium">{selectedMeter.customer.name}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Account Number</Label>
                      <p className="font-medium">{selectedMeter.customer.accountNumber}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Contact Phone</Label>
                      <p className="font-medium">{selectedMeter.customer.contactPhone}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Email</Label>
                      <p className="font-medium">{selectedMeter.customer.email || "N/A"}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Tariff Plan</Label>
                      <p className="font-medium">{selectedMeter.tariffPlan}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Billing Cycle</Label>
                      <p className="font-medium">{selectedMeter.billingCycle}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Payment Type</Label>
                      <p className="font-medium">{selectedMeter.prepaid ? "Prepaid" : "Postpaid"}</p>
                    </div>
                    {selectedMeter.prepaid && (
                      <div>
                        <Label className="text-muted-foreground">Balance</Label>
                        <p className="font-medium">{selectedMeter.balance?.toFixed(2)} ETB</p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="technical" className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-muted-foreground">Firmware Version</Label>
                      <p className="font-medium">{selectedMeter.firmwareVersion}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Last Communication</Label>
                      <p className="font-medium">{formatDate(selectedMeter.lastCommunication)}</p>
                    </div>

                    <div className="col-span-2">
                      <Label className="text-muted-foreground">Battery Level</Label>
                      <div className="flex items-center gap-2">
                        <Progress value={selectedMeter.batteryLevel} className="h-2" />
                        <span className="text-sm">{selectedMeter.batteryLevel}%</span>
                      </div>
                    </div>

                    <div className="col-span-2">
                      <Label className="text-muted-foreground">Signal Strength</Label>
                      <div className="flex items-center gap-2">
                        <Progress value={selectedMeter.signalStrength} className="h-2" />
                        <span className="text-sm">{selectedMeter.signalStrength}%</span>
                      </div>
                    </div>

                    <div className="col-span-2">
                      <Label className="text-muted-foreground">Coordinates</Label>
                      <p className="font-medium">
                        {selectedMeter.location.latitude}, {selectedMeter.location.longitude}
                      </p>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <DialogFooter>
                <Button variant="outline" onClick={() => setViewDialogOpen(false)}>
                  Close
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete smart meter {selectedMeter?.id}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={isLoading}
            >
              {isLoading ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Power Action Dialog */}
      <Dialog open={powerDialogOpen} onOpenChange={setPowerDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {selectedMeter?.status === "Connected" ? "Disconnect" : "Connect"} Smart Meter
            </DialogTitle>
            <DialogDescription>
              {selectedMeter?.status === "Connected"
                ? `Are you sure you want to disconnect smart meter ${selectedMeter?.id}?`
                : `Are you sure you want to connect smart meter ${selectedMeter?.id}?`}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setPowerDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant={selectedMeter?.status === "Connected" ? "destructive" : "default"}
              onClick={confirmPowerAction}
              disabled={isLoading}
            >
              {isLoading
                ? (selectedMeter?.status === "Connected" ? "Disconnecting..." : "Connecting...")
                : (selectedMeter?.status === "Connected" ? "Disconnect" : "Connect")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
