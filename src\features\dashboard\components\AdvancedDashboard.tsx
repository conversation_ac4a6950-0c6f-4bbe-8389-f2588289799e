"use client"

import { useState, useEffect } from 'react'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import {
  BarChart2,
  Calendar,
  Settings,
  Maximize,
  Minimize,
  X,
  RefreshCw,
  PlusCircle,
  Save,
  Download,
  MoreHorizontal,
  Edit
} from 'lucide-react'
import { Widget, DashboardLayout } from '@/src/shared/types/dashboard'
import TransformerStatusWidget from './widgets/TransformerStatusWidget'
import MaintenanceScheduleWidget from './widgets/MaintenanceScheduleWidget'
import OutageMapWidget from './widgets/OutageMapWidget'
import AlertsListWidget from './widgets/AlertsListWidget'
import WeatherImpactWidget from './widgets/WeatherImpactWidget'

interface AdvancedDashboardProps {
  userId: string
}

export default function AdvancedDashboard({ userId }: AdvancedDashboardProps) {
  const [layout, setLayout] = useState<DashboardLayout>({
    widgets: [
      { id: 'transformer-status', type: 'transformer-status', position: { x: 0, y: 0, w: 6, h: 4 }, isVisible: true },
      { id: 'maintenance-schedule', type: 'maintenance-schedule', position: { x: 6, y: 0, w: 6, h: 4 }, isVisible: true },
      { id: 'alerts-list', type: 'alerts-list', position: { x: 0, y: 4, w: 4, h: 6 }, isVisible: true },
      { id: 'outage-map', type: 'outage-map', position: { x: 4, y: 4, w: 8, h: 6 }, isVisible: true },
      { id: 'weather-impact', type: 'weather-impact', position: { x: 0, y: 10, w: 12, h: 4 }, isVisible: true }
    ],
    settings: {
      autoRefresh: true,
      refreshInterval: 30000,
      theme: 'light'
    }
  })

  const [isEditing, setIsEditing] = useState(false)
  const [lastUpdated, setLastUpdated] = useState(new Date())

  useEffect(() => {
    // Load user's dashboard layout
    const savedLayout = localStorage.getItem(`dashboard-layout-${userId}`)
    if (savedLayout) {
      setLayout(JSON.parse(savedLayout))
    }
  }, [userId])

  useEffect(() => {
    // Auto-refresh functionality
    if (layout.settings.autoRefresh) {
      const interval = setInterval(() => {
        setLastUpdated(new Date())
      }, layout.settings.refreshInterval)

      return () => clearInterval(interval)
    }
  }, [layout.settings.autoRefresh, layout.settings.refreshInterval])

  const handleDragEnd = (result: any) => {
    if (!result.destination || !isEditing) return

    const newWidgets = Array.from(layout.widgets)
    const [reorderedWidget] = newWidgets.splice(result.source.index, 1)
    newWidgets.splice(result.destination.index, 0, reorderedWidget)

    const newLayout = { ...layout, widgets: newWidgets }
    setLayout(newLayout)
    localStorage.setItem(`dashboard-layout-${userId}`, JSON.stringify(newLayout))
  }

  const toggleWidget = (widgetId: string) => {
    const newLayout = {
      ...layout,
      widgets: layout.widgets.map(widget =>
        widget.id === widgetId ? { ...widget, isVisible: !widget.isVisible } : widget
      )
    }
    setLayout(newLayout)
    localStorage.setItem(`dashboard-layout-${userId}`, JSON.stringify(newLayout))
  }

  const renderWidget = (widget: Widget) => {
    if (!widget.isVisible) return null

    switch (widget.type) {
      case 'transformer-status':
        return <TransformerStatusWidget key={widget.id} />
      case 'maintenance-schedule':
        return <MaintenanceScheduleWidget key={widget.id} />
      case 'alerts-list':
        return <AlertsListWidget key={widget.id} />
      case 'outage-map':
        return <OutageMapWidget key={widget.id} />
      case 'weather-impact':
        return <WeatherImpactWidget key={widget.id} />
      default:
        return null
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* Dashboard Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Advanced Dashboard</h1>
          <p className="text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setLastUpdated(new Date())}
            className="flex items-center gap-2 px-3 py-2 text-sm border rounded-md hover:bg-accent"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </button>
          
          <button
            onClick={() => setIsEditing(!isEditing)}
            className={`flex items-center gap-2 px-3 py-2 text-sm border rounded-md hover:bg-accent ${
              isEditing ? 'bg-primary text-primary-foreground' : ''
            }`}
          >
            <Edit className="h-4 w-4" />
            {isEditing ? 'Done' : 'Edit'}
          </button>
          
          <button className="flex items-center gap-2 px-3 py-2 text-sm border rounded-md hover:bg-accent">
            <Settings className="h-4 w-4" />
            Settings
          </button>
        </div>
      </div>

      {/* Widget Grid */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="dashboard" direction="vertical">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="grid grid-cols-12 gap-6"
            >
              {layout.widgets.map((widget, index) => (
                <Draggable
                  key={widget.id}
                  draggableId={widget.id}
                  index={index}
                  isDragDisabled={!isEditing}
                >
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      className={`col-span-${widget.position.w} ${
                        snapshot.isDragging ? 'opacity-50' : ''
                      } ${isEditing ? 'ring-2 ring-primary ring-opacity-50' : ''}`}
                    >
                      {renderWidget(widget)}
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Widget Visibility Controls */}
      {isEditing && (
        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-3">Widget Visibility</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
            {layout.widgets.map((widget) => (
              <label key={widget.id} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={widget.isVisible}
                  onChange={() => toggleWidget(widget.id)}
                  className="rounded"
                />
                <span className="text-sm capitalize">
                  {widget.type.replace('-', ' ')}
                </span>
              </label>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
