/**
 * Check MySQL Tables
 * 
 * This script checks what tables exist in the dtms_eeu_db database.
 */

const mysql = require('mysql2/promise');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db'
};

async function checkTables() {
  let connection;
  
  try {
    console.log('🔍 Connecting to MySQL database...');
    connection = await mysql.createConnection(config);
    console.log('✅ Connected successfully');
    
    // Show tables
    console.log('\n📋 Existing tables:');
    const [tables] = await connection.execute('SHOW TABLES');
    
    if (tables.length === 0) {
      console.log('   No tables found - database is empty');
    } else {
      tables.forEach((table, index) => {
        const tableName = Object.values(table)[0];
        console.log(`   ${index + 1}. ${tableName}`);
      });
    }
    
    // Show database info
    console.log('\n📊 Database information:');
    const [dbInfo] = await connection.execute('SELECT DATABASE() as current_db, VERSION() as version');
    console.log(`   Current Database: ${dbInfo[0].current_db}`);
    console.log(`   MySQL Version: ${dbInfo[0].version}`);
    
    // Check foreign key settings
    console.log('\n🔧 Foreign key settings:');
    const [fkCheck] = await connection.execute('SELECT @@foreign_key_checks as fk_checks');
    console.log(`   Foreign Key Checks: ${fkCheck[0].fk_checks ? 'Enabled' : 'Disabled'}`);
    
    // If tables exist, show their structure
    if (tables.length > 0) {
      console.log('\n🏗️  Table structures:');
      for (const table of tables) {
        const tableName = Object.values(table)[0];
        console.log(`\n   Table: ${tableName}`);
        
        try {
          const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
          columns.forEach(col => {
            console.log(`     - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''}`);
          });
          
          // Count records
          const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`     Records: ${count[0].count}`);
          
        } catch (error) {
          console.log(`     Error describing table: ${error.message}`);
        }
      }
    }
    
    console.log('\n✅ Database inspection completed');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 The database dtms_eeu_db does not exist.');
      console.log('   Creating it now...');
      
      try {
        // Connect without database
        const tempConfig = { ...config };
        delete tempConfig.database;
        const tempConnection = await mysql.createConnection(tempConfig);
        
        await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS \`${config.database}\``);
        console.log(`✅ Database ${config.database} created successfully`);
        
        await tempConnection.end();
        
        // Now connect to the new database
        connection = await mysql.createConnection(config);
        console.log('✅ Connected to new database');
        console.log('📋 Database is empty and ready for migration');
        
      } catch (createError) {
        console.error('❌ Error creating database:', createError.message);
      }
    }
    
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Connection closed');
    }
  }
}

checkTables();
