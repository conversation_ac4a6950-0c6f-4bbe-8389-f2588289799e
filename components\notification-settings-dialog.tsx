"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/src/components/ui/tabs"
import { Switch } from "@/src/components/ui/switch"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Input } from "@/src/components/ui/input"
import { Checkbox } from "@/src/components/ui/checkbox"
import { useToast } from "@/src/components/ui/use-toast"
import { Bell, Mail, MessageSquare, Save, RefreshCw, Clock, AlertTriangle, Smartphone } from "lucide-react"

interface NotificationSettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function NotificationSettingsDialog({
  open,
  onOpenChange
}: NotificationSettingsDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("channels")
  
  // Notification channels
  const [emailEnabled, setEmailEnabled] = useState(true)
  const [smsEnabled, setSmsEnabled] = useState(false)
  const [pushEnabled, setPushEnabled] = useState(true)
  const [emailAddress, setEmailAddress] = useState("<EMAIL>")
  const [phoneNumber, setPhoneNumber] = useState("+251911234567")
  
  // Alert preferences
  const [criticalAlerts, setCriticalAlerts] = useState(true)
  const [warningAlerts, setWarningAlerts] = useState(true)
  const [infoAlerts, setInfoAlerts] = useState(false)
  const [maintenanceAlerts, setMaintenanceAlerts] = useState(true)
  const [outageAlerts, setOutageAlerts] = useState(true)
  const [weatherAlerts, setWeatherAlerts] = useState(false)
  
  // Schedule settings
  const [notificationFrequency, setNotificationFrequency] = useState("realtime")
  const [quietHoursEnabled, setQuietHoursEnabled] = useState(false)
  const [quietHoursStart, setQuietHoursStart] = useState("22:00")
  const [quietHoursEnd, setQuietHoursEnd] = useState("07:00")
  const [workdaysOnly, setWorkdaysOnly] = useState(false)
  
  const handleSave = () => {
    // In a real app, this would save all settings
    toast({
      title: "Notification Settings Saved",
      description: "Your notification preferences have been updated."
    })
    
    onOpenChange(false)
  }
  
  const handleReset = () => {
    // Reset to defaults
    setEmailEnabled(true)
    setSmsEnabled(false)
    setPushEnabled(true)
    setEmailAddress("<EMAIL>")
    setPhoneNumber("+251911234567")
    setCriticalAlerts(true)
    setWarningAlerts(true)
    setInfoAlerts(false)
    setMaintenanceAlerts(true)
    setOutageAlerts(true)
    setWeatherAlerts(false)
    setNotificationFrequency("realtime")
    setQuietHoursEnabled(false)
    setQuietHoursStart("22:00")
    setQuietHoursEnd("07:00")
    setWorkdaysOnly(false)
    
    toast({
      title: "Settings Reset",
      description: "Notification settings have been reset to defaults."
    })
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Notification Settings</DialogTitle>
          <DialogDescription>
            Configure how and when you receive notifications
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-2">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="channels">
              <Mail className="mr-2 h-4 w-4" />
              Channels
            </TabsTrigger>
            <TabsTrigger value="alerts">
              <AlertTriangle className="mr-2 h-4 w-4" />
              Alerts
            </TabsTrigger>
            <TabsTrigger value="schedule">
              <Clock className="mr-2 h-4 w-4" />
              Schedule
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="channels" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    <Label htmlFor="email-enabled">Email Notifications</Label>
                  </div>
                  <Switch 
                    id="email-enabled" 
                    checked={emailEnabled} 
                    onCheckedChange={setEmailEnabled} 
                  />
                </div>
                {emailEnabled && (
                  <div className="pl-6 mt-2">
                    <Label htmlFor="email-address" className="text-sm mb-1 block">Email Address</Label>
                    <Input 
                      id="email-address" 
                      value={emailAddress} 
                      onChange={(e) => setEmailAddress(e.target.value)} 
                      placeholder="<EMAIL>"
                    />
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4" />
                    <Label htmlFor="sms-enabled">SMS Notifications</Label>
                  </div>
                  <Switch 
                    id="sms-enabled" 
                    checked={smsEnabled} 
                    onCheckedChange={setSmsEnabled} 
                  />
                </div>
                {smsEnabled && (
                  <div className="pl-6 mt-2">
                    <Label htmlFor="phone-number" className="text-sm mb-1 block">Phone Number</Label>
                    <Input 
                      id="phone-number" 
                      value={phoneNumber} 
                      onChange={(e) => setPhoneNumber(e.target.value)} 
                      placeholder="+251911234567"
                    />
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Smartphone className="h-4 w-4" />
                    <Label htmlFor="push-enabled">Push Notifications</Label>
                  </div>
                  <Switch 
                    id="push-enabled" 
                    checked={pushEnabled} 
                    onCheckedChange={setPushEnabled} 
                  />
                </div>
                {pushEnabled && (
                  <div className="pl-6 mt-2">
                    <p className="text-sm text-muted-foreground">
                      Push notifications will be sent to this device and any other devices where you're logged in.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="alerts" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium mb-2">Alert Types</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="critical-alerts" 
                      checked={criticalAlerts} 
                      onCheckedChange={(checked) => setCriticalAlerts(checked as boolean)} 
                    />
                    <label
                      htmlFor="critical-alerts"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Critical Alerts
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="warning-alerts" 
                      checked={warningAlerts} 
                      onCheckedChange={(checked) => setWarningAlerts(checked as boolean)} 
                    />
                    <label
                      htmlFor="warning-alerts"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Warning Alerts
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="info-alerts" 
                      checked={infoAlerts} 
                      onCheckedChange={(checked) => setInfoAlerts(checked as boolean)} 
                    />
                    <label
                      htmlFor="info-alerts"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Information Alerts
                    </label>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium mb-2">Event Types</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="maintenance-alerts" 
                      checked={maintenanceAlerts} 
                      onCheckedChange={(checked) => setMaintenanceAlerts(checked as boolean)} 
                    />
                    <label
                      htmlFor="maintenance-alerts"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Maintenance Events
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="outage-alerts" 
                      checked={outageAlerts} 
                      onCheckedChange={(checked) => setOutageAlerts(checked as boolean)} 
                    />
                    <label
                      htmlFor="outage-alerts"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Outage Events
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="weather-alerts" 
                      checked={weatherAlerts} 
                      onCheckedChange={(checked) => setWeatherAlerts(checked as boolean)} 
                    />
                    <label
                      htmlFor="weather-alerts"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Weather Alerts
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="schedule" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="notification-frequency">Notification Frequency</Label>
                <Select value={notificationFrequency} onValueChange={setNotificationFrequency}>
                  <SelectTrigger id="notification-frequency">
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="realtime">Real-time (Immediate)</SelectItem>
                    <SelectItem value="hourly">Hourly Digest</SelectItem>
                    <SelectItem value="daily">Daily Digest</SelectItem>
                    <SelectItem value="weekly">Weekly Summary</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="quiet-hours">Quiet Hours</Label>
                  <Switch 
                    id="quiet-hours" 
                    checked={quietHoursEnabled} 
                    onCheckedChange={setQuietHoursEnabled} 
                  />
                </div>
                {quietHoursEnabled && (
                  <div className="pl-6 mt-2 grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="quiet-start" className="text-sm">Start Time</Label>
                      <Input 
                        id="quiet-start" 
                        type="time" 
                        value={quietHoursStart} 
                        onChange={(e) => setQuietHoursStart(e.target.value)} 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="quiet-end" className="text-sm">End Time</Label>
                      <Input 
                        id="quiet-end" 
                        type="time" 
                        value={quietHoursEnd} 
                        onChange={(e) => setQuietHoursEnd(e.target.value)} 
                      />
                    </div>
                  </div>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="workdays-only" 
                  checked={workdaysOnly} 
                  onCheckedChange={(checked) => setWorkdaysOnly(checked as boolean)} 
                />
                <label
                  htmlFor="workdays-only"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Send notifications on workdays only (Monday-Friday)
                </label>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="flex justify-between items-center mt-4">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
