const mysql = require('mysql2');

// Database connection configuration
const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '', // Replace with your MySQL root password
  database: 'dtms_eeu_db',
  multipleStatements: true,
});

// List of tables to merge
const tablesToMerge = [
  { primary: 'app_alerts', duplicates: ['alerts', 'alerts_v1748141826022'] },
  { primary: 'app_maintenance_schedules', duplicates: ['maintenance_schedules', 'maintenance_schedules_v1748141826022'] },
  { primary: 'app_notifications', duplicates: ['notifications_v1748141826022'] },
  { primary: 'app_performance_metrics', duplicates: ['performance_metrics_v1748141826022'] },
  { primary: 'app_regions', duplicates: ['regions_v1748141826022'] },
  { primary: 'app_service_centers', duplicates: ['service_centers_v1748141826022'] },
  { primary: 'app_transformers', duplicates: ['transformers', 'transformers_v1748141826022'] },
  { primary: 'app_users', duplicates: ['users_v1748141826022'] },
  { primary: 'app_weather_data', duplicates: ['weather_data_v1748141826022'] },
];

// Function to merge tables
const mergeTables = async () => {
  for (const { primary, duplicates } of tablesToMerge) {
    for (const duplicate of duplicates) {
      const query = `
        INSERT IGNORE INTO \
          ${primary} \
        SELECT * FROM \
          ${duplicate};
        DROP TABLE IF EXISTS \
          ${duplicate};
      `;

      connection.query(query, (error) => {
        if (error) {
          console.error(`Error merging table ${duplicate} into ${primary}:`, error);
        } else {
          console.log(`Successfully merged table ${duplicate} into ${primary}.`);
        }
      });
    }
  }

  // Close the connection
  connection.end();
};

mergeTables();
