// drop-app-transformers-view.js
// Drops the app_transformers view if it exists, to resolve shadowing issues

const mysql = require('mysql2/promise');
require('dotenv').config();

async function dropView() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'dtms_eeu_db',
    multipleStatements: true
  });
  try {
    // Drop the view if it exists
    await connection.execute('DROP VIEW IF EXISTS app_transformers');
    console.log('View app_transformers dropped (if it existed).');
  } catch (err) {
    console.error('Error dropping view:', err);
  } finally {
    await connection.end();
  }
}

dropView();
