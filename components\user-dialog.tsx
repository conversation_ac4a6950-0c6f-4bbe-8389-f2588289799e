"use client"

import type React from "react"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Switch } from "@/src/components/ui/switch"
import type { User, UserRole } from "@/src/types/auth"

interface UserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: User | null
  onSave: (user: User) => void
  currentUserRole?: UserRole
  currentUserRegionId?: string
  currentUserServiceCenterId?: string
}

export function UserDialog({
  open,
  onOpenChange,
  user,
  onSave,
  currentUserRole,
  currentUserRegionId,
  currentUserServiceCenterId,
}: UserDialogProps) {
  const [formData, setFormData] = useState<Partial<User>>({
    name: "",
    email: "",
    role: "field_technician",
    organizationalLevel: "service_center",
    regionId: "",
    serviceCenterId: "",
    isActive: true,
    permissions: [],
  })

  // Reset form when dialog opens or user changes
  useEffect(() => {
    if (user) {
      setFormData({
        ...user,
      })
    } else {
      setFormData({
        name: "",
        email: "",
        role: "field_technician",
        organizationalLevel: "service_center",
        regionId: currentUserRegionId || "",
        serviceCenterId: currentUserServiceCenterId || "",
        isActive: true,
        permissions: [],
      })
    }
  }, [user, open, currentUserRegionId, currentUserServiceCenterId])

  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))

    // Update organizational level based on role
    if (field === "role") {
      const role = value as UserRole
      let organizationalLevel = "service_center"

      if (
        ["super_admin", "national_asset_manager", "national_maintenance_manager", "audit_compliance_officer"].includes(
          role,
        )
      ) {
        organizationalLevel = "head_office"
      } else if (["regional_admin", "regional_asset_manager", "regional_maintenance_engineer"].includes(role)) {
        organizationalLevel = "regional_office"
      }

      setFormData((prev) => ({
        ...prev,
        organizationalLevel,
      }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave({
      id: user?.id || `user-${Date.now()}`,
      name: formData.name || "",
      email: formData.email || "",
      role: formData.role as UserRole,
      organizationalLevel: formData.organizationalLevel || "service_center",
      regionId: formData.regionId,
      serviceCenterId: formData.serviceCenterId,
      isActive: formData.isActive || false,
      permissions: formData.permissions || [],
      lastLogin: user?.lastLogin || new Date().toISOString(),
      avatar: user?.avatar || `/placeholder.svg?height=40&width=40&text=${formData.name?.charAt(0) || "U"}`,
    })
  }

  // Get available roles based on current user's role
  const getAvailableRoles = () => {
    if (currentUserRole === "super_admin") {
      return [
        { value: "super_admin", label: "Super Admin" },
        { value: "national_asset_manager", label: "National Asset Manager" },
        { value: "national_maintenance_manager", label: "National Maintenance Manager" },
        { value: "regional_admin", label: "Regional Admin" },
        { value: "regional_asset_manager", label: "Regional Asset Manager" },
        { value: "regional_maintenance_engineer", label: "Regional Maintenance Engineer" },
        { value: "service_center_manager", label: "Service Center Manager" },
        { value: "field_technician", label: "Field Technician" },
        { value: "customer_service_agent", label: "Customer Service Agent" },
        { value: "audit_compliance_officer", label: "Audit & Compliance Officer" },
      ]
    } else if (currentUserRole === "regional_admin") {
      return [
        { value: "regional_asset_manager", label: "Regional Asset Manager" },
        { value: "regional_maintenance_engineer", label: "Regional Maintenance Engineer" },
        { value: "service_center_manager", label: "Service Center Manager" },
        { value: "field_technician", label: "Field Technician" },
        { value: "customer_service_agent", label: "Customer Service Agent" },
      ]
    } else if (currentUserRole === "service_center_manager") {
      return [
        { value: "field_technician", label: "Field Technician" },
        { value: "customer_service_agent", label: "Customer Service Agent" },
      ]
    }
    return []
  }

  // Get available regions based on current user's role and region
  const getAvailableRegions = () => {
    if (currentUserRole === "super_admin") {
      return [
        { value: "region-001", label: "Addis Ababa" },
        { value: "region-002", label: "Oromia" },
        { value: "region-003", label: "Amhara" },
        { value: "region-004", label: "Tigray" },
        { value: "region-005", label: "SNNPR" },
        { value: "region-006", label: "Sidama" },
        { value: "region-007", label: "Afar" },
        { value: "region-008", label: "Somali" },
        { value: "region-009", label: "Benishangul-Gumuz" },
        { value: "region-010", label: "Gambela" },
        { value: "region-011", label: "Harari" },
        { value: "region-012", label: "Dire Dawa" },
      ]
    } else if (currentUserRole === "regional_admin" && currentUserRegionId) {
      return [
        {
          value: currentUserRegionId,
          label: currentUserRegionId === "region-001" ? "Addis Ababa" : currentUserRegionId,
        },
      ]
    } else if (currentUserRole === "service_center_manager" && currentUserRegionId) {
      return [
        {
          value: currentUserRegionId,
          label: currentUserRegionId === "region-001" ? "Addis Ababa" : currentUserRegionId,
        },
      ]
    }
    return []
  }

  // Get available service centers based on current user's role, region, and service center
  const getAvailableServiceCenters = () => {
    if (currentUserRole === "super_admin" || currentUserRole === "regional_admin") {
      if (formData.regionId === "region-001") {
        return [
          { value: "sc-001", label: "Bole" },
          { value: "sc-002", label: "Kirkos" },
          { value: "sc-003", label: "Arada" },
          { value: "sc-004", label: "Yeka" },
          { value: "sc-005", label: "Akaki" },
        ]
      }
    } else if (currentUserRole === "service_center_manager" && currentUserServiceCenterId) {
      return [
        {
          value: currentUserServiceCenterId,
          label: currentUserServiceCenterId === "sc-001" ? "Bole" : currentUserServiceCenterId,
        },
      ]
    }
    return []
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>{user ? "Edit User" : "Add New User"}</DialogTitle>
            <DialogDescription>
              {user
                ? "Update user details, role, and permissions."
                : "Create a new user with specific role and permissions."}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={formData.name || ""}
                  onChange={(e) => handleChange("name", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email || ""}
                  onChange={(e) => handleChange("email", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Select
                value={formData.role}
                onValueChange={(value) => handleChange("role", value)}
                disabled={user?.role === "super_admin" && currentUserRole !== "super_admin"}
              >
                <SelectTrigger id="role">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  {getAvailableRoles().map((role) => (
                    <SelectItem key={role.value} value={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {formData.organizationalLevel !== "head_office" && (
              <div className="space-y-2">
                <Label htmlFor="region">Region</Label>
                <Select
                  value={formData.regionId}
                  onValueChange={(value) => handleChange("regionId", value)}
                  disabled={currentUserRole !== "super_admin" && currentUserRegionId !== undefined}
                >
                  <SelectTrigger id="region">
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    {getAvailableRegions().map((region) => (
                      <SelectItem key={region.value} value={region.value}>
                        {region.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {formData.organizationalLevel === "service_center" && (
              <div className="space-y-2">
                <Label htmlFor="serviceCenter">Service Center</Label>
                <Select
                  value={formData.serviceCenterId}
                  onValueChange={(value) => handleChange("serviceCenterId", value)}
                  disabled={currentUserRole === "service_center_manager"}
                >
                  <SelectTrigger id="serviceCenter">
                    <SelectValue placeholder="Select service center" />
                  </SelectTrigger>
                  <SelectContent>
                    {getAvailableServiceCenters().map((sc) => (
                      <SelectItem key={sc.value} value={sc.value}>
                        {sc.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleChange("isActive", checked)}
              />
              <Label htmlFor="isActive">Active Account</Label>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">{user ? "Update User" : "Create User"}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
