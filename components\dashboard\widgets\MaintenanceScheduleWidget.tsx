"use client"

import { useState, useEffect } from 'react'
import { Calendar, Clock, CheckCircle, AlertCircle, AlertTriangle, ChevronRight } from 'lucide-react'
import { MaintenanceScheduleWidget as MaintenanceScheduleWidgetType } from '../../../types/dashboard-widgets'

interface MaintenanceScheduleWidgetProps {
  widget: MaintenanceScheduleWidgetType
  isEditing: boolean
}

export default function MaintenanceScheduleWidget({ widget, isEditing }: MaintenanceScheduleWidgetProps) {
  const [data, setData] = useState({
    upcoming: [
      {
        id: 'maint-001',
        transformerId: 'tr-001',
        transformerName: 'Bole Sub-station T1',
        type: 'preventive',
        priority: 'medium',
        status: 'scheduled',
        scheduledDate: '2023-06-15T09:00:00Z',
        assignedTeam: 'Team Alpha',
        location: 'Bole, Addis Ababa'
      },
      {
        id: 'maint-002',
        transformerId: 'tr-015',
        transformerName: 'Kirkos Sub-station T3',
        type: 'corrective',
        priority: 'high',
        status: 'scheduled',
        scheduledDate: '2023-06-16T10:30:00Z',
        assignedTeam: 'Team Bravo',
        location: 'Kirkos, Addis Ababa'
      },
      {
        id: 'maint-003',
        transformerId: 'tr-042',
        transformerName: 'Arada Sub-station T2',
        type: 'predictive',
        priority: 'medium',
        status: 'scheduled',
        scheduledDate: '2023-06-18T08:00:00Z',
        assignedTeam: 'Team Charlie',
        location: 'Arada, Addis Ababa'
      }
    ],
    overdue: [
      {
        id: 'maint-004',
        transformerId: 'tr-023',
        transformerName: 'Yeka Sub-station T1',
        type: 'preventive',
        priority: 'high',
        status: 'overdue',
        scheduledDate: '2023-06-10T14:00:00Z',
        assignedTeam: 'Team Delta',
        location: 'Yeka, Addis Ababa'
      }
    ],
    inProgress: [
      {
        id: 'maint-005',
        transformerId: 'tr-037',
        transformerName: 'Lideta Sub-station T4',
        type: 'corrective',
        priority: 'critical',
        status: 'in-progress',
        scheduledDate: '2023-06-14T09:00:00Z',
        assignedTeam: 'Team Echo',
        location: 'Lideta, Addis Ababa'
      }
    ],
    completed: [
      {
        id: 'maint-006',
        transformerId: 'tr-019',
        transformerName: 'Akaki Sub-station T2',
        type: 'preventive',
        priority: 'medium',
        status: 'completed',
        scheduledDate: '2023-06-12T11:00:00Z',
        completedDate: '2023-06-12T15:30:00Z',
        assignedTeam: 'Team Foxtrot',
        location: 'Akaki, Addis Ababa'
      }
    ]
  })
  
  const [isLoading, setIsLoading] = useState(false)
  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>(
    widget.settings?.timeRange || 'week'
  )
  
  // Fetch data
  useEffect(() => {
    // In a real implementation, this would fetch from the API
    // For now, we're using mock data initialized above
  }, [widget.id, timeRange])
  
  // Refresh data
  const refreshData = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-blue-100 text-blue-800'
      case 'medium':
        return 'bg-green-100 text-green-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'critical':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
  
  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Calendar size={14} className="text-blue-500" />
      case 'in-progress':
        return <Clock size={14} className="text-green-500" />
      case 'completed':
        return <CheckCircle size={14} className="text-green-500" />
      case 'overdue':
        return <AlertTriangle size={14} className="text-red-500" />
      default:
        return <AlertCircle size={14} className="text-gray-500" />
    }
  }
  
  // Render maintenance item
  const renderMaintenanceItem = (item: any) => (
    <div key={item.id} className="border-b last:border-b-0 py-2">
      <div className="flex items-start">
        <div className="mt-0.5 mr-2">
          {getStatusIcon(item.status)}
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <div className="font-medium text-sm">{item.transformerName}</div>
            <span className={`text-xs rounded-full px-1.5 py-0.5 ${getPriorityColor(item.priority)}`}>
              {item.priority}
            </span>
          </div>
          <div className="text-xs text-gray-500 mt-0.5">{item.location}</div>
          <div className="flex items-center justify-between mt-1">
            <div className="text-xs text-gray-500">
              {formatDate(item.scheduledDate)}
            </div>
            <div className="text-xs text-gray-500">
              {item.assignedTeam}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
  
  return (
    <div>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center space-x-2">
          <button
            className={`px-2 py-0.5 rounded text-xs ${timeRange === 'day' ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setTimeRange('day')}
          >
            Day
          </button>
          
          <button
            className={`px-2 py-0.5 rounded text-xs ${timeRange === 'week' ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setTimeRange('week')}
          >
            Week
          </button>
          
          <button
            className={`px-2 py-0.5 rounded text-xs ${timeRange === 'month' ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setTimeRange('month')}
          >
            Month
          </button>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex items-center justify-center h-48">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-green-500"></div>
        </div>
      ) : (
        <div>
          {/* Overdue */}
          {widget.settings?.showOverdue && data.overdue.length > 0 && (
            <div className="mb-3">
              <h4 className="text-sm font-medium text-red-600 mb-1 flex items-center">
                <AlertTriangle size={14} className="mr-1" />
                Overdue
              </h4>
              <div className="bg-red-50 rounded-lg p-2">
                {data.overdue.map(renderMaintenanceItem)}
              </div>
            </div>
          )}
          
          {/* In Progress */}
          {data.inProgress.length > 0 && (
            <div className="mb-3">
              <h4 className="text-sm font-medium text-green-600 mb-1 flex items-center">
                <Clock size={14} className="mr-1" />
                In Progress
              </h4>
              <div className="bg-green-50 rounded-lg p-2">
                {data.inProgress.map(renderMaintenanceItem)}
              </div>
            </div>
          )}
          
          {/* Upcoming */}
          {data.upcoming.length > 0 && (
            <div className="mb-3">
              <h4 className="text-sm font-medium text-blue-600 mb-1 flex items-center">
                <Calendar size={14} className="mr-1" />
                Upcoming
              </h4>
              <div className="bg-blue-50 rounded-lg p-2">
                {data.upcoming.map(renderMaintenanceItem)}
              </div>
            </div>
          )}
          
          {/* Completed */}
          {widget.settings?.showCompleted && data.completed.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-600 mb-1 flex items-center">
                <CheckCircle size={14} className="mr-1" />
                Completed
              </h4>
              <div className="bg-gray-50 rounded-lg p-2">
                {data.completed.map(renderMaintenanceItem)}
              </div>
            </div>
          )}
          
          <div className="mt-3 text-center">
            <a href="/maintenance/schedule" className="text-green-600 text-sm hover:underline inline-flex items-center">
              View full schedule
              <ChevronRight size={14} className="ml-1" />
            </a>
          </div>
        </div>
      )}
    </div>
  )
}
