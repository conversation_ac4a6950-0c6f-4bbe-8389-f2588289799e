/**
 * Alert Service
 * Data access layer for alert operations
 */

import { executeQuery } from '@/src/lib/database'
import { Alert, FilterOptions } from '@/src/types'

/**
 * Get active alerts with optional filtering
 */
export async function getActiveAlerts(filters: FilterOptions = {}): Promise<Alert[]> {
  try {
    let query = `
      SELECT 
        a.*,
        t.name as transformer_name,
        t.serial_number as transformer_serial,
        r.name as region_name,
        sc.name as service_center_name
      FROM dtms_alerts a
      LEFT JOIN dtms_transformers t ON a.transformer_id = t.id
      LEFT JOIN dtms_regions r ON t.region_id = r.id
      LEFT JOIN dtms_service_centers sc ON t.service_center_id = sc.id
      WHERE a.status = 'active'
    `
    
    const params: any[] = []
    
    // Apply region filter
    if (filters.regions?.length) {
      query += ` AND r.code IN (${filters.regions.map(() => '?').join(',')})`
      params.push(...filters.regions)
    }
    
    // Apply service center filter
    if (filters.serviceCenters?.length) {
      query += ` AND sc.code IN (${filters.serviceCenters.map(() => '?').join(',')})`
      params.push(...filters.serviceCenters)
    }
    
    query += ` ORDER BY a.severity DESC, a.created_at DESC LIMIT 100`
    
    const alerts = await executeQuery<Alert>(query, params)
    return alerts
  } catch (error) {
    console.error('Error fetching active alerts:', error)
    return [] // Return empty array instead of throwing
  }
}

/**
 * Get alert by ID
 */
export async function getAlertById(id: string): Promise<Alert | null> {
  try {
    const query = `
      SELECT 
        a.*,
        t.name as transformer_name,
        t.serial_number as transformer_serial
      FROM dtms_alerts a
      LEFT JOIN dtms_transformers t ON a.transformer_id = t.id
      WHERE a.id = ?
    `
    
    const alerts = await executeQuery<Alert>(query, [id])
    return alerts[0] || null
  } catch (error) {
    console.error('Error fetching alert:', error)
    throw new Error('Failed to fetch alert')
  }
}

/**
 * Acknowledge an alert
 */
export async function acknowledgeAlert(id: string, userId: string): Promise<boolean> {
  try {
    const query = `
      UPDATE dtms_alerts 
      SET 
        status = 'acknowledged',
        acknowledged_by = ?,
        acknowledged_at = NOW(),
        updated_at = NOW()
      WHERE id = ?
    `
    
    await executeQuery(query, [userId, id])
    return true
  } catch (error) {
    console.error('Error acknowledging alert:', error)
    throw new Error('Failed to acknowledge alert')
  }
}
