import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const region = searchParams.get('region') || ''
    const offset = (page - 1) * limit

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    // Build WHERE clause
    let whereConditions = []
    let queryParams = []

    if (search) {
      whereConditions.push('(t.name LIKE ? OR t.serial_number LIKE ? OR t.location LIKE ?)')
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`)
    }

    if (status) {
      whereConditions.push('t.status = ?')
      queryParams.push(status)
    }

    if (region) {
      whereConditions.push('t.region_id = ?')
      queryParams.push(region)
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : ''

    // Get transformers with detailed information
    const transformers = await executeQuery(`
      SELECT 
        t.*,
        r.name as region_name,
        sc.name as service_center_name,
        COALESCE(ms.next_maintenance, 'Not Scheduled') as next_maintenance,
        COALESCE(ms.last_maintenance, 'Never') as last_maintenance,
        COALESCE(a.alert_count, 0) as active_alerts,
        pm.uptime_percentage,
        pm.efficiency_rating
      FROM app_transformers t
      LEFT JOIN app_regions r ON t.region_id = r.id
      LEFT JOIN app_service_centers sc ON t.service_center_id = sc.id
      LEFT JOIN (
        SELECT 
          transformer_id,
          MAX(scheduled_date) as next_maintenance,
          MAX(CASE WHEN status = 'completed' THEN completed_date END) as last_maintenance
        FROM app_maintenance_schedules
        GROUP BY transformer_id
      ) ms ON t.id = ms.transformer_id
      LEFT JOIN (
        SELECT 
          transformer_id,
          COUNT(*) as alert_count
        FROM app_alerts
        WHERE status = 'active'
        GROUP BY transformer_id
      ) a ON t.id = a.transformer_id
      LEFT JOIN (
        SELECT 
          transformer_id,
          AVG(uptime_percentage) as uptime_percentage,
          AVG(efficiency_rating) as efficiency_rating
        FROM app_performance_metrics
        WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY transformer_id
      ) pm ON t.id = pm.transformer_id
      ${whereClause}
      ORDER BY t.created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset])

    // Get total count
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total
      FROM app_transformers t
      LEFT JOIN app_regions r ON t.region_id = r.id
      LEFT JOIN app_service_centers sc ON t.service_center_id = sc.id
      ${whereClause}
    `, queryParams)

    const total = totalResult[0]?.total || 0

    // Get summary statistics
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'operational' THEN 1 ELSE 0 END) as operational,
        SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as maintenance,
        SUM(CASE WHEN status = 'faulty' THEN 1 ELSE 0 END) as faulty,
        SUM(CASE WHEN status = 'offline' THEN 1 ELSE 0 END) as offline,
        AVG(load_percentage) as avg_load,
        AVG(efficiency) as avg_efficiency
      FROM app_transformers t
      ${whereClause}
    `, queryParams)

    return NextResponse.json({
      success: true,
      data: {
        transformers: transformers.map((t: any) => ({
          ...t,
          uptime_percentage: Math.round(t.uptime_percentage || 0),
          efficiency_rating: Math.round(t.efficiency_rating || 0),
          health_score: Math.round(((t.uptime_percentage || 0) + (t.efficiency_rating || 0)) / 2)
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        statistics: stats[0]
      }
    })

  } catch (error) {
    console.error('❌ Error fetching unified management data:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch unified management data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, transformerId, data } = body

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    switch (action) {
      case 'update_status':
        await executeQuery(`
          UPDATE app_transformers 
          SET status = ?, updated_at = NOW()
          WHERE id = ?
        `, [data.status, transformerId])
        break

      case 'schedule_maintenance':
        await executeQuery(`
          INSERT INTO app_maintenance_schedules 
          (transformer_id, scheduled_date, maintenance_type, priority, description, created_at)
          VALUES (?, ?, ?, ?, ?, NOW())
        `, [
          transformerId,
          data.scheduledDate,
          data.maintenanceType,
          data.priority,
          data.description
        ])
        break

      case 'update_load':
        await executeQuery(`
          UPDATE app_transformers 
          SET load_percentage = ?, updated_at = NOW()
          WHERE id = ?
        `, [data.loadPercentage, transformerId])
        break

      case 'bulk_action':
        const { transformerIds, bulkAction, bulkData } = data
        
        if (bulkAction === 'update_status') {
          await executeQuery(`
            UPDATE app_transformers 
            SET status = ?, updated_at = NOW()
            WHERE id IN (${transformerIds.map(() => '?').join(',')})
          `, [bulkData.status, ...transformerIds])
        } else if (bulkAction === 'schedule_maintenance') {
          const values = transformerIds.map((id: number) => 
            `(${id}, '${bulkData.scheduledDate}', '${bulkData.maintenanceType}', '${bulkData.priority}', '${bulkData.description}', NOW())`
          ).join(',')
          
          await executeQuery(`
            INSERT INTO app_maintenance_schedules 
            (transformer_id, scheduled_date, maintenance_type, priority, description, created_at)
            VALUES ${values}
          `)
        }
        break

      case 'export_data':
        const exportData = await executeQuery(`
          SELECT 
            t.name,
            t.serial_number,
            t.status,
            t.location,
            t.kva_rating,
            t.voltage_primary,
            t.voltage_secondary,
            t.load_percentage,
            t.efficiency,
            r.name as region,
            sc.name as service_center
          FROM app_transformers t
          LEFT JOIN app_regions r ON t.region_id = r.id
          LEFT JOIN app_service_centers sc ON t.service_center_id = sc.id
          ORDER BY t.name
        `)

        return NextResponse.json({
          success: true,
          data: exportData,
          message: 'Export data generated successfully'
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: 'Action completed successfully'
    })

  } catch (error) {
    console.error('❌ Error processing unified management action:', error)
    return NextResponse.json(
      {
        error: 'Failed to process action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    // Build update query
    const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ')
    const updateValues = Object.values(updateData)

    await executeQuery(`
      UPDATE app_transformers 
      SET ${updateFields}, updated_at = NOW()
      WHERE id = ?
    `, [...updateValues, id])

    // Get updated transformer
    const updatedTransformer = await executeQuery(`
      SELECT * FROM app_transformers WHERE id = ?
    `, [id])

    return NextResponse.json({
      success: true,
      data: updatedTransformer[0],
      message: 'Transformer updated successfully'
    })

  } catch (error) {
    console.error('❌ Error updating transformer:', error)
    return NextResponse.json(
      {
        error: 'Failed to update transformer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Transformer ID is required' },
        { status: 400 }
      )
    }

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    // Soft delete transformer
    await executeQuery(`
      UPDATE app_transformers 
      SET status = 'decommissioned', updated_at = NOW()
      WHERE id = ?
    `, [id])

    return NextResponse.json({
      success: true,
      message: 'Transformer decommissioned successfully'
    })

  } catch (error) {
    console.error('❌ Error decommissioning transformer:', error)
    return NextResponse.json(
      {
        error: 'Failed to decommission transformer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
