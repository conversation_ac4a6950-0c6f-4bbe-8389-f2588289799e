'use client'

import { MainLayout } from "@/src/components/layout/main-layout"
import SimpleTransformerList from '@/components/transformers/SimpleTransformerList'

export default function TransformersPage() {
  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "national_maintenance_manager",
        "regional_admin",
        "regional_asset_manager",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician"
      ]}
      requiredPermissions={[{ resource: "transformers", action: "read" }]}
    >
      <SimpleTransformerList />
    </MainLayout>
  )
}
