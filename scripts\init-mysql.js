/**
 * MySQL Database Initialization Script
 *
 * This script initializes the MySQL database with schema and seed data.
 * It can be run manually or as part of the application startup process.
 */

// Set environment variables for MySQL connection
process.env.MYSQL_HOST = process.env.MYSQL_HOST || 'localhost';
process.env.MYSQL_PORT = process.env.MYSQL_PORT || '3306';
process.env.MYSQL_USER = process.env.MYSQL_USER || 'root';
process.env.MYSQL_PASSWORD = process.env.MYSQL_PASSWORD || '';
process.env.MYSQL_DATABASE = process.env.MYSQL_DATABASE || 'eeu_transformer';

// Import required modules
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Get command line arguments
const args = process.argv.slice(2);
const force = args.includes('--force');

// MySQL connection configuration
const config = {
  host: process.env.MYSQL_HOST,
  port: parseInt(process.env.MYSQL_PORT, 10),
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
};

/**
 * Create the database if it doesn't exist
 */
async function createDatabase() {
  let connection;

  try {
    // Connect to MySQL server
    connection = await mysql.createConnection(config);

    // Create database if it doesn't exist
    await connection.execute(`CREATE DATABASE IF NOT EXISTS ${process.env.MYSQL_DATABASE}`);

    console.log(`Database '${process.env.MYSQL_DATABASE}' created or already exists`);
  } catch (error) {
    console.error('Error creating database:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * Initialize the database schema
 */
async function initializeSchema() {
  let connection;

  try {
    // Connect to the database
    connection = await mysql.createConnection({
      ...config,
      database: process.env.MYSQL_DATABASE,
    });

    // Read the schema file
    const schemaPath = path.join(__dirname, '..', 'lib', 'mysql.ts');
    const schemaContent = fs.readFileSync(schemaPath, 'utf8');

    // Extract the schema SQL from the file
    const schemaMatch = schemaContent.match(/const schema = `([\s\S]*?)`;/);

    if (!schemaMatch || !schemaMatch[1]) {
      throw new Error('Could not extract schema SQL from mysql.ts');
    }

    const schema = schemaMatch[1];

    // Add auth tables to schema
    const authSchema = `
    -- Create users_auth table for authentication
    CREATE TABLE IF NOT EXISTS users_auth (
      user_id VARCHAR(36) PRIMARY KEY,
      email VARCHAR(255) UNIQUE NOT NULL,
      password_hash VARCHAR(255) NOT NULL,
      salt VARCHAR(255) NOT NULL,
      reset_token VARCHAR(255),
      reset_token_expires TIMESTAMP NULL,
      last_login TIMESTAMP NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );

    -- Create sessions table
    CREATE TABLE IF NOT EXISTS sessions (
      id VARCHAR(36) PRIMARY KEY,
      user_id VARCHAR(36) NOT NULL,
      token VARCHAR(255) NOT NULL,
      expires TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );
    `;

    const fullSchema = schema + authSchema;

    // Drop tables if force is true
    if (force) {
      console.log('Dropping existing tables...');

      // Disable foreign key checks
      await connection.execute('SET FOREIGN_KEY_CHECKS = 0');

      const tables = [
        'sessions',
        'users_auth',
        'weather_alerts',
        'outages',
        'alerts',
        'test_results',
        'inspection_records',
        'maintenance_records',
        'transformers',
        'service_centers',
        'users',
        'regions'
      ];

      for (const table of tables) {
        await connection.execute(`DROP TABLE IF EXISTS ${table}`);
      }

      // Re-enable foreign key checks
      await connection.execute('SET FOREIGN_KEY_CHECKS = 1');

      console.log('All tables dropped successfully');
    }

    // Split the schema into individual statements
    const statements = fullSchema
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);

    // Execute each statement
    for (const statement of statements) {
      await connection.execute(statement + ';');
    }

    console.log('MySQL schema initialized successfully');
  } catch (error) {
    console.error('Error initializing schema:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * Seed initial data
 */
async function seedInitialData() {
  let connection;

  try {
    // Connect to the database
    connection = await mysql.createConnection({
      ...config,
      database: process.env.MYSQL_DATABASE,
    });

    // Check if we already have users
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');

    if (users[0].count > 0 && !force) {
      console.log('Database already has data, skipping seed');
      return;
    }

    console.log('Seeding initial data...');

    // Create users
    const adminId = uuidv4();
    const assetManagerId = uuidv4();
    const maintenanceManagerId = uuidv4();
    const regionalAdminId = uuidv4();
    const technicianId = uuidv4();

    // Helper function to hash password
    function hashPassword(password, salt) {
      const crypto = require('crypto');
      return crypto.createHash('sha256').update(password + salt).digest('hex');
    }

    // Helper function to generate salt
    function generateSalt() {
      const crypto = require('crypto');
      return crypto.randomBytes(16).toString('hex');
    }

    // Create users with auth records
    const defaultPassword = 'password123';
    const defaultSalt = generateSalt();
    const defaultPasswordHash = hashPassword(defaultPassword, defaultSalt);

    // Admin user
    await connection.execute(
      `INSERT INTO users (id, email, name, role, is_active) VALUES (?, ?, ?, ?, ?)`,
      [adminId, '<EMAIL>', 'Admin User', 'super_admin', 1]
    );

    await connection.execute(
      `INSERT INTO users_auth (user_id, email, password_hash, salt) VALUES (?, ?, ?, ?)`,
      [adminId, '<EMAIL>', defaultPasswordHash, defaultSalt]
    );

    // Asset manager
    await connection.execute(
      `INSERT INTO users (id, email, name, role, is_active) VALUES (?, ?, ?, ?, ?)`,
      [assetManagerId, '<EMAIL>', 'Bekele Tadesse', 'national_asset_manager', 1]
    );

    await connection.execute(
      `INSERT INTO users_auth (user_id, email, password_hash, salt) VALUES (?, ?, ?, ?)`,
      [assetManagerId, '<EMAIL>', defaultPasswordHash, defaultSalt]
    );

    // Maintenance manager
    await connection.execute(
      `INSERT INTO users (id, email, name, role, is_active) VALUES (?, ?, ?, ?, ?)`,
      [maintenanceManagerId, '<EMAIL>', 'Tigist Haile', 'national_maintenance_manager', 1]
    );

    await connection.execute(
      `INSERT INTO users_auth (user_id, email, password_hash, salt) VALUES (?, ?, ?, ?)`,
      [maintenanceManagerId, '<EMAIL>', defaultPasswordHash, defaultSalt]
    );

    // Regional admin
    await connection.execute(
      `INSERT INTO users (id, email, name, role, is_active) VALUES (?, ?, ?, ?, ?)`,
      [regionalAdminId, '<EMAIL>', 'Abebe Kebede', 'regional_admin', 1]
    );

    await connection.execute(
      `INSERT INTO users_auth (user_id, email, password_hash, salt) VALUES (?, ?, ?, ?)`,
      [regionalAdminId, '<EMAIL>', defaultPasswordHash, defaultSalt]
    );

    // Technician
    await connection.execute(
      `INSERT INTO users (id, email, name, role, is_active) VALUES (?, ?, ?, ?, ?)`,
      [technicianId, '<EMAIL>', 'Dawit Mengistu', 'field_technician', 1]
    );

    await connection.execute(
      `INSERT INTO users_auth (user_id, email, password_hash, salt) VALUES (?, ?, ?, ?)`,
      [technicianId, '<EMAIL>', defaultPasswordHash, defaultSalt]
    );

    // Create regions
    const addisId = uuidv4();
    const oromiaId = uuidv4();
    const amharaId = uuidv4();
    const tigraId = uuidv4();

    await connection.execute(
      `INSERT INTO regions (id, name, code, coordinates, service_centers, transformers) VALUES (?, ?, ?, ?, ?, ?)`,
      [addisId, 'Addis Ababa', 'addis', JSON.stringify({ lat: 9.0222, lng: 38.7468 }), 0, 0]
    );

    await connection.execute(
      `INSERT INTO regions (id, name, code, coordinates, service_centers, transformers) VALUES (?, ?, ?, ?, ?, ?)`,
      [oromiaId, 'Oromia', 'oromia', JSON.stringify({ lat: 8.9806, lng: 39.3772 }), 0, 0]
    );

    await connection.execute(
      `INSERT INTO regions (id, name, code, coordinates, service_centers, transformers) VALUES (?, ?, ?, ?, ?, ?)`,
      [amharaId, 'Amhara', 'amhara', JSON.stringify({ lat: 11.5, lng: 37.3 }), 0, 0]
    );

    await connection.execute(
      `INSERT INTO regions (id, name, code, coordinates, service_centers, transformers) VALUES (?, ?, ?, ?, ?, ?)`,
      [tigraId, 'Tigray', 'tigray', JSON.stringify({ lat: 14.1, lng: 38.3 }), 0, 0]
    );

    // Create service centers
    const addisMainId = uuidv4();
    const addisEastId = uuidv4();
    const jimmaId = uuidv4();
    const nekmteId = uuidv4();

    await connection.execute(
      `INSERT INTO service_centers (id, name, region_id, address, contact_person, phone, email, latitude, longitude)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        addisMainId,
        'Addis Ababa Main',
        addisId,
        'Bole Road, Addis Ababa',
        'Yonas Alemu',
        '+************',
        '<EMAIL>',
        9.0222,
        38.7468
      ]
    );

    await connection.execute(
      `INSERT INTO service_centers (id, name, region_id, address, contact_person, phone, email, latitude, longitude)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        addisEastId,
        'Addis Ababa East',
        addisId,
        'Megenagna, Addis Ababa',
        'Sara Tesfaye',
        '+************',
        '<EMAIL>',
        9.0299,
        38.8079
      ]
    );

    await connection.execute(
      `INSERT INTO service_centers (id, name, region_id, address, contact_person, phone, email, latitude, longitude)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        jimmaId,
        'Jimma Service Center',
        oromiaId,
        'Jimma City Center',
        'Mohammed Ali',
        '+251933456789',
        '<EMAIL>',
        7.6781,
        36.8344
      ]
    );

    await connection.execute(
      `INSERT INTO service_centers (id, name, region_id, address, contact_person, phone, email, latitude, longitude)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        nekmteId,
        'Nekemte Service Center',
        oromiaId,
        'Nekemte Main Road',
        'Chaltu Gemechu',
        '+************',
        '<EMAIL>',
        9.0893,
        36.5613
      ]
    );

    // Update region service center counts
    await connection.execute(
      'UPDATE regions SET service_centers = 2 WHERE id = ?',
      [addisId]
    );

    await connection.execute(
      'UPDATE regions SET service_centers = 2 WHERE id = ?',
      [oromiaId]
    );

    // Create transformers
    const transformer1Id = uuidv4();
    const transformer2Id = uuidv4();
    const transformer3Id = uuidv4();
    const transformer4Id = uuidv4();

    await connection.execute(
      `INSERT INTO transformers (
        id, serial_number, name, status, type, manufacturer, model,
        manufacture_date, installation_date, capacity, primary_voltage, secondary_voltage,
        region_id, service_center_id, address, latitude, longitude,
        temperature, load_percentage, oil_level, health_index, tags
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        transformer1Id,
        'TRF-2023-001',
        'Bole Main Transformer',
        'operational',
        'Distribution',
        'Siemens',
        'SIT-500',
        '2022-05-15',
        '2023-01-10',
        500,
        33,
        0.4,
        addisId,
        addisMainId,
        'Bole Road, Near Friendship Building',
        9.0101,
        38.7612,
        65,
        78,
        90,
        85,
        JSON.stringify(['urban', 'commercial'])
      ]
    );

    await connection.execute(
      `INSERT INTO transformers (
        id, serial_number, name, status, type, manufacturer, model,
        manufacture_date, installation_date, capacity, primary_voltage, secondary_voltage,
        region_id, service_center_id, address, latitude, longitude,
        temperature, load_percentage, oil_level, health_index, tags
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        transformer2Id,
        'TRF-2023-002',
        'Megenagna Transformer',
        'warning',
        'Distribution',
        'ABB',
        'ABB-300',
        '2021-08-20',
        '2022-03-15',
        300,
        33,
        0.4,
        addisId,
        addisEastId,
        'Megenagna Roundabout',
        9.0299,
        38.8079,
        75,
        92,
        80,
        65,
        JSON.stringify(['urban', 'commercial', 'high-load'])
      ]
    );

    await connection.execute(
      `INSERT INTO transformers (
        id, serial_number, name, status, type, manufacturer, model,
        manufacture_date, installation_date, capacity, primary_voltage, secondary_voltage,
        region_id, service_center_id, address, latitude, longitude,
        temperature, load_percentage, oil_level, health_index, tags
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        transformer3Id,
        'TRF-2023-003',
        'Jimma Central Transformer',
        'operational',
        'Distribution',
        'Schneider Electric',
        'SE-200',
        '2022-11-10',
        '2023-02-20',
        200,
        33,
        0.4,
        oromiaId,
        jimmaId,
        'Jimma City Center, Main Square',
        7.6781,
        36.8344,
        60,
        65,
        95,
        90,
        JSON.stringify(['urban', 'residential'])
      ]
    );

    await connection.execute(
      `INSERT INTO transformers (
        id, serial_number, name, status, type, manufacturer, model,
        manufacture_date, installation_date, capacity, primary_voltage, secondary_voltage,
        region_id, service_center_id, address, latitude, longitude,
        temperature, load_percentage, oil_level, health_index, tags
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        transformer4Id,
        'TRF-2023-004',
        'Nekemte Industrial Transformer',
        'maintenance',
        'Distribution',
        'General Electric',
        'GE-400',
        '2021-06-05',
        '2022-01-15',
        400,
        33,
        0.4,
        oromiaId,
        nekmteId,
        'Nekemte Industrial Zone',
        9.0893,
        36.5613,
        50,
        30,
        85,
        75,
        JSON.stringify(['industrial', 'rural'])
      ]
    );

    // Update region transformer counts
    await connection.execute(
      'UPDATE regions SET transformers = 2 WHERE id = ?',
      [addisId]
    );

    await connection.execute(
      'UPDATE regions SET transformers = 2 WHERE id = ?',
      [oromiaId]
    );

    // Create maintenance records
    const maintenance1Id = uuidv4();
    const maintenance2Id = uuidv4();

    await connection.execute(
      `INSERT INTO maintenance_records (
        id, transformer_id, type, status, title, description,
        scheduled_date, assigned_to, reported_by, priority, estimated_duration
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        maintenance1Id,
        transformer2Id,
        'preventive',
        'scheduled',
        'Quarterly Maintenance - Megenagna Transformer',
        'Regular quarterly maintenance including oil check, temperature monitoring, and connection inspection.',
        '2025-06-15',
        technicianId,
        maintenanceManagerId,
        'medium',
        4
      ]
    );

    await connection.execute(
      `INSERT INTO maintenance_records (
        id, transformer_id, type, status, title, description,
        scheduled_date, completed_date, assigned_to, reported_by, priority,
        estimated_duration, actual_duration, cost
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        maintenance2Id,
        transformer4Id,
        'corrective',
        'in_progress',
        'Oil Leak Repair - Nekemte Industrial Transformer',
        'Repair oil leak detected during routine inspection. Replace gaskets and check for other potential issues.',
        '2025-05-25',
        null,
        technicianId,
        regionalAdminId,
        'high',
        8,
        null,
        null
      ]
    );

    // Create alerts
    const alert1Id = uuidv4();
    const alert2Id = uuidv4();

    await connection.execute(
      `INSERT INTO alerts (
        id, transformer_id, title, message, severity, type, is_read, is_resolved
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        alert1Id,
        transformer2Id,
        'High Temperature Alert',
        'Transformer temperature has exceeded 70°C. Immediate inspection recommended.',
        'high',
        'temperature',
        0,
        0
      ]
    );

    await connection.execute(
      `INSERT INTO alerts (
        id, transformer_id, title, message, severity, type, is_read, is_resolved
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        alert2Id,
        transformer2Id,
        'Overload Warning',
        'Transformer is operating at 92% of capacity, exceeding the recommended 85% threshold.',
        'medium',
        'load',
        1,
        0
      ]
    );

    console.log('Initial data seeded successfully');
  } catch (error) {
    console.error('Error seeding initial data:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('Initializing MySQL database...');

    // Create the database
    await createDatabase();

    // Initialize the schema
    await initializeSchema();

    // Seed initial data
    await seedInitialData();

    console.log('MySQL database initialization completed successfully');
  } catch (error) {
    console.error('Error initializing MySQL database:', error);
    process.exit(1);
  }
}

// Run the main function
main();
