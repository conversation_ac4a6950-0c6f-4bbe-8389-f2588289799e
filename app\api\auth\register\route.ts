import { NextRequest, NextResponse } from 'next/server';
import { registerUser } from '@/src/lib/auth/mysql-auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, name, role } = body;

    if (!email || !password || !name) {
      return NextResponse.json(
        { error: 'Email, password, and name are required' },
        { status: 400 }
      );
    }

    const user = await registerUser(email, password, name, role);

    if (!user) {
      return NextResponse.json(
        { error: 'Failed to register user' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      user,
      message: 'Registration successful'
    });
  } catch (error: any) {
    console.error('Registration error:', error);
    
    if (error.message === 'User already exists') {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'An error occurred during registration' },
      { status: 500 }
    );
  }
}
