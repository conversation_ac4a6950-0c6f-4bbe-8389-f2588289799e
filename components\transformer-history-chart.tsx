"use client"

import { useEffect, useState } from "react"
import { Card, CardContent } from "@/src/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/src/components/ui/tabs"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"

interface TransformerHistoryChartProps {
  transformerId: string
}

export function TransformerHistoryChart({ transformerId }: TransformerHistoryChartProps) {
  const [data, setData] = useState<any[]>([])
  const [timeRange, setTimeRange] = useState("30d")

  useEffect(() => {
    // In a real application, fetch the data from an API
    // For now, we'll generate mock data
    const generateMockData = () => {
      const now = new Date()
      const data = []

      let days = 30
      if (timeRange === "7d") days = 7
      if (timeRange === "90d") days = 90
      if (timeRange === "1y") days = 365

      for (let i = days; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(date.getDate() - i)

        // Generate realistic load pattern with daily peaks
        const hourOfDay = date.getHours()
        const baseLoad = 60 + Math.random() * 10
        const dailyPattern = Math.sin(((hourOfDay - 6) * Math.PI) / 12) * 20
        const weekendFactor = [0, 6].includes(date.getDay()) ? 0.8 : 1
        const randomFactor = Math.random() * 5

        let load = (baseLoad + dailyPattern) * weekendFactor + randomFactor
        load = Math.max(40, Math.min(95, load))

        // Temperature correlates somewhat with load
        const baseTemp = 45 + (load - 60) * 0.3
        const tempRandomFactor = Math.random() * 3
        const temperature = baseTemp + tempRandomFactor

        data.push({
          date: date.toISOString().split("T")[0],
          load: Math.round(load),
          temperature: Math.round(temperature),
          phaseA: Math.round(load * (0.9 + Math.random() * 0.2)),
          phaseB: Math.round(load * (0.9 + Math.random() * 0.2)),
          phaseC: Math.round(load * (0.9 + Math.random() * 0.2)),
        })
      }

      return data
    }

    setData(generateMockData())
  }, [transformerId, timeRange])

  const formatXAxis = (tickItem: string) => {
    const date = new Date(tickItem)
    return `${date.getDate()}/${date.getMonth() + 1}`
  }

  return (
    <Card className="border-none shadow-none">
      <CardContent className="p-0">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-medium">Performance History</h3>
          <Tabs value={timeRange} onValueChange={setTimeRange} className="w-auto">
            <TabsList className="grid w-[200px] grid-cols-4">
              <TabsTrigger value="7d">7D</TabsTrigger>
              <TabsTrigger value="30d">30D</TabsTrigger>
              <TabsTrigger value="90d">90D</TabsTrigger>
              <TabsTrigger value="1y">1Y</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <Tabs defaultValue="load" className="w-full">
          <TabsList>
            <TabsTrigger value="load">Load</TabsTrigger>
            <TabsTrigger value="temperature">Temperature</TabsTrigger>
            <TabsTrigger value="phases">Phase Balance</TabsTrigger>
          </TabsList>
          <TabsContent value="load">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={data}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={formatXAxis} tick={{ fontSize: 12 }} />
                  <YAxis domain={[0, 100]} tickFormatter={(value) => `${value}%`} tick={{ fontSize: 12 }} />
                  <Tooltip
                    formatter={(value: number) => [`${value}%`, "Load"]}
                    labelFormatter={(label) => `Date: ${label}`}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="load"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 6 }}
                    name="Load Percentage"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          <TabsContent value="temperature">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={data}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={formatXAxis} tick={{ fontSize: 12 }} />
                  <YAxis domain={[30, 80]} tickFormatter={(value) => `${value}°C`} tick={{ fontSize: 12 }} />
                  <Tooltip
                    formatter={(value: number) => [`${value}°C`, "Temperature"]}
                    labelFormatter={(label) => `Date: ${label}`}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="temperature"
                    stroke="#ef4444"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 6 }}
                    name="Oil Temperature"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          <TabsContent value="phases">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={data}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tickFormatter={formatXAxis} tick={{ fontSize: 12 }} />
                  <YAxis domain={[0, 100]} tickFormatter={(value) => `${value}%`} tick={{ fontSize: 12 }} />
                  <Tooltip
                    formatter={(value: number) => [`${value}%`, "Load"]}
                    labelFormatter={(label) => `Date: ${label}`}
                  />
                  <Legend />
                  <Line type="monotone" dataKey="phaseA" stroke="#3b82f6" strokeWidth={2} dot={false} name="Phase A" />
                  <Line type="monotone" dataKey="phaseB" stroke="#10b981" strokeWidth={2} dot={false} name="Phase B" />
                  <Line type="monotone" dataKey="phaseC" stroke="#f59e0b" strokeWidth={2} dot={false} name="Phase C" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
