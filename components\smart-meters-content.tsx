"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { But<PERSON> } from "@/src/components/ui/button"
import {
  Search, Filter, Plus, Download, Upload, BarChart3, Wifi, CheckCircle2,
  AlertCircle, Map, Bell, Settings, RefreshCw, MoreHorizontal
} from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { SmartMetersTable } from "@/components/smart-meters-table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { SmartMeterUsageChart } from "@/components/smart-meter-usage-chart"
import { SmartMeterAlerts } from "@/components/smart-meter-alerts"
import { SmartMeterMap } from "@/components/smart-meter-map"
import {
  Dialog, Dialog<PERSON>ontent, <PERSON><PERSON>D<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger
} from "@/src/components/ui/dialog"
import { Label } from "@/src/components/ui/label"
import { SmartMeter } from "@/src/types/smart-meter"
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger
} from "@/src/components/ui/dropdown-menu"

export function SmartMetersContent() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [regionFilter, setRegionFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [meters, setMeters] = useState<SmartMeter[]>([])
  const [statistics, setStatistics] = useState({
    total: 24568,
    connected: 22845,
    disconnected: 1723,
    avgDailyUsage: 12.4
  })
  const [loading, setLoading] = useState(true)
  const [importFile, setImportFile] = useState<File | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  // Fetch smart meters from API
  useEffect(() => {
    const fetchMeters = async () => {
      try {
        setLoading(true)

        // Build query parameters
        const params = new URLSearchParams()
        if (statusFilter !== "all") params.append("status", statusFilter)
        if (regionFilter !== "all") params.append("region", regionFilter)
        if (typeFilter !== "all") params.append("type", typeFilter)

        const response = await fetch(`/api/smart-meters?${params.toString()}`)

        if (response.ok) {
          const data = await response.json()
          setMeters(data)
        } else {
          console.error("Failed to fetch smart meters")
        }
      } catch (error) {
        console.error("Error fetching smart meters:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchMeters()
  }, [statusFilter, regionFilter, typeFilter])

  // Fetch statistics from API
  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        const response = await fetch('/api/smart-meters/statistics')

        if (response.ok) {
          const data = await response.json()
          setStatistics({
            total: data.total,
            connected: data.byStatus.Connected || 0,
            disconnected: data.byStatus.Disconnected || 0,
            avgDailyUsage: data.consumption.daily
          })
        } else {
          console.error("Failed to fetch statistics")
        }
      } catch (error) {
        console.error("Error fetching statistics:", error)
      }
    }

    fetchStatistics()
  }, [])

  // Handle import file change
  const handleImportFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImportFile(e.target.files[0])
    }
  }

  // Handle import submit
  const handleImportSubmit = async () => {
    if (!importFile) return

    // In a real app, you would upload the file to the server
    // For now, we'll just simulate a successful import
    setRefreshing(true)

    // Simulate API call delay
    setTimeout(() => {
      setRefreshing(false)
      // Reset the file input
      setImportFile(null)
      // Show success message (in a real app)
      alert("Import successful!")
    }, 1500)
  }

  // Handle export
  const handleExport = () => {
    // In a real app, you would generate a CSV or Excel file
    // For now, we'll just simulate a download

    // Create CSV content
    const headers = ["ID", "Serial Number", "Location", "Customer", "Type", "Status", "Last Reading"]
    const csvRows = [headers.join(",")]

    meters.forEach(meter => {
      const row = [
        meter.id,
        meter.serialNumber,
        meter.location.address,
        meter.customer.name,
        meter.type,
        meter.status,
        `${meter.lastReadingValue} kWh`
      ]
      csvRows.push(row.join(","))
    })

    const csvContent = csvRows.join("\n")

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", "smart-meters.csv")
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true)

    // Refetch data
    try {
      const response = await fetch('/api/smart-meters')

      if (response.ok) {
        const data = await response.json()
        setMeters(data)
      }

      const statsResponse = await fetch('/api/smart-meters/statistics')

      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStatistics({
          total: statsData.total,
          connected: statsData.byStatus.Connected || 0,
          disconnected: statsData.byStatus.Disconnected || 0,
          avgDailyUsage: statsData.consumption.daily
        })
      }
    } catch (error) {
      console.error("Error refreshing data:", error)
    } finally {
      setRefreshing(false)
    }
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <h1 className="text-2xl font-bold tracking-tight">Smart Meter Management</h1>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleRefresh}
            disabled={refreshing}
            className={refreshing ? "animate-spin" : ""}
          >
            <RefreshCw className="h-4 w-4" />
            <span className="sr-only">Refresh</span>
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Upload className="mr-2 h-4 w-4" />
                Import
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Import Smart Meters</DialogTitle>
                <DialogDescription>
                  Upload a CSV file containing smart meter data.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="file">File</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".csv,.xlsx"
                    onChange={handleImportFileChange}
                  />
                  <p className="text-sm text-muted-foreground">
                    Supported formats: CSV, Excel
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button
                  onClick={handleImportSubmit}
                  disabled={!importFile || refreshing}
                  className={refreshing ? "animate-spin" : ""}
                >
                  {refreshing ? "Importing..." : "Import"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>

          <Dialog>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Meter
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Add New Smart Meter</DialogTitle>
                <DialogDescription>
                  Enter the details for the new smart meter.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="serialNumber" className="text-right">
                    Serial Number
                  </Label>
                  <Input
                    id="serialNumber"
                    placeholder="ETH-SM-XXXXX"
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="manufacturer" className="text-right">
                    Manufacturer
                  </Label>
                  <Input
                    id="manufacturer"
                    placeholder="Manufacturer"
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="model" className="text-right">
                    Model
                  </Label>
                  <Input
                    id="model"
                    placeholder="Model"
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="type" className="text-right">
                    Type
                  </Label>
                  <Select>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Residential">Residential</SelectItem>
                      <SelectItem value="Commercial">Commercial</SelectItem>
                      <SelectItem value="Industrial">Industrial</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="location" className="text-right">
                    Location
                  </Label>
                  <Input
                    id="location"
                    placeholder="Address"
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="customer" className="text-right">
                    Customer
                  </Label>
                  <Input
                    id="customer"
                    placeholder="Customer name"
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">Add Meter</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">More options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                <span>Export as PDF</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Bell className="mr-2 h-4 w-4" />
                <span>Notification Settings</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Smart Meters</CardTitle>
            <Wifi className="h-4 w-4 text-teal-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.total.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">+1,245 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connected</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.connected.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((statistics.connected / statistics.total) * 100)}% of total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Disconnected</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.disconnected.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((statistics.disconnected / statistics.total) * 100)}% of total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Daily Usage</CardTitle>
            <BarChart3 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.avgDailyUsage.toFixed(1)} kWh</div>
            <p className="text-xs text-muted-foreground">+0.8 from last month</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="meters" className="space-y-4">
        <TabsList>
          <TabsTrigger value="meters">Meters</TabsTrigger>
          <TabsTrigger value="map">Map View</TabsTrigger>
          <TabsTrigger value="usage">Usage Analytics</TabsTrigger>
          <TabsTrigger value="alerts">Meter Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="meters" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Smart Meters</CardTitle>
              <CardDescription>Manage and monitor smart meters across Ethiopia</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search meters..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full sm:w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="Connected">Connected</SelectItem>
                      <SelectItem value="Disconnected">Disconnected</SelectItem>
                      <SelectItem value="Maintenance">Maintenance</SelectItem>
                      <SelectItem value="Tampered">Tampered</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={regionFilter} onValueChange={setRegionFilter}>
                    <SelectTrigger className="w-full sm:w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Region" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Regions</SelectItem>
                      <SelectItem value="Addis Ababa">Addis Ababa</SelectItem>
                      <SelectItem value="Dire Dawa">Dire Dawa</SelectItem>
                      <SelectItem value="Amhara">Amhara</SelectItem>
                      <SelectItem value="Oromia">Oromia</SelectItem>
                      <SelectItem value="SNNPR">SNNPR</SelectItem>
                      <SelectItem value="Tigray">Tigray</SelectItem>
                      <SelectItem value="Somali">Somali</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-full sm:w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="Residential">Residential</SelectItem>
                      <SelectItem value="Commercial">Commercial</SelectItem>
                      <SelectItem value="Industrial">Industrial</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <SmartMetersTable searchQuery={searchQuery} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="map" className="space-y-4">
          <SmartMeterMap meters={meters} />
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage Analytics</CardTitle>
              <CardDescription>Smart meter usage patterns and analytics</CardDescription>
            </CardHeader>
            <CardContent>
              <SmartMeterUsageChart />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <SmartMeterAlerts />
        </TabsContent>
      </Tabs>
    </div>
  )
}
