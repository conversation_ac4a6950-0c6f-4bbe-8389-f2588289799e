/**
 * Core Tables Migration for dtms_eeu_db
 *
 * This script adds the core application tables to the existing dtms_eeu_db database
 * and migrates data from the JSON database.
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db'
};

let connection;

/**
 * Initialize MySQL connection
 */
async function initConnection() {
  try {
    connection = await mysql.createConnection(config);
    console.log('✅ MySQL connection established');
  } catch (error) {
    console.error('❌ Failed to connect to MySQL:', error.message);
    throw error;
  }
}

/**
 * Execute MySQL query
 */
async function executeQuery(query, params = []) {
  try {
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('❌ Query error:', error.message);
    console.error('Query:', query.substring(0, 100) + '...');
    throw error;
  }
}

/**
 * Create core application tables
 */
async function createCoreTables() {
  console.log('🔄 Creating core application tables...');

  try {
    // Create users table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        salt VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        role VARCHAR(50) NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        last_login DATETIME NULL,
        region_id VARCHAR(36) NULL,
        service_center_id VARCHAR(36) NULL,
        INDEX idx_users_email (email),
        INDEX idx_users_role (role)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Users table created');

    // Create regions table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS regions (
        id VARCHAR(36) PRIMARY KEY,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(10) UNIQUE NOT NULL,
        description TEXT,
        coordinates_lat DECIMAL(10, 8) NOT NULL,
        coordinates_lng DECIMAL(11, 8) NOT NULL,
        population INT DEFAULT 0,
        area_km2 DECIMAL(10, 2) DEFAULT 0,
        INDEX idx_regions_code (code),
        INDEX idx_regions_name (name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Regions table created');

    // Create service_centers table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS service_centers (
        id VARCHAR(36) PRIMARY KEY,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(20) UNIQUE NOT NULL,
        region_id VARCHAR(36) NOT NULL,
        address TEXT,
        coordinates_lat DECIMAL(10, 8) NOT NULL,
        coordinates_lng DECIMAL(11, 8) NOT NULL,
        contact_phone VARCHAR(20),
        contact_email VARCHAR(255),
        manager_name VARCHAR(100),
        capacity INT DEFAULT 0,
        INDEX idx_service_centers_region (region_id),
        INDEX idx_service_centers_code (code)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Service centers table created');

    // Create transformers table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS transformers (
        id VARCHAR(36) PRIMARY KEY,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        serial_number VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(100) NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'operational',
        type VARCHAR(50) NOT NULL,
        manufacturer VARCHAR(100) NOT NULL,
        model VARCHAR(100) NOT NULL,
        manufacture_date DATE,
        installation_date DATE,
        last_maintenance_date DATE,
        next_maintenance_date DATE,
        capacity INT NOT NULL,
        voltage_primary DECIMAL(8, 2) NOT NULL,
        voltage_secondary DECIMAL(8, 2) NOT NULL,
        region_id VARCHAR(36) NOT NULL,
        service_center_id VARCHAR(36) NOT NULL,
        location_address TEXT,
        location_lat DECIMAL(10, 8) NOT NULL,
        location_lng DECIMAL(11, 8) NOT NULL,
        temperature DECIMAL(5, 2) DEFAULT 0,
        load_percentage DECIMAL(5, 2) DEFAULT 0,
        oil_level DECIMAL(5, 2) DEFAULT 0,
        health_index DECIMAL(5, 2) DEFAULT 0,
        tags JSON,
        INDEX idx_transformers_serial (serial_number),
        INDEX idx_transformers_status (status),
        INDEX idx_transformers_region (region_id),
        INDEX idx_transformers_service_center (service_center_id),
        INDEX idx_transformers_type (type),
        INDEX idx_transformers_manufacturer (manufacturer)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Transformers table created');

    // Create maintenance_records table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS maintenance_records (
        id VARCHAR(36) PRIMARY KEY,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        transformer_id VARCHAR(36) NOT NULL,
        type VARCHAR(50) NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
        priority VARCHAR(20) NOT NULL DEFAULT 'medium',
        scheduled_date DATE NOT NULL,
        completed_date DATE NULL,
        technician_id VARCHAR(36) NULL,
        description TEXT,
        work_performed TEXT,
        parts_used JSON,
        cost DECIMAL(10, 2) DEFAULT 0,
        notes TEXT,
        INDEX idx_maintenance_transformer (transformer_id),
        INDEX idx_maintenance_status (status),
        INDEX idx_maintenance_type (type),
        INDEX idx_maintenance_scheduled_date (scheduled_date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Maintenance records table created');

    // Create alerts table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS alerts (
        id VARCHAR(36) PRIMARY KEY,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        transformer_id VARCHAR(36) NULL,
        type VARCHAR(50) NOT NULL,
        severity VARCHAR(20) NOT NULL,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        is_resolved BOOLEAN DEFAULT FALSE,
        resolved_at DATETIME NULL,
        resolved_by VARCHAR(36) NULL,
        resolution_notes TEXT,
        INDEX idx_alerts_transformer (transformer_id),
        INDEX idx_alerts_severity (severity),
        INDEX idx_alerts_type (type),
        INDEX idx_alerts_resolved (is_resolved),
        INDEX idx_alerts_created (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Alerts table created');

    // Create outages table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS outages (
        id VARCHAR(36) PRIMARY KEY,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        transformer_id VARCHAR(36) NULL,
        region_id VARCHAR(36) NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        status VARCHAR(20) NOT NULL DEFAULT 'active',
        severity VARCHAR(20) NOT NULL,
        start_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        end_time DATETIME NULL,
        affected_customers INT DEFAULT 0,
        cause VARCHAR(200),
        resolution TEXT,
        INDEX idx_outages_transformer (transformer_id),
        INDEX idx_outages_region (region_id),
        INDEX idx_outages_status (status),
        INDEX idx_outages_severity (severity),
        INDEX idx_outages_start_time (start_time)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Outages table created');

    // Create weather_alerts table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS weather_alerts_new (
        id VARCHAR(36) PRIMARY KEY,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        region_id VARCHAR(36) NULL,
        type VARCHAR(50) NOT NULL,
        severity VARCHAR(20) NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        start_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        end_time DATETIME NULL,
        affected_areas JSON,
        INDEX idx_weather_alerts_new_region (region_id),
        INDEX idx_weather_alerts_new_type (type),
        INDEX idx_weather_alerts_new_severity (severity),
        INDEX idx_weather_alerts_new_active (is_active),
        INDEX idx_weather_alerts_new_start_time (start_time)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Weather alerts table created');

    console.log('✅ All core tables created successfully');

  } catch (error) {
    console.error('❌ Core table creation failed:', error.message);
    throw error;
  }
}

/**
 * Generate and insert sample data
 */
async function insertSampleData() {
  console.log('🔄 Inserting sample data...');

  try {
    // Generate sample users
    const users = [
      {
        id: uuidv4(),
        email: '<EMAIL>',
        passwordHash: 'hashed_password_admin',
        salt: 'salt_admin',
        firstName: 'System',
        lastName: 'Administrator',
        role: 'super_admin'
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        passwordHash: 'hashed_password_manager',
        salt: 'salt_manager',
        firstName: 'Asset',
        lastName: 'Manager',
        role: 'national_asset_manager'
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        passwordHash: 'hashed_password_tech',
        salt: 'salt_tech',
        firstName: 'Field',
        lastName: 'Technician',
        role: 'technician'
      }
    ];

    // Insert users
    for (const user of users) {
      await executeQuery(`
        INSERT IGNORE INTO users (
          id, email, password_hash, salt, first_name, last_name, role, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        user.id, user.email, user.passwordHash, user.salt,
        user.firstName, user.lastName, user.role, true
      ]);
    }
    console.log(`✅ Inserted ${users.length} users`);

    // Generate sample regions
    const regions = [
      {
        id: uuidv4(),
        name: 'Addis Ababa',
        code: 'AA',
        description: 'Capital city and administrative center',
        coordinates: { lat: 9.0320, lng: 38.7469 },
        population: 3500000,
        areaKm2: 527.0
      },
      {
        id: uuidv4(),
        name: 'Oromia',
        code: 'OR',
        description: 'Largest region by area and population',
        coordinates: { lat: 8.5000, lng: 39.5000 },
        population: 35000000,
        areaKm2: 353006.0
      },
      {
        id: uuidv4(),
        name: 'Amhara',
        code: 'AM',
        description: 'Northern highland region',
        coordinates: { lat: 11.5000, lng: 37.5000 },
        population: 21000000,
        areaKm2: 154708.0
      },
      {
        id: uuidv4(),
        name: 'SNNPR',
        code: 'SN',
        description: 'Southern Nations, Nationalities, and Peoples Region',
        coordinates: { lat: 6.5000, lng: 37.0000 },
        population: 15000000,
        areaKm2: 112343.0
      },
      {
        id: uuidv4(),
        name: 'Tigray',
        code: 'TI',
        description: 'Northern region',
        coordinates: { lat: 14.0000, lng: 38.5000 },
        population: 5000000,
        areaKm2: 50078.0
      }
    ];

    // Insert regions
    for (const region of regions) {
      await executeQuery(`
        INSERT IGNORE INTO regions (
          id, name, code, description, coordinates_lat, coordinates_lng, population, area_km2
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        region.id, region.name, region.code, region.description,
        region.coordinates.lat, region.coordinates.lng, region.population, region.areaKm2
      ]);
    }
    console.log(`✅ Inserted ${regions.length} regions`);

    // Generate sample service centers
    const serviceCenters = [];
    regions.forEach((region, index) => {
      serviceCenters.push({
        id: uuidv4(),
        name: `${region.name} Service Center`,
        code: `${region.code}-SC01`,
        regionId: region.id,
        address: `Main Service Center, ${region.name}, Ethiopia`,
        coordinates: {
          lat: region.coordinates.lat + (Math.random() - 0.5) * 0.1,
          lng: region.coordinates.lng + (Math.random() - 0.5) * 0.1
        },
        contactPhone: `+251-${10 + index}-123-456${index}`,
        contactEmail: `${region.code.toLowerCase()}.<EMAIL>`,
        managerName: `Manager ${index + 1}`,
        capacity: 50 + index * 20
      });
    });

    // Insert service centers
    for (const center of serviceCenters) {
      await executeQuery(`
        INSERT IGNORE INTO service_centers (
          id, name, code, region_id, address, coordinates_lat, coordinates_lng,
          contact_phone, contact_email, manager_name, capacity
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        center.id, center.name, center.code, center.regionId, center.address,
        center.coordinates.lat, center.coordinates.lng, center.contactPhone,
        center.contactEmail, center.managerName, center.capacity
      ]);
    }
    console.log(`✅ Inserted ${serviceCenters.length} service centers`);

    // Generate sample transformers
    const transformers = [];
    const statuses = ['operational', 'warning', 'critical', 'maintenance'];
    const types = ['Distribution', 'Power', 'Pad-mounted', 'Pole-mounted'];
    const manufacturers = ['ABB', 'Siemens', 'Schneider Electric', 'General Electric'];

    for (let i = 0; i < 25; i++) {
      const region = regions[i % regions.length];
      const serviceCenter = serviceCenters.find(sc => sc.regionId === region.id);

      transformers.push({
        id: uuidv4(),
        serialNumber: `EEU-${region.code}-${1000 + i}`,
        name: `${region.name} Transformer ${Math.floor(i / regions.length) + 1}`,
        status: statuses[i % statuses.length],
        type: types[i % types.length],
        manufacturer: manufacturers[i % manufacturers.length],
        model: `Model-${100 + i}`,
        manufactureDate: new Date(2018 + (i % 5), 0, 1).toISOString().split('T')[0],
        installationDate: new Date(2019 + (i % 4), 0, 1).toISOString().split('T')[0],
        lastMaintenanceDate: new Date(2024, i % 12, 1).toISOString().split('T')[0],
        nextMaintenanceDate: new Date(2024, (i % 12) + 6, 1).toISOString().split('T')[0],
        capacity: [100, 250, 500, 1000][i % 4],
        voltagePrimary: [11, 15, 33][i % 3],
        voltageSecondary: 400,
        regionId: region.id,
        serviceCenterId: serviceCenter.id,
        locationAddress: `${region.name}, Ethiopia`,
        locationLat: region.coordinates.lat + (Math.random() - 0.5) * 0.2,
        locationLng: region.coordinates.lng + (Math.random() - 0.5) * 0.2,
        temperature: 30 + Math.random() * 40,
        loadPercentage: 40 + Math.random() * 50,
        oilLevel: 80 + Math.random() * 20,
        healthIndex: 60 + Math.random() * 40,
        tags: JSON.stringify([])
      });
    }

    // Insert transformers
    for (const transformer of transformers) {
      await executeQuery(`
        INSERT IGNORE INTO transformers (
          id, serial_number, name, status, type, manufacturer, model,
          manufacture_date, installation_date, last_maintenance_date, next_maintenance_date,
          capacity, voltage_primary, voltage_secondary, region_id, service_center_id,
          location_address, location_lat, location_lng, temperature,
          load_percentage, oil_level, health_index, tags
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        transformer.id, transformer.serialNumber, transformer.name, transformer.status,
        transformer.type, transformer.manufacturer, transformer.model,
        transformer.manufactureDate, transformer.installationDate,
        transformer.lastMaintenanceDate, transformer.nextMaintenanceDate,
        transformer.capacity, transformer.voltagePrimary, transformer.voltageSecondary,
        transformer.regionId, transformer.serviceCenterId, transformer.locationAddress,
        transformer.locationLat, transformer.locationLng, transformer.temperature,
        transformer.loadPercentage, transformer.oilLevel, transformer.healthIndex, transformer.tags
      ]);
    }
    console.log(`✅ Inserted ${transformers.length} transformers`);

    // Generate sample alerts
    const alerts = [];
    const alertTypes = ['temperature', 'load', 'oil_level', 'maintenance'];
    const severities = ['low', 'medium', 'high', 'critical'];

    for (let i = 0; i < 15; i++) {
      const transformer = transformers[i % transformers.length];
      const alertType = alertTypes[i % alertTypes.length];
      const severity = severities[i % severities.length];

      alerts.push({
        id: uuidv4(),
        transformerId: transformer.id,
        type: alertType,
        severity: severity,
        title: `${alertType.charAt(0).toUpperCase() + alertType.slice(1)} Alert`,
        message: `${severity.charAt(0).toUpperCase() + severity.slice(1)} ${alertType} alert for transformer ${transformer.serialNumber}`,
        isResolved: i % 3 === 0,
        resolvedAt: i % 3 === 0 ? new Date().toISOString() : null,
        resolvedBy: i % 3 === 0 ? users[0].id : null,
        resolutionNotes: i % 3 === 0 ? 'Issue resolved by maintenance team' : null
      });
    }

    // Insert alerts
    for (const alert of alerts) {
      await executeQuery(`
        INSERT IGNORE INTO alerts (
          id, transformer_id, type, severity, title, message,
          is_resolved, resolved_at, resolved_by, resolution_notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        alert.id, alert.transformerId, alert.type, alert.severity,
        alert.title, alert.message, alert.isResolved, alert.resolvedAt,
        alert.resolvedBy, alert.resolutionNotes
      ]);
    }
    console.log(`✅ Inserted ${alerts.length} alerts`);

    console.log('✅ Sample data insertion completed');

  } catch (error) {
    console.error('❌ Sample data insertion failed:', error.message);
    throw error;
  }
}

/**
 * Main migration function
 */
async function runMigration() {
  console.log('🚀 Starting Core Tables Migration to dtms_eeu_db');
  console.log('===============================================');

  const startTime = Date.now();

  try {
    // Step 1: Initialize connection
    await initConnection();

    // Step 2: Create core tables
    await createCoreTables();

    // Step 3: Insert sample data
    await insertSampleData();

    // Step 4: Verify migration
    console.log('🔍 Verifying migration...');

    const counts = {
      users: (await executeQuery('SELECT COUNT(*) as count FROM users'))[0].count,
      regions: (await executeQuery('SELECT COUNT(*) as count FROM regions'))[0].count,
      service_centers: (await executeQuery('SELECT COUNT(*) as count FROM service_centers'))[0].count,
      transformers: (await executeQuery('SELECT COUNT(*) as count FROM transformers'))[0].count,
      alerts: (await executeQuery('SELECT COUNT(*) as count FROM alerts'))[0].count
    };

    console.log('📊 Migration Results:');
    console.log(`   Users: ${counts.users}`);
    console.log(`   Regions: ${counts.regions}`);
    console.log(`   Service Centers: ${counts.service_centers}`);
    console.log(`   Transformers: ${counts.transformers}`);
    console.log(`   Alerts: ${counts.alerts}`);

    // Show sample data
    console.log('\n📋 Sample transformer data:');
    const sampleTransformers = await executeQuery(`
      SELECT t.serial_number, t.name, t.status, r.name as region_name, t.capacity
      FROM transformers t
      JOIN regions r ON t.region_id = r.id
      LIMIT 5
    `);

    sampleTransformers.forEach(transformer => {
      console.log(`   ${transformer.serial_number}: ${transformer.name} (${transformer.status}) - ${transformer.region_name} - ${transformer.capacity}kVA`);
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`\n⏱️  Migration completed in ${duration.toFixed(2)} seconds`);
    console.log('🎉 Core tables migration successful!');
    console.log('');
    console.log('✅ Database dtms_eeu_db now contains:');
    console.log('   - Core application tables (users, regions, service_centers, transformers, etc.)');
    console.log('   - Sample data for development and testing');
    console.log('   - Existing specialized tables (inspection_records, sensor_readings, etc.)');
    console.log('');
    console.log('🔄 Next steps:');
    console.log('   1. Update your application to connect to MySQL instead of JSON database');
    console.log('   2. Create MySQL repository classes');
    console.log('   3. Test the application with MySQL data');
    console.log('   4. Configure production database settings');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run migration
runMigration().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
