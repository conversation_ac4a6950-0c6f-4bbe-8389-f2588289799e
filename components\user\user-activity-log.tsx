"use client"

import { useState } from "react"
import { 
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogT<PERSON>le,
  DialogFooter
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { <PERSON>roll<PERSON>rea } from "@/src/components/ui/scroll-area"
import { Badge } from "@/src/components/ui/badge"
import { 
  Activity, Clock, UserCheck, UserX, Shield, Key, 
  MapPin, AlertTriangle, RefreshCw, FileText
} from "lucide-react"
import { UserActivity } from "@/src/types/user-management"
import { format, parseISO } from "date-fns"

interface UserActivityLogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  activities: UserActivity[]
  userName: string
}

export function UserActivityLog({ 
  open, 
  onOpenChange, 
  activities,
  userName
}: UserActivityLogProps) {
  const [filter, setFilter] = useState<string>("all")
  
  // Get icon based on activity type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "login":
        return <UserCheck className="h-4 w-4 text-green-500" />
      case "logout":
        return <UserX className="h-4 w-4 text-slate-500" />
      case "create":
        return <FileText className="h-4 w-4 text-blue-500" />
      case "update":
        return <RefreshCw className="h-4 w-4 text-amber-500" />
      case "delete":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case "password_change":
        return <Key className="h-4 w-4 text-purple-500" />
      case "permission_change":
        return <Shield className="h-4 w-4 text-indigo-500" />
      case "assignment_change":
        return <MapPin className="h-4 w-4 text-teal-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }
  
  // Get badge color based on activity type
  const getActivityBadge = (type: string) => {
    switch (type) {
      case "login":
        return <Badge className="bg-green-500 hover:bg-green-600">Login</Badge>
      case "logout":
        return <Badge className="bg-slate-500 hover:bg-slate-600">Logout</Badge>
      case "create":
        return <Badge className="bg-blue-500 hover:bg-blue-600">Create</Badge>
      case "update":
        return <Badge className="bg-amber-500 hover:bg-amber-600">Update</Badge>
      case "delete":
        return <Badge className="bg-red-500 hover:bg-red-600">Delete</Badge>
      case "password_change":
        return <Badge className="bg-purple-500 hover:bg-purple-600">Password</Badge>
      case "permission_change":
        return <Badge className="bg-indigo-500 hover:bg-indigo-600">Permissions</Badge>
      case "assignment_change":
        return <Badge className="bg-teal-500 hover:bg-teal-600">Assignment</Badge>
      default:
        return <Badge>{type}</Badge>
    }
  }
  
  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), "MMM d, yyyy h:mm a")
    } catch (error) {
      return dateString
    }
  }
  
  // Filter activities
  const filteredActivities = filter === "all" 
    ? activities 
    : activities.filter(activity => activity.activityType === filter)
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            User Activity Log
          </DialogTitle>
          <DialogDescription>
            Activity history for {userName}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex gap-2 my-2 flex-wrap">
          <Button 
            variant={filter === "all" ? "default" : "outline"} 
            size="sm" 
            onClick={() => setFilter("all")}
          >
            All
          </Button>
          <Button 
            variant={filter === "login" ? "default" : "outline"} 
            size="sm" 
            onClick={() => setFilter("login")}
            className="gap-1"
          >
            <UserCheck className="h-3.5 w-3.5" />
            Login
          </Button>
          <Button 
            variant={filter === "update" ? "default" : "outline"} 
            size="sm" 
            onClick={() => setFilter("update")}
            className="gap-1"
          >
            <RefreshCw className="h-3.5 w-3.5" />
            Updates
          </Button>
          <Button 
            variant={filter === "permission_change" ? "default" : "outline"} 
            size="sm" 
            onClick={() => setFilter("permission_change")}
            className="gap-1"
          >
            <Shield className="h-3.5 w-3.5" />
            Permissions
          </Button>
          <Button 
            variant={filter === "assignment_change" ? "default" : "outline"} 
            size="sm" 
            onClick={() => setFilter("assignment_change")}
            className="gap-1"
          >
            <MapPin className="h-3.5 w-3.5" />
            Assignments
          </Button>
        </div>
        
        <ScrollArea className="h-[300px] rounded-md border p-4">
          {filteredActivities.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-4 text-muted-foreground">
              <Activity className="h-8 w-8 mb-2 opacity-50" />
              <p>No activity records found</p>
              <p className="text-sm">
                {filter !== "all" 
                  ? `No ${filter} activities have been recorded` 
                  : "User has no recorded activities"}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredActivities.map((activity) => (
                <div key={activity.id} className="flex gap-3 pb-4 border-b last:border-0">
                  <div className="mt-0.5">
                    {getActivityIcon(activity.activityType)}
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <div className="font-medium">
                        {getActivityBadge(activity.activityType)}
                      </div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock className="mr-1 h-3 w-3" />
                        {formatDate(activity.timestamp)}
                      </div>
                    </div>
                    <p className="text-sm">{activity.details}</p>
                    {activity.ipAddress && (
                      <p className="text-xs text-muted-foreground">
                        IP: {activity.ipAddress}
                        {activity.location && ` • Location: ${activity.location}`}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
