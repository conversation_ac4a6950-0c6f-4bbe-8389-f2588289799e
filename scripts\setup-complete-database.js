/**
 * Complete Database Setup for EEU DTMS
 * This master script creates all tables and seeds comprehensive data
 */

const { createComprehensiveDatabase } = require('./create-comprehensive-database');
const { seedComprehensiveData } = require('./seed-comprehensive-data');

async function setupCompleteDatabase() {
  console.log('🚀 EEU DTMS COMPLETE DATABASE SETUP');
  console.log('=' .repeat(60));
  console.log('🏢 Ethiopian Electric Utility');
  console.log('🔌 Digital Transformer Management System');
  console.log('📅 Setup Date:', new Date().toLocaleString());
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Create all database tables
    console.log('\n🔄 STEP 1: Creating Database Tables');
    console.log('-' .repeat(40));
    await createComprehensiveDatabase();
    console.log('✅ All database tables created successfully');
    
    // Step 2: Seed comprehensive data
    console.log('\n🔄 STEP 2: Seeding Comprehensive Data');
    console.log('-' .repeat(40));
    await seedComprehensiveData();
    console.log('✅ All data seeded successfully');
    
    // Step 3: Add additional data (performance metrics, weather, notifications)
    console.log('\n🔄 STEP 3: Adding Additional Data');
    console.log('-' .repeat(40));
    await seedAdditionalData();
    console.log('✅ Additional data added successfully');
    
    // Final summary
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 COMPLETE DATABASE SETUP SUCCESSFUL!');
    console.log('=' .repeat(60));
    
    console.log('\n📊 DATABASE SUMMARY:');
    console.log('  ✅ 11 Ethiopian Regions');
    console.log('  ✅ 8 Service Centers');
    console.log('  ✅ 10 Users (Various Roles)');
    console.log('  ✅ 8 Transformers (Different Types & Statuses)');
    console.log('  ✅ 6 Maintenance Schedules');
    console.log('  ✅ 6 Alerts (Various Severities)');
    console.log('  ✅ Performance Metrics');
    console.log('  ✅ Weather Data');
    console.log('  ✅ Notifications');
    console.log('  ✅ System Settings');
    
    console.log('\n🎯 DASHBOARD READY FOR:');
    console.log('  • Real-time transformer monitoring');
    console.log('  • Maintenance scheduling and tracking');
    console.log('  • Alert management and escalation');
    console.log('  • Performance analytics and reporting');
    console.log('  • Regional and service center management');
    console.log('  • User role-based access control');
    
    console.log('\n🌟 Your EEU DTMS is now fully operational!');
    console.log('🔗 Access at: http://localhost:3002');
    console.log('👤 Login: <EMAIL> / password123');
    
  } catch (error) {
    console.error('\n❌ DATABASE SETUP FAILED:', error);
    console.log('\n🔧 TROUBLESHOOTING STEPS:');
    console.log('  1. Check MySQL server is running');
    console.log('  2. Verify database credentials');
    console.log('  3. Ensure sufficient permissions');
    console.log('  4. Check for port conflicts');
    process.exit(1);
  }
}

async function seedAdditionalData() {
  const mysql = require('mysql2/promise');
  
  const config = {
    host: process.env.MYSQL_HOST || 'localhost',
    port: parseInt(process.env.MYSQL_PORT || '3306'),
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || '',
    database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
  };
  
  let connection;
  
  try {
    connection = await mysql.createConnection(config);
    
    // Seed Performance Metrics
    console.log('📈 Seeding Performance Metrics...');
    const performanceMetrics = [];
    const transformerIds = [1, 2, 3, 4, 5, 6, 7, 8];
    const metricTypes = ['efficiency', 'load_factor', 'temperature', 'voltage', 'current'];
    
    // Generate metrics for the last 7 days
    for (let day = 7; day >= 0; day--) {
      const date = new Date();
      date.setDate(date.getDate() - day);
      
      for (const transformerId of transformerIds) {
        for (const metricType of metricTypes) {
          let value, unit;
          switch (metricType) {
            case 'efficiency':
              value = 94 + Math.random() * 6; // 94-100%
              unit = '%';
              break;
            case 'load_factor':
              value = 60 + Math.random() * 35; // 60-95%
              unit = '%';
              break;
            case 'temperature':
              value = 55 + Math.random() * 25; // 55-80°C
              unit = '°C';
              break;
            case 'voltage':
              value = 32.5 + Math.random() * 1; // 32.5-33.5kV
              unit = 'kV';
              break;
            case 'current':
              value = 50 + Math.random() * 100; // 50-150A
              unit = 'A';
              break;
          }
          
          await connection.execute(`
            INSERT INTO app_performance_metrics (transformer_id, metric_type, value, unit, recorded_at)
            VALUES (?, ?, ?, ?, ?)
          `, [transformerId, metricType, value.toFixed(2), unit, date.toISOString()]);
        }
      }
    }
    console.log('✅ Performance metrics seeded');
    
    // Seed Weather Data
    console.log('🌤️  Seeding Weather Data...');
    const regions = [1, 2, 3, 4, 5]; // First 5 regions
    const weatherConditions = ['sunny', 'cloudy', 'rainy', 'stormy', 'clear'];
    
    for (let day = 7; day >= 0; day--) {
      const date = new Date();
      date.setDate(date.getDate() - day);
      
      for (const regionId of regions) {
        const temperature = 20 + Math.random() * 15; // 20-35°C
        const humidity = 40 + Math.random() * 40; // 40-80%
        const windSpeed = Math.random() * 20; // 0-20 km/h
        const condition = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
        const riskLevel = temperature > 30 ? 'medium' : 'low';
        
        await connection.execute(`
          INSERT INTO app_weather_data (region_id, temperature, humidity, wind_speed, weather_condition, risk_level, recorded_at)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [regionId, temperature.toFixed(1), humidity.toFixed(1), windSpeed.toFixed(1), condition, riskLevel, date.toISOString()]);
      }
    }
    console.log('✅ Weather data seeded');
    
    // Seed Notifications
    console.log('🔔 Seeding Notifications...');
    const notifications = [
      { user_id: 1, title: 'System Maintenance Complete', message: 'Database optimization completed successfully', type: 'success', category: 'system' },
      { user_id: 5, title: 'New Alert: High Temperature', message: 'Transformer EEU-AA-002 temperature alert requires attention', type: 'warning', category: 'alert', action_url: '/alerts/2' },
      { user_id: 8, title: 'Maintenance Task Assigned', message: 'You have been assigned to maintenance task for Bole Main Transformer', type: 'info', category: 'maintenance', action_url: '/maintenance/1' },
      { user_id: 6, title: 'Regional Report Available', message: 'Monthly performance report for Oromia region is ready', type: 'info', category: 'report', action_url: '/reports/monthly' },
      { user_id: 10, title: 'Critical Alert Resolved', message: 'Critical temperature alert for Mekelle transformer has been resolved', type: 'success', category: 'alert' }
    ];
    
    for (const notification of notifications) {
      await connection.execute(`
        INSERT INTO app_notifications (user_id, title, message, type, category, action_url, is_read)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [notification.user_id, notification.title, notification.message, notification.type, notification.category, notification.action_url || null, false]);
    }
    console.log('✅ Notifications seeded');
    
    // Seed System Settings
    console.log('⚙️  Seeding System Settings...');
    const systemSettings = [
      { setting_key: 'temperature_threshold_warning', setting_value: '70', data_type: 'number', category: 'alerts', description: 'Temperature threshold for warning alerts (°C)' },
      { setting_key: 'temperature_threshold_critical', setting_value: '85', data_type: 'number', category: 'alerts', description: 'Temperature threshold for critical alerts (°C)' },
      { setting_key: 'load_factor_threshold', setting_value: '85', data_type: 'number', category: 'alerts', description: 'Load factor threshold for alerts (%)' },
      { setting_key: 'maintenance_reminder_days', setting_value: '7', data_type: 'number', category: 'maintenance', description: 'Days before maintenance to send reminder' },
      { setting_key: 'system_timezone', setting_value: 'Africa/Addis_Ababa', data_type: 'string', category: 'system', description: 'System timezone' },
      { setting_key: 'dashboard_refresh_interval', setting_value: '30', data_type: 'number', category: 'dashboard', description: 'Dashboard auto-refresh interval (seconds)' },
      { setting_key: 'email_notifications_enabled', setting_value: 'true', data_type: 'boolean', category: 'notifications', description: 'Enable email notifications' },
      { setting_key: 'sms_notifications_enabled', setting_value: 'false', data_type: 'boolean', category: 'notifications', description: 'Enable SMS notifications' }
    ];
    
    for (const setting of systemSettings) {
      await connection.execute(`
        INSERT INTO app_system_settings (setting_key, setting_value, data_type, category, description, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [setting.setting_key, setting.setting_value, setting.data_type, setting.category, setting.description, 1]);
    }
    console.log('✅ System settings seeded');
    
  } catch (error) {
    console.error('❌ Error seeding additional data:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Export functions
module.exports = {
  setupCompleteDatabase,
  seedAdditionalData
};

// Run if called directly
if (require.main === module) {
  setupCompleteDatabase();
}
