// Application Configuration

export const APP_CONFIG = {
  // Application Info
  name: 'EEU Transformer Management System',
  version: '2.0.0',
  description: 'Ethiopian Electric Utility Transformer Management and Monitoring System',
  
  // Company Info
  company: {
    name: 'Ethiopian Electric Utility',
    shortName: 'EEU',
    website: 'https://eeu.gov.et',
    email: '<EMAIL>',
    phone: '+251-11-123-4567'
  },

  // API Configuration
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002/api',
    timeout: 30000,
    retries: 3
  },

  // Database Configuration
  database: {
    mysql: {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      database: process.env.DB_NAME || 'eeu_transformers',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || ''
    }
  },

  // Authentication
  auth: {
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
    refreshTokenExpiry: 7 * 24 * 60 * 60 * 1000, // 7 days
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000 // 15 minutes
  },

  // Dashboard Configuration
  dashboard: {
    refreshInterval: 30000, // 30 seconds
    maxWidgets: 12,
    defaultLayout: {
      autoRefresh: true,
      theme: 'light' as const,
      refreshInterval: 30000
    }
  },

  // Transformer Configuration
  transformers: {
    statusTypes: ['operational', 'maintenance', 'warning', 'critical', 'burnt'],
    priorityLevels: ['low', 'medium', 'high', 'critical'],
    maintenanceTypes: ['routine', 'preventive', 'corrective', 'emergency'],
    regions: [
      'Addis Ababa',
      'Oromia',
      'Amhara',
      'SNNPR',
      'Tigray',
      'Somali',
      'Afar',
      'Benishangul-Gumuz',
      'Gambela',
      'Harari',
      'Dire Dawa'
    ]
  },

  // Alerts Configuration
  alerts: {
    types: ['critical', 'warning', 'info', 'success'],
    categories: ['system', 'maintenance', 'performance', 'weather', 'security'],
    maxAlerts: 1000,
    retentionDays: 90
  },

  // File Upload Configuration
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/csv'],
    uploadPath: '/uploads'
  },

  // Pagination
  pagination: {
    defaultPageSize: 20,
    maxPageSize: 100,
    pageSizeOptions: [10, 20, 50, 100]
  },

  // Maps Configuration
  maps: {
    defaultCenter: {
      lat: 9.1450,
      lng: 40.4897
    }, // Ethiopia center
    defaultZoom: 6,
    maxZoom: 18,
    minZoom: 5
  },

  // Weather Configuration
  weather: {
    apiKey: process.env.WEATHER_API_KEY || '',
    updateInterval: 60 * 60 * 1000, // 1 hour
    riskThresholds: {
      temperature: { min: -10, max: 45 },
      humidity: { max: 85 },
      windSpeed: { max: 50 }
    }
  },

  // Notification Configuration
  notifications: {
    email: {
      enabled: true,
      provider: 'smtp',
      from: '<EMAIL>'
    },
    sms: {
      enabled: false,
      provider: 'twilio'
    },
    push: {
      enabled: true,
      vapidKey: process.env.VAPID_PUBLIC_KEY || ''
    }
  },

  // Security Configuration
  security: {
    encryption: {
      algorithm: 'aes-256-gcm',
      keyLength: 32
    },
    cors: {
      origin: process.env.CORS_ORIGIN || 'http://localhost:3002',
      credentials: true
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100 // requests per window
    }
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: 'json',
    maxFiles: 5,
    maxSize: '20m'
  },

  // Feature Flags
  features: {
    predictiveAnalytics: true,
    weatherIntegration: true,
    mobileApp: true,
    advancedReporting: true,
    realTimeMonitoring: true,
    gisIntegration: true,
    aiInsights: true
  },

  // UI Configuration
  ui: {
    theme: {
      primary: '#2563eb',
      secondary: '#64748b',
      success: '#059669',
      warning: '#d97706',
      error: '#dc2626',
      info: '#0284c7'
    },
    animations: {
      enabled: true,
      duration: 200
    },
    sidebar: {
      defaultCollapsed: false,
      width: 280,
      collapsedWidth: 80
    }
  },

  // Performance Configuration
  performance: {
    caching: {
      enabled: true,
      ttl: 5 * 60 * 1000 // 5 minutes
    },
    lazyLoading: true,
    imageOptimization: true,
    bundleAnalysis: process.env.ANALYZE === 'true'
  }
} as const

// Environment-specific overrides
export const getConfig = () => {
  const env = process.env.NODE_ENV || 'development'
  
  const envConfigs = {
    development: {
      api: {
        baseUrl: 'http://localhost:3002/api'
      },
      logging: {
        level: 'debug'
      }
    },
    production: {
      api: {
        baseUrl: 'https://api.eeu.gov.et'
      },
      logging: {
        level: 'error'
      },
      performance: {
        caching: {
          ttl: 15 * 60 * 1000 // 15 minutes in production
        }
      }
    },
    test: {
      api: {
        baseUrl: 'http://localhost:3003/api'
      },
      database: {
        mysql: {
          database: 'eeu_transformers_test'
        }
      }
    }
  }

  return {
    ...APP_CONFIG,
    ...envConfigs[env as keyof typeof envConfigs]
  }
}

export default getConfig()
