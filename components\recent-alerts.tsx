"use client"

import { AlertTriangle, Battery, Thermometer, Zap, AlertCircle, Eye, CheckCircle, UserCheck, ExternalLink } from "lucide-react"
import { cn } from "@/src/lib/utils"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { useAlerts } from "@/src/contexts/alert-context"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { AlertDetailDialog } from "./alert-detail-dialog"

interface RecentAlertsProps {
  extended?: boolean
}

export function RecentAlerts({ extended = false }: RecentAlertsProps) {
  const router = useRouter()
  const { filteredAlerts, acknowledgeAlert, resolveAlert } = useAlerts()
  const [selectedAlertId, setSelectedAlertId] = useState<string | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)

  // Mock current user ID
  const currentUserId = "Current User"

  // Get the most recent alerts
  const recentAlerts = [...filteredAlerts]
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, extended ? 5 : 3)

  // Navigate to transformer details page
  const navigateToTransformer = (transformerId: string) => {
    // Use the transformer ID directly (e.g., "tr-001")
    router.push(`/transformers/${transformerId}`)
  }

  // Get icon based on alert type
  const getAlertIcon = (type: string) => {
    switch (type) {
      case "Temperature": return Thermometer
      case "Oil Level": return Battery
      case "Load": return Zap
      case "Connection": return AlertCircle
      default: return AlertTriangle
    }
  }

  // Get color based on severity
  const getAlertColor = (severity: string) => {
    switch (severity) {
      case "Critical": return "text-red-500"
      case "High": return "text-orange-500"
      case "Medium": return "text-yellow-500"
      case "Low": return "text-blue-500"
      default: return "text-slate-500"
    }
  }

  const handleViewDetails = (alertId: string) => {
    setSelectedAlertId(alertId)
    setIsDetailDialogOpen(true)
  }

  const handleAcknowledge = (alertId: string) => {
    acknowledgeAlert(alertId, currentUserId)
  }

  const handleResolve = (alertId: string) => {
    resolveAlert(alertId, currentUserId, "Resolved via quick action")
  }

  return (
    <>
      <div className="space-y-4">
        {recentAlerts.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No alerts found</p>
          </div>
        ) : (
          recentAlerts.map((alert) => {
            const AlertIcon = getAlertIcon(alert.type)
            const alertColor = getAlertColor(alert.severity)

            return (
              <div
                key={alert.id}
                className="flex items-start space-x-4 group relative p-2 rounded-md hover:bg-muted cursor-pointer"
                onClick={() => navigateToTransformer(alert.transformer)}
              >
                <div
                  className={cn(
                    "mt-0.5 rounded-full p-1",
                    alert.severity === "Critical"
                      ? "bg-red-100 dark:bg-red-900/20"
                      : alert.severity === "High"
                        ? "bg-orange-100 dark:bg-orange-900/20"
                        : alert.severity === "Medium"
                          ? "bg-yellow-100 dark:bg-yellow-900/20"
                          : "bg-blue-100 dark:bg-blue-900/20",
                  )}
                >
                  <AlertIcon className={cn("h-4 w-4", alertColor)} />
                </div>
                <div className="space-y-1 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium leading-none">
                      {alert.transformer} - {alert.type} Alert
                      <Badge
                        className={cn(
                          "ml-2",
                          alert.severity === "Critical"
                            ? "bg-red-500 hover:bg-red-600"
                            : alert.severity === "High"
                              ? "bg-orange-500 hover:bg-orange-600"
                              : alert.severity === "Medium"
                                ? "bg-yellow-500 hover:bg-yellow-600"
                                : "bg-blue-500 hover:bg-blue-600",
                        )}
                      >
                        {alert.severity}
                      </Badge>
                      <Badge
                        variant="outline"
                        className={cn(
                          "ml-2",
                          alert.status === "Active" ? "border-red-500 text-red-500" :
                          alert.status === "Acknowledged" ? "border-blue-500 text-blue-500" :
                          alert.status === "In Progress" ? "border-yellow-500 text-yellow-500" :
                          "border-green-500 text-green-500"
                        )}
                      >
                        {alert.status}
                      </Badge>
                    </p>
                    <div className="hidden group-hover:flex items-center gap-1" onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewDetails(alert.id);
                        }}
                        title="View Alert Details"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigateToTransformer(alert.transformer);
                        }}
                        title="View Transformer"
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>

                      {alert.status === "Active" && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAcknowledge(alert.id);
                          }}
                          title="Acknowledge Alert"
                        >
                          <UserCheck className="h-3 w-3" />
                        </Button>
                      )}

                      {alert.status !== "Resolved" && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleResolve(alert.id);
                          }}
                          title="Resolve Alert"
                        >
                          <CheckCircle className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">{alert.message}</p>
                  <div className="flex items-center justify-between pt-1">
                    <p className="text-xs text-muted-foreground">
                      {alert.location} • {alert.timestamp}
                    </p>
                    {alert.assignedTo && (
                      <p className="text-xs text-muted-foreground">
                        Assigned to: {alert.assignedTo}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )
          })
        )}
      </div>

      {selectedAlertId && (
        <AlertDetailDialog
          open={isDetailDialogOpen}
          onOpenChange={setIsDetailDialogOpen}
          alertId={selectedAlertId}
        />
      )}
    </>
  )
}
