/**
 * Drop invalid MySQL view 'app_transformers' if it exists
 * This script will remove the broken view so the real table can be used.
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db'
};

async function dropAppTransformersView() {
  let connection;
  try {
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL');

    // Check if a view named 'app_transformers' exists
    const [views] = await connection.execute(
      `SELECT TABLE_NAME FROM information_schema.VIEWS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'app_transformers'`,
      [config.database]
    );
    if (views.length > 0) {
      console.log("⚠️  'app_transformers' is a VIEW. Dropping it...");
      await connection.execute('DROP VIEW IF EXISTS app_transformers');
      console.log("✅ Dropped view 'app_transformers'.");
    } else {
      console.log("ℹ️  No view named 'app_transformers' found.");
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    if (connection) await connection.end();
  }
}

dropAppTransformersView();
