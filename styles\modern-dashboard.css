/* Modern Dashboard Styles for EEU DTMS */

/* Custom CSS Variables for Modern Theme */
:root {
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  --gradient-info: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  --gradient-purple: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
  --gradient-emerald: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
  --gradient-orange: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  
  /* Glass morphism effects */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* Modern shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Animation durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
}

/* Modern Dashboard Background */
.modern-dashboard-bg {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.modern-dashboard-bg::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Glass Morphism Cards */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: 16px;
  transition: all var(--duration-normal) ease;
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-2xl);
}

/* Modern Metric Cards */
.metric-card {
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  background: white;
  box-shadow: var(--shadow-lg);
  transition: all var(--duration-normal) ease;
  border: 0;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 20px 20px 0 0;
}

.metric-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-2xl);
}

.metric-card .icon-container {
  background: var(--gradient-primary);
  border-radius: 16px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  transition: all var(--duration-normal) ease;
}

.metric-card:hover .icon-container {
  transform: scale(1.1) rotate(5deg);
}

/* Animated Number Counter */
.animated-number {
  font-variant-numeric: tabular-nums;
  transition: all var(--duration-normal) ease;
}

/* Modern Chart Containers */
.modern-chart-container {
  background: white;
  border-radius: 20px;
  box-shadow: var(--shadow-xl);
  border: 0;
  overflow: hidden;
  transition: all var(--duration-normal) ease;
}

.modern-chart-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-2xl);
}

.modern-chart-container .chart-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

/* Status Indicators */
.status-excellent {
  background: var(--gradient-success);
  color: white;
}

.status-good {
  background: var(--gradient-info);
  color: white;
}

.status-warning {
  background: var(--gradient-warning);
  color: white;
}

.status-critical {
  background: var(--gradient-danger);
  color: white;
}

/* Pulse Animation for Live Data */
.pulse-indicator {
  position: relative;
}

.pulse-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: currentColor;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
  opacity: 0.3;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
}

/* Modern Button Styles */
.modern-button {
  background: var(--gradient-primary);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  transition: all var(--duration-normal) ease;
  box-shadow: var(--shadow-md);
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.modern-button:active {
  transform: translateY(0);
}

/* Filter Panel Modern Styling */
.modern-filter-panel {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  box-shadow: var(--shadow-xl);
}

/* Gradient Text */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Modern Tabs */
.modern-tabs {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.modern-tab-trigger {
  background: transparent;
  border: none;
  padding: 16px 24px;
  font-weight: 600;
  color: #64748b;
  transition: all var(--duration-normal) ease;
  position: relative;
}

.modern-tab-trigger[data-state="active"] {
  background: var(--gradient-primary);
  color: white;
}

.modern-tab-trigger[data-state="active"]::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
}

/* Loading Animations */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .metric-card {
    margin-bottom: 16px;
  }
  
  .modern-chart-container {
    margin-bottom: 20px;
  }
  
  .glass-card {
    margin: 8px;
    border-radius: 12px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --glass-bg: rgba(0, 0, 0, 0.25);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
  }
  
  .modern-dashboard-bg {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  }
  
  .metric-card {
    background: #1f2937;
    color: white;
  }
  
  .modern-chart-container {
    background: #1f2937;
    color: white;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .metric-card {
    border: 2px solid #000;
  }
  
  .modern-button {
    border: 2px solid #000;
  }
}
