const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  const dbConfig = {
    host: process.env.MYSQL_HOST || 'localhost',
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || '',
    port: parseInt(process.env.MYSQL_PORT || '3306'),
    multipleStatements: true
  };

  const databaseName = process.env.MYSQL_DATABASE || 'eeu_dtms';

  try {
    console.log('🔄 Connecting to MySQL server...');
    const connection = await mysql.createConnection(dbConfig);

    console.log('✅ Connected to MySQL server');

    // Create database if it doesn't exist
    console.log(`🔄 Creating database ${databaseName} if it doesn't exist...`);
    await connection.execute(`CREATE DATABASE IF NOT EXISTS ${databaseName}`);
    console.log(`✅ Database ${databaseName} ready`);

    // Switch to the database
    await connection.query(`USE ${databaseName}`);

    console.log('✅ Connected to database');

    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', 'create_transformer_history_cards_simple.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('🔄 Running migration...');

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.query(statement);
          console.log('✅ Executed statement successfully');
        } catch (error) {
          if (error.code === 'ER_TABLE_EXISTS_ERROR') {
            console.log('ℹ️ Table already exists, skipping...');
          } else {
            console.error('❌ Error executing statement:', error.message);
          }
        }
      }
    }

    console.log('✅ Migration completed successfully');

    // Test the tables
    console.log('🔄 Testing tables...');
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME LIKE 'transformer_%'
    `, [databaseName]);

    console.log('📋 Available transformer tables:');
    tables.forEach(table => {
      console.log(`  - ${table.TABLE_NAME}`);
    });

    // Check sample data
    const [historyCards] = await connection.execute('SELECT COUNT(*) as count FROM transformer_history_cards');
    console.log(`📊 History cards in database: ${historyCards[0].count}`);

    await connection.end();
    console.log('🎉 Migration and testing completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
