// Dashboard Types - Centralized type definitions

export interface Transformer {
  id: string
  name: string
  location: string
  status: 'operational' | 'warning' | 'maintenance' | 'critical' | 'offline'
  capacity: number
  load: number
  temperature: number
  lastMaintenance: Date
  region?: string
  voltage?: string
  manufacturer?: string
}

export interface Alert {
  id: string
  title: string
  description: string
  severity: 'critical' | 'warning' | 'info'
  status: 'active' | 'acknowledged' | 'resolved'
  created_at: Date
  transformer_id?: string
  location?: string
  priority?: 'high' | 'medium' | 'low'
  assigned_to?: string
}

export interface MaintenanceRecord {
  id: string
  type: string
  description: string
  scheduled_date: Date
  completed_date?: Date
  priority: 'high' | 'medium' | 'low'
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
  transformer_id?: string
  assigned_technician?: string
  estimated_duration?: number
  actual_duration?: number
  cost?: number
  notes?: string
}

export interface Activity {
  id: string
  type: 'maintenance' | 'alert' | 'status_change' | 'user_action'
  description: string
  timestamp: Date
  user: string
  transformer_id?: string
  details?: Record<string, any>
}

export interface TransformerStatistics {
  total: number
  byStatus: Record<string, number>
  byRegion: Record<string, number>
  healthyPercentage: number
  averageLoad?: number
  averageTemperature?: number
  totalCapacity?: number
}

export interface AlertStatistics {
  total: number
  critical: number
  warning: number
  info: number
  unresolved: number
  resolved?: number
  acknowledged?: number
}

export interface MaintenanceStatistics {
  scheduled: number
  completed: number
  overdue: number
  total: number
  inProgress?: number
  cancelled?: number
}

export interface DashboardStats {
  totalTransformers: number
  healthyPercentage: number
  activeAlerts: number
  scheduledMaintenance: number
  criticalIssues: number
  systemUptime: number
  operationalCount: number
  warningCount: number
  maintenanceCount: number
  criticalCount: number
  offlineCount: number
}

export interface DashboardData {
  transformerStatistics?: TransformerStatistics
  alertStatistics?: AlertStatistics
  maintenanceStatistics?: MaintenanceStatistics
  recentAlerts?: Alert[]
  upcomingMaintenance?: MaintenanceRecord[]
  recentActivities?: Activity[]
  filteredTransformers?: Transformer[]
  weatherData?: WeatherData
  performanceMetrics?: PerformanceMetrics
}

export interface WeatherData {
  temperature: number
  humidity: number
  windSpeed: number
  conditions: string
  alerts?: WeatherAlert[]
}

export interface WeatherAlert {
  id: string
  type: string
  severity: string
  description: string
  startTime: Date
  endTime?: Date
}

export interface PerformanceMetrics {
  systemLoad: number
  responseTime: number
  uptime: number
  errorRate: number
  throughput: number
}

export interface DashboardFilters {
  region: string
  timeRange: string
  status?: string[]
  priority?: string[]
  dateFrom?: Date
  dateTo?: Date
}

export interface DashboardAction {
  type: string
  payload?: any
  timestamp: Date
  user: string
  result?: 'success' | 'error'
  message?: string
}

// API Response Types
export interface DashboardApiResponse {
  success: boolean
  data?: DashboardData
  message?: string
  error?: string
  timestamp: Date
}

export interface ActionApiResponse {
  success: boolean
  data?: any
  message?: string
  error?: string
}

// Component Props Types
export interface DashboardStatsProps {
  stats: DashboardStats
  isLoading?: boolean
  className?: string
}

export interface QuickActionsProps {
  permissions: {
    canAddTransformer: boolean
    canScheduleMaintenance: boolean
    canGenerateReports: boolean
    canExportData: boolean
  }
  onAction: (action: string, payload?: any) => void
  onOpenDialog: (dialog: string) => void
  className?: string
}

export interface AlertsPanelProps {
  alerts: Alert[]
  onAction: (action: string, payload?: any) => void
  isLoading?: boolean
  expanded?: boolean
  className?: string
}

export interface MaintenancePanelProps {
  maintenance: MaintenanceRecord[]
  onAction: (action: string, payload?: any) => void
  isLoading?: boolean
  expanded?: boolean
  className?: string
}

export interface AnalyticsPanelProps {
  data: DashboardData
  isLoading?: boolean
  className?: string
}

export interface ReportsPanelProps {
  onAction: (action: string, payload?: any) => void
  permissions: {
    canGenerateReports: boolean
    canExportData: boolean
  }
  className?: string
}

// Hook Types
export interface UseDashboardReturn {
  data: DashboardData
  stats: DashboardStats
  isLoading: boolean
  lastUpdated: Date
  realTimeEnabled: boolean
  refreshing: boolean
  region: string
  timeRange: string
  setRegion: (region: string) => void
  setTimeRange: (timeRange: string) => void
  toggleRealTime: () => void
  refresh: () => Promise<void>
  executeAction: (action: string, payload?: any) => Promise<any>
  fetchData: () => Promise<void>
}

// Constants
export const DASHBOARD_REFRESH_INTERVALS = {
  REAL_TIME: 30000, // 30 seconds
  NORMAL: 300000,   // 5 minutes
  SLOW: 600000      // 10 minutes
} as const

export const TRANSFORMER_STATUSES = [
  'operational',
  'warning', 
  'maintenance',
  'critical',
  'offline'
] as const

export const ALERT_SEVERITIES = [
  'critical',
  'warning',
  'info'
] as const

export const MAINTENANCE_PRIORITIES = [
  'high',
  'medium', 
  'low'
] as const

export const REGIONS = [
  'all',
  'addis-ababa',
  'oromia',
  'amhara',
  'tigray',
  'snnp'
] as const

export const TIME_RANGES = [
  '7d',
  '30d',
  '90d',
  '1y'
] as const
