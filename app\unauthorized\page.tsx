import Link from "next/link"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { AlertTriangle } from "lucide-react"

export default function UnauthorizedPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-slate-50 dark:bg-slate-900 p-4">
      <div className="mb-4 rounded-full bg-yellow-100 p-3 dark:bg-yellow-900/20">
        <AlertTriangle className="h-8 w-8 text-yellow-600" />
      </div>
      <h1 className="mb-2 text-2xl font-bold">Access Denied</h1>
      <p className="mb-6 text-center text-muted-foreground">
        You do not have permission to access this page.
        <br />
        Please contact your administrator if you believe this is an error.
      </p>
      <div className="flex gap-4">
        <Button asChild variant="outline">
          <Link href="/dashboard">Return to Dashboard</Link>
        </Button>
        <Button asChild>
          <Link href="/login">Sign In with Different Account</Link>
        </Button>
      </div>
    </div>
  )
}
