"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useRouter } from 'next/navigation'

// Types
interface User {
  id: string
  email: string
  name: string
  role: 'super_admin' | 'national_asset_manager' | 'national_maintenance_manager' | 'regional_admin' | 'regional_asset_manager' | 'regional_maintenance_engineer' | 'service_center_manager' | 'field_technician' | 'customer_service_agent' | 'audit_compliance_officer' | 'admin' | 'operator' | 'viewer'
  permissions: string[]
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  updateUser: (userData: Partial<User>) => void
  hasRole: (allowedRoles: string[]) => boolean
  hasPermission: (resource: string, action: string) => boolean
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth Provider Component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  // Check if user is authenticated on mount
  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true)

      // Check for stored auth token
      const token = localStorage.getItem('auth_token')
      const userData = localStorage.getItem('user_data')

      if (token && userData) {
        const parsedUser = JSON.parse(userData)
        setUser(parsedUser)

        // Ensure cookies are set for middleware
        document.cookie = `auth_token=${token}; path=/; max-age=${60 * 60 * 24 * 7}`
        document.cookie = `eeu_user=true; path=/; max-age=${60 * 60 * 24 * 7}`
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      // Clear invalid data
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')
      document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
      document.cookie = 'eeu_user=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      setError(null)

      // Mock authentication - replace with actual API call
      // Default password for all demo accounts is 'demo123'
      const demoPassword = 'demo123'

      const demoAccounts = {
        '<EMAIL>': {
          id: '1',
          email: '<EMAIL>',
          name: 'System Administrator',
          role: 'super_admin' as const,
          permissions: ['read', 'write', 'delete', 'admin', 'dashboard:read']
        },
        '<EMAIL>': {
          id: '2',
          email: '<EMAIL>',
          name: 'Asset Manager',
          role: 'national_asset_manager' as const,
          permissions: ['read', 'write', 'asset_management', 'dashboard:read']
        },
        '<EMAIL>': {
          id: '3',
          email: '<EMAIL>',
          name: 'Regional Administrator',
          role: 'regional_admin' as const,
          permissions: ['read', 'write', 'regional_admin', 'dashboard:read']
        },
        '<EMAIL>': {
          id: '4',
          email: '<EMAIL>',
          name: 'Service Manager',
          role: 'service_center_manager' as const,
          permissions: ['read', 'write', 'service_management', 'dashboard:read']
        },
        '<EMAIL>': {
          id: '5',
          email: '<EMAIL>',
          name: 'Field Technician',
          role: 'field_technician' as const,
          permissions: ['read', 'write', 'field_operations', 'dashboard:read']
        },
        '<EMAIL>': {
          id: '6',
          email: '<EMAIL>',
          name: 'Customer Service Representative',
          role: 'customer_service_agent' as const,
          permissions: ['read', 'customer_service', 'dashboard:read']
        }
      }

      if (password === demoPassword && demoAccounts[email as keyof typeof demoAccounts]) {
        const mockUser = demoAccounts[email as keyof typeof demoAccounts]

        // Store auth data in localStorage
        localStorage.setItem('auth_token', `mock_token_${mockUser.id}`)
        localStorage.setItem('user_data', JSON.stringify(mockUser))

        // Also set cookies for server-side middleware
        document.cookie = `auth_token=mock_token_${mockUser.id}; path=/; max-age=${60 * 60 * 24 * 7}` // 7 days
        document.cookie = `eeu_user=true; path=/; max-age=${60 * 60 * 24 * 7}` // 7 days

        setUser(mockUser)
        return true
      }

      // Invalid credentials
      setError('Invalid email or password. Please try again.')
      return false
    } catch (error) {
      console.error('Login failed:', error)
      setError('Login failed. Please try again.')
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    // Clear auth data
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')

    // Clear cookies
    document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    document.cookie = 'eeu_user=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'

    setUser(null)
    router.push('/login')
  }

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData }
      setUser(updatedUser)
      localStorage.setItem('user_data', JSON.stringify(updatedUser))
    }
  }

  // Check if user has any of the allowed roles
  const hasRole = (allowedRoles: string[]): boolean => {
    if (!user) return false
    return allowedRoles.includes(user.role)
  }

  // Check if user has specific permission
  const hasPermission = (resource: string, action: string): boolean => {
    if (!user) return false

    // Admin roles have all permissions
    if (user.role === 'admin' || user.role === 'super_admin') return true

    // Check if user has the specific permission
    const permissionKey = `${resource}:${action}`
    return user.permissions.includes(permissionKey) ||
           user.permissions.includes(resource) ||
           user.permissions.includes(action)
  }

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    error,
    login,
    logout,
    updateUser,
    hasRole,
    hasPermission
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Export types for use in other components
export type { User, AuthContextType }
