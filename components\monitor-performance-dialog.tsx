"use client"

import type React from "react"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Textarea } from "@/src/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useToast } from "@/src/components/ui/use-toast"
import { CalendarIcon } from "lucide-react"
import { Calendar } from "@/src/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/src/lib/utils"
import type { Transformer } from "@/src/types/transformer"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/src/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Slider } from "@/src/components/ui/slider"

interface MonitorPerformanceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  transformer?: Transformer
}

export function MonitorPerformanceDialog({ open, onOpenChange, transformer }: MonitorPerformanceDialogProps) {
  const [recordDate, setRecordDate] = useState<Date | undefined>(new Date())
  const [loadPercentage, setLoadPercentage] = useState(70)
  const [temperature, setTemperature] = useState("45")
  const [oilLevel, setOilLevel] = useState("100")
  const [noiseLevel, setNoiseLevel] = useState("40")
  const [vibrationLevel, setVibrationLevel] = useState("10")
  const [powerFactor, setPowerFactor] = useState("0.95")
  const [efficiency, setEfficiency] = useState("98")
  const [voltageReading, setVoltageReading] = useState({ primary: "11000", secondary: "400" })
  const [currentReading, setCurrentReading] = useState({ primary: "25", secondary: "680" })
  const [abnormalConditions, setAbnormalConditions] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // In a real application, this would be an API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Performance data recorded successfully",
        description: `Performance data for transformer ${transformer?.serialNumber} has been recorded for ${recordDate ? format(recordDate, "PPP") : "the selected date"}.`,
      })

      onOpenChange(false)
    } catch (error) {
      toast({
        title: "Error recording performance data",
        description: "There was an error recording the performance data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Monitor Transformer Performance</DialogTitle>
          <DialogDescription>
            Record performance data for transformer {transformer?.serialNumber || ""}. This data will be used for
            condition monitoring and predictive maintenance.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="recordDate">Recording Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !recordDate && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {recordDate ? format(recordDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={recordDate} onSelect={setRecordDate} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="loadPercentage">Load Percentage (%)</Label>
                <div className="pt-5">
                  <Slider
                    value={[loadPercentage]}
                    min={0}
                    max={150}
                    step={1}
                    onValueChange={(value) => setLoadPercentage(value[0])}
                  />
                  <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                    <span>0%</span>
                    <span>50%</span>
                    <span>100%</span>
                    <span>150%</span>
                  </div>
                  <p className="text-center font-medium mt-2">{loadPercentage}%</p>
                </div>
              </div>
            </div>

            <Tabs defaultValue="readings" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="readings">Readings</TabsTrigger>
                <TabsTrigger value="conditions">Conditions</TabsTrigger>
                <TabsTrigger value="electrical">Electrical</TabsTrigger>
              </TabsList>

              <TabsContent value="readings" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="temperature">Temperature (°C)</Label>
                    <Input
                      id="temperature"
                      type="number"
                      value={temperature}
                      onChange={(e) => setTemperature(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="oilLevel">Oil Level (%)</Label>
                    <Input
                      id="oilLevel"
                      type="number"
                      min="0"
                      max="100"
                      value={oilLevel}
                      onChange={(e) => setOilLevel(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="noiseLevel">Noise Level (dB)</Label>
                    <Input
                      id="noiseLevel"
                      type="number"
                      value={noiseLevel}
                      onChange={(e) => setNoiseLevel(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="vibrationLevel">Vibration Level (mm/s)</Label>
                    <Input
                      id="vibrationLevel"
                      type="number"
                      value={vibrationLevel}
                      onChange={(e) => setVibrationLevel(e.target.value)}
                      required
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="conditions" className="space-y-4 mt-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="loadCondition">Loading Condition</Label>
                    <Select defaultValue="normal">
                      <SelectTrigger id="loadCondition">
                        <SelectValue placeholder="Select loading condition" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light Load</SelectItem>
                        <SelectItem value="normal">Normal Load</SelectItem>
                        <SelectItem value="heavy">Heavy Load</SelectItem>
                        <SelectItem value="overload">Overload</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="coolingSystem">Cooling System Status</Label>
                    <Select defaultValue="normal">
                      <SelectTrigger id="coolingSystem">
                        <SelectValue placeholder="Select cooling system status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="normal">Normal Operation</SelectItem>
                        <SelectItem value="partial">Partially Functional</SelectItem>
                        <SelectItem value="failure">Failure</SelectItem>
                        <SelectItem value="na">Not Applicable</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ambientTemp">Ambient Temperature (°C)</Label>
                    <Input id="ambientTemp" type="number" placeholder="e.g., 25" required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="weatherCondition">Weather Condition</Label>
                    <Select defaultValue="clear">
                      <SelectTrigger id="weatherCondition">
                        <SelectValue placeholder="Select weather condition" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="clear">Clear</SelectItem>
                        <SelectItem value="rain">Rain</SelectItem>
                        <SelectItem value="storm">Storm</SelectItem>
                        <SelectItem value="hot">Hot</SelectItem>
                        <SelectItem value="humid">Humid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="electrical" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Voltage (V)</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="space-y-2">
                          <Label htmlFor="voltPrimary">Primary</Label>
                          <Input
                            id="voltPrimary"
                            type="number"
                            value={voltageReading.primary}
                            onChange={(e) => setVoltageReading({ ...voltageReading, primary: e.target.value })}
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="voltSecondary">Secondary</Label>
                          <Input
                            id="voltSecondary"
                            type="number"
                            value={voltageReading.secondary}
                            onChange={(e) => setVoltageReading({ ...voltageReading, secondary: e.target.value })}
                            required
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Current (A)</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="space-y-2">
                          <Label htmlFor="currPrimary">Primary</Label>
                          <Input
                            id="currPrimary"
                            type="number"
                            value={currentReading.primary}
                            onChange={(e) => setCurrentReading({ ...currentReading, primary: e.target.value })}
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="currSecondary">Secondary</Label>
                          <Input
                            id="currSecondary"
                            type="number"
                            value={currentReading.secondary}
                            onChange={(e) => setCurrentReading({ ...currentReading, secondary: e.target.value })}
                            required
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <div className="space-y-2">
                    <Label htmlFor="powerFactor">Power Factor</Label>
                    <Input
                      id="powerFactor"
                      type="number"
                      min="0"
                      max="1"
                      step="0.01"
                      value={powerFactor}
                      onChange={(e) => setPowerFactor(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="efficiency">Efficiency (%)</Label>
                    <Input
                      id="efficiency"
                      type="number"
                      min="0"
                      max="100"
                      value={efficiency}
                      onChange={(e) => setEfficiency(e.target.value)}
                      required
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <div className="space-y-2">
              <Label htmlFor="abnormalConditions">Abnormal Conditions Observed</Label>
              <Textarea
                id="abnormalConditions"
                placeholder="Note any abnormal conditions or anomalies observed"
                value={abnormalConditions}
                onChange={(e) => setAbnormalConditions(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : "Record Performance Data"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
