const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

async function checkViews() {
  const connection = await mysql.createConnection(config);
  const [rows] = await connection.execute("SHOW FULL TABLES WHERE Table_type = 'VIEW'");
  console.log('Views in dtms_eeu_db:');
  rows.forEach(row => console.log(Object.values(row)[0]));
  await connection.end();
}

checkViews();
