"use client"

import React, { useState, use<PERSON><PERSON>back, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Filter,
  Search,
  Save,
  FolderOpen,
  Trash2,
  Copy,
  Share2,
  Download,
  Upload,
  RotateCcw,
  History,
  Star,
  StarOff,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronUp,
  Plus,
  X,
  Check,
  AlertCircle,
  Calendar,
  Clock,
  MapPin,
  Zap,
  Settings,
  Users,
  Building,
  Activity,
  Gauge,
  ThermometerSun,
  DollarSign,
  Wrench,
  AlertTriangle
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { toast } from 'sonner'

export interface FilterPreset {
  id: string
  name: string
  description?: string
  filters: any
  isStarred: boolean
  isPublic: boolean
  createdAt: Date
  updatedAt: Date
  createdBy: string
  tags: string[]
  usageCount: number
}

export interface FilterHistory {
  id: string
  filters: any
  timestamp: Date
  description: string
}

export interface AdvancedFilterSystemProps {
  currentFilters: any
  onFiltersChange: (filters: any) => void
  onReset: () => void
  availableFields: Array<{
    key: string
    label: string
    type: 'text' | 'number' | 'date' | 'select' | 'multiselect' | 'range'
    options?: Array<{ value: string; label: string }>
    icon?: React.ReactNode
    category?: string
  }>
  className?: string
}

export function AdvancedFilterSystem({
  currentFilters,
  onFiltersChange,
  onReset,
  availableFields,
  className = ''
}: AdvancedFilterSystemProps) {
  const [presets, setPresets] = useState<FilterPreset[]>([])
  const [history, setHistory] = useState<FilterHistory[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null)
  const [showPresetDialog, setShowPresetDialog] = useState(false)
  const [showHistoryDialog, setShowHistoryDialog] = useState(false)
  const [newPresetName, setNewPresetName] = useState('')
  const [newPresetDescription, setNewPresetDescription] = useState('')
  const [newPresetTags, setNewPresetTags] = useState<string[]>([])
  const [isExpanded, setIsExpanded] = useState(true)
  const [quickFilters, setQuickFilters] = useState<any>({})
  const [autoSuggest, setAutoSuggest] = useState<string[]>([])
  const [showAdvanced, setShowAdvanced] = useState(false)

  // Load presets and history from localStorage
  useEffect(() => {
    const savedPresets = localStorage.getItem('filter-presets')
    if (savedPresets) {
      try {
        const parsed = JSON.parse(savedPresets)
        setPresets(parsed.map((p: any) => ({
          ...p,
          createdAt: new Date(p.createdAt),
          updatedAt: new Date(p.updatedAt)
        })))
      } catch (error) {
        console.error('Failed to load presets:', error)
      }
    }

    const savedHistory = localStorage.getItem('filter-history')
    if (savedHistory) {
      try {
        const parsed = JSON.parse(savedHistory)
        setHistory(parsed.map((h: any) => ({
          ...h,
          timestamp: new Date(h.timestamp)
        })))
      } catch (error) {
        console.error('Failed to load history:', error)
      }
    }
  }, [])

  // Save presets to localStorage
  const savePresets = useCallback((newPresets: FilterPreset[]) => {
    setPresets(newPresets)
    localStorage.setItem('filter-presets', JSON.stringify(newPresets))
  }, [])

  // Save history to localStorage
  const saveHistory = useCallback((newHistory: FilterHistory[]) => {
    setHistory(newHistory)
    localStorage.setItem('filter-history', JSON.stringify(newHistory))
  }, [])

  // Add to history when filters change
  useEffect(() => {
    if (Object.keys(currentFilters).length > 0) {
      const newHistoryItem: FilterHistory = {
        id: Date.now().toString(),
        filters: currentFilters,
        timestamp: new Date(),
        description: generateFilterDescription(currentFilters)
      }

      const updatedHistory = [newHistoryItem, ...history.slice(0, 19)] // Keep last 20
      saveHistory(updatedHistory)
    }
  }, [currentFilters, history, saveHistory])

  const generateFilterDescription = (filters: any): string => {
    const activeFilters = Object.entries(filters)
      .filter(([_, value]) => value !== null && value !== undefined && value !== '')
      .map(([key, value]) => {
        const field = availableFields.find(f => f.key === key)
        return field ? `${field.label}: ${value}` : `${key}: ${value}`
      })

    return activeFilters.length > 0 
      ? activeFilters.slice(0, 3).join(', ') + (activeFilters.length > 3 ? '...' : '')
      : 'No filters applied'
  }

  const savePreset = useCallback(() => {
    if (!newPresetName.trim()) {
      toast.error('Please enter a preset name')
      return
    }

    const newPreset: FilterPreset = {
      id: Date.now().toString(),
      name: newPresetName.trim(),
      description: newPresetDescription.trim(),
      filters: currentFilters,
      isStarred: false,
      isPublic: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'current-user', // In real app, get from auth
      tags: newPresetTags,
      usageCount: 0
    }

    const updatedPresets = [...presets, newPreset]
    savePresets(updatedPresets)
    
    setNewPresetName('')
    setNewPresetDescription('')
    setNewPresetTags([])
    setShowPresetDialog(false)
    
    toast.success('Filter preset saved successfully')
  }, [newPresetName, newPresetDescription, newPresetTags, currentFilters, presets, savePresets])

  const loadPreset = useCallback((preset: FilterPreset) => {
    onFiltersChange(preset.filters)
    setSelectedPreset(preset.id)
    
    // Update usage count
    const updatedPresets = presets.map(p => 
      p.id === preset.id 
        ? { ...p, usageCount: p.usageCount + 1, updatedAt: new Date() }
        : p
    )
    savePresets(updatedPresets)
    
    toast.success(`Loaded preset: ${preset.name}`)
  }, [onFiltersChange, presets, savePresets])

  const deletePreset = useCallback((presetId: string) => {
    const updatedPresets = presets.filter(p => p.id !== presetId)
    savePresets(updatedPresets)
    
    if (selectedPreset === presetId) {
      setSelectedPreset(null)
    }
    
    toast.success('Preset deleted')
  }, [presets, savePresets, selectedPreset])

  const togglePresetStar = useCallback((presetId: string) => {
    const updatedPresets = presets.map(p => 
      p.id === presetId ? { ...p, isStarred: !p.isStarred } : p
    )
    savePresets(updatedPresets)
  }, [presets, savePresets])

  const exportPresets = useCallback(() => {
    const dataStr = JSON.stringify(presets, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'filter-presets.json'
    link.click()
    toast.success('Presets exported')
  }, [presets])

  const importPresets = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string)
        const newPresets = imported.map((p: any) => ({
          ...p,
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          createdAt: new Date(p.createdAt),
          updatedAt: new Date(p.updatedAt)
        }))
        
        const updatedPresets = [...presets, ...newPresets]
        savePresets(updatedPresets)
        toast.success(`Imported ${newPresets.length} presets`)
      } catch (error) {
        toast.error('Failed to import presets')
      }
    }
    reader.readAsText(file)
  }, [presets, savePresets])

  const getActiveFilterCount = () => {
    return Object.values(currentFilters).filter(value => 
      value !== null && value !== undefined && value !== ''
    ).length
  }

  const clearAllFilters = () => {
    onReset()
    setSelectedPreset(null)
    toast.success('All filters cleared')
  }

  const filteredPresets = presets.filter(preset =>
    preset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    preset.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    preset.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const starredPresets = filteredPresets.filter(p => p.isStarred)
  const recentPresets = filteredPresets
    .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
    .slice(0, 5)

  return (
    <Card className={`border-0 shadow-xl bg-white/95 backdrop-blur-sm ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Advanced Filters
            </CardTitle>
            {getActiveFilterCount() > 0 && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                {getActiveFilterCount()} active
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Settings className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Filter Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={() => setShowPresetDialog(true)}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Preset
                </DropdownMenuItem>
                
                <DropdownMenuItem onClick={() => setShowHistoryDialog(true)}>
                  <History className="mr-2 h-4 w-4" />
                  View History
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={exportPresets}>
                  <Download className="mr-2 h-4 w-4" />
                  Export Presets
                </DropdownMenuItem>
                
                <DropdownMenuItem onClick={() => document.getElementById('import-presets')?.click()}>
                  <Upload className="mr-2 h-4 w-4" />
                  Import Presets
                </DropdownMenuItem>
                
                <input
                  id="import-presets"
                  type="file"
                  accept=".json"
                  onChange={importPresets}
                  className="hidden"
                />
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={clearAllFilters}>
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Clear All
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Quick Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search transformers, locations, or any field..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Preset Quick Access */}
        {(starredPresets.length > 0 || recentPresets.length > 0) && (
          <div className="space-y-2">
            {starredPresets.length > 0 && (
              <div>
                <Label className="text-xs text-gray-500 mb-1 block">Starred Presets</Label>
                <div className="flex flex-wrap gap-2">
                  {starredPresets.slice(0, 3).map((preset) => (
                    <Button
                      key={preset.id}
                      variant={selectedPreset === preset.id ? "default" : "outline"}
                      size="sm"
                      onClick={() => loadPreset(preset)}
                      className="h-7 text-xs"
                    >
                      <Star className="mr-1 h-3 w-3 fill-current" />
                      {preset.name}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {recentPresets.length > 0 && (
              <div>
                <Label className="text-xs text-gray-500 mb-1 block">Recent Presets</Label>
                <div className="flex flex-wrap gap-2">
                  {recentPresets.slice(0, 3).map((preset) => (
                    <Button
                      key={preset.id}
                      variant={selectedPreset === preset.id ? "default" : "outline"}
                      size="sm"
                      onClick={() => loadPreset(preset)}
                      className="h-7 text-xs"
                    >
                      <Clock className="mr-1 h-3 w-3" />
                      {preset.name}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          {/* Filter Categories */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableFields.map((field) => (
              <div key={field.key} className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-2">
                  {field.icon}
                  {field.label}
                </Label>
                
                {field.type === 'text' && (
                  <Input
                    placeholder={`Filter by ${field.label.toLowerCase()}`}
                    value={currentFilters[field.key] || ''}
                    onChange={(e) => onFiltersChange({
                      ...currentFilters,
                      [field.key]: e.target.value
                    })}
                  />
                )}
                
                {field.type === 'select' && field.options && (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-between">
                        {currentFilters[field.key] || `Select ${field.label.toLowerCase()}`}
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder={`Search ${field.label.toLowerCase()}...`} />
                        <CommandList>
                          <CommandEmpty>No options found.</CommandEmpty>
                          <CommandGroup>
                            {field.options.map((option) => (
                              <CommandItem
                                key={option.value}
                                onSelect={() => onFiltersChange({
                                  ...currentFilters,
                                  [field.key]: option.value
                                })}
                              >
                                <Check
                                  className={`mr-2 h-4 w-4 ${
                                    currentFilters[field.key] === option.value ? "opacity-100" : "opacity-0"
                                  }`}
                                />
                                {option.label}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                )}
              </div>
            ))}
          </div>

          {/* Active Filters Display */}
          {getActiveFilterCount() > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium text-blue-900">Active Filters</Label>
                <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {Object.entries(currentFilters)
                  .filter(([_, value]) => value !== null && value !== undefined && value !== '')
                  .map(([key, value]) => {
                    const field = availableFields.find(f => f.key === key)
                    return (
                      <Badge key={key} variant="secondary" className="bg-blue-100 text-blue-800">
                        {field?.label || key}: {String(value)}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-1 h-4 w-4 p-0"
                          onClick={() => onFiltersChange({
                            ...currentFilters,
                            [key]: null
                          })}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    )
                  })}
              </div>
            </div>
          )}
        </CardContent>
      )}

      {/* Save Preset Dialog */}
      <Dialog open={showPresetDialog} onOpenChange={setShowPresetDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Filter Preset</DialogTitle>
            <DialogDescription>
              Save your current filter configuration for quick access later.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="preset-name">Preset Name</Label>
              <Input
                id="preset-name"
                placeholder="Enter preset name"
                value={newPresetName}
                onChange={(e) => setNewPresetName(e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="preset-description">Description (Optional)</Label>
              <Input
                id="preset-description"
                placeholder="Enter description"
                value={newPresetDescription}
                onChange={(e) => setNewPresetDescription(e.target.value)}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPresetDialog(false)}>
              Cancel
            </Button>
            <Button onClick={savePreset}>
              Save Preset
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* History Dialog */}
      <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Filter History</DialogTitle>
            <DialogDescription>
              View and restore previous filter configurations.
            </DialogDescription>
          </DialogHeader>
          
          <div className="max-h-96 overflow-y-auto space-y-2">
            {history.map((item) => (
              <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <p className="font-medium">{item.description}</p>
                  <p className="text-sm text-gray-500">
                    {item.timestamp.toLocaleString()}
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    onFiltersChange(item.filters)
                    setShowHistoryDialog(false)
                    toast.success('Filter configuration restored')
                  }}
                >
                  Restore
                </Button>
              </div>
            ))}
            
            {history.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No filter history available
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
