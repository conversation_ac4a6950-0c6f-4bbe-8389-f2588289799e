"use client"

import { 
  HelpCircle, 
  BookOpen, 
  MessageSquare, 
  Video, 
  FileText, 
  Mail, 
  Phone, 
  ExternalLink,
  Lightbulb
} from "lucide-react"
import { But<PERSON> } from "@/src/components/ui/button"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuGroup,
  DropdownMenuLabel
} from "@/src/components/ui/dropdown-menu"
import { useToast } from "@/src/components/ui/use-toast"

export function HelpSupport() {
  const { toast } = useToast()

  // Handle help item click
  const handleHelpClick = (action: string, url?: string) => {
    toast({
      title: "Help & Support",
      description: `Opening: ${action}`,
    })
    
    if (url) {
      // In a real app, you would open the URL or navigate to the page
      window.open(url, "_blank")
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon">
          <HelpCircle className="h-5 w-5" />
          <span className="sr-only">Help & Support</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[240px]">
        <DropdownMenuLabel>Help & Support</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={() => handleHelpClick("Documentation", "/docs")}>
            <BookOpen className="mr-2 h-4 w-4" />
            <span>Documentation</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleHelpClick("Video Tutorials", "/tutorials")}>
            <Video className="mr-2 h-4 w-4" />
            <span>Video Tutorials</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleHelpClick("User Guide", "/guide")}>
            <FileText className="mr-2 h-4 w-4" />
            <span>User Guide</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleHelpClick("Tips & Tricks", "/tips")}>
            <Lightbulb className="mr-2 h-4 w-4" />
            <span>Tips & Tricks</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={() => handleHelpClick("Contact Support", "mailto:<EMAIL>")}>
            <Mail className="mr-2 h-4 w-4" />
            <span>Email Support</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleHelpClick("Live Chat", "/chat")}>
            <MessageSquare className="mr-2 h-4 w-4" />
            <span>Live Chat</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleHelpClick("Call Support", "tel:+************")}>
            <Phone className="mr-2 h-4 w-4" />
            <span>Phone Support</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => handleHelpClick("Report Issue", "/support/issue")}>
          <ExternalLink className="mr-2 h-4 w-4" />
          <span>Report an Issue</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
