"use client"

/**
 * Overview Tab Component
 * 
 * This component displays the overview dashboard with key metrics,
 * charts, and real-time data visualization.
 */

import React from 'react'
import {
  Zap,
  CheckCircle,
  AlertTriangle,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Activity,
  Battery,
  Thermometer,
  Gauge,
  MapPin,
  Clock,
  Users,
  Wrench,
  Shield,
  BarChart3,
  PieChart,
  LineChart
} from 'lucide-react'

interface OverviewTabProps {
  dashboardData: any
  isLoading: boolean
}

export default function OverviewTab({ dashboardData, isLoading }: OverviewTabProps) {
  const { transformerStatistics, recentAlerts, upcomingMaintenance } = dashboardData

  // Calculate health percentage
  const healthPercentage = transformerStatistics?.total > 0 
    ? Math.round(((transformerStatistics.byStatus?.operational || 0) / transformerStatistics.total) * 100)
    : 0

  // Get status color helper
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'operational': return 'text-green-600 bg-green-50 border-green-200'
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'maintenance': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      case 'offline': return 'text-gray-600 bg-gray-50 border-gray-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Loading skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg p-6 border border-gray-200 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Transformers */}
        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Total Transformers</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">
                {transformerStatistics?.total || 0}
              </p>
              <div className="flex items-center mt-3">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+5 from last month</span>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-blue-100">
              <Zap className="w-8 h-8 text-blue-600" />
            </div>
          </div>
        </div>

        {/* System Health */}
        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">System Health</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">
                {healthPercentage}%
              </p>
              <div className="flex items-center mt-3">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+2% from last week</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${healthPercentage}%` }}
                ></div>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-green-100">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </div>
        </div>

        {/* Active Alerts */}
        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Active Alerts</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">
                {transformerStatistics?.alerts?.total || 0}
              </p>
              <div className="flex items-center mt-3">
                <AlertTriangle className="w-4 h-4 text-orange-500 mr-1" />
                <span className="text-sm text-orange-600">
                  {transformerStatistics?.alerts?.critical || 0} critical
                </span>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-orange-100">
              <AlertTriangle className="w-8 h-8 text-orange-600" />
            </div>
          </div>
        </div>

        {/* Maintenance Due */}
        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600">Maintenance Due</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">
                {transformerStatistics?.maintenance?.scheduled || 0}
              </p>
              <div className="flex items-center mt-3">
                <Clock className="w-4 h-4 text-blue-500 mr-1" />
                <span className="text-sm text-blue-600">
                  {transformerStatistics?.maintenance?.overdue || 0} overdue
                </span>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-blue-100">
              <Wrench className="w-8 h-8 text-blue-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Status Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Transformer Status Breakdown */}
        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Status Distribution</h3>
            <PieChart className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {Object.entries(transformerStatistics?.byStatus || {}).map(([status, count]) => {
              const percentage = transformerStatistics?.total > 0 
                ? Math.round((count as number / transformerStatistics.total) * 100)
                : 0
              
              return (
                <div key={status} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      status === 'operational' ? 'bg-green-500' :
                      status === 'warning' ? 'bg-yellow-500' :
                      status === 'maintenance' ? 'bg-blue-500' :
                      status === 'critical' ? 'bg-red-500' :
                      'bg-gray-500'
                    }`}></div>
                    <span className="text-sm font-medium text-gray-900 capitalize">
                      {status}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{count}</span>
                    <span className="text-xs text-gray-500">({percentage}%)</span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Regional Distribution */}
        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Regional Distribution</h3>
            <BarChart3 className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {Object.entries(transformerStatistics?.byRegion || {}).slice(0, 5).map(([region, count]) => {
              const percentage = transformerStatistics?.total > 0 
                ? Math.round((count as number / transformerStatistics.total) * 100)
                : 0
              
              return (
                <div key={region} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">{region}</span>
                    <span className="text-sm text-gray-600">{count}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Alerts */}
        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Recent Alerts</h3>
            <span className="text-sm text-gray-500">Last 24 hours</span>
          </div>
          
          <div className="space-y-4">
            {recentAlerts?.slice(0, 5).map((alert: any) => (
              <div key={alert.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                <div className={`p-1 rounded-full ${getSeverityColor(alert.severity)}`}>
                  <AlertTriangle className="w-4 h-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {alert.title}
                  </p>
                  <p className="text-xs text-gray-600 mt-1">
                    {alert.transformerSerial} • {alert.regionName}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {new Date(alert.createdAt).toLocaleString()}
                  </p>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(alert.severity)}`}>
                  {alert.severity}
                </span>
              </div>
            )) || (
              <div className="text-center py-8">
                <AlertTriangle className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">No recent alerts</p>
              </div>
            )}
          </div>
        </div>

        {/* Upcoming Maintenance */}
        <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Upcoming Maintenance</h3>
            <span className="text-sm text-gray-500">Next 7 days</span>
          </div>
          
          <div className="space-y-4">
            {upcomingMaintenance?.slice(0, 5).map((maintenance: any) => (
              <div key={maintenance.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                <div className="p-1 rounded-full bg-blue-100">
                  <Wrench className="w-4 h-4 text-blue-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {maintenance.title}
                  </p>
                  <p className="text-xs text-gray-600 mt-1">
                    {maintenance.transformerSerial} • {maintenance.type}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {new Date(maintenance.scheduledDate).toLocaleDateString()}
                  </p>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  maintenance.priority === 'high' ? 'bg-red-100 text-red-800' :
                  maintenance.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {maintenance.priority}
                </span>
              </div>
            )) || (
              <div className="text-center py-8">
                <Wrench className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">No upcoming maintenance</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
