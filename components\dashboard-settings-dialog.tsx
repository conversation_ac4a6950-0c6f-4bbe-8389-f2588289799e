"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/src/components/ui/tabs"
import { Switch } from "@/src/components/ui/switch"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Checkbox } from "@/src/components/ui/checkbox"
import { useToast } from "@/src/components/ui/use-toast"
import { BarChart2, Bell, Filter, Save, RefreshCw, LayoutDashboard, Clock } from "lucide-react"

interface DashboardSettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function DashboardSettingsDialog({
  open,
  onOpenChange
}: DashboardSettingsDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("layout")
  
  // Layout settings
  const [showOverview, setShowOverview] = useState(true)
  const [showMapView, setShowMapView] = useState(true)
  const [showPredictive, setShowPredictive] = useState(true)
  const [showOutages, setShowOutages] = useState(true)
  const [refreshInterval, setRefreshInterval] = useState("5min")
  
  // Chart settings
  const [defaultChartType, setDefaultChartType] = useState("bar")
  const [showLegends, setShowLegends] = useState(true)
  const [showGridLines, setShowGridLines] = useState(true)
  const [animateCharts, setAnimateCharts] = useState(true)
  
  // Notification settings
  const [criticalAlerts, setCriticalAlerts] = useState(true)
  const [warningAlerts, setWarningAlerts] = useState(true)
  const [infoAlerts, setInfoAlerts] = useState(false)
  const [maintenanceAlerts, setMaintenanceAlerts] = useState(true)
  const [soundAlerts, setSoundAlerts] = useState(false)
  
  const handleSave = () => {
    // In a real app, this would save all settings
    toast({
      title: "Dashboard Settings Saved",
      description: "Your dashboard customization settings have been applied."
    })
    
    onOpenChange(false)
  }
  
  const handleReset = () => {
    // Reset to defaults
    setShowOverview(true)
    setShowMapView(true)
    setShowPredictive(true)
    setShowOutages(true)
    setRefreshInterval("5min")
    setDefaultChartType("bar")
    setShowLegends(true)
    setShowGridLines(true)
    setAnimateCharts(true)
    setCriticalAlerts(true)
    setWarningAlerts(true)
    setInfoAlerts(false)
    setMaintenanceAlerts(true)
    setSoundAlerts(false)
    
    toast({
      title: "Settings Reset",
      description: "Dashboard settings have been reset to defaults."
    })
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Dashboard Settings</DialogTitle>
          <DialogDescription>
            Customize your dashboard layout, charts, and notifications
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-2">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="layout">
              <LayoutDashboard className="mr-2 h-4 w-4" />
              Layout
            </TabsTrigger>
            <TabsTrigger value="charts">
              <BarChart2 className="mr-2 h-4 w-4" />
              Charts
            </TabsTrigger>
            <TabsTrigger value="notifications">
              <Bell className="mr-2 h-4 w-4" />
              Notifications
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="layout" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium mb-2">Visible Sections</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="show-overview" 
                      checked={showOverview} 
                      onCheckedChange={(checked) => setShowOverview(checked as boolean)} 
                    />
                    <label
                      htmlFor="show-overview"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Overview Tab
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="show-map-view" 
                      checked={showMapView} 
                      onCheckedChange={(checked) => setShowMapView(checked as boolean)} 
                    />
                    <label
                      htmlFor="show-map-view"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Map View Tab
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="show-predictive" 
                      checked={showPredictive} 
                      onCheckedChange={(checked) => setShowPredictive(checked as boolean)} 
                    />
                    <label
                      htmlFor="show-predictive"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Predictive Maintenance Tab
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="show-outages" 
                      checked={showOutages} 
                      onCheckedChange={(checked) => setShowOutages(checked as boolean)} 
                    />
                    <label
                      htmlFor="show-outages"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Outages Tab
                    </label>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="refresh-interval">Auto-Refresh Interval</Label>
                <Select value={refreshInterval} onValueChange={setRefreshInterval}>
                  <SelectTrigger id="refresh-interval">
                    <SelectValue placeholder="Select refresh interval" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="off">Off (Manual Refresh)</SelectItem>
                    <SelectItem value="1min">Every 1 Minute</SelectItem>
                    <SelectItem value="5min">Every 5 Minutes</SelectItem>
                    <SelectItem value="15min">Every 15 Minutes</SelectItem>
                    <SelectItem value="30min">Every 30 Minutes</SelectItem>
                    <SelectItem value="1hour">Every Hour</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="charts" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="default-chart-type">Default Chart Type</Label>
                <Select value={defaultChartType} onValueChange={setDefaultChartType}>
                  <SelectTrigger id="default-chart-type">
                    <SelectValue placeholder="Select chart type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bar">Bar Chart</SelectItem>
                    <SelectItem value="line">Line Chart</SelectItem>
                    <SelectItem value="pie">Pie Chart</SelectItem>
                    <SelectItem value="area">Area Chart</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-legends">Show Chart Legends</Label>
                  <Switch 
                    id="show-legends" 
                    checked={showLegends} 
                    onCheckedChange={setShowLegends} 
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-grid-lines">Show Grid Lines</Label>
                  <Switch 
                    id="show-grid-lines" 
                    checked={showGridLines} 
                    onCheckedChange={setShowGridLines} 
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="animate-charts">Animate Charts</Label>
                  <Switch 
                    id="animate-charts" 
                    checked={animateCharts} 
                    onCheckedChange={setAnimateCharts} 
                  />
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="notifications" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium mb-2">Alert Types</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="critical-alerts">Critical Alerts</Label>
                    <Switch 
                      id="critical-alerts" 
                      checked={criticalAlerts} 
                      onCheckedChange={setCriticalAlerts} 
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="warning-alerts">Warning Alerts</Label>
                    <Switch 
                      id="warning-alerts" 
                      checked={warningAlerts} 
                      onCheckedChange={setWarningAlerts} 
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="info-alerts">Information Alerts</Label>
                    <Switch 
                      id="info-alerts" 
                      checked={infoAlerts} 
                      onCheckedChange={setInfoAlerts} 
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="maintenance-alerts">Maintenance Alerts</Label>
                    <Switch 
                      id="maintenance-alerts" 
                      checked={maintenanceAlerts} 
                      onCheckedChange={setMaintenanceAlerts} 
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="sound-alerts">Sound Notifications</Label>
                <Switch 
                  id="sound-alerts" 
                  checked={soundAlerts} 
                  onCheckedChange={setSoundAlerts} 
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="flex justify-between items-center mt-4">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
