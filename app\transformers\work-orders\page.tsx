"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  FileText,
  Wrench,
  Clock,
  User,
  MapPin,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Play,
  Pause,
  Square,
  Edit,
  Eye,
  Trash2,
  Plus,
  Download,
  Upload,
  RefreshCw,
  Search,
  Filter,
  Settings,
  Bell,
  Target,
  TrendingUp,
  Activity,
  BarChart3,
  PieChart,
  Users,
  Package,
  Truck,
  Star,
  Award,
  Clipboard,
  Camera,
  MessageSquare,
  Phone,
  Mail
} from 'lucide-react'
import {
  <PERSON><PERSON><PERSON> as RechartsLine<PERSON>hart,
  Line,
  AreaChart,
  Area,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>rts<PERSON>ie<PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { MainLayout } from "@/src/components/layout/main-layout"

// Mock work order data
const mockWorkOrders = [
  {
    id: 'WO-2024-001',
    title: 'Routine Oil Analysis and Cooling System Inspection',
    transformerId: 'T-AA-001',
    transformerName: 'Lideta Primary Transformer',
    location: 'Lideta Substation, Addis Ababa',
    type: 'preventive',
    priority: 'medium',
    status: 'in_progress',
    assignedTo: 'John Doe',
    assignedTeam: ['John Doe', 'Alice Brown'],
    createdDate: '2024-02-10',
    scheduledDate: '2024-02-15',
    startedDate: '2024-02-15T08:00:00Z',
    estimatedCompletion: '2024-02-15T16:00:00Z',
    actualCompletion: null,
    description: 'Perform comprehensive oil analysis, check cooling system performance, and inspect transformer condition.',
    estimatedHours: 8,
    actualHours: 6.5,
    estimatedCost: 25000,
    actualCost: 22500,
    materials: [
      { item: 'Oil Filter', quantity: 2, cost: 5000 },
      { item: 'Gaskets', quantity: 4, cost: 2000 },
      { item: 'Testing Kit', quantity: 1, cost: 1500 }
    ],
    tasks: [
      { id: 1, description: 'Oil level check', completed: true, completedBy: 'John Doe', completedAt: '2024-02-15T09:30:00Z' },
      { id: 2, description: 'Oil quality analysis', completed: true, completedBy: 'John Doe', completedAt: '2024-02-15T11:00:00Z' },
      { id: 3, description: 'Cooling system inspection', completed: true, completedBy: 'Alice Brown', completedAt: '2024-02-15T13:00:00Z' },
      { id: 4, description: 'Temperature monitoring setup', completed: false, completedBy: null, completedAt: null },
      { id: 5, description: 'Final system testing', completed: false, completedBy: null, completedAt: null }
    ],
    notes: [
      { id: 1, author: 'John Doe', timestamp: '2024-02-15T09:45:00Z', content: 'Oil level is within normal range. Quality appears good.' },
      { id: 2, author: 'Alice Brown', timestamp: '2024-02-15T13:15:00Z', content: 'Cooling system operating efficiently. No issues detected.' }
    ],
    attachments: [
      { id: 1, name: 'oil_analysis_report.pdf', type: 'pdf', size: '2.4 MB', uploadedBy: 'John Doe' },
      { id: 2, name: 'cooling_system_photos.zip', type: 'zip', size: '15.7 MB', uploadedBy: 'Alice Brown' }
    ]
  },
  {
    id: 'WO-2024-002',
    title: 'High Temperature Alarm Investigation',
    transformerId: 'T-OR-045',
    transformerName: 'Sebeta Distribution Transformer',
    location: 'Sebeta Substation, Oromia',
    type: 'corrective',
    priority: 'high',
    status: 'pending',
    assignedTo: 'Jane Smith',
    assignedTeam: ['Jane Smith', 'Bob Johnson'],
    createdDate: '2024-02-12',
    scheduledDate: '2024-02-16',
    startedDate: null,
    estimatedCompletion: '2024-02-17T18:00:00Z',
    actualCompletion: null,
    description: 'Investigate high temperature alarm, check cooling system, and replace faulty components if necessary.',
    estimatedHours: 12,
    actualHours: 0,
    estimatedCost: 45000,
    actualCost: 0,
    materials: [
      { item: 'Cooling Fan', quantity: 1, cost: 15000 },
      { item: 'Temperature Sensor', quantity: 2, cost: 8000 },
      { item: 'Thermal Relay', quantity: 1, cost: 12000 }
    ],
    tasks: [
      { id: 1, description: 'Temperature sensor diagnostics', completed: false, completedBy: null, completedAt: null },
      { id: 2, description: 'Cooling fan inspection', completed: false, completedBy: null, completedAt: null },
      { id: 3, description: 'Thermal protection testing', completed: false, completedBy: null, completedAt: null },
      { id: 4, description: 'Component replacement', completed: false, completedBy: null, completedAt: null },
      { id: 5, description: 'System calibration', completed: false, completedBy: null, completedAt: null }
    ],
    notes: [],
    attachments: []
  },
  {
    id: 'WO-2024-003',
    title: 'Emergency Communication System Repair',
    transformerId: 'T-AM-023',
    transformerName: 'Bahir Dar Distribution Transformer',
    location: 'Bahir Dar Substation, Amhara',
    type: 'emergency',
    priority: 'critical',
    status: 'completed',
    assignedTo: 'Bob Johnson',
    assignedTeam: ['Bob Johnson', 'Mike Wilson', 'Sarah Davis'],
    createdDate: '2024-02-08',
    scheduledDate: '2024-02-09',
    startedDate: '2024-02-09T06:00:00Z',
    estimatedCompletion: '2024-02-10T22:00:00Z',
    actualCompletion: '2024-02-10T20:30:00Z',
    description: 'Emergency repair of communication system failure and control panel replacement.',
    estimatedHours: 16,
    actualHours: 14.5,
    estimatedCost: 78000,
    actualCost: 72000,
    materials: [
      { item: 'Control Panel', quantity: 1, cost: 35000 },
      { item: 'Communication Module', quantity: 1, cost: 25000 },
      { item: 'Wiring Harness', quantity: 1, cost: 12000 }
    ],
    tasks: [
      { id: 1, description: 'System diagnostics', completed: true, completedBy: 'Bob Johnson', completedAt: '2024-02-09T08:00:00Z' },
      { id: 2, description: 'Control panel replacement', completed: true, completedBy: 'Mike Wilson', completedAt: '2024-02-09T14:00:00Z' },
      { id: 3, description: 'Communication system setup', completed: true, completedBy: 'Sarah Davis', completedAt: '2024-02-10T10:00:00Z' },
      { id: 4, description: 'Full system testing', completed: true, completedBy: 'Bob Johnson', completedAt: '2024-02-10T18:00:00Z' },
      { id: 5, description: 'Documentation update', completed: true, completedBy: 'Sarah Davis', completedAt: '2024-02-10T20:00:00Z' }
    ],
    notes: [
      { id: 1, author: 'Bob Johnson', timestamp: '2024-02-09T08:30:00Z', content: 'Communication module completely failed. Requires full replacement.' },
      { id: 2, author: 'Mike Wilson', timestamp: '2024-02-09T14:30:00Z', content: 'Control panel installation completed. Testing connectivity.' },
      { id: 3, author: 'Sarah Davis', timestamp: '2024-02-10T20:15:00Z', content: 'All systems operational. Documentation updated in system.' }
    ],
    attachments: [
      { id: 1, name: 'diagnostic_report.pdf', type: 'pdf', size: '1.8 MB', uploadedBy: 'Bob Johnson' },
      { id: 2, name: 'installation_photos.zip', type: 'zip', size: '22.3 MB', uploadedBy: 'Mike Wilson' },
      { id: 3, name: 'test_results.xlsx', type: 'xlsx', size: '456 KB', uploadedBy: 'Sarah Davis' }
    ]
  }
]

const workOrderStats = {
  totalOrders: 247,
  pending: 45,
  inProgress: 23,
  completed: 179,
  avgCompletionTime: 7.2,
  onTimeCompletion: 89.5,
  totalCost: 4200000,
  avgCostPerOrder: 17000
}

const statusColors = {
  pending: 'bg-gray-100 text-gray-800 border-gray-200',
  in_progress: 'bg-blue-100 text-blue-800 border-blue-200',
  completed: 'bg-green-100 text-green-800 border-green-200',
  cancelled: 'bg-red-100 text-red-800 border-red-200',
  on_hold: 'bg-yellow-100 text-yellow-800 border-yellow-200'
}

const priorityColors = {
  low: 'bg-gray-100 text-gray-800 border-gray-200',
  medium: 'bg-blue-100 text-blue-800 border-blue-200',
  high: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  critical: 'bg-red-100 text-red-800 border-red-200'
}

const typeColors = {
  preventive: 'bg-green-100 text-green-800 border-green-200',
  corrective: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  emergency: 'bg-red-100 text-red-800 border-red-200',
  inspection: 'bg-blue-100 text-blue-800 border-blue-200'
}

export default function TransformerWorkOrdersPage() {
  const [workOrders, setWorkOrders] = useState(mockWorkOrders)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedPriority, setSelectedPriority] = useState('all')
  const [selectedType, setSelectedType] = useState('all')

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  const filteredOrders = workOrders.filter(order => {
    const matchesSearch = order.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.transformerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.assignedTo.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || order.status === selectedStatus
    const matchesPriority = selectedPriority === 'all' || order.priority === selectedPriority
    const matchesType = selectedType === 'all' || order.type === selectedType

    return matchesSearch && matchesStatus && matchesPriority && matchesType
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading work orders...</p>
        </div>
      </div>
    )
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_maintenance_manager",
        "regional_admin",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician"
      ]}
      requiredPermissions={[{ resource: "transformers", action: "read" }]}
    >
          <div className="space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Work Orders</h1>
                <p className="text-muted-foreground">
                  Comprehensive work order management for Ethiopian Electric Utility transformer operations
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Orders
                </Button>
                <Button variant="outline">
                  <Upload className="h-4 w-4 mr-2" />
                  Import Data
                </Button>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Work Order
                </Button>
              </div>
            </div>

            {/* Work Order Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{workOrderStats.totalOrders}</div>
                  <p className="text-xs text-muted-foreground">
                    {workOrderStats.completed} completed
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">In Progress</CardTitle>
                  <Activity className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">{workOrderStats.inProgress}</div>
                  <p className="text-xs text-muted-foreground">
                    Currently active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Completion</CardTitle>
                  <Clock className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{workOrderStats.avgCompletionTime}h</div>
                  <p className="text-xs text-muted-foreground">
                    Average duration
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">On-time Rate</CardTitle>
                  <Target className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">{workOrderStats.onTimeCompletion}%</div>
                  <p className="text-xs text-muted-foreground">
                    Performance metric
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Main Work Orders Interface */}
            <Tabs defaultValue="orders" className="space-y-4">
              <TabsList>
                <TabsTrigger value="orders">Work Orders</TabsTrigger>
                <TabsTrigger value="tracking">Progress Tracking</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
                <TabsTrigger value="resources">Resource Management</TabsTrigger>
              </TabsList>

              <TabsContent value="orders" className="space-y-4">
                {/* Filters */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-4">
                      <div className="flex-1">
                        <Input
                          placeholder="Search work orders..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="max-w-sm"
                        />
                      </div>
                      <select
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="on_hold">On Hold</option>
                      </select>
                      <select
                        value={selectedPriority}
                        onChange={(e) => setSelectedPriority(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Priority</option>
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="critical">Critical</option>
                      </select>
                      <select
                        value={selectedType}
                        onChange={(e) => setSelectedType(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Types</option>
                        <option value="preventive">Preventive</option>
                        <option value="corrective">Corrective</option>
                        <option value="emergency">Emergency</option>
                        <option value="inspection">Inspection</option>
                      </select>
                    </div>
                  </CardContent>
                </Card>

                {/* Work Orders List */}
                <div className="space-y-4">
                  {filteredOrders.map((order) => (
                    <Card key={order.id} className="hover:shadow-lg transition-shadow">
                      <CardContent className="pt-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold text-lg">{order.title}</h3>
                              <Badge className={statusColors[order.status as keyof typeof statusColors]}>
                                {order.status.replace('_', ' ')}
                              </Badge>
                              <Badge className={priorityColors[order.priority as keyof typeof priorityColors]}>
                                {order.priority}
                              </Badge>
                              <Badge className={typeColors[order.type as keyof typeof typeColors]}>
                                {order.type}
                              </Badge>
                            </div>

                            <div className="flex items-center gap-2 mb-3">
                              <span className="text-sm font-medium text-blue-600">{order.id}</span>
                              <span className="text-sm text-muted-foreground">•</span>
                              <span className="text-sm font-medium">{order.transformerName}</span>
                            </div>

                            <p className="text-muted-foreground mb-4">{order.description}</p>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                <span>{order.location}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                <span>{order.assignedTo}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>Due: {new Date(order.scheduledDate).toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>{order.estimatedHours}h estimated</span>
                              </div>
                            </div>

                            {/* Progress Bar */}
                            <div className="mb-4">
                              <div className="flex justify-between text-xs mb-1">
                                <span>Progress</span>
                                <span>{Math.round((order.tasks.filter(task => task.completed).length / order.tasks.length) * 100)}%</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${
                                    order.status === 'completed' ? 'bg-green-500' :
                                    order.status === 'in_progress' ? 'bg-blue-500' :
                                    'bg-gray-400'
                                  }`}
                                  style={{ width: `${(order.tasks.filter(task => task.completed).length / order.tasks.length) * 100}%` }}
                                ></div>
                              </div>
                            </div>

                            {/* Team and Cost */}
                            <div className="flex items-center gap-6 text-sm">
                              <div className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                <span>Team: {order.assignedTeam.length} members</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Package className="h-3 w-3" />
                                <span>Cost: {order.estimatedCost.toLocaleString()} ETB</span>
                              </div>
                              {order.actualCost > 0 && (
                                <div className="flex items-center gap-1">
                                  <TrendingUp className="h-3 w-3" />
                                  <span>Actual: {order.actualCost.toLocaleString()} ETB</span>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-2 ml-4">
                            <Button size="sm" variant="outline">
                              <Eye className="h-3 w-3 mr-1" />
                              View
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            {order.status === 'pending' && (
                              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                                <Play className="h-3 w-3 mr-1" />
                                Start
                              </Button>
                            )}
                            {order.status === 'in_progress' && (
                              <Button size="sm" className="bg-green-600 hover:bg-green-700">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Complete
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="tracking" className="space-y-4">
                {/* Active Work Orders Tracking */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {filteredOrders.filter(order => order.status === 'in_progress').map((order) => (
                    <Card key={order.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{order.id}</CardTitle>
                          <Badge className={priorityColors[order.priority as keyof typeof priorityColors]}>
                            {order.priority}
                          </Badge>
                        </div>
                        <CardDescription>{order.transformerName}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {/* Task Checklist */}
                          <div>
                            <h4 className="font-medium mb-2">Task Progress</h4>
                            <div className="space-y-2">
                              {order.tasks.map((task) => (
                                <div key={task.id} className="flex items-center gap-2">
                                  <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                                    task.completed ? 'bg-green-500 border-green-500' : 'border-gray-300'
                                  }`}>
                                    {task.completed && <CheckCircle className="h-3 w-3 text-white" />}
                                  </div>
                                  <span className={`text-sm ${task.completed ? 'line-through text-muted-foreground' : ''}`}>
                                    {task.description}
                                  </span>
                                  {task.completed && (
                                    <span className="text-xs text-muted-foreground">
                                      by {task.completedBy}
                                    </span>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Time Tracking */}
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium">Estimated:</span>
                              <div>{order.estimatedHours}h</div>
                            </div>
                            <div>
                              <span className="font-medium">Actual:</span>
                              <div>{order.actualHours}h</div>
                            </div>
                          </div>

                          {/* Recent Notes */}
                          {order.notes.length > 0 && (
                            <div>
                              <h4 className="font-medium mb-2">Recent Notes</h4>
                              <div className="space-y-2">
                                {order.notes.slice(-2).map((note) => (
                                  <div key={note.id} className="p-2 bg-gray-50 rounded text-sm">
                                    <div className="font-medium">{note.author}</div>
                                    <div className="text-muted-foreground">{note.content}</div>
                                    <div className="text-xs text-muted-foreground mt-1">
                                      {new Date(note.timestamp).toLocaleString()}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Overall Progress Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>Overall Progress Summary</CardTitle>
                    <CardDescription>Real-time tracking of all active work orders</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{workOrderStats.inProgress}</div>
                        <p className="text-sm text-muted-foreground">Active Orders</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {Math.round((workOrderStats.completed / workOrderStats.totalOrders) * 100)}%
                        </div>
                        <p className="text-sm text-muted-foreground">Completion Rate</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">{workOrderStats.avgCompletionTime}h</div>
                        <p className="text-sm text-muted-foreground">Avg Duration</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {(workOrderStats.totalCost / 1000000).toFixed(1)}M ETB
                        </div>
                        <p className="text-sm text-muted-foreground">Total Cost</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="analytics" className="space-y-4">
                {/* Work Order Analytics */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Work Order Status Distribution</CardTitle>
                      <CardDescription>Current status breakdown of all work orders</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsPieChart>
                          <Pie
                            data={[
                              { name: 'Completed', value: workOrderStats.completed, fill: '#10b981' },
                              { name: 'Pending', value: workOrderStats.pending, fill: '#6b7280' },
                              { name: 'In Progress', value: workOrderStats.inProgress, fill: '#3b82f6' },
                              { name: 'On Hold', value: 8, fill: '#f59e0b' }
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            dataKey="value"
                          />
                          <Tooltip />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Work Order Types</CardTitle>
                      <CardDescription>Distribution by maintenance type</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={[
                          { type: 'Preventive', count: 156, cost: 2800000 },
                          { type: 'Corrective', count: 67, cost: 1200000 },
                          { type: 'Emergency', count: 18, cost: 800000 },
                          { type: 'Inspection', count: 34, cost: 400000 }
                        ]}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="type" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="count" fill="#3b82f6" name="Count" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Monthly Work Order Trends</CardTitle>
                      <CardDescription>Work order volume over the past 12 months</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsLineChart data={Array.from({ length: 12 }, (_, i) => ({
                          month: new Date(2023, i).toLocaleDateString('en', { month: 'short' }),
                          created: Math.floor(Math.random() * 30) + 15,
                          completed: Math.floor(Math.random() * 25) + 12,
                          onTime: Math.floor(Math.random() * 20) + 10
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="created" stroke="#3b82f6" name="Created" />
                          <Line type="monotone" dataKey="completed" stroke="#10b981" name="Completed" />
                          <Line type="monotone" dataKey="onTime" stroke="#8b5cf6" name="On Time" />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Cost Analysis</CardTitle>
                      <CardDescription>Work order costs by type and month</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={Array.from({ length: 6 }, (_, i) => ({
                          month: new Date(2024, i).toLocaleDateString('en', { month: 'short' }),
                          preventive: Math.floor(Math.random() * 500000) + 300000,
                          corrective: Math.floor(Math.random() * 300000) + 200000,
                          emergency: Math.floor(Math.random() * 200000) + 100000
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Area type="monotone" dataKey="preventive" stackId="1" stroke="#10b981" fill="#10b981" />
                          <Area type="monotone" dataKey="corrective" stackId="1" stroke="#f59e0b" fill="#f59e0b" />
                          <Area type="monotone" dataKey="emergency" stackId="1" stroke="#ef4444" fill="#ef4444" />
                        </AreaChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Performance Metrics */}
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics</CardTitle>
                    <CardDescription>Key performance indicators for work order management</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{workOrderStats.onTimeCompletion}%</div>
                        <p className="text-sm text-muted-foreground">On-time Completion</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{workOrderStats.avgCompletionTime}h</div>
                        <p className="text-sm text-muted-foreground">Avg Completion Time</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">94.2%</div>
                        <p className="text-sm text-muted-foreground">Quality Score</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">{workOrderStats.avgCostPerOrder.toLocaleString()}</div>
                        <p className="text-sm text-muted-foreground">Avg Cost (ETB)</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">2.3%</div>
                        <p className="text-sm text-muted-foreground">Rework Rate</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-cyan-600">96.8%</div>
                        <p className="text-sm text-muted-foreground">Customer Satisfaction</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="resources" className="space-y-4">
                {/* Resource Overview */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-blue-600">Active Technicians</p>
                          <p className="text-2xl font-bold text-blue-600">28</p>
                        </div>
                        <Users className="h-8 w-8 text-blue-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-green-600">Utilization Rate</p>
                          <p className="text-2xl font-bold text-green-600">82%</p>
                        </div>
                        <BarChart3 className="h-8 w-8 text-green-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-purple-600">Available Tools</p>
                          <p className="text-2xl font-bold text-purple-600">156</p>
                        </div>
                        <Package className="h-8 w-8 text-purple-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-orange-600">Fleet Vehicles</p>
                          <p className="text-2xl font-bold text-orange-600">45</p>
                        </div>
                        <Truck className="h-8 w-8 text-orange-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Technician Workload */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Technician Workload</CardTitle>
                      <CardDescription>Current work order assignments by technician</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={[
                          { name: 'John Doe', active: 4, completed: 12, efficiency: 95 },
                          { name: 'Jane Smith', active: 3, completed: 15, efficiency: 92 },
                          { name: 'Bob Johnson', active: 5, completed: 8, efficiency: 88 },
                          { name: 'Alice Brown', active: 2, completed: 18, efficiency: 98 },
                          { name: 'Mike Wilson', active: 3, completed: 14, efficiency: 90 }
                        ]}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="active" fill="#3b82f6" name="Active Orders" />
                          <Bar dataKey="completed" fill="#10b981" name="Completed This Month" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Resource Allocation</CardTitle>
                      <CardDescription>Distribution of resources across regions</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsPieChart>
                          <Pie
                            data={[
                              { name: 'Addis Ababa', value: 12, fill: '#3b82f6' },
                              { name: 'Oromia', value: 8, fill: '#10b981' },
                              { name: 'Amhara', value: 5, fill: '#f59e0b' },
                              { name: 'Tigray', value: 3, fill: '#ef4444' }
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, value }) => `${name}: ${value}`}
                            outerRadius={80}
                            dataKey="value"
                          />
                          <Tooltip />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Resource Management Table */}
                <Card>
                  <CardHeader>
                    <CardTitle>Resource Management</CardTitle>
                    <CardDescription>Detailed resource allocation and availability</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Technician Details */}
                      <div>
                        <h4 className="font-medium mb-3">Technician Performance</h4>
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="border-b">
                                <th className="text-left p-3">Technician</th>
                                <th className="text-left p-3">Region</th>
                                <th className="text-left p-3">Active Orders</th>
                                <th className="text-left p-3">Completed</th>
                                <th className="text-left p-3">Efficiency</th>
                                <th className="text-left p-3">Specialization</th>
                                <th className="text-left p-3">Status</th>
                              </tr>
                            </thead>
                            <tbody>
                              {[
                                { name: 'John Doe', region: 'Addis Ababa', active: 4, completed: 12, efficiency: 95, specialization: 'High Voltage', status: 'available' },
                                { name: 'Jane Smith', region: 'Oromia', active: 3, completed: 15, efficiency: 92, specialization: 'Distribution', status: 'busy' },
                                { name: 'Bob Johnson', region: 'Amhara', active: 5, completed: 8, efficiency: 88, specialization: 'Emergency Repair', status: 'busy' },
                                { name: 'Alice Brown', region: 'Addis Ababa', active: 2, completed: 18, efficiency: 98, specialization: 'Preventive Maint.', status: 'available' },
                                { name: 'Mike Wilson', region: 'Tigray', active: 3, completed: 14, efficiency: 90, specialization: 'Diagnostics', status: 'on_leave' }
                              ].map((tech, index) => (
                                <tr key={index} className="border-b hover:bg-gray-50">
                                  <td className="p-3 font-medium">{tech.name}</td>
                                  <td className="p-3">{tech.region}</td>
                                  <td className="p-3">{tech.active}</td>
                                  <td className="p-3">{tech.completed}</td>
                                  <td className="p-3">{tech.efficiency}%</td>
                                  <td className="p-3">{tech.specialization}</td>
                                  <td className="p-3">
                                    <Badge className={
                                      tech.status === 'available' ? 'bg-green-100 text-green-800' :
                                      tech.status === 'busy' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-red-100 text-red-800'
                                    }>
                                      {tech.status.replace('_', ' ')}
                                    </Badge>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>

                      {/* Resource Optimization */}
                      <div>
                        <h4 className="font-medium mb-3">Resource Optimization Recommendations</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-blue-50 rounded-lg">
                            <h5 className="font-medium text-blue-900 mb-2">Workload Balancing</h5>
                            <p className="text-sm text-blue-700 mb-3">
                              Redistribute 2 orders from Bob Johnson to available technicians in Amhara region.
                            </p>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-blue-600">Efficiency gain: +12%</span>
                              <Button size="sm" variant="outline">Optimize</Button>
                            </div>
                          </div>
                          <div className="p-4 bg-green-50 rounded-lg">
                            <h5 className="font-medium text-green-900 mb-2">Cross-training</h5>
                            <p className="text-sm text-green-700 mb-3">
                              Train 3 technicians in emergency repair procedures to improve response times.
                            </p>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-green-600">Response time: -25%</span>
                              <Button size="sm" variant="outline">Schedule</Button>
                            </div>
                          </div>
                          <div className="p-4 bg-purple-50 rounded-lg">
                            <h5 className="font-medium text-purple-900 mb-2">Equipment Allocation</h5>
                            <p className="text-sm text-purple-700 mb-3">
                              Relocate 2 diagnostic tools from Addis Ababa to Oromia region for better coverage.
                            </p>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-purple-600">Coverage: +18%</span>
                              <Button size="sm" variant="outline">Relocate</Button>
                            </div>
                          </div>
                          <div className="p-4 bg-orange-50 rounded-lg">
                            <h5 className="font-medium text-orange-900 mb-2">Fleet Optimization</h5>
                            <p className="text-sm text-orange-700 mb-3">
                              Schedule vehicle maintenance during low-demand periods to maximize availability.
                            </p>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-orange-600">Availability: +8%</span>
                              <Button size="sm" variant="outline">Schedule</Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
      </div>
    </MainLayout>
  )
}
