/**
 * API route to initialize the database
 * 
 * This route can be called to initialize the database with seed data.
 * It's useful for development and testing purposes.
 */

import { NextRequest, NextResponse } from 'next/server';
import { initializeDatabase } from '@/src/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get the force parameter from the query string
    const force = request.nextUrl.searchParams.get('force') === 'true';
    
    // Initialize the database
    await initializeDatabase(force);
    
    // Return success response
    return NextResponse.json({ 
      success: true, 
      message: 'Database initialized successfully',
      force
    });
  } catch (error) {
    console.error('Error initializing database:', error);
    
    // Return error response
    return NextResponse.json(
      { 
        success: false, 
        message: 'Error initializing database',
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
