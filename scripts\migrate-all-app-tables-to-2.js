// Automatically migrate all app_* tables to app_*2 tables if both exist and columns match
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

async function getTableColumns(connection, table) {
  const [cols] = await connection.execute(`SHOW COLUMNS FROM \\`${table}\\``);
  return cols.map(col => col.Field);
}

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    // Get all app_* and app_*2 tables
    const [tables] = await connection.execute("SHOW TABLES");
    const tableNames = tables.map(row => Object.values(row)[0]);
    const appTables = tableNames.filter(name => name.startsWith('app_') && !name.endsWith('2'));
    const app2Tables = tableNames.filter(name => name.startsWith('app_') && name.endsWith('2'));
    let migrated = 0;
    for (const from of appTables) {
      const to = from + '2';
      if (app2Tables.includes(to)) {
        // Get columns in both tables
        const fromCols = await getTableColumns(connection, from);
        const toCols = await getTableColumns(connection, to);
        // Only migrate columns that exist in both
        const commonCols = fromCols.filter(col => toCols.includes(col));
        if (commonCols.length === 0) continue;
        // Clear destination table
        await connection.execute(`DELETE FROM \\`${to}\\``);
        // Copy data
        const [rows] = await connection.execute(`SELECT ${commonCols.join(', ')} FROM \\`${from}\\``);
        for (const row of rows) {
          const values = commonCols.map(col => row[col]);
          const placeholders = commonCols.map(() => '?').join(', ');
          await connection.execute(`INSERT INTO \\`${to}\\` (${commonCols.join(', ')}) VALUES (${placeholders})`, values);
        }
        console.log(`✅ Migrated ${rows.length} rows from ${from} to ${to}`);
        migrated++;
      }
    }
    if (migrated === 0) {
      console.log('No matching app_* and app_*2 tables found for migration.');
    }
  } catch (err) {
    console.error('❌ Error during automatic migration:', err);
  } finally {
    await connection.end();
  }
})();
