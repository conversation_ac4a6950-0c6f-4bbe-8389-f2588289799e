const mysql = require('mysql2/promise');

async function checkDatabase() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'dtms_eeu_db'
  });

  try {
    console.log('🔍 Checking database structure...');

    // Show all tables
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('\n📋 Available tables:');
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });

    // Check app_transformers table
    const [appTransformers] = await connection.execute("SHOW TABLES LIKE 'app_transformers'");
    if (appTransformers.length > 0) {
      console.log('\n🔧 app_transformers table structure:');
      const [columns] = await connection.execute('DESCRIBE app_transformers');
      columns.forEach(col => {
        console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(required)'}`);
      });

      // Check current data count
      const [count] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
      console.log(`\n📊 Current transformer count: ${count[0].count}`);

      // Show sample data if any exists
      if (count[0].count > 0) {
        const [sample] = await connection.execute('SELECT * FROM app_transformers LIMIT 3');
        console.log('\n📄 Sample data:');
        sample.forEach((row, index) => {
          console.log(`  Record ${index + 1}:`);
          console.log(`    ID: ${row.id}`);
          console.log(`    Serial: ${row.serial_number}`);
          console.log(`    Name: ${row.name}`);
          console.log(`    Status: ${row.status}`);
          console.log(`    Manufacturer: ${row.manufacturer}`);
          console.log(`    Capacity: ${row.capacity}`);
        });
      }
    } else {
      console.log('\n❌ app_transformers table not found');
    }

    // Check regions
    const [regions] = await connection.execute("SHOW TABLES LIKE 'app_regions'");
    if (regions.length > 0) {
      const [regionData] = await connection.execute('SELECT id, name FROM app_regions LIMIT 10');
      console.log('\n🌍 Available regions:');
      regionData.forEach(region => {
        console.log(`  ID: ${region.id}, Name: ${region.name}`);
      });
    }

    // Check service centers
    const [serviceCenters] = await connection.execute("SHOW TABLES LIKE 'app_service_centers'");
    if (serviceCenters.length > 0) {
      const [centerData] = await connection.execute('SELECT id, name FROM app_service_centers LIMIT 10');
      console.log('\n🏢 Available service centers:');
      centerData.forEach(center => {
        console.log(`  ID: ${center.id}, Name: ${center.name}`);
      });
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await connection.end();
  }
}

checkDatabase();
