"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Checkbox } from "@/src/components/ui/checkbox"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@/src/components/ui/tabs"
import { ScrollArea } from "@/src/components/ui/scroll-area"
import {
  Shield, FileText, Settings, Users, Wrench,
  BarChart, Database, AlertTriangle
} from "lucide-react"
import { User, UserPermission } from "@/src/types/user-management"
import { useToast } from "@/src/components/ui/use-toast"

interface UserPermissionsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: User | null
  onSave: (userId: string, permissions: UserPermission[]) => void
}

export function UserPermissionsDialog({
  open,
  onOpen<PERSON>hange,
  user,
  onSave
}: UserPermissionsDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("users")
  const [permissions, setPermissions] = useState<UserPermission[]>(user?.permissions || [])

  // Reset permissions when dialog opens or user changes
  useState(() => {
    if (open && user) {
      setPermissions(user.permissions || [])
    }
  })

  // Check if a permission exists
  const hasPermission = (resource: string, action: string) => {
    return permissions.some(p => p.resource === resource && p.action === action)
  }

  // Toggle a permission
  const togglePermission = (resource: string, action: string) => {
    setPermissions(prev => {
      const exists = prev.some(p => p.resource === resource && p.action === action)

      if (exists) {
        return prev.filter(p => !(p.resource === resource && p.action === action))
      } else {
        return [...prev, { resource, action }]
      }
    })
  }

  // Handle save
  const handleSave = () => {
    if (!user) return

    onSave(user.id, permissions)
    onOpenChange(false)

    toast({
      title: "Permissions Updated",
      description: `Permissions for ${user.name} have been updated successfully.`
    })
  }

  if (!user) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Shield className="mr-2 h-5 w-5" />
            User Permissions
          </DialogTitle>
          <DialogDescription>
            Manage permissions for {user.name} ({user.role})
          </DialogDescription>
        </DialogHeader>

        {user.role === "Administrator" ? (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <Shield className="h-12 w-12 text-primary mb-4" />
            <h3 className="text-lg font-medium">Administrator Access</h3>
            <p className="text-muted-foreground mt-2 max-w-md">
              This user has administrator privileges and has full access to all system features.
              Individual permissions cannot be modified for administrators.
            </p>
          </div>
        ) : (
          <>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="users" className="flex items-center">
                  <Users className="mr-2 h-4 w-4" />
                  Users
                </TabsTrigger>
                <TabsTrigger value="assets" className="flex items-center">
                  <Wrench className="mr-2 h-4 w-4" />
                  Assets
                </TabsTrigger>
                <TabsTrigger value="reports" className="flex items-center">
                  <BarChart className="mr-2 h-4 w-4" />
                  Reports
                </TabsTrigger>
                <TabsTrigger value="system" className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  System
                </TabsTrigger>
              </TabsList>

              <ScrollArea className="h-[300px] rounded-md border p-4">
                <TabsContent value="users" className="space-y-4 mt-0">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Users className="mr-2 h-5 w-5 text-blue-500" />
                        <div>
                          <h4 className="font-medium">User Management</h4>
                          <p className="text-sm text-muted-foreground">Control access to user-related features</p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 pl-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="users-view"
                          checked={hasPermission("users", "read")}
                          onCheckedChange={() => togglePermission("users", "read")}
                        />
                        <label
                          htmlFor="users-view"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          View Users
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="users-create"
                          checked={hasPermission("users", "create")}
                          onCheckedChange={() => togglePermission("users", "create")}
                        />
                        <label
                          htmlFor="users-create"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Create Users
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="users-edit"
                          checked={hasPermission("users", "update")}
                          onCheckedChange={() => togglePermission("users", "update")}
                        />
                        <label
                          htmlFor="users-edit"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Edit Users
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="users-delete"
                          checked={hasPermission("users", "delete")}
                          onCheckedChange={() => togglePermission("users", "delete")}
                        />
                        <label
                          htmlFor="users-delete"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Delete Users
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="users-assign"
                          checked={hasPermission("users", "assign")}
                          onCheckedChange={() => togglePermission("users", "assign")}
                        />
                        <label
                          htmlFor="users-assign"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Assign Users
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="users-permissions"
                          checked={hasPermission("users", "admin")}
                          onCheckedChange={() => togglePermission("users", "admin")}
                        />
                        <label
                          htmlFor="users-permissions"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Manage Permissions
                        </label>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="assets" className="space-y-4 mt-0">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Wrench className="mr-2 h-5 w-5 text-green-500" />
                        <div>
                          <h4 className="font-medium">Asset Management</h4>
                          <p className="text-sm text-muted-foreground">Control access to transformer and equipment features</p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 pl-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="assets-view"
                          checked={hasPermission("assets", "read")}
                          onCheckedChange={() => togglePermission("assets", "read")}
                        />
                        <label
                          htmlFor="assets-view"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          View Assets
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="assets-create"
                          checked={hasPermission("assets", "create")}
                          onCheckedChange={() => togglePermission("assets", "create")}
                        />
                        <label
                          htmlFor="assets-create"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Create Assets
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="assets-edit"
                          checked={hasPermission("assets", "update")}
                          onCheckedChange={() => togglePermission("assets", "update")}
                        />
                        <label
                          htmlFor="assets-edit"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Edit Assets
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="assets-delete"
                          checked={hasPermission("assets", "delete")}
                          onCheckedChange={() => togglePermission("assets", "delete")}
                        />
                        <label
                          htmlFor="assets-delete"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Delete Assets
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="maintenance-view"
                          checked={hasPermission("maintenance", "read")}
                          onCheckedChange={() => togglePermission("maintenance", "read")}
                        />
                        <label
                          htmlFor="maintenance-view"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          View Maintenance
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="maintenance-create"
                          checked={hasPermission("maintenance", "create")}
                          onCheckedChange={() => togglePermission("maintenance", "create")}
                        />
                        <label
                          htmlFor="maintenance-create"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Create Maintenance
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="maintenance-approve"
                          checked={hasPermission("maintenance", "approve")}
                          onCheckedChange={() => togglePermission("maintenance", "approve")}
                        />
                        <label
                          htmlFor="maintenance-approve"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Approve Maintenance
                        </label>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="reports" className="space-y-4 mt-0">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <BarChart className="mr-2 h-5 w-5 text-amber-500" />
                        <div>
                          <h4 className="font-medium">Reports & Analytics</h4>
                          <p className="text-sm text-muted-foreground">Control access to reporting features</p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 pl-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="reports-view"
                          checked={hasPermission("reports", "read")}
                          onCheckedChange={() => togglePermission("reports", "read")}
                        />
                        <label
                          htmlFor="reports-view"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          View Reports
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="reports-create"
                          checked={hasPermission("reports", "create")}
                          onCheckedChange={() => togglePermission("reports", "create")}
                        />
                        <label
                          htmlFor="reports-create"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Create Reports
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="reports-export"
                          checked={hasPermission("reports", "export")}
                          onCheckedChange={() => togglePermission("reports", "export")}
                        />
                        <label
                          htmlFor="reports-export"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Export Reports
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="analytics-view"
                          checked={hasPermission("analytics", "read")}
                          onCheckedChange={() => togglePermission("analytics", "read")}
                        />
                        <label
                          htmlFor="analytics-view"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          View Analytics
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="dashboard-view"
                          checked={hasPermission("dashboard", "read")}
                          onCheckedChange={() => togglePermission("dashboard", "read")}
                        />
                        <label
                          htmlFor="dashboard-view"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          View Dashboard
                        </label>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="system" className="space-y-4 mt-0">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Settings className="mr-2 h-5 w-5 text-purple-500" />
                        <div>
                          <h4 className="font-medium">System Settings</h4>
                          <p className="text-sm text-muted-foreground">Control access to system configuration</p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 pl-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="settings-view"
                          checked={hasPermission("settings", "read")}
                          onCheckedChange={() => togglePermission("settings", "read")}
                        />
                        <label
                          htmlFor="settings-view"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          View Settings
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="settings-edit"
                          checked={hasPermission("settings", "update")}
                          onCheckedChange={() => togglePermission("settings", "update")}
                        />
                        <label
                          htmlFor="settings-edit"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Edit Settings
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="logs-view"
                          checked={hasPermission("logs", "read")}
                          onCheckedChange={() => togglePermission("logs", "read")}
                        />
                        <label
                          htmlFor="logs-view"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          View Logs
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="backup-manage"
                          checked={hasPermission("backup", "admin")}
                          onCheckedChange={() => togglePermission("backup", "admin")}
                        />
                        <label
                          htmlFor="backup-manage"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          Manage Backups
                        </label>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </ScrollArea>
            </Tabs>

            <div className="flex items-center mt-2 text-sm text-muted-foreground">
              <AlertTriangle className="h-4 w-4 mr-2" />
              <p>Changes to permissions will take effect immediately after saving.</p>
            </div>
          </>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          {user.role !== "Administrator" && (
            <Button onClick={handleSave}>
              Save Permissions
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
