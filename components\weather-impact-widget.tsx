"use client"

import type React from "react"

import { useState } from "react"
import { Cloud, CloudLightning, CloudRain, CloudSnow, Sun, Thermometer, Wind, RefreshCw } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Progress } from "@/src/components/ui/progress"
import { Button } from "@/src/components/ui/button"
import { useWeatherData } from "@/src/hooks/use-weather-data"
import { analyzeWeatherImpact, analyzeWeatherForecast, calculateHistoricalImpact } from "@/src/utils/weather-analysis"
import { Skeleton } from "@/src/components/ui/skeleton"

export function WeatherImpactWidget() {
  const [region, setRegion] = useState("addis-ababa")
  const { weatherData, isLoading, error, refreshData } = useWeatherData(region)

  // Analyze the weather data
  const impacts = analyzeWeatherImpact(weatherData)
  const forecasts = analyzeWeatherForecast(weatherData)
  const historicalImpacts = calculateHistoricalImpact(weatherData)

  // Get weather card data from current weather
  const getWeatherCardData = () => {
    if (!weatherData || !weatherData.current) {
      return [
        {
          title: "Temperature",
          value: "--",
          description: "Loading data...",
          icon: Thermometer,
          impact: 0,
          impactText: "--",
          impactColor: "bg-gray-300",
        },
        {
          title: "Rainfall",
          value: "--",
          description: "Loading data...",
          icon: CloudRain,
          impact: 0,
          impactText: "--",
          impactColor: "bg-gray-300",
        },
        {
          title: "Wind Speed",
          value: "--",
          description: "Loading data...",
          icon: Wind,
          impact: 0,
          impactText: "--",
          impactColor: "bg-gray-300",
        },
        {
          title: "Conditions",
          value: "--",
          description: "Loading data...",
          icon: Cloud,
          impact: 0,
          impactText: "--",
          impactColor: "bg-gray-300",
        },
      ]
    }

    const current = weatherData.current

    // Temperature card
    const tempCard = {
      title: "Temperature",
      value: `${current.main.temp.toFixed(1)}°C`,
      description: current.main.temp > 30 ? "High temperature alert" : "Normal temperature",
      icon: Thermometer,
      impact: current.main.temp > 40 ? 90 : current.main.temp > 35 ? 75 : current.main.temp > 30 ? 50 : 25,
      impactText: current.main.temp > 35 ? "High impact" : current.main.temp > 30 ? "Moderate impact" : "Low impact",
      impactColor: current.main.temp > 35 ? "bg-orange-500" : current.main.temp > 30 ? "bg-yellow-500" : "bg-green-500",
    }

    // Rainfall card
    const rainAmount = current.rain ? current.rain["1h"] || (current.rain["3h"] ? current.rain["3h"] / 3 : 0) : 0
    const rainCard = {
      title: "Rainfall",
      value: `${rainAmount.toFixed(1)}mm`,
      description:
        rainAmount > 10 ? "Heavy rainfall warning" : rainAmount > 2.5 ? "Moderate rainfall" : "Light or no rainfall",
      icon: CloudRain,
      impact: rainAmount > 25 ? 90 : rainAmount > 10 ? 75 : rainAmount > 2.5 ? 50 : 10,
      impactText: rainAmount > 10 ? "High impact" : rainAmount > 2.5 ? "Moderate impact" : "Low impact",
      impactColor: rainAmount > 10 ? "bg-red-500" : rainAmount > 2.5 ? "bg-yellow-500" : "bg-green-500",
    }

    // Wind card
    const windSpeed = current.wind.speed * 3.6 // Convert m/s to km/h
    const windCard = {
      title: "Wind Speed",
      value: `${windSpeed.toFixed(1)}km/h`,
      description: windSpeed > 50 ? "Strong winds" : windSpeed > 20 ? "Moderate winds" : "Light winds",
      icon: Wind,
      impact: windSpeed > 50 ? 80 : windSpeed > 30 ? 60 : windSpeed > 20 ? 40 : 20,
      impactText: windSpeed > 50 ? "High impact" : windSpeed > 30 ? "Moderate impact" : "Low impact",
      impactColor: windSpeed > 50 ? "bg-orange-500" : windSpeed > 30 ? "bg-yellow-500" : "bg-green-500",
    }

    // Weather conditions card
    const weatherCondition = current.weather[0]
    const isThunderstorm = weatherCondition.id >= 200 && weatherCondition.id < 300
    const isRain = weatherCondition.id >= 300 && weatherCondition.id < 600
    const isSnow = weatherCondition.id >= 600 && weatherCondition.id < 700

    let conditionIcon = Cloud
    if (isThunderstorm) conditionIcon = CloudLightning
    else if (isRain) conditionIcon = CloudRain
    else if (isSnow) conditionIcon = CloudSnow
    else if (weatherCondition.id === 800) conditionIcon = Sun

    const conditionImpact = isThunderstorm ? 90 : isRain ? 70 : isSnow ? 60 : 30

    const conditionCard = {
      title: "Conditions",
      value: weatherCondition.main,
      description: weatherCondition.description,
      icon: conditionIcon,
      impact: conditionImpact,
      impactText: isThunderstorm
        ? "Critical impact"
        : isRain
          ? "High impact"
          : isSnow
            ? "Moderate impact"
            : "Low impact",
      impactColor: isThunderstorm ? "bg-red-600" : isRain ? "bg-red-500" : isSnow ? "bg-yellow-500" : "bg-green-500",
    }

    return [tempCard, rainCard, windCard, conditionCard]
  }

  const weatherCards = getWeatherCardData()

  // Get the icon component based on the icon name
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case "cloud-rain":
        return CloudRain
      case "cloud-lightning":
        return CloudLightning
      case "cloud-snow":
        return CloudSnow
      case "cloud-drizzle":
        return CloudRain
      case "wind":
        return Wind
      case "thermometer":
        return Thermometer
      case "sun":
        return Sun
      default:
        return Cloud
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Weather Impact Analysis</h3>
        <div className="flex items-center gap-2">
          <Select value={region} onValueChange={setRegion}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select region" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="addis-ababa">Addis Ababa</SelectItem>
              <SelectItem value="oromia">Oromia</SelectItem>
              <SelectItem value="amhara">Amhara</SelectItem>
              <SelectItem value="tigray">Tigray</SelectItem>
              <SelectItem value="afar">Afar</SelectItem>
              <SelectItem value="somali">Somali</SelectItem>
              <SelectItem value="snnpr">Southern Nations (SNNPR)</SelectItem>
              <SelectItem value="benishangul-gumuz">Benishangul-Gumuz</SelectItem>
              <SelectItem value="gambela">Gambela</SelectItem>
              <SelectItem value="harari">Harari</SelectItem>
              <SelectItem value="dire-dawa">Dire Dawa</SelectItem>
              <SelectItem value="sidama">Sidama</SelectItem>
              <SelectItem value="south-west">South West Ethiopia</SelectItem>

              {/* Major cities and areas */}
              <SelectItem value="bahir-dar">Bahir Dar</SelectItem>
              <SelectItem value="hawassa">Hawassa</SelectItem>
              <SelectItem value="mekelle">Mekelle</SelectItem>
              <SelectItem value="jimma">Jimma</SelectItem>
              <SelectItem value="gondar">Gondar</SelectItem>
              <SelectItem value="dessie">Dessie</SelectItem>
              <SelectItem value="adama">Adama (Nazret)</SelectItem>
              <SelectItem value="bishoftu">Bishoftu (Debre Zeit)</SelectItem>
              <SelectItem value="debre-berhan">Debre Berhan</SelectItem>
              <SelectItem value="jijiga">Jijiga</SelectItem>
              <SelectItem value="nekemte">Nekemte</SelectItem>
              <SelectItem value="sodo">Sodo</SelectItem>
              <SelectItem value="arba-minch">Arba Minch</SelectItem>
              <SelectItem value="debre-markos">Debre Markos</SelectItem>
              <SelectItem value="harar">Harar</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={refreshData} disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
        </div>
      </div>

      <div className="flex flex-wrap gap-3 mt-2">
        <WeatherSeverityIndicator level="critical" label="Critical Risk" size="sm" />
        <WeatherSeverityIndicator level="high" label="High Risk" size="sm" />
        <WeatherSeverityIndicator level="moderate" label="Moderate Risk" size="sm" />
        <WeatherSeverityIndicator level="low" label="Low Risk" size="sm" />
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <p className="font-medium">Error loading weather data</p>
          <p className="text-sm">{error}</p>
          <p className="text-sm mt-1">Using simulated data instead.</p>
        </div>
      )}

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {weatherCards.map((card, index) => (
          <WeatherCard
            key={index}
            title={card.title}
            value={card.value}
            description={card.description}
            icon={<card.icon className="h-5 w-5 text-teal-600" />}
            impact={card.impact}
            impactText={card.impactText}
            impactColor={card.impactColor}
            isLoading={isLoading}
          />
        ))}
      </div>

      <Tabs defaultValue="current" className="mt-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="current">Current</TabsTrigger>
          <TabsTrigger value="forecast">Forecast</TabsTrigger>
          <TabsTrigger value="historical">Historical</TabsTrigger>
        </TabsList>
        <TabsContent value="current" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Current Weather Impact</CardTitle>
              <CardDescription>
                Weather conditions affecting transformer network in{" "}
                {region
                  .split("-")
                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(" ")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <Skeleton className="h-4 w-48" />
                      </div>
                      <Skeleton className="h-4 w-24" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {impacts.map((impact, index) => {
                    const IconComponent = getIconComponent(impact.icon)
                    return (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <IconComponent className="h-5 w-5 text-teal-600" />
                          <span className="font-medium">
                            {impact.condition} affecting {impact.affectedTransformers} transformers
                          </span>
                        </div>
                        <WeatherSeverityIndicator level={impact.severity} />
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="forecast" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Weather Forecast Impact</CardTitle>
              <CardDescription>Predicted weather impact for the next 5 days</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <Skeleton className="h-4 w-48" />
                      </div>
                      <Skeleton className="h-4 w-24" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {forecasts.map((forecast, index) => {
                    const IconComponent = getIconComponent(forecast.icon)
                    return (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <IconComponent className="h-5 w-5 text-teal-600" />
                          <span className="font-medium">
                            {forecast.day}: {forecast.condition}
                          </span>
                        </div>
                        <WeatherSeverityIndicator level={forecast.severity} />
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="historical" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Historical Weather Impact</CardTitle>
              <CardDescription>Weather-related incidents in the past 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {historicalImpacts.map((impact, index) => {
                  const IconComponent = getIconComponent(impact.icon)
                  return (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <IconComponent className="h-5 w-5 text-teal-600" />
                        <span className="font-medium">
                          {impact.condition}-related incidents: {impact.incidents}
                        </span>
                      </div>
                      <span className="text-teal-600 font-medium">{impact.percentage}% of all incidents</span>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface WeatherCardProps {
  title: string
  value: string
  description: string
  icon: React.ReactNode
  impact: number
  impactText: string
  impactColor: string
  isLoading?: boolean
}

function WeatherCard({
  title,
  value,
  description,
  icon,
  impact,
  impactText,
  impactColor,
  isLoading = false,
}: WeatherCardProps) {
  // Determine severity level based on impact value
  let severityLevel: "critical" | "high" | "moderate" | "low" = "low"
  if (impact >= 90) severityLevel = "critical"
  else if (impact >= 70) severityLevel = "high"
  else if (impact >= 40) severityLevel = "moderate"

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center gap-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          {!isLoading && <WeatherSeverityIndicator level={severityLevel} showLabel={false} size="sm" />}
        </div>
        {icon}
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <>
            <Skeleton className="h-8 w-24 mb-1" />
            <Skeleton className="h-4 w-full" />
            <div className="mt-3">
              <div className="flex items-center justify-between mb-1">
                <Skeleton className="h-4 w-12" />
                <Skeleton className="h-4 w-24" />
              </div>
              <Skeleton className="h-2 w-full" />
            </div>
          </>
        ) : (
          <>
            <div className="text-2xl font-bold">{value}</div>
            <p className="text-xs text-muted-foreground">{description}</p>
            <div className="mt-3">
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs">Impact</span>
                <span className="text-xs font-medium">{impactText}</span>
              </div>
              <Progress value={impact} className={impactColor} />
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

interface WeatherSeverityIndicatorProps {
  level: "critical" | "high" | "moderate" | "low"
  label?: string
  showLabel?: boolean
  size?: "sm" | "md" | "lg"
}

function WeatherSeverityIndicator({ level, label, showLabel = true, size = "md" }: WeatherSeverityIndicatorProps) {
  const sizeClasses = {
    sm: "h-2 w-2",
    md: "h-3 w-3",
    lg: "h-4 w-4",
  }

  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base",
  }

  const colors = {
    critical: "bg-red-600",
    high: "bg-red-500",
    moderate: "bg-yellow-500",
    low: "bg-green-500",
  }

  const defaultLabels = {
    critical: "Critical",
    high: "High",
    moderate: "Moderate",
    low: "Low",
  }

  const displayLabel = label || defaultLabels[level]

  return (
    <div className="flex items-center gap-1.5">
      <div className={`rounded-full ${sizeClasses[size]} ${colors[level]}`}></div>
      {showLabel && <span className={`${textSizeClasses[size]} font-medium`}>{displayLabel}</span>}
    </div>
  )
}
