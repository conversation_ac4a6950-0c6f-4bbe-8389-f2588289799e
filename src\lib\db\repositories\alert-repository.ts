/**
 * Alert repository
 * 
 * This class provides specialized methods for working with alert entities.
 */

import { BaseRepository } from './base-repository';
import { Alert, AlertSeverity } from '../schema';

export class AlertRepository extends BaseRepository<Alert> {
  constructor() {
    super('alerts');
  }
  
  /**
   * Find alerts by transformer
   */
  findByTransformer(transformerId: string): Alert[] {
    return this.find({ transformerId });
  }
  
  /**
   * Find alerts by severity
   */
  findBySeverity(severity: AlertSeverity | AlertSeverity[]): Alert[] {
    if (Array.isArray(severity)) {
      return this.find({}).filter(alert => severity.includes(alert.severity));
    }
    return this.find({ severity });
  }
  
  /**
   * Find alerts by type
   */
  findByType(type: string): Alert[] {
    return this.find({ type });
  }
  
  /**
   * Find unread alerts
   */
  findUnread(): Alert[] {
    return this.find({ isRead: false });
  }
  
  /**
   * Find unresolved alerts
   */
  findUnresolved(): Alert[] {
    return this.find({ isResolved: false });
  }
  
  /**
   * Find resolved alerts
   */
  findResolved(): Alert[] {
    return this.find({ isResolved: true });
  }
  
  /**
   * Find alerts by date range
   */
  findByDateRange(startDate: Date, endDate: Date): Alert[] {
    return this.find({}).filter(alert => {
      const alertDate = new Date(alert.createdAt);
      return alertDate >= startDate && alertDate <= endDate;
    });
  }
  
  /**
   * Mark alert as read
   */
  markAsRead(id: string): Alert | null {
    return this.update(id, { isRead: true });
  }
  
  /**
   * Mark alert as resolved
   */
  markAsResolved(id: string, resolvedBy: string): Alert | null {
    return this.update(id, {
      isResolved: true,
      resolvedAt: new Date().toISOString(),
      resolvedBy
    });
  }
  
  /**
   * Get alert statistics
   */
  getStatistics() {
    const alerts = this.getAll();
    
    // Count by severity
    const severityCounts = alerts.reduce((counts, alert) => {
      counts[alert.severity] = (counts[alert.severity] || 0) + 1;
      return counts;
    }, {} as Record<AlertSeverity, number>);
    
    // Count by type
    const typeCounts = alerts.reduce((counts, alert) => {
      counts[alert.type] = (counts[alert.type] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);
    
    // Count read vs unread
    const readCount = alerts.filter(alert => alert.isRead).length;
    const unreadCount = alerts.length - readCount;
    
    // Count resolved vs unresolved
    const resolvedCount = alerts.filter(alert => alert.isResolved).length;
    const unresolvedCount = alerts.length - resolvedCount;
    
    return {
      total: alerts.length,
      read: readCount,
      unread: unreadCount,
      resolved: resolvedCount,
      unresolved: unresolvedCount,
      bySeverity: severityCounts,
      byType: typeCounts
    };
  }
}

// Export a singleton instance
export const alertRepository = new AlertRepository();
