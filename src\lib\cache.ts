/**
 * Simple in-memory cache implementation for development
 * In production, this could be replaced with Redis or other caching solutions
 */

interface CacheItem<T> {
  value: T
  expiry: number
}

class Cache {
  private cache = new Map<string, CacheItem<any>>()
  private defaultTTL = 5 * 60 * 1000 // 5 minutes

  set<T>(key: string, value: T, ttl?: number): void {
    const expiry = Date.now() + (ttl || this.defaultTTL)
    this.cache.set(key, { value, expiry })
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key)

    if (!item) {
      return null
    }

    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return null
    }

    return item.value
  }

  has(key: string): boolean {
    const item = this.cache.get(key)

    if (!item) {
      return false
    }

    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    // Clean expired items first
    this.cleanExpired()
    return this.cache.size
  }

  private cleanExpired(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key)
      }
    }
  }

  // Get all keys (for debugging)
  keys(): string[] {
    this.cleanExpired()
    return Array.from(this.cache.keys())
  }

  // Get cache statistics
  stats() {
    this.cleanExpired()
    return {
      size: this.cache.size,
      keys: this.keys(),
    }
  }
}

// Export singleton instance
export const cache = new Cache()

// Export transformer-specific cache instance
export const transformerCache = new Cache()

// Export class for testing
export { Cache }

// Helper functions
export const cacheKey = {
  transformers: (filters?: Record<string, any>) =>
    `transformers:${JSON.stringify(filters || {})}`,
  transformer: (id: string) => `transformer:${id}`,
  statistics: () => 'statistics',
  alerts: (filters?: Record<string, any>) =>
    `alerts:${JSON.stringify(filters || {})}`,
  users: (filters?: Record<string, any>) =>
    `users:${JSON.stringify(filters || {})}`,
  user: (id: string) => `user:${id}`,
  dashboard: (timeRange?: string) => `dashboard:${timeRange || '24h'}`,
}
