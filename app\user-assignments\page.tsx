import type { Metadata } from "next"
import { MainLayout } from "@/src/components/layout/main-layout"
import { UserAssignmentContent } from "@/components/user-assignment-content"

export const metadata: Metadata = {
  title: "User Assignments | EEU DTMS",
  description: "Assign users to regions and service centers",
}

export default function UserAssignmentsPage() {
  return (
    <MainLayout
      allowedRoles={["super_admin", "regional_admin", "service_center_manager"]}
    >
      <UserAssignmentContent />
    </MainLayout>
  )
}
