"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../../src/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../src/components/ui/card"
import { Badge } from "../../src/components/ui/badge"
import { Alert, AlertDescription } from "../../src/components/ui/alert"
import { 
  Database, 
  CheckCircle, 
  XCircle, 
  RefreshCw, 
  Trash2, 
  Settings,
  AlertTriangle,
  Info,
  Loader2
} from "lucide-react"

interface DatabaseStatus {
  exists: boolean
  message: string
  stats?: {
    regions: number
    transformers: number
  }
  missingTables?: string[]
}

interface SetupResult {
  success: boolean
  message: string
  stats?: {
    regions: number
    transformers: number
    alerts: number
    users: number
    maintenance: number
  }
  error?: any
}

export default function DatabaseSetupPage() {
  const [status, setStatus] = useState<DatabaseStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [setupResult, setSetupResult] = useState<SetupResult | null>(null)
  const [lastChecked, setLastChecked] = useState<Date | null>(null)

  const checkStatus = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/setup-database')
      const data = await response.json()
      
      if (data.success) {
        setStatus(data.status)
        setLastChecked(new Date())
      } else {
        setStatus({
          exists: false,
          message: data.error || 'Failed to check database status'
        })
      }
    } catch (error) {
      setStatus({
        exists: false,
        message: 'Failed to connect to database API'
      })
    } finally {
      setLoading(false)
    }
  }

  const performAction = async (action: 'setup' | 'reset') => {
    setLoading(true)
    setSetupResult(null)
    
    try {
      const response = await fetch('/api/setup-database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action })
      })
      
      const data = await response.json()
      setSetupResult(data)
      
      // Refresh status after action
      if (data.success) {
        setTimeout(() => {
          checkStatus()
        }, 1000)
      }
    } catch (error) {
      setSetupResult({
        success: false,
        message: 'Failed to perform database operation',
        error
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    checkStatus()
  }, [])

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
            <Database className="h-8 w-8" />
            Database Setup & Management
          </h1>
          <p className="text-muted-foreground">
            Ethiopian Electric Utility Transformer Management System
          </p>
        </div>

        {/* Database Status Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Database Status
              {lastChecked && (
                <span className="text-sm font-normal text-muted-foreground">
                  (Last checked: {lastChecked.toLocaleTimeString()})
                </span>
              )}
            </CardTitle>
            <CardDescription>
              Current status of the transformer management database
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loading && !status ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Checking database status...</span>
                </div>
              ) : status ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    {status.exists ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600" />
                    )}
                    <Badge variant={status.exists ? "default" : "destructive"}>
                      {status.exists ? "Database Ready" : "Database Not Ready"}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-muted-foreground">
                    {status.message}
                  </p>
                  
                  {status.stats && (
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {status.stats.regions}
                        </div>
                        <div className="text-sm text-muted-foreground">Regions</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {status.stats.transformers}
                        </div>
                        <div className="text-sm text-muted-foreground">Transformers</div>
                      </div>
                    </div>
                  )}
                  
                  {status.missingTables && status.missingTables.length > 0 && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        Missing tables: {status.missingTables.join(', ')}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">No status information available</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Actions Card */}
        <Card>
          <CardHeader>
            <CardTitle>Database Actions</CardTitle>
            <CardDescription>
              Manage your database setup and data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <Button
                onClick={checkStatus}
                disabled={loading}
                variant="outline"
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Refresh Status
              </Button>
              
              <Button
                onClick={() => performAction('setup')}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <Database className="h-4 w-4" />
                Setup Database
              </Button>
              
              <Button
                onClick={() => performAction('reset')}
                disabled={loading}
                variant="destructive"
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                Reset Database
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Setup Result */}
        {setupResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {setupResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                Operation Result
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Alert className={setupResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  {setupResult.message}
                </AlertDescription>
              </Alert>
              
              {setupResult.success && setupResult.stats && (
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mt-4">
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-xl font-bold text-blue-600">
                      {setupResult.stats.regions}
                    </div>
                    <div className="text-xs text-muted-foreground">Regions</div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-xl font-bold text-green-600">
                      {setupResult.stats.transformers}
                    </div>
                    <div className="text-xs text-muted-foreground">Transformers</div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-xl font-bold text-red-600">
                      {setupResult.stats.alerts}
                    </div>
                    <div className="text-xs text-muted-foreground">Alerts</div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-xl font-bold text-purple-600">
                      {setupResult.stats.users}
                    </div>
                    <div className="text-xs text-muted-foreground">Users</div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <div className="text-xl font-bold text-orange-600">
                      {setupResult.stats.maintenance}
                    </div>
                    <div className="text-xs text-muted-foreground">Maintenance</div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p><strong>Setup Database:</strong> Creates the database schema and populates it with sample Ethiopian transformer data.</p>
              <p><strong>Reset Database:</strong> Completely removes the existing database and recreates it with fresh data.</p>
              <p><strong>Refresh Status:</strong> Checks the current status of the database and tables.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
