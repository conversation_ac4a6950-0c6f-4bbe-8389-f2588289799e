/**
 * Additional Transformer Seeding Script
 * Adds more unique transformer demo data without duplicates
 */

const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '',
  database: 'dtms_eeu_db'
};

// Generate unique serial numbers based on timestamp
function generateUniqueSerial(manufacturer, index) {
  const timestamp = Date.now();
  const manufacturerCode = manufacturer.substring(0, 3).toUpperCase();
  return `${manufacturerCode}-${timestamp}-${String(index).padStart(3, '0')}`;
}

// Additional transformer configurations
const additionalTransformers = [
  // Industrial transformers
  {
    name: 'Hawassa Industrial Park Transformer A',
    type: 'power',
    capacity_kva: 5000,
    voltage_primary: 132.0,
    voltage_secondary: 15.0,
    manufacturer: 'ABB',
    model: 'ONAN-5000',
    location_name: 'Hawassa Industrial Park, SNNPR',
    latitude: 7.0622,
    longitude: 38.4759,
    status: 'operational'
  },
  {
    name: 'Dire Dawa Railway Station Transformer',
    type: 'distribution',
    capacity_kva: 800,
    voltage_primary: 15.0,
    voltage_secondary: 0.4,
    manufacturer: 'Siemens',
    model: 'SIT-800',
    location_name: 'Dire Dawa Railway Station',
    latitude: 9.6,
    longitude: 41.9,
    status: 'operational'
  },
  {
    name: 'Mekelle University Campus Transformer',
    type: 'distribution',
    capacity_kva: 1250,
    voltage_primary: 33.0,
    voltage_secondary: 0.4,
    manufacturer: 'Schneider Electric',
    model: 'SE-1250',
    location_name: 'Mekelle University, Tigray',
    latitude: 13.4967,
    longitude: 39.4734,
    status: 'operational'
  },
  {
    name: 'Bahir Dar Hospital Emergency Transformer',
    type: 'distribution',
    capacity_kva: 500,
    voltage_primary: 11.0,
    voltage_secondary: 0.4,
    manufacturer: 'General Electric',
    model: 'GE-500-Emergency',
    location_name: 'Bahir Dar Hospital, Amhara',
    latitude: 11.5954,
    longitude: 37.3914,
    status: 'operational'
  },
  {
    name: 'Jimma Coffee Processing Plant Transformer',
    type: 'distribution',
    capacity_kva: 1600,
    voltage_primary: 33.0,
    voltage_secondary: 11.0,
    manufacturer: 'TBEA',
    model: 'TBEA-1600',
    location_name: 'Jimma Coffee Processing, Oromia',
    latitude: 7.6667,
    longitude: 36.8333,
    status: 'warning'
  },
  // Rural electrification transformers
  {
    name: 'Gambela Rural Electrification T1',
    type: 'distribution',
    capacity_kva: 200,
    voltage_primary: 15.0,
    voltage_secondary: 0.4,
    manufacturer: 'Hyundai Heavy Industries',
    model: 'HHI-200',
    location_name: 'Gambela Rural Area 1',
    latitude: 8.25,
    longitude: 34.58,
    status: 'operational'
  },
  {
    name: 'Afar Pastoral Community Transformer',
    type: 'distribution',
    capacity_kva: 100,
    voltage_primary: 11.0,
    voltage_secondary: 0.4,
    manufacturer: 'TBEA',
    model: 'TBEA-100-Rural',
    location_name: 'Afar Pastoral Area',
    latitude: 11.75,
    longitude: 40.95,
    status: 'maintenance'
  },
  {
    name: 'Benishangul-Gumuz Mining Transformer',
    type: 'power',
    capacity_kva: 3150,
    voltage_primary: 66.0,
    voltage_secondary: 15.0,
    manufacturer: 'ABB',
    model: 'ONAN-3150',
    location_name: 'Mining Site, Benishangul-Gumuz',
    latitude: 10.78,
    longitude: 35.56,
    status: 'operational'
  },
  // Critical infrastructure transformers
  {
    name: 'Addis Ababa Airport Main Transformer',
    type: 'power',
    capacity_kva: 2500,
    voltage_primary: 66.0,
    voltage_secondary: 15.0,
    manufacturer: 'Siemens',
    model: 'SIT-2500-Airport',
    location_name: 'Bole International Airport',
    latitude: 8.9806,
    longitude: 38.7992,
    status: 'operational'
  },
  {
    name: 'Harari Regional Hospital Transformer',
    type: 'distribution',
    capacity_kva: 630,
    voltage_primary: 15.0,
    voltage_secondary: 0.4,
    manufacturer: 'General Electric',
    model: 'GE-630-Medical',
    location_name: 'Harar Regional Hospital',
    latitude: 9.3,
    longitude: 42.1,
    status: 'critical'
  }
];

async function seedAdditionalTransformers() {
  let connection;

  try {
    console.log('🔄 Connecting to MySQL database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to MySQL database');

    // Check current transformer count
    const [currentCount] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    console.log(`📊 Current transformer count: ${currentCount[0].count}`);

    console.log('🔄 Adding additional unique transformers...');
    
    let addedCount = 0;
    
    for (let i = 0; i < additionalTransformers.length; i++) {
      const transformer = additionalTransformers[i];
      
      // Generate unique serial number
      const serialNumber = generateUniqueSerial(transformer.manufacturer, i + 1);
      
      // Set default values
      const yearManufactured = 2018 + Math.floor(Math.random() * 6); // 2018-2023
      const installationDate = `${yearManufactured + 1}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`;
      const regionId = Math.floor(Math.random() * 10) + 1;
      const serviceCenterId = Math.floor(Math.random() * 8) + 1;
      const efficiencyRating = Math.round((Math.random() * 5 + 95) * 100) / 100;
      const loadFactor = Math.round((Math.random() * 30 + 70) * 100) / 100;
      const temperature = Math.round((Math.random() * 40 + 30) * 100) / 100;
      const oilLevel = Math.round((Math.random() * 20 + 80) * 100) / 100;
      const lastMaintenance = `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`;
      const nextMaintenance = `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`;

      try {
        await connection.execute(`
          INSERT INTO app_transformers (
            serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
            manufacturer, model, year_manufactured, installation_date, location_name,
            latitude, longitude, region_id, service_center_id, status,
            efficiency_rating, load_factor, temperature, oil_level,
            last_maintenance, next_maintenance, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          serialNumber,
          transformer.name,
          transformer.type,
          transformer.capacity_kva,
          transformer.voltage_primary,
          transformer.voltage_secondary,
          transformer.manufacturer,
          transformer.model,
          yearManufactured,
          installationDate,
          transformer.location_name,
          transformer.latitude,
          transformer.longitude,
          regionId,
          serviceCenterId,
          transformer.status,
          efficiencyRating,
          loadFactor,
          temperature,
          oilLevel,
          lastMaintenance,
          nextMaintenance
        ]);
        
        addedCount++;
        console.log(`✅ Added: ${transformer.name} (${serialNumber})`);
        
      } catch (error) {
        console.log(`⚠️  Skipped ${transformer.name}: ${error.message}`);
      }
    }

    // Display final summary
    const [finalCount] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    console.log(`\n📊 Added ${addedCount} new transformers`);
    console.log(`📊 Total transformers in database: ${finalCount[0].count}`);

    // Show status distribution
    const [statusDist] = await connection.execute(`
      SELECT status, COUNT(*) as count, AVG(capacity_kva) as avg_capacity
      FROM app_transformers 
      GROUP BY status 
      ORDER BY count DESC
    `);

    console.log('\n📈 Updated Status Distribution:');
    statusDist.forEach(row => {
      console.log(`  ${row.status}: ${row.count} transformers (Avg: ${Math.round(row.avg_capacity)}kVA)`);
    });

    console.log('\n🎉 Additional transformer seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error seeding additional transformers:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the seeding script
if (require.main === module) {
  seedAdditionalTransformers()
    .then(() => {
      console.log('✅ Additional transformer seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Additional transformer seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedAdditionalTransformers };
