"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Textarea } from "@/src/components/ui/textarea"
import { Progress } from "@/src/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/src/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/src/components/ui/dialog"
import {
  Package,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  ShoppingCart,
  Truck,
  AlertTriangle,
  CheckCircle,
  XCircle,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Download,
  RefreshCw,
  Settings,
  Warehouse,
  DollarSign,
  Calendar,
  User,
  MapPin,
  Activity,
  FileText
} from 'lucide-react'
import { format } from 'date-fns'

interface InventoryItem {
  id: string
  partNumber: string
  name: string
  description: string
  category: 'electrical' | 'mechanical' | 'oil' | 'cooling' | 'protection' | 'accessories'
  manufacturer: string
  model?: string
  currentStock: number
  minStock: number
  maxStock: number
  unitPrice: number
  totalValue: number
  location: string
  warehouse: string
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'on_order' | 'discontinued'
  lastUpdated: string
  lastOrderDate?: string
  supplier: string
  leadTime: number // in days
  usageHistory: {
    date: string
    quantity: number
    purpose: string
    transformerId?: string
  }[]
  specifications: {
    voltage?: string
    current?: string
    power?: string
    material?: string
    dimensions?: string
    weight?: string
  }
}

interface PurchaseOrder {
  id: string
  orderNumber: string
  supplier: string
  status: 'draft' | 'pending' | 'approved' | 'ordered' | 'received' | 'cancelled'
  orderDate: string
  expectedDelivery?: string
  actualDelivery?: string
  totalAmount: number
  items: {
    partId: string
    partNumber: string
    name: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }[]
  requestedBy: string
  approvedBy?: string
}

interface PartsInventoryProps {
  className?: string
}

export function PartsInventory({ className }: PartsInventoryProps) {
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [filteredInventory, setFilteredInventory] = useState<InventoryItem[]>([])
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('inventory')
  const [showAddItemDialog, setShowAddItemDialog] = useState(false)
  const [showOrderDialog, setShowOrderDialog] = useState(false)
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null)
  const [filters, setFilters] = useState({
    search: '',
    category: 'all',
    status: 'all',
    warehouse: 'all'
  })

  // Mock data for development
  const mockInventory: InventoryItem[] = [
    {
      id: 'INV-001',
      partNumber: 'TR-OIL-001',
      name: 'Transformer Oil',
      description: 'High-grade mineral insulating oil for power transformers',
      category: 'oil',
      manufacturer: 'Shell',
      model: 'Diala S4 ZX-I',
      currentStock: 500,
      minStock: 100,
      maxStock: 1000,
      unitPrice: 25.50,
      totalValue: 12750,
      location: 'A-01-001',
      warehouse: 'Main Warehouse',
      status: 'in_stock',
      lastUpdated: '2024-02-10T10:00:00Z',
      lastOrderDate: '2024-01-15T09:00:00Z',
      supplier: 'Shell Ethiopia',
      leadTime: 14,
      usageHistory: [
        {
          date: '2024-02-05T14:00:00Z',
          quantity: 50,
          purpose: 'Routine maintenance',
          transformerId: 'T-AA-001'
        }
      ],
      specifications: {
        voltage: '132kV',
        material: 'Mineral Oil',
        weight: '25L per drum'
      }
    },
    {
      id: 'INV-002',
      partNumber: 'TR-BUSH-001',
      name: 'High Voltage Bushing',
      description: '132kV porcelain bushing for transformer connections',
      category: 'electrical',
      manufacturer: 'ABB',
      model: 'GOB 145',
      currentStock: 8,
      minStock: 5,
      maxStock: 20,
      unitPrice: 2500.00,
      totalValue: 20000,
      location: 'B-02-003',
      warehouse: 'Electrical Parts',
      status: 'in_stock',
      lastUpdated: '2024-02-08T15:30:00Z',
      supplier: 'ABB Ethiopia',
      leadTime: 30,
      usageHistory: [
        {
          date: '2024-01-20T11:00:00Z',
          quantity: 2,
          purpose: 'Emergency repair',
          transformerId: 'T-OR-045'
        }
      ],
      specifications: {
        voltage: '132kV',
        current: '1250A',
        material: 'Porcelain',
        dimensions: '1200mm x 300mm'
      }
    },
    {
      id: 'INV-003',
      partNumber: 'TR-COOL-001',
      name: 'Cooling Fan Motor',
      description: 'Variable speed cooling fan motor for transformer cooling system',
      category: 'cooling',
      manufacturer: 'Siemens',
      model: '1LA7 113-4AA60',
      currentStock: 2,
      minStock: 3,
      maxStock: 10,
      unitPrice: 1200.00,
      totalValue: 2400,
      location: 'C-01-005',
      warehouse: 'Mechanical Parts',
      status: 'low_stock',
      lastUpdated: '2024-02-12T09:15:00Z',
      supplier: 'Siemens Ethiopia',
      leadTime: 21,
      usageHistory: [
        {
          date: '2024-02-01T16:00:00Z',
          quantity: 1,
          purpose: 'Preventive maintenance',
          transformerId: 'T-AM-023'
        }
      ],
      specifications: {
        power: '5.5kW',
        voltage: '400V',
        current: '11.5A',
        dimensions: '400mm x 300mm x 250mm'
      }
    }
  ]

  const mockPurchaseOrders: PurchaseOrder[] = [
    {
      id: 'PO-001',
      orderNumber: 'PO-2024-001',
      supplier: 'ABB Ethiopia',
      status: 'ordered',
      orderDate: '2024-02-01T10:00:00Z',
      expectedDelivery: '2024-03-01T10:00:00Z',
      totalAmount: 15000,
      items: [
        {
          partId: 'INV-002',
          partNumber: 'TR-BUSH-001',
          name: 'High Voltage Bushing',
          quantity: 6,
          unitPrice: 2500,
          totalPrice: 15000
        }
      ],
      requestedBy: 'John Doe',
      approvedBy: 'Jane Smith'
    },
    {
      id: 'PO-002',
      orderNumber: 'PO-2024-002',
      supplier: 'Siemens Ethiopia',
      status: 'pending',
      orderDate: '2024-02-10T14:00:00Z',
      totalAmount: 6000,
      items: [
        {
          partId: 'INV-003',
          partNumber: 'TR-COOL-001',
          name: 'Cooling Fan Motor',
          quantity: 5,
          unitPrice: 1200,
          totalPrice: 6000
        }
      ],
      requestedBy: 'Bob Johnson'
    }
  ]

  // Load inventory data
  const loadInventory = async () => {
    try {
      setIsLoading(true)
      // In a real app, this would fetch from the database
      // const data = await inventoryService.getInventoryItems()
      setInventory(mockInventory)
      setFilteredInventory(mockInventory)
      setPurchaseOrders(mockPurchaseOrders)
    } catch (error) {
      console.error('Error loading inventory:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Apply filters
  const applyFilters = () => {
    let filtered = [...inventory]

    // Search filter
    if (filters.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(search) ||
        item.partNumber.toLowerCase().includes(search) ||
        item.manufacturer.toLowerCase().includes(search) ||
        item.description.toLowerCase().includes(search)
      )
    }

    // Category filter
    if (filters.category !== 'all') {
      filtered = filtered.filter(item => item.category === filters.category)
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(item => item.status === filters.status)
    }

    // Warehouse filter
    if (filters.warehouse !== 'all') {
      filtered = filtered.filter(item => item.warehouse === filters.warehouse)
    }

    setFilteredInventory(filtered)
  }

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock': return 'bg-green-100 text-green-800'
      case 'low_stock': return 'bg-yellow-100 text-yellow-800'
      case 'out_of_stock': return 'bg-red-100 text-red-800'
      case 'on_order': return 'bg-blue-100 text-blue-800'
      case 'discontinued': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'electrical': return 'bg-blue-100 text-blue-800'
      case 'mechanical': return 'bg-green-100 text-green-800'
      case 'oil': return 'bg-yellow-100 text-yellow-800'
      case 'cooling': return 'bg-cyan-100 text-cyan-800'
      case 'protection': return 'bg-red-100 text-red-800'
      case 'accessories': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Calculate stock level percentage
  const getStockPercentage = (current: number, min: number, max: number) => {
    return Math.round((current / max) * 100)
  }

  // Handle inventory actions
  const handleAddStock = (itemId: string, quantity: number) => {
    setInventory(prev => prev.map(item =>
      item.id === itemId
        ? {
            ...item,
            currentStock: item.currentStock + quantity,
            totalValue: (item.currentStock + quantity) * item.unitPrice,
            lastUpdated: new Date().toISOString()
          }
        : item
    ))
  }

  const handleRemoveStock = (itemId: string, quantity: number, purpose: string, transformerId?: string) => {
    setInventory(prev => prev.map(item =>
      item.id === itemId
        ? {
            ...item,
            currentStock: Math.max(0, item.currentStock - quantity),
            totalValue: Math.max(0, item.currentStock - quantity) * item.unitPrice,
            lastUpdated: new Date().toISOString(),
            usageHistory: [
              ...item.usageHistory,
              {
                date: new Date().toISOString(),
                quantity,
                purpose,
                transformerId
              }
            ]
          }
        : item
    ))
  }

  const handleCreateOrder = () => {
    setShowOrderDialog(true)
  }

  const handleViewDetails = (item: InventoryItem) => {
    setSelectedItem(item)
  }

  useEffect(() => {
    loadInventory()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [filters, inventory])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Parts & Inventory Management</h2>
          <p className="text-muted-foreground">
            Comprehensive inventory tracking and parts management for transformer maintenance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={loadInventory}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={() => setShowAddItemDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Item
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Items</p>
                <p className="text-2xl font-bold">{inventory.length}</p>
              </div>
              <Package className="h-8 w-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Low Stock</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {inventory.filter(item => item.status === 'low_stock').length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Out of Stock</p>
                <p className="text-2xl font-bold text-red-600">
                  {inventory.filter(item => item.status === 'out_of_stock').length}
                </p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold text-green-600">
                  ${inventory.reduce((sum, item) => sum + item.totalValue, 0).toLocaleString()}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Orders</p>
                <p className="text-2xl font-bold text-blue-600">
                  {purchaseOrders.filter(order => order.status === 'pending' || order.status === 'ordered').length}
                </p>
              </div>
              <ShoppingCart className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="orders">Purchase Orders</TabsTrigger>
          <TabsTrigger value="usage">Usage History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="inventory" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Search</label>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search parts..."
                      value={filters.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>

                {/* Category Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Category</label>
                  <Select
                    value={filters.category}
                    onValueChange={(value) => handleFilterChange('category', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="electrical">Electrical</SelectItem>
                      <SelectItem value="mechanical">Mechanical</SelectItem>
                      <SelectItem value="oil">Oil & Fluids</SelectItem>
                      <SelectItem value="cooling">Cooling</SelectItem>
                      <SelectItem value="protection">Protection</SelectItem>
                      <SelectItem value="accessories">Accessories</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Status Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Status</label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => handleFilterChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="in_stock">In Stock</SelectItem>
                      <SelectItem value="low_stock">Low Stock</SelectItem>
                      <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                      <SelectItem value="on_order">On Order</SelectItem>
                      <SelectItem value="discontinued">Discontinued</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Warehouse Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Warehouse</label>
                  <Select
                    value={filters.warehouse}
                    onValueChange={(value) => handleFilterChange('warehouse', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All warehouses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Warehouses</SelectItem>
                      <SelectItem value="Main Warehouse">Main Warehouse</SelectItem>
                      <SelectItem value="Electrical Parts">Electrical Parts</SelectItem>
                      <SelectItem value="Mechanical Parts">Mechanical Parts</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Results Summary */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>
              Showing {filteredInventory.length} of {inventory.length} items
            </span>
            {filters.search && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilterChange('search', '')}
              >
                Clear search
              </Button>
            )}
          </div>

          {/* Inventory Table */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Part Details</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Stock Level</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                          <span className="ml-2">Loading inventory...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredInventory.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        No inventory items found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredInventory.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.name}</div>
                            <div className="text-sm text-muted-foreground">{item.partNumber}</div>
                            <div className="text-xs text-muted-foreground">{item.manufacturer}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getCategoryColor(item.category)}>
                            {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between text-sm">
                              <span>{item.currentStock} / {item.maxStock}</span>
                              <span className="text-muted-foreground">
                                {getStockPercentage(item.currentStock, item.minStock, item.maxStock)}%
                              </span>
                            </div>
                            <Progress
                              value={getStockPercentage(item.currentStock, item.minStock, item.maxStock)}
                              className="h-2"
                            />
                            <div className="text-xs text-muted-foreground">
                              Min: {item.minStock}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(item.status)}>
                            {item.status.replace('_', ' ').charAt(0).toUpperCase() +
                             item.status.replace('_', ' ').slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium text-sm">{item.warehouse}</div>
                            <div className="text-xs text-muted-foreground">{item.location}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">${item.totalValue.toLocaleString()}</div>
                            <div className="text-xs text-muted-foreground">
                              ${item.unitPrice} each
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewDetails(item)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Item
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleAddStock(item.id, 10)}>
                                <Plus className="mr-2 h-4 w-4" />
                                Add Stock
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleRemoveStock(item.id, 1, 'Maintenance use')}>
                                <Package className="mr-2 h-4 w-4" />
                                Use Stock
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={handleCreateOrder}>
                                <ShoppingCart className="mr-2 h-4 w-4" />
                                Create Order
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          {/* Purchase Orders */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Purchase Orders</CardTitle>
                  <CardDescription>Manage and track purchase orders</CardDescription>
                </div>
                <Button onClick={handleCreateOrder}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Order
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order Number</TableHead>
                    <TableHead>Supplier</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Order Date</TableHead>
                    <TableHead>Expected Delivery</TableHead>
                    <TableHead>Total Amount</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {purchaseOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <div className="font-medium">{order.orderNumber}</div>
                      </TableCell>
                      <TableCell>{order.supplier}</TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(order.status)}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {format(new Date(order.orderDate), 'MMM d, yyyy')}
                      </TableCell>
                      <TableCell>
                        {order.expectedDelivery ?
                          format(new Date(order.expectedDelivery), 'MMM d, yyyy') :
                          'TBD'
                        }
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">${order.totalAmount.toLocaleString()}</div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Order
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {order.status === 'pending' && (
                              <DropdownMenuItem>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Approve
                              </DropdownMenuItem>
                            )}
                            {order.status === 'ordered' && (
                              <DropdownMenuItem>
                                <Truck className="mr-2 h-4 w-4" />
                                Mark Received
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Usage History
              </CardTitle>
              <CardDescription>
                Track parts usage and consumption patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  Usage Analytics
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Detailed usage history, consumption patterns, and
                  predictive analytics for inventory planning.
                </p>
                <div className="text-sm text-gray-500">
                  Usage tracking features coming soon
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Inventory Analytics
              </CardTitle>
              <CardDescription>
                Performance metrics and inventory insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <TrendingUp className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  Analytics Dashboard
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Comprehensive analytics for inventory performance,
                  cost optimization, and demand forecasting.
                </p>
                <div className="text-sm text-gray-500">
                  Advanced analytics features coming soon
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Item Dialog */}
      <Dialog open={showAddItemDialog} onOpenChange={setShowAddItemDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Inventory Item</DialogTitle>
            <DialogDescription>
              Add a new part or component to the inventory system.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Part Number</label>
                <Input placeholder="TR-XXX-XXX" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Name</label>
                <Input placeholder="Part name" />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Description</label>
              <Textarea placeholder="Detailed description of the part..." />
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="electrical">Electrical</SelectItem>
                    <SelectItem value="mechanical">Mechanical</SelectItem>
                    <SelectItem value="oil">Oil & Fluids</SelectItem>
                    <SelectItem value="cooling">Cooling</SelectItem>
                    <SelectItem value="protection">Protection</SelectItem>
                    <SelectItem value="accessories">Accessories</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Manufacturer</label>
                <Input placeholder="Manufacturer name" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Unit Price</label>
                <Input type="number" placeholder="0.00" />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Current Stock</label>
                <Input type="number" placeholder="0" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Min Stock</label>
                <Input type="number" placeholder="0" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Max Stock</label>
                <Input type="number" placeholder="0" />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddItemDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowAddItemDialog(false)}>
              Add Item
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Order Dialog */}
      <Dialog open={showOrderDialog} onOpenChange={setShowOrderDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Purchase Order</DialogTitle>
            <DialogDescription>
              Create a new purchase order for inventory replenishment.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Supplier</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="abb">ABB Ethiopia</SelectItem>
                    <SelectItem value="siemens">Siemens Ethiopia</SelectItem>
                    <SelectItem value="shell">Shell Ethiopia</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Expected Delivery</label>
                <Input type="date" />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Items</label>
              <div className="border rounded-lg p-4">
                <p className="text-sm text-muted-foreground">
                  Select items from inventory to include in this order
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Notes</label>
              <Textarea placeholder="Additional notes or special instructions..." />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowOrderDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowOrderDialog(false)}>
              Create Order
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
