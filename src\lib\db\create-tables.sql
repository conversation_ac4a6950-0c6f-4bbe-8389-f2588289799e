-- Ethiopian Electric Utility Transformer Management Database Schema
-- Create database and tables for the enhanced dashboard

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS dtms_eeu_db;
USE dtms_eeu_db;

-- Regions table
CREATE TABLE IF NOT EXISTS app_regions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(10) NOT NULL UNIQUE,
  population INT,
  area_km2 DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Service Centers table
CREATE TABLE IF NOT EXISTS app_service_centers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) NOT NULL UNIQUE,
  region_id INT,
  address TEXT,
  phone VARCHAR(20),
  email VARCHAR(100),
  manager_name VARCHA<PERSON>(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (region_id) REFERENCES app_regions(id)
);

-- Users table
CREATE TABLE IF NOT EXISTS app_users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  first_name VARCHAR(50) NOT NULL,
  last_name VARCHAR(50) NOT NULL,
  phone VARCHAR(20),
  role ENUM('super_admin', 'national_asset_manager', 'national_maintenance_manager',
           'regional_admin', 'regional_asset_manager', 'regional_maintenance_engineer',
           'service_center_manager', 'field_technician', 'customer_service_agent',
           'audit_compliance_officer') NOT NULL,
  region_id INT,
  service_center_id INT,
  is_active BOOLEAN DEFAULT TRUE,
  last_login TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (region_id) REFERENCES app_regions(id),
  FOREIGN KEY (service_center_id) REFERENCES app_service_centers(id)
);

-- Transformers table
CREATE TABLE IF NOT EXISTS app_transformers (
  id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT, -- Ensure id is UNSIGNED to match transformer_id in app_sensor_readings
  serial_number VARCHAR(50) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  type ENUM('distribution', 'power', 'instrument', 'auto') NOT NULL,
  capacity_kva DECIMAL(10,2) NOT NULL,
  voltage_primary DECIMAL(10,2) NOT NULL,
  voltage_secondary DECIMAL(10,2) NOT NULL,
  manufacturer VARCHAR(100),
  model VARCHAR(100),
  year_manufactured YEAR,
  installation_date DATE,
  location_name VARCHAR(200),
  latitude DECIMAL(10,8),
  longitude DECIMAL(11,8),
  region_id INT NOT NULL,
  service_center_id INT,
  status ENUM('operational', 'warning', 'maintenance', 'critical', 'burnt') DEFAULT 'operational',
  efficiency_rating DECIMAL(5,2) DEFAULT 95.0,
  load_factor DECIMAL(5,2) DEFAULT 75.0,
  temperature DECIMAL(5,2),
  oil_level DECIMAL(5,2),
  last_maintenance DATE,
  next_maintenance DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (region_id) REFERENCES app_regions(id),
  FOREIGN KEY (service_center_id) REFERENCES app_service_centers(id)
);

-- Alerts table
CREATE TABLE IF NOT EXISTS app_alerts (
  id INT PRIMARY KEY AUTO_INCREMENT,
  transformer_id INT,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  type ENUM('temperature', 'voltage', 'load', 'maintenance', 'communication', 'weather', 'security') NOT NULL,
  status ENUM('active', 'investigating', 'resolved', 'monitoring') DEFAULT 'active',
  priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  created_by INT,
  assigned_to INT,
  resolved_at TIMESTAMP NULL,
  resolved_by INT,
  is_resolved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (transformer_id) REFERENCES app_transformers(id),
  FOREIGN KEY (created_by) REFERENCES app_users(id),
  FOREIGN KEY (assigned_to) REFERENCES app_users(id),
  FOREIGN KEY (resolved_by) REFERENCES app_users(id)
);

-- Maintenance Schedules table
CREATE TABLE IF NOT EXISTS app_maintenance_schedules (
  id INT PRIMARY KEY AUTO_INCREMENT,
  transformer_id INT NOT NULL,
  type ENUM('routine', 'preventive', 'corrective', 'emergency', 'scheduled') NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  scheduled_date DATE NOT NULL,
  estimated_duration INT, -- in hours
  completion_days INT DEFAULT NULL, -- New column to store completion days
  priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
  status ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'postponed') DEFAULT 'scheduled',
  technician_id INT,
  supervisor_id INT,
  cost_estimate DECIMAL(10,2),
  actual_cost DECIMAL(10,2),
  started_at TIMESTAMP NULL,
  completed_at TIMESTAMP NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (transformer_id) REFERENCES app_transformers(id),
  FOREIGN KEY (technician_id) REFERENCES app_users(id),
  FOREIGN KEY (supervisor_id) REFERENCES app_users(id)
);

-- Notifications table
CREATE TABLE IF NOT EXISTS app_notifications (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(200) NOT NULL,
  message TEXT NOT NULL,
  type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
  recipient_id INT,
  sender_id INT,
  is_read BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (recipient_id) REFERENCES app_users(id),
  FOREIGN KEY (sender_id) REFERENCES app_users(id)
);

-- Performance Metrics table
CREATE TABLE IF NOT EXISTS app_performance_metrics (
  id INT PRIMARY KEY AUTO_INCREMENT,
  transformer_id INT,
  metric_type ENUM('power_generation', 'efficiency', 'load_factor', 'temperature', 'voltage') NOT NULL,
  value DECIMAL(10,4) NOT NULL,
  unit VARCHAR(20),
  recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (transformer_id) REFERENCES app_transformers(id)
);

-- Weather Data table
CREATE TABLE IF NOT EXISTS app_weather_data (
  id INT PRIMARY KEY AUTO_INCREMENT,
  region_id INT NOT NULL,
  temperature DECIMAL(5,2),
  humidity DECIMAL(5,2),
  wind_speed DECIMAL(5,2),
  weather_condition VARCHAR(50),
  risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
  recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (region_id) REFERENCES app_regions(id)
);

-- Sensor Readings table
CREATE TABLE IF NOT EXISTS app_sensor_readings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  transformer_id INT UNSIGNED NOT NULL, -- Ensure compatibility with app_transformers.id
  sensor_type ENUM('temperature', 'voltage', 'current', 'oil_level') NOT NULL,
  reading_value DECIMAL(10, 2) NOT NULL,
  recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (transformer_id) REFERENCES app_transformers(id)
);

-- Create indexes for better performance
CREATE INDEX idx_transformers_status ON app_transformers(status);
CREATE INDEX idx_transformers_region ON app_transformers(region_id);
CREATE INDEX idx_alerts_severity ON app_alerts(severity);
CREATE INDEX idx_alerts_status ON app_alerts(status);
CREATE INDEX idx_maintenance_status ON app_maintenance_schedules(status);
CREATE INDEX idx_maintenance_date ON app_maintenance_schedules(scheduled_date);
CREATE INDEX idx_performance_type ON app_performance_metrics(metric_type);
CREATE INDEX idx_performance_recorded ON app_performance_metrics(recorded_at);
