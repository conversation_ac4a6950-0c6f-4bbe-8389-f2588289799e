"use client"

import React, { createContext, useContext, useState, useEffect } from "react"
import { useTheme } from "next-themes"
import { useToast } from "@/src/components/ui/use-toast"

// Define the settings types
export interface SystemSettings {
  companyName: string
  timezone: string
  dateFormat: string
  darkMode: boolean
  maintenanceWindow: string
  dataRetention: number
  apiKey: string
  systemNotes: string
}

export interface NotificationSettings {
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  notificationFrequency: string
  criticalAlertsOnly: boolean
  maintenanceReminders: boolean
  outageAlerts: boolean
  weatherAlerts: boolean
}

export interface SecuritySettings {
  twoFactorAuth: boolean
  passwordExpiry: number
  sessionTimeout: number
  ipRestriction: boolean
  allowedIPs: string[]
  auditLogging: boolean
  failedLoginAttempts: number
}

export interface LanguageSettings {
  primaryLanguage: string
  secondaryLanguage: string
  showTranslations: boolean
  autoTranslate: boolean
  regionalFormat: string
}

export interface AdvancedSettings {
  debugMode: boolean
  enableBetaFeatures: boolean
  performanceMode: "balanced" | "performance" | "power-saving"
  autoBackup: boolean
  backupFrequency: string
  customCss: string
  apiRateLimit: number
}

export interface Settings {
  system: SystemSettings
  notifications: NotificationSettings
  security: SecuritySettings
  language: LanguageSettings
  advanced: AdvancedSettings
}

// Default settings
const defaultSettings: Settings = {
  system: {
    companyName: "Ethiopia Electric Utility",
    timezone: "eat",
    dateFormat: "dmy",
    darkMode: false,
    maintenanceWindow: "weekend",
    dataRetention: 90,
    apiKey: "sk_live_abcdefghijklmnopqrstuvwxyz",
    systemNotes: ""
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    notificationFrequency: "realtime",
    criticalAlertsOnly: false,
    maintenanceReminders: true,
    outageAlerts: true,
    weatherAlerts: true
  },
  security: {
    twoFactorAuth: false,
    passwordExpiry: 90,
    sessionTimeout: 30,
    ipRestriction: false,
    allowedIPs: [],
    auditLogging: true,
    failedLoginAttempts: 5
  },
  language: {
    primaryLanguage: "en",
    secondaryLanguage: "am",
    showTranslations: true,
    autoTranslate: false,
    regionalFormat: "et"
  },
  advanced: {
    debugMode: false,
    enableBetaFeatures: false,
    performanceMode: "balanced",
    autoBackup: true,
    backupFrequency: "daily",
    customCss: "",
    apiRateLimit: 100
  }
}

// Create the context
interface SettingsContextType {
  settings: Settings
  updateSystemSettings: (settings: Partial<SystemSettings>) => void
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => void
  updateSecuritySettings: (settings: Partial<SecuritySettings>) => void
  updateLanguageSettings: (settings: Partial<LanguageSettings>) => void
  updateAdvancedSettings: (settings: Partial<AdvancedSettings>) => void
  resetToDefaults: () => void
  saveSettings: () => void
  hasUnsavedChanges: boolean
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const { toast } = useToast()
  const { setTheme } = useTheme()
  const [settings, setSettings] = useState<Settings>(defaultSettings)
  const [originalSettings, setOriginalSettings] = useState<Settings>(defaultSettings)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Load settings from localStorage on mount
  useEffect(() => {
    const loadSettings = () => {
      try {
        const savedSettings = localStorage.getItem("eeu_settings")
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings) as Settings
          setSettings(parsedSettings)
          setOriginalSettings(parsedSettings)
          
          // Apply theme setting
          if (parsedSettings.system.darkMode) {
            setTheme("dark")
          } else {
            setTheme("light")
          }
        }
      } catch (error) {
        console.error("Failed to load settings:", error)
        toast({
          title: "Error",
          description: "Failed to load settings. Using defaults.",
          variant: "destructive"
        })
      }
    }

    loadSettings()
  }, [setTheme, toast])

  // Update settings functions
  const updateSystemSettings = (newSettings: Partial<SystemSettings>) => {
    setSettings(prev => {
      const updated = {
        ...prev,
        system: {
          ...prev.system,
          ...newSettings
        }
      }
      setHasUnsavedChanges(JSON.stringify(updated) !== JSON.stringify(originalSettings))
      return updated
    })

    // Apply theme change immediately if darkMode is changed
    if (newSettings.darkMode !== undefined) {
      setTheme(newSettings.darkMode ? "dark" : "light")
    }
  }

  const updateNotificationSettings = (newSettings: Partial<NotificationSettings>) => {
    setSettings(prev => {
      const updated = {
        ...prev,
        notifications: {
          ...prev.notifications,
          ...newSettings
        }
      }
      setHasUnsavedChanges(JSON.stringify(updated) !== JSON.stringify(originalSettings))
      return updated
    })
  }

  const updateSecuritySettings = (newSettings: Partial<SecuritySettings>) => {
    setSettings(prev => {
      const updated = {
        ...prev,
        security: {
          ...prev.security,
          ...newSettings
        }
      }
      setHasUnsavedChanges(JSON.stringify(updated) !== JSON.stringify(originalSettings))
      return updated
    })
  }

  const updateLanguageSettings = (newSettings: Partial<LanguageSettings>) => {
    setSettings(prev => {
      const updated = {
        ...prev,
        language: {
          ...prev.language,
          ...newSettings
        }
      }
      setHasUnsavedChanges(JSON.stringify(updated) !== JSON.stringify(originalSettings))
      return updated
    })
  }

  const updateAdvancedSettings = (newSettings: Partial<AdvancedSettings>) => {
    setSettings(prev => {
      const updated = {
        ...prev,
        advanced: {
          ...prev.advanced,
          ...newSettings
        }
      }
      setHasUnsavedChanges(JSON.stringify(updated) !== JSON.stringify(originalSettings))
      return updated
    })
  }

  const resetToDefaults = () => {
    setSettings(defaultSettings)
    setHasUnsavedChanges(true)
    toast({
      title: "Settings Reset",
      description: "All settings have been reset to default values. Click Save to apply changes."
    })
  }

  const saveSettings = () => {
    try {
      localStorage.setItem("eeu_settings", JSON.stringify(settings))
      setOriginalSettings(settings)
      setHasUnsavedChanges(false)
      toast({
        title: "Settings Saved",
        description: "Your settings have been saved successfully."
      })
    } catch (error) {
      console.error("Failed to save settings:", error)
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive"
      })
    }
  }

  return (
    <SettingsContext.Provider
      value={{
        settings,
        updateSystemSettings,
        updateNotificationSettings,
        updateSecuritySettings,
        updateLanguageSettings,
        updateAdvancedSettings,
        resetToDefaults,
        saveSettings,
        hasUnsavedChanges
      }}
    >
      {children}
    </SettingsContext.Provider>
  )
}

export function useSettings() {
  const context = useContext(SettingsContext)
  if (context === undefined) {
    throw new Error("useSettings must be used within a SettingsProvider")
  }
  return context
}
