export interface SmartMeterLocation {
  latitude: number;
  longitude: number;
  region: string;
  serviceCenter?: string;
  address: string;
  installationSite?: string;
}

export interface SmartMeterReading {
  timestamp: string;
  value: number; // in kWh
  peak: boolean;
}

export interface SmartMeterAlert {
  id: string;
  meterId: string;
  timestamp: string;
  type: "Disconnection" | "Tampering" | "LowBattery" | "CommunicationFailure" | "HighConsumption" | "LowConsumption";
  severity: "Low" | "Medium" | "High" | "Critical";
  message: string;
  status: "New" | "Acknowledged" | "Resolved" | "Ignored";
  resolvedAt?: string;
  resolvedBy?: string;
  notes?: string;
}

export interface SmartMeter {
  id: string;
  serialNumber: string;
  manufacturer: string;
  model: string;
  type: "Residential" | "Commercial" | "Industrial";
  installDate: string;
  lastReading: string;
  lastReadingValue: number;
  lastCommunication: string;
  firmwareVersion: string;
  status: "Connected" | "Disconnected" | "Maintenance" | "Tampered";
  location: SmartMeterLocation;
  customer: {
    id: string;
    name: string;
    accountNumber: string;
    contactPhone: string;
    email?: string;
  };
  readings?: SmartMeterReading[];
  alerts?: SmartMeterAlert[];
  batteryLevel?: number;
  signalStrength?: number;
  tariffPlan: string;
  billingCycle: "Monthly" | "Quarterly";
  prepaid: boolean;
  balance?: number;
  notes?: string;
}

export interface SmartMeterStatistics {
  total: number;
  byStatus: Record<string, number>;
  byRegion: Record<string, number>;
  byType: Record<string, number>;
  byManufacturer: Record<string, number>;
  alerts: {
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    byStatus: Record<string, number>;
  };
  consumption: {
    daily: number;
    weekly: number;
    monthly: number;
    byRegion: Record<string, number>;
    byType: Record<string, number>;
  };
}

export interface SmartMeterUsageData {
  timestamp: string;
  residential: number;
  commercial: number;
  industrial: number;
}
