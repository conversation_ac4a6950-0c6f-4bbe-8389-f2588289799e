/**
 * Demo Data Summary Script
 * Provides comprehensive overview of seeded transformer demo data
 */

const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '',
  database: 'dtms_eeu_db'
};

async function generateDemoDataSummary() {
  let connection;

  try {
    console.log('🔄 Connecting to MySQL database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to MySQL database\n');

    console.log('🎉 ETHIOPIAN ELECTRIC UTILITY - TRANSFORMER DEMO DATA SUMMARY');
    console.log('=' .repeat(70));

    // Total transformer count
    const [totalCount] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    console.log(`\n📊 TOTAL TRANSFORMERS: ${totalCount[0].count}`);

    // Status distribution
    const [statusDist] = await connection.execute(`
      SELECT status, COUNT(*) as count, 
             ROUND(AVG(capacity_kva), 0) as avg_capacity,
             ROUND(AVG(efficiency_rating), 1) as avg_efficiency,
             ROUND(AVG(load_factor), 1) as avg_load
      FROM app_transformers 
      GROUP BY status 
      ORDER BY count DESC
    `);

    console.log('\n📈 STATUS DISTRIBUTION:');
    statusDist.forEach(row => {
      const percentage = ((row.count / totalCount[0].count) * 100).toFixed(1);
      console.log(`  ✓ ${row.status.toUpperCase()}: ${row.count} transformers (${percentage}%)`);
      console.log(`    - Avg Capacity: ${row.avg_capacity}kVA`);
      console.log(`    - Avg Efficiency: ${row.avg_efficiency}%`);
      console.log(`    - Avg Load Factor: ${row.avg_load}%\n`);
    });

    // Manufacturer distribution
    const [manufacturerDist] = await connection.execute(`
      SELECT manufacturer, COUNT(*) as count, 
             ROUND(AVG(capacity_kva), 0) as avg_capacity
      FROM app_transformers 
      GROUP BY manufacturer 
      ORDER BY count DESC
    `);

    console.log('🏭 MANUFACTURER DISTRIBUTION:');
    manufacturerDist.forEach(row => {
      const percentage = ((row.count / totalCount[0].count) * 100).toFixed(1);
      console.log(`  ✓ ${row.manufacturer}: ${row.count} units (${percentage}%) - Avg: ${row.avg_capacity}kVA`);
    });

    // Type distribution
    const [typeDist] = await connection.execute(`
      SELECT type, COUNT(*) as count, 
             ROUND(AVG(capacity_kva), 0) as avg_capacity,
             MIN(capacity_kva) as min_capacity,
             MAX(capacity_kva) as max_capacity
      FROM app_transformers 
      GROUP BY type 
      ORDER BY count DESC
    `);

    console.log('\n⚡ TRANSFORMER TYPE DISTRIBUTION:');
    typeDist.forEach(row => {
      const percentage = ((row.count / totalCount[0].count) * 100).toFixed(1);
      console.log(`  ✓ ${row.type.toUpperCase()}: ${row.count} units (${percentage}%)`);
      console.log(`    - Capacity Range: ${row.min_capacity}kVA - ${row.max_capacity}kVA`);
      console.log(`    - Average: ${row.avg_capacity}kVA\n`);
    });

    // Capacity ranges
    const [capacityRanges] = await connection.execute(`
      SELECT 
        CASE 
          WHEN capacity_kva < 500 THEN 'Small (< 500kVA)'
          WHEN capacity_kva < 2000 THEN 'Medium (500-2000kVA)'
          WHEN capacity_kva < 10000 THEN 'Large (2000-10000kVA)'
          ELSE 'Extra Large (> 10000kVA)'
        END as capacity_range,
        COUNT(*) as count
      FROM app_transformers 
      GROUP BY capacity_range
      ORDER BY 
        CASE 
          WHEN capacity_kva < 500 THEN 1
          WHEN capacity_kva < 2000 THEN 2
          WHEN capacity_kva < 10000 THEN 3
          ELSE 4
        END
    `);

    console.log('📏 CAPACITY DISTRIBUTION:');
    capacityRanges.forEach(row => {
      const percentage = ((row.count / totalCount[0].count) * 100).toFixed(1);
      console.log(`  ✓ ${row.capacity_range}: ${row.count} units (${percentage}%)`);
    });

    // Regional distribution
    const [regionDist] = await connection.execute(`
      SELECT r.name as region_name, COUNT(t.id) as transformer_count,
             ROUND(AVG(t.capacity_kva), 0) as avg_capacity
      FROM app_transformers t
      LEFT JOIN app_regions r ON t.region_id = r.id
      GROUP BY r.name, t.region_id
      ORDER BY transformer_count DESC
    `);

    console.log('\n🗺️  REGIONAL DISTRIBUTION:');
    regionDist.forEach(row => {
      const regionName = row.region_name || `Region ID ${row.region_id || 'Unknown'}`;
      const percentage = ((row.transformer_count / totalCount[0].count) * 100).toFixed(1);
      console.log(`  ✓ ${regionName}: ${row.transformer_count} transformers (${percentage}%) - Avg: ${row.avg_capacity}kVA`);
    });

    // Age distribution
    const [ageDist] = await connection.execute(`
      SELECT 
        CASE 
          WHEN year_manufactured >= 2020 THEN 'New (2020+)'
          WHEN year_manufactured >= 2015 THEN 'Recent (2015-2019)'
          WHEN year_manufactured >= 2010 THEN 'Mature (2010-2014)'
          ELSE 'Old (< 2010)'
        END as age_group,
        COUNT(*) as count,
        ROUND(AVG(efficiency_rating), 1) as avg_efficiency
      FROM app_transformers 
      WHERE year_manufactured IS NOT NULL
      GROUP BY age_group
      ORDER BY 
        CASE 
          WHEN year_manufactured >= 2020 THEN 1
          WHEN year_manufactured >= 2015 THEN 2
          WHEN year_manufactured >= 2010 THEN 3
          ELSE 4
        END
    `);

    console.log('\n📅 AGE DISTRIBUTION:');
    ageDist.forEach(row => {
      const percentage = ((row.count / totalCount[0].count) * 100).toFixed(1);
      console.log(`  ✓ ${row.age_group}: ${row.count} units (${percentage}%) - Avg Efficiency: ${row.avg_efficiency}%`);
    });

    // Critical transformers
    const [criticalTransformers] = await connection.execute(`
      SELECT name, serial_number, capacity_kva, status, temperature, oil_level
      FROM app_transformers 
      WHERE status IN ('critical', 'warning', 'burnt')
      ORDER BY 
        CASE status 
          WHEN 'burnt' THEN 1 
          WHEN 'critical' THEN 2 
          WHEN 'warning' THEN 3 
        END,
        temperature DESC
      LIMIT 10
    `);

    console.log('\n⚠️  TRANSFORMERS REQUIRING ATTENTION:');
    criticalTransformers.forEach((transformer, i) => {
      console.log(`  ${i+1}. ${transformer.name}`);
      console.log(`     Serial: ${transformer.serial_number} | Status: ${transformer.status.toUpperCase()}`);
      console.log(`     Capacity: ${transformer.capacity_kva}kVA | Temp: ${transformer.temperature}°C | Oil: ${transformer.oil_level}%\n`);
    });

    console.log('=' .repeat(70));
    console.log('✅ Demo data summary completed successfully!');
    console.log('🌐 Access the application at: http://localhost:3002');
    console.log('📊 Database: dtms_eeu_db with comprehensive transformer data');

  } catch (error) {
    console.error('❌ Error generating demo data summary:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the summary script
if (require.main === module) {
  generateDemoDataSummary()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Demo data summary failed:', error);
      process.exit(1);
    });
}

module.exports = { generateDemoDataSummary };
