/**
 * Validation Utilities
 * Common validation functions and schemas
 */

import { isEmail, isPhone, isUrl, isNumeric, isEmpty } from './string.utils'

// Basic validation functions
export const required = (value: any, message: string = 'This field is required'): string | null => {
  if (value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) {
    return message
  }
  return null
}

export const minLength = (value: string, min: number, message?: string): string | null => {
  if (!value) return null
  if (value.length < min) {
    return message || `Must be at least ${min} characters long`
  }
  return null
}

export const maxLength = (value: string, max: number, message?: string): string | null => {
  if (!value) return null
  if (value.length > max) {
    return message || `Must be no more than ${max} characters long`
  }
  return null
}

export const minValue = (value: number, min: number, message?: string): string | null => {
  if (value === null || value === undefined) return null
  if (value < min) {
    return message || `Must be at least ${min}`
  }
  return null
}

export const maxValue = (value: number, max: number, message?: string): string | null => {
  if (value === null || value === undefined) return null
  if (value > max) {
    return message || `Must be no more than ${max}`
  }
  return null
}

export const pattern = (value: string, regex: RegExp, message: string): string | null => {
  if (!value) return null
  if (!regex.test(value)) {
    return message
  }
  return null
}

// Email validation
export const validateEmail = (email: string): string | null => {
  if (!email) return null
  if (!isEmail(email)) {
    return 'Please enter a valid email address'
  }
  return null
}

// Phone validation
export const validatePhone = (phone: string): string | null => {
  if (!phone) return null
  if (!isPhone(phone)) {
    return 'Please enter a valid phone number'
  }
  return null
}

// URL validation
export const validateUrl = (url: string): string | null => {
  if (!url) return null
  if (!isUrl(url)) {
    return 'Please enter a valid URL'
  }
  return null
}

// Password validation
export const validatePassword = (password: string, options: {
  minLength?: number
  requireUppercase?: boolean
  requireLowercase?: boolean
  requireNumbers?: boolean
  requireSpecialChars?: boolean
  specialChars?: string
} = {}): string | null => {
  if (!password) return null
  
  const {
    minLength: min = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = true,
    specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?'
  } = options
  
  if (password.length < min) {
    return `Password must be at least ${min} characters long`
  }
  
  if (requireUppercase && !/[A-Z]/.test(password)) {
    return 'Password must contain at least one uppercase letter'
  }
  
  if (requireLowercase && !/[a-z]/.test(password)) {
    return 'Password must contain at least one lowercase letter'
  }
  
  if (requireNumbers && !/[0-9]/.test(password)) {
    return 'Password must contain at least one number'
  }
  
  if (requireSpecialChars) {
    const specialCharRegex = new RegExp(`[${specialChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`)
    if (!specialCharRegex.test(password)) {
      return 'Password must contain at least one special character'
    }
  }
  
  return null
}

// Confirm password validation
export const validateConfirmPassword = (password: string, confirmPassword: string): string | null => {
  if (!confirmPassword) return null
  if (password !== confirmPassword) {
    return 'Passwords do not match'
  }
  return null
}

// Date validation
export const validateDate = (date: string | Date, message: string = 'Please enter a valid date'): string | null => {
  if (!date) return null
  
  const dateObj = typeof date === 'string' ? new Date(date) : date
  if (isNaN(dateObj.getTime())) {
    return message
  }
  
  return null
}

export const validateDateRange = (
  startDate: string | Date,
  endDate: string | Date,
  message: string = 'End date must be after start date'
): string | null => {
  if (!startDate || !endDate) return null
  
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return 'Please enter valid dates'
  }
  
  if (end <= start) {
    return message
  }
  
  return null
}

export const validateAge = (birthDate: string | Date, minAge: number = 18): string | null => {
  if (!birthDate) return null
  
  const birth = typeof birthDate === 'string' ? new Date(birthDate) : birthDate
  if (isNaN(birth.getTime())) {
    return 'Please enter a valid birth date'
  }
  
  const today = new Date()
  const age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    if (age - 1 < minAge) {
      return `You must be at least ${minAge} years old`
    }
  } else if (age < minAge) {
    return `You must be at least ${minAge} years old`
  }
  
  return null
}

// File validation
export const validateFile = (file: File, options: {
  maxSize?: number
  allowedTypes?: string[]
  allowedExtensions?: string[]
} = {}): string | null => {
  if (!file) return null
  
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = [],
    allowedExtensions = []
  } = options
  
  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024))
    return `File size must be less than ${maxSizeMB}MB`
  }
  
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    return `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
  }
  
  if (allowedExtensions.length > 0) {
    const extension = file.name.split('.').pop()?.toLowerCase()
    if (!extension || !allowedExtensions.includes(extension)) {
      return `File extension not allowed. Allowed extensions: ${allowedExtensions.join(', ')}`
    }
  }
  
  return null
}

// Array validation
export const validateArray = (
  array: any[],
  options: {
    minLength?: number
    maxLength?: number
    unique?: boolean
  } = {}
): string | null => {
  if (!Array.isArray(array)) return 'Must be an array'
  
  const { minLength, maxLength, unique = false } = options
  
  if (minLength !== undefined && array.length < minLength) {
    return `Must have at least ${minLength} items`
  }
  
  if (maxLength !== undefined && array.length > maxLength) {
    return `Must have no more than ${maxLength} items`
  }
  
  if (unique && new Set(array).size !== array.length) {
    return 'All items must be unique'
  }
  
  return null
}

// Custom validation function type
export type ValidationFunction = (value: any) => string | null

// Validation schema type
export interface ValidationSchema {
  [key: string]: ValidationFunction[]
}

// Validate object against schema
export const validateObject = (obj: Record<string, any>, schema: ValidationSchema): Record<string, string> => {
  const errors: Record<string, string> = {}
  
  Object.keys(schema).forEach(key => {
    const validators = schema[key]
    const value = obj[key]
    
    for (const validator of validators) {
      const error = validator(value)
      if (error) {
        errors[key] = error
        break // Stop at first error for this field
      }
    }
  })
  
  return errors
}

// Common validation schemas
export const commonSchemas = {
  user: {
    email: [required, validateEmail],
    firstName: [required, (value: string) => minLength(value, 2)],
    lastName: [required, (value: string) => minLength(value, 2)],
    phone: [validatePhone],
    password: [required, validatePassword]
  },
  
  transformer: {
    serialNumber: [required, (value: string) => minLength(value, 3)],
    capacity: [required, (value: string) => isNumeric(value) ? null : 'Must be a number'],
    manufacturer: [required],
    location: [required]
  },
  
  maintenance: {
    transformerId: [required],
    scheduledDate: [required, validateDate],
    type: [required],
    assignedTo: [required]
  }
}

// Validation helpers
export const isValidationError = (errors: Record<string, string>): boolean => {
  return Object.keys(errors).length > 0
}

export const getFirstError = (errors: Record<string, string>): string | null => {
  const keys = Object.keys(errors)
  return keys.length > 0 ? errors[keys[0]] : null
}

export const combineValidators = (...validators: ValidationFunction[]): ValidationFunction => {
  return (value: any) => {
    for (const validator of validators) {
      const error = validator(value)
      if (error) return error
    }
    return null
  }
}

// Async validation function type
export type AsyncValidationFunction = (value: any) => Promise<string | null>

// Async validation
export const validateAsync = async (
  obj: Record<string, any>,
  schema: Record<string, AsyncValidationFunction[]>
): Promise<Record<string, string>> => {
  const errors: Record<string, string> = {}
  
  await Promise.all(
    Object.keys(schema).map(async (key) => {
      const validators = schema[key]
      const value = obj[key]
      
      for (const validator of validators) {
        const error = await validator(value)
        if (error) {
          errors[key] = error
          break
        }
      }
    })
  )
  
  return errors
}

// Ethiopian-specific validations
export const validateEthiopianPhone = (phone: string): string | null => {
  if (!phone) return null
  
  // Ethiopian phone number format: +251XXXXXXXXX or 09XXXXXXXX
  const cleanPhone = phone.replace(/\s|-/g, '')
  const ethiopianPhoneRegex = /^(\+251|0)[79]\d{8}$/
  
  if (!ethiopianPhoneRegex.test(cleanPhone)) {
    return 'Please enter a valid Ethiopian phone number'
  }
  
  return null
}

export const validateEmployeeId = (employeeId: string): string | null => {
  if (!employeeId) return null
  
  // EEU employee ID format: EEU-XXXX-XXXX
  const employeeIdRegex = /^EEU-\d{4}-\d{4}$/
  
  if (!employeeIdRegex.test(employeeId)) {
    return 'Employee ID must be in format: EEU-XXXX-XXXX'
  }
  
  return null
}

// Export all validation utilities
export default {
  required,
  minLength,
  maxLength,
  minValue,
  maxValue,
  pattern,
  validateEmail,
  validatePhone,
  validateUrl,
  validatePassword,
  validateConfirmPassword,
  validateDate,
  validateDateRange,
  validateAge,
  validateFile,
  validateArray,
  validateObject,
  commonSchemas,
  isValidationError,
  getFirstError,
  combineValidators,
  validateAsync,
  validateEthiopianPhone,
  validateEmployeeId
}
