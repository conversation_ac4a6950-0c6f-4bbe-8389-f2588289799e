"use client"

import { useState, useEffect, useRef } from "react"
import { use<PERSON><PERSON>, Controller } from "react-hook-form"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Checkbox } from "@/src/components/ui/checkbox"
import { Textarea } from "@/src/components/ui/textarea"
import { useToast } from "@/src/components/ui/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar"
import { Badge } from "@/src/components/ui/badge"
import {
  User, UserCheck, UserX, Shield, Building, MapPin, Key,
  Mail, Phone, FileText, Upload, X, Clock, AlertTriangle,
  Calendar, Briefcase, Home, Award, GraduationCap, ClipboardList
} from "lucide-react"
import { UserFormData } from "@/src/types/user-management"
import { StatusBadge } from "./status-badge"

interface EnhancedUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user?: UserFormData | null
  onSave: (userData: UserFormData) => void
}

export function EnhancedUserDialog({ open, onOpenChange, user, onSave }: EnhancedUserDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("basic")
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null)
  const firstInputRef = useRef<HTMLInputElement>(null)

  // React Hook Form setup
  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
    watch
  } = useForm<UserFormData>({
    defaultValues: user || {
      name: "",
      email: "",
      role: "Technician",
      department: "Maintenance",
      status: "Active",
      phoneNumber: "",
      employeeId: "",
      password: "",
      confirmPassword: "",
      position: "",
      organizationalLevel: "service_center",
      regionId: "",
      serviceCenterId: "",
      hireDate: "",
      emergencyContact: "",
      address: "",
      skills: [],
      certifications: [],
      notes: ""
    }
  })

  // Watch password fields for validation
  const password = watch("password")

  // Reset form when dialog opens/closes or user changes
  useEffect(() => {
    if (open) {
      reset(user || {
        name: "",
        email: "",
        role: "Technician",
        department: "Maintenance",
        status: "Active",
        phoneNumber: "",
        employeeId: "",
        password: "",
        confirmPassword: "",
        position: "",
        organizationalLevel: "service_center",
        regionId: "",
        serviceCenterId: "",
        hireDate: "",
        emergencyContact: "",
        address: "",
        skills: [],
        certifications: [],
        notes: ""
      })
      setActiveTab("basic")
      setAvatarPreview(user?.avatar || null)

      // Focus the first input when dialog opens
      setTimeout(() => {
        firstInputRef.current?.focus()
      }, 100)
    }
  }, [user, open, reset])

  const onSubmit = (data: UserFormData) => {
    // Create avatar if not exists
    const avatar = avatarPreview || `/placeholder.svg?height=40&width=40&text=${data.name.charAt(0)}`

    onSave({
      ...data,
      avatar,
      id: user?.id || `USR-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
    })

    onOpenChange(false)
  }

  const handleAvatarUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // In a real app, you would upload the file to a server
      // For this demo, we'll just create a data URL
      const reader = new FileReader()
      reader.onload = () => {
        setAvatarPreview(reader.result as string)
      }
      reader.readAsDataURL(file)

      toast({
        title: "Avatar Uploaded",
        description: "Profile picture has been updated.",
      })
    }
  }

  const handleRemoveAvatar = () => {
    setAvatarPreview(null)
    toast({
      title: "Avatar Removed",
      description: "Profile picture has been removed.",
    })
  }

  const handleSendPasswordReset = () => {
    toast({
      title: "Password Reset Link Sent",
      description: "A password reset link has been sent to the user's email.",
    })
  }

  const handleGenerateTemporaryPassword = () => {
    const tempPassword = Math.random().toString(36).slice(-8)
    navigator.clipboard.writeText(tempPassword)
    toast({
      title: "Temporary Password Generated",
      description: "Password copied to clipboard: " + tempPassword,
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>{user ? "Edit User" : "Add New User"}</DialogTitle>
            <DialogDescription>
              {user
                ? "Update user details, role, and permissions."
                : "Create a new user with specific role and permissions."}
            </DialogDescription>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">
                <User className="mr-2 h-4 w-4" />
                Basic Info
              </TabsTrigger>
              <TabsTrigger value="role">
                <Shield className="mr-2 h-4 w-4" />
                Role & Access
              </TabsTrigger>
              <TabsTrigger value="location">
                <MapPin className="mr-2 h-4 w-4" />
                Assignment
              </TabsTrigger>
              <TabsTrigger value="account">
                <Key className="mr-2 h-4 w-4" />
                Account
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 py-4">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Avatar className="h-20 w-20">
                    <AvatarImage
                      src={avatarPreview || `/placeholder.svg?height=80&width=80&text=${watch("name").charAt(0) || "U"}`}
                      alt={watch("name") || "User"}
                    />
                    <AvatarFallback>{watch("name").charAt(0) || "U"}</AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-2 -right-2 flex gap-1">
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="h-6 w-6 rounded-full bg-background"
                      onClick={() => document.getElementById('avatar-upload')?.click()}
                      aria-label="Upload avatar"
                    >
                      <Upload className="h-3 w-3" />
                    </Button>
                    {avatarPreview && (
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        className="h-6 w-6 rounded-full bg-background"
                        onClick={handleRemoveAvatar}
                        aria-label="Remove avatar"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                    <input
                      id="avatar-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleAvatarUpload}
                    />
                  </div>
                </div>
                <div className="space-y-1">
                  <h3 className="text-lg font-medium">{watch("name") || "New User"}</h3>
                  <p className="text-sm text-muted-foreground">{watch("email") || "<EMAIL>"}</p>
                  {watch("status") && (
                    <StatusBadge status={watch("status")} showIcon size="sm" />
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className={errors.name ? "text-destructive" : ""}>
                    Full Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    {...register("name", {
                      required: "Name is required"
                    })}
                    ref={firstInputRef}
                    aria-invalid={errors.name ? "true" : "false"}
                    className={errors.name ? "border-destructive" : ""}
                  />
                  {errors.name && (
                    <p className="text-xs text-destructive mt-1">{errors.name.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email" className={errors.email ? "text-destructive" : ""}>
                    Email Address <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    {...register("email", {
                      required: "Email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address"
                      }
                    })}
                    aria-invalid={errors.email ? "true" : "false"}
                    className={errors.email ? "border-destructive" : ""}
                  />
                  {errors.email && (
                    <p className="text-xs text-destructive mt-1">{errors.email.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    type="tel"
                    {...register("phoneNumber")}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employeeId" className={errors.employeeId ? "text-destructive" : ""}>
                    Employee ID <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="employeeId"
                    {...register("employeeId", {
                      required: "Employee ID is required"
                    })}
                    aria-invalid={errors.employeeId ? "true" : "false"}
                    className={errors.employeeId ? "border-destructive" : ""}
                  />
                  {errors.employeeId && (
                    <p className="text-xs text-destructive mt-1">{errors.employeeId.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">
                    Department <span className="text-red-500">*</span>
                  </Label>
                  <Controller
                    name="department"
                    control={control}
                    rules={{ required: "Department is required" }}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger id="department">
                          <SelectValue placeholder="Select department" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="IT">IT</SelectItem>
                          <SelectItem value="Maintenance">Maintenance</SelectItem>
                          <SelectItem value="Operations">Operations</SelectItem>
                          <SelectItem value="Management">Management</SelectItem>
                          <SelectItem value="Customer Service">Customer Service</SelectItem>
                          <SelectItem value="Finance">Finance</SelectItem>
                          <SelectItem value="HR">HR</SelectItem>
                          <SelectItem value="Audit">Audit</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.department && (
                    <p className="text-xs text-destructive mt-1">{errors.department.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="position">
                    Position <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="position"
                    {...register("position", {
                      required: "Position is required"
                    })}
                    aria-invalid={errors.position ? "true" : "false"}
                    className={errors.position ? "border-destructive" : ""}
                    placeholder="e.g. Senior Technician"
                  />
                  {errors.position && (
                    <p className="text-xs text-destructive mt-1">{errors.position.message}</p>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="role" className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="role">
                    User Role <span className="text-red-500">*</span>
                  </Label>
                  <Controller
                    name="role"
                    control={control}
                    rules={{ required: "Role is required" }}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger id="role">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Administrator">Administrator</SelectItem>
                          <SelectItem value="Manager">Manager</SelectItem>
                          <SelectItem value="Supervisor">Supervisor</SelectItem>
                          <SelectItem value="Technician">Technician</SelectItem>
                          <SelectItem value="Operator">Operator</SelectItem>
                          <SelectItem value="Viewer">Viewer</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.role && (
                    <p className="text-xs text-destructive mt-1">{errors.role.message}</p>
                  )}
                  <p className="text-xs text-muted-foreground mt-1">
                    {watch("role") === "Administrator" && "Full access to all system features and settings."}
                    {watch("role") === "Manager" && "Department-level management with approval capabilities."}
                    {watch("role") === "Supervisor" && "Team management and oversight responsibilities."}
                    {watch("role") === "Technician" && "Access to maintenance tasks and field operations."}
                    {watch("role") === "Operator" && "Monitoring and operational control capabilities."}
                    {watch("role") === "Viewer" && "Read-only access to system data and reports."}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Permissions</Label>
                  <div className="grid grid-cols-2 gap-2 border rounded-md p-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="view-users"
                        defaultChecked={["Administrator", "Manager", "Supervisor"].includes(watch("role"))}
                      />
                      <label
                        htmlFor="view-users"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        View Users
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="manage-users"
                        defaultChecked={["Administrator", "Manager"].includes(watch("role"))}
                      />
                      <label
                        htmlFor="manage-users"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Manage Users
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="view-reports"
                        defaultChecked={["Administrator", "Manager", "Supervisor", "Operator", "Viewer"].includes(watch("role"))}
                      />
                      <label
                        htmlFor="view-reports"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        View Reports
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="manage-reports"
                        defaultChecked={["Administrator", "Manager"].includes(watch("role"))}
                      />
                      <label
                        htmlFor="manage-reports"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Manage Reports
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="view-maintenance"
                        defaultChecked={["Administrator", "Manager", "Supervisor", "Technician", "Operator"].includes(watch("role"))}
                      />
                      <label
                        htmlFor="view-maintenance"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        View Maintenance
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="manage-maintenance"
                        defaultChecked={["Administrator", "Manager", "Technician"].includes(watch("role"))}
                      />
                      <label
                        htmlFor="manage-maintenance"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Manage Maintenance
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="approve-actions"
                        defaultChecked={["Administrator", "Manager", "Supervisor"].includes(watch("role"))}
                      />
                      <label
                        htmlFor="approve-actions"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Approve Actions
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="system-settings"
                        defaultChecked={["Administrator"].includes(watch("role"))}
                      />
                      <label
                        htmlFor="system-settings"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        System Settings
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="location" className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="organizationalLevel">
                    Organizational Level <span className="text-red-500">*</span>
                  </Label>
                  <Controller
                    name="organizationalLevel"
                    control={control}
                    rules={{ required: "Organizational level is required" }}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger id="organizationalLevel">
                          <SelectValue placeholder="Select level" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="head_office">
                            <div className="flex items-center">
                              <Building className="mr-2 h-4 w-4 text-blue-500" />
                              Head Office
                            </div>
                          </SelectItem>
                          <SelectItem value="regional_office">
                            <div className="flex items-center">
                              <MapPin className="mr-2 h-4 w-4 text-green-500" />
                              Regional Office
                            </div>
                          </SelectItem>
                          <SelectItem value="service_center">
                            <div className="flex items-center">
                              <Briefcase className="mr-2 h-4 w-4 text-amber-500" />
                              Service Center
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.organizationalLevel && (
                    <p className="text-xs text-destructive mt-1">{errors.organizationalLevel.message}</p>
                  )}
                </div>

                {watch("organizationalLevel") !== "head_office" && (
                  <div className="space-y-2">
                    <Label htmlFor="regionId">
                      Region {watch("organizationalLevel") !== "head_office" && <span className="text-red-500">*</span>}
                    </Label>
                    <Controller
                      name="regionId"
                      control={control}
                      rules={{
                        required: watch("organizationalLevel") !== "head_office" ? "Region is required" : false
                      }}
                      render={({ field }) => (
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger id="regionId">
                            <SelectValue placeholder="Select region" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="REG-001">Addis Ababa</SelectItem>
                            <SelectItem value="REG-002">Oromia</SelectItem>
                            <SelectItem value="REG-003">Amhara</SelectItem>
                            <SelectItem value="REG-004">Tigray</SelectItem>
                            <SelectItem value="REG-005">SNNPR</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.regionId && (
                      <p className="text-xs text-destructive mt-1">{errors.regionId.message}</p>
                    )}
                  </div>
                )}

                {watch("organizationalLevel") === "service_center" && watch("regionId") && (
                  <div className="space-y-2">
                    <Label htmlFor="serviceCenterId">
                      Service Center <span className="text-red-500">*</span>
                    </Label>
                    <Controller
                      name="serviceCenterId"
                      control={control}
                      rules={{
                        required: watch("organizationalLevel") === "service_center" ? "Service center is required" : false
                      }}
                      render={({ field }) => (
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger id="serviceCenterId">
                            <SelectValue placeholder="Select service center" />
                          </SelectTrigger>
                          <SelectContent>
                            {watch("regionId") === "REG-001" && (
                              <>
                                <SelectItem value="SVC-001">Bole Service Center</SelectItem>
                                <SelectItem value="SVC-002">Megenagna Service Center</SelectItem>
                              </>
                            )}
                            {watch("regionId") === "REG-002" && (
                              <>
                                <SelectItem value="SVC-003">Adama Service Center</SelectItem>
                                <SelectItem value="SVC-004">Bishoftu Service Center</SelectItem>
                              </>
                            )}
                            {watch("regionId") === "REG-003" && (
                              <>
                                <SelectItem value="SVC-005">Bahir Dar Service Center</SelectItem>
                                <SelectItem value="SVC-006">Gondar Service Center</SelectItem>
                              </>
                            )}
                            {watch("regionId") === "REG-004" && (
                              <SelectItem value="SVC-007">Mekelle Service Center</SelectItem>
                            )}
                            {watch("regionId") === "REG-005" && (
                              <SelectItem value="SVC-008">Hawassa Service Center</SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.serviceCenterId && (
                      <p className="text-xs text-destructive mt-1">{errors.serviceCenterId.message}</p>
                    )}
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="hireDate">Hire Date</Label>
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="hireDate"
                      type="date"
                      {...register("hireDate")}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    {...register("address")}
                    placeholder="Physical address"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="emergencyContact">Emergency Contact</Label>
                  <Input
                    id="emergencyContact"
                    {...register("emergencyContact")}
                    placeholder="Name and phone number"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="account" className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Account Status</Label>
                  <Controller
                    name="status"
                    control={control}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger id="status">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Active">
                            <div className="flex items-center">
                              <UserCheck className="mr-2 h-4 w-4 text-green-500" />
                              Active
                            </div>
                          </SelectItem>
                          <SelectItem value="Inactive">
                            <div className="flex items-center">
                              <UserX className="mr-2 h-4 w-4 text-slate-500" />
                              Inactive
                            </div>
                          </SelectItem>
                          <SelectItem value="Pending">
                            <div className="flex items-center">
                              <Clock className="mr-2 h-4 w-4 text-yellow-500" />
                              Pending
                            </div>
                          </SelectItem>
                          <SelectItem value="Suspended">
                            <div className="flex items-center">
                              <AlertTriangle className="mr-2 h-4 w-4 text-red-500" />
                              Suspended
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>

                {!user && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="password" className={errors.password ? "text-destructive" : ""}>
                        Password {!user && <span className="text-red-500">*</span>}
                      </Label>
                      <Input
                        id="password"
                        type="password"
                        {...register("password", {
                          required: !user ? "Password is required" : false,
                          minLength: {
                            value: 8,
                            message: "Password must be at least 8 characters"
                          }
                        })}
                        aria-invalid={errors.password ? "true" : "false"}
                        className={errors.password ? "border-destructive" : ""}
                      />
                      {errors.password && (
                        <p className="text-xs text-destructive mt-1">{errors.password.message}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword" className={errors.confirmPassword ? "text-destructive" : ""}>
                        Confirm Password {!user && <span className="text-red-500">*</span>}
                      </Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        {...register("confirmPassword", {
                          required: !user ? "Please confirm your password" : false,
                          validate: value =>
                            !user ? value === password || "Passwords do not match" : true
                        })}
                        aria-invalid={errors.confirmPassword ? "true" : "false"}
                        className={errors.confirmPassword ? "border-destructive" : ""}
                      />
                      {errors.confirmPassword && (
                        <p className="text-xs text-destructive mt-1">{errors.confirmPassword.message}</p>
                      )}
                    </div>
                  </>
                )}

                <div className="space-y-2 pt-4 border-t">
                  <Label htmlFor="skills" className="flex items-center">
                    <Award className="mr-2 h-4 w-4 text-muted-foreground" />
                    Skills & Qualifications
                  </Label>
                  <div className="grid grid-cols-2 gap-2 border rounded-md p-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="skill-electrical" />
                      <label
                        htmlFor="skill-electrical"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Electrical Systems
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="skill-mechanical" />
                      <label
                        htmlFor="skill-mechanical"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Mechanical Systems
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="skill-safety" />
                      <label
                        htmlFor="skill-safety"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Safety Protocols
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="skill-troubleshooting" />
                      <label
                        htmlFor="skill-troubleshooting"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Troubleshooting
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="skill-scada" />
                      <label
                        htmlFor="skill-scada"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        SCADA Systems
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="skill-project" />
                      <label
                        htmlFor="skill-project"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Project Management
                      </label>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="certifications" className="flex items-center">
                    <GraduationCap className="mr-2 h-4 w-4 text-muted-foreground" />
                    Certifications
                  </Label>
                  <div className="grid grid-cols-2 gap-2 border rounded-md p-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="cert-electrical" />
                      <label
                        htmlFor="cert-electrical"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Electrical Safety
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="cert-high-voltage" />
                      <label
                        htmlFor="cert-high-voltage"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        High Voltage Operations
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="cert-control-room" />
                      <label
                        htmlFor="cert-control-room"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Control Room Operations
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="cert-emergency" />
                      <label
                        htmlFor="cert-emergency"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Emergency Management
                      </label>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes" className="flex items-center">
                    <ClipboardList className="mr-2 h-4 w-4 text-muted-foreground" />
                    Notes
                  </Label>
                  <Textarea
                    id="notes"
                    {...register("notes")}
                    placeholder="Additional notes about this user..."
                    className="min-h-[100px]"
                  />
                </div>

                {user && (
                  <div className="space-y-2">
                    <Label htmlFor="resetPassword">Reset Password</Label>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleSendPasswordReset}
                      >
                        <Mail className="mr-2 h-4 w-4" />
                        Send Reset Link
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleGenerateTemporaryPassword}
                      >
                        <Key className="mr-2 h-4 w-4" />
                        Generate Temporary Password
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              {user ? "Update User" : "Create User"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
