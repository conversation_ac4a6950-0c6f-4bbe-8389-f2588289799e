"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from '@/src/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Badge } from '@/src/components/ui/badge'
import { UnifiedTransformerManagement } from './UnifiedTransformerManagement'
import {
  FileText,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Download,
  Upload,
  ClipboardList,
  TestTube,
  Zap,
  Calendar,
  MapPin,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users
} from 'lucide-react'

interface TransformerRecord {
  id: string
  cardNo: string
  substationName: string
  transformerCode: string
  kvaRating: number
  status: 'active' | 'maintenance' | 'damaged' | 'retired'
  lastInspection: string
  lastMeggerTest: string
  location: string
  manufacturer: string
}

// Mock data for demonstration
const mockTransformers: TransformerRecord[] = [
  {
    id: '1',
    cardNo: 'TRC-001',
    substationName: 'Addis Ababa Central',
    transformerCode: 'TR-AA-001',
    kvaRating: 500,
    status: 'active',
    lastInspection: '2024-01-15',
    lastMeggerTest: '2024-01-10',
    location: 'Addis Ababa, Kirkos',
    manufacturer: 'ABB'
  },
  {
    id: '2',
    cardNo: 'TRC-002',
    substationName: 'Bahir Dar North',
    transformerCode: 'TR-BD-002',
    kvaRating: 315,
    status: 'maintenance',
    lastInspection: '2024-01-12',
    lastMeggerTest: '2024-01-08',
    location: 'Bahir Dar, Zone 1',
    manufacturer: 'Siemens'
  },
  {
    id: '3',
    cardNo: 'TRC-003',
    substationName: 'Hawassa Industrial',
    transformerCode: 'TR-HW-003',
    kvaRating: 800,
    status: 'damaged',
    lastInspection: '2024-01-05',
    lastMeggerTest: '2023-12-28',
    location: 'Hawassa, Industrial Zone',
    manufacturer: 'Schneider Electric'
  }
]

export function TransformerManagementIntegration() {
  const [transformers, setTransformers] = useState<TransformerRecord[]>(mockTransformers)
  const [selectedTransformer, setSelectedTransformer] = useState<string | null>(null)
  const [showUnifiedForm, setShowUnifiedForm] = useState(false)
  const [initialTab, setInitialTab] = useState<'inventory' | 'inspection' | 'megger-test'>('inventory')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // Filter transformers based on search and status
  const filteredTransformers = transformers.filter(transformer => {
    const matchesSearch = transformer.substationName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transformer.transformerCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transformer.cardNo.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || transformer.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'maintenance': return 'bg-yellow-100 text-yellow-800'
      case 'damaged': return 'bg-red-100 text-red-800'
      case 'retired': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />
      case 'maintenance': return <Clock className="h-4 w-4" />
      case 'damaged': return <AlertTriangle className="h-4 w-4" />
      case 'retired': return <Trash2 className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const handleOpenForm = (transformerId?: string, tab: 'inventory' | 'inspection' | 'megger-test' = 'inventory') => {
    setSelectedTransformer(transformerId || null)
    setInitialTab(tab)
    setShowUnifiedForm(true)
  }

  const handleCloseForm = () => {
    setShowUnifiedForm(false)
    setSelectedTransformer(null)
  }

  // Enhanced functionality handlers
  const handleImportData = () => {
    console.log('Import data functionality triggered')
    // Create file input element
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.csv,.xlsx,.xls'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        console.log('Selected file:', file.name)
        alert(`Import functionality would process file: ${file.name}\n\nSupported formats:\n- CSV (.csv)\n- Excel (.xlsx, .xls)\n\nThis would import transformer data into the system.`)
      }
    }
    input.click()
  }

  const handleExportData = () => {
    console.log('Export data functionality triggered')

    // Prepare data for export
    const exportData = filteredTransformers.map(transformer => ({
      'Card Number': transformer.cardNo,
      'Substation Name': transformer.substationName,
      'Transformer Code': transformer.transformerCode,
      'KVA Rating': transformer.kvaRating,
      'Status': transformer.status,
      'Location': transformer.location,
      'Manufacturer': transformer.manufacturer,
      'Last Inspection': transformer.lastInspection,
      'Last Megger Test': transformer.lastMeggerTest
    }))

    // Convert to CSV
    const headers = Object.keys(exportData[0]).join(',')
    const rows = exportData.map(row => Object.values(row).join(','))
    const csvContent = [headers, ...rows].join('\n')

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `transformer_management_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
    window.URL.revokeObjectURL(url)

    alert(`Exported ${filteredTransformers.length} transformer records to CSV file`)
  }

  const handleViewDetails = (transformerId: string) => {
    console.log('🔍 View details clicked for transformer:', transformerId)
    const transformer = transformers.find(t => t.id === transformerId)
    if (transformer) {
      console.log('✅ Transformer found:', transformer)
      alert(`🔍 Transformer Details:\n\nCard No: ${transformer.cardNo}\nSubstation: ${transformer.substationName}\nCode: ${transformer.transformerCode}\nRating: ${transformer.kvaRating} KVA\nStatus: ${transformer.status}\nLocation: ${transformer.location}\nManufacturer: ${transformer.manufacturer}\nLast Inspection: ${transformer.lastInspection}\nLast Megger Test: ${transformer.lastMeggerTest}`)
    } else {
      console.log('❌ Transformer not found for ID:', transformerId)
      alert('❌ Transformer not found!')
    }
  }

  const handleEditTransformer = (transformerId: string) => {
    console.log('✏️ Edit transformer clicked for:', transformerId)
    handleOpenForm(transformerId, 'inventory')
  }

  const handleDeleteTransformer = (transformerId: string) => {
    console.log('🗑️ Delete transformer clicked for:', transformerId)
    const transformer = transformers.find(t => t.id === transformerId)
    if (transformer) {
      const confirmed = window.confirm(`🗑️ Are you sure you want to delete transformer "${transformer.cardNo}" from ${transformer.substationName}?\n\nThis action cannot be undone.`)
      if (confirmed) {
        setTransformers(prev => prev.filter(t => t.id !== transformerId))
        alert(`✅ Transformer "${transformer.cardNo}" has been deleted successfully.`)
      }
    }
  }

  const handleViewOnMap = (transformerId: string) => {
    console.log('🗺️ View on map clicked for transformer:', transformerId)
    const transformer = transformers.find(t => t.id === transformerId)
    if (transformer) {
      console.log('✅ Opening map for transformer:', transformer.cardNo, 'at location:', transformer.location)
      // Open map view with transformer highlighted
      window.open(`/transformers/map?highlight=${transformerId}&location=${encodeURIComponent(transformer.location)}`, '_blank')
    }
  }

  const handleScheduleInspection = (transformerId: string) => {
    console.log('📅 Schedule inspection clicked for transformer:', transformerId)
    const transformer = transformers.find(t => t.id === transformerId)
    if (transformer) {
      const date = prompt(`📅 Schedule inspection for transformer "${transformer.cardNo}":\n\nEnter inspection date (YYYY-MM-DD):`)
      if (date) {
        alert(`✅ Inspection scheduled for ${transformer.cardNo} on ${date}.\n\nA notification will be sent to the maintenance team.`)
      }
    }
  }

  const handleScheduleMeggerTest = (transformerId: string) => {
    console.log('Schedule megger test for transformer:', transformerId)
    const transformer = transformers.find(t => t.id === transformerId)
    if (transformer) {
      const date = prompt(`Schedule megger test for transformer "${transformer.cardNo}":\n\nEnter test date (YYYY-MM-DD):`)
      if (date) {
        alert(`Megger test scheduled for ${transformer.cardNo} on ${date}.\n\nTest equipment and technician will be assigned.`)
      }
    }
  }

  const handlePrintReport = (transformerId?: string) => {
    console.log('Print report for transformer:', transformerId || 'all')
    if (transformerId) {
      const transformer = transformers.find(t => t.id === transformerId)
      if (transformer) {
        alert(`Printing detailed report for transformer "${transformer.cardNo}"...\n\nReport includes:\n- Inventory details\n- Inspection history\n- Megger test results\n- Maintenance records`)
      }
    } else {
      alert(`Printing summary report for ${filteredTransformers.length} transformers...\n\nReport includes:\n- Statistics overview\n- Status distribution\n- Recent activities\n- Maintenance schedule`)
    }
    // In a real implementation, this would generate and print a PDF report
    window.print()
  }

  const handleBulkAction = (action: string) => {
    console.log('Bulk action:', action)
    const selectedCount = filteredTransformers.length

    switch (action) {
      case 'export':
        handleExportData()
        break
      case 'schedule_inspection':
        const inspectionDate = prompt(`Schedule inspection for ${selectedCount} transformers:\n\nEnter inspection date (YYYY-MM-DD):`)
        if (inspectionDate) {
          alert(`Inspection scheduled for ${selectedCount} transformers on ${inspectionDate}.\n\nNotifications sent to maintenance teams.`)
        }
        break
      case 'schedule_megger':
        const meggerDate = prompt(`Schedule megger tests for ${selectedCount} transformers:\n\nEnter test date (YYYY-MM-DD):`)
        if (meggerDate) {
          alert(`Megger tests scheduled for ${selectedCount} transformers on ${meggerDate}.\n\nTest equipment and technicians assigned.`)
        }
        break
      case 'print_report':
        handlePrintReport()
        break
      default:
        alert(`Bulk action "${action}" would be applied to ${selectedCount} transformers.`)
    }
  }

  // Memoized handlers to prevent re-renders
  const handleTestClick = () => {
    console.log('🧪 Test button clicked!')
    alert('🧪 Test button is working! All icons should be functional.')
  }

  const handleClearFilters = () => {
    setSearchTerm('')
    setStatusFilter('all')
  }

  const handleMapView = () => {
    window.open('/transformers/map', '_blank')
  }

  const handleNewRecord = () => {
    handleOpenForm()
  }

  const handleStatusFilterAll = () => setStatusFilter('all')
  const handleStatusFilterActive = () => setStatusFilter('active')
  const handleStatusFilterMaintenance = () => setStatusFilter('maintenance')
  const handleStatusFilterDamaged = () => setStatusFilter('damaged')

  // Bulk action handlers
  const handleBulkScheduleInspection = () => handleBulkAction('schedule_inspection')
  const handleBulkScheduleMegger = () => handleBulkAction('schedule_megger')
  const handleBulkExport = () => handleBulkAction('export')
  const handleBulkPrintReport = () => handleBulkAction('print_report')

  // Create memoized handlers for table actions to prevent re-renders
  const createActionHandler = (action: string, transformerId: string) => {
    switch (action) {
      case 'inventory':
        return () => handleOpenForm(transformerId, 'inventory')
      case 'inspection':
        return () => handleOpenForm(transformerId, 'inspection')
      case 'megger-test':
        return () => handleOpenForm(transformerId, 'megger-test')
      case 'view-details':
        return () => handleViewDetails(transformerId)
      case 'view-map':
        return () => handleViewOnMap(transformerId)
      case 'schedule-inspection':
        return () => handleScheduleInspection(transformerId)
      case 'edit':
        return () => handleEditTransformer(transformerId)
      case 'delete':
        return () => handleDeleteTransformer(transformerId)
      default:
        return () => console.log('Unknown action:', action)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Transformer Management</h1>
          <p className="text-gray-600">Unified inventory, inspection, and megger test management</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleImportData}>
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportData}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={handlePrintReport}>
            <FileText className="h-4 w-4 mr-2" />
            Print Report
          </Button>
          <Button onClick={handleNewRecord} className="bg-green-600 hover:bg-green-700">
            <Plus className="h-4 w-4 mr-2" />
            New Record
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleTestClick}
            className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
          >
            🧪 Test
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleStatusFilterAll}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Transformers</p>
                <p className="text-2xl font-bold text-gray-900">{transformers.length}</p>
                <p className="text-xs text-gray-500 mt-1">Click to view all</p>
              </div>
              <Zap className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleStatusFilterActive}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold text-green-600">
                  {transformers.filter(t => t.status === 'active').length}
                </p>
                <p className="text-xs text-gray-500 mt-1">Click to filter</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleStatusFilterMaintenance}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Under Maintenance</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {transformers.filter(t => t.status === 'maintenance').length}
                </p>
                <p className="text-xs text-gray-500 mt-1">Click to filter</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleStatusFilterDamaged}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Damaged</p>
                <p className="text-2xl font-bold text-red-600">
                  {transformers.filter(t => t.status === 'damaged').length}
                </p>
                <p className="text-xs text-gray-500 mt-1">Click to filter</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search by substation, transformer code, or card number..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="maintenance">Maintenance</option>
                <option value="damaged">Damaged</option>
                <option value="retired">Retired</option>
              </select>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearFilters}
                title="Clear all filters"
              >
                Clear Filters
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleMapView}
                title="View all transformers on map"
              >
                <MapPin className="h-4 w-4 mr-2" />
                Map View
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Transformers List */}
      <Card>
        <CardHeader>
          <CardTitle>Transformer Records</CardTitle>
          <CardDescription>
            Manage transformer inventory, inspections, and test results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium">Card No.</th>
                  <th className="text-left py-3 px-4 font-medium">Substation</th>
                  <th className="text-left py-3 px-4 font-medium">Transformer Code</th>
                  <th className="text-left py-3 px-4 font-medium">KVA Rating</th>
                  <th className="text-left py-3 px-4 font-medium">Status</th>
                  <th className="text-left py-3 px-4 font-medium">Location</th>
                  <th className="text-left py-3 px-4 font-medium">Last Inspection</th>
                  <th className="text-left py-3 px-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredTransformers.map((transformer) => (
                  <tr key={transformer.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4 font-medium">{transformer.cardNo}</td>
                    <td className="py-3 px-4">{transformer.substationName}</td>
                    <td className="py-3 px-4">{transformer.transformerCode}</td>
                    <td className="py-3 px-4">{transformer.kvaRating} KVA</td>
                    <td className="py-3 px-4">
                      <Badge className={`${getStatusColor(transformer.status)} flex items-center gap-1 w-fit`}>
                        {getStatusIcon(transformer.status)}
                        {transformer.status}
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-600">{transformer.location}</td>
                    <td className="py-3 px-4 text-sm text-gray-600">{transformer.lastInspection}</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={createActionHandler('inventory', transformer.id)}
                          title="View/Edit Inventory"
                        >
                          <Zap className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={createActionHandler('inspection', transformer.id)}
                          title="Component Inspection"
                        >
                          <ClipboardList className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={createActionHandler('megger-test', transformer.id)}
                          title="Megger Test"
                        >
                          <TestTube className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={createActionHandler('view-details', transformer.id)}
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={createActionHandler('view-map', transformer.id)}
                          title="View on Map"
                        >
                          <MapPin className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={createActionHandler('schedule-inspection', transformer.id)}
                          title="Schedule Inspection"
                        >
                          <Calendar className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={createActionHandler('edit', transformer.id)}
                          title="Edit Transformer"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={createActionHandler('delete', transformer.id)}
                          title="Delete Transformer"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredTransformers.length === 0 && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No transformers found matching your criteria</p>
            </div>
          )}

          {/* Bulk Actions */}
          {filteredTransformers.length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Showing {filteredTransformers.length} transformer{filteredTransformers.length !== 1 ? 's' : ''}
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Bulk Actions:</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkScheduleInspection}
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule Inspections
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkScheduleMegger}
                  >
                    <TestTube className="h-4 w-4 mr-2" />
                    Schedule Megger Tests
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkExport}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export Selected
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkPrintReport}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Print Reports
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Unified Form Modal */}
      {showUnifiedForm && (
        <UnifiedTransformerManagement
          transformerId={selectedTransformer || undefined}
          onClose={handleCloseForm}
          initialTab={initialTab}
        />
      )}
    </div>
  )
}
