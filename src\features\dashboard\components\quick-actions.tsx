"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { 
  Plus, 
  Search, 
  Download, 
  Upload, 
  Settings, 
  RefreshCw,
  FileText,
  Users,
  MapPin,
  BarChart3,
  Zap,
  Wrench
} from "lucide-react"

export function QuickActions() {
  const quickActions = [
    {
      title: "Add Transformer",
      description: "Register new transformer",
      icon: Plus,
      action: () => console.log("Add transformer"),
      variant: "default" as const,
      badge: "New"
    },
    {
      title: "Search Assets",
      description: "Find transformers & equipment",
      icon: Search,
      action: () => console.log("Search assets"),
      variant: "outline" as const
    },
    {
      title: "Generate Report",
      description: "Create maintenance report",
      icon: FileText,
      action: () => console.log("Generate report"),
      variant: "outline" as const
    },
    {
      title: "Schedule Maintenance",
      description: "Plan maintenance tasks",
      icon: Wrench,
      action: () => console.log("Schedule maintenance"),
      variant: "outline" as const,
      badge: "Priority"
    },
    {
      title: "View Analytics",
      description: "Performance insights",
      icon: BarChart3,
      action: () => console.log("View analytics"),
      variant: "outline" as const
    },
    {
      title: "Export Data",
      description: "Download system data",
      icon: Download,
      action: () => console.log("Export data"),
      variant: "outline" as const
    }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Quick Actions
        </CardTitle>
        <CardDescription>
          Frequently used operations and shortcuts
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {quickActions.map((action, index) => {
            const IconComponent = action.icon
            return (
              <div key={index} className="relative">
                <Button
                  variant={action.variant}
                  className="w-full h-auto p-4 flex flex-col items-center gap-2 text-center"
                  onClick={action.action}
                >
                  <IconComponent className="h-6 w-6" />
                  <div>
                    <div className="font-medium">{action.title}</div>
                    <div className="text-xs text-muted-foreground">
                      {action.description}
                    </div>
                  </div>
                </Button>
                {action.badge && (
                  <Badge 
                    variant="secondary" 
                    className="absolute -top-2 -right-2 text-xs"
                  >
                    {action.badge}
                  </Badge>
                )}
              </div>
            )
          })}
        </div>
        
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              System Status: <Badge variant="outline" className="text-green-600">Operational</Badge>
            </div>
            <Button variant="ghost" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
