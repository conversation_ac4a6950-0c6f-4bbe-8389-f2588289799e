import { NextRequest, NextResponse } from 'next/server'

/**
 * Comprehensive Transformer Inventory Data Seeding API
 *
 * This endpoint seeds realistic transformer inventory data that is consistent
 * across all modules including monitoring, diagnostics, load management, and lifecycle.
 */

// Ethiopian regions and service centers
const ethiopianData = {
  regions: [
    { id: 'region-aa', name: 'Addis Ababa', code: 'AA' },
    { id: 'region-or', name: 'Oromia', code: 'OR' },
    { id: 'region-am', name: 'Amhara', code: 'AM' },
    { id: 'region-ti', name: 'Tigray', code: 'TI' },
    { id: 'region-sn', name: 'SNNP', code: 'SN' },
    { id: 'region-so', name: 'Somali', code: 'SO' },
    { id: 'region-af', name: 'Afar', code: 'AF' }
  ],
  serviceCenters: [
    { id: 'sc-001', name: 'Addis Ababa Central', regionId: 'region-aa', city: 'Addis Ababa' },
    { id: 'sc-002', name: 'Dire Dawa Service Center', regionId: 'region-or', city: 'Dire Dawa' },
    { id: 'sc-003', name: 'Mekelle Service Center', regionId: 'region-ti', city: 'Mekelle' },
    { id: 'sc-004', name: 'Bahir Dar Service Center', regionId: 'region-am', city: 'Bahir Dar' },
    { id: 'sc-005', name: 'Hawassa Service Center', regionId: 'region-sn', city: 'Hawassa' },
    { id: 'sc-006', name: 'Jimma Service Center', regionId: 'region-or', city: 'Jimma' },
    { id: 'sc-007', name: 'Gondar Service Center', regionId: 'region-am', city: 'Gondar' },
    { id: 'sc-008', name: 'Dessie Service Center', regionId: 'region-am', city: 'Dessie' },
    { id: 'sc-009', name: 'Adama Service Center', regionId: 'region-or', city: 'Adama' },
    { id: 'sc-010', name: 'Harar Service Center', regionId: 'region-or', city: 'Harar' }
  ]
}

// Transformer specifications
const transformerSpecs = {
  manufacturers: [
    { name: 'ABB', country: 'Switzerland', models: ['PowerTrans X3', 'PowerTrans X2', 'GridMax Pro', 'EcoMax'] },
    { name: 'Siemens', country: 'Germany', models: ['TransMax 2000', 'TransMax 3000', 'PowerGrid Elite', 'EfficientTrans'] },
    { name: 'Schneider Electric', country: 'France', models: ['GridTech Pro', 'GridTech Standard', 'EcoGrid Plus', 'PowerLink'] },
    { name: 'General Electric', country: 'USA', models: ['PowerCore 500', 'PowerCore 1000', 'GridSmart Pro', 'EnergyMax'] },
    { name: 'Hyundai Heavy Industries', country: 'South Korea', models: ['HyPower 750', 'HyPower 1500', 'GridForce', 'PowerLink HD'] },
    { name: 'TBEA', country: 'China', models: ['PowerGrid 800', 'EfficientGrid', 'SmartTrans Pro', 'GridMax'] }
  ],
  types: ['Distribution', 'Power', 'Instrument', 'Auto', 'Isolation'],
  voltageRatings: [
    { primary: 15000, secondary: 400, type: 'Distribution' },
    { primary: 33000, secondary: 11000, type: 'Distribution' },
    { primary: 66000, secondary: 15000, type: 'Sub-transmission' },
    { primary: 132000, secondary: 33000, type: 'Transmission' },
    { primary: 230000, secondary: 66000, type: 'Transmission' },
    { primary: 400000, secondary: 132000, type: 'Extra High Voltage' }
  ],
  capacities: [50, 100, 160, 250, 315, 400, 500, 630, 800, 1000, 1250, 1600, 2000, 2500, 3150, 4000, 5000]
}

// Ethiopian technician names
const ethiopianNames = {
  first: ['Abebe', 'Almaz', 'Bekele', 'Chaltu', 'Dawit', 'Emebet', 'Fikru', 'Genet', 'Haile', 'Iyasu', 'Kassa', 'Lemlem', 'Meron', 'Negash', 'Rahel', 'Selamawit', 'Tadesse', 'Tigist', 'Worku', 'Yohannes', 'Zenebech', 'Mulugeta', 'Birtukan', 'Getachew', 'Hiwot'],
  last: ['Abera', 'Bekele', 'Chala', 'Desta', 'Eshetu', 'Fanta', 'Girma', 'Hailu', 'Kebede', 'Lemma', 'Mekonnen', 'Negash', 'Regassa', 'Sisay', 'Tadesse', 'Wolde', 'Yimer', 'Zenebe', 'Tesfaye', 'Mulatu']
}

// Helper functions
function generateEthiopianName() {
  const firstName = ethiopianNames.first[Math.floor(Math.random() * ethiopianNames.first.length)]
  const lastName = ethiopianNames.last[Math.floor(Math.random() * ethiopianNames.last.length)]
  return `${firstName} ${lastName}`
}

function generateEthiopianCoordinates(city: string) {
  const cityCoords: { [key: string]: { lat: number, lng: number } } = {
    'Addis Ababa': { lat: 9.0320, lng: 38.7469 },
    'Dire Dawa': { lat: 9.5930, lng: 41.8660 },
    'Mekelle': { lat: 13.4967, lng: 39.4753 },
    'Bahir Dar': { lat: 11.5942, lng: 37.3906 },
    'Hawassa': { lat: 7.0621, lng: 38.4776 },
    'Jimma': { lat: 7.6731, lng: 36.8344 },
    'Gondar': { lat: 12.6090, lng: 37.4661 },
    'Dessie': { lat: 11.1300, lng: 39.6330 },
    'Adama': { lat: 8.5400, lng: 39.2690 },
    'Harar': { lat: 9.3140, lng: 42.1180 }
  }

  const baseCoord = cityCoords[city] || { lat: 9.0320, lng: 38.7469 }
  return {
    lat: baseCoord.lat + (Math.random() - 0.5) * 0.1,
    lng: baseCoord.lng + (Math.random() - 0.5) * 0.1
  }
}

function generateSerialNumber(manufacturer: string, year: number, sequence: number) {
  const prefix = manufacturer.substring(0, 3).toUpperCase()
  const yearCode = year.toString().substring(2)
  return `${prefix}-${yearCode}-${String(sequence).padStart(4, '0')}`
}

function generateTransformerName(serviceCenter: string, sequence: number) {
  const centerName = serviceCenter.replace(' Service Center', '').replace(' Central', '')
  return `${centerName} Transformer ${String.fromCharCode(65 + (sequence % 26))}`
}

// Allow GET requests for easier testing
export async function GET(request: NextRequest) {
  return POST(request)
}

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Starting comprehensive transformer inventory seeding...')

    // Import database connection utility
    const { executeQuery } = await import('../../../lib/mysql-connection')

    // Test connection first
    const connectionTest = await executeQuery('SELECT 1 as test')
    console.log('✅ Database connection verified')

    // Check if table exists, create if not
    const tableExists = await executeQuery(`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = 'dtms_eeu_db'
      AND table_name = 'app_transformers'
    `)

    if (tableExists[0].count === 0) {
      console.log('📋 Creating app_transformers table...')
      await executeQuery(`
        CREATE TABLE app_transformers (
          id VARCHAR(50) PRIMARY KEY,
          serial_number VARCHAR(100) UNIQUE NOT NULL,
          name VARCHAR(200) NOT NULL,
          manufacturer VARCHAR(100),
          model VARCHAR(100),
          type VARCHAR(50),
          capacity INT,
          voltage_primary INT,
          voltage_secondary INT,
          manufacture_date DATE,
          installation_date DATE,
          last_maintenance_date DATE,
          next_maintenance_date DATE,
          status VARCHAR(50) DEFAULT 'operational',
          health_index DECIMAL(5,2) DEFAULT 100.00,
          temperature DECIMAL(5,2) DEFAULT 0.00,
          load_percentage DECIMAL(5,2) DEFAULT 0.00,
          oil_level DECIMAL(5,2) DEFAULT 100.00,
          region_id VARCHAR(50),
          service_center_id VARCHAR(50),
          location_address TEXT,
          lat DECIMAL(10,8),
          lng DECIMAL(11,8),
          specifications JSON,
          financial_data JSON,
          maintenance_history JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `)
      console.log('✅ app_transformers table created')
    }

    // Clear existing transformer data
    console.log('🧹 Clearing existing transformer data...')
    await executeQuery('SET FOREIGN_KEY_CHECKS = 0')
    await executeQuery('DELETE FROM app_transformers')
    await executeQuery('SET FOREIGN_KEY_CHECKS = 1')

    // Generate comprehensive transformer inventory
    console.log('⚡ Generating realistic transformer inventory...')
    const transformers = []

    for (let i = 0; i < 150; i++) {
      const manufacturer = transformerSpecs.manufacturers[Math.floor(Math.random() * transformerSpecs.manufacturers.length)]
      const model = manufacturer.models[Math.floor(Math.random() * manufacturer.models.length)]
      const voltageRating = transformerSpecs.voltageRatings[Math.floor(Math.random() * transformerSpecs.voltageRatings.length)]
      const capacity = transformerSpecs.capacities[Math.floor(Math.random() * transformerSpecs.capacities.length)]
      const serviceCenter = ethiopianData.serviceCenters[Math.floor(Math.random() * ethiopianData.serviceCenters.length)]
      const region = ethiopianData.regions.find(r => r.id === serviceCenter.regionId)!

      // Generate manufacturer code for serial number
      const manufacturerCode = manufacturer.name.substring(0, 3).toUpperCase()

      const installationYear = 2010 + Math.floor(Math.random() * 14)
      const manufactureYear = installationYear - Math.floor(Math.random() * 2)
      const coordinates = generateEthiopianCoordinates(serviceCenter.city)

      // Generate realistic status based on age and other factors
      const age = new Date().getFullYear() - installationYear
      let status = 'operational'
      let healthIndex = 85

      if (age > 10) {
        const random = Math.random()
        if (random < 0.1) status = 'critical'
        else if (random < 0.25) status = 'warning'
        else if (random < 0.35) status = 'maintenance'
        healthIndex = 60 + Math.random() * 25
      } else if (age > 5) {
        const random = Math.random()
        if (random < 0.05) status = 'warning'
        else if (random < 0.15) status = 'maintenance'
        healthIndex = 75 + Math.random() * 20
      } else {
        healthIndex = 85 + Math.random() * 15
      }

      // Generate realistic metrics
      const temperature = 35 + Math.random() * 30
      const loadPercentage = 40 + Math.random() * 50
      const oilLevel = 80 + Math.random() * 20
      const efficiency = 94 + Math.random() * 5

      // Generate maintenance dates
      const lastMaintenanceDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
      const nextMaintenanceDate = new Date(lastMaintenanceDate.getTime() + (180 + Math.random() * 180) * 24 * 60 * 60 * 1000)

      const transformer = {
        id: `transformer-${String(i + 1).padStart(3, '0')}`,
        serial_number: generateSerialNumber(manufacturer.name, installationYear, i + 1),
        name: generateTransformerName(serviceCenter.name, i),
        manufacturer: manufacturer.name,
        model: model,
        type: voltageRating.type,
        capacity: capacity,
        voltage_primary: voltageRating.primary,
        voltage_secondary: voltageRating.secondary,

        installation_date: `${installationYear}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        last_maintenance_date: lastMaintenanceDate.toISOString().split('T')[0],
        next_maintenance_date: nextMaintenanceDate.toISOString().split('T')[0],
        status: status,
        health_index: parseFloat(healthIndex.toFixed(1)),
        temperature: parseFloat(temperature.toFixed(1)),
        load_percentage: parseFloat(loadPercentage.toFixed(1)),
        oil_level: parseFloat(oilLevel.toFixed(1)),
        region_id: region.id,
        service_center_id: serviceCenter.id,
        location_address: `${serviceCenter.city} Substation, ${region.name}, Ethiopia`,
        lat: coordinates.lat,
        lng: coordinates.lng,
        specifications: JSON.stringify({
          cooling_type: ['ONAN', 'ONAF', 'OFAF', 'ODAF'][Math.floor(Math.random() * 4)],
          tap_changer: Math.random() > 0.3,
          protection_class: ['IP23', 'IP44', 'IP54'][Math.floor(Math.random() * 3)],
          frequency: 50,
          phases: 3,
          connection_type: ['Dyn11', 'Yyn0', 'Dyn1'][Math.floor(Math.random() * 3)],
          insulation_class: ['A', 'B', 'F', 'H'][Math.floor(Math.random() * 4)],
          cooling_capacity: Math.floor(capacity * 0.1),
          weight: Math.floor(capacity * 2.5),
          oil_volume: Math.floor(capacity * 0.8)
        }),
        efficiency: parseFloat((95.0 + Math.random() * 4.0).toFixed(2)), // 95-99%
        power_factor: parseFloat((0.85 + Math.random() * 0.15).toFixed(2)), // 0.85-1.0
        warranty_expiry: `${installationYear + 5}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        cost: parseFloat((Math.floor((50000 + capacity * 100) * (1 + Math.random() * 0.3))).toFixed(2)),
        depreciation_rate: parseFloat((3.0 + Math.random() * 4.0).toFixed(2)), // 3-7%
        smart_meter_enabled: Math.random() > 0.3 ? 1 : 0, // 70% have smart meters
        iot_device_id: Math.random() > 0.3 ? `IOT-${String(i + 1).padStart(3, '0')}-${manufacturerCode}` : null,
        is_critical: Math.random() > 0.8 ? 1 : 0, // 20% are critical
        tags: JSON.stringify(['power_distribution', 'ethiopian_grid', region.name.toLowerCase().replace(' ', '_')])
      }

      transformers.push(transformer)

      // Insert transformer into database (matching existing schema)
      await executeQuery(
        `INSERT INTO app_transformers (
          id, serial_number, name, manufacturer, model, type, capacity,
          voltage_primary, voltage_secondary, installation_date,
          last_maintenance_date, next_maintenance_date, status, health_index,
          temperature, load_percentage, oil_level, region_id, service_center_id,
          location_address, lat, lng, efficiency, power_factor, warranty_expiry,
          cost, depreciation_rate, smart_meter_enabled, iot_device_id, is_critical,
          tags, specifications, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          transformer.id, transformer.serial_number, transformer.name,
          transformer.manufacturer, transformer.model, transformer.type,
          transformer.capacity, transformer.voltage_primary, transformer.voltage_secondary,
          transformer.installation_date,
          transformer.last_maintenance_date, transformer.next_maintenance_date,
          transformer.status, transformer.health_index, transformer.temperature,
          transformer.load_percentage, transformer.oil_level, transformer.region_id,
          transformer.service_center_id, transformer.location_address,
          transformer.lat, transformer.lng, transformer.efficiency, transformer.power_factor,
          transformer.warranty_expiry, transformer.cost, transformer.depreciation_rate,
          transformer.smart_meter_enabled, transformer.iot_device_id, transformer.is_critical,
          transformer.tags, transformer.specifications
        ]
      )
    }

    console.log(`✅ Successfully seeded ${transformers.length} transformers`)

    return NextResponse.json({
      success: true,
      message: 'Comprehensive transformer inventory seeded successfully',
      data: {
        transformers: transformers.length,
        regions: ethiopianData.regions.length,
        serviceCenters: ethiopianData.serviceCenters.length,
        manufacturers: transformerSpecs.manufacturers.length
      }
    })

  } catch (error) {
    console.error('❌ Error seeding transformer inventory:', error)
    return NextResponse.json(
      {
        error: 'Failed to seed transformer inventory',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
