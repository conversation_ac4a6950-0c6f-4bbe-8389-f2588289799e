"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { UnifiedMap } from "@/components/unified-map"
import { useTransformers } from "@/src/contexts/transformer-context"
import { mapService } from "@/src/services/map-service"
import type { MapLocation } from "@/src/services/map-service"
import { Skeleton } from "@/src/components/ui/skeleton"

interface DashboardTransformerMapProps {
  region?: string;
  timeRange?: string;
}

export function DashboardTransformerMap({ region = 'all', timeRange = '30d' }: DashboardTransformerMapProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [locations, setLocations] = useState<MapLocation[]>([])

  // Try to use the transformer context if available
  let transformerContext: any = null
  try {
    transformerContext = useTransformers()
  } catch (error) {
    // Context not available, we'll load data directly
    console.log("TransformerProvider not available in this context")
  }

  useEffect(() => {
    async function loadMapData() {
      setIsLoading(true)
      try {
        // Always load data directly from the service to ensure we're using the database
        const mapLocations = await mapService.getTransformerLocations(region)
        setLocations(mapLocations)
      } catch (error) {
        console.error("Error loading map data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadMapData()
  }, [region, timeRange])

  const handleMarkerClick = (location: MapLocation) => {
    if (location.data?.id) {
      router.push(`/transformers/${location.data.id}`)
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="text-center">
          <div className="mb-2 h-8 w-8 animate-spin rounded-full border-4 border-teal-600 border-t-transparent mx-auto"></div>
          <p className="text-sm text-muted-foreground">Loading map data...</p>
        </div>
      </div>
    )
  }

  return (
    <UnifiedMap
      locations={locations}
      height="100%"
      onMarkerClick={handleMarkerClick}
      clustered={false} // Disable clustering to show all individual transformers
      showControls={true}
      showLegend={true}
      showFilters={true}
      interactive={true}
      initialZoom={5} // Lower zoom level to show more area and ensure all transformers are visible
      allowFullscreen={true}
      showHeatmap={true}
      showSearch={true}
      showExport={true}
      showMeasurement={true}
      mapStyle="light"
    />
  )
}
