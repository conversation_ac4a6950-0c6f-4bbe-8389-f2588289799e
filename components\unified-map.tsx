"use client"

import { useState, useEffect } from "react"
import { MapLocation } from "@/src/services/map-service"
import { LeafletMap } from "@/components/leaflet-map"
import { MapboxMap } from "@/components/mapbox-map"
import { TransformerListMap } from "@/components/transformer-list-map"
import { Button } from "@/src/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Badge } from "@/src/components/ui/badge"
import {
  Map,
  Layers,
  Search,
  Download,
  Maximize2,
  Minimize2,
  Filter,
  BarChart3,
  Ruler,
  Locate,
  Settings
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Input } from "@/src/components/ui/input"
import { Switch } from "@/src/components/ui/switch"
import { Label } from "@/src/components/ui/label"

interface UnifiedMapProps {
  locations: MapLocation[]
  height?: string
  onMarkerClick?: (location: MapLocation) => void
  clustered?: boolean
  mapStyle?: 'light' | 'dark' | 'satellite' | 'streets' | 'terrain'
  showControls?: boolean
  showLegend?: boolean
  showFilters?: boolean
  interactive?: boolean
  allowFullscreen?: boolean
  showHeatmap?: boolean
  showSearch?: boolean
  showExport?: boolean
  showMeasurement?: boolean
  initialZoom?: number
  center?: [number, number]
}

export function UnifiedMap({
  locations,
  height = "500px",
  onMarkerClick,
  clustered = true,
  mapStyle = 'satellite',
  showControls = true,
  showLegend = true,
  showFilters = false,
  interactive = true,
  allowFullscreen = false,
  showHeatmap = false,
  showSearch = false,
  showExport = false,
  showMeasurement = false,
  initialZoom = 6,
  center
}: UnifiedMapProps) {
  const [mapProvider, setMapProvider] = useState<'leaflet' | 'mapbox' | 'list'>('mapbox')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredLocations, setFilteredLocations] = useState(locations)
  const [mapError, setMapError] = useState<string | null>(null)

  // Filter locations based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredLocations(locations)
    } else {
      const filtered = locations.filter(location =>
        location.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        location.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        location.status.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredLocations(filtered)
    }
  }, [locations, searchTerm])

  // Get status counts for legend
  const statusCounts = filteredLocations.reduce((acc, location) => {
    acc[location.status] = (acc[location.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const handleExport = () => {
    // Export functionality - could export as CSV, JSON, etc.
    const dataStr = JSON.stringify(filteredLocations, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'map-locations.json'
    link.click()
    URL.revokeObjectURL(url)
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'operational': return '#22c55e'
      case 'maintenance': return '#f59e0b'
      case 'critical': return '#ef4444'
      case 'burnt': return '#dc2626'
      case 'offline': return '#6b7280'
      default: return '#6b7280'
    }
  }

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-background' : ''}`}>
      {/* Map Controls */}
      {showControls && (
        <div className="absolute top-4 left-4 z-10 flex flex-col gap-2">
          <Card className="p-2">
            <div className="flex items-center gap-2">
              <Map className="h-4 w-4" />
              <Select value={mapProvider} onValueChange={(value: 'leaflet' | 'mapbox' | 'list') => setMapProvider(value)}>
                <SelectTrigger className="w-24 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="leaflet">Leaflet</SelectItem>
                  <SelectItem value="mapbox">Mapbox</SelectItem>
                  <SelectItem value="list">List</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </Card>
        </div>
      )}

      {/* Search */}
      {showSearch && (
        <div className="absolute top-4 right-4 z-10">
          <Card className="p-2">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <Input
                placeholder="Search locations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-48 h-8"
              />
            </div>
          </Card>
        </div>
      )}

      {/* Action Buttons */}
      <div className="absolute top-4 right-4 z-10 flex gap-2">
        {showExport && (
          <Button size="sm" variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4" />
          </Button>
        )}
        {allowFullscreen && (
          <Button
            size="sm"
            variant="outline"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        )}
      </div>

      {/* Legend */}
      {showLegend && Object.keys(statusCounts).length > 0 && (
        <div className="absolute bottom-4 left-4 z-10">
          <Card className="p-3">
            <CardHeader className="p-0 pb-2">
              <CardTitle className="text-sm">Status Legend</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="flex flex-col gap-1">
                {Object.entries(statusCounts).map(([status, count]) => (
                  <div key={status} className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: getStatusColor(status) }}
                    />
                    <span className="text-xs">{status}</span>
                    <Badge variant="secondary" className="text-xs">
                      {count}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Map Component */}
      <div style={{ height: isFullscreen ? '100vh' : height }}>
        {mapError && (
          <div className="h-full flex items-center justify-center bg-red-50 border border-red-200 rounded-lg">
            <div className="text-center p-6">
              <div className="text-red-600 mb-2">Map Error</div>
              <div className="text-sm text-red-500 mb-4">{mapError}</div>
              <Button
                variant="outline"
                onClick={() => {
                  setMapError(null)
                  setMapProvider('list')
                }}
              >
                Switch to List View
              </Button>
            </div>
          </div>
        )}

        {!mapError && mapProvider === 'list' && (
          <TransformerListMap
            locations={filteredLocations}
            height="100%"
            onMarkerClick={onMarkerClick}
            clustered={clustered}
          />
        )}

        {!mapError && mapProvider === 'mapbox' && (
          <MapboxMap
            locations={filteredLocations}
            height="100%"
            onMarkerClick={onMarkerClick}
            clustered={clustered}
            mapStyle={mapStyle}
            showControls={false} // We handle controls ourselves
            interactive={interactive}
            zoom={initialZoom}
            center={center}
          />
        )}

        {!mapError && mapProvider === 'leaflet' && (
          <div className="h-full">
            <LeafletMap
              locations={filteredLocations}
              height="100%"
              onMarkerClick={onMarkerClick}
              clustered={clustered}
            />
          </div>
        )}
      </div>
    </div>
  )
}
