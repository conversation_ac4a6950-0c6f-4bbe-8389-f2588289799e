import mysql from 'mysql2/promise'

// Database configuration
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: 'dtms_eeu_db',
  multipleStatements: true
}

export async function bypassTablespaceIssue() {
  let connection: mysql.Connection | null = null
  
  try {
    console.log('🔄 Connecting to dtms_eeu_db database...')
    
    // Connect directly to the database
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to dtms_eeu_db database')
    
    // Create tables with completely different names to bypass tablespace issues
    console.log('🔄 Creating tables with new names to bypass tablespace issues...')
    
    const timestamp = Date.now()
    const suffix = `_v${timestamp}`
    
    // Create new tables with unique names
    console.log('🔄 Creating new regions table...')
    await connection.query(`
      CREATE TABLE regions${suffix} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(10) NOT NULL UNIQUE,
        population INT,
        area_km2 DECIMAL(10,2),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
    `)
    console.log('✅ Created regions table')
    
    console.log('🔄 Creating new service_centers table...')
    await connection.query(`
      CREATE TABLE service_centers${suffix} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(20) NOT NULL UNIQUE,
        region_id INT,
        address TEXT,
        phone VARCHAR(20),
        email VARCHAR(100),
        manager_name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES regions${suffix}(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
    `)
    console.log('✅ Created service_centers table')
    
    console.log('🔄 Creating new users table...')
    await connection.query(`
      CREATE TABLE users${suffix} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        phone VARCHAR(20),
        role ENUM('super_admin', 'national_asset_manager', 'national_maintenance_manager',
                 'regional_admin', 'regional_asset_manager', 'regional_maintenance_engineer',
                 'service_center_manager', 'field_technician', 'customer_service_agent',
                 'audit_compliance_officer') NOT NULL,
        region_id INT,
        service_center_id INT,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES regions${suffix}(id),
        FOREIGN KEY (service_center_id) REFERENCES service_centers${suffix}(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
    `)
    console.log('✅ Created users table')
    
    console.log('🔄 Creating new transformers table...')
    await connection.query(`
      CREATE TABLE transformers${suffix} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        serial_number VARCHAR(50) NOT NULL UNIQUE,
        name VARCHAR(100) NOT NULL,
        type ENUM('distribution', 'power', 'instrument', 'auto') NOT NULL,
        capacity_kva DECIMAL(10,2) NOT NULL,
        voltage_primary DECIMAL(10,2) NOT NULL,
        voltage_secondary DECIMAL(10,2) NOT NULL,
        manufacturer VARCHAR(100),
        model VARCHAR(100),
        year_manufactured YEAR,
        installation_date DATE,
        location_name VARCHAR(200),
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        region_id INT NOT NULL,
        service_center_id INT,
        status ENUM('operational', 'warning', 'maintenance', 'critical', 'burnt') DEFAULT 'operational',
        efficiency_rating DECIMAL(5,2) DEFAULT 95.0,
        load_factor DECIMAL(5,2) DEFAULT 75.0,
        temperature DECIMAL(5,2),
        oil_level DECIMAL(5,2),
        last_maintenance DATE,
        next_maintenance DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES regions${suffix}(id),
        FOREIGN KEY (service_center_id) REFERENCES service_centers${suffix}(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
    `)
    console.log('✅ Created transformers table')
    
    console.log('🔄 Creating new alerts table...')
    await connection.query(`
      CREATE TABLE alerts${suffix} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        type ENUM('temperature', 'voltage', 'load', 'maintenance', 'communication', 'weather', 'security') NOT NULL,
        status ENUM('active', 'investigating', 'resolved', 'monitoring') DEFAULT 'active',
        priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        created_by INT,
        assigned_to INT,
        resolved_at TIMESTAMP NULL,
        resolved_by INT,
        is_resolved BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES transformers${suffix}(id),
        FOREIGN KEY (created_by) REFERENCES users${suffix}(id),
        FOREIGN KEY (assigned_to) REFERENCES users${suffix}(id),
        FOREIGN KEY (resolved_by) REFERENCES users${suffix}(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
    `)
    console.log('✅ Created alerts table')
    
    console.log('🔄 Creating new maintenance_schedules table...')
    await connection.query(`
      CREATE TABLE maintenance_schedules${suffix} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT NOT NULL,
        type ENUM('routine', 'preventive', 'corrective', 'emergency', 'scheduled') NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        scheduled_date DATE NOT NULL,
        estimated_duration INT,
        priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        status ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'postponed') DEFAULT 'scheduled',
        technician_id INT,
        supervisor_id INT,
        cost_estimate DECIMAL(10,2),
        actual_cost DECIMAL(10,2),
        started_at TIMESTAMP NULL,
        completed_at TIMESTAMP NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES transformers${suffix}(id),
        FOREIGN KEY (technician_id) REFERENCES users${suffix}(id),
        FOREIGN KEY (supervisor_id) REFERENCES users${suffix}(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
    `)
    console.log('✅ Created maintenance_schedules table')
    
    console.log('🔄 Creating new notifications table...')
    await connection.query(`
      CREATE TABLE notifications${suffix} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
        recipient_id INT,
        sender_id INT,
        is_read BOOLEAN DEFAULT FALSE,
        read_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (recipient_id) REFERENCES users${suffix}(id),
        FOREIGN KEY (sender_id) REFERENCES users${suffix}(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
    `)
    console.log('✅ Created notifications table')
    
    console.log('🔄 Creating new performance_metrics table...')
    await connection.query(`
      CREATE TABLE performance_metrics${suffix} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT,
        metric_type ENUM('power_generation', 'efficiency', 'load_factor', 'temperature', 'voltage') NOT NULL,
        value DECIMAL(10,4) NOT NULL,
        unit VARCHAR(20),
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES transformers${suffix}(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
    `)
    console.log('✅ Created performance_metrics table')
    
    console.log('🔄 Creating new weather_data table...')
    await connection.query(`
      CREATE TABLE weather_data${suffix} (
        id INT PRIMARY KEY AUTO_INCREMENT,
        region_id INT NOT NULL,
        temperature DECIMAL(5,2),
        humidity DECIMAL(5,2),
        wind_speed DECIMAL(5,2),
        weather_condition VARCHAR(50),
        risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES regions${suffix}(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC
    `)
    console.log('✅ Created weather_data table')
    
    // Now create views with the standard names that point to the new tables
    console.log('🔄 Creating views with standard names...')
    
    await connection.query(`CREATE OR REPLACE VIEW app_regions AS SELECT * FROM regions${suffix}`)
    await connection.query(`CREATE OR REPLACE VIEW app_service_centers AS SELECT * FROM service_centers${suffix}`)
    await connection.query(`CREATE OR REPLACE VIEW app_users AS SELECT * FROM users${suffix}`)
    await connection.query(`CREATE OR REPLACE VIEW app_transformers AS SELECT * FROM transformers${suffix}`)
    await connection.query(`CREATE OR REPLACE VIEW app_alerts AS SELECT * FROM alerts${suffix}`)
    await connection.query(`CREATE OR REPLACE VIEW app_maintenance_schedules AS SELECT * FROM maintenance_schedules${suffix}`)
    await connection.query(`CREATE OR REPLACE VIEW app_notifications AS SELECT * FROM notifications${suffix}`)
    await connection.query(`CREATE OR REPLACE VIEW app_performance_metrics AS SELECT * FROM performance_metrics${suffix}`)
    await connection.query(`CREATE OR REPLACE VIEW app_weather_data AS SELECT * FROM weather_data${suffix}`)
    
    console.log('✅ Created views with standard names')
    
    console.log('✅ All tables and views created successfully, bypassing tablespace issues')
    
    return {
      success: true,
      message: 'Tables created successfully with tablespace bypass',
      tableNames: {
        regions: `regions${suffix}`,
        service_centers: `service_centers${suffix}`,
        users: `users${suffix}`,
        transformers: `transformers${suffix}`,
        alerts: `alerts${suffix}`,
        maintenance_schedules: `maintenance_schedules${suffix}`,
        notifications: `notifications${suffix}`,
        performance_metrics: `performance_metrics${suffix}`,
        weather_data: `weather_data${suffix}`
      }
    }
    
  } catch (error) {
    console.error('❌ Tablespace bypass failed:', error)
    return {
      success: false,
      message: `Tablespace bypass failed: ${error}`,
      error
    }
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}
