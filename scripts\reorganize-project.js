#!/usr/bin/env node

/**
 * Project Reorganization Script
 * Migrates existing files to the new organized structure
 */

const fs = require('fs')
const path = require('path')

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`)
}

// Utility functions
const ensureDir = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
    log.success(`Created directory: ${dirPath}`)
  }
}

const moveFile = (from, to) => {
  try {
    if (fs.existsSync(from)) {
      ensureDir(path.dirname(to))
      fs.renameSync(from, to)
      log.success(`Moved: ${from} → ${to}`)
      return true
    } else {
      log.warning(`File not found: ${from}`)
      return false
    }
  } catch (error) {
    log.error(`Failed to move ${from} → ${to}: ${error.message}`)
    return false
  }
}

const copyFile = (from, to) => {
  try {
    if (fs.existsSync(from)) {
      ensureDir(path.dirname(to))
      fs.copyFileSync(from, to)
      log.success(`Copied: ${from} → ${to}`)
      return true
    } else {
      log.warning(`File not found: ${from}`)
      return false
    }
  } catch (error) {
    log.error(`Failed to copy ${from} → ${to}: ${error.message}`)
    return false
  }
}

const createIndexFile = (dirPath, exports) => {
  const indexPath = path.join(dirPath, 'index.ts')
  const content = exports.map(exp => `export * from './${exp}'`).join('\n') + '\n'
  
  try {
    ensureDir(dirPath)
    fs.writeFileSync(indexPath, content)
    log.success(`Created index file: ${indexPath}`)
  } catch (error) {
    log.error(`Failed to create index file ${indexPath}: ${error.message}`)
  }
}

// Migration plan
const migrationPlan = {
  // Create new directory structure
  directories: [
    'src',
    'src/components',
    'src/components/ui',
    'src/components/layout',
    'src/components/forms',
    'src/components/charts',
    'src/components/maps',
    'src/components/common',
    'src/features',
    'src/features/authentication',
    'src/features/dashboard',
    'src/features/transformers',
    'src/features/maintenance',
    'src/features/users',
    'src/features/alerts',
    'src/features/reports',
    'src/shared',
    'src/shared/lib',
    'src/shared/lib/auth',
    'src/shared/lib/database',
    'src/shared/lib/validation',
    'src/shared/hooks',
    'src/shared/contexts',
    'src/styles',
    'src/styles/themes',
    'src/assets',
    'src/assets/images',
    'src/assets/icons',
    'src/assets/fonts',
    'src/assets/data',
    'docs',
    'scripts',
    'tests',
    'tests/__mocks__',
    'tests/components',
    'tests/features',
    'tests/integration',
    'tests/e2e'
  ],

  // File movements
  moves: [
    // Move existing components
    { from: 'components/main-layout.tsx', to: 'src/components/layout/main-layout.tsx' },
    { from: 'components/protected-route.tsx', to: 'src/components/layout/protected-route.tsx' },
    { from: 'components/providers-wrapper.tsx', to: 'src/shared/contexts/providers-wrapper.tsx' },
    
    // Move transformer components
    { from: 'components/transformer', to: 'src/features/transformers/components' },
    
    // Move dashboard components
    { from: 'components/dashboard', to: 'src/features/dashboard/components' },
    
    // Move contexts
    { from: 'contexts/auth-context.tsx', to: 'src/shared/contexts/auth-context.tsx' },
    { from: 'contexts/transformer-context.tsx', to: 'src/features/transformers/contexts/transformer-context.tsx' },
    
    // Move services
    { from: 'services/transformer-service.ts', to: 'src/features/transformers/services/transformer-service.ts' },
    { from: 'services/auth-service.ts', to: 'src/features/authentication/services/auth-service.ts' },
    
    // Move types
    { from: 'types/transformer.ts', to: 'src/features/transformers/types/transformer.types.ts' },
    { from: 'types/auth.ts', to: 'src/features/authentication/types/auth.types.ts' },
    
    // Move styles
    { from: 'app/globals.css', to: 'src/styles/globals.css' }
  ],

  // Files to copy (keep originals for now)
  copies: [
    { from: 'README.md', to: 'docs/README.md' }
  ]
}

// Main reorganization function
async function reorganizeProject() {
  log.header('🚀 Starting Project Reorganization')
  
  try {
    // Step 1: Create directory structure
    log.header('📁 Creating Directory Structure')
    migrationPlan.directories.forEach(dir => {
      ensureDir(dir)
    })
    
    // Step 2: Move files
    log.header('📦 Moving Files')
    let moveCount = 0
    migrationPlan.moves.forEach(({ from, to }) => {
      if (moveFile(from, to)) {
        moveCount++
      }
    })
    
    // Step 3: Copy files
    log.header('📋 Copying Files')
    let copyCount = 0
    migrationPlan.copies.forEach(({ from, to }) => {
      if (copyFile(from, to)) {
        copyCount++
      }
    })
    
    // Step 4: Create index files
    log.header('📝 Creating Index Files')
    
    // Create feature index files
    const features = [
      'authentication',
      'dashboard', 
      'transformers',
      'maintenance',
      'users',
      'alerts',
      'reports'
    ]
    
    features.forEach(feature => {
      const featurePath = `src/features/${feature}`
      if (fs.existsSync(featurePath)) {
        const subDirs = ['components', 'hooks', 'services', 'types', 'utils']
        const existingSubDirs = subDirs.filter(subDir => 
          fs.existsSync(path.join(featurePath, subDir))
        )
        
        if (existingSubDirs.length > 0) {
          createIndexFile(featurePath, existingSubDirs)
        }
      }
    })
    
    // Create component index files
    const componentDirs = [
      'src/components/ui',
      'src/components/layout',
      'src/components/forms',
      'src/components/charts',
      'src/components/maps',
      'src/components/common'
    ]
    
    componentDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir)
          .filter(file => file.endsWith('.tsx') && file !== 'index.ts')
          .map(file => file.replace('.tsx', ''))
        
        if (files.length > 0) {
          createIndexFile(dir, files)
        }
      }
    })
    
    // Step 5: Update package.json scripts
    log.header('⚙️ Updating Configuration')
    
    const packageJsonPath = 'package.json'
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
        
        // Add new scripts
        packageJson.scripts = {
          ...packageJson.scripts,
          'type-check': 'tsc --noEmit',
          'lint:fix': 'next lint --fix',
          'test:watch': 'jest --watch',
          'test:coverage': 'jest --coverage',
          'build:analyze': 'ANALYZE=true npm run build',
          'db:migrate': 'node scripts/migrate.js',
          'db:seed': 'node scripts/seed.js'
        }
        
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))
        log.success('Updated package.json scripts')
      } catch (error) {
        log.error(`Failed to update package.json: ${error.message}`)
      }
    }
    
    // Step 6: Create documentation
    log.header('📚 Creating Documentation')
    
    const readmeContent = `# EEU Transformer Management System

## Project Structure

This project follows a modern, scalable architecture with clear separation of concerns:

### 📁 Directory Structure

\`\`\`
src/
├── components/          # Reusable UI components
├── features/           # Feature-based modules
├── shared/            # Shared utilities and services
├── styles/            # Global styles and themes
└── assets/            # Static assets
\`\`\`

### 🎯 Key Features

- **Modern Architecture**: Feature-based organization
- **Type Safety**: Comprehensive TypeScript coverage
- **Reusable Components**: Shared UI component library
- **Consistent Styling**: Tailwind CSS with custom themes
- **Performance Optimized**: Code splitting and lazy loading

### 🚀 Getting Started

1. Install dependencies: \`npm install\`
2. Run development server: \`npm run dev\`
3. Open [http://localhost:3002](http://localhost:3002)

### 📖 Documentation

- [Setup Guide](./docs/SETUP.md)
- [API Documentation](./docs/API.md)
- [Deployment Guide](./docs/DEPLOYMENT.md)
- [Contributing Guidelines](./docs/CONTRIBUTING.md)

### 🛠️ Development

- \`npm run dev\` - Start development server
- \`npm run build\` - Build for production
- \`npm run test\` - Run tests
- \`npm run lint\` - Lint code
- \`npm run type-check\` - Type checking

### 📝 License

Ethiopian Electric Utility - Internal Use Only
`

    fs.writeFileSync('README.md', readmeContent)
    log.success('Created updated README.md')
    
    // Summary
    log.header('📊 Migration Summary')
    log.info(`Directories created: ${migrationPlan.directories.length}`)
    log.info(`Files moved: ${moveCount}`)
    log.info(`Files copied: ${copyCount}`)
    log.success('Project reorganization completed successfully!')
    
    log.header('🎉 Next Steps')
    log.info('1. Update import statements in your code to use new paths')
    log.info('2. Test the application to ensure everything works')
    log.info('3. Update any remaining references to old file locations')
    log.info('4. Consider running: npm run type-check')
    
  } catch (error) {
    log.error(`Reorganization failed: ${error.message}`)
    process.exit(1)
  }
}

// Run the reorganization
if (require.main === module) {
  reorganizeProject()
}

module.exports = { reorganizeProject }
