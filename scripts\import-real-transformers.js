/**
 * Import Real Transformer Data for EEU
 * This script helps import actual transformer data from CSV or manual entry
 */

const mysql = require('mysql2/promise');
const fs = require('fs');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

// Sample real transformer data template for EEU
const realTransformerTemplate = [
  {
    serial_number: 'EEU-AA-001',
    name: 'Addis Ababa Main Distribution Transformer',
    type: 'distribution',
    capacity_kva: 1000,
    voltage_primary: 33,
    voltage_secondary: 0.4,
    manufacturer: 'Siemens',
    model: 'GEAFOL',
    year_manufactured: 2020,
    installation_date: '2020-06-15',
    location_name: 'Bole Road, Near EEU Headquarters',
    latitude: 9.0222,
    longitude: 38.7468,
    region_id: 1, // Addis Ababa
    status: 'operational',
    efficiency_rating: 98.5,
    load_factor: 75.0,
    temperature: 65.0,
    oil_level: 95.0
  },
  {
    serial_number: 'EEU-OR-001',
    name: 'Jimma Central Distribution Transformer',
    type: 'distribution',
    capacity_kva: 500,
    voltage_primary: 33,
    voltage_secondary: 0.4,
    manufacturer: 'ABB',
    model: 'UniGear ZS1',
    year_manufactured: 2019,
    installation_date: '2019-08-20',
    location_name: 'Jimma City Center, Main Square',
    latitude: 7.6781,
    longitude: 36.8344,
    region_id: 2, // Oromia
    status: 'operational',
    efficiency_rating: 97.8,
    load_factor: 68.0,
    temperature: 62.0,
    oil_level: 92.0
  },
  {
    serial_number: 'EEU-AM-001',
    name: 'Bahir Dar Power Distribution Transformer',
    type: 'power',
    capacity_kva: 2000,
    voltage_primary: 132,
    voltage_secondary: 33,
    manufacturer: 'Schneider Electric',
    model: 'Trihal',
    year_manufactured: 2021,
    installation_date: '2021-03-10',
    location_name: 'Bahir Dar Industrial Zone',
    latitude: 11.5959,
    longitude: 37.3906,
    region_id: 3, // Amhara
    status: 'operational',
    efficiency_rating: 99.2,
    load_factor: 82.0,
    temperature: 58.0,
    oil_level: 98.0
  }
];

async function setupRegions() {
  let connection;
  
  try {
    connection = await mysql.createConnection(config);
    
    // Create regions table if it doesn't exist
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_regions (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(10) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Insert Ethiopian regions (using existing schema)
    const regions = [
      { id: 1, name: 'Addis Ababa', code: 'AA', population: 3500000, area_km2: 527.0 },
      { id: 2, name: 'Oromia', code: 'OR', population: 37000000, area_km2: 353006.81 },
      { id: 3, name: 'Amhara', code: 'AM', population: 21000000, area_km2: 154708.96 },
      { id: 4, name: 'Tigray', code: 'TI', population: 5500000, area_km2: 50078.64 },
      { id: 5, name: 'SNNP', code: 'SN', population: 20000000, area_km2: 105887.18 },
      { id: 6, name: 'Somali', code: 'SO', population: 5500000, area_km2: 279252.0 },
      { id: 7, name: 'Benishangul-Gumuz', code: 'BG', population: 1100000, area_km2: 50699.0 },
      { id: 8, name: 'Gambela', code: 'GA', population: 435000, area_km2: 25802.0 },
      { id: 9, name: 'Harari', code: 'HA', population: 250000, area_km2: 311.25 },
      { id: 10, name: 'Afar', code: 'AF', population: 1800000, area_km2: 96707.0 },
      { id: 11, name: 'Dire Dawa', code: 'DD', population: 500000, area_km2: 1213.0 }
    ];

    for (const region of regions) {
      await connection.execute(`
        INSERT IGNORE INTO app_regions (id, name, code, population, area_km2, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `, [region.id, region.name, region.code, region.population, region.area_km2]);
    }

    console.log('✅ Regions setup complete');
    return connection;
    
  } catch (error) {
    console.error('❌ Error setting up regions:', error);
    if (connection) await connection.end();
    throw error;
  }
}

async function importRealTransformers() {
  let connection;
  
  try {
    console.log('🏢 Setting up EEU Transformer Management System for Production...');
    
    // Setup regions first
    connection = await setupRegions();
    
    console.log('⚡ Importing real transformer data...');
    
    for (const transformer of realTransformerTemplate) {
      await connection.execute(`
        INSERT INTO app_transformers (
          serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
          manufacturer, model, year_manufactured, installation_date, location_name,
          latitude, longitude, region_id, status, efficiency_rating, load_factor,
          temperature, oil_level, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        transformer.serial_number, transformer.name, transformer.type,
        transformer.capacity_kva, transformer.voltage_primary, transformer.voltage_secondary,
        transformer.manufacturer, transformer.model, transformer.year_manufactured,
        transformer.installation_date, transformer.location_name, transformer.latitude,
        transformer.longitude, transformer.region_id, transformer.status,
        transformer.efficiency_rating, transformer.load_factor, transformer.temperature,
        transformer.oil_level
      ]);
    }

    // Show summary
    const [tCount] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    const [rCount] = await connection.execute('SELECT COUNT(*) as count FROM app_regions');
    
    console.log('\n📊 Production Setup Summary:');
    console.log(`  - Regions: ${rCount[0].count} (All Ethiopian regions)`);
    console.log(`  - Transformers: ${tCount[0].count} (Including real EEU data)`);
    
    console.log('\n🎉 Production setup complete!');
    console.log('\n📋 Next Steps:');
    console.log('  1. Add more transformers using the web interface');
    console.log('  2. Set up user accounts for your team');
    console.log('  3. Configure maintenance schedules');
    console.log('  4. Customize alert thresholds');
    
  } catch (error) {
    console.error('❌ Error importing real transformers:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { importRealTransformers, realTransformerTemplate };

// Run if called directly
if (require.main === module) {
  importRealTransformers();
}
