"use client"

import { Transformer } from "@/src/types/transformer"
import { transformerService } from "./transformer-service"
import { transformerStore } from "./transformer-store"
import { isValidTransformer, sanitizeTransformer, safeParseFloat } from "@/src/lib/validation"
import { TransformerDataStandardizer } from "./transformer-data-standardizer"

// Mapbox access token - in a real app, this would be in environment variables
const MAPBOX_ACCESS_TOKEN = "pk.eyJ1IjoiZXRoaW9waWFlZXUiLCJhIjoiY2xzMnRqMnRpMGF1eTJrcGFtdXJ5Z2VsZSJ9.Qr9MQNqvAYZLjLz0_kDrqw"

export interface MapLocation {
  id: string
  latitude: number
  longitude: number
  title: string
  description: string
  status: string
  icon?: string
  data?: any
  weight?: number // For heatmap intensity
}

export interface ClusterInfo {
  count: number
  center: [number, number]
  statusCounts: Record<string, number>
}

export interface MapBounds {
  north: number
  south: number
  east: number
  west: number
}

class MapService {
  // Get Mapbox access token
  getMapboxToken(): string {
    return MAPBOX_ACCESS_TOKEN
  }

  // Convert transformer data to map locations
  async getTransformerLocations(region?: string, timeRange?: string): Promise<MapLocation[]> {
    let transformers: Transformer[];

    try {
      if (region && region !== 'all') {
        // If a specific region is provided, filter by it
        transformers = await transformerStore.filterTransformers({
          region: [region]
        });
      } else {
        // Otherwise, get all transformers
        transformers = await transformerStore.getAllTransformers();
      }

      return transformers.map(transformer => this.transformerToMapLocation(transformer));
    } catch (error) {
      console.error("Error getting transformer locations:", error);
      return [];
    }
  }

  // Standardized function to convert a transformer to a map location
  transformerToMapLocation(transformer: Transformer | Partial<Transformer>): MapLocation {
    try {
      // Validate and sanitize the transformer data
      if (!transformer) {
        throw new Error("Transformer data is missing");
      }

      // Use the standardizer to ensure consistent data format
      const standardized = TransformerDataStandardizer.standardize(transformer);

      return {
        id: standardized.id,
        latitude: standardized.location.coordinates.lat,
        longitude: standardized.location.coordinates.lng,
        title: `${standardized.manufacturer} ${standardized.model}`,
        description: `${standardized.capacity} kVA, ${standardized.location.region}`,
        status: standardized.status,
        data: standardized,
        // Add weight for heatmap based on status
        weight: this.getStatusWeight(standardized.status)
      };
    } catch (error) {
      console.error("Error converting transformer to map location:", error);

      // Return a default location for error cases
      return {
        id: transformer?.id || `error-${Date.now()}`,
        latitude: 9.0222,
        longitude: 38.7468,
        title: "Error: Invalid Transformer",
        description: "Could not process transformer data",
        status: "Error",
        data: transformer,
        weight: 0
      };
    }
  }

  // Get weight value based on status for heatmap
  private getStatusWeight(status: string): number {
    switch (status.toLowerCase()) {
      case 'critical':
        return 1;
      case 'burnt':
        return 0.8;
      case 'maintenance':
        return 0.5;
      case 'offline':
        return 0.3;
      case 'operational':
        return 0.1;
      default:
        return 0.1;
    }
  }

  // Get transformer locations by status
  async getTransformerLocationsByStatus(status: string): Promise<MapLocation[]> {
    try {
      const transformers = await transformerStore.filterTransformers({
        status: [status]
      });
      return transformers.map(transformer => this.transformerToMapLocation(transformer));
    } catch (error) {
      console.error(`Error getting transformer locations by status ${status}:`, error);
      return [];
    }
  }

  // Get transformer locations by region
  async getTransformerLocationsByRegion(region: string): Promise<MapLocation[]> {
    try {
      const transformers = await transformerStore.filterTransformers({
        region: [region]
      });
      return transformers.map(transformer => this.transformerToMapLocation(transformer));
    } catch (error) {
      console.error(`Error getting transformer locations by region ${region}:`, error);
      return [];
    }
  }

  // Get transformer locations by service center
  async getTransformerLocationsByServiceCenter(serviceCenter: string): Promise<MapLocation[]> {
    try {
      const transformers = await transformerStore.filterTransformers({
        serviceCenter: [serviceCenter]
      });
      return transformers.map(transformer => this.transformerToMapLocation(transformer));
    } catch (error) {
      console.error(`Error getting transformer locations by service center ${serviceCenter}:`, error);
      return [];
    }
  }

  // Get map center coordinates for Ethiopia
  getEthiopiaCenter(): [number, number] {
    // Approximate center of Ethiopia
    return [38.7468, 9.0222]
  }

  // Get map bounds for Ethiopia
  getEthiopiaBounds(): MapBounds {
    // Approximate bounds for Ethiopia
    return {
      north: 14.8,
      south: 3.5,
      east: 48.0,
      west: 33.0
    }
  }

  // Get map style URL
  getMapStyle(theme: 'light' | 'dark' | 'satellite' | 'streets' | 'terrain' = 'light'): string {
    const styles = {
      light: 'mapbox://styles/mapbox/light-v11',
      dark: 'mapbox://styles/mapbox/dark-v11',
      satellite: 'mapbox://styles/mapbox/satellite-streets-v12',
      streets: 'mapbox://styles/mapbox/streets-v12',
      terrain: 'mapbox://styles/mapbox/outdoors-v12'
    }

    return styles[theme]
  }

  // Get status color for markers
  getStatusColor(status: string): string {
    const colors = {
      'Operational': '#22c55e', // green-500
      'Maintenance': '#f59e0b', // amber-500
      'Warning': '#f97316', // orange-500
      'Critical': '#ef4444', // red-500
      'Burnt': '#7c3aed', // purple-600
      'Offline': '#6b7280', // gray-500
    }

    // Case-insensitive matching
    const statusLower = status.toLowerCase()
    for (const [key, value] of Object.entries(colors)) {
      if (key.toLowerCase() === statusLower) {
        return value
      }
    }

    return '#6b7280' // Default to gray
  }

  // Get weather data for a location
  async getWeatherData(latitude: number, longitude: number): Promise<any> {
    try {
      const response = await fetch(`/api/weather?lat=${latitude}&lon=${longitude}`)
      if (!response.ok) {
        throw new Error('Failed to fetch weather data')
      }
      return await response.json()
    } catch (error) {
      console.error('Error fetching weather data:', error)
      return null
    }
  }

  // Convert locations to GeoJSON for clustering
  locationsToGeoJSON(locations: MapLocation[]): GeoJSON.FeatureCollection {
    const features: GeoJSON.Feature[] = locations.map(location => ({
      type: 'Feature',
      properties: {
        id: location.id,
        title: location.title,
        description: location.description,
        status: location.status,
        color: this.getStatusColor(location.status),
        // Add weight property for heatmap (higher for critical/burnt)
        weight: location.status === 'Critical' ? 1 :
                location.status === 'Burnt' ? 0.8 :
                location.status === 'Maintenance' ? 0.5 :
                location.status === 'Offline' ? 0.3 : 0.1
      },
      geometry: {
        type: 'Point',
        coordinates: [location.longitude, location.latitude]
      }
    }))

    return {
      type: 'FeatureCollection',
      features
    }
  }

  // Calculate distance between two points in kilometers
  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371 // Radius of the earth in km
    const dLat = this.deg2rad(lat2 - lat1)
    const dLon = this.deg2rad(lon2 - lon1)
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    const d = R * c // Distance in km
    return d
  }

  // Convert degrees to radians
  private deg2rad(deg: number): number {
    return deg * (Math.PI/180)
  }

  // Get nearby transformers
  async getNearbyTransformers(latitude: number, longitude: number, radiusKm: number = 10): Promise<MapLocation[]> {
    const allLocations = await this.getTransformerLocations()

    return allLocations.filter(location => {
      const distance = this.calculateDistance(
        latitude,
        longitude,
        location.latitude,
        location.longitude
      )

      return distance <= radiusKm
    })
  }

  // Get transformer density data for heatmap
  async getTransformerDensityData(): Promise<any[]> {
    const locations = await this.getTransformerLocations()

    return locations.map(location => ({
      latitude: location.latitude,
      longitude: location.longitude,
      weight: location.status === 'Critical' ? 1 :
              location.status === 'Burnt' ? 0.8 :
              location.status === 'Maintenance' ? 0.5 :
              location.status === 'Offline' ? 0.3 : 0.1
    }))
  }

  // Get transformer locations with alerts
  async getTransformerLocationsWithAlerts(): Promise<MapLocation[]> {
    const locations = await this.getTransformerLocations()

    // Filter to only include transformers with critical or warning status
    return locations.filter(location =>
      location.status === 'Critical' ||
      location.status === 'Warning' ||
      location.status === 'Maintenance'
    )
  }

  // Get transformer locations with pending maintenance
  async getTransformerLocationsWithMaintenance(): Promise<MapLocation[]> {
    const locations = await this.getTransformerLocations()

    // Filter to only include transformers with maintenance status
    return locations.filter(location => location.status === 'Maintenance')
  }

  // Get transformer locations by multiple filters
  async getFilteredTransformerLocations(filters: {
    status?: string[],
    region?: string[] | string,
    serviceCenter?: string[],
    capacity?: [number, number], // min, max
    search?: string,
    timeRange?: string
  }): Promise<MapLocation[]> {
    try {
      // Handle string region for backward compatibility
      if (typeof filters.region === 'string') {
        if (filters.region !== 'all' && filters.region !== '') {
          filters.region = [filters.region];
        } else {
          delete filters.region; // Remove 'all' region to get all regions
        }
      }

      // Use the transformer store directly for filtering
      const transformers = await transformerStore.filterTransformers(filters);

      // Convert to map locations using the standardized function
      return transformers.map(transformer => this.transformerToMapLocation(transformer));
    } catch (error) {
      console.error("Error getting filtered transformer locations:", error);
      return [];
    }
  }
}

export const mapService = new MapService()
