import Link from "next/link"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Zap, BarChart2, Settings, Users, Database, Bell } from "lucide-react"

export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="bg-primary text-primary-foreground py-12 px-4">
        <div className="container mx-auto max-w-6xl">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            EEU Transformer Management System
          </h1>
          <p className="text-lg md:text-xl opacity-90 max-w-3xl">
            A comprehensive solution for monitoring, managing, and maintaining the Ethiopian Electric Utility transformer network.
          </p>
          <div className="mt-8 flex flex-wrap gap-4">
            <Button asChild size="lg" className="gap-2">
              <Link href="/dashboard">
                <BarChart2 className="h-5 w-5" />
                Unified Dashboard
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="bg-primary-foreground/10 text-primary-foreground border-primary-foreground/20 gap-2">
              <Link href="/transformers">
                <Zap className="h-5 w-5" />
                View Transformers
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="bg-primary-foreground/10 text-primary-foreground border-primary-foreground/20 gap-2">
              <Link href="/alerts">
                <Bell className="h-5 w-5" />
                View Alerts
              </Link>
            </Button>
          </div>
        </div>
      </header>

      <main className="flex-1 py-12 px-4">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-2xl font-bold mb-6">System Features</h2>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <BarChart2 className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle>Real-time Monitoring</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  Monitor transformer status, health metrics, and performance indicators in real-time across the entire network.
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Button asChild variant="ghost" className="w-full">
                  <Link href="/dashboard">
                    View Dashboard
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Zap className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle>Transformer Management</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  Comprehensive tools for managing transformer inventory, maintenance schedules, and service history.
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Button asChild variant="ghost" className="w-full">
                  <Link href="/transformers">
                    View Transformers
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Settings className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle>Maintenance Planning</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  Schedule and track maintenance activities, assign teams, and manage spare parts inventory.
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Button asChild variant="ghost" className="w-full">
                  <Link href="/maintenance">
                    View Maintenance
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </main>

      <footer className="bg-muted py-8 px-4">
        <div className="container mx-auto max-w-6xl text-center">
          <p className="text-muted-foreground">
            © {new Date().getFullYear()} Ethiopian Electric Utility | Transformer Management System
          </p>
        </div>
      </footer>
    </div>
  )
}
