/**
 * Date Utilities
 * Common date manipulation and formatting functions
 */

import { format, parseISO, isValid, differenceInDays, differenceInHours, differenceInMinutes, addDays, addHours, addMinutes, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear } from 'date-fns'

// Date formatting functions
export const formatDate = (date: Date | string, pattern: string = 'MMM dd, yyyy'): string => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    return isValid(dateObj) ? format(dateObj, pattern) : 'Invalid Date'
  } catch {
    return 'Invalid Date'
  }
}

export const formatDateTime = (date: Date | string): string => {
  return formatDate(date, 'MMM dd, yyyy HH:mm')
}

export const formatTime = (date: Date | string): string => {
  return formatDate(date, 'HH:mm')
}

export const formatRelativeTime = (date: Date | string): string => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    if (!isValid(dateObj)) return 'Invalid Date'
    
    const now = new Date()
    const diffInMinutes = differenceInMinutes(now, dateObj)
    const diffInHours = differenceInHours(now, dateObj)
    const diffInDays = differenceInDays(now, dateObj)
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`
    if (diffInDays < 7) return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} week${Math.floor(diffInDays / 7) === 1 ? '' : 's'} ago`
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} month${Math.floor(diffInDays / 30) === 1 ? '' : 's'} ago`
    
    return `${Math.floor(diffInDays / 365)} year${Math.floor(diffInDays / 365) === 1 ? '' : 's'} ago`
  } catch {
    return 'Invalid Date'
  }
}

// Date parsing functions
export const parseDate = (dateString: string): Date | null => {
  try {
    const date = parseISO(dateString)
    return isValid(date) ? date : null
  } catch {
    return null
  }
}

export const isValidDate = (date: any): boolean => {
  if (!date) return false
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  return isValid(dateObj)
}

// Date calculation functions
export const addTime = (date: Date | string, amount: number, unit: 'days' | 'hours' | 'minutes'): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  
  switch (unit) {
    case 'days':
      return addDays(dateObj, amount)
    case 'hours':
      return addHours(dateObj, amount)
    case 'minutes':
      return addMinutes(dateObj, amount)
    default:
      return dateObj
  }
}

export const getDateRange = (
  type: 'day' | 'week' | 'month' | 'year',
  date: Date | string = new Date()
): { start: Date; end: Date } => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  
  switch (type) {
    case 'day':
      return {
        start: startOfDay(dateObj),
        end: endOfDay(dateObj)
      }
    case 'week':
      return {
        start: startOfWeek(dateObj, { weekStartsOn: 1 }), // Monday
        end: endOfWeek(dateObj, { weekStartsOn: 1 })
      }
    case 'month':
      return {
        start: startOfMonth(dateObj),
        end: endOfMonth(dateObj)
      }
    case 'year':
      return {
        start: startOfYear(dateObj),
        end: endOfYear(dateObj)
      }
    default:
      return {
        start: startOfDay(dateObj),
        end: endOfDay(dateObj)
      }
  }
}

// Date comparison functions
export const isSameDay = (date1: Date | string, date2: Date | string): boolean => {
  try {
    const d1 = typeof date1 === 'string' ? parseISO(date1) : date1
    const d2 = typeof date2 === 'string' ? parseISO(date2) : date2
    
    return d1.getFullYear() === d2.getFullYear() &&
           d1.getMonth() === d2.getMonth() &&
           d1.getDate() === d2.getDate()
  } catch {
    return false
  }
}

export const isToday = (date: Date | string): boolean => {
  return isSameDay(date, new Date())
}

export const isYesterday = (date: Date | string): boolean => {
  const yesterday = addDays(new Date(), -1)
  return isSameDay(date, yesterday)
}

export const isTomorrow = (date: Date | string): boolean => {
  const tomorrow = addDays(new Date(), 1)
  return isSameDay(date, tomorrow)
}

export const isOverdue = (date: Date | string): boolean => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    return dateObj < new Date()
  } catch {
    return false
  }
}

export const isDueSoon = (date: Date | string, daysThreshold: number = 7): boolean => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    const now = new Date()
    const diffInDays = differenceInDays(dateObj, now)
    
    return diffInDays >= 0 && diffInDays <= daysThreshold
  } catch {
    return false
  }
}

// Date range functions
export const getLastNDays = (n: number): { start: Date; end: Date } => {
  const end = new Date()
  const start = addDays(end, -n)
  
  return {
    start: startOfDay(start),
    end: endOfDay(end)
  }
}

export const getNextNDays = (n: number): { start: Date; end: Date } => {
  const start = new Date()
  const end = addDays(start, n)
  
  return {
    start: startOfDay(start),
    end: endOfDay(end)
  }
}

// Ethiopian calendar utilities (basic)
export const getEthiopianDate = (date: Date | string): string => {
  // This is a simplified version - a full implementation would require
  // proper Ethiopian calendar conversion
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  const year = dateObj.getFullYear() - 7 // Ethiopian calendar is ~7-8 years behind
  const month = dateObj.getMonth() + 1
  const day = dateObj.getDate()
  
  return `${day}/${month}/${year} E.C.`
}

// Timezone utilities
export const convertToTimezone = (date: Date | string, timezone: string): Date => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    // This is a basic implementation - for production, use a proper timezone library
    return new Date(dateObj.toLocaleString('en-US', { timeZone: timezone }))
  } catch {
    return typeof date === 'string' ? parseISO(date) : date
  }
}

export const getTimezoneOffset = (timezone: string): number => {
  try {
    const now = new Date()
    const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000))
    const target = new Date(utc.toLocaleString('en-US', { timeZone: timezone }))
    
    return (target.getTime() - utc.getTime()) / (1000 * 60 * 60)
  } catch {
    return 0
  }
}

// Date validation utilities
export const isDateInRange = (
  date: Date | string,
  startDate: Date | string,
  endDate: Date | string
): boolean => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    const startObj = typeof startDate === 'string' ? parseISO(startDate) : startDate
    const endObj = typeof endDate === 'string' ? parseISO(endDate) : endDate
    
    return dateObj >= startObj && dateObj <= endObj
  } catch {
    return false
  }
}

export const isWeekend = (date: Date | string): boolean => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    const dayOfWeek = dateObj.getDay()
    return dayOfWeek === 0 || dayOfWeek === 6 // Sunday or Saturday
  } catch {
    return false
  }
}

export const isWorkingDay = (date: Date | string): boolean => {
  return !isWeekend(date)
}

// Date formatting for different locales
export const formatDateForLocale = (
  date: Date | string,
  locale: string = 'en-US',
  options: Intl.DateTimeFormatOptions = {}
): string => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    return dateObj.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...options
    })
  } catch {
    return 'Invalid Date'
  }
}

// Age calculation
export const calculateAge = (birthDate: Date | string): number => {
  try {
    const birthObj = typeof birthDate === 'string' ? parseISO(birthDate) : birthDate
    const today = new Date()
    let age = today.getFullYear() - birthObj.getFullYear()
    const monthDiff = today.getMonth() - birthObj.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthObj.getDate())) {
      age--
    }
    
    return age
  } catch {
    return 0
  }
}

// Duration formatting
export const formatDuration = (startDate: Date | string, endDate: Date | string): string => {
  try {
    const start = typeof startDate === 'string' ? parseISO(startDate) : startDate
    const end = typeof endDate === 'string' ? parseISO(endDate) : endDate
    
    const diffInMinutes = differenceInMinutes(end, start)
    const diffInHours = differenceInHours(end, start)
    const diffInDays = differenceInDays(end, start)
    
    if (diffInMinutes < 60) return `${diffInMinutes} minutes`
    if (diffInHours < 24) return `${diffInHours} hours`
    return `${diffInDays} days`
  } catch {
    return 'Invalid duration'
  }
}

// Export all utilities
export default {
  formatDate,
  formatDateTime,
  formatTime,
  formatRelativeTime,
  parseDate,
  isValidDate,
  addTime,
  getDateRange,
  isSameDay,
  isToday,
  isYesterday,
  isTomorrow,
  isOverdue,
  isDueSoon,
  getLastNDays,
  getNextNDays,
  getEthiopianDate,
  convertToTimezone,
  getTimezoneOffset,
  isDateInRange,
  isWeekend,
  isWorkingDay,
  formatDateForLocale,
  calculateAge,
  formatDuration
}
