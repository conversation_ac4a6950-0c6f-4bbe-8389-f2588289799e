import { executeQuery, initializeDatabase } from '../lib/mysql-connection'

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...')

    // Initialize database tables first
    await initializeDatabase()

    // Clear existing data
    await executeQuery('DELETE FROM maintenance_schedules')
    await executeQuery('DELETE FROM alerts')
    await executeQuery('DELETE FROM transformers')

    console.log('📊 Seeding transformers data...')

    // Seed transformers data
    const transformers = [
      // Addis Ababa Region
      { id: 'T001', name: 'Central Distribution Transformer 1', location: 'Addis Ababa Central', region: 'Addis Ababa', status: 'operational', voltage: '33/11 kV', capacity: '25 MVA', load_percentage: 78.5, condition_score: 92, created_at: '2024-01-15' },
      { id: 'T002', name: 'Bole Industrial Transformer', location: 'Bole Industrial Zone', region: 'Addis Ababa', status: 'operational', voltage: '132/33 kV', capacity: '50 MVA', load_percentage: 82.1, condition_score: 88, created_at: '2024-01-10' },
      { id: 'T003', name: 'Kirkos Residential Transformer', location: 'Kirkos Sub City', region: 'Addis Ababa', status: 'maintenance', voltage: '11/0.4 kV', capacity: '1 MVA', load_percentage: 0, condition_score: 75, created_at: '2024-01-20' },
      { id: 'T004', name: 'Mercato Commercial Transformer', location: 'Mercato Area', region: 'Addis Ababa', status: 'burned', voltage: '33/11 kV', capacity: '15 MVA', load_percentage: 0, condition_score: 25, created_at: '2024-02-05' },

      // Oromia Region
      { id: 'T005', name: 'Adama Main Transformer', location: 'Adama City', region: 'Oromia', status: 'operational', voltage: '132/33 kV', capacity: '63 MVA', load_percentage: 76.3, condition_score: 90, created_at: '2024-01-08' },
      { id: 'T006', name: 'Jimma Distribution Transformer', location: 'Jimma Town', region: 'Oromia', status: 'operational', voltage: '33/11 kV', capacity: '25 MVA', load_percentage: 68.7, condition_score: 85, created_at: '2024-01-12' },
      { id: 'T007', name: 'Nekemte Power Transformer', location: 'Nekemte City', region: 'Oromia', status: 'maintenance', voltage: '132/33 kV', capacity: '40 MVA', load_percentage: 0, condition_score: 78, created_at: '2024-02-01' },
      { id: 'T008', name: 'Shashemene Industrial Transformer', location: 'Shashemene Industrial Park', region: 'Oromia', status: 'burned', voltage: '33/11 kV', capacity: '20 MVA', load_percentage: 0, condition_score: 30, created_at: '2024-02-10' },

      // Amhara Region
      { id: 'T009', name: 'Bahir Dar Main Transformer', location: 'Bahir Dar City', region: 'Amhara', status: 'operational', voltage: '132/33 kV', capacity: '50 MVA', load_percentage: 74.8, condition_score: 87, created_at: '2024-01-05' },
      { id: 'T010', name: 'Gondar Distribution Transformer', location: 'Gondar Town', region: 'Amhara', status: 'operational', voltage: '33/11 kV', capacity: '30 MVA', load_percentage: 71.2, condition_score: 83, created_at: '2024-01-18' },
      { id: 'T011', name: 'Dessie Power Transformer', location: 'Dessie City', region: 'Amhara', status: 'maintenance', voltage: '132/33 kV', capacity: '35 MVA', load_percentage: 0, condition_score: 80, created_at: '2024-01-25' },

      // Tigray Region
      { id: 'T012', name: 'Mekelle Central Transformer', location: 'Mekelle City', region: 'Tigray', status: 'operational', voltage: '132/33 kV', capacity: '40 MVA', load_percentage: 69.5, condition_score: 89, created_at: '2024-01-14' },
      { id: 'T013', name: 'Axum Distribution Transformer', location: 'Axum Town', region: 'Tigray', status: 'operational', voltage: '33/11 kV', capacity: '15 MVA', load_percentage: 65.3, condition_score: 86, created_at: '2024-01-22' },

      // SNNPR Region
      { id: 'T014', name: 'Hawassa Main Transformer', location: 'Hawassa City', region: 'SNNPR', status: 'operational', voltage: '132/33 kV', capacity: '45 MVA', load_percentage: 73.5, condition_score: 88, created_at: '2024-01-16' },
      { id: 'T015', name: 'Arba Minch Transformer', location: 'Arba Minch Town', region: 'SNNPR', status: 'burned', voltage: '33/11 kV', capacity: '20 MVA', load_percentage: 0, condition_score: 35, created_at: '2024-02-08' }
    ]

    for (const transformer of transformers) {
      await executeQuery(`
        INSERT INTO transformers (id, name, location, region, status, voltage, capacity, load_percentage, condition_score, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        transformer.id,
        transformer.name,
        transformer.location,
        transformer.region,
        transformer.status,
        transformer.voltage,
        transformer.capacity,
        transformer.load_percentage,
        transformer.condition_score,
        transformer.created_at
      ])
    }

    console.log('🔧 Seeding maintenance schedules...')

    // Seed maintenance schedules
    const maintenanceSchedules = [
      { id: 'M001', transformer_id: 'T001', maintenance_type: 'Preventive', status: 'completed', scheduled_date: '2024-01-15', completed_date: '2024-01-17', completion_days: 2 },
      { id: 'M002', transformer_id: 'T002', maintenance_type: 'Preventive', status: 'pending', scheduled_date: '2024-03-15', completion_days: null },
      { id: 'M003', transformer_id: 'T003', maintenance_type: 'Corrective', status: 'in_progress', scheduled_date: '2024-02-20', completion_days: null },
      { id: 'M004', transformer_id: 'T005', maintenance_type: 'Preventive', status: 'completed', scheduled_date: '2024-01-08', completed_date: '2024-01-10', completion_days: 2 },
      { id: 'M005', transformer_id: 'T007', maintenance_type: 'Emergency', status: 'overdue', scheduled_date: '2024-02-01', completion_days: null },
      { id: 'M006', transformer_id: 'T009', maintenance_type: 'Inspection', status: 'completed', scheduled_date: '2024-01-05', completed_date: '2024-01-06', completion_days: 1 },
      { id: 'M007', transformer_id: 'T011', maintenance_type: 'Corrective', status: 'in_progress', scheduled_date: '2024-01-25', completion_days: null },
      { id: 'M008', transformer_id: 'T012', maintenance_type: 'Preventive', status: 'pending', scheduled_date: '2024-03-20', completion_days: null }
    ]

    for (const maintenance of maintenanceSchedules) {
      await executeQuery(`
        INSERT INTO maintenance_schedules (id, transformer_id, maintenance_type, status, scheduled_date, completed_date, completion_days, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        maintenance.id,
        maintenance.transformer_id,
        maintenance.maintenance_type,
        maintenance.status,
        maintenance.scheduled_date,
        maintenance.completed_date,
        maintenance.completion_days
      ])
    }

    console.log('🚨 Seeding alerts...')

    // Seed alerts
    const alerts = [
      { id: 'A001', transformer_id: 'T004', priority: 'critical', status: 'active', message: 'Transformer burned - immediate replacement required', resolution_time: null },
      { id: 'A002', transformer_id: 'T008', priority: 'critical', status: 'active', message: 'Transformer failure detected', resolution_time: null },
      { id: 'A003', transformer_id: 'T015', priority: 'critical', status: 'active', message: 'Transformer burned - service disrupted', resolution_time: null },
      { id: 'A004', transformer_id: 'T003', priority: 'high', status: 'active', message: 'Maintenance required - oil leak detected', resolution_time: null },
      { id: 'A005', transformer_id: 'T007', priority: 'high', status: 'active', message: 'Overdue maintenance - cooling system issue', resolution_time: null },
      { id: 'A006', transformer_id: 'T001', priority: 'medium', status: 'resolved', message: 'High load detected', resolution_time: 4.5 },
      { id: 'A007', transformer_id: 'T002', priority: 'low', status: 'resolved', message: 'Routine inspection reminder', resolution_time: 2.0 },
      { id: 'A008', transformer_id: 'T005', priority: 'medium', status: 'resolved', message: 'Temperature threshold exceeded', resolution_time: 6.2 }
    ]

    for (const alert of alerts) {
      await executeQuery(`
        INSERT INTO alerts (id, transformer_id, priority, status, message, resolution_time, created_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        alert.id,
        alert.transformer_id,
        alert.priority,
        alert.status,
        alert.message,
        alert.resolution_time
      ])
    }

    console.log('✅ Database seeding completed successfully!')
    console.log(`📊 Seeded:`)
    console.log(`   - ${transformers.length} transformers`)
    console.log(`   - ${maintenanceSchedules.length} maintenance schedules`)
    console.log(`   - ${alerts.length} alerts`)

  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('🎉 Database seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Database seeding failed:', error)
      process.exit(1)
    })
}

export default seedDatabase
