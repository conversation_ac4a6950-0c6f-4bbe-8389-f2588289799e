"use client"

import type React from "react"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Textarea } from "@/src/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useToast } from "@/src/components/ui/use-toast"
import { AlertTriangle, CalendarIcon, PlusCircle, X } from "lucide-react"
import { Calendar } from "@/src/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/src/lib/utils"
import type { Transformer, MeggerTest } from "@/src/types/transformer"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/src/components/ui/radio-group"
import { Badge } from "@/src/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/src/components/ui/alert"
import { transformerService } from "@/src/services/transformer-service"

interface AddTestResultDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  transformer?: Transformer
  onTestResultAdded?: () => void
}

type TestParameter = {
  id: string
  name: string
  value: string
  unit: string
}

export function AddTestResultDialog({ open, onOpenChange, transformer, onTestResultAdded }: AddTestResultDialogProps) {
  const [testType, setTestType] = useState("")
  const [testDate, setTestDate] = useState<Date | undefined>(new Date())
  const [technician, setTechnician] = useState("")
  const [nextTestDate, setNextTestDate] = useState<Date | undefined>(new Date())
  const [status, setStatus] = useState("pass")
  const [conclusion, setConclusion] = useState("")
  const [observations, setObservations] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [parameters, setParameters] = useState<TestParameter[]>([])
  const [newParameter, setNewParameter] = useState({ name: "", value: "", unit: "" })

  // Megger test specific fields
  const [insulationResistanceHV, setInsulationResistanceHV] = useState("")
  const [insulationResistanceLV, setInsulationResistanceLV] = useState("")
  const [insulationResistanceHVLV, setInsulationResistanceHVLV] = useState("")
  const [polarizationIndex, setPolarizationIndex] = useState("")
  const [dielectricAbsorptionRatio, setDielectricAbsorptionRatio] = useState("")
  const [testVoltage, setTestVoltage] = useState("")
  const [ambientTemperature, setAmbientTemperature] = useState("")
  const [humidity, setHumidity] = useState("")
  const [testEquipment, setTestEquipment] = useState("")
  const [testDuration, setTestDuration] = useState("")
  const [meggerResult, setMeggerResult] = useState<"Pass" | "Fail" | "Borderline">("Pass")
  const [recommendation, setRecommendation] = useState<"Return to Service" | "Maintenance Required" | "Burnt - Replace" | "Further Testing Required">("Return to Service")
  const [showBurntWarning, setShowBurntWarning] = useState(false)

  const { toast } = useToast()

  // Reset megger test fields when test type changes
  const handleTestTypeChange = (value: string) => {
    setTestType(value)
    if (value !== "megger_test") {
      setShowBurntWarning(false)
    }
  }

  // Update recommendation and warning when megger result changes
  const handleMeggerResultChange = (value: "Pass" | "Fail" | "Borderline") => {
    setMeggerResult(value)
    if (value === "Fail") {
      setRecommendation("Burnt - Replace" as "Return to Service" | "Maintenance Required" | "Burnt - Replace" | "Further Testing Required")
      setShowBurntWarning(true)
    } else if (value === "Borderline") {
      setRecommendation("Maintenance Required" as "Return to Service" | "Maintenance Required" | "Burnt - Replace" | "Further Testing Required")
      setShowBurntWarning(false)
    } else {
      setRecommendation("Return to Service" as "Return to Service" | "Maintenance Required" | "Burnt - Replace" | "Further Testing Required")
      setShowBurntWarning(false)
    }
  }

  const addParameter = () => {
    if (newParameter.name && newParameter.value && newParameter.unit) {
      setParameters([...parameters, { ...newParameter, id: `param-${Date.now()}` }])
      setNewParameter({ name: "", value: "", unit: "" })
    }
  }

  const removeParameter = (id: string) => {
    setParameters(parameters.filter((param) => param.id !== id))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      if (testType === 'megger_test') {
        // Create megger test object
        const meggerTest: MeggerTest = {
          id: `mt-${Date.now()}`,
          testDate: testDate ? format(testDate, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd'),
          performedBy: technician,
          switchgearTeamId: "swt-001", // Default switchgear team ID
          insulationResistanceHV: insulationResistanceHV + " MΩ",
          insulationResistanceLV: insulationResistanceLV + " MΩ",
          insulationResistanceHVLV: insulationResistanceHVLV + " MΩ",
          polarizationIndex: polarizationIndex,
          dielectricAbsorptionRatio: dielectricAbsorptionRatio,
          testVoltage: testVoltage + "V",
          ambientTemperature: ambientTemperature + "°C",
          humidity: humidity + "%",
          testEquipment: testEquipment,
          testDuration: testDuration + " minutes",
          result: meggerResult,
          recommendation: recommendation,
          notes: observations
        }

        console.log('Megger test to save:', meggerTest)

        // Add the megger test to the transformer's megger tests array
        if (transformer && transformer.id) {
          // Create a copy of the existing megger tests array or initialize a new one
          const updatedMeggerTests = [...(transformer.meggerTests || []), meggerTest]

          // If the test failed, update the transformer status to "Burnt"
          if (meggerResult === "Fail") {
            await transformerService.updateTransformerStatus(transformer.id, "Burnt")

            toast({
              title: "Transformer Status Updated",
              description: `Transformer ${transformer.serialNumber} has been marked as Burnt based on the megger test results.`,
              variant: "destructive"
            })
          }

          // Update the transformer with the new megger test
          await transformerService.updateTransformer(transformer.id, {
            meggerTests: updatedMeggerTests
          })

          toast({
            title: "Megger Test Added Successfully",
            description: `Megger test for transformer ${transformer.serialNumber} has been recorded.`,
          })
        }
      } else {
        // Convert parameters array to object format
        const parametersObject: Record<string, string> = {}
        parameters.forEach(param => {
          parametersObject[param.name] = param.value + (param.unit ? ` ${param.unit}` : '')
        })

        // Create test result object
        const testResult = {
          date: testDate ? format(testDate, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd'),
          type: testType === 'oil_quality' ? 'Oil Quality Test' :
                testType === 'winding_resistance' ? 'Winding Resistance Test' :
                testType === 'turns_ratio' ? 'Turns Ratio Test' :
                testType === 'insulation_resistance' ? 'Insulation Resistance Test' :
                testType === 'power_factor' ? 'Power Factor Test' :
                testType === 'dissolved_gas' ? 'Dissolved Gas Analysis' : testType,
          result: status === 'pass' ? 'Pass' :
                  status === 'conditional_pass' ? 'Conditional Pass' : 'Fail',
          performedBy: technician,
          parameters: parametersObject,
          nextTestDate: nextTestDate ? format(nextTestDate, 'yyyy-MM-dd') : '',
          conclusion: conclusion,
          observations: observations
        }

        console.log('Test result to save:', testResult)

        // Add the test result to the transformer's test results array
        if (transformer && transformer.id) {
          // Create a copy of the existing test results array or initialize a new one
          const updatedTestResults = [...(transformer.testResults || []), testResult]

          // Update the transformer with the new test result
          await transformerService.updateTransformer(transformer.id, {
            testResults: updatedTestResults
          })
        }

        toast({
          title: "Test Result Added Successfully",
          description: `Test result for transformer ${transformer?.serialNumber} has been recorded.`,
        })
      }

      // Reset form
      setTestType("")
      setTestDate(new Date())
      setTechnician("")
      setNextTestDate(new Date())
      setStatus("pass")
      setConclusion("")
      setObservations("")
      setParameters([])
      setInsulationResistanceHV("")
      setInsulationResistanceLV("")
      setInsulationResistanceHVLV("")
      setPolarizationIndex("")
      setDielectricAbsorptionRatio("")
      setTestVoltage("")
      setAmbientTemperature("")
      setHumidity("")
      setTestEquipment("")
      setTestDuration("")
      setMeggerResult("Pass")
      setRecommendation("Return to Service")
      setShowBurntWarning(false)

      // Call the onTestResultAdded callback if provided
      if (onTestResultAdded) {
        onTestResultAdded()
      }

      onOpenChange(false)
    } catch (error) {
      console.error("Error adding test result:", error)
      toast({
        title: "Error Adding Test Result",
        description: "There was an error adding the test result. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Test Result</DialogTitle>
          <DialogDescription>
            Add a new test result for transformer {transformer?.serialNumber || ""}. Fill out the form below with all
            test details and parameters.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="testType">Test Type</Label>
                <Select value={testType} onValueChange={handleTestTypeChange} required>
                  <SelectTrigger id="testType">
                    <SelectValue placeholder="Select test type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="oil_quality">Oil Quality Test</SelectItem>
                    <SelectItem value="winding_resistance">Winding Resistance Test</SelectItem>
                    <SelectItem value="turns_ratio">Turns Ratio Test</SelectItem>
                    <SelectItem value="insulation_resistance">Insulation Resistance Test</SelectItem>
                    <SelectItem value="power_factor">Power Factor Test</SelectItem>
                    <SelectItem value="dissolved_gas">Dissolved Gas Analysis</SelectItem>
                    <SelectItem value="megger_test">Megger Test</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="technician">Technician Name</Label>
                <Input
                  id="technician"
                  value={technician}
                  onChange={(e) => setTechnician(e.target.value)}
                  placeholder="Name of the technician"
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="testDate">Test Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn("w-full justify-start text-left font-normal", !testDate && "text-muted-foreground")}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {testDate ? format(testDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={testDate} onSelect={setTestDate} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="nextTestDate">Next Test Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !nextTestDate && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {nextTestDate ? format(nextTestDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={nextTestDate} onSelect={setNextTestDate} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {testType === 'megger_test' ? (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Megger Test Parameters</CardTitle>
                  <p className="text-xs text-muted-foreground">
                    Enter the insulation resistance measurements and other test parameters
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {showBurntWarning && (
                      <Alert variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertTitle>Warning: Transformer Will Be Marked as Burnt</AlertTitle>
                        <AlertDescription>
                          This test result will mark the transformer as burnt. This action will update the transformer status and affect its availability.
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="insulationResistanceHV">Insulation Resistance HV (MΩ)</Label>
                        <Input
                          id="insulationResistanceHV"
                          value={insulationResistanceHV}
                          onChange={(e) => setInsulationResistanceHV(e.target.value)}
                          placeholder="e.g., 500"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="insulationResistanceLV">Insulation Resistance LV (MΩ)</Label>
                        <Input
                          id="insulationResistanceLV"
                          value={insulationResistanceLV}
                          onChange={(e) => setInsulationResistanceLV(e.target.value)}
                          placeholder="e.g., 250"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="insulationResistanceHVLV">Insulation Resistance HV-LV (MΩ)</Label>
                        <Input
                          id="insulationResistanceHVLV"
                          value={insulationResistanceHVLV}
                          onChange={(e) => setInsulationResistanceHVLV(e.target.value)}
                          placeholder="e.g., 400"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="polarizationIndex">Polarization Index</Label>
                        <Input
                          id="polarizationIndex"
                          value={polarizationIndex}
                          onChange={(e) => setPolarizationIndex(e.target.value)}
                          placeholder="e.g., 1.5"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="dielectricAbsorptionRatio">Dielectric Absorption Ratio</Label>
                        <Input
                          id="dielectricAbsorptionRatio"
                          value={dielectricAbsorptionRatio}
                          onChange={(e) => setDielectricAbsorptionRatio(e.target.value)}
                          placeholder="e.g., 1.3"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="testVoltage">Test Voltage (V)</Label>
                        <Input
                          id="testVoltage"
                          value={testVoltage}
                          onChange={(e) => setTestVoltage(e.target.value)}
                          placeholder="e.g., 5000"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="ambientTemperature">Ambient Temperature (°C)</Label>
                        <Input
                          id="ambientTemperature"
                          value={ambientTemperature}
                          onChange={(e) => setAmbientTemperature(e.target.value)}
                          placeholder="e.g., 25"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="humidity">Humidity (%)</Label>
                        <Input
                          id="humidity"
                          value={humidity}
                          onChange={(e) => setHumidity(e.target.value)}
                          placeholder="e.g., 45"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="testDuration">Test Duration (minutes)</Label>
                        <Input
                          id="testDuration"
                          value={testDuration}
                          onChange={(e) => setTestDuration(e.target.value)}
                          placeholder="e.g., 10"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="testEquipment">Test Equipment</Label>
                      <Input
                        id="testEquipment"
                        value={testEquipment}
                        onChange={(e) => setTestEquipment(e.target.value)}
                        placeholder="e.g., Megger MIT1025"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Test Result</Label>
                      <RadioGroup value={meggerResult} onValueChange={handleMeggerResultChange} className="flex gap-4 mt-2">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="Pass" id="result-pass" />
                          <Label htmlFor="result-pass" className="font-normal">
                            Pass
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="Borderline" id="result-borderline" />
                          <Label htmlFor="result-borderline" className="font-normal">
                            Borderline
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="Fail" id="result-fail" />
                          <Label htmlFor="result-fail" className="font-normal">
                            Fail
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="space-y-2">
                      <Label>Recommendation</Label>
                      <Select value={recommendation} onValueChange={(value) => setRecommendation(value as "Return to Service" | "Maintenance Required" | "Burnt - Replace" | "Further Testing Required")}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select recommendation" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Return to Service">Return to Service</SelectItem>
                          <SelectItem value="Maintenance Required">Maintenance Required</SelectItem>
                          <SelectItem value="Further Testing Required">Further Testing Required</SelectItem>
                          <SelectItem value="Burnt - Replace">Burnt - Replace</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex justify-between items-center">
                    <span>Test Parameters</span>
                    <Badge variant="outline" className="ml-2">
                      {parameters.length} parameters
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {parameters.length > 0 && (
                      <div className="space-y-2">
                        {parameters.map((param) => (
                          <div key={param.id} className="flex items-center justify-between bg-muted p-2 rounded-md">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{param.name}:</span>
                              <span>
                                {param.value} {param.unit}
                              </span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeParameter(param.id)}
                              className="h-7 w-7 p-0"
                            >
                              <X className="h-4 w-4" />
                              <span className="sr-only">Remove</span>
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}

                    <div className="grid grid-cols-3 gap-2">
                      <div>
                        <Label htmlFor="param-name">Parameter Name</Label>
                        <Input
                          id="param-name"
                          value={newParameter.name}
                          onChange={(e) => setNewParameter({ ...newParameter, name: e.target.value })}
                          placeholder="e.g., Dielectric Strength"
                        />
                      </div>
                      <div>
                        <Label htmlFor="param-value">Value</Label>
                        <Input
                          id="param-value"
                          value={newParameter.value}
                          onChange={(e) => setNewParameter({ ...newParameter, value: e.target.value })}
                          placeholder="e.g., 45"
                        />
                      </div>
                      <div>
                        <Label htmlFor="param-unit">Unit</Label>
                        <Input
                          id="param-unit"
                          value={newParameter.unit}
                          onChange={(e) => setNewParameter({ ...newParameter, unit: e.target.value })}
                          placeholder="e.g., kV"
                        />
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addParameter}
                      className="mt-2"
                      disabled={!newParameter.name || !newParameter.value || !newParameter.unit}
                    >
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Add Parameter
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {testType !== 'megger_test' && (
              <div className="space-y-2">
                <Label>Test Result</Label>
                <RadioGroup value={status} onValueChange={setStatus} className="flex gap-4 mt-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="pass" id="status-pass" />
                    <Label htmlFor="status-pass" className="font-normal">
                      Pass
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="conditional_pass" id="status-conditional" />
                    <Label htmlFor="status-conditional" className="font-normal">
                      Conditional Pass
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="fail" id="status-fail" />
                    <Label htmlFor="status-fail" className="font-normal">
                      Fail
                    </Label>
                  </div>
                </RadioGroup>
              </div>
            )}

            {testType !== 'megger_test' && (
              <div className="space-y-2">
                <Label htmlFor="conclusion">Conclusion</Label>
                <Textarea
                  id="conclusion"
                  placeholder="Summarize your conclusions based on test results"
                  value={conclusion}
                  onChange={(e) => setConclusion(e.target.value)}
                  rows={2}
                  required={testType !== 'megger_test'}
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="observations">
                {testType === 'megger_test' ? 'Additional Notes' : 'Additional Observations'}
              </Label>
              <Textarea
                id="observations"
                placeholder={testType === 'megger_test' ? "Enter any additional notes..." : "Add any additional observations or notes"}
                value={observations}
                onChange={(e) => setObservations(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : "Add Test Result"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
