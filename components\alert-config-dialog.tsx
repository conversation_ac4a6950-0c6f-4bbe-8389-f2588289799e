"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/src/components/ui/tabs"
import { Switch } from "@/src/components/ui/switch"
import { Label } from "@/src/components/ui/label"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useToast } from "@/src/components/ui/use-toast"
import { 
  Bell, 
  Mail, 
  MessageSquare, 
  Smartphone, 
  Settings, 
  Users, 
  Clock, 
  AlertTriangle,
  Thermometer,
  Battery,
  Zap,
  AlertCircle
} from "lucide-react"
import { Slider } from "@/src/components/ui/slider"

interface AlertConfigDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AlertConfigDialog({ open, onOpenChange }: AlertConfigDialogProps) {
  const [activeTab, setActiveTab] = useState("notifications")
  const { toast } = useToast()
  
  // Notification settings
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [smsNotifications, setSmsNotifications] = useState(true)
  const [pushNotifications, setPushNotifications] = useState(true)
  const [criticalAlerts, setCriticalAlerts] = useState(true)
  const [highAlerts, setHighAlerts] = useState(true)
  const [mediumAlerts, setMediumAlerts] = useState(true)
  const [lowAlerts, setLowAlerts] = useState(false)
  
  // Threshold settings
  const [temperatureThreshold, setTemperatureThreshold] = useState(85)
  const [oilLevelThreshold, setOilLevelThreshold] = useState(20)
  const [loadThreshold, setLoadThreshold] = useState(90)
  const [voltageThreshold, setVoltageThreshold] = useState(10)
  
  // Auto-assignment settings
  const [autoAssignment, setAutoAssignment] = useState(true)
  const [defaultAssignee, setDefaultAssignee] = useState("team1")
  
  const handleSave = () => {
    // In a real app, this would save the settings to the backend
    toast({
      title: "Settings Saved",
      description: "Your alert configuration has been updated successfully."
    })
    onOpenChange(false)
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Alert Configuration
          </DialogTitle>
          <DialogDescription>
            Configure alert settings, thresholds, and notification preferences.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-2">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="notifications">
              <Bell className="mr-2 h-4 w-4" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="thresholds">
              <AlertTriangle className="mr-2 h-4 w-4" />
              Thresholds
            </TabsTrigger>
            <TabsTrigger value="assignments">
              <Users className="mr-2 h-4 w-4" />
              Assignments
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="notifications" className="space-y-4 mt-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Notification Channels</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-primary" />
                    <Label htmlFor="email-notifications">Email Notifications</Label>
                  </div>
                  <Switch 
                    id="email-notifications" 
                    checked={emailNotifications} 
                    onCheckedChange={setEmailNotifications} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Smartphone className="h-4 w-4 text-primary" />
                    <Label htmlFor="sms-notifications">SMS Notifications</Label>
                  </div>
                  <Switch 
                    id="sms-notifications" 
                    checked={smsNotifications} 
                    onCheckedChange={setSmsNotifications} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Bell className="h-4 w-4 text-primary" />
                    <Label htmlFor="push-notifications">Push Notifications</Label>
                  </div>
                  <Switch 
                    id="push-notifications" 
                    checked={pushNotifications} 
                    onCheckedChange={setPushNotifications} 
                  />
                </div>
              </div>
              
              <h4 className="text-sm font-medium pt-4">Alert Severity Notifications</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <Label htmlFor="critical-alerts">Critical Alerts</Label>
                  </div>
                  <Switch 
                    id="critical-alerts" 
                    checked={criticalAlerts} 
                    onCheckedChange={setCriticalAlerts} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-orange-500" />
                    <Label htmlFor="high-alerts">High Priority Alerts</Label>
                  </div>
                  <Switch 
                    id="high-alerts" 
                    checked={highAlerts} 
                    onCheckedChange={setHighAlerts} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    <Label htmlFor="medium-alerts">Medium Priority Alerts</Label>
                  </div>
                  <Switch 
                    id="medium-alerts" 
                    checked={mediumAlerts} 
                    onCheckedChange={setMediumAlerts} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-blue-500" />
                    <Label htmlFor="low-alerts">Low Priority Alerts</Label>
                  </div>
                  <Switch 
                    id="low-alerts" 
                    checked={lowAlerts} 
                    onCheckedChange={setLowAlerts} 
                  />
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="thresholds" className="space-y-4 mt-4">
            <div className="space-y-6">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Thermometer className="h-4 w-4 text-red-500" />
                  <Label>Temperature Threshold (°C)</Label>
                </div>
                <div className="flex items-center gap-4">
                  <Slider 
                    value={[temperatureThreshold]} 
                    onValueChange={(value) => setTemperatureThreshold(value[0])}
                    max={100}
                    step={1}
                    className="flex-1"
                  />
                  <span className="w-12 text-center">{temperatureThreshold}°C</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Alert will trigger when temperature exceeds this threshold.
                </p>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Battery className="h-4 w-4 text-orange-500" />
                  <Label>Oil Level Threshold (%)</Label>
                </div>
                <div className="flex items-center gap-4">
                  <Slider 
                    value={[oilLevelThreshold]} 
                    onValueChange={(value) => setOilLevelThreshold(value[0])}
                    max={100}
                    step={1}
                    className="flex-1"
                  />
                  <span className="w-12 text-center">{oilLevelThreshold}%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Alert will trigger when oil level falls below this threshold.
                </p>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-yellow-500" />
                  <Label>Load Threshold (%)</Label>
                </div>
                <div className="flex items-center gap-4">
                  <Slider 
                    value={[loadThreshold]} 
                    onValueChange={(value) => setLoadThreshold(value[0])}
                    max={100}
                    step={1}
                    className="flex-1"
                  />
                  <span className="w-12 text-center">{loadThreshold}%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Alert will trigger when load exceeds this percentage of rated capacity.
                </p>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-blue-500" />
                  <Label>Voltage Deviation Threshold (%)</Label>
                </div>
                <div className="flex items-center gap-4">
                  <Slider 
                    value={[voltageThreshold]} 
                    onValueChange={(value) => setVoltageThreshold(value[0])}
                    max={20}
                    step={1}
                    className="flex-1"
                  />
                  <span className="w-12 text-center">±{voltageThreshold}%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Alert will trigger when voltage deviates from nominal by this percentage.
                </p>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="assignments" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium">Automatic Assignment</h4>
                  <p className="text-xs text-muted-foreground">
                    Automatically assign alerts to default teams or individuals
                  </p>
                </div>
                <Switch 
                  id="auto-assignment" 
                  checked={autoAssignment} 
                  onCheckedChange={setAutoAssignment} 
                />
              </div>
              
              {autoAssignment && (
                <div className="space-y-3 pt-2">
                  <Label htmlFor="default-assignee">Default Assignee</Label>
                  <Select value={defaultAssignee} onValueChange={setDefaultAssignee}>
                    <SelectTrigger id="default-assignee">
                      <SelectValue placeholder="Select default assignee" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user1">John Doe (Technician)</SelectItem>
                      <SelectItem value="user2">Jane Smith (Engineer)</SelectItem>
                      <SelectItem value="user3">Mike Johnson (Supervisor)</SelectItem>
                      <SelectItem value="team1">Tech Team Alpha (Maintenance Team)</SelectItem>
                      <SelectItem value="team2">Field Crew Beta (Field Team)</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <div className="pt-4">
                    <h4 className="text-sm font-medium">Assignment Rules</h4>
                    <p className="text-xs text-muted-foreground mb-2">
                      Configure rules for automatic assignment based on alert type and severity
                    </p>
                    
                    <div className="space-y-2 border rounded-md p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Thermometer className="h-4 w-4 text-red-500" />
                          <span className="text-sm">Temperature Alerts</span>
                        </div>
                        <Select defaultValue="team1">
                          <SelectTrigger className="w-[180px]">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="user1">John Doe</SelectItem>
                            <SelectItem value="user2">Jane Smith</SelectItem>
                            <SelectItem value="team1">Tech Team Alpha</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Battery className="h-4 w-4 text-orange-500" />
                          <span className="text-sm">Oil Level Alerts</span>
                        </div>
                        <Select defaultValue="user2">
                          <SelectTrigger className="w-[180px]">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="user1">John Doe</SelectItem>
                            <SelectItem value="user2">Jane Smith</SelectItem>
                            <SelectItem value="team1">Tech Team Alpha</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm">Load Alerts</span>
                        </div>
                        <Select defaultValue="team2">
                          <SelectTrigger className="w-[180px]">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="user3">Mike Johnson</SelectItem>
                            <SelectItem value="team1">Tech Team Alpha</SelectItem>
                            <SelectItem value="team2">Field Crew Beta</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="pt-4">
                <h4 className="text-sm font-medium">Escalation Settings</h4>
                <p className="text-xs text-muted-foreground mb-2">
                  Configure when alerts should be escalated if not acknowledged
                </p>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="critical-escalation">Critical Alerts</Label>
                    <Select defaultValue="15">
                      <SelectTrigger id="critical-escalation">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">After 5 minutes</SelectItem>
                        <SelectItem value="15">After 15 minutes</SelectItem>
                        <SelectItem value="30">After 30 minutes</SelectItem>
                        <SelectItem value="60">After 1 hour</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="high-escalation">High Priority Alerts</Label>
                    <Select defaultValue="30">
                      <SelectTrigger id="high-escalation">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="15">After 15 minutes</SelectItem>
                        <SelectItem value="30">After 30 minutes</SelectItem>
                        <SelectItem value="60">After 1 hour</SelectItem>
                        <SelectItem value="120">After 2 hours</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="medium-escalation">Medium Priority Alerts</Label>
                    <Select defaultValue="60">
                      <SelectTrigger id="medium-escalation">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30">After 30 minutes</SelectItem>
                        <SelectItem value="60">After 1 hour</SelectItem>
                        <SelectItem value="120">After 2 hours</SelectItem>
                        <SelectItem value="240">After 4 hours</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="low-escalation">Low Priority Alerts</Label>
                    <Select defaultValue="240">
                      <SelectTrigger id="low-escalation">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="60">After 1 hour</SelectItem>
                        <SelectItem value="120">After 2 hours</SelectItem>
                        <SelectItem value="240">After 4 hours</SelectItem>
                        <SelectItem value="480">After 8 hours</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
