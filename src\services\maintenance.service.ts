/**
 * Maintenance Service
 * Data access layer for maintenance operations
 */

import { executeQuery } from '@/src/lib/database'
import { MaintenanceSchedule, FilterOptions } from '@/src/types'

/**
 * Get pending maintenance with optional filtering
 */
export async function getPendingMaintenance(filters: FilterOptions = {}): Promise<MaintenanceSchedule[]> {
  try {
    let query = `
      SELECT 
        m.*,
        t.name as transformer_name,
        t.serial_number as transformer_serial,
        r.name as region_name,
        sc.name as service_center_name
      FROM dtms_maintenance_schedules m
      LEFT JOIN dtms_transformers t ON m.transformer_id = t.id
      LEFT JOIN dtms_regions r ON t.region_id = r.id
      LEFT JOIN dtms_service_centers sc ON t.service_center_id = sc.id
      WHERE m.status IN ('scheduled', 'in_progress')
    `
    
    const params: any[] = []
    
    // Apply region filter
    if (filters.regions?.length) {
      query += ` AND r.code IN (${filters.regions.map(() => '?').join(',')})`
      params.push(...filters.regions)
    }
    
    // Apply service center filter
    if (filters.serviceCenters?.length) {
      query += ` AND sc.code IN (${filters.serviceCenters.map(() => '?').join(',')})`
      params.push(...filters.serviceCenters)
    }
    
    query += ` ORDER BY m.scheduled_date ASC LIMIT 100`
    
    const maintenance = await executeQuery<MaintenanceSchedule>(query, params)
    return maintenance
  } catch (error) {
    console.error('Error fetching pending maintenance:', error)
    return [] // Return empty array instead of throwing
  }
}

/**
 * Get maintenance by ID
 */
export async function getMaintenanceById(id: string): Promise<MaintenanceSchedule | null> {
  try {
    const query = `
      SELECT 
        m.*,
        t.name as transformer_name,
        t.serial_number as transformer_serial
      FROM dtms_maintenance_schedules m
      LEFT JOIN dtms_transformers t ON m.transformer_id = t.id
      WHERE m.id = ?
    `
    
    const maintenance = await executeQuery<MaintenanceSchedule>(query, [id])
    return maintenance[0] || null
  } catch (error) {
    console.error('Error fetching maintenance:', error)
    throw new Error('Failed to fetch maintenance')
  }
}

/**
 * Update maintenance status
 */
export async function updateMaintenanceStatus(
  id: string, 
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
): Promise<boolean> {
  try {
    const query = `
      UPDATE dtms_maintenance_schedules 
      SET 
        status = ?,
        updated_at = NOW(),
        ${status === 'completed' ? 'completed_date = NOW(),' : ''}
      WHERE id = ?
    `
    
    await executeQuery(query, [status, id])
    return true
  } catch (error) {
    console.error('Error updating maintenance status:', error)
    throw new Error('Failed to update maintenance status')
  }
}
