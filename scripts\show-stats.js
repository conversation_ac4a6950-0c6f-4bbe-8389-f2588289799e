const mysql = require('mysql2/promise');

async function showStatistics() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'dtms_eeu_db'
  });
  
  try {
    console.log('📊 Transformer Database Statistics\n');
    
    // Total count
    const [total] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    console.log(`🔢 Total Transformers: ${total[0].count}`);
    
    // Status distribution
    console.log('\n📈 Status Distribution:');
    const [statusStats] = await connection.execute(`
      SELECT status, COUNT(*) as count, 
             ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM app_transformers), 1) as percentage
      FROM app_transformers 
      GROUP BY status 
      ORDER BY count DESC
    `);
    statusStats.forEach(row => {
      console.log(`  ${row.status}: ${row.count} (${row.percentage}%)`);
    });
    
    // Type distribution
    console.log('\n🔧 Type Distribution:');
    const [typeStats] = await connection.execute(`
      SELECT type, COUNT(*) as count, 
             AVG(capacity_kva) as avg_capacity,
             MIN(capacity_kva) as min_capacity,
             MAX(capacity_kva) as max_capacity
      FROM app_transformers 
      GROUP BY type 
      ORDER BY count DESC
    `);
    typeStats.forEach(row => {
      console.log(`  ${row.type}: ${row.count} transformers`);
      console.log(`    Capacity range: ${row.min_capacity} - ${row.max_capacity} kVA`);
      console.log(`    Average capacity: ${Math.round(row.avg_capacity)} kVA`);
    });
    
    // Manufacturer distribution
    console.log('\n🏭 Manufacturer Distribution:');
    const [mfgStats] = await connection.execute(`
      SELECT manufacturer, COUNT(*) as count
      FROM app_transformers 
      GROUP BY manufacturer 
      ORDER BY count DESC
    `);
    mfgStats.forEach(row => {
      console.log(`  ${row.manufacturer}: ${row.count} transformers`);
    });
    
    // Regional distribution
    console.log('\n🌍 Regional Distribution:');
    const [regionStats] = await connection.execute(`
      SELECT r.name as region_name, COUNT(t.id) as count
      FROM app_regions r
      LEFT JOIN app_transformers t ON r.id = t.region_id
      GROUP BY r.id, r.name
      ORDER BY count DESC
    `);
    regionStats.forEach(row => {
      console.log(`  ${row.region_name}: ${row.count} transformers`);
    });
    
    // Capacity statistics
    console.log('\n⚡ Capacity Statistics:');
    const [capacityStats] = await connection.execute(`
      SELECT 
        MIN(capacity_kva) as min_capacity,
        MAX(capacity_kva) as max_capacity,
        AVG(capacity_kva) as avg_capacity,
        SUM(capacity_kva) as total_capacity
      FROM app_transformers
    `);
    const stats = capacityStats[0];
    console.log(`  Minimum: ${stats.min_capacity} kVA`);
    console.log(`  Maximum: ${stats.max_capacity} kVA`);
    console.log(`  Average: ${Math.round(stats.avg_capacity)} kVA`);
    console.log(`  Total: ${Math.round(stats.total_capacity)} kVA`);
    
    // Health metrics
    console.log('\n🏥 Health Metrics:');
    const [healthStats] = await connection.execute(`
      SELECT 
        AVG(efficiency_rating) as avg_efficiency,
        AVG(load_factor) as avg_load,
        AVG(temperature) as avg_temp,
        AVG(oil_level) as avg_oil
      FROM app_transformers
    `);
    const health = healthStats[0];
    console.log(`  Average Efficiency: ${Math.round(health.avg_efficiency * 100) / 100}%`);
    console.log(`  Average Load Factor: ${Math.round(health.avg_load * 100) / 100}%`);
    console.log(`  Average Temperature: ${Math.round(health.avg_temp * 100) / 100}°C`);
    console.log(`  Average Oil Level: ${Math.round(health.avg_oil * 100) / 100}%`);
    
    // Recent installations
    console.log('\n📅 Installation Timeline:');
    const [installStats] = await connection.execute(`
      SELECT 
        YEAR(installation_date) as year,
        COUNT(*) as count
      FROM app_transformers 
      WHERE installation_date IS NOT NULL
      GROUP BY YEAR(installation_date)
      ORDER BY year DESC
    `);
    installStats.forEach(row => {
      console.log(`  ${row.year}: ${row.count} installations`);
    });
    
    console.log('\n✅ Statistics complete!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await connection.end();
  }
}

showStatistics();
