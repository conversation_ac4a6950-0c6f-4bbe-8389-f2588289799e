import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // Import MySQL database service
    const { default: MySQLDatabaseService } = await import('../../../src/services/mysql-database-service')

    // Test connection first
    const isConnected = await MySQLDatabaseService.testConnection()
    if (!isConnected) {
      return NextResponse.json({
        success: false,
        message: 'Database connection failed'
      }, { status: 500 })
    }

    // Clear existing data first
    console.log('🧹 Clearing existing data...')
    try {
      await MySQLDatabaseService.clearAllData()
      console.log('✅ Existing data cleared successfully')
    } catch (error) {
      console.warn('⚠️ Warning: Could not clear existing data:', error)
    }

    // Generate comprehensive sample data
    const currentTime = new Date()

    // Ethiopian regions and cities
    const regions = [
      { id: 'addis-ababa', name: 'Addis Ababa', cities: ['Addis Ababa', '<PERSON><PERSON>', 'Kirkos', 'Ye<PERSON>'] },
      { id: 'or<PERSON>a', name: 'Oromia', cities: ['Adama', 'Jimma', 'Nek<PERSON><PERSON>', 'Hawassa'] },
      { id: 'amhara', name: 'Amhara', cities: ['Bahir Dar', 'Gondar', 'Dessie', 'Debre Birhan'] },
      { id: 'tigray', name: 'Tigray', cities: ['Mekelle', 'Axum', 'Adigrat', 'Shire'] },
      { id: 'snnp', name: 'SNNP', cities: ['Hawassa', 'Arba Minch', 'Sodo', 'Dilla'] }
    ]

    const manufacturers = ['ABB', 'Siemens', 'GE', 'Schneider Electric', 'Hyundai', 'TBEA']
    const transformerTypes = ['Distribution', 'Power', 'Pad Mounted', 'Pole Mounted', 'Dry Type']
    const voltageRatings = [
      { primary: 15000, secondary: 400, level: '15kV/400V' },
      { primary: 33000, secondary: 15000, level: '33kV/15kV' },
      { primary: 132000, secondary: 33000, level: '132kV/33kV' },
      { primary: 230000, secondary: 132000, level: '230kV/132kV' }
    ]

    // Sample transformers with realistic Ethiopian data
    const sampleTransformers = Array.from({ length: 100 }, (_, i) => {
      const region = regions[Math.floor(Math.random() * regions.length)]
      const city = region.cities[Math.floor(Math.random() * region.cities.length)]
      const voltage = voltageRatings[Math.floor(Math.random() * voltageRatings.length)]
      const manufacturer = manufacturers[Math.floor(Math.random() * manufacturers.length)]
      const type = transformerTypes[Math.floor(Math.random() * transformerTypes.length)]

      return {
        serial_number: `${manufacturer.substring(0, 3).toUpperCase()}-${(i + 1).toString().padStart(4, '0')}`,
        name: `${city} ${type} Transformer ${i + 1}`,
        manufacturer,
        model: `${manufacturer}-${type.replace(' ', '')}-${Math.floor(Math.random() * 1000) + 100}`,
        type,
        capacity: [100, 250, 500, 630, 1000, 1600, 2500][Math.floor(Math.random() * 7)],
        voltage_primary: voltage.primary,
        voltage_secondary: voltage.secondary,
        voltage_level: voltage.level,
        installation_date: new Date(2018 + Math.floor(Math.random() * 6), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
        last_maintenance: new Date(currentTime.getTime() - Math.random() * 90 * 24 * 60 * 60 * 1000),
        next_maintenance: new Date(currentTime.getTime() + Math.random() * 180 * 24 * 60 * 60 * 1000),
        status: ['operational', 'warning', 'maintenance', 'critical', 'offline'][Math.floor(Math.random() * 5)],
        health_index: Math.floor(Math.random() * 40) + 60, // 60-100
        temperature: Math.floor(Math.random() * 30) + 25, // 25-55°C
        load_percentage: Math.floor(Math.random() * 85) + 15, // 15-100%
        oil_level: Math.floor(Math.random() * 20) + 80, // 80-100%
        region_id: region.id,
        service_center_id: `sc-${region.id}-${Math.floor(Math.random() * 3) + 1}`,
        location_address: `${city} Substation, ${region.name}, Ethiopia`,
        lat: 9.0 + Math.random() * 5, // Ethiopian latitude range
        lng: 38.0 + Math.random() * 10, // Ethiopian longitude range
        specifications: JSON.stringify({
          cooling_type: ['ONAN', 'ONAF', 'OFAF'][Math.floor(Math.random() * 3)],
          tap_changer: Math.random() > 0.5,
          protection_class: ['IP54', 'IP55', 'IP65'][Math.floor(Math.random() * 3)],
          frequency: 50,
          phases: 3,
          connection_type: ['Yyn0', 'Dyn11', 'YNd11'][Math.floor(Math.random() * 3)],
          insulation_class: ['A', 'B', 'F', 'H'][Math.floor(Math.random() * 4)],
          weight: Math.floor(Math.random() * 2000) + 500,
          oil_volume: Math.floor(Math.random() * 500) + 100
        }),
        financial_data: JSON.stringify({
          purchase_cost: Math.floor(Math.random() * 100000) + 50000,
          installation_cost: Math.floor(Math.random() * 20000) + 10000,
          maintenance_cost_annual: Math.floor(Math.random() * 10000) + 2000,
          warranty_years: Math.floor(Math.random() * 3) + 3,
          depreciation_rate: 0.05 + Math.random() * 0.05,
          insurance_value: Math.floor(Math.random() * 80000) + 40000
        }),
        maintenance_history: JSON.stringify([
          {
            date: new Date(currentTime.getTime() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            type: 'Preventive Maintenance',
            technician: ['Abebe Tadesse', 'Meron Haile', 'Dawit Bekele', 'Sara Tesfaye'][Math.floor(Math.random() * 4)],
            cost: Math.floor(Math.random() * 15000) + 5000,
            notes: 'Routine maintenance completed successfully'
          }
        ]),
        created_at: currentTime,
        updated_at: currentTime
      }
    })

    // Sample alerts with realistic scenarios
    const alertTypes = [
      { title: 'High Temperature Alert', description: 'Transformer temperature exceeding normal operating range', severity: 'warning' },
      { title: 'Overload Condition', description: 'Transformer load exceeding 90% capacity', severity: 'critical' },
      { title: 'Oil Level Low', description: 'Transformer oil level below minimum threshold', severity: 'warning' },
      { title: 'Voltage Fluctuation', description: 'Abnormal voltage readings detected', severity: 'warning' },
      { title: 'Cooling System Failure', description: 'Cooling fan malfunction detected', severity: 'critical' },
      { title: 'Insulation Resistance Low', description: 'Insulation resistance below acceptable limits', severity: 'critical' },
      { title: 'Tap Changer Issue', description: 'Tap changer operation anomaly detected', severity: 'warning' },
      { title: 'Ground Fault', description: 'Ground fault detected in transformer circuit', severity: 'critical' },
      { title: 'Maintenance Due', description: 'Scheduled maintenance approaching due date', severity: 'info' },
      { title: 'Power Quality Issue', description: 'Harmonic distortion levels elevated', severity: 'warning' }
    ]

    const sampleAlerts = Array.from({ length: 25 }, (_, i) => {
      const alertType = alertTypes[Math.floor(Math.random() * alertTypes.length)]
      return {
        title: alertType.title,
        description: alertType.description,
        severity: alertType.severity,
        status: ['active', 'acknowledged', 'resolved'][Math.floor(Math.random() * 3)],
        transformer_id: Math.floor(Math.random() * 100) + 1,
        alert_code: `ALT-${(i + 1).toString().padStart(4, '0')}`,
        location: regions[Math.floor(Math.random() * regions.length)].name,
        priority: alertType.severity === 'critical' ? 'high' : alertType.severity === 'warning' ? 'medium' : 'low',
        assigned_to: ['Abebe Tadesse', 'Meron Haile', 'Dawit Bekele', 'Sara Tesfaye', 'Yohannes Girma'][Math.floor(Math.random() * 5)],
        created_at: new Date(currentTime.getTime() - Math.random() * 14 * 24 * 60 * 60 * 1000),
        updated_at: currentTime
      }
    })

    // Sample maintenance records with comprehensive data
    const maintenanceTypes = [
      'Preventive Maintenance', 'Corrective Maintenance', 'Predictive Maintenance',
      'Emergency Repair', 'Inspection', 'Testing', 'Oil Analysis', 'Cleaning'
    ]

    const technicians = [
      'Abebe Tadesse', 'Meron Haile', 'Dawit Bekele', 'Sara Tesfaye', 'Yohannes Girma',
      'Tigist Alemayehu', 'Mulugeta Assefa', 'Hanan Mohammed', 'Getachew Worku', 'Almaz Tesfaye'
    ]

    const sampleMaintenance = Array.from({ length: 50 }, (_, i) => ({
      transformer_id: Math.floor(Math.random() * 100) + 1,
      type: maintenanceTypes[Math.floor(Math.random() * maintenanceTypes.length)],
      description: `Comprehensive maintenance activities including inspection, testing, and component replacement as needed`,
      scheduled_date: new Date(currentTime.getTime() + (Math.random() - 0.5) * 60 * 24 * 60 * 60 * 1000), // ±60 days
      completed_date: Math.random() > 0.7 ? new Date(currentTime.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000) : null,
      priority: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)],
      status: ['scheduled', 'in_progress', 'completed', 'cancelled'][Math.floor(Math.random() * 4)],
      assigned_technician: technicians[Math.floor(Math.random() * technicians.length)],
      estimated_duration: Math.floor(Math.random() * 8) + 2, // 2-10 hours
      actual_duration: Math.random() > 0.5 ? Math.floor(Math.random() * 10) + 1 : null,
      cost: Math.floor(Math.random() * 15000) + 5000,
      parts_used: JSON.stringify([
        { name: 'Oil Filter', quantity: Math.floor(Math.random() * 3) + 1, cost: 500 },
        { name: 'Gasket Set', quantity: 1, cost: 200 },
        { name: 'Insulation Oil', quantity: Math.floor(Math.random() * 100) + 50, cost: 50 }
      ]),
      safety_requirements: 'PPE required, lockout/tagout procedures, confined space entry protocols',
      notes: 'All safety protocols followed. Equipment performance within normal parameters.',
      created_at: currentTime,
      updated_at: currentTime
    }))

    // Insert data into database
    let insertedTransformers = 0
    let insertedAlerts = 0
    let insertedMaintenance = 0

    // Insert transformers
    for (const transformer of sampleTransformers) {
      try {
        await MySQLDatabaseService.addTransformer(transformer)
        insertedTransformers++
      } catch (error) {
        console.warn('Error inserting transformer:', error)
      }
    }

    // Insert alerts
    for (const alert of sampleAlerts) {
      try {
        await MySQLDatabaseService.addAlert(alert)
        insertedAlerts++
      } catch (error) {
        console.warn('Error inserting alert:', error)
      }
    }

    // Insert maintenance records
    for (const maintenance of sampleMaintenance) {
      try {
        await MySQLDatabaseService.addMaintenanceRecord(maintenance)
        insertedMaintenance++
      } catch (error) {
        console.warn('Error inserting maintenance:', error)
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Database populated successfully',
      data: {
        transformers: insertedTransformers,
        alerts: insertedAlerts,
        maintenance: insertedMaintenance,
        total: insertedTransformers + insertedAlerts + insertedMaintenance
      }
    })

  } catch (error) {
    console.error('Error populating database:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to populate database',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    success: true,
    message: 'Use POST method to populate database with sample data',
    endpoints: {
      populate: 'POST /api/populate-db',
      dashboard: 'GET /api/mysql/dashboard',
      transformers: 'GET /api/mysql/transformers'
    }
  })
}
