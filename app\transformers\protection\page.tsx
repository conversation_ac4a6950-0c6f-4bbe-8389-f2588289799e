"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { But<PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Shield,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Activity,
  TrendingUp,
  TrendingDown,
  Clock,
  MapPin,
  Settings,
  Eye,
  Edit,
  RefreshCw,
  Download,
  Upload,
  Search,
  Filter,
  Bell,
  Target,
  BarChart3,
  PieChart,
  Thermometer,
  Gauge,
  Radio,
  Wifi,
  Power,
  Battery,
  Cpu,
  HardDrive,
  Monitor,
  Layers,
  Lock,
  Unlock,
  Key,
  ShieldCheck,
  ShieldAlert,
  ShieldX
} from 'lucide-react'
import {
  <PERSON><PERSON>hart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts'
import { TransformerLayout } from '@/components/transformer/TransformerLayout'
import { ProtectedRoute } from "@/src/components/layout/protected-route"
import ProvidersWrapper from "@/components/providers-wrapper"

// Mock protection system data
const mockProtectionSystems = [
  {
    id: 'PROT-AA-001',
    transformerId: 'T-AA-001',
    transformerName: 'Lideta Primary Transformer',
    location: 'Lideta Substation, Addis Ababa',
    protectionScheme: 'Differential + Distance',
    status: 'active',
    healthScore: 98.5,
    lastTested: '2024-02-01',
    nextTest: '2024-08-01',
    protectionDevices: [
      { type: 'Differential Relay', model: 'SEL-387E', status: 'operational', lastMaintenance: '2024-01-15' },
      { type: 'Distance Relay', model: 'SEL-421', status: 'operational', lastMaintenance: '2024-01-15' },
      { type: 'Overcurrent Relay', model: 'SEL-751A', status: 'operational', lastMaintenance: '2024-01-10' },
      { type: 'Buchholz Relay', model: 'MR-BG1', status: 'operational', lastMaintenance: '2024-01-20' }
    ],
    recentEvents: [
      { timestamp: '2024-02-10T14:30:00Z', type: 'test', description: 'Routine protection test completed successfully' },
      { timestamp: '2024-02-05T09:15:00Z', type: 'alarm', description: 'Minor overcurrent detected - cleared automatically' }
    ],
    settings: {
      differentialPickup: 0.3,
      distanceZone1: 80,
      distanceZone2: 120,
      overcurrentPickup: 1.2,
      timeDelay: 0.1
    }
  },
  {
    id: 'PROT-OR-045',
    transformerId: 'T-OR-045',
    transformerName: 'Sebeta Distribution Transformer',
    location: 'Sebeta Substation, Oromia',
    protectionScheme: 'Overcurrent + Earth Fault',
    status: 'warning',
    healthScore: 85.2,
    lastTested: '2024-01-15',
    nextTest: '2024-07-15',
    protectionDevices: [
      { type: 'Overcurrent Relay', model: 'SEL-751A', status: 'operational', lastMaintenance: '2024-01-10' },
      { type: 'Earth Fault Relay', model: 'SEL-751A', status: 'warning', lastMaintenance: '2023-12-15' },
      { type: 'Voltage Relay', model: 'SEL-710', status: 'operational', lastMaintenance: '2024-01-05' },
      { type: 'Frequency Relay', model: 'SEL-710', status: 'operational', lastMaintenance: '2024-01-05' }
    ],
    recentEvents: [
      { timestamp: '2024-02-12T11:45:00Z', type: 'fault', description: 'Earth fault detected - protection operated correctly' },
      { timestamp: '2024-02-08T16:20:00Z', type: 'alarm', description: 'Earth fault relay requires calibration' }
    ],
    settings: {
      overcurrentPickup: 1.5,
      earthFaultPickup: 0.2,
      voltagePickup: 0.9,
      frequencyPickup: 49.5,
      timeDelay: 0.2
    }
  },
  {
    id: 'PROT-AM-023',
    transformerId: 'T-AM-023',
    transformerName: 'Bahir Dar Distribution Transformer',
    location: 'Bahir Dar Substation, Amhara',
    protectionScheme: 'Numerical Protection',
    status: 'fault',
    healthScore: 65.8,
    lastTested: '2023-12-10',
    nextTest: '2024-06-10',
    protectionDevices: [
      { type: 'Numerical Relay', model: 'SEL-487E', status: 'fault', lastMaintenance: '2023-11-20' },
      { type: 'Communication Module', model: 'SEL-2032', status: 'offline', lastMaintenance: '2023-11-20' },
      { type: 'Monitoring Unit', model: 'SEL-3530', status: 'operational', lastMaintenance: '2024-01-05' }
    ],
    recentEvents: [
      { timestamp: '2024-02-11T08:30:00Z', type: 'fault', description: 'Communication failure - manual intervention required' },
      { timestamp: '2024-02-09T14:15:00Z', type: 'alarm', description: 'Numerical relay self-test failed' }
    ],
    settings: {
      differentialPickup: 0.25,
      restrictedEarthFault: 0.1,
      overfluxPickup: 1.1,
      thermalOverload: 1.25,
      timeDelay: 0.05
    }
  }
]

const protectionStats = {
  totalSystems: 1247,
  operational: 1156,
  warning: 67,
  fault: 24,
  avgHealthScore: 92.3,
  testCompliance: 94.8,
  responseTime: 0.08,
  reliability: 99.2
}

const protectionTypes = [
  { name: 'Differential', count: 456, reliability: 99.8 },
  { name: 'Distance', count: 387, reliability: 99.5 },
  { name: 'Overcurrent', count: 298, reliability: 98.9 },
  { name: 'Earth Fault', count: 234, reliability: 99.1 },
  { name: 'Numerical', count: 156, reliability: 99.6 }
]

const statusColors = {
  active: 'bg-green-100 text-green-800 border-green-200',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  fault: 'bg-red-100 text-red-800 border-red-200',
  offline: 'bg-gray-100 text-gray-800 border-gray-200'
}

const deviceStatusColors = {
  operational: 'bg-green-100 text-green-800 border-green-200',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  fault: 'bg-red-100 text-red-800 border-red-200',
  offline: 'bg-gray-100 text-gray-800 border-gray-200'
}

const eventTypeColors = {
  test: 'bg-blue-100 text-blue-800 border-blue-200',
  alarm: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  fault: 'bg-red-100 text-red-800 border-red-200',
  maintenance: 'bg-purple-100 text-purple-800 border-purple-200'
}

export default function TransformerProtectionPage() {
  const [protectionData, setProtectionData] = useState(mockProtectionSystems)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedScheme, setSelectedScheme] = useState('all')

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  const filteredData = protectionData.filter(system => {
    const matchesSearch = system.transformerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         system.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         system.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || system.status === selectedStatus
    const matchesScheme = selectedScheme === 'all' || system.protectionScheme.toLowerCase().includes(selectedScheme.toLowerCase())

    return matchesSearch && matchesStatus && matchesScheme
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading protection systems...</p>
        </div>
      </div>
    )
  }

  return (
    <ProvidersWrapper>
      <ProtectedRoute
        allowedRoles={[
          "super_admin",
          "national_protection_engineer",
          "regional_admin",
          "regional_protection_engineer",
          "service_center_manager",
          "protection_technician"
        ]}
      >
        <TransformerLayout>
          <div className="space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Protection Systems</h1>
                <p className="text-muted-foreground">
                  Advanced protection and safety systems for Ethiopian Electric Utility transformers
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </Button>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </Button>
                <Button>
                  <ShieldCheck className="h-4 w-4 mr-2" />
                  Test Protection
                </Button>
              </div>
            </div>

            {/* Protection Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Systems</CardTitle>
                  <Shield className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{protectionStats.totalSystems}</div>
                  <p className="text-xs text-muted-foreground">
                    {protectionStats.operational} operational
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">System Health</CardTitle>
                  <Activity className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{protectionStats.avgHealthScore}%</div>
                  <p className="text-xs text-muted-foreground">
                    Average health score
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Response Time</CardTitle>
                  <Zap className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">{protectionStats.responseTime}s</div>
                  <p className="text-xs text-muted-foreground">
                    Average response time
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Reliability</CardTitle>
                  <Target className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">{protectionStats.reliability}%</div>
                  <p className="text-xs text-muted-foreground">
                    System reliability
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Main Protection Interface */}
            <Tabs defaultValue="systems" className="space-y-4">
              <TabsList>
                <TabsTrigger value="systems">Protection Systems</TabsTrigger>
                <TabsTrigger value="monitoring">Real-time Monitoring</TabsTrigger>
                <TabsTrigger value="testing">Testing & Maintenance</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
              </TabsList>

              <TabsContent value="systems" className="space-y-4">
                {/* Filters */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-4">
                      <div className="flex-1">
                        <Input
                          placeholder="Search protection systems..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="max-w-sm"
                        />
                      </div>
                      <select
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Status</option>
                        <option value="active">Active</option>
                        <option value="warning">Warning</option>
                        <option value="fault">Fault</option>
                        <option value="offline">Offline</option>
                      </select>
                      <select
                        value={selectedScheme}
                        onChange={(e) => setSelectedScheme(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Schemes</option>
                        <option value="differential">Differential</option>
                        <option value="distance">Distance</option>
                        <option value="overcurrent">Overcurrent</option>
                        <option value="numerical">Numerical</option>
                      </select>
                    </div>
                  </CardContent>
                </Card>

                {/* Protection Systems List */}
                <div className="space-y-4">
                  {filteredData.map((system) => (
                    <Card key={system.id} className="hover:shadow-lg transition-shadow">
                      <CardContent className="pt-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold text-lg">{system.transformerName}</h3>
                              <Badge className={statusColors[system.status as keyof typeof statusColors]}>
                                {system.status}
                              </Badge>
                              <Badge variant="outline">
                                {system.protectionScheme}
                              </Badge>
                            </div>

                            <div className="flex items-center gap-2 mb-3">
                              <span className="text-sm font-medium text-blue-600">{system.id}</span>
                              <span className="text-sm text-muted-foreground">•</span>
                              <span className="text-sm text-muted-foreground">{system.location}</span>
                            </div>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                              <div className="flex items-center gap-1">
                                <Activity className="h-3 w-3" />
                                <span>Health: {system.healthScore}%</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>Last Test: {new Date(system.lastTested).toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Bell className="h-3 w-3" />
                                <span>Next Test: {new Date(system.nextTest).toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Shield className="h-3 w-3" />
                                <span>Devices: {system.protectionDevices.length}</span>
                              </div>
                            </div>

                            {/* Health Score Bar */}
                            <div className="mb-4">
                              <div className="flex justify-between text-xs mb-1">
                                <span>System Health</span>
                                <span>{system.healthScore}%</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${
                                    system.healthScore > 90 ? 'bg-green-500' :
                                    system.healthScore > 75 ? 'bg-yellow-500' :
                                    'bg-red-500'
                                  }`}
                                  style={{ width: `${system.healthScore}%` }}
                                ></div>
                              </div>
                            </div>

                            {/* Protection Devices */}
                            <div className="mb-4">
                              <Label className="text-sm font-medium">Protection Devices:</Label>
                              <div className="grid grid-cols-2 gap-2 mt-2">
                                {system.protectionDevices.map((device, index) => (
                                  <div key={index} className="flex items-center justify-between p-2 border rounded">
                                    <div>
                                      <div className="text-sm font-medium">{device.type}</div>
                                      <div className="text-xs text-muted-foreground">{device.model}</div>
                                    </div>
                                    <Badge className={deviceStatusColors[device.status as keyof typeof deviceStatusColors]} variant="outline">
                                      {device.status}
                                    </Badge>
                                  </div>
                                ))}
                              </div>
                            </div>

                            {/* Recent Events */}
                            {system.recentEvents.length > 0 && (
                              <div>
                                <Label className="text-sm font-medium">Recent Events:</Label>
                                <div className="space-y-1 mt-2">
                                  {system.recentEvents.slice(0, 2).map((event, index) => (
                                    <div key={index} className="flex items-start gap-2 text-sm">
                                      <Badge className={eventTypeColors[event.type as keyof typeof eventTypeColors]} variant="outline">
                                        {event.type}
                                      </Badge>
                                      <div className="flex-1">
                                        <div>{event.description}</div>
                                        <div className="text-xs text-muted-foreground">
                                          {new Date(event.timestamp).toLocaleString()}
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center gap-2 ml-4">
                            <Button size="sm" variant="outline">
                              <Eye className="h-3 w-3 mr-1" />
                              Monitor
                            </Button>
                            <Button size="sm" variant="outline">
                              <Settings className="h-3 w-3 mr-1" />
                              Configure
                            </Button>
                            <Button size="sm">
                              <ShieldCheck className="h-3 w-3 mr-1" />
                              Test
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="monitoring" className="space-y-4">
                {/* Real-time Protection Monitoring */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Protection System Status</CardTitle>
                      <CardDescription>Real-time status of all protection systems</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsPieChart>
                          <Pie
                            data={[
                              { name: 'Operational', value: protectionStats.operational, fill: '#10b981' },
                              { name: 'Warning', value: protectionStats.warning, fill: '#f59e0b' },
                              { name: 'Fault', value: protectionStats.fault, fill: '#ef4444' }
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            dataKey="value"
                          />
                          <Tooltip />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Protection Type Distribution</CardTitle>
                      <CardDescription>Distribution by protection scheme type</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={protectionTypes}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="count" fill="#3b82f6" name="Count" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Live Protection Events */}
                <Card>
                  <CardHeader>
                    <CardTitle>Live Protection Events</CardTitle>
                    <CardDescription>Real-time protection system events and alarms</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {[
                        { time: '14:32:15', system: 'PROT-AA-001', event: 'Routine test completed successfully', severity: 'info' },
                        { time: '14:28:42', system: 'PROT-OR-045', event: 'Earth fault relay calibration required', severity: 'warning' },
                        { time: '14:25:18', system: 'PROT-AM-023', event: 'Communication module offline', severity: 'critical' },
                        { time: '14:22:05', system: 'PROT-TI-012', event: 'Overcurrent protection operated', severity: 'alarm' },
                        { time: '14:18:33', system: 'PROT-SN-008', event: 'Distance relay zone 2 pickup', severity: 'warning' }
                      ].map((event, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${
                              event.severity === 'critical' ? 'bg-red-500' :
                              event.severity === 'alarm' ? 'bg-orange-500' :
                              event.severity === 'warning' ? 'bg-yellow-500' :
                              'bg-blue-500'
                            }`}></div>
                            <div>
                              <div className="font-medium">{event.system}</div>
                              <div className="text-sm text-muted-foreground">{event.event}</div>
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground">{event.time}</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </TransformerLayout>
      </ProtectedRoute>
    </ProvidersWrapper>
  )
}
