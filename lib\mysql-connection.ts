import mysql from 'mysql2/promise'

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db', // <-- fixed database name
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  charset: 'utf8mb4'
}

// Create connection pool
let pool: mysql.Pool | null = null

function createPool() {
  if (!pool) {
    pool = mysql.createPool(dbConfig)
  }
  return pool
}

// Execute query function
export async function executeQuery(query: string, params: any[] = []): Promise<any> {
  try {
    const connection = createPool()
    const [results] = await connection.execute(query, params)
    return results
  } catch (error) {
    console.error('Database query error:', error)

    // Check if this is a dashboard analytics query
    const queryLower = query.toLowerCase()
    if (queryLower.includes('dashboard') || queryLower.includes('analytics')) {
      return await getDashboardAnalytics()
    }

    // Return mock data if database connection fails
    return getMockData(query)
  }
}

// Get connection for transactions
export async function getConnection() {
  try {
    const connection = createPool()
    return await connection.getConnection()
  } catch (error) {
    console.error('Database connection error:', error)
    throw error
  }
}

// Close all connections
export async function closeConnections() {
  if (pool) {
    await pool.end()
    pool = null
  }
}

// Database queries for real data
export async function getDashboardAnalytics(): Promise<any> {
  try {
    const connection = createPool();

    // Get transformer overview statistics
    // Updated query to remove non-existent column `load_percentage` and fetch valid data
    const [overviewResults]: [any, any] = await connection.execute(`
      SELECT
        COUNT(*) as totalTransformers,
        SUM(CASE WHEN status = 'operational' THEN 1 ELSE 0 END) as operationalTransformers,
        SUM(CASE WHEN status = 'burnt' THEN 1 ELSE 0 END) as burntTransformers,
        SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as underMaintenance,
        SUM(CASE WHEN status = 'critical' THEN 1 ELSE 0 END) as criticalTransformers
      FROM app_transformers
    `);

    const overview = overviewResults[0];

    // Get maintenance statistics
    // First try app_maintenance_schedules, then fall back to maintenance_records
    let maintenanceResults: any;
    try {
      const [results]: [any, any] = await connection.execute(`
        SELECT
          COUNT(*) as totalSchedules,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedTasks,
          SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as inProgressTasks,
          SUM(CASE WHEN status = 'pending' OR status = 'scheduled' THEN 1 ELSE 0 END) as pendingTasks,
          SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdueTasks,
          AVG(CASE WHEN estimated_duration IS NOT NULL THEN estimated_duration ELSE 0 END) as averageCompletionDays
        FROM app_maintenance_schedules
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
      `);
      maintenanceResults = results;
    } catch (error) {
      // Fallback to maintenance_records table if app_maintenance_schedules doesn't exist
      try {
        const [results]: [any, any] = await connection.execute(`
          SELECT
            COUNT(*) as totalSchedules,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedTasks,
            SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as inProgressTasks,
            SUM(CASE WHEN status = 'pending' OR status = 'scheduled' THEN 1 ELSE 0 END) as pendingTasks,
            SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdueTasks,
            AVG(CASE WHEN estimated_duration IS NOT NULL THEN estimated_duration ELSE 0 END) as averageCompletionDays
          FROM maintenance_records
          WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        `);
        maintenanceResults = results;
      } catch (fallbackError) {
        // If both tables fail, return mock data
        maintenanceResults = [{
          totalSchedules: 0,
          completedTasks: 0,
          inProgressTasks: 0,
          pendingTasks: 0,
          overdueTasks: 0,
          averageCompletionDays: 0
        }];
      }
    }

    const maintenance = maintenanceResults[0];

    // Get alerts statistics
    // Try app_alerts first, then fall back to alerts
    let alertsResults: any;
    try {
      const [results]: [any, any] = await connection.execute(`
        SELECT
          COUNT(*) as totalAlerts,
          SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as criticalAlerts,
          SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as highAlerts,
          SUM(CASE WHEN severity = 'medium' THEN 1 ELSE 0 END) as mediumAlerts,
          SUM(CASE WHEN severity = 'low' THEN 1 ELSE 0 END) as lowAlerts
        FROM app_alerts
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
      `);
      alertsResults = results;
    } catch (error) {
      // Fallback to alerts table
      try {
        const [results]: [any, any] = await connection.execute(`
          SELECT
            COUNT(*) as totalAlerts,
            SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as criticalAlerts,
            SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as highAlerts,
            SUM(CASE WHEN severity = 'medium' THEN 1 ELSE 0 END) as mediumAlerts,
            SUM(CASE WHEN severity = 'low' THEN 1 ELSE 0 END) as lowAlerts
          FROM alerts
          WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        `);
        alertsResults = results;
      } catch (fallbackError) {
        // If both tables fail, return mock data
        alertsResults = [{
          totalAlerts: 0,
          criticalAlerts: 0,
          highAlerts: 0,
          mediumAlerts: 0,
          lowAlerts: 0
        }];
      }
    }

    const alerts = alertsResults[0];

    return {
      overview,
      maintenance,
      alerts
    };
  } catch (error) {
    console.error('Error fetching dashboard analytics:', error);
    throw error;
  }
}

// Mock data fallback when database is not available
function getMockData(query: string): any {
  const queryLower = query.toLowerCase()

  // Dashboard analytics mock data for distribution transformers
  if (queryLower.includes('dashboard') || queryLower.includes('analytics')) {
    return {
      overview: {
        totalTransformers: 1247,
        operationalTransformers: 1189,
        averageLoad: 78.5,
        goodCondition: 87.3,
        serviceAvailability: 98.7,
        efficiencyTrend: 'stable',
        burnedTransformers: 23,
        underMaintenance: 35,
        newInstallations: 12,
        replacements: 8
      },
      maintenance: {
        totalSchedules: 156,
        completedTasks: 142,
        inProgressTasks: 8,
        pendingTasks: 14,
        overdueTasks: 3,
        efficiency: 91.0,
        averageCompletionDays: 2.3,
        nextScheduledCount: 28
      },
      alerts: {
        totalAlerts: 23,
        activeAlerts: 8,
        criticalAlerts: 2,
        highAlerts: 3,
        mediumAlerts: 2,
        lowAlerts: 1,
        resolvedAlerts: 15,
        resolutionRate: 87.5,
        averageResolutionTime: 4.2
      },
      performance: {
        averageUptime: 98.7,
        averageEfficiency: 94.2,
        averageLoadFactor: 76.8,
        monitoredTransformers: 1189
      },
      regional: [
        {
          name: 'Addis Ababa',
          code: 'AA',
          transformerCount: 287,
          averageLoad: 82.1,
          operationalCount: 275,
          operationalPercentage: 95.8
        },
        {
          name: 'Oromia',
          code: 'OR',
          transformerCount: 342,
          averageLoad: 76.3,
          operationalCount: 328,
          operationalPercentage: 95.9
        },
        {
          name: 'Amhara',
          code: 'AM',
          transformerCount: 298,
          averageLoad: 74.8,
          operationalCount: 285,
          operationalPercentage: 95.6
        },
        {
          name: 'Tigray',
          code: 'TI',
          transformerCount: 156,
          averageLoad: 71.2,
          operationalCount: 148,
          operationalPercentage: 94.9
        },
        {
          name: 'SNNPR',
          code: 'SN',
          transformerCount: 164,
          averageLoad: 73.5,
          operationalCount: 153,
          operationalPercentage: 93.3
        }
      ],
      trends: {
        daily: [
          { date: '2024-01-01', alerts: 5, criticalPercentage: 20 },
          { date: '2024-01-02', alerts: 3, criticalPercentage: 33 },
          { date: '2024-01-03', alerts: 7, criticalPercentage: 14 },
          { date: '2024-01-04', alerts: 4, criticalPercentage: 25 },
          { date: '2024-01-05', alerts: 6, criticalPercentage: 17 },
          { date: '2024-01-06', alerts: 2, criticalPercentage: 50 },
          { date: '2024-01-07', alerts: 8, criticalPercentage: 12 }
        ]
      },
      summary: {
        systemHealth: 95.4,
        maintenanceBacklog: 14,
        criticalIssues: 2,
        overallEfficiency: 87.3,
        assetCondition: 'Good',
        serviceReliability: 98.7
      },
      charts: {
        transformerStatus: [
          { name: 'Operational', value: 1189, color: '#22c55e' },
          { name: 'Burned', value: 23, color: '#ef4444' },
          { name: 'Under Maintenance', value: 35, color: '#f97316' },
          { name: 'Offline', value: 0, color: '#6b7280' }
        ],
        monthlyBurns: [
          { month: 'Jan', burns: 3, maintenance: 12, replacements: 2 },
          { month: 'Feb', burns: 5, maintenance: 15, replacements: 3 },
          { month: 'Mar', burns: 2, maintenance: 18, replacements: 1 },
          { month: 'Apr', burns: 7, maintenance: 14, replacements: 4 },
          { month: 'May', burns: 4, maintenance: 16, replacements: 3 },
          { month: 'Jun', burns: 6, maintenance: 13, replacements: 5 }
        ],
        maintenanceTypes: [
          { type: 'Preventive', count: 45, percentage: 37.5 },
          { type: 'Corrective', count: 28, percentage: 23.3 },
          { type: 'Emergency', count: 12, percentage: 10.0 },
          { type: 'Inspection', count: 35, percentage: 29.2 }
        ],
        regionalPerformance: [
          { region: 'Addis Ababa', operational: 145, burned: 3, maintenance: 7 },
          { region: 'Oromia', operational: 298, burned: 8, maintenance: 12 },
          { region: 'Amhara', operational: 234, burned: 5, maintenance: 9 },
          { region: 'Tigray', operational: 156, burned: 2, maintenance: 4 },
          { region: 'SNNPR', operational: 187, burned: 3, maintenance: 6 }
        ]
      }
    }
  }
}

// Test database connection
export async function testConnection(): Promise<boolean> {
  try {
    const connection = createPool()
    await connection.execute('SELECT 1')
    return true
  } catch (error) {
    console.error('Database connection test failed:', error)
    return false
  }
}

// Initialize database tables (for development)
export async function initializeDatabase(): Promise<void> {
  try {
    const connection = createPool()

    // Create transformers table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS transformers (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        location VARCHAR(255) NOT NULL,
        region VARCHAR(100) NOT NULL,
        status ENUM('operational', 'maintenance', 'offline', 'burned', 'new_installation', 'replacement') DEFAULT 'operational',
        voltage VARCHAR(50),
        capacity VARCHAR(50),
        load_percentage DECIMAL(5,2) DEFAULT 0,
        condition_score DECIMAL(5,2) DEFAULT 0,
        efficiency DECIMAL(5,2) DEFAULT 0,
        temperature DECIMAL(5,2) DEFAULT 0,
        last_maintenance DATE,
        next_maintenance DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    // Create maintenance table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS maintenance (
        id VARCHAR(50) PRIMARY KEY,
        transformer_id VARCHAR(50),
        type ENUM('preventive', 'corrective', 'emergency') NOT NULL,
        status ENUM('scheduled', 'in_progress', 'completed', 'cancelled') DEFAULT 'scheduled',
        scheduled_date DATE,
        completed_date DATE,
        description TEXT,
        technician VARCHAR(255),
        priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES transformers(id)
      )
    `)

    // Create users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(20),
        role ENUM('super_admin', 'national_asset_manager', 'regional_admin', 'field_technician') NOT NULL,
        department VARCHAR(255),
        region_name VARCHAR(255),
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    console.log('Database tables initialized successfully')
  } catch (error) {
    console.error('Database initialization error:', error)
  }
}

export default {
  executeQuery,
  getConnection,
  closeConnections,
  testConnection,
  initializeDatabase
}
