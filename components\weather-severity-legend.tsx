import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"

interface SeverityLevel {
  level: "critical" | "high" | "moderate" | "low"
  label: string
  color: string
  description: string
  action: string
}

export function WeatherSeverityLegend() {
  const severityLevels: SeverityLevel[] = [
    {
      level: "critical",
      label: "Critical Risk",
      color: "bg-red-600",
      description: "Severe weather conditions that pose immediate danger to transformer infrastructure",
      action: "Immediate action required. Deploy emergency response teams.",
    },
    {
      level: "high",
      label: "High Risk",
      color: "bg-red-500",
      description: "Significant weather conditions that may cause damage to transformers",
      action: "Urgent attention needed. Prepare response teams and monitor closely.",
    },
    {
      level: "moderate",
      label: "Moderate Risk",
      color: "bg-yellow-500",
      description: "Weather conditions that may affect transformer performance",
      action: "Regular monitoring required. Prepare for potential escalation.",
    },
    {
      level: "low",
      label: "Low Risk",
      color: "bg-green-500",
      description: "Minimal weather impact on transformer infrastructure",
      action: "Standard monitoring procedures. No immediate action required.",
    },
  ]

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Weather Severity Levels</CardTitle>
        <CardDescription>Understanding weather impact indicators</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {severityLevels.map((level) => (
            <div key={level.level} className="flex items-start gap-3 pb-3 border-b last:border-0 last:pb-0">
              <div className={`mt-1 h-4 w-4 flex-shrink-0 rounded-full ${level.color}`}></div>
              <div>
                <h4 className="font-medium">{level.label}</h4>
                <p className="text-sm text-muted-foreground">{level.description}</p>
                <p className="text-sm font-medium mt-1">{level.action}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
