import { NextRequest, NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

// Database configuration
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: 'dtms_eeu_db',
  multipleStatements: true
}

export async function GET(request: NextRequest) {
  let connection: mysql.Connection | null = null
  
  try {
    console.log('🔍 Checking bypass tables status...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Check if bypass tables exist
    const [tables] = await connection.query('SHOW TABLES')
    const tableList = (tables as any[]).map(row => Object.values(row)[0])
    
    // Find tables with timestamp suffixes
    const bypassTables = tableList.filter(name => 
      name.includes('_v') && /\d{13}/.test(name)
    )
    
    // Check if views exist
    const [views] = await connection.query('SHOW FULL TABLES WHERE Table_type = "VIEW"')
    const viewList = (views as any[]).map(row => Object.values(row)[0])
    
    const standardViews = ['app_regions', 'app_service_centers', 'app_users', 'app_transformers', 'app_alerts']
    const existingViews = standardViews.filter(view => viewList.includes(view))
    
    // Check if data exists in bypass tables
    let dataExists = false
    if (bypassTables.length > 0) {
      const regionsTable = bypassTables.find(name => name.startsWith('regions_v'))
      if (regionsTable) {
        const [result] = await connection.query(`SELECT COUNT(*) as count FROM ${regionsTable}`)
        dataExists = (result as any)[0].count > 0
      }
    }

    const bypassSuccess = bypassTables.length > 0 && existingViews.length >= 4
    
    console.log(`📋 Found ${bypassTables.length} bypass tables`)
    console.log(`📋 Found ${existingViews.length} standard views`)
    console.log(`📊 Data exists: ${dataExists}`)

    return NextResponse.json({
      success: true,
      bypassSuccess,
      dataExists,
      bypassTables: bypassTables.length,
      standardViews: existingViews.length,
      tableList: bypassTables,
      viewList: existingViews,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error checking bypass tables:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to check bypass tables',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}
