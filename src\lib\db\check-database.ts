import mysql from 'mysql2/promise'

// Database configuration
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: 'dtms_eeu_db',
  multipleStatements: true
}

export async function checkDatabaseStatus() {
  let connection: mysql.Connection | null = null
  
  try {
    console.log('🔍 Checking database status...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to dtms_eeu_db database')
    
    // Check if database exists and get table list
    const [tables] = await connection.query('SHOW TABLES')
    console.log('📋 Tables found:', tables)
    
    const tableList = (tables as any[]).map(row => Object.values(row)[0])
    
    // Check each expected table
    const expectedTables = [
      'app_regions',
      'app_service_centers', 
      'app_users',
      'app_transformers',
      'app_alerts',
      'app_maintenance_schedules',
      'app_notifications',
      'app_performance_metrics',
      'app_weather_data'
    ]
    
    const tableStatus = {}
    
    for (const tableName of expectedTables) {
      if (tableList.includes(tableName)) {
        // Get row count
        const [countResult] = await connection.query(`SELECT COUNT(*) as count FROM ${tableName}`)
        const count = (countResult as any)[0].count
        tableStatus[tableName] = {
          exists: true,
          rowCount: count
        }
        console.log(`✅ ${tableName}: ${count} rows`)
      } else {
        tableStatus[tableName] = {
          exists: false,
          rowCount: 0
        }
        console.log(`❌ ${tableName}: missing`)
      }
    }
    
    // Check if we have any data
    let hasData = false
    for (const table of Object.values(tableStatus)) {
      if ((table as any).exists && (table as any).rowCount > 0) {
        hasData = true
        break
      }
    }
    
    return {
      success: true,
      databaseExists: true,
      tables: tableStatus,
      hasData,
      message: hasData ? 'Database has data' : 'Database exists but is empty'
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error)
    
    if ((error as any).code === 'ER_BAD_DB_ERROR') {
      return {
        success: false,
        databaseExists: false,
        tables: {},
        hasData: false,
        message: 'Database dtms_eeu_db does not exist',
        error: error
      }
    }
    
    return {
      success: false,
      databaseExists: false,
      tables: {},
      hasData: false,
      message: `Database check failed: ${error}`,
      error
    }
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}

export async function getTableStructure(tableName: string) {
  let connection: mysql.Connection | null = null
  
  try {
    connection = await mysql.createConnection(dbConfig)
    
    // Get table structure
    const [columns] = await connection.query(`DESCRIBE ${tableName}`)
    
    return {
      success: true,
      tableName,
      columns
    }
    
  } catch (error) {
    console.error(`❌ Failed to get structure for ${tableName}:`, error)
    return {
      success: false,
      tableName,
      error: error
    }
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}
