"use client"

import React, { useState, useEffect } from 'react'
import { MaintenanceDashboard } from '@/components/maintenance/maintenance-dashboard'
import { MaintenanceTableEnhanced } from '@/components/maintenance/maintenance-table-enhanced'
import { ScheduleMaintenanceDialogEnhanced } from '@/components/maintenance/schedule-maintenance-dialog-enhanced'
import { ScheduledTasks } from '@/components/maintenance/scheduled-tasks'
import { WorkOrders } from '@/components/maintenance/work-orders'
import { PreventiveMaintenance } from '@/components/maintenance/preventive-maintenance'
import { InspectionManagement } from '@/components/maintenance/inspection-management'
import { PartsInventory } from '@/components/maintenance/parts-inventory'
import { TechnicianManagement } from '@/components/maintenance/technician-management'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Progress } from "@/src/components/ui/progress"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/src/components/ui/tabs"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import {
  Wrench,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  User,
  MapPin,
  Zap,
  FileText,
  TrendingUp,
  BarChart3,
  PieChart,
  Activity,
  ClipboardList,
  Shield,
  Package,
  Users
} from 'lucide-react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { MainLayout } from "@/src/components/layout/main-layout"

// Mock data
const mockMaintenanceData = {
  summary: {
    total: 156,
    scheduled: 42,
    inProgress: 8,
    completed: 98,
    overdue: 8,
    avgCost: 2850,
    avgDuration: 4.2
  },
  recentActivities: [
    {
      id: 1,
      transformer: 'T-AA-001',
      type: 'Preventive',
      status: 'completed',
      technician: 'John Doe',
      date: '2024-02-10',
      duration: 3.5,
      cost: 1200,
      location: 'Addis Ababa'
    },
    {
      id: 2,
      transformer: 'T-OR-045',
      type: 'Corrective',
      status: 'in_progress',
      technician: 'Jane Smith',
      date: '2024-02-12',
      duration: null,
      cost: null,
      location: 'Oromia'
    },
    {
      id: 3,
      transformer: 'T-AM-023',
      type: 'Emergency',
      status: 'scheduled',
      technician: 'Bob Johnson',
      date: '2024-02-15',
      duration: null,
      cost: null,
      location: 'Amhara'
    }
  ],
  upcomingSchedule: [
    {
      id: 1,
      transformer: 'T-TG-012',
      type: 'Inspection',
      scheduledDate: '2024-02-16',
      technician: 'Alice Brown',
      priority: 'Medium',
      estimatedDuration: 2,
      location: 'Tigray'
    },
    {
      id: 2,
      transformer: 'T-SN-008',
      type: 'Preventive',
      scheduledDate: '2024-02-18',
      technician: 'Charlie Wilson',
      priority: 'High',
      estimatedDuration: 4,
      location: 'SNNPR'
    }
  ],
  performanceData: Array.from({ length: 12 }, (_, i) => ({
    month: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][i],
    completed: 8 + Math.floor(Math.random() * 12),
    scheduled: 5 + Math.floor(Math.random() * 8),
    cost: 15000 + Math.floor(Math.random() * 10000)
  })),
  typeDistribution: [
    { name: 'Preventive', value: 45, color: '#10b981' },
    { name: 'Corrective', value: 30, color: '#f59e0b' },
    { name: 'Emergency', value: 15, color: '#ef4444' },
    { name: 'Inspection', value: 10, color: '#3b82f6' }
  ]
}

const statusColors = {
  scheduled: 'bg-blue-100 text-blue-800 border-blue-200',
  in_progress: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  completed: 'bg-green-100 text-green-800 border-green-200',
  overdue: 'bg-red-100 text-red-800 border-red-200'
}

const priorityColors = {
  Low: 'bg-gray-100 text-gray-800 border-gray-200',
  Medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  High: 'bg-orange-100 text-orange-800 border-orange-200',
  Critical: 'bg-red-100 text-red-800 border-red-200'
}

export default function MaintenancePage() {
  const [maintenanceData, setMaintenanceData] = useState(mockMaintenanceData)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedType, setSelectedType] = useState('all')
  const [showScheduleDialog, setShowScheduleDialog] = useState(false)
  const [activeTab, setActiveTab] = useState('dashboard')

  // Handle URL tab parameter for sidebar navigation
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const tabParam = urlParams.get('tab')
    if (tabParam) {
      setActiveTab(tabParam)
    }
  }, [])

  // Load maintenance data from database
  useEffect(() => {
    const loadMaintenanceData = async () => {
      try {
        console.log('🔧 Loading maintenance data from MySQL...')
        const response = await fetch('/api/mysql/maintenance')

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            const maintenanceSchedules = data.data || []

            // Transform database data to component format
            const transformedData = {
              summary: {
                total: maintenanceSchedules.length,
                scheduled: maintenanceSchedules.filter((m: any) => m.status === 'scheduled').length,
                inProgress: maintenanceSchedules.filter((m: any) => m.status === 'in_progress').length,
                completed: maintenanceSchedules.filter((m: any) => m.status === 'completed').length,
                overdue: maintenanceSchedules.filter((m: any) => m.status === 'overdue').length,
                avgCost: 2850, // Would calculate from actual cost data
                avgDuration: 4.2 // Would calculate from actual duration data
              },
              recentActivities: maintenanceSchedules.slice(0, 5).map((m: any) => ({
                id: m.id,
                transformer: m.transformerSerial || `T-${m.id}`,
                type: m.type,
                status: m.status,
                technician: m.technicianName || 'Unassigned',
                date: new Date(m.scheduledDate).toLocaleDateString(),
                duration: m.estimatedDuration,
                cost: null, // Would come from cost tracking
                location: m.regionName || 'Unknown'
              })),
              upcomingSchedule: maintenanceSchedules
                .filter((m: any) => m.status === 'scheduled')
                .slice(0, 5)
                .map((m: any) => ({
                  id: m.id,
                  transformer: m.transformerSerial || `T-${m.id}`,
                  type: m.type,
                  scheduledDate: new Date(m.scheduledDate).toLocaleDateString(),
                  technician: m.technicianName || 'Unassigned',
                  priority: m.priority || 'Medium',
                  estimatedDuration: m.estimatedDuration || 4,
                  location: m.regionName || 'Unknown'
                })),
              performanceData: mockMaintenanceData.performanceData, // Keep mock for now
              typeDistribution: mockMaintenanceData.typeDistribution // Keep mock for now
            }

            setMaintenanceData(transformedData)
            console.log('✅ Maintenance data loaded successfully')
          } else {
            throw new Error(data.error || 'Failed to load maintenance data')
          }
        } else {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
      } catch (error) {
        console.error('❌ Error loading maintenance data:', error)
        // Keep mock data as fallback
      } finally {
        setLoading(false)
      }
    }

    loadMaintenanceData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading maintenance data...</p>
        </div>
      </div>
    )
  }

  const handleScheduleSuccess = () => {
    // Refresh data or show success message
    console.log('Maintenance scheduled successfully')
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_maintenance_manager",
        "regional_admin",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician"
      ]}
      requiredPermissions={[{ resource: "maintenance", action: "read" }]}
    >
      <div className="space-y-6">
        {/* Enhanced Maintenance Management */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                Maintenance Management
              </h1>
              <p className="text-gray-600 mt-1">
                Comprehensive maintenance management for Ethiopian Electric Utility transformers
              </p>
            </div>
            <TabsList className="grid w-fit grid-cols-10">
              <TabsTrigger value="dashboard" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Dashboard
              </TabsTrigger>
              <TabsTrigger value="records" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Records
              </TabsTrigger>
              <TabsTrigger value="scheduled" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Scheduled Tasks
              </TabsTrigger>
              <TabsTrigger value="workorders" className="flex items-center gap-2">
                <ClipboardList className="h-4 w-4" />
                Work Orders
              </TabsTrigger>
              <TabsTrigger value="preventive" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Preventive
              </TabsTrigger>
              <TabsTrigger value="inspections" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                Inspections
              </TabsTrigger>
              <TabsTrigger value="inventory" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Inventory
              </TabsTrigger>
              <TabsTrigger value="technicians" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Technicians
              </TabsTrigger>
              <TabsTrigger value="schedule" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Schedule
              </TabsTrigger>
              <TabsTrigger value="analytics" className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Analytics
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Dashboard Tab */}
          <TabsContent value="dashboard" className="space-y-6">
            <MaintenanceDashboard />
          </TabsContent>

          {/* Records Tab */}
          <TabsContent value="records" className="space-y-6">
            <MaintenanceTableEnhanced
              showFilters={true}
              className="w-full"
            />
          </TabsContent>

          {/* Scheduled Tasks Tab */}
          <TabsContent value="scheduled" className="space-y-6">
            <ScheduledTasks />
          </TabsContent>

          {/* Work Orders Tab */}
          <TabsContent value="workorders" className="space-y-6">
            <WorkOrders />
          </TabsContent>

          {/* Preventive Maintenance Tab */}
          <TabsContent value="preventive" className="space-y-6">
            <PreventiveMaintenance />
          </TabsContent>

          {/* Inspections Tab */}
          <TabsContent value="inspections" className="space-y-6">
            <InspectionManagement />
          </TabsContent>

          {/* Inventory Tab */}
          <TabsContent value="inventory" className="space-y-6">
            <PartsInventory />
          </TabsContent>

          {/* Technicians Tab */}
          <TabsContent value="technicians" className="space-y-6">
            <TechnicianManagement />
          </TabsContent>

          {/* Schedule Tab */}
          <TabsContent value="schedule" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Quick Schedule */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Quick Schedule
                  </CardTitle>
                  <CardDescription>
                    Schedule new maintenance or view upcoming schedules
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Schedule New Maintenance
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Create maintenance schedules for transformers
                    </p>
                    <Button
                      onClick={() => setShowScheduleDialog(true)}
                      className="inline-flex items-center"
                    >
                      <Calendar className="h-4 w-4 mr-2" />
                      Schedule Maintenance
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Schedule Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Schedule Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div>
                      <div className="text-sm font-medium text-blue-900">Today</div>
                      <div className="text-xs text-blue-700">Scheduled maintenance</div>
                    </div>
                    <div className="text-2xl font-bold text-blue-900">{maintenanceData.summary.scheduled}</div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div>
                      <div className="text-sm font-medium text-yellow-900">In Progress</div>
                      <div className="text-xs text-yellow-700">Active maintenance</div>
                    </div>
                    <div className="text-2xl font-bold text-yellow-900">{maintenanceData.summary.inProgress}</div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div>
                      <div className="text-sm font-medium text-red-900">Overdue</div>
                      <div className="text-xs text-red-700">Requires attention</div>
                    </div>
                    <div className="text-2xl font-bold text-red-900">{maintenanceData.summary.overdue}</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Maintenance Analytics
                </CardTitle>
                <CardDescription>
                  Performance metrics and trends analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <TrendingUp className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-medium text-gray-900 mb-2">
                    Advanced Analytics
                  </h3>
                  <p className="text-gray-600 mb-6 max-w-md mx-auto">
                    Comprehensive analytics dashboard with performance metrics,
                    trend analysis, and predictive maintenance insights.
                  </p>
                  <div className="text-sm text-gray-500">
                    Coming soon - Advanced analytics features
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Enhanced Schedule Dialog */}
        <ScheduleMaintenanceDialogEnhanced
          open={showScheduleDialog}
          onOpenChange={setShowScheduleDialog}
          onSuccess={handleScheduleSuccess}
        />
      </div>
    </MainLayout>
  )
}
