"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/src/components/ui/form"
import { Input } from "@/src/components/ui/input"
import { Button } from "@/src/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Checkbox } from "@/src/components/ui/checkbox"
import { useToast } from "@/src/components/ui/use-toast"
import { Loader2, FileText, Calendar, Filter } from "lucide-react"

const reportSchema = z.object({
  reportType: z.string().min(1, "Report type is required"),
  title: z.string().min(1, "Report title is required"),
  format: z.string().min(1, "Format is required"),
  dateRange: z.string().min(1, "Date range is required"),
  regions: z.array(z.string()).min(1, "At least one region must be selected"),
  includeCharts: z.boolean().default(true),
  includeStatistics: z.boolean().default(true),
  includeRecommendations: z.boolean().default(false),
  includeAppendix: z.boolean().default(false),
  customFilters: z.string().optional(),
})

type ReportFormData = z.infer<typeof reportSchema>

interface GenerateReportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function GenerateReportDialog({ open, onOpenChange, onSuccess }: GenerateReportDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const form = useForm<ReportFormData>({
    resolver: zodResolver(reportSchema),
    defaultValues: {
      reportType: "",
      title: "",
      format: "pdf",
      dateRange: "30d",
      regions: [],
      includeCharts: true,
      includeStatistics: true,
      includeRecommendations: false,
      includeAppendix: false,
      customFilters: "",
    },
  })

  const onSubmit = async (data: ReportFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/mysql/dashboard/actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generateReport',
          payload: data,
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Report Generation Started",
          description: "Your report is being generated. You'll be notified when it's ready.",
        })
        form.reset()
        onOpenChange(false)
        onSuccess()
      } else {
        throw new Error(result.message || 'Failed to generate report')
      }
    } catch (error) {
      console.error('Error generating report:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate report",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const regions = [
    { id: "addis", label: "Addis Ababa" },
    { id: "oromia", label: "Oromia" },
    { id: "amhara", label: "Amhara" },
    { id: "tigray", label: "Tigray" },
    { id: "snnpr", label: "SNNPR" },
    { id: "afar", label: "Afar" },
    { id: "somali", label: "Somali" },
    { id: "benishangul", label: "Benishangul-Gumuz" },
    { id: "gambela", label: "Gambela" },
    { id: "harari", label: "Harari" },
    { id: "dire_dawa", label: "Dire Dawa" },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Generate Report
          </DialogTitle>
          <DialogDescription>
            Create a comprehensive report with customizable content and formatting.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="reportType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Report Type</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select report type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="summary">Executive Summary</SelectItem>
                          <SelectItem value="detailed">Detailed Analysis</SelectItem>
                          <SelectItem value="maintenance">Maintenance Report</SelectItem>
                          <SelectItem value="performance">Performance Report</SelectItem>
                          <SelectItem value="financial">Financial Report</SelectItem>
                          <SelectItem value="compliance">Compliance Report</SelectItem>
                          <SelectItem value="incident">Incident Report</SelectItem>
                          <SelectItem value="custom">Custom Report</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="format"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Output Format</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pdf">PDF Document</SelectItem>
                          <SelectItem value="excel">Excel Spreadsheet</SelectItem>
                          <SelectItem value="word">Word Document</SelectItem>
                          <SelectItem value="powerpoint">PowerPoint Presentation</SelectItem>
                          <SelectItem value="csv">CSV Data Export</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Report Title</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Monthly Transformer Performance Report" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dateRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Date Range
                    </FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select date range" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="7d">Last 7 days</SelectItem>
                          <SelectItem value="30d">Last 30 days</SelectItem>
                          <SelectItem value="90d">Last 90 days</SelectItem>
                          <SelectItem value="6m">Last 6 months</SelectItem>
                          <SelectItem value="1y">Last year</SelectItem>
                          <SelectItem value="ytd">Year to date</SelectItem>
                          <SelectItem value="custom">Custom range</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="regions"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel className="text-base">Regions to Include</FormLabel>
                    <FormDescription>
                      Select the regions to include in the report
                    </FormDescription>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {regions.map((region) => (
                      <FormField
                        key={region.id}
                        control={form.control}
                        name="regions"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={region.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(region.id)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...field.value, region.id])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== region.id
                                          )
                                        )
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="text-sm font-normal">
                                {region.label}
                              </FormLabel>
                            </FormItem>
                          )
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-3">
              <FormLabel className="text-base">Report Content Options</FormLabel>
              
              <FormField
                control={form.control}
                name="includeCharts"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Include Charts and Graphs</FormLabel>
                      <FormDescription>
                        Add visual representations of data
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="includeStatistics"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Include Statistical Analysis</FormLabel>
                      <FormDescription>
                        Add detailed statistics and metrics
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="includeRecommendations"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Include Recommendations</FormLabel>
                      <FormDescription>
                        Add AI-generated recommendations and insights
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="includeAppendix"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Include Technical Appendix</FormLabel>
                      <FormDescription>
                        Add detailed technical data and specifications
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="customFilters"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <Filter className="h-4 w-4 mr-1" />
                    Custom Filters (Optional)
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., status:operational, capacity:>500kVA"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Add custom filters to refine the data included in the report
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Generate Report
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
