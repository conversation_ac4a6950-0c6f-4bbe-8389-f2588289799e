"use client"

import {
  Zap,
  Plus,
  Search,
  FileText,
  AlertTriangle,
  Wrench,
  Download,
  Upload,
  Map,
  BarChart2,
  Setting<PERSON>
} from "lucide-react"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuShortcut
} from "@/src/components/ui/dropdown-menu"
import { useToast } from "@/src/components/ui/use-toast"
import { useRouter } from "next/navigation"

export function QuickActions() {
  const { toast } = useToast()
  const router = useRouter()

  // Handle quick action click
  const handleActionClick = (action: string, path?: string) => {
    toast({
      title: "Quick Action",
      description: `Executing: ${action}`,
    })

    if (path) {
      router.push(path)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon">
          <Zap className="h-5 w-5" />
          <span className="sr-only">Quick Actions</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[220px]">
        <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={() => handleActionClick("Add New Transformer", "/transformers/new")}>
            <Plus className="mr-2 h-4 w-4" />
            <span>New Transformer</span>
            <DropdownMenuShortcut>⌘N</DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleActionClick("Report Issue", "/alerts/report")}>
            <AlertTriangle className="mr-2 h-4 w-4" />
            <span>Report Issue</span>
            <DropdownMenuShortcut>⌘I</DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleActionClick("Schedule Maintenance", "/maintenance/schedule")}>
            <Wrench className="mr-2 h-4 w-4" />
            <span>Schedule Maintenance</span>
            <DropdownMenuShortcut>⌘M</DropdownMenuShortcut>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={() => handleActionClick("Generate Report", "/reports/generate")}>
            <FileText className="mr-2 h-4 w-4" />
            <span>Generate Report</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleActionClick("Export Data", "/data/export")}>
            <Download className="mr-2 h-4 w-4" />
            <span>Export Data</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleActionClick("Import Data", "/data/import")}>
            <Upload className="mr-2 h-4 w-4" />
            <span>Import Data</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={() => handleActionClick("View Transformers", "/transformers")}>
            <Zap className="mr-2 h-4 w-4" />
            <span>Transformers</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleActionClick("View Map", "/map")}>
            <Map className="mr-2 h-4 w-4" />
            <span>Map View</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleActionClick("View Analytics", "/analytics")}>
            <BarChart2 className="mr-2 h-4 w-4" />
            <span>Analytics</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
