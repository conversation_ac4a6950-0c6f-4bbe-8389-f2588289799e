"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Switch } from "@/src/components/ui/switch"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Settings,
  User,
  Bell,
  Shield,
  Database,
  Globe,
  Palette,
  Monitor,
  Smartphone,
  Mail,
  Phone,
  Lock,
  Key,
  Eye,
  EyeOff,
  Save,
  RefreshCw,
  Download,
  Upload,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  MapPin,
  Zap,
  Activity,
  BarChart3,
  FileText,
  Users
} from 'lucide-react'
import { MainLayout } from "@/src/components/layout/main-layout"

// Mock settings data
const mockSettings = {
  profile: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+251-911-123456',
    department: 'Maintenance Engineering',
    region: 'Addis Ababa',
    role: 'Regional Maintenance Engineer',
    employeeId: 'EEU-2024-001',
    avatar: null
  },
  notifications: {
    emailAlerts: true,
    smsAlerts: false,
    pushNotifications: true,
    criticalAlerts: true,
    maintenanceReminders: true,
    reportNotifications: false,
    systemUpdates: true,
    weatherAlerts: true,
    outageNotifications: true,
    fieldTaskUpdates: true
  },
  security: {
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginNotifications: true,
    deviceTracking: true,
    ipWhitelist: false,
    auditLogging: true
  },
  system: {
    language: 'en',
    timezone: 'Africa/Addis_Ababa',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    currency: 'ETB',
    units: 'metric',
    autoRefresh: true,
    refreshInterval: 30,
    dataRetention: 365
  },
  dashboard: {
    theme: 'light',
    compactMode: false,
    showWelcome: true,
    defaultView: 'overview',
    widgetLayout: 'grid',
    animationsEnabled: true,
    soundEnabled: false,
    autoSave: true
  },
  alerts: {
    criticalThreshold: 85,
    warningThreshold: 70,
    alertFrequency: 'immediate',
    escalationTime: 15,
    autoAcknowledge: false,
    alertHistory: 30,
    customRules: true
  }
}

const regions = [
  'Addis Ababa',
  'Oromia',
  'Amhara',
  'Tigray',
  'SNNPR',
  'Somali',
  'Afar',
  'Benishangul-Gumuz',
  'Gambela',
  'Harari',
  'Dire Dawa'
]

const departments = [
  'Maintenance Engineering',
  'Asset Management',
  'Field Operations',
  'Customer Service',
  'Emergency Response',
  'Technical Support',
  'Quality Assurance',
  'Safety & Compliance'
]

export default function SettingsPage() {
  const [settings, setSettings] = useState(mockSettings)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // Load settings data from database
  useEffect(() => {
    const loadSettingsData = async () => {
      try {
        console.log('⚙️ Loading settings data from MySQL...')

        // Fetch user data from the database
        const response = await fetch('/api/mysql/users')

        if (response.ok) {
          const data = await response.json()
          if (data.success && data.data && data.data.length > 0) {
            // Use the first user as the current user (in a real app, this would be based on authentication)
            const currentUser = data.data[0]

            // Update settings with real user data
            const updatedSettings = {
              ...mockSettings,
              profile: {
                firstName: currentUser.firstName || 'John',
                lastName: currentUser.lastName || 'Doe',
                email: currentUser.email || '<EMAIL>',
                phone: currentUser.phone || '+251-911-123456',
                department: currentUser.department || 'Maintenance Engineering',
                region: currentUser.regionName || 'Addis Ababa',
                role: currentUser.role || 'Regional Maintenance Engineer',
                employeeId: `EEU-${currentUser.id || '001'}`,
                avatar: null
              }
            }

            setSettings(updatedSettings)
            console.log('✅ Settings data loaded successfully with real user data')
          } else {
            throw new Error('No user data available')
          }
        } else {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
      } catch (error) {
        console.error('❌ Error loading settings data:', error)
        // Keep mock data as fallback
      } finally {
        setLoading(false)
      }
    }

    loadSettingsData()
  }, [])

  const handleSettingChange = (section: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [key]: value
      }
    }))
    setHasChanges(true)
  }

  const handleSaveSettings = async () => {
    setSaving(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setSaving(false)
    setHasChanges(false)
  }

  const handleResetSettings = () => {
    setSettings(mockSettings)
    setHasChanges(false)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    )
  }

  return (
    <MainLayout
      allowedRoles={["super_admin", "national_asset_manager", "regional_admin"]}
    >
      <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
              <p className="text-muted-foreground">
                Configure your account, notifications, and system preferences
              </p>
            </div>
            <div className="flex items-center gap-2">
              {hasChanges && (
                <Badge variant="secondary" className="mr-2">
                  Unsaved changes
                </Badge>
              )}
              <Button variant="outline" onClick={handleResetSettings}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button
                onClick={handleSaveSettings}
                disabled={!hasChanges || saving}
              >
                {saving ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save Changes
              </Button>
            </div>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="profile" className="space-y-4">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
              <TabsTrigger value="system">System</TabsTrigger>
              <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
              <TabsTrigger value="alerts">Alerts</TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Profile Information
                  </CardTitle>
                  <CardDescription>Update your personal and professional information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        value={settings.profile.firstName}
                        onChange={(e) => handleSettingChange('profile', 'firstName', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        value={settings.profile.lastName}
                        onChange={(e) => handleSettingChange('profile', 'lastName', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={settings.profile.email}
                        onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={settings.profile.phone}
                        onChange={(e) => handleSettingChange('profile', 'phone', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="department">Department</Label>
                      <select
                        id="department"
                        value={settings.profile.department}
                        onChange={(e) => handleSettingChange('profile', 'department', e.target.value)}
                        className="w-full border rounded-md px-3 py-2"
                      >
                        {departments.map(dept => (
                          <option key={dept} value={dept}>{dept}</option>
                        ))}
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="region">Region</Label>
                      <select
                        id="region"
                        value={settings.profile.region}
                        onChange={(e) => handleSettingChange('profile', 'region', e.target.value)}
                        className="w-full border rounded-md px-3 py-2"
                      >
                        {regions.map(region => (
                          <option key={region} value={region}>{region}</option>
                        ))}
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="role">Role</Label>
                      <Input
                        id="role"
                        value={settings.profile.role}
                        onChange={(e) => handleSettingChange('profile', 'role', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="employeeId">Employee ID</Label>
                      <Input
                        id="employeeId"
                        value={settings.profile.employeeId}
                        disabled
                        className="bg-gray-50"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    Notification Preferences
                  </CardTitle>
                  <CardDescription>Configure how you receive alerts and updates</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium">Alert Channels</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            <Label htmlFor="emailAlerts">Email Alerts</Label>
                          </div>
                          <Switch
                            id="emailAlerts"
                            checked={settings.notifications.emailAlerts}
                            onCheckedChange={(checked) => handleSettingChange('notifications', 'emailAlerts', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Smartphone className="h-4 w-4" />
                            <Label htmlFor="smsAlerts">SMS Alerts</Label>
                          </div>
                          <Switch
                            id="smsAlerts"
                            checked={settings.notifications.smsAlerts}
                            onCheckedChange={(checked) => handleSettingChange('notifications', 'smsAlerts', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Monitor className="h-4 w-4" />
                            <Label htmlFor="pushNotifications">Push Notifications</Label>
                          </div>
                          <Switch
                            id="pushNotifications"
                            checked={settings.notifications.pushNotifications}
                            onCheckedChange={(checked) => handleSettingChange('notifications', 'pushNotifications', checked)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">Alert Types</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4" />
                            <Label htmlFor="criticalAlerts">Critical Alerts</Label>
                          </div>
                          <Switch
                            id="criticalAlerts"
                            checked={settings.notifications.criticalAlerts}
                            onCheckedChange={(checked) => handleSettingChange('notifications', 'criticalAlerts', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Settings className="h-4 w-4" />
                            <Label htmlFor="maintenanceReminders">Maintenance Reminders</Label>
                          </div>
                          <Switch
                            id="maintenanceReminders"
                            checked={settings.notifications.maintenanceReminders}
                            onCheckedChange={(checked) => handleSettingChange('notifications', 'maintenanceReminders', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            <Label htmlFor="reportNotifications">Report Notifications</Label>
                          </div>
                          <Switch
                            id="reportNotifications"
                            checked={settings.notifications.reportNotifications}
                            onCheckedChange={(checked) => handleSettingChange('notifications', 'reportNotifications', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Globe className="h-4 w-4" />
                            <Label htmlFor="weatherAlerts">Weather Alerts</Label>
                          </div>
                          <Switch
                            id="weatherAlerts"
                            checked={settings.notifications.weatherAlerts}
                            onCheckedChange={(checked) => handleSettingChange('notifications', 'weatherAlerts', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Zap className="h-4 w-4" />
                            <Label htmlFor="outageNotifications">Outage Notifications</Label>
                          </div>
                          <Switch
                            id="outageNotifications"
                            checked={settings.notifications.outageNotifications}
                            onCheckedChange={(checked) => handleSettingChange('notifications', 'outageNotifications', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Security Settings
                  </CardTitle>
                  <CardDescription>Manage your account security and access controls</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium">Authentication</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Key className="h-4 w-4" />
                            <Label htmlFor="twoFactorAuth">Two-Factor Authentication</Label>
                          </div>
                          <Switch
                            id="twoFactorAuth"
                            checked={settings.security.twoFactorAuth}
                            onCheckedChange={(checked) => handleSettingChange('security', 'twoFactorAuth', checked)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                          <Input
                            id="sessionTimeout"
                            type="number"
                            value={settings.security.sessionTimeout}
                            onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="passwordExpiry">Password Expiry (days)</Label>
                          <Input
                            id="passwordExpiry"
                            type="number"
                            value={settings.security.passwordExpiry}
                            onChange={(e) => handleSettingChange('security', 'passwordExpiry', parseInt(e.target.value))}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">Monitoring</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Bell className="h-4 w-4" />
                            <Label htmlFor="loginNotifications">Login Notifications</Label>
                          </div>
                          <Switch
                            id="loginNotifications"
                            checked={settings.security.loginNotifications}
                            onCheckedChange={(checked) => handleSettingChange('security', 'loginNotifications', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Monitor className="h-4 w-4" />
                            <Label htmlFor="deviceTracking">Device Tracking</Label>
                          </div>
                          <Switch
                            id="deviceTracking"
                            checked={settings.security.deviceTracking}
                            onCheckedChange={(checked) => handleSettingChange('security', 'deviceTracking', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Activity className="h-4 w-4" />
                            <Label htmlFor="auditLogging">Audit Logging</Label>
                          </div>
                          <Switch
                            id="auditLogging"
                            checked={settings.security.auditLogging}
                            onCheckedChange={(checked) => handleSettingChange('security', 'auditLogging', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="system" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    System Preferences
                  </CardTitle>
                  <CardDescription>Configure system-wide settings and preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium">Localization</h4>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label htmlFor="language">Language</Label>
                          <select
                            id="language"
                            value={settings.system.language}
                            onChange={(e) => handleSettingChange('system', 'language', e.target.value)}
                            className="w-full border rounded-md px-3 py-2"
                          >
                            <option value="en">English</option>
                            <option value="am">Amharic (አማርኛ)</option>
                            <option value="or">Oromo (Afaan Oromoo)</option>
                            <option value="ti">Tigrinya (ትግርኛ)</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="timezone">Timezone</Label>
                          <select
                            id="timezone"
                            value={settings.system.timezone}
                            onChange={(e) => handleSettingChange('system', 'timezone', e.target.value)}
                            className="w-full border rounded-md px-3 py-2"
                          >
                            <option value="Africa/Addis_Ababa">Africa/Addis Ababa (EAT)</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="dateFormat">Date Format</Label>
                          <select
                            id="dateFormat"
                            value={settings.system.dateFormat}
                            onChange={(e) => handleSettingChange('system', 'dateFormat', e.target.value)}
                            className="w-full border rounded-md px-3 py-2"
                          >
                            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="currency">Currency</Label>
                          <select
                            id="currency"
                            value={settings.system.currency}
                            onChange={(e) => handleSettingChange('system', 'currency', e.target.value)}
                            className="w-full border rounded-md px-3 py-2"
                          >
                            <option value="ETB">Ethiopian Birr (ETB)</option>
                            <option value="USD">US Dollar (USD)</option>
                            <option value="EUR">Euro (EUR)</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">Data & Performance</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <RefreshCw className="h-4 w-4" />
                            <Label htmlFor="autoRefresh">Auto Refresh</Label>
                          </div>
                          <Switch
                            id="autoRefresh"
                            checked={settings.system.autoRefresh}
                            onCheckedChange={(checked) => handleSettingChange('system', 'autoRefresh', checked)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="refreshInterval">Refresh Interval (seconds)</Label>
                          <Input
                            id="refreshInterval"
                            type="number"
                            value={settings.system.refreshInterval}
                            onChange={(e) => handleSettingChange('system', 'refreshInterval', parseInt(e.target.value))}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="dataRetention">Data Retention (days)</Label>
                          <Input
                            id="dataRetention"
                            type="number"
                            value={settings.system.dataRetention}
                            onChange={(e) => handleSettingChange('system', 'dataRetention', parseInt(e.target.value))}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="dashboard" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Dashboard Customization
                  </CardTitle>
                  <CardDescription>Personalize your dashboard appearance and behavior</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium">Appearance</h4>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label htmlFor="theme">Theme</Label>
                          <select
                            id="theme"
                            value={settings.dashboard.theme}
                            onChange={(e) => handleSettingChange('dashboard', 'theme', e.target.value)}
                            className="w-full border rounded-md px-3 py-2"
                          >
                            <option value="light">Light</option>
                            <option value="dark">Dark</option>
                            <option value="auto">Auto (System)</option>
                          </select>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Monitor className="h-4 w-4" />
                            <Label htmlFor="compactMode">Compact Mode</Label>
                          </div>
                          <Switch
                            id="compactMode"
                            checked={settings.dashboard.compactMode}
                            onCheckedChange={(checked) => handleSettingChange('dashboard', 'compactMode', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Activity className="h-4 w-4" />
                            <Label htmlFor="animationsEnabled">Animations</Label>
                          </div>
                          <Switch
                            id="animationsEnabled"
                            checked={settings.dashboard.animationsEnabled}
                            onCheckedChange={(checked) => handleSettingChange('dashboard', 'animationsEnabled', checked)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">Behavior</h4>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label htmlFor="defaultView">Default View</Label>
                          <select
                            id="defaultView"
                            value={settings.dashboard.defaultView}
                            onChange={(e) => handleSettingChange('dashboard', 'defaultView', e.target.value)}
                            className="w-full border rounded-md px-3 py-2"
                          >
                            <option value="overview">Overview</option>
                            <option value="transformers">Transformers</option>
                            <option value="alerts">Alerts</option>
                            <option value="maintenance">Maintenance</option>
                          </select>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4" />
                            <Label htmlFor="showWelcome">Show Welcome Message</Label>
                          </div>
                          <Switch
                            id="showWelcome"
                            checked={settings.dashboard.showWelcome}
                            onCheckedChange={(checked) => handleSettingChange('dashboard', 'showWelcome', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Save className="h-4 w-4" />
                            <Label htmlFor="autoSave">Auto Save</Label>
                          </div>
                          <Switch
                            id="autoSave"
                            checked={settings.dashboard.autoSave}
                            onCheckedChange={(checked) => handleSettingChange('dashboard', 'autoSave', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="alerts" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    Alert Configuration
                  </CardTitle>
                  <CardDescription>Configure alert thresholds and escalation rules</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium">Thresholds</h4>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label htmlFor="criticalThreshold">Critical Threshold (%)</Label>
                          <Input
                            id="criticalThreshold"
                            type="number"
                            min="0"
                            max="100"
                            value={settings.alerts.criticalThreshold}
                            onChange={(e) => handleSettingChange('alerts', 'criticalThreshold', parseInt(e.target.value))}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="warningThreshold">Warning Threshold (%)</Label>
                          <Input
                            id="warningThreshold"
                            type="number"
                            min="0"
                            max="100"
                            value={settings.alerts.warningThreshold}
                            onChange={(e) => handleSettingChange('alerts', 'warningThreshold', parseInt(e.target.value))}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="alertFrequency">Alert Frequency</Label>
                          <select
                            id="alertFrequency"
                            value={settings.alerts.alertFrequency}
                            onChange={(e) => handleSettingChange('alerts', 'alertFrequency', e.target.value)}
                            className="w-full border rounded-md px-3 py-2"
                          >
                            <option value="immediate">Immediate</option>
                            <option value="5min">Every 5 minutes</option>
                            <option value="15min">Every 15 minutes</option>
                            <option value="30min">Every 30 minutes</option>
                            <option value="1hour">Every hour</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">Escalation</h4>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label htmlFor="escalationTime">Escalation Time (minutes)</Label>
                          <Input
                            id="escalationTime"
                            type="number"
                            value={settings.alerts.escalationTime}
                            onChange={(e) => handleSettingChange('alerts', 'escalationTime', parseInt(e.target.value))}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="alertHistory">Alert History (days)</Label>
                          <Input
                            id="alertHistory"
                            type="number"
                            value={settings.alerts.alertHistory}
                            onChange={(e) => handleSettingChange('alerts', 'alertHistory', parseInt(e.target.value))}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4" />
                            <Label htmlFor="autoAcknowledge">Auto Acknowledge</Label>
                          </div>
                          <Switch
                            id="autoAcknowledge"
                            checked={settings.alerts.autoAcknowledge}
                            onCheckedChange={(checked) => handleSettingChange('alerts', 'autoAcknowledge', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Settings className="h-4 w-4" />
                            <Label htmlFor="customRules">Custom Rules</Label>
                          </div>
                          <Switch
                            id="customRules"
                            checked={settings.alerts.customRules}
                            onCheckedChange={(checked) => handleSettingChange('alerts', 'customRules', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
      </div>
    </MainLayout>
  )
}
