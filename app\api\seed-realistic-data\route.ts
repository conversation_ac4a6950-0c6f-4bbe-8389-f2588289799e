import { NextRequest, NextResponse } from 'next/server'

/**
 * API Route to Seed Realistic Data
 *
 * This endpoint seeds comprehensive realistic data for all modules and features
 * including transformers, maintenance, smart meters, field operations, and more.
 */

// Ethiopian regions and major cities
const ethiopianRegions = [
  { name: 'Addis Ababa', code: 'AA', population: 3500000, coordinates: { lat: 9.0320, lng: 38.7469 } },
  { name: 'Oromia', code: 'OR', population: 37000000, coordinates: { lat: 8.5000, lng: 39.5000 } },
  { name: 'Amhara', code: 'AM', population: 21000000, coordinates: { lat: 11.5000, lng: 37.5000 } },
  { name: 'Tigray', code: 'TI', population: 5500000, coordinates: { lat: 14.0000, lng: 38.5000 } },
  { name: 'SNNP', code: 'SN', population: 20000000, coordinates: { lat: 6.5000, lng: 37.0000 } },
  { name: 'Somali', code: 'SO', population: 5500000, coordinates: { lat: 6.0000, lng: 43.0000 } },
  { name: '<PERSON><PERSON>', code: 'AF', population: 1800000, coordinates: { lat: 11.5000, lng: 41.0000 } }
]

// Ethiopian cities
const ethiopianCities = [
  'Addis Ababa', 'Dire Dawa', 'Mekelle', 'Gondar', 'Dessie', 'Jimma', 'Jijiga', 'Shashamane',
  'Bahir Dar', 'Hawassa', 'Adama', 'Debre Markos', 'Harar', 'Dilla', 'Nekemte', 'Debre Birhan'
]

// Ethiopian names
const ethiopianNames = {
  first: ['Abebe', 'Almaz', 'Bekele', 'Chaltu', 'Dawit', 'Emebet', 'Fikru', 'Genet', 'Haile', 'Iyasu'],
  last: ['Abera', 'Bekele', 'Chala', 'Desta', 'Eshetu', 'Fanta', 'Girma', 'Hailu', 'Kebede', 'Lemma']
}

// Transformer data
const transformerData = {
  manufacturers: ['ABB', 'Siemens', 'Schneider Electric', 'General Electric', 'Hyundai Heavy Industries'],
  types: ['Distribution', 'Power', 'Instrument', 'Auto', 'Isolation'],
  voltageRatings: [
    { primary: 15000, secondary: 400 },
    { primary: 33000, secondary: 11000 },
    { primary: 66000, secondary: 15000 },
    { primary: 132000, secondary: 33000 },
    { primary: 230000, secondary: 66000 }
  ],
  capacities: [50, 100, 160, 250, 315, 400, 500, 630, 800, 1000, 1250, 1600, 2000]
}

// Helper functions
function generateEthiopianPhone() {
  const prefixes = ['091', '092', '093', '094']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const number = Math.floor(Math.random() * 10000000).toString().padStart(7, '0')
  return `+251-${prefix}-${number}`
}

function generateEthiopianName() {
  const firstName = ethiopianNames.first[Math.floor(Math.random() * ethiopianNames.first.length)]
  const lastName = ethiopianNames.last[Math.floor(Math.random() * ethiopianNames.last.length)]
  return `${firstName} ${lastName}`
}

function generateEthiopianCoordinates() {
  const lat = 3.0 + Math.random() * 12.0
  const lng = 33.0 + Math.random() * 15.0
  return { lat: parseFloat(lat.toFixed(6)), lng: parseFloat(lng.toFixed(6)) }
}

function generateTransformerSerial(manufacturer: string, year: number) {
  const prefix = manufacturer.substring(0, 3).toUpperCase()
  const yearCode = year.toString().substring(2)
  const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0')
  return `${prefix}-${yearCode}-${sequence}`
}

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Starting realistic data seeding for Ethiopian Electric Utility...')

    // Import database connection utility
    const { testConnection, executeQuery } = await import('../../../lib/mysql-connection')

    // Test connection
    const isConnected = await testConnection()
    if (!isConnected) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      )
    }

    // Clear existing data
    console.log('🧹 Clearing existing data...')
    await executeQuery('SET FOREIGN_KEY_CHECKS = 0')

    const tables = [
      'app_sensor_readings', 'app_notifications', 'app_work_orders', 'app_outages',
      'app_maintenance_schedules', 'app_alerts', 'app_transformers', 'app_inventory_items',
      'app_service_centers', 'app_regions', 'app_performance_metrics',
      'app_energy_consumption', 'app_weather_data', 'app_system_settings',
      'app_audit_logs', 'app_user_sessions', 'app_reports'
    ]

    for (const table of tables) {
      try {
        await executeQuery(`DELETE FROM ${table}`)
        console.log(`  Cleared ${table}`)
      } catch (error) {
        console.log(`  Table ${table} might not exist, skipping...`)
      }
    }

    await executeQuery('SET FOREIGN_KEY_CHECKS = 1')

    // Seed regions
    console.log('🌍 Seeding Ethiopian regions...')
    for (const region of ethiopianRegions) {
      await executeQuery(
        `INSERT INTO app_regions (id, name, code, country, population, area_km2, coordinates, timezone, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          `region-${region.code.toLowerCase()}`,
          region.name,
          region.code,
          'Ethiopia',
          region.population,
          Math.floor(Math.random() * 100000) + 10000,
          JSON.stringify(region.coordinates),
          'Africa/Addis_Ababa'
        ]
      )
    }

    // Seed service centers
    console.log('🏢 Seeding service centers...')
    const serviceCenters = []
    for (let i = 0; i < 15; i++) {
      const region = ethiopianRegions[Math.floor(Math.random() * ethiopianRegions.length)]
      const city = ethiopianCities[Math.floor(Math.random() * ethiopianCities.length)]
      const coordinates = generateEthiopianCoordinates()

      const serviceCenter = {
        id: `sc-${String(i + 1).padStart(3, '0')}`,
        name: `${city} Service Center`,
        regionId: `region-${region.code.toLowerCase()}`,
        address: `${city}, ${region.name}, Ethiopia`,
        coordinates: coordinates,
        phone: generateEthiopianPhone(),
        email: `${city.toLowerCase().replace(/\s+/g, '')}@eeu.gov.et`,
        managerName: generateEthiopianName(),
        operatingHours: '08:00-17:00',
        facilities: JSON.stringify(['Workshop', 'Storage', 'Testing Lab', 'Vehicle Fleet'])
      }

      serviceCenters.push(serviceCenter)

      await executeQuery(
        `INSERT INTO app_service_centers (id, name, region_id, address, coordinates, phone, email, manager_name, operating_hours, facilities, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          serviceCenter.id,
          serviceCenter.name,
          serviceCenter.regionId,
          serviceCenter.address,
          JSON.stringify(serviceCenter.coordinates),
          serviceCenter.phone,
          serviceCenter.email,
          serviceCenter.managerName,
          serviceCenter.operatingHours,
          serviceCenter.facilities
        ]
      )
    }

    // Seed transformers
    console.log('⚡ Seeding realistic transformers...')
    const transformers = []
    for (let i = 0; i < 100; i++) {
      const manufacturer = transformerData.manufacturers[Math.floor(Math.random() * transformerData.manufacturers.length)]
      const type = transformerData.types[Math.floor(Math.random() * transformerData.types.length)]
      const voltage = transformerData.voltageRatings[Math.floor(Math.random() * transformerData.voltageRatings.length)]
      const capacity = transformerData.capacities[Math.floor(Math.random() * transformerData.capacities.length)]
      const installationYear = 2010 + Math.floor(Math.random() * 14)
      const serviceCenter = serviceCenters[Math.floor(Math.random() * serviceCenters.length)]
      const coordinates = generateEthiopianCoordinates()

      const status = ['operational', 'warning', 'maintenance', 'critical', 'offline'][Math.floor(Math.random() * 5)]
      const healthIndex = status === 'operational' ? 80 + Math.random() * 20 :
                         status === 'warning' ? 60 + Math.random() * 20 :
                         status === 'maintenance' ? 70 + Math.random() * 15 :
                         status === 'critical' ? 20 + Math.random() * 40 :
                         10 + Math.random() * 30

      const transformer = {
        id: `transformer-${String(i + 1).padStart(3, '0')}`,
        serialNumber: generateTransformerSerial(manufacturer, installationYear),
        name: `${serviceCenter.name.split(' ')[0]} Transformer ${i + 1}`,
        manufacturer: manufacturer,
        model: `${manufacturer.substring(0, 3).toUpperCase()}-${capacity}${type.substring(0, 1)}`,
        type: type,
        capacity: capacity,
        primaryVoltage: voltage.primary,
        secondaryVoltage: voltage.secondary,
        status: status,
        healthIndex: parseFloat(healthIndex.toFixed(1)),
        installationDate: `${installationYear}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        lastMaintenanceDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        nextMaintenanceDate: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        serviceCenterId: serviceCenter.id,
        regionId: serviceCenter.regionId,
        location: {
          address: `${serviceCenter.address} Substation`,
          coordinates: coordinates,
          substationName: `${serviceCenter.name.split(' ')[0]} Substation`
        },
        specifications: {
          coolingType: ['ONAN', 'ONAF', 'OFAF', 'ODAF'][Math.floor(Math.random() * 4)],
          tapChanger: Math.random() > 0.3,
          protectionClass: ['IP23', 'IP44', 'IP54'][Math.floor(Math.random() * 3)],
          frequency: 50,
          phases: 3,
          connectionType: ['Dyn11', 'Yyn0', 'Dyn1'][Math.floor(Math.random() * 3)]
        },
        currentMetrics: {
          temperature: 35 + Math.random() * 30,
          loadPercentage: Math.random() * 100,
          oilLevel: 80 + Math.random() * 20,
          vibration: Math.random() * 5,
          powerFactor: 0.85 + Math.random() * 0.1,
          efficiency: 95 + Math.random() * 4
        }
      }

      transformers.push(transformer)

      await executeQuery(
        `INSERT INTO app_transformers (id, serial_number, name, manufacturer, model, type, capacity, primary_voltage, secondary_voltage, status, health_index, installation_date, last_maintenance_date, next_maintenance_date, service_center_id, region_id, location, specifications, current_metrics, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          transformer.id,
          transformer.serialNumber,
          transformer.name,
          transformer.manufacturer,
          transformer.model,
          transformer.type,
          transformer.capacity,
          transformer.primaryVoltage,
          transformer.secondaryVoltage,
          transformer.status,
          transformer.healthIndex,
          transformer.installationDate,
          transformer.lastMaintenanceDate,
          transformer.nextMaintenanceDate,
          transformer.serviceCenterId,
          transformer.regionId,
          JSON.stringify(transformer.location),
          JSON.stringify(transformer.specifications),
          JSON.stringify(transformer.currentMetrics)
        ]
      )
    }

    console.log('✅ Realistic data seeding completed successfully!')

    return NextResponse.json({
      success: true,
      message: 'Realistic data seeded successfully',
      data: {
        regions: ethiopianRegions.length,
        serviceCenters: serviceCenters.length,
        transformers: transformers.length
      }
    })

  } catch (error) {
    console.error('❌ Error seeding realistic data:', error)
    return NextResponse.json(
      { error: 'Failed to seed realistic data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
