const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

async function recreateAppTransformersTable() {
  const connection = await mysql.createConnection(config);
  // Drop the table if it exists
  await connection.execute('DROP TABLE IF EXISTS app_transformers2');
  // Recreate the table with the correct schema
  await connection.execute(`
    CREATE TABLE app_transformers2 (
      id INT PRIMARY KEY AUTO_INCREMENT,
      serial_number VARCHAR(50) NOT NULL UNIQUE,
      name VARCHAR(100) NOT NULL,
      type ENUM('distribution', 'power', 'instrument', 'auto') NOT NULL,
      capacity_kva DECIMAL(10,2) NOT NULL,
      voltage_primary DECIMAL(10,2) NOT NULL,
      voltage_secondary DECIMAL(10,2) NOT NULL,
      manufacturer VARCHAR(100),
      model VARCHAR(100),
      year_manufactured YEAR,
      installation_date DATE,
      location_name VARCHAR(200),
      latitude DECIMAL(10,8),
      longitude DECIMAL(11,8),
      region_id INT NOT NULL,
      service_center_id INT,
      status ENUM('operational', 'warning', 'maintenance', 'critical', 'burnt') DEFAULT 'operational',
      efficiency_rating DECIMAL(5,2) DEFAULT 95.0,
      load_factor DECIMAL(5,2) DEFAULT 75.0,
      temperature DECIMAL(5,2),
      oil_level DECIMAL(5,2),
      last_maintenance DATE,
      next_maintenance DATE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (region_id) REFERENCES app_regions2(id),
      FOREIGN KEY (service_center_id) REFERENCES app_service_centers2(id)
    ) ENGINE=InnoDB;
  `);
  await connection.end();
  console.log('✅ app_transformers2 table recreated successfully.');
}

recreateAppTransformersTable().catch(err => {
  console.error('❌ Error recreating app_transformers table:', err);
  process.exit(1);
});
