import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar"
import {
  Edit, Trash, MoreHorizontal, UserCheck, UserX, Mail,
  KeyRound, Shield, Eye, Copy
} from "lucide-react"
import { useToast } from "@/src/components/ui/use-toast"
import { EnhancedUserDialog } from "@/components/enhanced-user-dialog"
import { UserDeleteDialog } from "@/components/user-delete-dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rigger,
} from "@/src/components/ui/tooltip"

interface UserData {
  id: string
  name: string
  email: string
  role: string
  department: string
  status: string
  lastActive: string
  avatar?: string
}

interface UsersTableProps {
  role?: string
  onUserUpdated?: () => void
}

export function UsersTable({ role, onUserUpdated }: UsersTableProps) {
  const { toast } = useToast()
  const [users, setUsers] = useState<UserData[]>([
    {
      id: "USR-001",
      name: "John Smith",
      email: "<EMAIL>",
      role: "Administrator",
      department: "IT",
      status: "Active",
      lastActive: "Apr 27, 2025 10:24 AM",
    },
    {
      id: "USR-002",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      role: "Technician",
      department: "Maintenance",
      status: "Active",
      lastActive: "Apr 27, 2025 09:15 AM",
    },
    {
      id: "USR-003",
      name: "Michael Brown",
      email: "<EMAIL>",
      role: "Operator",
      department: "Operations",
      status: "Active",
      lastActive: "Apr 27, 2025 08:30 AM",
    },
    {
      id: "USR-004",
      name: "Emily Davis",
      email: "<EMAIL>",
      role: "Administrator",
      department: "Management",
      status: "Active",
      lastActive: "Apr 26, 2025 04:45 PM",
    },
    {
      id: "USR-005",
      name: "David Wilson",
      email: "<EMAIL>",
      role: "Technician",
      department: "Maintenance",
      status: "Inactive",
      lastActive: "Apr 20, 2025 02:30 PM",
    },
  ])

  const [selectedUser, setSelectedUser] = useState<UserData | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  const filteredUsers = role ? users.filter((user) => user.role === role) : users

  const handleEditUser = (user: UserData) => {
    setSelectedUser(user)
    setIsEditDialogOpen(true)
  }

  const handleDeleteUser = (user: UserData) => {
    setSelectedUser(user)
    setIsDeleteDialogOpen(true)
  }

  const handleUserSave = (userData: UserData) => {
    // Update user in the list
    setUsers(users.map(user => user.id === userData.id ? userData : user))

    if (onUserUpdated) {
      onUserUpdated()
    }
  }

  const confirmDeleteUser = () => {
    if (selectedUser) {
      // Remove user from the list
      setUsers(users.filter(user => user.id !== selectedUser.id))

      if (onUserUpdated) {
        onUserUpdated()
      }
    }
  }

  const toggleUserStatus = (userId: string) => {
    setUsers(users.map(user => {
      if (user.id === userId) {
        const newStatus = user.status === "Active" ? "Inactive" : "Active"

        toast({
          title: `User ${newStatus}`,
          description: `${user.name} has been set to ${newStatus}.`,
        })

        return {
          ...user,
          status: newStatus
        }
      }
      return user
    }))

    if (onUserUpdated) {
      onUserUpdated()
    }
  }

  const sendPasswordReset = (user: UserData) => {
    toast({
      title: "Password Reset Sent",
      description: `A password reset link has been sent to ${user.email}.`,
    })
  }

  const copyEmail = (email: string) => {
    navigator.clipboard.writeText(email)
    toast({
      title: "Email Copied",
      description: "Email address has been copied to clipboard.",
    })
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>User</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Department</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Last Active</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredUsers.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                No users found.
              </TableCell>
            </TableRow>
          ) : (
            filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={user.avatar || `/placeholder.svg?height=32&width=32&text=${user.name.charAt(0)}`}
                        alt={user.name}
                      />
                      <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>{user.role}</TableCell>
                <TableCell>{user.department}</TableCell>
                <TableCell>
                  <Badge
                    className={
                      user.status === "Active" ? "bg-green-500 hover:bg-green-600" : "bg-slate-500 hover:bg-slate-600"
                    }
                  >
                    {user.status}
                  </Badge>
                </TableCell>
                <TableCell>{user.lastActive}</TableCell>
                <TableCell className="text-right">
                  <TooltipProvider>
                    <div className="flex justify-end gap-1">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" onClick={() => handleEditUser(user)}>
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Edit user</p>
                        </TooltipContent>
                      </Tooltip>

                      <DropdownMenu>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">More options</span>
                              </Button>
                            </DropdownMenuTrigger>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>More options</p>
                          </TooltipContent>
                        </Tooltip>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>User Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleEditUser(user)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit User
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => toggleUserStatus(user.id)}>
                            {user.status === "Active" ? (
                              <>
                                <UserX className="mr-2 h-4 w-4" />
                                Deactivate User
                              </>
                            ) : (
                              <>
                                <UserCheck className="mr-2 h-4 w-4" />
                                Activate User
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => sendPasswordReset(user)}>
                            <KeyRound className="mr-2 h-4 w-4" />
                            Reset Password
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => copyEmail(user.email)}>
                            <Copy className="mr-2 h-4 w-4" />
                            Copy Email
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600 focus:text-red-600"
                            onClick={() => handleDeleteUser(user)}
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TooltipProvider>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {/* Edit User Dialog */}
      <EnhancedUserDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        user={selectedUser}
        onSave={handleUserSave}
      />

      {/* Delete User Dialog */}
      {selectedUser && (
        <UserDeleteDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          userName={selectedUser.name}
          onDelete={confirmDeleteUser}
        />
      )}
    </>
  )
}
