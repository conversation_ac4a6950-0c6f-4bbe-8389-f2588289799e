"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Progress } from "@/src/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Calendar,
  Clock,
  Wrench,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Activity,
  Users,
  Target,
  BarChart3,
  Plus,
  Filter,
  Download,
  RefreshCw,
  Settings,
  Bell,
  Zap,
  Gauge
} from 'lucide-react'
import { maintenanceDatabaseService, MaintenanceStatistics } from '@/src/services/maintenance-database-service'
import { MaintenanceRecord } from '@/src/lib/db/schema'

interface MaintenanceDashboardProps {
  className?: string
}

export function MaintenanceDashboard({ className }: MaintenanceDashboardProps) {
  const [statistics, setStatistics] = useState<MaintenanceStatistics | null>(null)
  const [upcomingMaintenance, setUpcomingMaintenance] = useState<MaintenanceRecord[]>([])
  const [overdueMaintenance, setOverdueMaintenance] = useState<MaintenanceRecord[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  // Load data
  const loadData = async () => {
    try {
      setIsLoading(true)
      const [stats, upcoming, overdue] = await Promise.all([
        maintenanceDatabaseService.getMaintenanceStatistics(),
        maintenanceDatabaseService.getUpcomingMaintenance(7),
        maintenanceDatabaseService.getOverdueMaintenance()
      ])

      setStatistics(stats)
      setUpcomingMaintenance(upcoming)
      setOverdueMaintenance(overdue)
    } catch (error) {
      console.error('Error loading maintenance data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true)
    await loadData()
    setRefreshing(false)
  }

  useEffect(() => {
    loadData()
  }, [])

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-500'
      case 'in_progress': return 'bg-yellow-500'
      case 'completed': return 'bg-green-500'
      case 'cancelled': return 'bg-gray-500'
      case 'overdue': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'text-green-600 bg-green-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      case 'high': return 'text-orange-600 bg-orange-50'
      case 'critical': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Maintenance Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive maintenance management for Ethiopian Electric Utility transformers
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Schedule Maintenance
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Maintenance */}
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-gray-600">Total Maintenance</CardTitle>
                <Wrench className="h-5 w-5 text-blue-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{statistics.total}</div>
              <div className="flex items-center mt-2 text-sm">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">+12% from last month</span>
              </div>
            </CardContent>
          </Card>

          {/* Scheduled */}
          <Card className="border-l-4 border-l-yellow-500">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-gray-600">Scheduled</CardTitle>
                <Calendar className="h-5 w-5 text-yellow-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{statistics.byStatus.scheduled}</div>
              <div className="flex items-center mt-2 text-sm">
                <Clock className="h-4 w-4 text-yellow-500 mr-1" />
                <span className="text-gray-600">{statistics.upcomingCount} upcoming</span>
              </div>
            </CardContent>
          </Card>

          {/* In Progress */}
          <Card className="border-l-4 border-l-orange-500">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-gray-600">In Progress</CardTitle>
                <Activity className="h-5 w-5 text-orange-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{statistics.byStatus.in_progress}</div>
              <div className="flex items-center mt-2 text-sm">
                <Users className="h-4 w-4 text-orange-500 mr-1" />
                <span className="text-gray-600">Active teams</span>
              </div>
            </CardContent>
          </Card>

          {/* Overdue */}
          <Card className="border-l-4 border-l-red-500">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-gray-600">Overdue</CardTitle>
                <AlertTriangle className="h-5 w-5 text-red-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{statistics.byStatus.overdue}</div>
              <div className="flex items-center mt-2 text-sm">
                <XCircle className="h-4 w-4 text-red-500 mr-1" />
                <span className="text-red-600">Requires attention</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Status Overview */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Maintenance Overview
            </CardTitle>
            <CardDescription>
              Current status distribution and performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {statistics && (
              <>
                {/* Status Distribution */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-gray-900">Status Distribution</h4>
                  <div className="space-y-3">
                    {Object.entries(statistics.byStatus).map(([status, count]) => (
                      <div key={status} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(status)}`} />
                          <span className="text-sm font-medium capitalize">{status.replace('_', ' ')}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${getStatusColor(status)}`}
                              style={{ width: `${(count / statistics.total) * 100}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium w-8 text-right">{count}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-gray-900">Performance Metrics</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Clock className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-900">Avg Duration</span>
                      </div>
                      <div className="text-2xl font-bold text-blue-900">{statistics.averageDuration}h</div>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-900">Completion Rate</span>
                      </div>
                      <div className="text-2xl font-bold text-green-900">
                        {Math.round((statistics.byStatus.completed / statistics.total) * 100)}%
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions & Alerts */}
        <div className="space-y-6">
          {/* Critical Alerts */}
          {overdueMaintenance.length > 0 && (
            <Card className="border-red-200 bg-red-50">
              <CardHeader className="pb-3">
                <CardTitle className="text-red-800 flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Overdue Maintenance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {overdueMaintenance.slice(0, 3).map((maintenance) => (
                    <div key={maintenance.id} className="flex items-center justify-between p-2 bg-white rounded border">
                      <div>
                        <div className="font-medium text-sm">{maintenance.title}</div>
                        <div className="text-xs text-gray-600">ID: {maintenance.transformerId}</div>
                      </div>
                      <Badge className={getPriorityColor(maintenance.priority)}>
                        {maintenance.priority}
                      </Badge>
                    </div>
                  ))}
                  {overdueMaintenance.length > 3 && (
                    <Button variant="outline" size="sm" className="w-full">
                      View All ({overdueMaintenance.length})
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Upcoming Maintenance */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-600" />
                Upcoming This Week
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {upcomingMaintenance.slice(0, 4).map((maintenance) => (
                  <div key={maintenance.id} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <div className="font-medium text-sm">{maintenance.title}</div>
                      <div className="text-xs text-gray-600">
                        {new Date(maintenance.scheduledDate).toLocaleDateString()}
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {maintenance.estimatedDuration}h
                    </Badge>
                  </div>
                ))}
                {upcomingMaintenance.length === 0 && (
                  <div className="text-center py-4 text-gray-500 text-sm">
                    No upcoming maintenance scheduled
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Plus className="h-4 w-4 mr-2" />
                Schedule New Maintenance
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Users className="h-4 w-4 mr-2" />
                Assign Technician
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Bell className="h-4 w-4 mr-2" />
                Send Notifications
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <BarChart3 className="h-4 w-4 mr-2" />
                Generate Report
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
