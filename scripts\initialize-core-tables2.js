// Create app_regions2 and app_service_centers2 tables for workaround
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

const createRegions2 = `CREATE TABLE IF NOT EXISTS app_regions2 (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(10) NOT NULL UNIQUE,
  population INT,
  area_km2 DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);`;

const createServiceCenters2 = `CREATE TABLE IF NOT EXISTS app_service_centers2 (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) NOT NULL UNIQUE,
  region_id INT,
  address TEXT,
  phone VARCHAR(20),
  email VARCHAR(100),
  manager_name VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (region_id) REFERENCES app_regions2(id)
);`;

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    await connection.execute(createRegions2);
    console.log('✅ Created app_regions2 table');
    await connection.execute(createServiceCenters2);
    console.log('✅ Created app_service_centers2 table');
  } catch (err) {
    console.error('❌ Error creating tables:', err);
  } finally {
    await connection.end();
  }
})();
