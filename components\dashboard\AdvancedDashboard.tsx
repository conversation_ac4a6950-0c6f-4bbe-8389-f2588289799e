"use client"

import { useState, useEffect } from 'react'
import { DragDrop<PERSON>ontext, Droppable, Draggable } from 'react-beautiful-dnd'
import { 
  BarChart2, 
  Calendar, 
  Settings, 
  Maximize, 
  Minimize, 
  X, 
  RefreshCw,
  PlusCircle,
  Save,
  Download,
  MoreHorizontal,
  Edit
} from 'lucide-react'
import { Widget, DashboardLayout } from '../../types/dashboard-widgets'
import TransformerStatusWidget from './widgets/TransformerStatusWidget'
import MaintenanceScheduleWidget from './widgets/MaintenanceScheduleWidget'
import OutageMapWidget from './widgets/OutageMapWidget'
import AlertsListWidget from './widgets/AlertsListWidget'
import WeatherImpactWidget from './widgets/WeatherImpactWidget'

interface AdvancedDashboardProps {
  userId: string
}

export default function AdvancedDashboard({ userId }: AdvancedDashboardProps) {
  const [widgets, setWidgets] = useState<Widget[]>([])
  const [layouts, setLayouts] = useState<DashboardLayout[]>([])
  const [activeLayout, setActiveLayout] = useState<string | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)
        
        // In a real implementation, this would fetch from the API
        // For now, we'll use mock data
        const mockWidgets: Widget[] = [
          {
            id: 'widget-1',
            userId,
            type: 'transformer-status',
            title: 'Transformer Status Overview',
            position: { x: 0, y: 0 },
            size: { w: 2, h: 1 },
            settings: {
              showTotal: true,
              showByStatus: true,
              showByRegion: true,
              displayType: 'cards'
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: 'widget-2',
            userId,
            type: 'maintenance-schedule',
            title: 'Upcoming Maintenance',
            position: { x: 2, y: 0 },
            size: { w: 2, h: 1 },
            settings: {
              timeRange: 'week',
              showCompleted: false,
              showPending: true,
              showOverdue: true
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: 'widget-3',
            userId,
            type: 'outage-map',
            title: 'Active Outages Map',
            position: { x: 0, y: 1 },
            size: { w: 2, h: 2 },
            settings: {
              mapType: 'standard',
              showLegend: true,
              autoZoom: true,
              defaultZoom: 8,
              clusterMarkers: true
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: 'widget-4',
            userId,
            type: 'alerts-list',
            title: 'Critical Alerts',
            position: { x: 2, y: 1 },
            size: { w: 2, h: 1 },
            settings: {
              maxItems: 5,
              showCritical: true,
              showWarning: true,
              showInfo: false,
              autoRefresh: true,
              refreshInterval: 60
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: 'widget-5',
            userId,
            type: 'weather-impact',
            title: 'Weather Forecast',
            position: { x: 2, y: 2 },
            size: { w: 2, h: 1 },
            settings: {
              regions: ['region-001'],
              forecastDays: 5,
              showTemperature: true,
              showPrecipitation: true,
              showWind: true,
              showAlerts: true
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
        
        const mockLayouts: DashboardLayout[] = [
          {
            id: 'layout-1',
            userId,
            name: 'Default Dashboard',
            isDefault: true,
            widgets: mockWidgets.map(widget => widget.id),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
        
        setWidgets(mockWidgets)
        setLayouts(mockLayouts)
        setActiveLayout(mockLayouts[0].id)
        
      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError('Failed to load dashboard data')
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchDashboardData()
  }, [userId])

  // Handle drag and drop
  const handleDragEnd = (result: any) => {
    if (!result.destination) return
    
    const items = Array.from(widgets)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)
    
    // Update positions
    const updatedWidgets = items.map((widget, index) => ({
      ...widget,
      position: {
        x: index % 2 === 0 ? 0 : 2,
        y: Math.floor(index / 2)
      }
    }))
    
    setWidgets(updatedWidgets)
  }

  // Save dashboard layout
  const saveDashboardLayout = async () => {
    try {
      // In a real implementation, this would save to the API
      console.log('Saving dashboard layout:', widgets)
      setIsEditing(false)
    } catch (err) {
      console.error('Error saving dashboard layout:', err)
      setError('Failed to save dashboard layout')
    }
  }

  // Render widget based on type
  const renderWidget = (widget: Widget) => {
    switch (widget.type) {
      case 'transformer-status':
        return <TransformerStatusWidget widget={widget} isEditing={isEditing} />
      case 'maintenance-schedule':
        return <MaintenanceScheduleWidget widget={widget} isEditing={isEditing} />
      case 'outage-map':
        return <OutageMapWidget widget={widget} isEditing={isEditing} />
      case 'alerts-list':
        return <AlertsListWidget widget={widget} isEditing={isEditing} />
      case 'weather-impact':
        return <WeatherImpactWidget widget={widget} isEditing={isEditing} />
      default:
        return <div>Unknown widget type: {widget.type}</div>
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-red-500">{error}</div>
      </div>
    )
  }

  return (
    <div className="h-full">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-medium">Dashboard</h2>
          
          {/* Layout selector */}
          <div className="relative">
            <select 
              className="appearance-none bg-white border rounded-md px-3 py-1.5 pr-8 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
              value={activeLayout || ''}
              onChange={(e) => setActiveLayout(e.target.value)}
            >
              {layouts.map(layout => (
                <option key={layout.id} value={layout.id}>{layout.name}</option>
              ))}
            </select>
          </div>
          
          <button 
            className="text-gray-600 border rounded-md p-1.5 hover:bg-gray-50"
            title="Refresh dashboard"
          >
            <RefreshCw size={16} />
          </button>
        </div>
        
        <div className="flex items-center space-x-2">
          {isEditing ? (
            <>
              <button 
                className="flex items-center space-x-1 bg-green-600 text-white rounded-md px-3 py-1.5 text-sm hover:bg-green-700"
                onClick={saveDashboardLayout}
              >
                <Save size={14} />
                <span>Save Layout</span>
              </button>
              
              <button 
                className="flex items-center space-x-1 border rounded-md px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-50"
                onClick={() => setIsEditing(false)}
              >
                <X size={14} />
                <span>Cancel</span>
              </button>
            </>
          ) : (
            <>
              <button 
                className="flex items-center space-x-1 border rounded-md px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-50"
                onClick={() => setIsEditing(true)}
              >
                <Edit size={14} />
                <span>Edit Layout</span>
              </button>
              
              <button 
                className="flex items-center space-x-1 border rounded-md px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-50"
              >
                <PlusCircle size={14} />
                <span>Add Widget</span>
              </button>
              
              <button 
                className="text-gray-600 border rounded-md p-1.5 hover:bg-gray-50"
                title="Download dashboard"
              >
                <Download size={16} />
              </button>
              
              <button 
                className="text-gray-600 border rounded-md p-1.5 hover:bg-gray-50"
                title="Dashboard settings"
              >
                <Settings size={16} />
              </button>
            </>
          )}
        </div>
      </div>
      
      {/* Dashboard grid */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="dashboard" isDropDisabled={!isEditing}>
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="grid grid-cols-4 gap-4"
            >
              {widgets.map((widget, index) => (
                <Draggable 
                  key={widget.id} 
                  draggableId={widget.id} 
                  index={index}
                  isDragDisabled={!isEditing}
                >
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={`bg-white rounded-lg border shadow-sm overflow-hidden
                        col-span-${widget.size.w} row-span-${widget.size.h}`}
                      style={{
                        ...provided.draggableProps.style,
                        gridColumn: `span ${widget.size.w}`,
                        gridRow: `span ${widget.size.h}`
                      }}
                    >
                      <div className="p-3 border-b flex items-center justify-between">
                        <h3 className="font-medium text-gray-700">{widget.title}</h3>
                        
                        <div className="flex items-center space-x-1">
                          {isEditing && (
                            <div {...provided.dragHandleProps} className="cursor-move p-1 hover:bg-gray-100 rounded">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8 6H10V8H8V6Z" fill="currentColor" />
                                <path d="M14 6H16V8H14V6Z" fill="currentColor" />
                                <path d="M8 11H10V13H8V11Z" fill="currentColor" />
                                <path d="M14 11H16V13H14V11Z" fill="currentColor" />
                                <path d="M8 16H10V18H8V16Z" fill="currentColor" />
                                <path d="M14 16H16V18H14V16Z" fill="currentColor" />
                              </svg>
                            </div>
                          )}
                          
                          <button className="p-1 hover:bg-gray-100 rounded">
                            <RefreshCw size={14} className="text-gray-500" />
                          </button>
                          
                          <button className="p-1 hover:bg-gray-100 rounded">
                            <Settings size={14} className="text-gray-500" />
                          </button>
                          
                          {isEditing && (
                            <button className="p-1 hover:bg-gray-100 rounded text-red-500">
                              <X size={14} />
                            </button>
                          )}
                        </div>
                      </div>
                      
                      <div className="p-4">
                        {renderWidget(widget)}
                      </div>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  )
}
