"use client"

import { useState, useEffect } from "react"
import { useSearch<PERSON>ara<PERSON>, useR<PERSON><PERSON> } from "next/navigation"
import { useToast } from "@/src/components/ui/use-toast"
import { transformerService } from "@/src/services/transformer-service"
import { mapService } from "@/src/services/map-service"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger } from "@/src/components/ui/tabs"
import { Button } from "@/src/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { TransformerSpecifications } from "@/components/transformer-specifications"
import { TransformerHistoryChart } from "@/components/transformer-history-chart"
import { TransformerLocationMap } from "@/components/transformer-location-map"
import { PDFPreviewDialog } from "@/components/pdf-preview-dialog"
import { ReportIssueDialog } from "@/components/report-issue-dialog"
import { ScheduleMaintenanceDialog } from "@/components/schedule-maintenance-dialog"
import { AddInspectionDialog } from "@/components/add-inspection-dialog"
import { AddTestResultDialog } from "@/components/add-test-result-dialog"
import { MonitorPerformanceDialog } from "@/components/monitor-performance-dialog"
import { UnifiedMap } from "@/components/unified-map"
import { useMaintenance } from "@/src/contexts/maintenance-context"
import { Badge } from "@/src/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@/src/components/ui/dialog"
import { format } from "date-fns"
import {
  AlertCircle,
  Calendar,
  BarChartIcon as ChartBar,
  ClipboardCheck,
  ClipboardList,
  Download,
  FileBarChart,
  FileText,
  Map,
  Pencil,
  PlusCircle,
  Printer,
  Thermometer,
  Wrench,
} from "lucide-react"
import type { Transformer } from "@/src/types/transformer"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/src/components/ui/dropdown-menu"

interface TransformerDetailsProps {
  transformer: Transformer
}

export function TransformerDetails({ transformer }: TransformerDetailsProps) {
  const [isPdfPreviewOpen, setIsPdfPreviewOpen] = useState(false)
  const [isReportIssueOpen, setIsReportIssueOpen] = useState(false)
  const [isScheduleMaintenanceOpen, setIsScheduleMaintenanceOpen] = useState(false)
  const [isAddInspectionOpen, setIsAddInspectionOpen] = useState(false)
  const [isAddTestResultOpen, setIsAddTestResultOpen] = useState(false)
  const [isMonitorPerformanceOpen, setIsMonitorPerformanceOpen] = useState(false)
  const [isMapViewOpen, setIsMapViewOpen] = useState(false)
  const [isLocationMapViewOpen, setIsLocationMapViewOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("specifications")
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [maintenanceRecords, setMaintenanceRecords] = useState<any[]>([])
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()

  // Get maintenance data from context
  const { getMaintenanceRecordsByTransformerId, completeMaintenanceRecord, cancelMaintenanceRecord } = useMaintenance()

  // Set active tab based on URL parameter
  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab) {
      setActiveTab(tab)
    }
  }, [searchParams])

  // Handle downloading PDF
  const handleDownloadPDF = () => {
    toast({
      title: "PDF Downloaded",
      description: `Transformer report for ${transformer.serialNumber} has been downloaded.`,
    })
  }

  // Handle exporting data
  const handleExportData = () => {
    toast({
      title: "Data Exported",
      description: `Transformer data for ${transformer.serialNumber} has been exported.`,
    })
  }

  // Handle editing location
  const handleEditLocation = () => {
    toast({
      title: "Location Updated",
      description: `Location information for transformer ${transformer.serialNumber} has been updated.`,
    })
  }

  // Load maintenance records
  useEffect(() => {
    const loadMaintenanceRecords = async () => {
      try {
        const records = await getMaintenanceRecordsByTransformerId(transformer.id)
        setMaintenanceRecords(records)
      } catch (error) {
        console.error("Error loading maintenance records:", error)
      }
    }

    loadMaintenanceRecords()
  }, [transformer.id, getMaintenanceRecordsByTransformerId, refreshTrigger])

  // Handle record updates
  const handleRecordUpdate = () => {
    setRefreshTrigger(prev => prev + 1)
    toast({
      title: "Record Updated",
      description: "The record has been successfully updated.",
    })
  }

  // Handle completing maintenance
  const handleCompleteMaintenance = async (id: string) => {
    try {
      await completeMaintenanceRecord(id)
      setRefreshTrigger(prev => prev + 1)
      toast({
        title: "Maintenance Completed",
        description: "The maintenance record has been marked as completed.",
      })
    } catch (error) {
      console.error("Error completing maintenance:", error)
      toast({
        title: "Error",
        description: "Failed to complete maintenance record. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Handle cancelling maintenance
  const handleCancelMaintenance = async (id: string) => {
    try {
      await cancelMaintenanceRecord(id)
      setRefreshTrigger(prev => prev + 1)
      toast({
        title: "Maintenance Cancelled",
        description: "The maintenance record has been cancelled.",
      })
    } catch (error) {
      console.error("Error cancelling maintenance:", error)
      toast({
        title: "Error",
        description: "Failed to cancel maintenance record. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Scheduled":
        return "bg-blue-100 text-blue-800 hover:bg-blue-100 dark:bg-blue-800/20 dark:text-blue-400"
      case "In Progress":
        return "bg-amber-100 text-amber-800 hover:bg-amber-100 dark:bg-amber-800/20 dark:text-amber-400"
      case "Completed":
        return "bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-800/20 dark:text-green-400"
      case "Cancelled":
        return "bg-gray-100 text-gray-800 hover:bg-gray-100 dark:bg-gray-800/20 dark:text-gray-400"
      case "Urgent":
        return "bg-red-100 text-red-800 hover:bg-red-100 dark:bg-red-800/20 dark:text-red-400"
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100 dark:bg-gray-800/20 dark:text-gray-400"
    }
  }

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 hover:bg-red-100 dark:bg-red-800/20 dark:text-red-400"
      case "medium":
        return "bg-amber-100 text-amber-800 hover:bg-amber-100 dark:bg-amber-800/20 dark:text-amber-400"
      case "low":
        return "bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-800/20 dark:text-green-400"
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100 dark:bg-gray-800/20 dark:text-gray-400"
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-gray-900">Transformer Details</h1>
          <p className="text-gray-600">
            Serial Number: {transformer.serialNumber || 'Unknown'} | Manufacturer: {transformer.manufacturer || 'Unknown'}
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsReportIssueOpen(true)}>
            <AlertCircle className="mr-2 h-4 w-4" />
            Report Issue
          </Button>
          <Button variant="outline" size="sm" onClick={() => setIsScheduleMaintenanceOpen(true)}>
            <Calendar className="mr-2 h-4 w-4" />
            Schedule Maintenance
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="specifications">Specifications</TabsTrigger>
          <TabsTrigger value="location">Location</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance History</TabsTrigger>
          <TabsTrigger value="inspections">Inspection Records</TabsTrigger>
          <TabsTrigger value="tests">Test Results</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>
        <TabsContent value="specifications" className="space-y-4">
          <TransformerSpecifications transformer={transformer} />
        </TabsContent>
        <TabsContent value="location" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Location Information</CardTitle>
              <CardDescription>Geographic location and installation details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium mb-2">Installation Details</h3>
                  <div className="space-y-2">
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-muted-foreground">Region:</span>
                      <span className="col-span-2">{transformer.location?.region || "Addis Ababa"}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-muted-foreground">Service Center:</span>
                      <span className="col-span-2">{transformer.location?.serviceCenter || "Bole"}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-muted-foreground">Installation Date:</span>
                      <span className="col-span-2">{transformer.installationDate || "2020-05-15"}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-muted-foreground">Last Inspection:</span>
                      <span className="col-span-2">{transformer.lastInspectionDate || "2023-02-10"}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-muted-foreground">GPS Coordinates:</span>
                      <span className="col-span-2">
                        {transformer.location?.latitude || "9.0222"}°N, {transformer.location?.longitude || "38.7468"}°E
                      </span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Address</h3>
                  <div className="space-y-2">
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-muted-foreground">Street:</span>
                      <span className="col-span-2">{transformer.location?.street || "Bole Road"}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-muted-foreground">Sub-City:</span>
                      <span className="col-span-2">{transformer.location?.subCity || "Bole"}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-muted-foreground">City:</span>
                      <span className="col-span-2">{transformer.location?.city || "Addis Ababa"}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <span className="text-muted-foreground">Postal Code:</span>
                      <span className="col-span-2">{transformer.location?.postalCode || "1000"}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="h-[300px] mt-4 border rounded-md overflow-hidden">
                <TransformerLocationMap transformer={transformer} />
              </div>
              <div className="flex justify-between mt-3">
                <Button variant="outline" size="sm" onClick={() => setIsLocationMapViewOpen(true)}>
                  <Map className="mr-2 h-4 w-4" />
                  View Fullscreen Map
                </Button>
                <Button variant="outline" size="sm" onClick={handleEditLocation}>
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit Location
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="maintenance" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>Maintenance History</CardTitle>
                <CardDescription>Record of all maintenance activities</CardDescription>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => setIsMapViewOpen(true)}>
                  <Map className="mr-2 h-4 w-4" />
                  Map View
                </Button>
                <Button variant="outline" size="sm" onClick={() => setIsScheduleMaintenanceOpen(true)}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Schedule Maintenance
                </Button>
                <Button variant="outline" size="sm" onClick={() => router.push(`/maintenance?transformer=${transformer.id}`)}>
                  <Wrench className="mr-2 h-4 w-4" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {maintenanceRecords.length > 0 ? (
                  maintenanceRecords.map((record) => (
                    <div key={record.id} className="border rounded-md p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-medium">{record.type}</h3>
                          <p className="text-sm text-muted-foreground">
                            Scheduled: {format(new Date(record.scheduledDate), "PPP")}
                          </p>
                        </div>
                        <Badge className={getStatusColor(record.status)}>
                          {record.status}
                        </Badge>
                      </div>
                      <p className="text-sm mb-2">{record.description}</p>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Technician: {record.assignedTo}</span>
                        <Badge variant="outline" className={getPriorityColor(record.priority)}>
                          {record.priority} priority
                        </Badge>
                      </div>
                      <div className="flex justify-end mt-3">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <span className="sr-only">Actions</span>
                              <span>Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {record.status === "Scheduled" && (
                              <>
                                <DropdownMenuItem onClick={() => handleCompleteMaintenance(record.id)}>
                                  <ClipboardCheck className="mr-2 h-4 w-4" />
                                  Mark as Completed
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleCancelMaintenance(record.id)}>
                                  <AlertCircle className="mr-2 h-4 w-4" />
                                  Cancel Maintenance
                                </DropdownMenuItem>
                              </>
                            )}
                            {record.status === "In Progress" && (
                              <DropdownMenuItem onClick={() => handleCompleteMaintenance(record.id)}>
                                <ClipboardCheck className="mr-2 h-4 w-4" />
                                Mark as Completed
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={() => router.push(`/maintenance?transformer=${transformer.id}`)}>
                              <FileText className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Wrench className="mx-auto h-8 w-8 mb-2 opacity-50" />
                    <p>No maintenance records found</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={() => setIsScheduleMaintenanceOpen(true)}
                    >
                      <PlusCircle className="mr-2 h-4 w-4" />
                      Schedule Maintenance
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="inspections" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>Inspection Records</CardTitle>
                <CardDescription>History of inspections and findings</CardDescription>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => setIsAddInspectionOpen(true)}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Inspection
                </Button>
                <Button variant="outline" size="sm">
                  <FileText className="mr-2 h-4 w-4" />
                  Export Records
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {transformer.inspectionRecords?.map((record, index) => (
                  <div key={index} className="border rounded-md p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-medium">{record.type}</h3>
                        <p className="text-sm text-muted-foreground">{record.date}</p>
                      </div>
                      <div
                        className={`px-2 py-1 rounded-full text-xs ${
                          record.result === "Pass" || record.status === "Completed"
                            ? "bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400"
                            : "bg-amber-100 text-amber-800 dark:bg-amber-800/20 dark:text-amber-400"
                        }`}
                      >
                        {record.result || record.status}
                      </div>
                    </div>
                    <p className="text-sm mb-2">{record.findings}</p>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Inspector: {record.inspector}</span>
                      <span className="text-muted-foreground">Next Inspection: {record.nextInspectionDate}</span>
                    </div>
                    <div className="flex justify-end mt-3">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <span className="sr-only">Actions</span>
                            <span>Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Pencil className="mr-2 h-4 w-4" />
                            Edit Record
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <FileText className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Calendar className="mr-2 h-4 w-4" />
                            Schedule Follow-up
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                )) || (
                  <div className="text-center py-8 text-muted-foreground">
                    <ClipboardList className="mx-auto h-8 w-8 mb-2 opacity-50" />
                    <p>No inspection records found</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="tests" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>Test Results</CardTitle>
                <CardDescription>Results from various tests performed</CardDescription>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => setIsAddTestResultOpen(true)}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Test Result
                </Button>
                <Button variant="outline" size="sm">
                  <FileText className="mr-2 h-4 w-4" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {transformer.testResults?.map((test, index) => (
                  <div key={index} className="border rounded-md p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-medium">{test.type}</h3>
                        <p className="text-sm text-muted-foreground">{test.date}</p>
                      </div>
                      <div
                        className={`px-2 py-1 rounded-full text-xs ${
                          (test.status || test.result) === "Pass"
                            ? "bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400"
                            : (test.status || test.result) === "Fail"
                              ? "bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-400"
                              : "bg-amber-100 text-amber-800 dark:bg-amber-800/20 dark:text-amber-400"
                        }`}
                      >
                        {test.status || test.result}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-2">
                      {test.parameters && Object.entries(test.parameters).map(([key, value], idx) => (
                        <div key={idx} className="flex justify-between">
                          <span className="text-sm text-muted-foreground">{key}:</span>
                          <span className="text-sm font-medium">
                            {value}
                          </span>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Technician: {test.technician || test.performedBy}</span>
                      <span className="text-muted-foreground">Next Test: {test.nextTestDate}</span>
                    </div>
                    <div className="flex justify-end mt-3">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <span className="sr-only">Actions</span>
                            <span>Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Pencil className="mr-2 h-4 w-4" />
                            Edit Result
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <FileText className="mr-2 h-4 w-4" />
                            View Report
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <FileBarChart className="mr-2 h-4 w-4" />
                            Analyze Data
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                )) || (
                  <div className="text-center py-8 text-muted-foreground">
                    <ClipboardList className="mx-auto h-8 w-8 mb-2 opacity-50" />
                    <p>No test results found</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Historical performance data</CardDescription>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => setIsMonitorPerformanceOpen(true)}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Monitor Performance
                </Button>
                <Button variant="outline" size="sm">
                  <ChartBar className="mr-2 h-4 w-4" />
                  View Analytics
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Current Load</p>
                        <h3 className="text-2xl font-bold">78%</h3>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center dark:bg-green-800/20">
                        <ChartBar className="h-6 w-6 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">Updated 10 minutes ago</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Temperature</p>
                        <h3 className="text-2xl font-bold">52°C</h3>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center dark:bg-amber-800/20">
                        <Thermometer className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">Updated 10 minutes ago</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Efficiency</p>
                        <h3 className="text-2xl font-bold">97.8%</h3>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center dark:bg-blue-800/20">
                        <FileBarChart className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">Average over last 30 days</p>
                  </CardContent>
                </Card>
              </div>
              <div className="h-[300px]">
                <TransformerHistoryChart transformerId={transformer.id} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <PDFPreviewDialog
        open={isPdfPreviewOpen}
        onOpenChange={setIsPdfPreviewOpen}
        transformer={transformer}
      />

      <ReportIssueDialog
        open={isReportIssueOpen}
        onOpenChange={setIsReportIssueOpen}
        transformer={transformer}
        onIssueReported={handleRecordUpdate}
      />

      <ScheduleMaintenanceDialog
        open={isScheduleMaintenanceOpen}
        onOpenChange={setIsScheduleMaintenanceOpen}
        transformer={transformer}
        onScheduled={handleRecordUpdate}
        preselectedTransformerId={transformer.id}
      />

      <AddInspectionDialog
        open={isAddInspectionOpen}
        onOpenChange={setIsAddInspectionOpen}
        transformer={transformer}
        onInspectionAdded={handleRecordUpdate}
      />

      <AddTestResultDialog
        open={isAddTestResultOpen}
        onOpenChange={setIsAddTestResultOpen}
        transformer={transformer}
        onTestResultAdded={handleRecordUpdate}
      />

      <MonitorPerformanceDialog
        open={isMonitorPerformanceOpen}
        onOpenChange={setIsMonitorPerformanceOpen}
        transformer={transformer}
        onDataRecorded={handleRecordUpdate}
      />

      {/* Maintenance Map View Dialog */}
      <Dialog open={isMapViewOpen} onOpenChange={setIsMapViewOpen}>
        <DialogContent className="sm:max-w-[90vw] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Maintenance Map View</DialogTitle>
            <DialogDescription>
              View maintenance activities for this transformer on the map
            </DialogDescription>
          </DialogHeader>
          <div className="h-[70vh] w-full">
            <UnifiedMap
              locations={[mapService.transformerToMapLocation(transformer)]}
              height="70vh"
              clustered={false}
              showControls={true}
              showLegend={true}
              interactive={true}
              allowFullscreen={true}
              initialZoom={12}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Location Map View Dialog */}
      <Dialog open={isLocationMapViewOpen} onOpenChange={setIsLocationMapViewOpen}>
        <DialogContent className="sm:max-w-[90vw] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Transformer Location</DialogTitle>
            <DialogDescription>
              Interactive map showing the transformer's location and nearby infrastructure
            </DialogDescription>
          </DialogHeader>
          <div className="h-[70vh] w-full">
            <UnifiedMap
              locations={[mapService.transformerToMapLocation(transformer)]}
              height="70vh"
              clustered={false}
              showControls={true}
              showLegend={true}
              showFilters={false}
              interactive={true}
              allowFullscreen={true}
              initialZoom={16}
              mapStyle="streets"
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
