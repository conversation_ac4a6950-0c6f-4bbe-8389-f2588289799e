import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs/promises'
import path from 'path'

/**
 * API Route to Seed Realistic Data to JSON Database
 * 
 * This endpoint seeds comprehensive realistic data for all modules and features
 * to the JSON database, which can then be migrated to MySQL.
 */

// Ethiopian regions and major cities
const ethiopianRegions = [
  { name: 'Addis Ababa', code: 'AA', population: 3500000, coordinates: { lat: 9.0320, lng: 38.7469 } },
  { name: 'Oromia', code: 'OR', population: 37000000, coordinates: { lat: 8.5000, lng: 39.5000 } },
  { name: 'Amhara', code: 'AM', population: 21000000, coordinates: { lat: 11.5000, lng: 37.5000 } },
  { name: 'Tigray', code: 'TI', population: 5500000, coordinates: { lat: 14.0000, lng: 38.5000 } },
  { name: 'SNNP', code: 'SN', population: 20000000, coordinates: { lat: 6.5000, lng: 37.0000 } },
  { name: 'Somali', code: 'S<PERSON>', population: 5500000, coordinates: { lat: 6.0000, lng: 43.0000 } },
  { name: '<PERSON><PERSON>', code: 'AF', population: 1800000, coordinates: { lat: 11.5000, lng: 41.0000 } }
]

// Ethiopian cities
const ethiopianCities = [
  'Addis Ababa', 'Dire Dawa', 'Mekelle', 'Gondar', 'Dessie', 'Jimma', 'Jijiga', 'Shashamane',
  'Bahir Dar', 'Hawassa', 'Adama', 'Debre Markos', 'Harar', 'Dilla', 'Nekemte', 'Debre Birhan'
]

// Ethiopian names
const ethiopianNames = {
  first: ['Abebe', 'Almaz', 'Bekele', 'Chaltu', 'Dawit', 'Emebet', 'Fikru', 'Genet', 'Haile', 'Iyasu', 'Kassa', 'Lemlem', 'Meron', 'Negash', 'Rahel', 'Selamawit', 'Tadesse', 'Tigist', 'Worku', 'Yohannes'],
  last: ['Abera', 'Bekele', 'Chala', 'Desta', 'Eshetu', 'Fanta', 'Girma', 'Hailu', 'Kebede', 'Lemma', 'Mekonnen', 'Negash', 'Regassa', 'Sisay', 'Tadesse', 'Wolde', 'Yimer', 'Zenebe']
}

// Transformer data
const transformerData = {
  manufacturers: ['ABB', 'Siemens', 'Schneider Electric', 'General Electric', 'Hyundai Heavy Industries', 'TBEA', 'Crompton Greaves'],
  types: ['Distribution', 'Power', 'Instrument', 'Auto', 'Isolation', 'Rectifier'],
  voltageRatings: [
    { primary: 15000, secondary: 400 },
    { primary: 33000, secondary: 11000 },
    { primary: 66000, secondary: 15000 },
    { primary: 132000, secondary: 33000 },
    { primary: 230000, secondary: 66000 },
    { primary: 400000, secondary: 132000 }
  ],
  capacities: [50, 100, 160, 250, 315, 400, 500, 630, 800, 1000, 1250, 1600, 2000, 2500, 3150, 4000, 5000]
}

// Maintenance types
const maintenanceTypes = [
  'Preventive Maintenance', 'Corrective Maintenance', 'Predictive Maintenance', 'Emergency Repair',
  'Routine Inspection', 'Oil Analysis', 'Bushing Replacement', 'Tap Changer Service',
  'Cooling System Maintenance', 'Protection System Testing', 'Grounding System Check', 'Insulation Testing'
]

// Alert types
const alertTypes = [
  { type: 'Temperature', severity: 'high', description: 'Transformer temperature exceeding normal limits' },
  { type: 'Oil Level', severity: 'medium', description: 'Oil level below recommended threshold' },
  { type: 'Load', severity: 'high', description: 'Transformer overload condition detected' },
  { type: 'Vibration', severity: 'medium', description: 'Abnormal vibration patterns detected' },
  { type: 'Insulation', severity: 'critical', description: 'Insulation resistance below safety limits' },
  { type: 'Protection', severity: 'critical', description: 'Protection system malfunction' },
  { type: 'Communication', severity: 'low', description: 'Communication link interruption' },
  { type: 'Power Quality', severity: 'medium', description: 'Power quality parameters out of range' }
]

// Helper functions
function generateEthiopianPhone() {
  const prefixes = ['091', '092', '093', '094', '097', '098']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const number = Math.floor(Math.random() * 10000000).toString().padStart(7, '0')
  return `+251-${prefix}-${number}`
}

function generateEthiopianName() {
  const firstName = ethiopianNames.first[Math.floor(Math.random() * ethiopianNames.first.length)]
  const lastName = ethiopianNames.last[Math.floor(Math.random() * ethiopianNames.last.length)]
  return `${firstName} ${lastName}`
}

function generateEthiopianCoordinates() {
  const lat = 3.0 + Math.random() * 12.0
  const lng = 33.0 + Math.random() * 15.0
  return { lat: parseFloat(lat.toFixed(6)), lng: parseFloat(lng.toFixed(6)) }
}

function generateTransformerSerial(manufacturer: string, year: number) {
  const prefix = manufacturer.substring(0, 3).toUpperCase()
  const yearCode = year.toString().substring(2)
  const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0')
  return `${prefix}-${yearCode}-${sequence}`
}

function generateMaintenanceCost(type: string) {
  const baseCosts: { [key: string]: number } = {
    'Preventive Maintenance': 5000,
    'Corrective Maintenance': 15000,
    'Predictive Maintenance': 8000,
    'Emergency Repair': 25000,
    'Routine Inspection': 2000,
    'Oil Analysis': 3000,
    'Bushing Replacement': 20000,
    'Tap Changer Service': 12000,
    'Cooling System Maintenance': 8000,
    'Protection System Testing': 6000,
    'Grounding System Check': 4000,
    'Insulation Testing': 5000
  }
  
  const baseCost = baseCosts[type] || 5000
  const variation = 0.3 // ±30% variation
  const multiplier = 1 + (Math.random() - 0.5) * 2 * variation
  return Math.round(baseCost * multiplier)
}

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Starting realistic data seeding to JSON database...')
    
    const dataDir = path.join(process.cwd(), 'data')
    
    // Ensure data directory exists
    try {
      await fs.access(dataDir)
    } catch {
      await fs.mkdir(dataDir, { recursive: true })
    }
    
    // Generate realistic data
    const realisticData: any = {}
    
    // Generate regions
    console.log('🌍 Generating Ethiopian regions...')
    realisticData.regions = ethiopianRegions.map(region => ({
      id: `region-${region.code.toLowerCase()}`,
      name: region.name,
      code: region.code,
      country: 'Ethiopia',
      population: region.population,
      area_km2: Math.floor(Math.random() * 100000) + 10000,
      coordinates: region.coordinates,
      timezone: 'Africa/Addis_Ababa',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }))
    
    // Generate service centers
    console.log('🏢 Generating service centers...')
    realisticData.serviceCenters = []
    for (let i = 0; i < 15; i++) {
      const region = ethiopianRegions[Math.floor(Math.random() * ethiopianRegions.length)]
      const city = ethiopianCities[Math.floor(Math.random() * ethiopianCities.length)]
      const coordinates = generateEthiopianCoordinates()
      
      realisticData.serviceCenters.push({
        id: `sc-${String(i + 1).padStart(3, '0')}`,
        name: `${city} Service Center`,
        region_id: `region-${region.code.toLowerCase()}`,
        address: `${city}, ${region.name}, Ethiopia`,
        coordinates: coordinates,
        phone: generateEthiopianPhone(),
        email: `${city.toLowerCase().replace(/\s+/g, '')}@eeu.gov.et`,
        manager_name: generateEthiopianName(),
        operating_hours: '08:00-17:00',
        facilities: ['Workshop', 'Storage', 'Testing Lab', 'Vehicle Fleet'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    }
    
    // Generate transformers
    console.log('⚡ Generating realistic transformers...')
    realisticData.transformers = []
    for (let i = 0; i < 100; i++) {
      const manufacturer = transformerData.manufacturers[Math.floor(Math.random() * transformerData.manufacturers.length)]
      const type = transformerData.types[Math.floor(Math.random() * transformerData.types.length)]
      const voltage = transformerData.voltageRatings[Math.floor(Math.random() * transformerData.voltageRatings.length)]
      const capacity = transformerData.capacities[Math.floor(Math.random() * transformerData.capacities.length)]
      const installationYear = 2010 + Math.floor(Math.random() * 14)
      const serviceCenter = realisticData.serviceCenters[Math.floor(Math.random() * realisticData.serviceCenters.length)]
      const coordinates = generateEthiopianCoordinates()
      
      const status = ['operational', 'warning', 'maintenance', 'critical', 'offline'][Math.floor(Math.random() * 5)]
      const healthIndex = status === 'operational' ? 80 + Math.random() * 20 :
                         status === 'warning' ? 60 + Math.random() * 20 :
                         status === 'maintenance' ? 70 + Math.random() * 15 :
                         status === 'critical' ? 20 + Math.random() * 40 :
                         10 + Math.random() * 30
      
      realisticData.transformers.push({
        id: `transformer-${String(i + 1).padStart(3, '0')}`,
        serial_number: generateTransformerSerial(manufacturer, installationYear),
        name: `${serviceCenter.name.split(' ')[0]} Transformer ${i + 1}`,
        manufacturer: manufacturer,
        model: `${manufacturer.substring(0, 3).toUpperCase()}-${capacity}${type.substring(0, 1)}`,
        type: type,
        capacity: capacity,
        primary_voltage: voltage.primary,
        secondary_voltage: voltage.secondary,
        status: status,
        health_index: parseFloat(healthIndex.toFixed(1)),
        installation_date: `${installationYear}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        last_maintenance_date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        next_maintenance_date: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        service_center_id: serviceCenter.id,
        region_id: serviceCenter.region_id,
        location: {
          address: `${serviceCenter.address} Substation`,
          coordinates: coordinates,
          substation_name: `${serviceCenter.name.split(' ')[0]} Substation`
        },
        specifications: {
          cooling_type: ['ONAN', 'ONAF', 'OFAF', 'ODAF'][Math.floor(Math.random() * 4)],
          tap_changer: Math.random() > 0.3,
          protection_class: ['IP23', 'IP44', 'IP54'][Math.floor(Math.random() * 3)],
          frequency: 50,
          phases: 3,
          connection_type: ['Dyn11', 'Yyn0', 'Dyn1'][Math.floor(Math.random() * 3)]
        },
        current_metrics: {
          temperature: 35 + Math.random() * 30,
          load_percentage: Math.random() * 100,
          oil_level: 80 + Math.random() * 20,
          vibration: Math.random() * 5,
          power_factor: 0.85 + Math.random() * 0.1,
          efficiency: 95 + Math.random() * 4
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    }
    
    // Generate maintenance schedules
    console.log('🔧 Generating maintenance schedules...')
    realisticData.maintenanceSchedules = []
    for (let i = 0; i < 150; i++) {
      const transformer = realisticData.transformers[Math.floor(Math.random() * realisticData.transformers.length)]
      const type = maintenanceTypes[Math.floor(Math.random() * maintenanceTypes.length)]
      const priority = ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)]
      const status = ['scheduled', 'in_progress', 'completed', 'cancelled', 'overdue'][Math.floor(Math.random() * 5)]
      const technicianName = generateEthiopianName()
      const supervisorName = generateEthiopianName()
      
      const scheduledDate = new Date(Date.now() + (Math.random() - 0.5) * 180 * 24 * 60 * 60 * 1000)
      const estimatedDuration = Math.floor(Math.random() * 8) + 1
      const cost = generateMaintenanceCost(type)
      
      realisticData.maintenanceSchedules.push({
        id: `maint-${String(i + 1).padStart(3, '0')}`,
        title: `${type} - ${transformer.name}`,
        description: `${type} for transformer ${transformer.serial_number} at ${transformer.location.address}`,
        type: type,
        status: status,
        priority: priority,
        scheduled_date: scheduledDate.toISOString().split('T')[0],
        scheduled_time: `${String(Math.floor(Math.random() * 16) + 6).padStart(2, '0')}:00`,
        estimated_duration: estimatedDuration,
        transformer_id: transformer.id,
        transformer_serial: transformer.serial_number,
        transformer_name: transformer.name,
        technician_name: technicianName,
        supervisor_name: supervisorName,
        cost: cost,
        completion_percentage: status === 'completed' ? 100 : 
                             status === 'in_progress' ? Math.floor(Math.random() * 80) + 10 : 0,
        work_instructions: `Detailed work instructions for ${type.toLowerCase()} on ${transformer.type} transformer`,
        safety_requirements: 'Standard electrical safety procedures, PPE required, lockout/tagout procedures',
        required_parts: ['Oil', 'Gaskets', 'Filters', 'Testing Equipment'].slice(0, Math.floor(Math.random() * 4) + 1),
        notes: `Maintenance notes for ${transformer.serial_number}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    }
    
    // Generate alerts
    console.log('🚨 Generating realistic alerts...')
    realisticData.alerts = []
    for (let i = 0; i < 75; i++) {
      const transformer = realisticData.transformers[Math.floor(Math.random() * realisticData.transformers.length)]
      const alertType = alertTypes[Math.floor(Math.random() * alertTypes.length)]
      const status = ['active', 'acknowledged', 'resolved', 'escalated'][Math.floor(Math.random() * 4)]
      const createdDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      
      realisticData.alerts.push({
        id: `alert-${String(i + 1).padStart(3, '0')}`,
        title: `${alertType.type} Alert - ${transformer.name}`,
        message: alertType.description,
        type: alertType.type.toLowerCase().replace(' ', '_'),
        severity: alertType.severity,
        status: status,
        transformer_id: transformer.id,
        transformer_serial: transformer.serial_number,
        transformer_name: transformer.name,
        region_name: transformer.region_id.replace('region-', '').toUpperCase(),
        location: transformer.location.address,
        acknowledged_by: status !== 'active' ? generateEthiopianName() : null,
        acknowledged_at: status !== 'active' ? new Date(createdDate.getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString() : null,
        resolved_at: status === 'resolved' ? new Date(createdDate.getTime() + Math.random() * 48 * 60 * 60 * 1000).toISOString() : null,
        escalation_level: alertType.severity === 'critical' ? Math.floor(Math.random() * 3) + 1 : 0,
        affected_customers: Math.floor(Math.random() * 1000),
        estimated_restore_time: status === 'active' ? new Date(Date.now() + Math.random() * 12 * 60 * 60 * 1000).toISOString() : null,
        root_cause: status === 'resolved' ? `Root cause analysis for ${alertType.type.toLowerCase()} issue` : null,
        corrective_actions: status === 'resolved' ? `Corrective actions taken for ${alertType.type.toLowerCase()}` : null,
        created_at: createdDate.toISOString(),
        updated_at: new Date().toISOString()
      })
    }
    
    // Write data to JSON files
    console.log('💾 Writing realistic data to JSON files...')
    
    await fs.writeFile(
      path.join(dataDir, 'realistic-data.json'),
      JSON.stringify(realisticData, null, 2)
    )
    
    // Also update the existing database files
    await fs.writeFile(
      path.join(dataDir, 'transformers.json'),
      JSON.stringify(realisticData.transformers, null, 2)
    )
    
    await fs.writeFile(
      path.join(dataDir, 'maintenance.json'),
      JSON.stringify(realisticData.maintenanceSchedules, null, 2)
    )
    
    await fs.writeFile(
      path.join(dataDir, 'alerts.json'),
      JSON.stringify(realisticData.alerts, null, 2)
    )
    
    console.log('✅ Realistic data seeding completed successfully!')
    
    return NextResponse.json({
      success: true,
      message: 'Realistic Ethiopian data seeded successfully to JSON database',
      data: {
        regions: realisticData.regions.length,
        serviceCenters: realisticData.serviceCenters.length,
        transformers: realisticData.transformers.length,
        maintenanceSchedules: realisticData.maintenanceSchedules.length,
        alerts: realisticData.alerts.length
      }
    })
    
  } catch (error) {
    console.error('❌ Error seeding realistic data:', error)
    return NextResponse.json(
      { error: 'Failed to seed realistic data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
