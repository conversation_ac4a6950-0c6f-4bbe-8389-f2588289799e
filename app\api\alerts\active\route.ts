import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const severity = searchParams.get('severity') || ''
    const category = searchParams.get('category') || ''
    const region = searchParams.get('region') || ''
    const search = searchParams.get('search') || ''
    const offset = (page - 1) * limit

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    // Build WHERE clause
    let whereConditions = ['a.status = "active"']
    let queryParams = []

    if (severity) {
      whereConditions.push('a.severity = ?')
      queryParams.push(severity)
    }

    if (category) {
      whereConditions.push('a.category = ?')
      queryParams.push(category)
    }

    if (region) {
      whereConditions.push('t.region_id = ?')
      queryParams.push(region)
    }

    if (search) {
      whereConditions.push('(a.title LIKE ? OR a.description LIKE ? OR t.name LIKE ?)')
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`)
    }

    const whereClause = 'WHERE ' + whereConditions.join(' AND ')

    // Get active alerts
    const alerts = await executeQuery(`
      SELECT 
        a.*,
        t.name as transformer_name,
        t.serial_number,
        t.location as transformer_location,
        t.status as transformer_status,
        r.name as region_name,
        sc.name as service_center_name,
        u.first_name as assigned_first_name,
        u.last_name as assigned_last_name,
        TIMESTAMPDIFF(MINUTE, a.created_at, NOW()) as minutes_since_created,
        CASE 
          WHEN a.severity = 'critical' AND TIMESTAMPDIFF(HOUR, a.created_at, NOW()) > 1 THEN 'overdue'
          WHEN a.severity = 'high' AND TIMESTAMPDIFF(HOUR, a.created_at, NOW()) > 4 THEN 'overdue'
          WHEN a.severity = 'medium' AND TIMESTAMPDIFF(HOUR, a.created_at, NOW()) > 24 THEN 'overdue'
          ELSE 'active'
        END as response_status,
        wo.id as work_order_id,
        wo.status as work_order_status
      FROM app_alerts a
      LEFT JOIN app_transformers t ON a.transformer_id = t.id
      LEFT JOIN app_regions r ON t.region_id = r.id
      LEFT JOIN app_service_centers sc ON t.service_center_id = sc.id
      LEFT JOIN app_users u ON a.assigned_to = u.id
      LEFT JOIN app_work_orders wo ON a.work_order_id = wo.id
      ${whereClause}
      ORDER BY 
        CASE a.severity 
          WHEN 'critical' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'medium' THEN 3 
          WHEN 'low' THEN 4 
        END,
        a.created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset])

    // Get total count
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total
      FROM app_alerts a
      LEFT JOIN app_transformers t ON a.transformer_id = t.id
      ${whereClause}
    `, queryParams)

    const total = totalResult[0]?.total || 0

    // Get summary statistics
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_active,
        SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as critical_count,
        SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as high_count,
        SUM(CASE WHEN severity = 'medium' THEN 1 ELSE 0 END) as medium_count,
        SUM(CASE WHEN severity = 'low' THEN 1 ELSE 0 END) as low_count,
        SUM(CASE WHEN assigned_to IS NOT NULL THEN 1 ELSE 0 END) as assigned_count,
        SUM(CASE WHEN work_order_id IS NOT NULL THEN 1 ELSE 0 END) as with_work_orders,
        AVG(TIMESTAMPDIFF(MINUTE, created_at, NOW())) as avg_age_minutes
      FROM app_alerts a
      LEFT JOIN app_transformers t ON a.transformer_id = t.id
      ${whereClause}
    `, queryParams)

    // Get recent critical alerts (last 24 hours)
    const criticalAlerts = await executeQuery(`
      SELECT 
        a.id,
        a.title,
        a.severity,
        a.created_at,
        t.name as transformer_name,
        t.location,
        TIMESTAMPDIFF(MINUTE, a.created_at, NOW()) as minutes_ago
      FROM app_alerts a
      LEFT JOIN app_transformers t ON a.transformer_id = t.id
      WHERE a.status = 'active' 
        AND a.severity = 'critical'
        AND a.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      ORDER BY a.created_at DESC
      LIMIT 10
    `)

    // Get alert trends (last 7 days)
    const alertTrends = await executeQuery(`
      SELECT 
        DATE(a.created_at) as date,
        COUNT(*) as total_alerts,
        SUM(CASE WHEN a.severity = 'critical' THEN 1 ELSE 0 END) as critical_alerts,
        SUM(CASE WHEN a.severity = 'high' THEN 1 ELSE 0 END) as high_alerts
      FROM app_alerts a
      WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(a.created_at)
      ORDER BY date DESC
    `)

    // Get top alert categories
    const topCategories = await executeQuery(`
      SELECT 
        a.category,
        COUNT(*) as count,
        AVG(CASE WHEN a.severity = 'critical' THEN 4 
                 WHEN a.severity = 'high' THEN 3 
                 WHEN a.severity = 'medium' THEN 2 
                 ELSE 1 END) as avg_severity_score
      FROM app_alerts a
      WHERE a.status = 'active'
      GROUP BY a.category
      ORDER BY count DESC
      LIMIT 5
    `)

    return NextResponse.json({
      success: true,
      data: {
        alerts: alerts.map((alert: any) => ({
          ...alert,
          assigned_name: alert.assigned_first_name && alert.assigned_last_name ? 
            `${alert.assigned_first_name} ${alert.assigned_last_name}` : null,
          age_display: alert.minutes_since_created < 60 ? 
            `${alert.minutes_since_created}m ago` : 
            `${Math.floor(alert.minutes_since_created / 60)}h ago`,
          has_work_order: !!alert.work_order_id
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        statistics: {
          ...stats[0],
          avg_age_hours: Math.round((stats[0]?.avg_age_minutes || 0) / 60),
          unassigned_count: (stats[0]?.total_active || 0) - (stats[0]?.assigned_count || 0)
        },
        criticalAlerts,
        trends: alertTrends,
        topCategories
      }
    })

  } catch (error) {
    console.error('❌ Error fetching active alerts:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch active alerts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, alertId, data } = body

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    switch (action) {
      case 'assign_alert':
        await executeQuery(`
          UPDATE app_alerts 
          SET assigned_to = ?, updated_at = NOW()
          WHERE id = ?
        `, [data.userId, alertId])
        break

      case 'update_priority':
        await executeQuery(`
          UPDATE app_alerts 
          SET severity = ?, updated_at = NOW()
          WHERE id = ?
        `, [data.severity, alertId])
        break

      case 'add_comment':
        await executeQuery(`
          INSERT INTO app_alert_comments 
          (alert_id, user_id, comment, created_at)
          VALUES (?, ?, ?, NOW())
        `, [alertId, data.userId, data.comment])
        break

      case 'create_work_order':
        // Create work order from alert
        const workOrderId = await executeQuery(`
          INSERT INTO app_work_orders 
          (title, description, transformer_id, work_order_type, priority, due_date, 
           created_by, status, created_at)
          VALUES (?, ?, ?, 'corrective', ?, DATE_ADD(CURDATE(), INTERVAL 3 DAY), ?, 'open', NOW())
        `, [
          `Alert Response: ${data.title}`,
          data.description,
          data.transformerId,
          data.severity === 'critical' ? 'critical' : 'high',
          data.createdBy
        ])

        // Link work order to alert
        await executeQuery(`
          UPDATE app_alerts 
          SET work_order_id = ?, updated_at = NOW()
          WHERE id = ?
        `, [workOrderId.insertId, alertId])

        return NextResponse.json({
          success: true,
          workOrderId: workOrderId.insertId,
          message: 'Work order created successfully'
        })

      case 'acknowledge':
        await executeQuery(`
          UPDATE app_alerts 
          SET acknowledged = 1, acknowledged_by = ?, acknowledged_at = NOW(), updated_at = NOW()
          WHERE id = ?
        `, [data.userId, alertId])
        break

      case 'escalate':
        await executeQuery(`
          UPDATE app_alerts 
          SET severity = CASE 
            WHEN severity = 'low' THEN 'medium'
            WHEN severity = 'medium' THEN 'high'
            WHEN severity = 'high' THEN 'critical'
            ELSE severity
          END,
          escalated = 1,
          escalated_by = ?,
          escalated_at = NOW(),
          updated_at = NOW()
          WHERE id = ?
        `, [data.userId, alertId])
        break

      case 'resolve':
        await executeQuery(`
          UPDATE app_alerts 
          SET status = 'resolved', 
              resolved_by = ?, 
              resolved_at = NOW(), 
              resolution_notes = ?,
              updated_at = NOW()
          WHERE id = ?
        `, [data.userId, data.resolutionNotes, alertId])
        break

      case 'bulk_assign':
        const { alertIds, userId } = data
        await executeQuery(`
          UPDATE app_alerts 
          SET assigned_to = ?, updated_at = NOW()
          WHERE id IN (${alertIds.map(() => '?').join(',')})
        `, [userId, ...alertIds])
        break

      case 'bulk_acknowledge':
        const { alertIds: ackAlertIds, userId: ackUserId } = data
        await executeQuery(`
          UPDATE app_alerts 
          SET acknowledged = 1, acknowledged_by = ?, acknowledged_at = NOW(), updated_at = NOW()
          WHERE id IN (${ackAlertIds.map(() => '?').join(',')})
        `, [ackUserId, ...ackAlertIds])
        break

      case 'auto_resolve_similar':
        // Auto-resolve similar alerts
        const alertData = await executeQuery(`
          SELECT category, transformer_id FROM app_alerts WHERE id = ?
        `, [alertId])

        if (alertData.length > 0) {
          const { category, transformer_id } = alertData[0]
          await executeQuery(`
            UPDATE app_alerts 
            SET status = 'resolved', 
                resolved_by = ?, 
                resolved_at = NOW(), 
                resolution_notes = 'Auto-resolved: Similar to resolved alert',
                updated_at = NOW()
            WHERE category = ? 
              AND transformer_id = ? 
              AND status = 'active'
              AND id != ?
          `, [data.userId, category, transformer_id, alertId])
        }
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: 'Action completed successfully'
    })

  } catch (error) {
    console.error('❌ Error processing active alert action:', error)
    return NextResponse.json(
      {
        error: 'Failed to process action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
