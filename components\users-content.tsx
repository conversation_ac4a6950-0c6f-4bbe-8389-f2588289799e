"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { But<PERSON> } from "@/src/components/ui/button"
import {
  Search, Filter, Plus, Download, Upload, X, UserCheck,
  UserX, RefreshCw, Users, Shield, Wrench, Activity,
  BadgeCheck, BadgeX
} from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { UsersTable } from "@/components/users-table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Badge } from "@/src/components/ui/badge"
import { useToast } from "@/src/components/ui/use-toast"
import { EnhancedUserDialog } from "@/components/enhanced-user-dialog"
import { UserImportExportDialog } from "@/components/user-import-export-dialog"
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/src/components/ui/tooltip"

export function UsersContent() {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("all")
  const [showSearchClear, setShowSearchClear] = useState(false)
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // User counts for badges
  const [userCounts, setUserCounts] = useState({
    all: 0,
    administrators: 0,
    technicians: 0,
    operators: 0,
    active: 0,
    inactive: 0
  })

  // Simulate fetching user counts
  useEffect(() => {
    // In a real app, this would be an API call
    setUserCounts({
      all: 5,
      administrators: 2,
      technicians: 2,
      operators: 1,
      active: 4,
      inactive: 1
    })
  }, [refreshTrigger])

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
    setShowSearchClear(e.target.value.length > 0)
  }

  const handleClearSearch = () => {
    setSearchQuery("")
    setShowSearchClear(false)
  }

  const handleAddUser = (userData: any) => {
    toast({
      title: "User Created",
      description: `${userData.name} has been successfully added.`,
    })

    // Refresh the user list
    setRefreshTrigger(prev => prev + 1)
  }

  const handleImportComplete = () => {
    toast({
      title: "Import Complete",
      description: "Users have been successfully imported.",
    })

    // Refresh the user list
    setRefreshTrigger(prev => prev + 1)
  }

  const handleExportComplete = () => {
    // Nothing to do here as the toast is shown in the dialog
    // Just for consistency
  }

  const handleRefresh = () => {
    toast({
      title: "Refreshing",
      description: "Updating user list...",
    })

    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">User Management</h1>
          <p className="text-sm text-muted-foreground">
            Manage system users, roles, and permissions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={() => setIsImportDialogOpen(true)}>
                  <Upload className="mr-2 h-4 w-4" />
                  Import
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Import users from CSV or Excel</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={() => setIsExportDialogOpen(true)}>
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export users to file</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleRefresh}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Refresh user list</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button size="sm" onClick={() => setIsAddUserDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add User
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Create a new user</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="flex">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            All Users
            <Badge variant="outline" className="ml-1 px-1 py-0 text-xs">
              {userCounts.all}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="admins" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Administrators
            <Badge variant="outline" className="ml-1 px-1 py-0 text-xs">
              {userCounts.administrators}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="technicians" className="flex items-center gap-2">
            <Wrench className="h-4 w-4" />
            Technicians
            <Badge variant="outline" className="ml-1 px-1 py-0 text-xs">
              {userCounts.technicians}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="operators" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Operators
            <Badge variant="outline" className="ml-1 px-1 py-0 text-xs">
              {userCounts.operators}
            </Badge>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Users</CardTitle>
                  <CardDescription>Manage system users and their permissions</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <BadgeCheck className="h-3 w-3 text-green-500" />
                    <span>Active: {userCounts.active}</span>
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <BadgeX className="h-3 w-3 text-slate-500" />
                    <span>Inactive: {userCounts.inactive}</span>
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search users..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={handleSearchChange}
                  />
                  {showSearchClear && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full rounded-l-none"
                      onClick={handleClearSearch}
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">Clear search</span>
                    </Button>
                  )}
                </div>
                <div className="flex gap-2">
                  <Select value={roleFilter} onValueChange={setRoleFilter}>
                    <SelectTrigger className="w-[160px]">
                      <Shield className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Roles</SelectItem>
                      <SelectItem value="admin">Administrator</SelectItem>
                      <SelectItem value="technician">Technician</SelectItem>
                      <SelectItem value="operator">Operator</SelectItem>
                      <SelectItem value="viewer">Viewer</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">
                        <div className="flex items-center">
                          All Status
                        </div>
                      </SelectItem>
                      <SelectItem value="active">
                        <div className="flex items-center">
                          <UserCheck className="mr-2 h-4 w-4 text-green-500" />
                          Active
                        </div>
                      </SelectItem>
                      <SelectItem value="inactive">
                        <div className="flex items-center">
                          <UserX className="mr-2 h-4 w-4 text-slate-500" />
                          Inactive
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <UsersTable onUserUpdated={() => setRefreshTrigger(prev => prev + 1)} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="admins" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Administrators</CardTitle>
              <CardDescription>Users with administrative privileges</CardDescription>
            </CardHeader>
            <CardContent>
              <UsersTable
                role="Administrator"
                onUserUpdated={() => setRefreshTrigger(prev => prev + 1)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="technicians" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Technicians</CardTitle>
              <CardDescription>Field technicians responsible for maintenance</CardDescription>
            </CardHeader>
            <CardContent>
              <UsersTable
                role="Technician"
                onUserUpdated={() => setRefreshTrigger(prev => prev + 1)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="operators" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Operators</CardTitle>
              <CardDescription>System operators with monitoring capabilities</CardDescription>
            </CardHeader>
            <CardContent>
              <UsersTable
                role="Operator"
                onUserUpdated={() => setRefreshTrigger(prev => prev + 1)}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add User Dialog */}
      <EnhancedUserDialog
        open={isAddUserDialogOpen}
        onOpenChange={setIsAddUserDialogOpen}
        onSave={handleAddUser}
      />

      {/* Import Dialog */}
      <UserImportExportDialog
        open={isImportDialogOpen}
        onOpenChange={setIsImportDialogOpen}
        mode="import"
        onComplete={handleImportComplete}
      />

      {/* Export Dialog */}
      <UserImportExportDialog
        open={isExportDialogOpen}
        onOpenChange={setIsExportDialogOpen}
        mode="export"
        onComplete={handleExportComplete}
      />
    </div>
  )
}
