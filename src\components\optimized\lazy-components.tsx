/**
 * Lazy-loaded Components for Performance Optimization
 * Provides lazy loading with loading states and error boundaries
 */

import { lazy, Suspense, ComponentType } from 'react'
import { RefreshCw, AlertTriangle } from 'lucide-react'
import { Button } from '@/src/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'

// Loading component
export function ComponentLoader({ message = "Loading..." }: { message?: string }) {
  return (
    <div className="flex items-center justify-center h-64 w-full">
      <div className="flex flex-col items-center gap-3">
        <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  )
}

// Error fallback component
export function ComponentError({
  error,
  retry,
  componentName = "Component"
}: {
  error: Error
  retry: () => void
  componentName?: string
}) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-destructive">
          <AlertTriangle className="h-5 w-5" />
          Failed to Load {componentName}
        </CardTitle>
        <CardDescription>
          An error occurred while loading this component. Please try again.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
          <strong>Error:</strong> {error.message}
        </div>
        <Button onClick={retry} variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Retry
        </Button>
      </CardContent>
    </Card>
  )
}

// Higher-order component for lazy loading with error boundary
export function withLazyLoading<P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  componentName: string,
  loadingMessage?: string
) {
  const LazyComponent = lazy(importFn)

  return function LazyLoadedComponent(props: P) {
    return (
      <Suspense fallback={<ComponentLoader message={loadingMessage || `Loading ${componentName}...`} />}>
        <LazyComponent {...props} />
      </Suspense>
    )
  }
}

// Lazy-loaded Dashboard Analytics
export const LazyDashboardAnalytics = withLazyLoading(
  () => import('@/components/dashboard-analytics'),
  'Dashboard Analytics',
  'Loading analytics dashboard...'
)

// Lazy-loaded Transformer Unified Management
export const LazyTransformerUnifiedManagement = withLazyLoading(
  () => import('@/components/transformer-unified-management'),
  'Transformer Management',
  'Loading transformer management...'
)

// Lazy-loaded Maintenance Scheduled Tasks
export const LazyMaintenanceScheduledTasks = withLazyLoading(
  () => import('@/components/maintenance-scheduled-tasks'),
  'Maintenance Tasks',
  'Loading maintenance tasks...'
)

// Lazy-loaded Smart Meters Content
export const LazySmartMetersContent = withLazyLoading(
  () => import('@/components/smart-meters-content'),
  'Smart Meters',
  'Loading smart meters monitoring...'
)

// Lazy-loaded Alerts Content
export const LazyAlertsContent = withLazyLoading(
  () => import('@/components/alerts-content'),
  'Active Alerts',
  'Loading active alerts...'
)

// Lazy-loaded Reports Content
export const LazyReportsContent = withLazyLoading(
  () => import('@/components/reports-content'),
  'Reports',
  'Loading reports...'
)

// Lazy-loaded Settings Content
export const LazySettingsContent = withLazyLoading(
  () => import('@/components/settings-content'),
  'Settings',
  'Loading settings...'
)

// Lazy-loaded Performance Dashboard
export const LazyPerformanceDashboard = withLazyLoading(
  () => import('@/components/performance/performance-dashboard'),
  'Performance Dashboard',
  'Loading performance dashboard...'
)

// Lazy-loaded Weather Content
export const LazyWeatherContent = withLazyLoading(
  () => import('@/components/weather-content'),
  'Weather Integration',
  'Loading weather data...'
)

// Progressive loading component for heavy components
export function ProgressiveLoader({
  children,
  delay = 100,
  className = ""
}: {
  children: React.ReactNode
  delay?: number
  className?: string
}) {
  return (
    <div
      className={`transition-opacity duration-300 ${className}`}
      style={{
        animation: `fadeIn 0.3s ease-in-out ${delay}ms both`
      }}
    >
      {children}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  )
}

// Skeleton loader for consistent loading states
export function SkeletonLoader({
  lines = 3,
  className = "",
  showAvatar = false
}: {
  lines?: number
  className?: string
  showAvatar?: boolean
}) {
  return (
    <div className={`animate-pulse space-y-3 ${className}`}>
      {showAvatar && (
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-muted rounded-full"></div>
          <div className="space-y-2 flex-1">
            <div className="h-4 bg-muted rounded w-1/4"></div>
            <div className="h-3 bg-muted rounded w-1/6"></div>
          </div>
        </div>
      )}
      {Array.from({ length: lines }).map((_, i) => (
        <div key={i} className="space-y-2">
          <div className="h-4 bg-muted rounded w-full"></div>
          <div className="h-4 bg-muted rounded w-5/6"></div>
        </div>
      ))}
    </div>
  )
}

// Card skeleton for dashboard cards
export function CardSkeleton({
  showIcon = true,
  showProgress = false
}: {
  showIcon?: boolean
  showProgress?: boolean
}) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="h-4 bg-muted rounded w-1/3 animate-pulse"></div>
        {showIcon && <div className="h-4 w-4 bg-muted rounded animate-pulse"></div>}
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="h-8 bg-muted rounded w-1/2 animate-pulse"></div>
          {showProgress && <div className="h-2 bg-muted rounded w-full animate-pulse"></div>}
          <div className="h-3 bg-muted rounded w-3/4 animate-pulse"></div>
        </div>
      </CardContent>
    </Card>
  )
}

// Table skeleton for data tables
export function TableSkeleton({
  rows = 5,
  columns = 4
}: {
  rows?: number
  columns?: number
}) {
  return (
    <div className="space-y-3">
      {/* Header */}
      <div className="flex space-x-4">
        {Array.from({ length: columns }).map((_, i) => (
          <div key={i} className="h-4 bg-muted rounded flex-1 animate-pulse"></div>
        ))}
      </div>

      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div
              key={colIndex}
              className="h-6 bg-muted rounded flex-1 animate-pulse"
              style={{ animationDelay: `${(rowIndex * columns + colIndex) * 50}ms` }}
            ></div>
          ))}
        </div>
      ))}
    </div>
  )
}

// Chart skeleton for analytics charts
export function ChartSkeleton({ height = "h-64" }: { height?: string }) {
  return (
    <div className={`${height} bg-muted rounded animate-pulse flex items-end justify-center space-x-2 p-4`}>
      {Array.from({ length: 8 }).map((_, i) => (
        <div
          key={i}
          className="bg-muted-foreground/20 rounded-t"
          style={{
            height: `${Math.random() * 60 + 20}%`,
            width: '12%',
            animationDelay: `${i * 100}ms`
          }}
        ></div>
      ))}
    </div>
  )
}

// Optimized image component with lazy loading
export function OptimizedImage({
  src,
  alt,
  className = "",
  fallback = "/placeholder.svg",
  ...props
}: {
  src: string
  alt: string
  className?: string
  fallback?: string
  [key: string]: any
}) {
  return (
    <img
      src={src}
      alt={alt}
      className={`transition-opacity duration-300 ${className}`}
      loading="lazy"
      onError={(e) => {
        const target = e.target as HTMLImageElement
        target.src = fallback
      }}
      {...props}
    />
  )
}

// Virtualized list component for large datasets
export function VirtualizedList<T>({
  items,
  renderItem,
  itemHeight = 60,
  containerHeight = 400,
  overscan = 5
}: {
  items: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  itemHeight?: number
  containerHeight?: number
  overscan?: number
}) {
  const visibleCount = Math.ceil(containerHeight / itemHeight)
  const totalHeight = items.length * itemHeight

  return (
    <div
      className="overflow-auto"
      style={{ height: containerHeight }}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {items.slice(0, visibleCount + overscan).map((item, index) => (
          <div
            key={index}
            style={{
              position: 'absolute',
              top: index * itemHeight,
              height: itemHeight,
              width: '100%'
            }}
          >
            {renderItem(item, index)}
          </div>
        ))}
      </div>
    </div>
  )
}
