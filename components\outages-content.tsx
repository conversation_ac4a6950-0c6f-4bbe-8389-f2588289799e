"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { But<PERSON> } from "@/src/components/ui/button"
import { Filter, Download, Plus, RefreshCw } from "lucide-react"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { OutageMap } from "@/components/outage-map"
import { OutagesTable } from "@/components/outages-table"
import { OutageStats } from "@/components/outage-stats"

export function OutagesContent() {
  const [searchQuery, setSearchQuery] = useState("")

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Outage Management System</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Report Outage
          </Button>
        </div>
      </div>

      <OutageStats />

      <Tabs defaultValue="map" className="space-y-4">
        <TabsList>
          <TabsTrigger value="map">Map View</TabsTrigger>
          <TabsTrigger value="active">Active Outages</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="resolved">Resolved</TabsTrigger>
        </TabsList>

        <TabsContent value="map" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Outage Map</CardTitle>
              <CardDescription>Geographic view of current power outages across Ethiopia</CardDescription>
            </CardHeader>
            <CardContent className="h-[500px] p-0">
              <OutageMap />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Active Outages</CardTitle>
              <CardDescription>Currently ongoing power outages</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-4">
                <div className="relative flex-1">
                  <Input
                    type="search"
                    placeholder="Search outages..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Cause" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Causes</SelectItem>
                      <SelectItem value="equipment">Equipment Failure</SelectItem>
                      <SelectItem value="weather">Weather Related</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="unknown">Unknown</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Region" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Regions</SelectItem>
                      <SelectItem value="addis">Addis Ababa</SelectItem>
                      <SelectItem value="dire">Dire Dawa</SelectItem>
                      <SelectItem value="bahir">Bahir Dar</SelectItem>
                      <SelectItem value="hawassa">Hawassa</SelectItem>
                      <SelectItem value="mekelle">Mekelle</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <OutagesTable status="active" />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Outages</CardTitle>
              <CardDescription>Planned maintenance outages</CardDescription>
            </CardHeader>
            <CardContent>
              <OutagesTable status="scheduled" />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="resolved" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Resolved Outages</CardTitle>
              <CardDescription>Recently resolved power outages</CardDescription>
            </CardHeader>
            <CardContent>
              <OutagesTable status="resolved" />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
