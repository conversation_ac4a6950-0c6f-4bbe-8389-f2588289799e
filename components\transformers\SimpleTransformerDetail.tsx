'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, MapPin, Wrench, Search, Clock, Calendar, CheckCircle, AlertTriangle, XCircle } from 'lucide-react'
import { Button } from '@/src/components/ui/button'
import { TransformerDataStandardizer } from '@/src/services/transformer-data-standardizer'

interface TransformerData {
  id: string
  name: string
  serialNumber: string
  status: string
  manufacturer: string
  model: string
  capacity: number
  voltagePrimary: number
  voltageSecondary: number
  location: string
  installationDate: string
  temperature: number
  loadFactor: number
  oilLevel: number
  efficiencyRating: number
}

interface SimpleTransformerDetailProps {
  transformerId: string
}

export default function SimpleTransformerDetail({ transformerId }: SimpleTransformerDetailProps) {
  const router = useRouter()
  const [transformer, setTransformer] = useState<TransformerData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('details')

  useEffect(() => {
    const loadTransformer = async () => {
      try {
        setLoading(true)
        setError(null)

        console.log('🔄 Loading transformer details for ID:', transformerId)
        const response = await fetch(`/api/mysql/transformers/${transformerId}`)
        console.log('📊 Response status:', response.status, response.ok)

        if (response.ok) {
          const data = await response.json()
          console.log('📊 API Response:', data)

          if (data.success && data.data) {
            const t = data.data
            console.log('📊 Raw transformer data:', t)

            // Transform the data using standardizer
            const standardized = TransformerDataStandardizer.standardize(t)
            const transformedData: TransformerData = {
              id: standardized.id,
              name: standardized.name,
              serialNumber: standardized.serialNumber,
              status: standardized.status,
              manufacturer: standardized.manufacturer,
              model: standardized.model,
              capacity: standardized.capacity,
              voltagePrimary: standardized.voltagePrimary,
              voltageSecondary: standardized.voltageSecondary,
              location: standardized.location.name,
              installationDate: standardized.installationDate,
              temperature: standardized.temperature || 0,
              loadFactor: standardized.loadFactor || 0,
              oilLevel: 85, // Default oil level
              efficiencyRating: standardized.healthIndex || 85
            }

            console.log('✅ Transformed transformer data:', transformedData)
            setTransformer(transformedData)
          } else {
            console.warn('⚠️ API response indicates failure or no data:', data)
            setError('Transformer data not found in response')
          }
        } else {
          console.error('❌ API request failed:', response.status, response.statusText)
          const errorData = await response.json().catch(() => ({}))
          console.error('❌ Error details:', errorData)
          setError(`Failed to load transformer: ${response.status} ${response.statusText}`)
        }
      } catch (err) {
        console.error('❌ Error loading transformer:', err)
        setError(err instanceof Error ? err.message : 'Unknown error occurred')
      } finally {
        setLoading(false)
      }
    }

    if (transformerId) {
      loadTransformer()
    }
  }, [transformerId])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'operational': return 'bg-green-100 text-green-800'
      case 'maintenance': return 'bg-yellow-100 text-yellow-800'
      case 'critical':
      case 'burnt': return 'bg-red-100 text-red-800'
      case 'warning': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const renderDetailsContent = () => (
    <>
      {/* Transformer Details */}
      <div className="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Transformer Details</h2>
          <p className="text-sm text-gray-500">Technical specifications</p>
        </div>

        <div className="grid grid-cols-2 gap-6">
          <div>
            <label className="text-sm font-medium text-gray-500">Card No.</label>
            <p className="text-gray-900 font-medium">{transformer.serialNumber}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">KVA Rating</label>
            <p className="text-gray-900 font-medium">{transformer.capacity}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Primary Service Voltage</label>
            <p className="text-gray-900 font-medium">{transformer.voltagePrimary}V/{transformer.voltageSecondary}V</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Manufacturer</label>
            <p className="text-gray-900 font-medium">{transformer.manufacturer}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Manufacturing Year</label>
            <p className="text-gray-900 font-medium">{new Date(transformer.installationDate).getFullYear()}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Serial Number</label>
            <p className="text-gray-900 font-medium">{transformer.serialNumber}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Tag Number</label>
            <p className="text-gray-900 font-medium">ET-DT-{transformer.id}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Installation Date</label>
            <p className="text-gray-900 font-medium">{new Date(transformer.installationDate).toLocaleDateString()}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Construction Type</label>
            <p className="text-gray-900 font-medium">Pole Mounted</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Customer Type</label>
            <p className="text-gray-900 font-medium">Mixed Commercial</p>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Details</h3>
          <p className="text-gray-700">Shopping complex and office buildings</p>
        </div>
      </div>

      {/* Location */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Location</h2>
          <p className="text-sm text-gray-500">Geographic information</p>
        </div>

        {/* Map placeholder */}
        <div className="bg-gray-100 rounded-lg h-32 flex items-center justify-center mb-6">
          <MapPin className="h-12 w-12 text-gray-400" />
        </div>

        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-500">GPS Coordinates</label>
            <p className="text-gray-900 font-medium">9.005401, 38.763611</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Region</label>
            <p className="text-gray-900 font-medium">Addis Ababa</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">District</label>
            <p className="text-gray-900 font-medium">North District</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Sub City</label>
            <p className="text-gray-900 font-medium">Bole</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Kebele</label>
            <p className="text-gray-900 font-medium">05</p>
          </div>
        </div>
      </div>
    </>
  )

  const renderMaintenanceContent = () => (
    <>
      {/* Maintenance Schedule */}
      <div className="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Maintenance Schedule</h2>
          <p className="text-sm text-gray-500">Planned and completed maintenance activities</p>
        </div>

        <div className="space-y-4">
          <div className="border rounded-lg p-4 bg-green-50 border-green-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <h3 className="font-medium text-gray-900">Routine Oil Analysis</h3>
              </div>
              <span className="text-sm text-green-600 font-medium">Completed</span>
            </div>
            <p className="text-sm text-gray-600 mb-2">Last performed: March 15, 2024</p>
            <p className="text-sm text-gray-700">Oil quality analysis shows normal parameters. No action required.</p>
          </div>

          <div className="border rounded-lg p-4 bg-yellow-50 border-yellow-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-yellow-600 mr-2" />
                <h3 className="font-medium text-gray-900">Cooling System Inspection</h3>
              </div>
              <span className="text-sm text-yellow-600 font-medium">Scheduled</span>
            </div>
            <p className="text-sm text-gray-600 mb-2">Scheduled: April 20, 2024</p>
            <p className="text-sm text-gray-700">Inspect cooling fans, radiators, and temperature sensors.</p>
          </div>

          <div className="border rounded-lg p-4 bg-blue-50 border-blue-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Wrench className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="font-medium text-gray-900">Annual Preventive Maintenance</h3>
              </div>
              <span className="text-sm text-blue-600 font-medium">Upcoming</span>
            </div>
            <p className="text-sm text-gray-600 mb-2">Scheduled: June 10, 2024</p>
            <p className="text-sm text-gray-700">Comprehensive maintenance including bushing inspection, tap changer service, and protection system testing.</p>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Maintenance History</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <div>
                <p className="font-medium text-gray-900">Bushing Replacement</p>
                <p className="text-sm text-gray-500">January 12, 2024</p>
              </div>
              <span className="text-sm text-green-600">Completed</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <div>
                <p className="font-medium text-gray-900">Load Tap Changer Service</p>
                <p className="text-sm text-gray-500">December 5, 2023</p>
              </div>
              <span className="text-sm text-green-600">Completed</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <div>
                <p className="font-medium text-gray-900">Oil Filtration</p>
                <p className="text-sm text-gray-500">October 18, 2023</p>
              </div>
              <span className="text-sm text-green-600">Completed</span>
            </div>
          </div>
        </div>
      </div>

      {/* Maintenance Status */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Current Status</h2>
          <p className="text-sm text-gray-500">Maintenance indicators</p>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-sm font-medium text-gray-900">Oil Level</span>
            </div>
            <span className="text-sm text-green-600 font-medium">Normal</span>
          </div>

          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-sm font-medium text-gray-900">Temperature</span>
            </div>
            <span className="text-sm text-green-600 font-medium">Normal</span>
          </div>

          <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
              <span className="text-sm font-medium text-gray-900">Next Service</span>
            </div>
            <span className="text-sm text-yellow-600 font-medium">Due Soon</span>
          </div>

          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-gray-900">Last Service</span>
            </div>
            <span className="text-sm text-blue-600 font-medium">Mar 15, 2024</span>
          </div>
        </div>

        <div className="mt-6">
          <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
            Schedule Maintenance
          </Button>
        </div>
      </div>
    </>
  )

  const renderInspectionsContent = () => (
    <>
      {/* Inspection Reports */}
      <div className="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Inspection Reports</h2>
          <p className="text-sm text-gray-500">Recent inspection findings and recommendations</p>
        </div>

        <div className="space-y-4">
          <div className="border rounded-lg p-4 bg-green-50 border-green-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <h3 className="font-medium text-gray-900">Visual Inspection</h3>
              </div>
              <span className="text-sm text-green-600 font-medium">Passed</span>
            </div>
            <p className="text-sm text-gray-600 mb-2">Conducted: March 20, 2024</p>
            <p className="text-sm text-gray-700">No visible damage, corrosion, or oil leaks detected. All external components in good condition.</p>
            <div className="mt-2">
              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Inspector: John Doe</span>
            </div>
          </div>

          <div className="border rounded-lg p-4 bg-yellow-50 border-yellow-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                <h3 className="font-medium text-gray-900">Thermal Inspection</h3>
              </div>
              <span className="text-sm text-yellow-600 font-medium">Minor Issues</span>
            </div>
            <p className="text-sm text-gray-600 mb-2">Conducted: March 18, 2024</p>
            <p className="text-sm text-gray-700">Slight temperature elevation detected in bushing connection. Recommend tightening connections during next maintenance.</p>
            <div className="mt-2">
              <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Inspector: Jane Smith</span>
            </div>
          </div>

          <div className="border rounded-lg p-4 bg-blue-50 border-blue-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Search className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="font-medium text-gray-900">Electrical Testing</h3>
              </div>
              <span className="text-sm text-blue-600 font-medium">Scheduled</span>
            </div>
            <p className="text-sm text-gray-600 mb-2">Scheduled: April 25, 2024</p>
            <p className="text-sm text-gray-700">Comprehensive electrical testing including insulation resistance, turns ratio, and protection system verification.</p>
            <div className="mt-2">
              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Assigned: Mike Johnson</span>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Inspection Checklist</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                <span className="text-sm text-gray-700">Oil level and quality</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                <span className="text-sm text-gray-700">Bushing condition</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                <span className="text-sm text-gray-700">Cooling system</span>
              </div>
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                <span className="text-sm text-gray-700">Connection tightness</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                <span className="text-sm text-gray-700">Grounding system</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                <span className="text-sm text-gray-700">Protection devices</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 text-blue-600 mr-2" />
                <span className="text-sm text-gray-700">Electrical testing</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                <span className="text-sm text-gray-700">Documentation review</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Inspection Schedule */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Inspection Schedule</h2>
          <p className="text-sm text-gray-500">Upcoming inspections</p>
        </div>

        <div className="space-y-4">
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-gray-900">Electrical Testing</span>
              <span className="text-xs text-blue-600">Apr 25</span>
            </div>
            <p className="text-xs text-gray-600">Comprehensive electrical inspection</p>
          </div>

          <div className="p-3 bg-yellow-50 rounded-lg">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-gray-900">Thermal Imaging</span>
              <span className="text-xs text-yellow-600">May 10</span>
            </div>
            <p className="text-xs text-gray-600">Thermal condition assessment</p>
          </div>

          <div className="p-3 bg-green-50 rounded-lg">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-gray-900">Annual Inspection</span>
              <span className="text-xs text-green-600">Jun 15</span>
            </div>
            <p className="text-xs text-gray-600">Complete annual inspection</p>
          </div>
        </div>

        <div className="mt-6">
          <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
            Schedule Inspection
          </Button>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">Quick Stats</h3>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Last Inspection:</span>
              <span className="text-gray-900">Mar 20, 2024</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Next Due:</span>
              <span className="text-gray-900">Apr 25, 2024</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Total Issues:</span>
              <span className="text-yellow-600">1 Minor</span>
            </div>
          </div>
        </div>
      </div>
    </>
  )

  const renderHistoryContent = () => (
    <>
      {/* Activity Timeline */}
      <div className="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Activity Timeline</h2>
          <p className="text-sm text-gray-500">Complete history of transformer activities</p>
        </div>

        <div className="space-y-6">
          <div className="relative">
            <div className="absolute left-4 top-6 bottom-0 w-0.5 bg-gray-200"></div>

            <div className="relative flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">Oil Analysis Completed</p>
                  <p className="text-sm text-gray-500">March 15, 2024</p>
                </div>
                <p className="text-sm text-gray-600">Routine oil quality analysis performed. All parameters within normal range.</p>
                <div className="mt-1">
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Maintenance</span>
                </div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="relative flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Search className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">Visual Inspection</p>
                  <p className="text-sm text-gray-500">March 10, 2024</p>
                </div>
                <p className="text-sm text-gray-600">Comprehensive visual inspection completed. No issues found.</p>
                <div className="mt-1">
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Inspection</span>
                </div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="relative flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <Wrench className="h-4 w-4 text-yellow-600" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">Bushing Replacement</p>
                  <p className="text-sm text-gray-500">January 12, 2024</p>
                </div>
                <p className="text-sm text-gray-600">High voltage bushing replaced due to aging. System tested and operational.</p>
                <div className="mt-1">
                  <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Repair</span>
                </div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="relative flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <Calendar className="h-4 w-4 text-purple-600" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">Load Tap Changer Service</p>
                  <p className="text-sm text-gray-500">December 5, 2023</p>
                </div>
                <p className="text-sm text-gray-600">Annual load tap changer maintenance completed. All contacts cleaned and tested.</p>
                <div className="mt-1">
                  <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">Maintenance</span>
                </div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="relative flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">Temperature Alert</p>
                  <p className="text-sm text-gray-500">November 18, 2023</p>
                </div>
                <p className="text-sm text-gray-600">High temperature alarm triggered. Investigation revealed blocked cooling fan. Fan replaced and system normalized.</p>
                <div className="mt-1">
                  <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Alert</span>
                </div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="relative flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">Installation Completed</p>
                  <p className="text-sm text-gray-500">March 15, 2022</p>
                </div>
                <p className="text-sm text-gray-600">Transformer successfully installed and commissioned. All tests passed.</p>
                <div className="mt-1">
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Installation</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* History Summary */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Summary</h2>
          <p className="text-sm text-gray-500">Historical overview</p>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-900">Service Years</span>
              <span className="text-lg font-bold text-blue-600">2.1</span>
            </div>
            <p className="text-xs text-gray-600">Years in operation</p>
          </div>

          <div className="p-4 bg-green-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-900">Uptime</span>
              <span className="text-lg font-bold text-green-600">99.8%</span>
            </div>
            <p className="text-xs text-gray-600">Operational reliability</p>
          </div>

          <div className="p-4 bg-yellow-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-900">Maintenance Events</span>
              <span className="text-lg font-bold text-yellow-600">12</span>
            </div>
            <p className="text-xs text-gray-600">Total maintenance activities</p>
          </div>

          <div className="p-4 bg-purple-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-900">Inspections</span>
              <span className="text-lg font-bold text-purple-600">8</span>
            </div>
            <p className="text-xs text-gray-600">Completed inspections</p>
          </div>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">Recent Activity</h3>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Last Activity:</span>
              <span className="text-gray-900">Mar 15, 2024</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Activity Type:</span>
              <span className="text-green-600">Maintenance</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Status:</span>
              <span className="text-green-600">Completed</span>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
            Export History
          </Button>
        </div>
      </div>
    </>
  )

  if (loading) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center mb-6">
            <Button variant="ghost" onClick={() => router.push('/transformers')} className="mr-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Transformers
            </Button>
          </div>
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p>Loading transformer details...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !transformer) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center mb-6">
            <Button variant="ghost" onClick={() => router.push('/transformers')} className="mr-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Transformers
            </Button>
          </div>
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <div className="text-red-600 mb-4">❌ Transformer Not Found</div>
            <p className="text-gray-600 mb-4">
              {error || 'The requested transformer could not be found.'}
            </p>
            <Button onClick={() => router.push('/transformers')} variant="outline">
              Back to Transformers
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" onClick={() => router.push('/transformers')} className="mr-4 text-gray-600">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Transformers
            </Button>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" className="text-gray-600">
              <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
              </svg>
              Print
            </Button>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              Actions
              <svg className="h-4 w-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </Button>
          </div>
        </div>

        {/* Title Section */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-3xl font-bold text-gray-900">{transformer.serialNumber}</h1>
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(transformer.status)}`}>
              {transformer.status}
            </span>
          </div>
        </div>

        <p className="text-blue-600 text-lg mb-8">{transformer.location} / {transformer.serialNumber}</p>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('details')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'details'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Details
              </button>
              <button
                onClick={() => setActiveTab('maintenance')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'maintenance'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Maintenance
              </button>
              <button
                onClick={() => setActiveTab('inspections')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'inspections'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Inspections
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'history'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                History
              </button>
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {activeTab === 'details' && renderDetailsContent()}
          {activeTab === 'maintenance' && renderMaintenanceContent()}
          {activeTab === 'inspections' && renderInspectionsContent()}
          {activeTab === 'history' && renderHistoryContent()}
        </div>
      </div>
    </div>
  )
}
