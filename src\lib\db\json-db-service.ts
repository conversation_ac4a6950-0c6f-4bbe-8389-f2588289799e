/**
 * JSON Database Service
 *
 * This service provides a high-level API for interacting with a JSON file-based database.
 * It maintains the same API as the existing database service but stores data in JSON files.
 */

import { v4 as uuidv4 } from 'uuid';

// Import fs and path only on the server side
let fs: any;
let path: any;
if (typeof window === 'undefined') {
  fs = require('fs');
  path = require('path');
}

// Database directory path and file paths
let DB_DIR: string;
let TRANSFORMERS_FILE: string;
let REGIONS_FILE: string;
let SERVICE_CENTERS_FILE: string;

// Initialize paths only on the server side
if (typeof window === 'undefined') {
  DB_DIR = path.join(process.cwd(), 'data');

  // Ensure the data directory exists
  if (!fs.existsSync(DB_DIR)) {
    fs.mkdirSync(DB_DIR, { recursive: true });
  }

  // Database file paths
  TRANSFORMERS_FILE = path.join(DB_DIR, 'transformers.json');
  REGIONS_FILE = path.join(DB_DIR, 'regions.json');
  SERVICE_CENTERS_FILE = path.join(DB_DIR, 'service-centers.json');
}

// Helper function to read a JSON file
async function readJsonFile(filePath: string, defaultValue: any = []) {
  try {
    if (typeof window !== 'undefined') {
      // Client-side: Fetch from API
      const fileName = filePath.split('/').pop()?.replace('.json', '');
      if (!fileName) return defaultValue;

      try {
        const response = await fetch(`/api/db/${fileName}`);
        if (!response.ok) {
          console.error(`Error fetching ${fileName} from API:`, response.statusText);
          return defaultValue;
        }
        return await response.json();
      } catch (error) {
        console.error(`Error fetching ${fileName} from API:`, error);
        return defaultValue;
      }
    }

    // Server-side: Read from file
    if (!fs.existsSync(filePath)) {
      // File doesn't exist, return default value
      return defaultValue;
    }

    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading JSON file ${filePath}:`, error);
    return defaultValue;
  }
}

// Synchronous version for server-side use
function readJsonFileSync(filePath: string, defaultValue: any = []) {
  try {
    if (typeof window !== 'undefined') {
      // Client-side: Return default value
      return defaultValue;
    }

    if (!fs.existsSync(filePath)) {
      // File doesn't exist, return default value
      return defaultValue;
    }

    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading JSON file ${filePath}:`, error);
    return defaultValue;
  }
}

// Helper function to write a JSON file
function writeJsonFile(filePath: string, data: any) {
  try {
    if (typeof window !== 'undefined') {
      // Client-side: Do nothing
      return;
    }

    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
  } catch (error) {
    console.error(`Error writing JSON file ${filePath}:`, error);
  }
}

/**
 * Transformer Service
 */
export const transformerService = {
  /**
   * Get all transformers
   */
  async getAllTransformers() {
    if (typeof window !== 'undefined') {
      // Client-side: Fetch from API
      try {
        const response = await fetch('/api/db/transformers');
        if (!response.ok) {
          console.error('Error fetching transformers from API:', response.statusText);
          return [];
        }
        return await response.json();
      } catch (error) {
        console.error('Error fetching transformers from API:', error);
        return [];
      }
    } else {
      // Server-side: Read from file
      return readJsonFileSync(TRANSFORMERS_FILE);
    }
  },

  /**
   * Get a transformer by ID
   */
  async getTransformerById(id: string) {
    const transformers = await this.getAllTransformers();
    return transformers.find((transformer: any) => transformer.id === id) || null;
  },

  /**
   * Add a new transformer
   */
  addTransformer(transformer: any) {
    const transformers = readJsonFile(TRANSFORMERS_FILE);
    const id = transformer.id || uuidv4();
    const newTransformer = { ...transformer, id };

    transformers.push(newTransformer);
    writeJsonFile(TRANSFORMERS_FILE, transformers);

    return newTransformer;
  },

  /**
   * Update a transformer
   */
  updateTransformer(id: string, transformer: any) {
    const transformers = readJsonFile(TRANSFORMERS_FILE);
    const index = transformers.findIndex((t: any) => t.id === id);

    if (index === -1) {
      return null;
    }

    const updatedTransformer = { ...transformers[index], ...transformer, id };
    transformers[index] = updatedTransformer;

    writeJsonFile(TRANSFORMERS_FILE, transformers);

    return updatedTransformer;
  },

  /**
   * Delete a transformer
   */
  deleteTransformer(id: string) {
    const transformers = readJsonFile(TRANSFORMERS_FILE);
    const filteredTransformers = transformers.filter((t: any) => t.id !== id);

    if (filteredTransformers.length === transformers.length) {
      return false;
    }

    writeJsonFile(TRANSFORMERS_FILE, filteredTransformers);

    return true;
  }
};

/**
 * Region Service
 */
export const regionService = {
  /**
   * Get all regions
   */
  async getAllRegions() {
    if (typeof window !== 'undefined') {
      // Client-side: Fetch from API
      try {
        const response = await fetch('/api/db/regions');
        if (!response.ok) {
          console.error('Error fetching regions from API:', response.statusText);
          return [];
        }
        return await response.json();
      } catch (error) {
        console.error('Error fetching regions from API:', error);
        return [];
      }
    } else {
      // Server-side: Read from file
      return readJsonFileSync(REGIONS_FILE);
    }
  },

  /**
   * Get a region by ID
   */
  async getRegionById(id: string) {
    const regions = await this.getAllRegions();
    return regions.find((region: any) => region.id === id) || null;
  },

  /**
   * Add a new region
   */
  addRegion(region: any) {
    const regions = readJsonFile(REGIONS_FILE);
    const id = region.id || uuidv4();
    const newRegion = { ...region, id };

    regions.push(newRegion);
    writeJsonFile(REGIONS_FILE, regions);

    return newRegion;
  }
};

/**
 * Service Center Service
 */
export const serviceCenterService = {
  /**
   * Get all service centers
   */
  async getAllServiceCenters() {
    if (typeof window !== 'undefined') {
      // Client-side: Fetch from API
      try {
        const response = await fetch('/api/db/service-centers');
        if (!response.ok) {
          console.error('Error fetching service centers from API:', response.statusText);
          return [];
        }
        return await response.json();
      } catch (error) {
        console.error('Error fetching service centers from API:', error);
        return [];
      }
    } else {
      // Server-side: Read from file
      return readJsonFileSync(SERVICE_CENTERS_FILE);
    }
  },

  /**
   * Get a service center by ID
   */
  async getServiceCenterById(id: string) {
    const serviceCenters = await this.getAllServiceCenters();
    return serviceCenters.find((serviceCenter: any) => serviceCenter.id === id) || null;
  },

  /**
   * Get service centers by region
   */
  async getServiceCentersByRegion(region: string) {
    const serviceCenters = await this.getAllServiceCenters();
    return serviceCenters.filter((serviceCenter: any) => serviceCenter.region === region);
  },

  /**
   * Add a new service center
   */
  addServiceCenter(serviceCenter: any) {
    const serviceCenters = readJsonFile(SERVICE_CENTERS_FILE);
    const id = serviceCenter.id || uuidv4();
    const newServiceCenter = { ...serviceCenter, id };

    serviceCenters.push(newServiceCenter);
    writeJsonFile(SERVICE_CENTERS_FILE, serviceCenters);

    return newServiceCenter;
  }
};

/**
 * JSON Database Service
 */
export const jsonDbService = {
  transformers: transformerService,
  regions: regionService,
  serviceCenters: serviceCenterService,

  /**
   * Initialize the database
   */
  initialize() {
    // Create empty files if they don't exist (server-side only)
    if (typeof window === 'undefined') {
      // Ensure the data directory exists
      if (!fs.existsSync(DB_DIR)) {
        fs.mkdirSync(DB_DIR, { recursive: true });
      }

      if (!fs.existsSync(TRANSFORMERS_FILE)) {
        writeJsonFile(TRANSFORMERS_FILE, []);
      }

      if (!fs.existsSync(REGIONS_FILE)) {
        writeJsonFile(REGIONS_FILE, []);
      }

      if (!fs.existsSync(SERVICE_CENTERS_FILE)) {
        writeJsonFile(SERVICE_CENTERS_FILE, []);
      }

      console.log('JSON database initialized successfully (server-side)');
    } else {
      console.log('JSON database initialized successfully (client-side)');
    }
  }
};

export default jsonDbService;
