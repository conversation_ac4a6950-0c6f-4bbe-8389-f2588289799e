"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Badge } from '@/src/components/ui/badge'
import { Cloud, CloudRain, Sun, Wind, Thermometer, Droplets, AlertTriangle } from 'lucide-react'

interface WeatherData {
  location: string
  temperature: number
  humidity: number
  windSpeed: number
  condition: 'sunny' | 'cloudy' | 'rainy' | 'stormy'
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  transformersAtRisk: number
  recommendations: string[]
}

interface WeatherImpact {
  region: string
  weather: WeatherData
  alerts: string[]
}

export default function WeatherImpactWidget() {
  const [weatherData, setWeatherData] = useState<WeatherImpact[]>([
    {
      region: 'Addis Ababa',
      weather: {
        location: 'Addis Ababa',
        temperature: 22,
        humidity: 65,
        windSpeed: 15,
        condition: 'cloudy',
        riskLevel: 'low',
        transformersAtRisk: 2,
        recommendations: ['Monitor humidity levels', 'Check insulation integrity']
      },
      alerts: []
    },
    {
      region: 'Oromia',
      weather: {
        location: 'Adama',
        temperature: 28,
        humidity: 80,
        windSpeed: 25,
        condition: 'rainy',
        riskLevel: 'high',
        transformersAtRisk: 8,
        recommendations: [
          'Increase monitoring frequency',
          'Prepare emergency response teams',
          'Check drainage systems'
        ]
      },
      alerts: ['Heavy rain expected', 'High humidity warning']
    },
    {
      region: 'Amhara',
      weather: {
        location: 'Bahir Dar',
        temperature: 25,
        humidity: 45,
        windSpeed: 10,
        condition: 'sunny',
        riskLevel: 'low',
        transformersAtRisk: 0,
        recommendations: ['Normal operations']
      },
      alerts: []
    }
  ])

  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Fetch weather data
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // In a real implementation, this would fetch from weather API
        await new Promise(resolve => setTimeout(resolve, 800))
      } catch (error) {
        console.error('Error fetching weather data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'sunny':
        return <Sun className="h-5 w-5 text-yellow-500" />
      case 'cloudy':
        return <Cloud className="h-5 w-5 text-gray-500" />
      case 'rainy':
        return <CloudRain className="h-5 w-5 text-blue-500" />
      case 'stormy':
        return <Wind className="h-5 w-5 text-purple-500" />
      default:
        return <Cloud className="h-5 w-5 text-gray-500" />
    }
  }

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'critical':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'sunny':
        return 'text-yellow-600'
      case 'cloudy':
        return 'text-gray-600'
      case 'rainy':
        return 'text-blue-600'
      case 'stormy':
        return 'text-purple-600'
      default:
        return 'text-gray-600'
    }
  }

  const totalTransformersAtRisk = weatherData.reduce((sum, data) => sum + data.weather.transformersAtRisk, 0)
  const highRiskRegions = weatherData.filter(data => data.weather.riskLevel === 'high' || data.weather.riskLevel === 'critical').length

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Weather Impact</CardTitle>
          <CardDescription>Loading weather data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-48">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Cloud className="h-5 w-5" />
          Weather Impact Assessment
        </CardTitle>
        <CardDescription>
          Weather conditions and their impact on transformer operations
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{weatherData.length}</div>
            <div className="text-sm text-muted-foreground">Regions Monitored</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{totalTransformersAtRisk}</div>
            <div className="text-sm text-muted-foreground">At Risk</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{highRiskRegions}</div>
            <div className="text-sm text-muted-foreground">High Risk Areas</div>
          </div>
        </div>

        {/* Regional Weather Data */}
        <div className="space-y-4">
          {weatherData.map((data, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  {getWeatherIcon(data.weather.condition)}
                  <div>
                    <h4 className="font-medium">{data.region}</h4>
                    <p className={`text-sm capitalize ${getConditionColor(data.weather.condition)}`}>
                      {data.weather.condition}
                    </p>
                  </div>
                </div>
                <Badge variant="outline" className={getRiskColor(data.weather.riskLevel)}>
                  {data.weather.riskLevel} risk
                </Badge>
              </div>

              {/* Weather Metrics */}
              <div className="grid grid-cols-3 gap-4 mb-3">
                <div className="flex items-center gap-2 text-sm">
                  <Thermometer className="h-4 w-4 text-muted-foreground" />
                  <span>{data.weather.temperature}°C</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Droplets className="h-4 w-4 text-muted-foreground" />
                  <span>{data.weather.humidity}%</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Wind className="h-4 w-4 text-muted-foreground" />
                  <span>{data.weather.windSpeed} km/h</span>
                </div>
              </div>

              {/* Alerts */}
              {data.alerts.length > 0 && (
                <div className="mb-3">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-4 w-4 text-orange-500" />
                    <span className="text-sm font-medium">Active Alerts</span>
                  </div>
                  <div className="space-y-1">
                    {data.alerts.map((alert, alertIndex) => (
                      <div key={alertIndex} className="text-sm text-orange-600 bg-orange-50 rounded px-2 py-1">
                        {alert}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Transformers at Risk */}
              {data.weather.transformersAtRisk > 0 && (
                <div className="mb-3">
                  <div className="text-sm">
                    <span className="font-medium text-orange-600">
                      {data.weather.transformersAtRisk} transformers at risk
                    </span>
                  </div>
                </div>
              )}

              {/* Recommendations */}
              <div>
                <h5 className="text-sm font-medium mb-2">Recommendations</h5>
                <div className="space-y-1">
                  {data.weather.recommendations.map((rec, recIndex) => (
                    <div key={recIndex} className="text-sm text-muted-foreground">
                      • {rec}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Risk Level Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-4 gap-2 text-center">
            <div>
              <div className="text-lg font-semibold text-green-600">
                {weatherData.filter(d => d.weather.riskLevel === 'low').length}
              </div>
              <div className="text-xs text-muted-foreground">Low Risk</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-yellow-600">
                {weatherData.filter(d => d.weather.riskLevel === 'medium').length}
              </div>
              <div className="text-xs text-muted-foreground">Medium Risk</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-orange-600">
                {weatherData.filter(d => d.weather.riskLevel === 'high').length}
              </div>
              <div className="text-xs text-muted-foreground">High Risk</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-red-600">
                {weatherData.filter(d => d.weather.riskLevel === 'critical').length}
              </div>
              <div className="text-xs text-muted-foreground">Critical</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
