import React from 'react'
import { render, screen } from '@testing-library/react'
import { OptimizedImage, OptimizedAvatar } from '@/components/optimized-image'

describe('OptimizedImage', () => {
  it('renders an image with the correct props', () => {
    render(
      <OptimizedImage
        src="/test-image.jpg"
        alt="Test image"
        width={200}
        height={100}
      />
    )
    
    const image = screen.getByAltText('Test image')
    expect(image).toBeInTheDocument()
    expect(image).toHaveAttribute('src')
    expect(image).toHaveAttribute('width', '200')
    expect(image).toHaveAttribute('height', '100')
  })
  
  it('applies custom className', () => {
    render(
      <OptimizedImage
        src="/test-image.jpg"
        alt="Test image"
        width={200}
        height={100}
        className="custom-class"
      />
    )
    
    const image = screen.getByAltText('Test image')
    expect(image).toHaveClass('custom-class')
  })
})

describe('OptimizedAvatar', () => {
  it('renders an avatar with the correct props', () => {
    render(
      <OptimizedAvatar
        src="/test-avatar.jpg"
        alt="Test avatar"
        size={40}
      />
    )
    
    const avatar = screen.getByAltText('Test avatar')
    expect(avatar).toBeInTheDocument()
    expect(avatar).toHaveAttribute('src')
    expect(avatar).toHaveAttribute('width', '40')
    expect(avatar).toHaveAttribute('height', '40')
    expect(avatar).toHaveClass('rounded-full')
  })
  
  it('applies custom className', () => {
    render(
      <OptimizedAvatar
        src="/test-avatar.jpg"
        alt="Test avatar"
        size={40}
        className="custom-class"
      />
    )
    
    const avatar = screen.getByAltText('Test avatar')
    expect(avatar).toHaveClass('custom-class')
    expect(avatar).toHaveClass('rounded-full')
  })
})
