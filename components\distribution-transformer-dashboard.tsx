'use client'

import { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import { Progress } from '@/src/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/src/components/ui/tabs'
import {
  Zap,
  Wrench,
  Shield,
  CheckCircle,
  AlertTriangle,
  Calendar,
  MapPin,
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw,
  FileText,
  Users,
  Building,
  Clock,
  Activity,
  BarChart3,
  Flame,
  Settings,
  PieChart,
  LineChart
} from 'lucide-react'
import { useOptimizedData } from '@/src/hooks/use-optimized-data'

interface DashboardData {
  overview: {
    totalTransformers: number
    operationalTransformers: number
    averageLoad: number
    goodCondition: number
    serviceAvailability: number
    efficiencyTrend: string
    burnedTransformers: number
    underMaintenance: number
    newInstallations: number
    replacements: number
  }
  maintenance: {
    totalSchedules: number
    completedTasks: number
    inProgressTasks: number
    pendingTasks: number
    overdueTasks: number
    efficiency: number
    averageCompletionDays: number
    nextScheduledCount: number
  }
  alerts: {
    totalAlerts: number
    activeAlerts: number
    criticalAlerts: number
    highAlerts: number
    mediumAlerts: number
    lowAlerts: number
    resolvedAlerts: number
    resolutionRate: number
    averageResolutionTime: number
  }
  regional: Array<{
    name: string
    code: string
    transformerCount: number
    averageLoad: number
    operationalCount: number
    operationalPercentage: number
  }>
  summary: {
    systemHealth: number
    maintenanceBacklog: number
    criticalIssues: number
    overallEfficiency: number
    assetCondition: string
    serviceReliability: number
  }
  charts: {
    transformerStatus: Array<{
      name: string
      value: number
      color: string
    }>
    monthlyBurns: Array<{
      month: string
      burns: number
      maintenance: number
      replacements: number
    }>
    maintenanceTypes: Array<{
      type: string
      count: number
      percentage: number
    }>
    regionalPerformance: Array<{
      region: string
      operational: number
      burned: number
      maintenance: number
    }>
  }
}

export default function DistributionTransformerDashboard() {
  const [activeTab, setActiveTab] = useState('overview')

  // Fetch dashboard data with optimized caching
  const {
    data,
    loading,
    refresh,
    isCached
  } = useOptimizedData<DashboardData>(
    useCallback(async () => {
      const response = await fetch('/api/dashboard/analytics')
      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch dashboard data')
      }

      return result.data
    }, []),
    {
      cacheKey: 'distribution-transformer-dashboard',
      cacheTTL: 10 * 60 * 1000, // 10 minutes cache for non-real-time data
      enableCache: true
    }
  )

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      default:
        return <Minus className="h-4 w-4 text-gray-600" />
    }
  }

  const getConditionColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 bg-muted rounded w-64 animate-pulse"></div>
            <div className="h-4 bg-muted rounded w-96 animate-pulse mt-2"></div>
          </div>
          <div className="h-10 bg-muted rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="h-32 bg-muted rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Distribution Transformer Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage distribution transformers across Ethiopian Electric Utility network
            {isCached && <span className="ml-2 text-xs">(Cached data)</span>}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={refresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Data
          </Button>
          <Button variant="outline" size="sm">
            <FileText className="mr-2 h-4 w-4" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Distribution Transformers</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data?.overview?.totalTransformers || 0}</div>
            <p className="text-xs text-muted-foreground">
              {data?.overview?.operationalTransformers || 0} in service
            </p>
            <div className="mt-2">
              <Progress
                value={(data?.overview?.operationalTransformers || 0) / (data?.overview?.totalTransformers || 1) * 100}
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance Due</CardTitle>
            <Wrench className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data?.maintenance?.pendingTasks || 0}</div>
            <p className="text-xs text-muted-foreground">
              {data?.maintenance?.overdueTasks || 0} overdue tasks
            </p>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant={data?.maintenance?.overdueTasks ? "destructive" : "secondary"} className="text-xs">
                {data?.maintenance?.overdueTasks ? "Action Required" : "On Schedule"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Asset Condition</CardTitle>
            <Shield className={`h-4 w-4 ${getConditionColor(data?.overview?.goodCondition || 0)}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data?.overview?.goodCondition || 0}%</div>
            <p className="text-xs text-muted-foreground">
              Good to excellent condition
            </p>
            <div className="mt-2">
              <Progress
                value={data?.overview?.goodCondition || 0}
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Service Availability</CardTitle>
            <CheckCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data?.overview?.serviceAvailability || 0}%</div>
            <p className="text-xs text-muted-foreground">
              Last 30 days average
            </p>
            <div className="flex items-center gap-1 mt-2">
              {getTrendIcon(data?.overview?.efficiencyTrend || 'stable')}
              <span className="text-xs text-muted-foreground">
                {data?.overview?.efficiencyTrend === 'stable' ? 'Stable' :
                 data?.overview?.efficiencyTrend === 'up' ? 'Improving' : 'Declining'}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Burned Transformers</CardTitle>
            <Flame className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{data?.overview?.burnedTransformers || 0}</div>
            <p className="text-xs text-muted-foreground">
              Requires replacement
            </p>
            <div className="mt-2">
              <Badge variant="destructive" className="text-xs">
                Critical Priority
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Under Maintenance</CardTitle>
            <Settings className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{data?.overview?.underMaintenance || 0}</div>
            <p className="text-xs text-muted-foreground">
              Currently being serviced
            </p>
            <div className="mt-2">
              <Progress
                value={(data?.overview?.underMaintenance || 0) / (data?.overview?.totalTransformers || 1) * 100}
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="charts">Charts & Analytics</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="regional">Regional Analysis</TabsTrigger>
          <TabsTrigger value="alerts">Alerts & Issues</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* System Health Summary */}
            <Card>
              <CardHeader>
                <CardTitle>System Health Summary</CardTitle>
                <CardDescription>
                  Overall health of distribution transformer network
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Overall Health</span>
                    <span className="text-sm font-bold">{data?.summary?.systemHealth || 0}%</span>
                  </div>
                  <Progress value={data?.summary?.systemHealth || 0} className="h-2" />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Service Reliability</span>
                    <span className="text-sm font-bold">{data?.summary?.serviceReliability || 0}%</span>
                  </div>
                  <Progress value={data?.summary?.serviceReliability || 0} className="h-2" />
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{data?.summary?.maintenanceBacklog || 0}</div>
                    <div className="text-xs text-muted-foreground">Maintenance Backlog</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{data?.summary?.criticalIssues || 0}</div>
                    <div className="text-xs text-muted-foreground">Critical Issues</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common management tasks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule Maintenance
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <MapPin className="mr-2 h-4 w-4" />
                  View Transformer Map
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Generate Performance Report
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Review Active Alerts
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="charts" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Transformer Status Pie Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="mr-2 h-5 w-5" />
                  Transformer Status Distribution
                </CardTitle>
                <CardDescription>
                  Current status of all distribution transformers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Pie Chart Representation */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm">Operational</span>
                        </div>
                        <span className="text-sm font-bold">{data?.overview?.operationalTransformers || 0}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          <span className="text-sm">Burned</span>
                        </div>
                        <span className="text-sm font-bold">{data?.overview?.burnedTransformers || 0}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                          <span className="text-sm">Maintenance</span>
                        </div>
                        <span className="text-sm font-bold">{data?.overview?.underMaintenance || 0}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                          <span className="text-sm">Offline</span>
                        </div>
                        <span className="text-sm font-bold">
                          {(data?.overview?.totalTransformers || 0) -
                           (data?.overview?.operationalTransformers || 0) -
                           (data?.overview?.burnedTransformers || 0) -
                           (data?.overview?.underMaintenance || 0)}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-center">
                      {/* Visual Pie Chart Representation */}
                      <div className="relative w-32 h-32">
                        <div className="absolute inset-0 rounded-full border-8 border-green-500"
                             style={{
                               background: `conic-gradient(
                                 #22c55e 0deg ${((data?.overview?.operationalTransformers || 0) / (data?.overview?.totalTransformers || 1)) * 360}deg,
                                 #ef4444 ${((data?.overview?.operationalTransformers || 0) / (data?.overview?.totalTransformers || 1)) * 360}deg ${(((data?.overview?.operationalTransformers || 0) + (data?.overview?.burnedTransformers || 0)) / (data?.overview?.totalTransformers || 1)) * 360}deg,
                                 #f97316 ${(((data?.overview?.operationalTransformers || 0) + (data?.overview?.burnedTransformers || 0)) / (data?.overview?.totalTransformers || 1)) * 360}deg ${(((data?.overview?.operationalTransformers || 0) + (data?.overview?.burnedTransformers || 0) + (data?.overview?.underMaintenance || 0)) / (data?.overview?.totalTransformers || 1)) * 360}deg,
                                 #6b7280 ${(((data?.overview?.operationalTransformers || 0) + (data?.overview?.burnedTransformers || 0) + (data?.overview?.underMaintenance || 0)) / (data?.overview?.totalTransformers || 1)) * 360}deg 360deg
                               )`
                             }}>
                        </div>
                        <div className="absolute inset-4 bg-white rounded-full flex items-center justify-center">
                          <div className="text-center">
                            <div className="text-lg font-bold">{data?.overview?.totalTransformers || 0}</div>
                            <div className="text-xs text-muted-foreground">Total</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Monthly Burns and Maintenance Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <LineChart className="mr-2 h-5 w-5" />
                  Monthly Trends
                </CardTitle>
                <CardDescription>
                  Burns, maintenance, and replacements over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Chart Legend */}
                  <div className="flex items-center justify-center space-x-6 text-sm">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span>Burns</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                      <span>Maintenance</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span>Replacements</span>
                    </div>
                  </div>

                  {/* Simple Bar Chart */}
                  <div className="space-y-3">
                    {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'].map((month, index) => {
                      const burns = [3, 5, 2, 7, 4, 6][index]
                      const maintenance = [12, 15, 18, 14, 16, 13][index]
                      const replacements = [2, 3, 1, 4, 3, 5][index]
                      const maxValue = 20

                      return (
                        <div key={month} className="space-y-1">
                          <div className="flex items-center justify-between text-sm">
                            <span className="w-8">{month}</span>
                            <div className="flex space-x-4 text-xs">
                              <span className="text-red-600">{burns}</span>
                              <span className="text-orange-600">{maintenance}</span>
                              <span className="text-blue-600">{replacements}</span>
                            </div>
                          </div>
                          <div className="flex space-x-1 h-4">
                            <div
                              className="bg-red-500 rounded-sm"
                              style={{ width: `${(burns / maxValue) * 100}%` }}
                            ></div>
                            <div
                              className="bg-orange-500 rounded-sm"
                              style={{ width: `${(maintenance / maxValue) * 100}%` }}
                            ></div>
                            <div
                              className="bg-blue-500 rounded-sm"
                              style={{ width: `${(replacements / maxValue) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Charts Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Maintenance Types Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Types Distribution</CardTitle>
                <CardDescription>
                  Breakdown of maintenance activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { type: 'Preventive', count: 45, color: 'bg-green-500' },
                    { type: 'Corrective', count: 28, color: 'bg-yellow-500' },
                    { type: 'Emergency', count: 12, color: 'bg-red-500' },
                    { type: 'Inspection', count: 35, color: 'bg-blue-500' }
                  ].map((item) => {
                    const percentage = (item.count / 120) * 100
                    return (
                      <div key={item.type} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{item.type}</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm">{item.count}</span>
                            <span className="text-xs text-muted-foreground">({percentage.toFixed(1)}%)</span>
                          </div>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`${item.color} h-2 rounded-full transition-all duration-300`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Regional Performance Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Regional Performance Overview</CardTitle>
                <CardDescription>
                  Transformer status by region
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data?.regional?.slice(0, 5).map((region) => (
                    <div key={region.code} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{region.name}</span>
                        <span className="text-sm text-muted-foreground">{region.transformerCount} total</span>
                      </div>
                      <div className="flex space-x-1 h-3">
                        <div
                          className="bg-green-500 rounded-sm"
                          style={{ width: `${region.operationalPercentage}%` }}
                          title={`${region.operationalCount} operational`}
                        ></div>
                        <div
                          className="bg-red-500 rounded-sm"
                          style={{ width: `${((region.transformerCount - region.operationalCount) / region.transformerCount) * 100}%` }}
                          title={`${region.transformerCount - region.operationalCount} non-operational`}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{region.operationalCount} operational</span>
                        <span>{region.transformerCount - region.operationalCount} issues</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Completed Tasks</span>
                  <Badge variant="secondary">{data?.maintenance?.completedTasks || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">In Progress</span>
                  <Badge variant="outline">{data?.maintenance?.inProgressTasks || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Pending</span>
                  <Badge variant="secondary">{data?.maintenance?.pendingTasks || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Overdue</span>
                  <Badge variant="destructive">{data?.maintenance?.overdueTasks || 0}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Efficiency Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold">{data?.maintenance?.efficiency || 0}%</div>
                  <div className="text-sm text-muted-foreground">Task Completion Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{data?.maintenance?.averageCompletionDays || 0}</div>
                  <div className="text-sm text-muted-foreground">Avg. Completion Days</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upcoming Schedule</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">{data?.maintenance?.nextScheduledCount || 0}</div>
                  <div className="text-sm text-muted-foreground">Tasks Next 30 Days</div>
                </div>
                <Button variant="outline" className="w-full mt-4">
                  <Calendar className="mr-2 h-4 w-4" />
                  View Schedule
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="regional" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Regional Distribution</CardTitle>
              <CardDescription>
                Transformer distribution across Ethiopian regions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data?.regional?.map((region) => (
                  <div key={region.code} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Building className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{region.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {region.transformerCount} transformers
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{region.operationalPercentage}%</div>
                      <div className="text-sm text-muted-foreground">Operational</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Alert Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Critical Alerts</span>
                  <Badge variant="destructive">{data?.alerts?.criticalAlerts || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">High Priority</span>
                  <Badge variant="secondary" className="bg-orange-100 text-orange-800">{data?.alerts?.highAlerts || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Medium Priority</span>
                  <Badge variant="outline">{data?.alerts?.mediumAlerts || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Low Priority</span>
                  <Badge variant="secondary">{data?.alerts?.lowAlerts || 0}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Resolution Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold">{data?.alerts?.resolutionRate || 0}%</div>
                  <div className="text-sm text-muted-foreground">Resolution Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{data?.alerts?.averageResolutionTime || 0}h</div>
                  <div className="text-sm text-muted-foreground">Avg. Resolution Time</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
