"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Car<PERSON>ianG<PERSON>, Legend, ResponsiveContainer, <PERSON>ltip, XAxis, Y<PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltipContent } from "@/src/components/ui/chart"

const data = [
  {
    name: "<PERSON>",
    completed: 18,
    scheduled: 20,
    efficiency: 90,
  },
  {
    name: "Feb",
    completed: 22,
    scheduled: 24,
    efficiency: 92,
  },
  {
    name: "<PERSON>",
    completed: 25,
    scheduled: 26,
    efficiency: 96,
  },
  {
    name: "Apr",
    completed: 24,
    scheduled: 25,
    efficiency: 96,
  },
  {
    name: "May",
    completed: 28,
    scheduled: 30,
    efficiency: 93,
  },
  {
    name: "<PERSON>",
    completed: 30,
    scheduled: 32,
    efficiency: 94,
  },
]

export function MaintenancePerformanceChart() {
  return (
    <ChartContainer
      config={{
        completed: {
          label: "Completed",
          color: "hsl(143, 85%, 40%)",
        },
        scheduled: {
          label: "Scheduled",
          color: "hsl(220, 85%, 60%)",
        },
        efficiency: {
          label: "Efficiency %",
          color: "hsl(40, 95%, 55%)",
        },
      }}
      className="h-[300px]"
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="name" />
          <YAxis yAxisId="left" />
          <YAxis yAxisId="right" orientation="right" domain={[80, 100]} />
          <Tooltip content={<ChartTooltipContent />} />
          <Legend />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="completed"
            stroke="var(--color-completed)"
            strokeWidth={2}
            activeDot={{ r: 8 }}
          />
          <Line yAxisId="left" type="monotone" dataKey="scheduled" stroke="var(--color-scheduled)" strokeWidth={2} />
          <Line yAxisId="right" type="monotone" dataKey="efficiency" stroke="var(--color-efficiency)" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
