"use client"

import { useState } from "react"
import Image from "next/image"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import {
  Download,
  Calendar,
  BarChart3,
  <PERSON><PERSON>hart,
  LineChart,
  FileText,
  RefreshCw,
  Plus,
  Filter,
  ArrowUpDown,
  Printer,
  Share2,
  Settings,
  Loader2,
  MoreHorizontal
} from "lucide-react"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { TransformerStatusChart } from "@/components/transformer-status-chart"
import { MaintenancePerformanceChart } from "@/components/maintenance-performance-chart"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Input } from "@/src/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover"
import { Calendar as CalendarComponent } from "@/src/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/src/lib/utils"
import { Badge } from "@/src/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu"
import { ReportsProvider, useReports } from "@/src/contexts/reports-context"
import {
  OverviewCharts,
  PerformanceCharts,
  MaintenanceCharts,
  CustomCharts,
  TransformerDistributionCharts,
  RegionalAnalysisCharts,
  InfrastructureAnalysisCharts,
  ComprehensiveReportCharts
} from "@/components/report-charts"
import { CustomReportBuilder } from "@/components/custom-report-builder"
import { SavedReports } from "@/components/saved-reports"

function ReportsContentInner() {
  const {
    activeTab,
    setActiveTab,
    timeframe,
    setTimeframe,
    dateRange,
    setDateRange,
    exportReport,
    resetFilters,
    isGeneratingReport
  } = useReports()

  const [showDatePicker, setShowDatePicker] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [selectedRegion, setSelectedRegion] = useState("all")
  const [selectedInfrastructure, setSelectedInfrastructure] = useState("all")
  const [selectedMaintenanceType, setSelectedMaintenanceType] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [isExporting, setIsExporting] = useState(false)
  const [isPrinting, setIsPrinting] = useState(false)
  const [isSharing, setIsSharing] = useState(false)
  const [showFilters, setShowFilters] = useState(false)

  const handleExport = async (format: "pdf" | "excel" | "csv" | "json") => {
    setIsExporting(true)
    try {
      await exportReport(format)
      // Show success notification
      console.log(`Report exported as ${format.toUpperCase()}`)
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setIsExporting(false)
    }
  }

  const handlePrint = () => {
    setIsPrinting(true)
    try {
      window.print()
    } catch (error) {
      console.error('Print failed:', error)
    } finally {
      setIsPrinting(false)
    }
  }

  const handleShare = async () => {
    setIsSharing(true)
    try {
      if (navigator.share) {
        await navigator.share({
          title: 'Ethiopian Electric Utility - Transformer Report',
          text: 'Comprehensive transformer analysis report',
          url: window.location.href
        })
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href)
        console.log('Report link copied to clipboard')
      }
    } catch (error) {
      console.error('Share failed:', error)
    } finally {
      setIsSharing(false)
    }
  }

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      // Simulate data refresh
      await new Promise(resolve => setTimeout(resolve, 1500))
      console.log('Data refreshed successfully')
    } catch (error) {
      console.error('Refresh failed:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleTimeframeChange = (value: string) => {
    setTimeframe(value as any)

    if (value === "custom") {
      setShowDatePicker(true)
    } else {
      setShowDatePicker(false)
    }
  }

  const handleRegionChange = (value: string) => {
    setSelectedRegion(value)
    console.log('Region filter changed to:', value)
  }

  const handleInfrastructureChange = (value: string) => {
    setSelectedInfrastructure(value)
    console.log('Infrastructure filter changed to:', value)
  }

  const handleMaintenanceTypeChange = (value: string) => {
    setSelectedMaintenanceType(value)
    console.log('Maintenance type filter changed to:', value)
  }

  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
    console.log('Search query:', value)
  }

  const toggleFilters = () => {
    setShowFilters(!showFilters)
  }

  const handleViewDistribution = () => {
    setActiveTab('distribution')
  }

  const handleViewEfficiencyTrends = () => {
    setActiveTab('performance')
  }

  const handleViewCostBreakdown = () => {
    setActiveTab('maintenance')
  }

  const handleViewDowntimeAnalysis = () => {
    setActiveTab('comprehensive')
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-3">
          <Image
            src="/eeu-logo.svg"
            alt="Ethiopian Electric Utility Logo"
            width={40}
            height={40}
            className="h-10 w-10 hidden sm:block"
          />
          <h1 className="text-2xl font-bold tracking-tight text-primary">Reports & Analytics</h1>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Select value={timeframe} onValueChange={handleTimeframeChange}>
            <SelectTrigger className="w-[160px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Time Period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Last Week</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
              <SelectItem value="quarter">Last Quarter</SelectItem>
              <SelectItem value="year">Last Year</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>

          {showDatePicker && (
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                  <Calendar className="mr-2 h-4 w-4" />
                  {dateRange.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                      </>
                    ) : (
                      format(dateRange.from, "LLL dd, y")
                    )
                  ) : (
                    <span>Pick a date range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <CalendarComponent
                  mode="range"
                  selected={dateRange}
                  onSelect={setDateRange}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={toggleFilters}
          >
            <Filter className="mr-2 h-4 w-4" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={isExporting}
              >
                <Download className="mr-2 h-4 w-4" />
                {isExporting ? 'Exporting...' : 'Export'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Export Options</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleExport("pdf")}>
                <FileText className="mr-2 h-4 w-4" />
                Export as PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport("excel")}>
                <FileText className="mr-2 h-4 w-4" />
                Export as Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport("csv")}>
                <FileText className="mr-2 h-4 w-4" />
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport("json")}>
                <FileText className="mr-2 h-4 w-4" />
                Export as JSON
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handlePrint} disabled={isPrinting}>
                <Printer className="mr-2 h-4 w-4" />
                {isPrinting ? 'Printing...' : 'Print Report'}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleShare} disabled={isSharing}>
                <Share2 className="mr-2 h-4 w-4" />
                {isSharing ? 'Sharing...' : 'Share Report'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Dynamic Filter Panel */}
      {showFilters && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Advanced Filters</CardTitle>
            <CardDescription>Customize your report view with advanced filtering options</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search transformers..."
                    value={searchQuery}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Region</label>
                <Select value={selectedRegion} onValueChange={handleRegionChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Regions</SelectItem>
                    <SelectItem value="addis-ababa">Addis Ababa</SelectItem>
                    <SelectItem value="oromia">Oromia</SelectItem>
                    <SelectItem value="amhara">Amhara</SelectItem>
                    <SelectItem value="snnpr">SNNPR</SelectItem>
                    <SelectItem value="tigray">Tigray</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Infrastructure</label>
                <Select value={selectedInfrastructure} onValueChange={handleInfrastructureChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Infrastructure Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Infrastructure</SelectItem>
                    <SelectItem value="feeders">Feeders Only</SelectItem>
                    <SelectItem value="substations">Substations Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Maintenance Type</label>
                <Select value={selectedMaintenanceType} onValueChange={handleMaintenanceTypeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Maintenance Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="preventive">Preventive</SelectItem>
                    <SelectItem value="corrective">Corrective</SelectItem>
                    <SelectItem value="predictive">Predictive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex items-center gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedRegion("all")
                  setSelectedInfrastructure("all")
                  setSelectedMaintenanceType("all")
                  setSearchQuery("")
                }}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Reset Filters
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleExport("csv")}
              >
                <Download className="mr-2 h-4 w-4" />
                Export Filtered Data
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="distribution" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            Distribution
          </TabsTrigger>
          <TabsTrigger value="regional" className="flex items-center gap-2">
            <LineChart className="h-4 w-4" />
            Regional
          </TabsTrigger>
          <TabsTrigger value="infrastructure" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Infrastructure
          </TabsTrigger>
          <TabsTrigger value="comprehensive" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Comprehensive
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <LineChart className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="maintenance" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            Maintenance
          </TabsTrigger>
          <TabsTrigger value="custom" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Custom Reports
          </TabsTrigger>
        </TabsList>

        {isGeneratingReport && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-lg">Generating report...</span>
          </div>
        )}

        {!isGeneratingReport && (
          <>
            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card className="relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent rounded-lg"></div>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
                    <CardTitle className="text-sm font-medium">Total Transformers</CardTitle>
                    <div className="rounded-full bg-primary/10 p-1 dark:bg-primary/20">
                      <BarChart3 className="h-4 w-4 text-primary" />
                    </div>
                  </CardHeader>
                  <CardContent className="relative">
                    <div className="text-2xl font-bold">1,248</div>
                    <div className="flex items-center">
                      <Badge variant="outline" className="text-xs font-normal text-green-600 border-green-600">
                        +12
                      </Badge>
                      <p className="text-xs text-muted-foreground ml-1">from last month</p>
                    </div>
                    <Button
                      variant="link"
                      size="sm"
                      className="p-0 h-auto mt-2 text-primary"
                      onClick={handleViewDistribution}
                    >
                      View distribution
                    </Button>
                  </CardContent>
                </Card>

                <Card className="relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-secondary/10 to-transparent rounded-lg"></div>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
                    <CardTitle className="text-sm font-medium">Avg. Efficiency</CardTitle>
                    <div className="rounded-full bg-secondary/10 p-1 dark:bg-secondary/20">
                      <PieChart className="h-4 w-4 text-secondary" />
                    </div>
                  </CardHeader>
                  <CardContent className="relative">
                    <div className="text-2xl font-bold">94.2%</div>
                    <div className="flex items-center">
                      <Badge variant="outline" className="text-xs font-normal text-secondary border-secondary">
                        +1.1%
                      </Badge>
                      <p className="text-xs text-muted-foreground ml-1">from last month</p>
                    </div>
                    <Button
                      variant="link"
                      size="sm"
                      className="p-0 h-auto mt-2 text-secondary"
                      onClick={handleViewEfficiencyTrends}
                    >
                      View efficiency trends
                    </Button>
                  </CardContent>
                </Card>

                <Card className="relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-transparent rounded-lg"></div>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
                    <CardTitle className="text-sm font-medium">Maintenance Cost</CardTitle>
                    <div className="rounded-full bg-orange-100 p-1 dark:bg-orange-900/20">
                      <LineChart className="h-4 w-4 text-orange-600" />
                    </div>
                  </CardHeader>
                  <CardContent className="relative">
                    <div className="text-2xl font-bold">$42,500</div>
                    <div className="flex items-center">
                      <Badge variant="outline" className="text-xs font-normal text-green-600 border-green-600">
                        -8%
                      </Badge>
                      <p className="text-xs text-muted-foreground ml-1">from last month</p>
                    </div>
                    <Button
                      variant="link"
                      size="sm"
                      className="p-0 h-auto mt-2 text-orange-600"
                      onClick={handleViewCostBreakdown}
                    >
                      View cost breakdown
                    </Button>
                  </CardContent>
                </Card>

                <Card className="relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-transparent rounded-lg"></div>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
                    <CardTitle className="text-sm font-medium">Downtime Hours</CardTitle>
                    <div className="rounded-full bg-red-100 p-1 dark:bg-red-900/20">
                      <FileText className="h-4 w-4 text-red-600" />
                    </div>
                  </CardHeader>
                  <CardContent className="relative">
                    <div className="text-2xl font-bold">124</div>
                    <div className="flex items-center">
                      <Badge variant="outline" className="text-xs font-normal text-green-600 border-green-600">
                        -12
                      </Badge>
                      <p className="text-xs text-muted-foreground ml-1">from last month</p>
                    </div>
                    <Button
                      variant="link"
                      size="sm"
                      className="p-0 h-auto mt-2 text-red-600"
                      onClick={handleViewDowntimeAnalysis}
                    >
                      View downtime analysis
                    </Button>
                  </CardContent>
                </Card>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Transformer Status by Region</CardTitle>
                        <CardDescription>Distribution of transformer status across regions</CardDescription>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={handleRefresh}>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Refresh
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleExport("pdf")}>
                            <Download className="mr-2 h-4 w-4" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={toggleFilters}>
                            <Filter className="mr-2 h-4 w-4" />
                            Filter
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => console.log('Sort clicked')}>
                            <ArrowUpDown className="mr-2 h-4 w-4" />
                            Sort
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="pl-2">
                    <TransformerStatusChart />
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Maintenance Performance</CardTitle>
                        <CardDescription>Maintenance completion rate and efficiency</CardDescription>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={handleRefresh}>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Refresh
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleExport("pdf")}>
                            <Download className="mr-2 h-4 w-4" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={toggleFilters}>
                            <Filter className="mr-2 h-4 w-4" />
                            Filter
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => console.log('Sort clicked')}>
                            <ArrowUpDown className="mr-2 h-4 w-4" />
                            Sort
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="pl-2">
                    <MaintenancePerformanceChart />
                  </CardContent>
                </Card>
              </div>

              <OverviewCharts />
            </TabsContent>

            <TabsContent value="distribution" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <CardTitle>Transformer Distribution Analysis</CardTitle>
                      <CardDescription>Comprehensive breakdown by voltage level, kVA rating, and customer type</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleExport("pdf")}
                        disabled={isExporting}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        {isExporting ? 'Exporting...' : 'Export'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={isRefreshing}
                      >
                        <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                        {isRefreshing ? 'Refreshing...' : 'Refresh'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <TransformerDistributionCharts />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="regional" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <CardTitle>Regional Analysis</CardTitle>
                      <CardDescription>Transformer distribution across regions and service centers</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select
                        value={selectedRegion}
                        onValueChange={handleRegionChange}
                      >
                        <SelectTrigger className="w-[160px]">
                          <SelectValue placeholder="Select Region" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Regions</SelectItem>
                          <SelectItem value="addis-ababa">Addis Ababa</SelectItem>
                          <SelectItem value="oromia">Oromia</SelectItem>
                          <SelectItem value="amhara">Amhara</SelectItem>
                          <SelectItem value="snnpr">SNNPR</SelectItem>
                          <SelectItem value="tigray">Tigray</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={toggleFilters}
                      >
                        <Filter className="mr-2 h-4 w-4" />
                        {showFilters ? 'Hide Filters' : 'Filter'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <RegionalAnalysisCharts />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="infrastructure" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <CardTitle>Infrastructure Analysis</CardTitle>
                      <CardDescription>Transformer distribution by feeders and substations</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select
                        value={selectedInfrastructure}
                        onValueChange={handleInfrastructureChange}
                      >
                        <SelectTrigger className="w-[160px]">
                          <SelectValue placeholder="Infrastructure Type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Infrastructure</SelectItem>
                          <SelectItem value="feeders">Feeders Only</SelectItem>
                          <SelectItem value="substations">Substations Only</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={toggleFilters}
                      >
                        <Filter className="mr-2 h-4 w-4" />
                        {showFilters ? 'Hide Filters' : 'Filter'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <InfrastructureAnalysisCharts />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="comprehensive" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <CardTitle>Comprehensive Transformer Report</CardTitle>
                      <CardDescription>Complete analysis including all distribution categories and metrics</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleExport("pdf")}
                        disabled={isExporting}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        {isExporting ? 'Exporting...' : 'Export Full Report'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handlePrint}
                        disabled={isPrinting}
                      >
                        <Printer className="mr-2 h-4 w-4" />
                        {isPrinting ? 'Printing...' : 'Print'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleShare}
                        disabled={isSharing}
                      >
                        <Share2 className="mr-2 h-4 w-4" />
                        {isSharing ? 'Sharing...' : 'Share'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ComprehensiveReportCharts />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <CardTitle>Performance Metrics</CardTitle>
                      <CardDescription>Detailed performance analysis of the transformer network</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="Search metrics..."
                        className="w-full sm:w-[200px]"
                        value={searchQuery}
                        onChange={(e) => handleSearchChange(e.target.value)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={toggleFilters}
                      >
                        <Filter className="mr-2 h-4 w-4" />
                        {showFilters ? 'Hide Filters' : 'Filter'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={isRefreshing}
                      >
                        <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                        {isRefreshing ? 'Refreshing...' : 'Refresh'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <PerformanceCharts />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="maintenance" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <CardTitle>Maintenance Reports</CardTitle>
                      <CardDescription>Detailed maintenance statistics and trends</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select
                        value={selectedMaintenanceType}
                        onValueChange={handleMaintenanceTypeChange}
                      >
                        <SelectTrigger className="w-[160px]">
                          <SelectValue placeholder="Maintenance Type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Types</SelectItem>
                          <SelectItem value="preventive">Preventive</SelectItem>
                          <SelectItem value="corrective">Corrective</SelectItem>
                          <SelectItem value="predictive">Predictive</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={toggleFilters}
                      >
                        <Filter className="mr-2 h-4 w-4" />
                        {showFilters ? 'Hide Filters' : 'Filter'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleExport("pdf")}
                        disabled={isExporting}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        {isExporting ? 'Exporting...' : 'Export'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <MaintenanceCharts />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="custom" className="space-y-4">
              <Tabs defaultValue="builder" className="space-y-4">
                <TabsList>
                  <TabsTrigger value="builder" className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Report Builder
                  </TabsTrigger>
                  <TabsTrigger value="saved" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Saved Reports
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="builder" className="space-y-4">
                  <CustomReportBuilder />
                </TabsContent>

                <TabsContent value="saved" className="space-y-4">
                  <SavedReports />
                </TabsContent>
              </Tabs>
            </TabsContent>
          </>
        )}
      </Tabs>
    </div>
  )
}

export function ReportsContent() {
  return (
    <ReportsProvider>
      <ReportsContentInner />
    </ReportsProvider>
  )
}
