/**
 * Test API Response Structure
 * Tests the transformer API endpoint to verify response structure
 */

const fetch = require('node-fetch');

async function testApiResponse() {
  try {
    console.log('🔄 Testing transformer API response...');
    
    const response = await fetch('http://localhost:3002/api/mysql/transformers');
    
    if (!response.ok) {
      console.error(`❌ API request failed: ${response.status} ${response.statusText}`);
      return;
    }
    
    const data = await response.json();
    
    console.log('📊 API Response Structure:');
    console.log(`  success: ${data.success}`);
    console.log(`  count: ${data.count}`);
    console.log(`  source: ${data.source}`);
    
    if (data.data) {
      console.log('  data:');
      console.log(`    transformers: ${data.data.transformers ? data.data.transformers.length : 'undefined'} items`);
      console.log(`    statistics: ${data.data.statistics ? 'present' : 'undefined'}`);
      
      if (data.data.transformers && data.data.transformers.length > 0) {
        const firstTransformer = data.data.transformers[0];
        console.log('\n📋 First transformer structure:');
        Object.keys(firstTransformer).forEach(key => {
          const value = firstTransformer[key];
          const type = typeof value;
          const preview = type === 'object' ? JSON.stringify(value).substring(0, 50) + '...' : value;
          console.log(`    ${key}: ${type} = ${preview}`);
        });
      }
    }
    
    console.log('\n✅ API test completed');
    
  } catch (error) {
    console.error('❌ Error testing API:', error);
  }
}

testApiResponse();
