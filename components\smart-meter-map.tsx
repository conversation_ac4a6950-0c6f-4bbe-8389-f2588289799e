"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/src/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import {
  MapIcon, Layers, Filter, Maximize2, Minimize2, Download,
  BarChart3, PieChart, Wifi, WifiOff, Battery, BatteryMedium,
  BatteryLow, Search, RefreshCw, Info, Locate, MapPin
} from "lucide-react"
import { SmartMeter } from "@/src/types/smart-meter"
import { MapboxMap } from "@/components/mapbox-map"
import { LeafletMap } from "@/components/leaflet-map"
import { MapLocation } from "@/src/services/map-service"
import { Badge } from "@/src/components/ui/badge"
import { Progress } from "@/src/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from "@/src/components/ui/dialog"
import { Label } from "@/src/components/ui/label"
import { Input } from "@/src/components/ui/input"
import { Switch } from "@/src/components/ui/switch"
import { useToast } from "@/src/components/ui/use-toast"

interface SmartMeterMapProps {
  meters?: SmartMeter[]
}

export function SmartMeterMap({ meters = [] }: SmartMeterMapProps) {
  const { toast } = useToast()
  const [mapType, setMapType] = useState<"mapbox" | "leaflet">("mapbox")
  const [mapStyle, setMapStyle] = useState<"light" | "dark" | "satellite" | "streets">("light")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [regionFilter, setRegionFilter] = useState<string>("all")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [filteredLocations, setFilteredLocations] = useState<MapLocation[]>([])
  const [selectedMeter, setSelectedMeter] = useState<SmartMeter | null>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [showHeatmap, setShowHeatmap] = useState(false)
  const [showClusters, setShowClusters] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [statistics, setStatistics] = useState({
    total: 0,
    connected: 0,
    disconnected: 0,
    maintenance: 0,
    tampered: 0,
    byRegion: {} as Record<string, number>,
    byType: {} as Record<string, number>
  })

  // Calculate statistics
  useEffect(() => {
    // Count meters by status
    const byStatus = {
      total: meters.length,
      connected: meters.filter(m => m.status === "Connected").length,
      disconnected: meters.filter(m => m.status === "Disconnected").length,
      maintenance: meters.filter(m => m.status === "Maintenance").length,
      tampered: meters.filter(m => m.status === "Tampered").length
    }

    // Count meters by region
    const byRegion: Record<string, number> = {}
    meters.forEach(meter => {
      const region = meter.location.region
      byRegion[region] = (byRegion[region] || 0) + 1
    })

    // Count meters by type
    const byType: Record<string, number> = {}
    meters.forEach(meter => {
      const type = meter.type
      byType[type] = (byType[type] || 0) + 1
    })

    setStatistics({
      ...byStatus,
      byRegion,
      byType
    })
  }, [meters])

  // Convert smart meters to map locations
  useEffect(() => {
    setIsLoading(true)

    try {
      let filtered = [...meters]

      // Apply status filter
      if (statusFilter !== "all") {
        filtered = filtered.filter(meter => meter.status === statusFilter)
      }

      // Apply region filter
      if (regionFilter !== "all") {
        filtered = filtered.filter(meter => meter.location.region === regionFilter)
      }

      // Apply type filter
      if (typeFilter !== "all") {
        filtered = filtered.filter(meter => meter.type === typeFilter)
      }

      // Apply search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        filtered = filtered.filter(meter =>
          meter.id.toLowerCase().includes(query) ||
          meter.serialNumber.toLowerCase().includes(query) ||
          meter.customer.name.toLowerCase().includes(query) ||
          meter.location.address.toLowerCase().includes(query)
        )
      }

      // Convert to map locations
      const locations: MapLocation[] = filtered.map(meter => ({
        id: meter.id,
        latitude: meter.location.latitude,
        longitude: meter.location.longitude,
        title: `${meter.id} - ${meter.customer.name}`,
        description: `${meter.type} meter in ${meter.location.address}`,
        status: meter.status,
        data: meter,
        weight: meter.status === "Tampered" ? 1 :
                meter.status === "Disconnected" ? 0.8 :
                meter.status === "Maintenance" ? 0.5 : 0.2
      }))

      setFilteredLocations(locations)
    } catch (error) {
      console.error("Error filtering meters:", error)
      toast({
        title: "Error",
        description: "Failed to filter smart meters. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }, [meters, statusFilter, regionFilter, typeFilter, searchQuery, toast])

  // Get unique regions from meters
  const regions = Array.from(new Set(meters.map(meter => meter.location.region)))

  // Get unique types from meters
  const types = Array.from(new Set(meters.map(meter => meter.type)))

  // Handle marker click
  const handleMarkerClick = (location: MapLocation) => {
    const meter = meters.find(m => m.id === location.id)
    setSelectedMeter(meter || null)
    setShowDetails(true)
  }

  // Handle fullscreen toggle
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Connected":
        return "bg-green-500 hover:bg-green-600"
      case "Disconnected":
        return "bg-yellow-500 hover:bg-yellow-600"
      case "Maintenance":
        return "bg-blue-500 hover:bg-blue-600"
      case "Tampered":
        return "bg-red-500 hover:bg-red-600"
      default:
        return "bg-gray-500 hover:bg-gray-600"
    }
  }

  // Handle refresh
  const handleRefresh = () => {
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Map Refreshed",
        description: "Smart meter map data has been refreshed."
      })
    }, 1000)
  }

  return (
    <div className={`space-y-4 ${isFullscreen ? 'fixed inset-0 z-50 p-4 bg-background' : ''}`}>
      <Card className={isFullscreen ? 'h-full flex flex-col' : ''}>
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-2">
              <div>
                <CardTitle>Smart Meter Map</CardTitle>
                <CardDescription>Geographic distribution of smart meters</CardDescription>
              </div>
              {isLoading && (
                <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full ml-2"></div>
              )}
            </div>
            <div className="flex items-center gap-2 mt-2 sm:mt-0">
              <Button
                variant="outline"
                size="icon"
                onClick={handleRefresh}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                <span className="sr-only">Refresh</span>
              </Button>

              <Button
                variant="outline"
                size="icon"
                onClick={toggleFullscreen}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
                <span className="sr-only">Toggle fullscreen</span>
              </Button>

              <Select value={mapType} onValueChange={(value: "mapbox" | "leaflet") => setMapType(value)}>
                <SelectTrigger className="w-[130px]">
                  <MapIcon className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Map Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="mapbox">Mapbox</SelectItem>
                  <SelectItem value="leaflet">Leaflet</SelectItem>
                </SelectContent>
              </Select>

              <Select value={mapStyle} onValueChange={(value: "light" | "dark" | "satellite" | "streets") => setMapStyle(value)}>
                <SelectTrigger className="w-[130px]">
                  <Layers className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Map Style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="satellite">Satellite</SelectItem>
                  <SelectItem value="streets">Streets</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent className={`${isFullscreen ? 'flex-1 overflow-hidden flex flex-col' : ''}`}>
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search meters..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-[160px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Connected">Connected</SelectItem>
                  <SelectItem value="Disconnected">Disconnected</SelectItem>
                  <SelectItem value="Maintenance">Maintenance</SelectItem>
                  <SelectItem value="Tampered">Tampered</SelectItem>
                </SelectContent>
              </Select>

              <Select value={regionFilter} onValueChange={setRegionFilter}>
                <SelectTrigger className="w-full sm:w-[160px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Regions</SelectItem>
                  {regions.map(region => (
                    <SelectItem key={region} value={region}>{region}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full sm:w-[160px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {types.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <Card className="bg-green-50 dark:bg-green-900/20">
              <CardContent className="p-4 flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-700 dark:text-green-300">Connected</p>
                  <p className="text-2xl font-bold">{statistics.connected}</p>
                  <p className="text-xs text-muted-foreground">
                    {statistics.total > 0 ? Math.round((statistics.connected / statistics.total) * 100) : 0}% of total
                  </p>
                </div>
                <Wifi className="h-8 w-8 text-green-600" />
              </CardContent>
            </Card>

            <Card className="bg-yellow-50 dark:bg-yellow-900/20">
              <CardContent className="p-4 flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-yellow-700 dark:text-yellow-300">Disconnected</p>
                  <p className="text-2xl font-bold">{statistics.disconnected}</p>
                  <p className="text-xs text-muted-foreground">
                    {statistics.total > 0 ? Math.round((statistics.disconnected / statistics.total) * 100) : 0}% of total
                  </p>
                </div>
                <WifiOff className="h-8 w-8 text-yellow-600" />
              </CardContent>
            </Card>

            <Card className="bg-blue-50 dark:bg-blue-900/20">
              <CardContent className="p-4 flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-700 dark:text-blue-300">Maintenance</p>
                  <p className="text-2xl font-bold">{statistics.maintenance}</p>
                  <p className="text-xs text-muted-foreground">
                    {statistics.total > 0 ? Math.round((statistics.maintenance / statistics.total) * 100) : 0}% of total
                  </p>
                </div>
                <BatteryMedium className="h-8 w-8 text-blue-600" />
              </CardContent>
            </Card>

            <Card className="bg-red-50 dark:bg-red-900/20">
              <CardContent className="p-4 flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-700 dark:text-red-300">Tampered</p>
                  <p className="text-2xl font-bold">{statistics.tampered}</p>
                  <p className="text-xs text-muted-foreground">
                    {statistics.total > 0 ? Math.round((statistics.tampered / statistics.total) * 100) : 0}% of total
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </CardContent>
            </Card>
          </div>

          <div className={`rounded-md overflow-hidden border ${isFullscreen ? 'flex-1' : 'h-[500px]'}`}>
            {mapType === "mapbox" ? (
              <MapboxMap
                locations={filteredLocations}
                mapStyle={mapStyle}
                onMarkerClick={handleMarkerClick}
                height="100%"
                showControls={true}
                clustered={showClusters}
              />
            ) : (
              <LeafletMap
                locations={filteredLocations}
                height="100%"
                onMarkerClick={handleMarkerClick}
                clustered={showClusters}
              />
            )}
          </div>

          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-xs">Connected</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span className="text-xs">Disconnected</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                <span className="text-xs">Maintenance</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span className="text-xs">Tampered</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <Switch
                  id="cluster-mode"
                  checked={showClusters}
                  onCheckedChange={setShowClusters}
                />
                <Label htmlFor="cluster-mode" className="text-xs">Cluster Mode</Label>
              </div>

              <div className="flex items-center gap-2">
                <Switch
                  id="heatmap-mode"
                  checked={showHeatmap}
                  onCheckedChange={setShowHeatmap}
                />
                <Label htmlFor="heatmap-mode" className="text-xs">Heatmap</Label>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-0">
          <p className="text-xs text-muted-foreground">
            Showing {filteredLocations.length} of {meters.length} smart meters
          </p>
        </CardFooter>
      </Card>

      {/* Smart Meter Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="sm:max-w-[700px]">
          {selectedMeter && (
            <>
              <DialogHeader>
                <DialogTitle>Smart Meter Details</DialogTitle>
                <DialogDescription>
                  Detailed information about smart meter {selectedMeter.id}
                </DialogDescription>
              </DialogHeader>

              <Tabs defaultValue="details" className="mt-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="customer">Customer</TabsTrigger>
                  <TabsTrigger value="technical">Technical</TabsTrigger>
                </TabsList>

                <TabsContent value="details" className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-muted-foreground">ID</Label>
                      <p className="font-medium">{selectedMeter.id}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Serial Number</Label>
                      <p className="font-medium">{selectedMeter.serialNumber}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Manufacturer</Label>
                      <p className="font-medium">{selectedMeter.manufacturer}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Model</Label>
                      <p className="font-medium">{selectedMeter.model}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Type</Label>
                      <p className="font-medium">{selectedMeter.type}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Status</Label>
                      <p>
                        <Badge className={getStatusColor(selectedMeter.status)}>
                          {selectedMeter.status}
                        </Badge>
                      </p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Installation Date</Label>
                      <p className="font-medium">{selectedMeter.installDate}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Last Reading</Label>
                      <p className="font-medium">{selectedMeter.lastReadingValue} kWh on {formatDate(selectedMeter.lastReading)}</p>
                    </div>
                  </div>

                  <div className="mt-4">
                    <Label className="text-muted-foreground">Location</Label>
                    <p className="font-medium">{selectedMeter.location.address}</p>
                    <p className="text-sm text-muted-foreground">
                      {selectedMeter.location.region}, {selectedMeter.location.serviceCenter}
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="customer" className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-muted-foreground">Customer Name</Label>
                      <p className="font-medium">{selectedMeter.customer.name}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Account Number</Label>
                      <p className="font-medium">{selectedMeter.customer.accountNumber}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Contact Phone</Label>
                      <p className="font-medium">{selectedMeter.customer.contactPhone}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Email</Label>
                      <p className="font-medium">{selectedMeter.customer.email || "N/A"}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Tariff Plan</Label>
                      <p className="font-medium">{selectedMeter.tariffPlan}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Billing Cycle</Label>
                      <p className="font-medium">{selectedMeter.billingCycle}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Payment Type</Label>
                      <p className="font-medium">{selectedMeter.prepaid ? "Prepaid" : "Postpaid"}</p>
                    </div>
                    {selectedMeter.prepaid && (
                      <div>
                        <Label className="text-muted-foreground">Balance</Label>
                        <p className="font-medium">{selectedMeter.balance?.toFixed(2)} ETB</p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="technical" className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-muted-foreground">Firmware Version</Label>
                      <p className="font-medium">{selectedMeter.firmwareVersion}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Last Communication</Label>
                      <p className="font-medium">{formatDate(selectedMeter.lastCommunication)}</p>
                    </div>

                    <div className="col-span-2">
                      <Label className="text-muted-foreground">Battery Level</Label>
                      <div className="flex items-center gap-2">
                        <Progress value={selectedMeter.batteryLevel} className="h-2" />
                        <span className="text-sm">{selectedMeter.batteryLevel}%</span>
                      </div>
                    </div>

                    <div className="col-span-2">
                      <Label className="text-muted-foreground">Signal Strength</Label>
                      <div className="flex items-center gap-2">
                        <Progress value={selectedMeter.signalStrength} className="h-2" />
                        <span className="text-sm">{selectedMeter.signalStrength}%</span>
                      </div>
                    </div>

                    <div className="col-span-2">
                      <Label className="text-muted-foreground">Coordinates</Label>
                      <p className="font-medium">
                        {selectedMeter.location.latitude}, {selectedMeter.location.longitude}
                      </p>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <DialogFooter>
                <Button variant="outline" onClick={() => setShowDetails(false)}>
                  Close
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
