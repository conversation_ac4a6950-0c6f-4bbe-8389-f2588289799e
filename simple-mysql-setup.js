/**
 * Simple MySQL Setup for dtms_eeu_db
 * 
 * This script creates basic tables and inserts sample data using simple SQL.
 */

const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db'
};

let connection;

async function initConnection() {
  connection = await mysql.createConnection(config);
  console.log('✅ MySQL connection established');
}

async function executeQuery(query, params = []) {
  try {
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('❌ Query error:', error.message);
    console.error('Query:', query.substring(0, 100) + '...');
    throw error;
  }
}

async function createSimpleTables() {
  console.log('🔄 Creating simple tables...');
  
  try {
    // Disable foreign key checks and strict mode
    await executeQuery('SET FOREIGN_KEY_CHECKS = 0');
    await executeQuery('SET sql_mode = ""');
    
    // Drop existing tables if they exist
    const tables = ['app_alerts', 'app_transformers', 'app_service_centers', 'app_regions', 'app_users'];
    for (const table of tables) {
      await executeQuery(`DROP TABLE IF EXISTS ${table}`);
    }
    
    // Create users table
    await executeQuery(`
      CREATE TABLE app_users (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        role VARCHAR(50) NOT NULL,
        is_active TINYINT(1) DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Users table created');
    
    // Create regions table
    await executeQuery(`
      CREATE TABLE app_regions (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(10) UNIQUE NOT NULL,
        description TEXT,
        lat DECIMAL(10, 8) NOT NULL,
        lng DECIMAL(11, 8) NOT NULL,
        population INT DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Regions table created');
    
    // Create service centers table
    await executeQuery(`
      CREATE TABLE app_service_centers (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(20) UNIQUE NOT NULL,
        region_id VARCHAR(36) NOT NULL,
        address TEXT,
        lat DECIMAL(10, 8) NOT NULL,
        lng DECIMAL(11, 8) NOT NULL,
        contact_phone VARCHAR(20),
        contact_email VARCHAR(255),
        manager_name VARCHAR(100),
        capacity INT DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Service centers table created');
    
    // Create transformers table
    await executeQuery(`
      CREATE TABLE app_transformers (
        id VARCHAR(36) PRIMARY KEY,
        serial_number VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(100) NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'operational',
        type VARCHAR(50) NOT NULL,
        manufacturer VARCHAR(100) NOT NULL,
        model VARCHAR(100) NOT NULL,
        capacity INT NOT NULL,
        voltage_primary DECIMAL(8, 2) NOT NULL,
        voltage_secondary DECIMAL(8, 2) NOT NULL,
        region_id VARCHAR(36) NOT NULL,
        service_center_id VARCHAR(36) NOT NULL,
        location_address TEXT,
        lat DECIMAL(10, 8) NOT NULL,
        lng DECIMAL(11, 8) NOT NULL,
        temperature DECIMAL(5, 2) DEFAULT 0,
        load_percentage DECIMAL(5, 2) DEFAULT 0,
        oil_level DECIMAL(5, 2) DEFAULT 0,
        health_index DECIMAL(5, 2) DEFAULT 0,
        installation_date DATE,
        last_maintenance_date DATE,
        next_maintenance_date DATE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Transformers table created');
    
    // Create alerts table
    await executeQuery(`
      CREATE TABLE app_alerts (
        id VARCHAR(36) PRIMARY KEY,
        transformer_id VARCHAR(36),
        type VARCHAR(50) NOT NULL,
        severity VARCHAR(20) NOT NULL,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        is_resolved TINYINT(1) DEFAULT 0,
        resolved_at DATETIME NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Alerts table created');
    
    // Re-enable foreign key checks
    await executeQuery('SET FOREIGN_KEY_CHECKS = 1');
    
    console.log('✅ All tables created successfully');
    
  } catch (error) {
    console.error('❌ Table creation failed:', error.message);
    throw error;
  }
}

async function insertSampleData() {
  console.log('🔄 Inserting sample data...');
  
  try {
    // Insert users
    const users = [
      {
        id: uuidv4(),
        email: '<EMAIL>',
        passwordHash: 'hashed_password_admin',
        firstName: 'System',
        lastName: 'Administrator',
        role: 'super_admin'
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        passwordHash: 'hashed_password_manager',
        firstName: 'Asset',
        lastName: 'Manager',
        role: 'national_asset_manager'
      }
    ];
    
    for (const user of users) {
      await executeQuery(`
        INSERT INTO app_users (id, email, password_hash, first_name, last_name, role)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [user.id, user.email, user.passwordHash, user.firstName, user.lastName, user.role]);
    }
    console.log(`✅ Inserted ${users.length} users`);
    
    // Insert regions
    const regions = [
      { id: uuidv4(), name: 'Addis Ababa', code: 'AA', description: 'Capital city', lat: 9.0320, lng: 38.7469, population: 3500000 },
      { id: uuidv4(), name: 'Oromia', code: 'OR', description: 'Largest region', lat: 8.5000, lng: 39.5000, population: 35000000 },
      { id: uuidv4(), name: 'Amhara', code: 'AM', description: 'Northern region', lat: 11.5000, lng: 37.5000, population: 21000000 },
      { id: uuidv4(), name: 'SNNPR', code: 'SN', description: 'Southern region', lat: 6.5000, lng: 37.0000, population: 15000000 },
      { id: uuidv4(), name: 'Tigray', code: 'TI', description: 'Northern region', lat: 14.0000, lng: 38.5000, population: 5000000 }
    ];
    
    for (const region of regions) {
      await executeQuery(`
        INSERT INTO app_regions (id, name, code, description, lat, lng, population)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [region.id, region.name, region.code, region.description, region.lat, region.lng, region.population]);
    }
    console.log(`✅ Inserted ${regions.length} regions`);
    
    // Insert service centers
    const serviceCenters = [];
    regions.forEach((region, index) => {
      serviceCenters.push({
        id: uuidv4(),
        name: `${region.name} Service Center`,
        code: `${region.code}-SC01`,
        regionId: region.id,
        address: `Main Service Center, ${region.name}, Ethiopia`,
        lat: region.lat + (Math.random() - 0.5) * 0.1,
        lng: region.lng + (Math.random() - 0.5) * 0.1,
        contactPhone: `+251-${10 + index}-123-456${index}`,
        contactEmail: `${region.code.toLowerCase()}.<EMAIL>`,
        managerName: `Manager ${index + 1}`,
        capacity: 50 + index * 20
      });
    });
    
    for (const center of serviceCenters) {
      await executeQuery(`
        INSERT INTO app_service_centers (id, name, code, region_id, address, lat, lng, contact_phone, contact_email, manager_name, capacity)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [center.id, center.name, center.code, center.regionId, center.address, center.lat, center.lng, center.contactPhone, center.contactEmail, center.managerName, center.capacity]);
    }
    console.log(`✅ Inserted ${serviceCenters.length} service centers`);
    
    // Insert transformers
    const transformers = [];
    const statuses = ['operational', 'warning', 'critical', 'maintenance'];
    const types = ['Distribution', 'Power', 'Pad-mounted', 'Pole-mounted'];
    const manufacturers = ['ABB', 'Siemens', 'Schneider Electric', 'General Electric'];
    
    for (let i = 0; i < 30; i++) {
      const region = regions[i % regions.length];
      const serviceCenter = serviceCenters.find(sc => sc.regionId === region.id);
      
      transformers.push({
        id: uuidv4(),
        serialNumber: `EEU-${region.code}-${1000 + i}`,
        name: `${region.name} Transformer ${Math.floor(i / regions.length) + 1}`,
        status: statuses[i % statuses.length],
        type: types[i % types.length],
        manufacturer: manufacturers[i % manufacturers.length],
        model: `Model-${100 + i}`,
        capacity: [100, 250, 500, 1000][i % 4],
        voltagePrimary: [11, 15, 33][i % 3],
        voltageSecondary: 400,
        regionId: region.id,
        serviceCenterId: serviceCenter.id,
        locationAddress: `${region.name}, Ethiopia`,
        lat: region.lat + (Math.random() - 0.5) * 0.2,
        lng: region.lng + (Math.random() - 0.5) * 0.2,
        temperature: 30 + Math.random() * 40,
        loadPercentage: 40 + Math.random() * 50,
        oilLevel: 80 + Math.random() * 20,
        healthIndex: 60 + Math.random() * 40,
        installationDate: new Date(2019 + (i % 4), 0, 1).toISOString().split('T')[0],
        lastMaintenanceDate: new Date(2024, i % 12, 1).toISOString().split('T')[0],
        nextMaintenanceDate: new Date(2024, (i % 12) + 6, 1).toISOString().split('T')[0]
      });
    }
    
    for (const transformer of transformers) {
      await executeQuery(`
        INSERT INTO app_transformers (
          id, serial_number, name, status, type, manufacturer, model, capacity,
          voltage_primary, voltage_secondary, region_id, service_center_id,
          location_address, lat, lng, temperature, load_percentage, oil_level,
          health_index, installation_date, last_maintenance_date, next_maintenance_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        transformer.id, transformer.serialNumber, transformer.name, transformer.status,
        transformer.type, transformer.manufacturer, transformer.model, transformer.capacity,
        transformer.voltagePrimary, transformer.voltageSecondary, transformer.regionId,
        transformer.serviceCenterId, transformer.locationAddress, transformer.lat, transformer.lng,
        transformer.temperature, transformer.loadPercentage, transformer.oilLevel,
        transformer.healthIndex, transformer.installationDate, transformer.lastMaintenanceDate,
        transformer.nextMaintenanceDate
      ]);
    }
    console.log(`✅ Inserted ${transformers.length} transformers`);
    
    // Insert alerts
    const alerts = [];
    const alertTypes = ['temperature', 'load', 'oil_level', 'maintenance'];
    const severities = ['low', 'medium', 'high', 'critical'];
    
    for (let i = 0; i < 20; i++) {
      const transformer = transformers[i % transformers.length];
      const alertType = alertTypes[i % alertTypes.length];
      const severity = severities[i % severities.length];
      
      alerts.push({
        id: uuidv4(),
        transformerId: transformer.id,
        type: alertType,
        severity: severity,
        title: `${alertType.charAt(0).toUpperCase() + alertType.slice(1)} Alert`,
        message: `${severity.charAt(0).toUpperCase() + severity.slice(1)} ${alertType} alert for transformer ${transformer.serialNumber}`,
        isResolved: i % 4 === 0
      });
    }
    
    for (const alert of alerts) {
      await executeQuery(`
        INSERT INTO app_alerts (id, transformer_id, type, severity, title, message, is_resolved)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [alert.id, alert.transformerId, alert.type, alert.severity, alert.title, alert.message, alert.isResolved]);
    }
    console.log(`✅ Inserted ${alerts.length} alerts`);
    
    console.log('✅ Sample data insertion completed');
    
  } catch (error) {
    console.error('❌ Sample data insertion failed:', error.message);
    throw error;
  }
}

async function runSetup() {
  console.log('🚀 Starting Simple MySQL Setup for dtms_eeu_db');
  console.log('===============================================');
  
  const startTime = Date.now();
  
  try {
    await initConnection();
    await createSimpleTables();
    await insertSampleData();
    
    // Verify setup
    console.log('🔍 Verifying setup...');
    
    const counts = {
      users: (await executeQuery('SELECT COUNT(*) as count FROM app_users'))[0].count,
      regions: (await executeQuery('SELECT COUNT(*) as count FROM app_regions'))[0].count,
      service_centers: (await executeQuery('SELECT COUNT(*) as count FROM app_service_centers'))[0].count,
      transformers: (await executeQuery('SELECT COUNT(*) as count FROM app_transformers'))[0].count,
      alerts: (await executeQuery('SELECT COUNT(*) as count FROM app_alerts'))[0].count
    };
    
    console.log('📊 Setup Results:');
    console.log(`   Users: ${counts.users}`);
    console.log(`   Regions: ${counts.regions}`);
    console.log(`   Service Centers: ${counts.service_centers}`);
    console.log(`   Transformers: ${counts.transformers}`);
    console.log(`   Alerts: ${counts.alerts}`);
    
    // Show sample data
    console.log('\n📋 Sample transformer data:');
    const sampleTransformers = await executeQuery(`
      SELECT t.serial_number, t.name, t.status, r.name as region_name, t.capacity
      FROM app_transformers t
      JOIN app_regions r ON t.region_id = r.id
      LIMIT 5
    `);
    
    sampleTransformers.forEach(transformer => {
      console.log(`   ${transformer.serial_number}: ${transformer.name} (${transformer.status}) - ${transformer.region_name} - ${transformer.capacity}kVA`);
    });
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`\n⏱️  Setup completed in ${duration.toFixed(2)} seconds`);
    console.log('🎉 MySQL setup successful!');
    console.log('');
    console.log('✅ Database dtms_eeu_db now contains application tables with "app_" prefix');
    console.log('✅ Ready for application integration');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

runSetup().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
