"use client"

import { AuthProvider } from "@/src/features/auth/context/auth-context"
import { AlertProvider } from "@/src/contexts/alert-context"
import { SidebarProvider } from "@/src/components/layout/sidebar-provider"
import { TransformerProvider } from "@/src/contexts/transformer-context"
import { MaintenanceProvider } from "@/src/contexts/maintenance-context"
import { Toaster } from "@/src/components/ui/toaster"

export default function ProvidersWrapper({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      <AlertProvider>
        <SidebarProvider>
          <TransformerProvider>
            <MaintenanceProvider>
              {children}
              <Toaster />
            </MaintenanceProvider>
          </TransformerProvider>
        </SidebarProvider>
      </AlertProvider>
    </AuthProvider>
  )
}
