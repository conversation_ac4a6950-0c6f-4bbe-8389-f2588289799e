"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadialBarChart,
  RadialBar,
  Legend,
  ComposedChart
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  MoreVertical, 
  Download, 
  Maximize2,
  RefreshCw
} from 'lucide-react'

const MODERN_COLORS = {
  primary: '#3b82f6',
  secondary: '#8b5cf6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#06b6d4',
  purple: '#8b5cf6',
  pink: '#ec4899',
  indigo: '#6366f1',
  emerald: '#10b981',
  orange: '#f97316',
  teal: '#14b8a6'
}

interface ModernChartCardProps {
  title: string
  subtitle?: string
  children: React.ReactNode
  actions?: React.ReactNode
  fullWidth?: boolean
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down' | 'stable'
  }
}

export function ModernChartCard({ 
  title, 
  subtitle, 
  children, 
  actions, 
  fullWidth = false,
  trend 
}: ModernChartCardProps) {
  return (
    <Card className={`${fullWidth ? 'col-span-full' : ''} border-0 shadow-xl bg-white/95 backdrop-blur-sm hover:shadow-2xl transition-all duration-500`}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-2">
              {title}
              {trend && (
                <Badge 
                  variant="secondary" 
                  className={`ml-2 ${
                    trend.direction === 'up' ? 'bg-emerald-100 text-emerald-700' :
                    trend.direction === 'down' ? 'bg-red-100 text-red-700' :
                    'bg-gray-100 text-gray-700'
                  }`}
                >
                  {trend.direction === 'up' ? <TrendingUp className="h-3 w-3 mr-1" /> :
                   trend.direction === 'down' ? <TrendingDown className="h-3 w-3 mr-1" /> : null}
                  {trend.value > 0 ? '+' : ''}{trend.value}% {trend.label}
                </Badge>
              )}
            </CardTitle>
            {subtitle && (
              <p className="text-sm text-gray-500">{subtitle}</p>
            )}
          </div>
          {actions && (
            <div className="flex items-center gap-2">
              {actions}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {children}
      </CardContent>
    </Card>
  )
}

interface StatusPieChartProps {
  data: Array<{
    name: string
    value: number
    color: string
  }>
  title: string
}

export function StatusPieChart({ data, title }: StatusPieChartProps) {
  const total = data.reduce((sum, item) => sum + item.value, 0)
  
  return (
    <ModernChartCard 
      title={title}
      subtitle={`Total: ${total} transformers`}
      actions={
        <>
          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </>
      }
    >
      <div className="relative">
        <ResponsiveContainer width="100%" height={350}>
          <PieChart>
            <defs>
              {data.map((entry, index) => (
                <linearGradient key={index} id={`gradient-${index}`} x1="0" y1="0" x2="1" y2="1">
                  <stop offset="0%" stopColor={entry.color} stopOpacity={0.8} />
                  <stop offset="100%" stopColor={entry.color} stopOpacity={1} />
                </linearGradient>
              ))}
            </defs>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={70}
              outerRadius={120}
              paddingAngle={3}
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={`url(#gradient-${index})`}
                  stroke="white"
                  strokeWidth={2}
                />
              ))}
            </Pie>
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                border: 'none', 
                borderRadius: '12px', 
                boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                backdropFilter: 'blur(10px)'
              }} 
            />
            <Legend 
              verticalAlign="bottom" 
              height={36}
              iconType="circle"
              wrapperStyle={{ paddingTop: '20px' }}
            />
          </PieChart>
        </ResponsiveContainer>
        
        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900">{total}</div>
            <div className="text-sm text-gray-500">Total</div>
          </div>
        </div>
      </div>
    </ModernChartCard>
  )
}

interface PerformanceTrendChartProps {
  data: Array<{
    time: string
    efficiency: number
    load: number
    temperature: number
  }>
  title: string
}

export function PerformanceTrendChart({ data, title }: PerformanceTrendChartProps) {
  return (
    <ModernChartCard 
      title={title}
      subtitle="Real-time performance monitoring"
      fullWidth
      trend={{
        value: 2.3,
        label: "vs yesterday",
        direction: "up"
      }}
      actions={
        <>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Maximize2 className="h-4 w-4" />
          </Button>
        </>
      }
    >
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id="efficiencyGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={MODERN_COLORS.success} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={MODERN_COLORS.success} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="loadGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={MODERN_COLORS.primary} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={MODERN_COLORS.primary} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="temperatureGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={MODERN_COLORS.warning} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={MODERN_COLORS.warning} stopOpacity={0.1}/>
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" strokeWidth={1} />
          <XAxis 
            dataKey="time" 
            stroke="#64748b" 
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <YAxis 
            stroke="#64748b" 
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip 
            contentStyle={{ 
              backgroundColor: 'rgba(255, 255, 255, 0.95)', 
              border: 'none', 
              borderRadius: '12px', 
              boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
              backdropFilter: 'blur(10px)'
            }} 
          />
          <Area 
            type="monotone" 
            dataKey="efficiency" 
            stroke={MODERN_COLORS.success} 
            fillOpacity={1} 
            fill="url(#efficiencyGradient)" 
            strokeWidth={3}
            dot={{ fill: MODERN_COLORS.success, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: MODERN_COLORS.success, strokeWidth: 2 }}
          />
          <Line 
            type="monotone" 
            dataKey="load" 
            stroke={MODERN_COLORS.primary} 
            strokeWidth={3}
            dot={{ fill: MODERN_COLORS.primary, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: MODERN_COLORS.primary, strokeWidth: 2 }}
          />
          <Bar 
            dataKey="temperature" 
            fill="url(#temperatureGradient)"
            radius={[2, 2, 0, 0]}
            opacity={0.7}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </ModernChartCard>
  )
}

interface RegionalBarChartProps {
  data: Array<{
    name: string
    transformers: number
    efficiency: number
    code: string
  }>
  title: string
}

export function RegionalBarChart({ data, title }: RegionalBarChartProps) {
  return (
    <ModernChartCard 
      title={title}
      subtitle="Performance across Ethiopian regions"
      actions={
        <Button variant="ghost" size="sm">
          <MoreVertical className="h-4 w-4" />
        </Button>
      }
    >
      <ResponsiveContainer width="100%" height={350}>
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor={MODERN_COLORS.primary} stopOpacity={0.8} />
              <stop offset="100%" stopColor={MODERN_COLORS.indigo} stopOpacity={1} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" strokeWidth={1} />
          <XAxis 
            dataKey="code" 
            stroke="#64748b" 
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <YAxis 
            stroke="#64748b" 
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip 
            contentStyle={{ 
              backgroundColor: 'rgba(255, 255, 255, 0.95)', 
              border: 'none', 
              borderRadius: '12px', 
              boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
              backdropFilter: 'blur(10px)'
            }} 
          />
          <Bar 
            dataKey="transformers" 
            fill="url(#barGradient)" 
            radius={[6, 6, 0, 0]}
            stroke="white"
            strokeWidth={1}
          />
        </BarChart>
      </ResponsiveContainer>
    </ModernChartCard>
  )
}

interface SystemHealthRadialProps {
  data: Array<{
    name: string
    value: number
    fill: string
  }>
  title: string
}

export function SystemHealthRadial({ data, title }: SystemHealthRadialProps) {
  return (
    <ModernChartCard 
      title={title}
      subtitle="Overall system performance indicators"
    >
      <ResponsiveContainer width="100%" height={300}>
        <RadialBarChart 
          cx="50%" 
          cy="50%" 
          innerRadius="20%" 
          outerRadius="90%" 
          data={data}
          startAngle={90}
          endAngle={-270}
        >
          <RadialBar 
            dataKey="value" 
            cornerRadius={10} 
            fill="#8884d8"
            stroke="white"
            strokeWidth={2}
          />
          <Tooltip 
            contentStyle={{ 
              backgroundColor: 'rgba(255, 255, 255, 0.95)', 
              border: 'none', 
              borderRadius: '12px', 
              boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
              backdropFilter: 'blur(10px)'
            }} 
          />
          <Legend 
            iconSize={12}
            layout="vertical"
            verticalAlign="middle"
            align="right"
            wrapperStyle={{ paddingLeft: '20px' }}
          />
        </RadialBarChart>
      </ResponsiveContainer>
    </ModernChartCard>
  )
}
