/**
 * API Utility Functions
 * Centralized API calling functions with error handling
 */

import { ApiResponse, PaginatedResponse } from '@/src/types'

/**
 * Base API configuration
 */
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ''

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

/**
 * Generic API fetch function with error handling
 */
async function apiFetch<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const url = `${API_BASE_URL}${endpoint}`
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    }

    const response = await fetch(url, { ...defaultOptions, ...options })
    
    if (!response.ok) {
      throw new ApiError(
        `HTTP error! status: ${response.status}`,
        response.status,
        response
      )
    }

    const data = await response.json()
    return data
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    
    throw new ApiError(
      error instanceof Error ? error.message : 'Unknown API error',
      0
    )
  }
}

/**
 * GET request
 */
export async function apiGet<T = any>(
  endpoint: string,
  params?: Record<string, string | number | boolean>
): Promise<ApiResponse<T>> {
  let url = endpoint
  
  if (params) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })
    
    if (searchParams.toString()) {
      url += `?${searchParams.toString()}`
    }
  }
  
  return apiFetch<T>(url, { method: 'GET' })
}

/**
 * POST request
 */
export async function apiPost<T = any>(
  endpoint: string,
  data?: any
): Promise<ApiResponse<T>> {
  return apiFetch<T>(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * PUT request
 */
export async function apiPut<T = any>(
  endpoint: string,
  data?: any
): Promise<ApiResponse<T>> {
  return apiFetch<T>(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * DELETE request
 */
export async function apiDelete<T = any>(
  endpoint: string
): Promise<ApiResponse<T>> {
  return apiFetch<T>(endpoint, { method: 'DELETE' })
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error: unknown): string {
  if (error instanceof ApiError) {
    switch (error.status) {
      case 400:
        return 'Bad request. Please check your input.'
      case 401:
        return 'Unauthorized. Please log in again.'
      case 403:
        return 'Forbidden. You do not have permission to perform this action.'
      case 404:
        return 'Resource not found.'
      case 500:
        return 'Internal server error. Please try again later.'
      default:
        return error.message || 'An unexpected error occurred.'
    }
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  return 'An unknown error occurred.'
}

/**
 * Retry API calls with exponential backoff
 */
export async function apiRetry<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<ApiResponse<T>> {
  let lastError: unknown
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall()
    } catch (error) {
      lastError = error
      
      if (attempt === maxRetries) {
        break
      }
      
      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}

/**
 * Build query parameters from filter object
 */
export function buildQueryParams(filters: Record<string, any>): string {
  const params = new URLSearchParams()
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        if (value.length > 0) {
          params.set(key, value.join(','))
        }
      } else {
        params.set(key, String(value))
      }
    }
  })
  
  return params.toString()
}

/**
 * Cache API responses for a specified duration
 */
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

export async function apiGetCached<T = any>(
  endpoint: string,
  params?: Record<string, string | number | boolean>,
  ttl: number = 30000 // 30 seconds default
): Promise<ApiResponse<T>> {
  const cacheKey = `${endpoint}?${buildQueryParams(params || {})}`
  const cached = cache.get(cacheKey)
  
  if (cached && Date.now() - cached.timestamp < cached.ttl) {
    return cached.data
  }
  
  const response = await apiGet<T>(endpoint, params)
  
  if (response.success) {
    cache.set(cacheKey, {
      data: response,
      timestamp: Date.now(),
      ttl
    })
  }
  
  return response
}

/**
 * Clear API cache
 */
export function clearApiCache(pattern?: string): void {
  if (pattern) {
    const regex = new RegExp(pattern)
    for (const key of cache.keys()) {
      if (regex.test(key)) {
        cache.delete(key)
      }
    }
  } else {
    cache.clear()
  }
}
