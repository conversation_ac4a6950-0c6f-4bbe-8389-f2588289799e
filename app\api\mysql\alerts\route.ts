/**
 * MySQL Alerts API Route
 * 
 * This API route handles alert data requests from MySQL database.
 */

import { NextRequest, NextResponse } from 'next/server';
import { MySQLServerService } from '@/src/lib/mysql-server';

export async function GET(request: NextRequest) {
  try {
    console.log('🚨 API: Fetching alerts from MySQL...');
    
    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();
    
    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        { 
          error: 'MySQL connection failed',
          fallback: true 
        },
        { status: 503 }
      );
    }
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const severity = searchParams.get('severity');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    // Get alerts data
    const alerts = await MySQLServerService.getAlerts({
      severity,
      status,
      limit,
      offset
    });
    
    console.log(`✅ API: ${alerts.length} alerts fetched successfully from MySQL`);
    
    return NextResponse.json({
      success: true,
      data: alerts,
      count: alerts.length,
      source: 'mysql'
    });
    
  } catch (error) {
    console.error('❌ API: Error fetching alerts:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch alerts',
        message: error instanceof Error ? error.message : 'Unknown error',
        fallback: true
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🚨 API: Creating new alert...');
    
    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();
    
    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        { 
          error: 'MySQL connection failed',
          fallback: true 
        },
        { status: 503 }
      );
    }
    
    const alertData = await request.json();
    
    // Create new alert
    const newAlert = await MySQLServerService.createAlert(alertData);
    
    console.log('✅ API: Alert created successfully');
    
    return NextResponse.json({
      success: true,
      data: newAlert,
      source: 'mysql'
    });
    
  } catch (error) {
    console.error('❌ API: Error creating alert:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to create alert',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    console.log('🚨 API: Updating alert...');
    
    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();
    
    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        { 
          error: 'MySQL connection failed',
          fallback: true 
        },
        { status: 503 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    const alertId = searchParams.get('id');
    const updateData = await request.json();
    
    if (!alertId) {
      return NextResponse.json(
        { error: 'Alert ID is required' },
        { status: 400 }
      );
    }
    
    // Update alert
    const updatedAlert = await MySQLServerService.updateAlert(alertId, updateData);
    
    console.log('✅ API: Alert updated successfully');
    
    return NextResponse.json({
      success: true,
      data: updatedAlert,
      source: 'mysql'
    });
    
  } catch (error) {
    console.error('❌ API: Error updating alert:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to update alert',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
