"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Edit, Trash, Building, Plus } from "lucide-react"
import { useAuth } from "@/src/features/auth/context/auth-context"
import { useToast } from "@/src/components/ui/use-toast"
import { ServiceCenterDialog } from "@/components/service-center-dialog"

interface RegionServiceCentersProps {
  regionId: string
  regionName: string
  onServiceCenterUpdated?: () => void
}

export function RegionServiceCenters({ regionId, regionName, onServiceCenterUpdated }: RegionServiceCentersProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedServiceCenter, setSelectedServiceCenter] = useState<any | null>(null)
  const { user: currentUser } = useAuth()
  const { toast } = useToast()

  // Mock service centers data - in a real app, this would come from an API
  // Filter to only show service centers for the given region
  const serviceCenters = [
    {
      id: "sc-001",
      name: "Bole Service Center",
      regionId: "region-001",
      regionName: "Addis Ababa",
      address: "Bole Road, Addis Ababa",
      managerName: "Abebe Kebede",
      contactPhone: "+251911234567",
      transformers: 245,
      technicians: 12,
      status: "active",
    },
    {
      id: "sc-002",
      name: "Kirkos Service Center",
      regionId: "region-001",
      regionName: "Addis Ababa",
      address: "Kirkos Sub-city, Addis Ababa",
      managerName: "Tigist Haile",
      contactPhone: "+251922345678",
      transformers: 198,
      technicians: 8,
      status: "active",
    },
    {
      id: "sc-003",
      name: "Adama Service Center",
      regionId: "region-002",
      regionName: "Oromia",
      address: "Main Street, Adama",
      managerName: "Dawit Mengistu",
      contactPhone: "+251933456789",
      transformers: 156,
      technicians: 6,
      status: "active",
    },
    {
      id: "sc-004",
      name: "Bahir Dar Service Center",
      regionId: "region-003",
      regionName: "Amhara",
      address: "Lake Tana Road, Bahir Dar",
      managerName: "Hiwot Tadesse",
      contactPhone: "+251944567890",
      transformers: 132,
      technicians: 5,
      status: "active",
    },
    {
      id: "sc-005",
      name: "Mekelle Service Center",
      regionId: "region-004",
      regionName: "Tigray",
      address: "Central Avenue, Mekelle",
      managerName: "Solomon Bekele",
      contactPhone: "+251955678901",
      transformers: 87,
      technicians: 4,
      status: "inactive",
    },
    {
      id: "sc-006",
      name: "Hawassa Service Center",
      regionId: "region-006",
      regionName: "Sidama",
      address: "Lake View Road, Hawassa",
      managerName: "Meron Alemu",
      contactPhone: "+251966789012",
      transformers: 112,
      technicians: 5,
      status: "active",
    },
  ].filter(sc => sc.regionId === regionId)

  // Get unique regions for the filter
  const regions = [
    { id: "region-001", name: "Addis Ababa" },
    { id: "region-002", name: "Oromia" },
    { id: "region-003", name: "Amhara" },
    { id: "region-004", name: "Tigray" },
    { id: "region-005", name: "SNNPR" },
    { id: "region-006", name: "Sidama" },
  ]

  const handleEditServiceCenter = (serviceCenter: any) => {
    setSelectedServiceCenter(serviceCenter)
    setIsDialogOpen(true)
  }

  const handleDeleteServiceCenter = (serviceCenter: any) => {
    // In a real app, this would call an API to delete the service center
    toast({
      title: "Service Center deleted",
      description: `${serviceCenter.name} has been deleted.`,
    })
    if (onServiceCenterUpdated) {
      onServiceCenterUpdated()
    }
  }

  const handleServiceCenterSave = (serviceCenter: any) => {
    // In a real app, this would call an API to save the service center
    toast({
      title: selectedServiceCenter ? "Service Center updated" : "Service Center created",
      description: `${serviceCenter.name} has been ${selectedServiceCenter ? "updated" : "created"}.`,
    })
    setIsDialogOpen(false)
    if (onServiceCenterUpdated) {
      onServiceCenterUpdated()
    }
  }

  // Check if user has permission to manage service centers
  const canManageServiceCenters =
    currentUser?.role === "super_admin" ||
    currentUser?.role === "national_asset_manager" ||
    currentUser?.role === "regional_admin"

  const handleAddServiceCenter = () => {
    // Create a new service center with the region pre-selected
    setSelectedServiceCenter({
      regionId: regionId,
      regionName: regionName,
      status: "active"
    })
    setIsDialogOpen(true)
  }

  return (
    <div className="mt-2">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-medium text-muted-foreground">Service Centers in {regionName}</h3>
        {canManageServiceCenters && (
          <Button size="sm" variant="outline" onClick={handleAddServiceCenter}>
            <Plus className="mr-2 h-3 w-3" />
            Add Service Center
          </Button>
        )}
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Service Center</TableHead>
              <TableHead>Manager</TableHead>
              <TableHead>Transformers</TableHead>
              <TableHead>Technicians</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {serviceCenters.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-12 text-center">
                  No service centers found for this region.
                </TableCell>
              </TableRow>
            ) : (
              serviceCenters.map((sc) => (
                <TableRow key={sc.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Building className="h-4 w-4 text-teal-600" />
                      <div>
                        <p className="font-medium">{sc.name}</p>
                        <p className="text-xs text-muted-foreground">{sc.address}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p>{sc.managerName}</p>
                      <p className="text-xs text-muted-foreground">{sc.contactPhone}</p>
                    </div>
                  </TableCell>
                  <TableCell>{sc.transformers}</TableCell>
                  <TableCell>{sc.technicians}</TableCell>
                  <TableCell>
                    <Badge
                      variant={sc.status === "active" ? "default" : "outline"}
                      className={sc.status === "active" ? "bg-green-500 hover:bg-green-600" : ""}
                    >
                      {sc.status === "active" ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon" onClick={() => handleEditServiceCenter(sc)}>
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      {canManageServiceCenters && (
                        <Button variant="ghost" size="icon" onClick={() => handleDeleteServiceCenter(sc)}>
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <ServiceCenterDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        serviceCenter={selectedServiceCenter}
        onSave={handleServiceCenterSave}
        regions={regions}
        currentUserRole={currentUser?.role}
        currentUserRegionId={currentUser?.regionId}
      />
    </div>
  )
}
