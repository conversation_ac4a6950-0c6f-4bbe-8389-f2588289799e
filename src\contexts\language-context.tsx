"use client"

import React, { createContext, useContext, useState, useEffect } from "react"

// Define available languages
export type Language = "en" | "am" | "ti" | "or"

// Define language names for display
export const languageNames: Record<Language, string> = {
  en: "English",
  am: "አማርኛ (Amharic)",
  ti: "ትግርኛ (T<PERSON><PERSON>ya)",
  or: "Afaan <PERSON>"
}

// Define language context type
type LanguageContextType = {
  language: Language
  setLanguage: (language: Language) => void
  t: (key: string) => string
}

// Create context with default values
const LanguageContext = createContext<LanguageContextType>({
  language: "en",
  setLanguage: () => {},
  t: (key: string) => key
})

// Define translations
const translations: Record<Language, Record<string, string>> = {
  en: {
    // Dashboard
    "dashboard": "Dashboard",
    "welcome": "Welcome",
    "last_updated": "Last updated",
    "total_transformers": "Total Transformers",
    "healthy_status": "Healthy Status",
    "active_outages": "Active Outages",
    "critical_alerts": "Critical Alerts",
    "view_inventory": "View inventory",
    "view_outage_map": "View outage map",
    "view_all_alerts": "View all alerts",
    "from_last_month": "from last month",
    "from_last_week": "from last week",

    // Navigation
    "transformers": "Transformers",
    "map_view": "Map View",
    "maintenance": "Maintenance",
    "alerts": "Alerts",
    "reports": "Reports",
    "smart_meters": "Smart Meters",
    "settings": "Settings",

    // Header
    "search": "Search...",
    "language": "Language",
    "notifications": "Notifications",
    "help_support": "Help & Support",
    "quick_actions": "Quick Actions",
    "eeu_logo_alt": "Ethiopian Electric Utility Logo",
    "eeu_name": "Ethiopian Electric Utility",
    "system_name": "Transformer Management System",
    "language_changed": "Language Changed",
    "language_set_as_default": "is now set as your default language",

    // Common actions
    "refresh": "Refresh",
    "export": "Export",
    "filter": "Filter",
    "search_transformers": "Search transformers...",
    "schedule_maintenance": "Schedule Maintenance",
    "view_all": "View All",
    "no_results_found": "No results found",
    "no_results_for": "No results found for",
    "try_different_search": "Try a different search term",
    "search_system": "Search the system",
    "start_typing": "Start typing to search for transformers, maintenance records, alerts, and more",

    // Tabs
    "overview": "Overview",
    "map": "Map View",
    "predictive": "Predictive AI",
    "outages": "Outages",
    "weather": "Weather Impact",

    // User menu
    "profile": "Profile",
    "account_settings": "Account Settings",
    "preferences": "Preferences",
    "help": "Help",
    "logout": "Logout"
  },
  am: {
    // Dashboard
    "dashboard": "ዳሽቦርድ",
    "welcome": "እንኳን ደህና መጡ",
    "last_updated": "መጨረሻ የተዘመነው",
    "total_transformers": "ጠቅላላ ትራንስፎርመሮች",
    "healthy_status": "ጤናማ ሁኔታ",
    "active_outages": "ንቁ መቋረጦች",
    "critical_alerts": "አደገኛ ማስጠንቀቂያዎች",
    "view_inventory": "ዕቃዎችን ይመልከቱ",
    "view_outage_map": "የመቋረጥ ካርታ ይመልከቱ",
    "view_all_alerts": "ሁሉንም ማስጠንቀቂያዎች ይመልከቱ",
    "from_last_month": "ካለፈው ወር",
    "from_last_week": "ካለፈው ሳምንት",

    // Navigation
    "transformers": "ትራንስፎርመሮች",
    "map_view": "የካርታ እይታ",
    "maintenance": "ጥገና",
    "alerts": "ማስጠንቀቂያዎች",
    "reports": "ሪፖርቶች",
    "smart_meters": "ስማርት ሜትሮች",
    "settings": "ቅንብሮች",

    // Header
    "search": "ይፈልጉ...",
    "language": "ቋንቋ",
    "notifications": "ማሳወቂያዎች",
    "help_support": "እገዛ እና ድጋፍ",
    "quick_actions": "ፈጣን እርምጃዎች",
    "eeu_logo_alt": "የኢትዮጵያ ኤሌክትሪክ ኃይል ድርጅት ምልክት",
    "eeu_name": "የኢትዮጵያ ኤሌክትሪክ ኃይል ድርጅት",
    "system_name": "የትራንስፎርመር አስተዳደር ሥርዓት",
    "language_changed": "ቋንቋ ተቀይሯል",
    "language_set_as_default": "እንደ ነባሪ ቋንቋዎ ተዘጋጅቷል",

    // Common actions
    "refresh": "አድስ",
    "export": "ላክ",
    "filter": "አጣራ",
    "search_transformers": "ትራንስፎርመሮችን ይፈልጉ...",
    "schedule_maintenance": "የጥገና መርሃግብር",
    "view_all": "ሁሉንም ይመልከቱ",
    "no_results_found": "ምንም ውጤት አልተገኘም",
    "no_results_for": "ለ \"{searchQuery}\" ምንም ውጤት አልተገኘም",
    "try_different_search": "የተለየ የፍለጋ ቃል ይሞክሩ",
    "search_system": "ሲስተሙን ይፈልጉ",
    "start_typing": "ትራንስፎርመሮችን፣ የጥገና መዝገቦችን፣ ማስጠንቀቂያዎችን እና ሌሎችንም ለመፈለግ መተየብ ይጀምሩ",

    // Tabs
    "overview": "አጠቃላይ እይታ",
    "map": "የካርታ እይታ",
    "predictive": "ትንበያ ኤአይ",
    "outages": "መቋረጦች",
    "weather": "የአየር ሁኔታ ተጽዕኖ",

    // User menu
    "profile": "መገለጫ",
    "account_settings": "የመለያ ቅንብሮች",
    "preferences": "ምርጫዎች",
    "help": "እገዛ",
    "logout": "ውጣ"
  },
  ti: {
    // Dashboard
    "dashboard": "ዳሽቦርድ",
    "welcome": "እንቋዕ ብደሓን መጻእኩም",
    "last_updated": "ናይ መወዳእታ ዝተሓደሰ",
    "total_transformers": "ጠቕላላ ትራንስፎርመራት",
    "healthy_status": "ጥዑይ ኩነታት",
    "active_outages": "ንጡፍ መቋረጺታት",
    "critical_alerts": "ኣዝዩ ኣገዳሲ መጠንቀቕታታት",
    "view_inventory": "ኢንቬንቶሪ ርአ",
    "view_outage_map": "ካርታ መቋረጺ ርአ",
    "view_all_alerts": "ኩሎም መጠንቀቕታታት ርአ",
    "from_last_month": "ካብ ዝሓለፈ ወርሒ",
    "from_last_week": "ካብ ዝሓለፈ ሰሙን",

    // Navigation
    "transformers": "ትራንስፎርመራት",
    "map_view": "ካርታ ምርኣይ",
    "maintenance": "ጽገና",
    "alerts": "መጠንቀቕታታት",
    "reports": "ጸብጻባት",
    "smart_meters": "ስማርት ሜትራት",
    "settings": "ቅንብራት",

    // Header
    "search": "ድለይ...",
    "language": "ቋንቋ",
    "notifications": "መፍለጢታት",
    "help_support": "ሓገዝን ደገፍን",
    "quick_actions": "ቅልጡፍ ስጉምትታት",
    "eeu_logo_alt": "ናይ ኢትዮጵያ ኤሌክትሪክ ኣገልግሎት ምልክት",
    "eeu_name": "ናይ ኢትዮጵያ ኤሌክትሪክ ኣገልግሎት",
    "system_name": "ናይ ትራንስፎርመር ምሕደራ ስርዓት",
    "language_changed": "ቋንቋ ተቐይሩ",
    "language_set_as_default": "ከም ናትካ ቀዋሚ ቋንቋ ተሰሪዑ",

    // Common actions
    "refresh": "ኣሓድስ",
    "export": "ኣውጽእ",
    "filter": "ኣጻሪ",
    "search_transformers": "ትራንስፎርመራት ድለይ...",
    "schedule_maintenance": "ጽገና መደብ",
    "view_all": "ኩሎም ርአ",
    "no_results_found": "ዝኾነ ውጽኢት ኣይተረኸበን",
    "no_results_for": "ንዝኾነ ውጽኢት ኣይተረኸበን",
    "try_different_search": "ካልእ ናይ ምድላይ ቃል ፈትን",
    "search_system": "ስርዓት ድለይ",
    "start_typing": "ትራንስፎርመራት፣ ናይ ጽገና መዛግብቲ፣ መጠንቀቕታታት፣ ከምኡ'ውን ካልኦት ንምድላይ ምጽሓፍ ጀምር",

    // Tabs
    "overview": "ሓፈሻዊ ምርኣይ",
    "map": "ካርታ ምርኣይ",
    "predictive": "ትንበያዊ ኤአይ",
    "outages": "መቋረጺታት",
    "weather": "ናይ ኣየር ኩነታት ጽልዋ",

    // User menu
    "profile": "መግለጺ",
    "account_settings": "ናይ ኣካውንት ቅንብራት",
    "preferences": "ምርጫታት",
    "help": "ሓገዝ",
    "logout": "ውጻእ"
  },
  or: {
    // Dashboard
    "dashboard": "Dashboordii",
    "welcome": "Baga nagaan dhuftan",
    "last_updated": "Yeroo dhuma haaromfame",
    "total_transformers": "Transformeroota waliigala",
    "healthy_status": "Haala fayyaa",
    "active_outages": "Kutaalee hojii irra jiran",
    "critical_alerts": "Akeekkachiisawwan murteessoo",
    "view_inventory": "Kuusaa ilaali",
    "view_outage_map": "Kaartaa kutaa ilaali",
    "view_all_alerts": "Akeekkachiisawwan hunda ilaali",
    "from_last_month": "ji'a darbe irraa",
    "from_last_week": "torban darbe irraa",

    // Navigation
    "transformers": "Transformeroota",
    "map_view": "Mul'ata Kaartaa",
    "maintenance": "Suphaa",
    "alerts": "Akeekkachiisawwan",
    "reports": "Gabaasawwan",
    "smart_meters": "Meetiroota Ismaartii",
    "settings": "Qindaa'inoota",

    // Header
    "search": "Barbaadi...",
    "language": "Afaan",
    "notifications": "Beeksisawwan",
    "help_support": "Gargaarsaa fi Deeggarsa",
    "quick_actions": "Tarkaanfiiwwan Ariifachiisaa",
    "eeu_logo_alt": "Mallattoo Tajaajila Elektirikii Itoophiyaa",
    "eeu_name": "Tajaajila Elektirikii Itoophiyaa",
    "system_name": "Siistama Bulchiinsa Transformerootaa",
    "language_changed": "Afaan Jijjiirame",
    "language_set_as_default": "afaan durtii kee ta'ee jira",

    // Common actions
    "refresh": "Haaromsi",
    "export": "Baasi",
    "filter": "Calleessi",
    "search_transformers": "Transformeroota barbaadi...",
    "schedule_maintenance": "Suphaa Sagantaa",
    "view_all": "Hunda Ilaali",
    "no_results_found": "Bu'aan hin argamne",
    "no_results_for": "Bu'aan hin argamne",
    "try_different_search": "Jecha barbaacha biraa yaali",
    "search_system": "Sirna barbaadi",
    "start_typing": "Transformeroota, galmee suphaa, akeekkachiisawwan, fi kan biroo barbaaduuf barreessuu jalqabi",

    // Tabs
    "overview": "Ilaalcha Waliigalaa",
    "map": "Mul'ata Kaartaa",
    "predictive": "AI Raagaa",
    "outages": "Kutaalee",
    "weather": "Dhiibbaa Qilleensaa",

    // User menu
    "profile": "Profaayilii",
    "account_settings": "Qindaa'inoota Herregaa",
    "preferences": "Filannoolee",
    "help": "Gargaarsa",
    "logout": "Ba'i"
  }
}

// Create language provider component
export function LanguageProvider({ children }: { children: React.ReactNode }) {
  // Initialize language from localStorage or default to English
  const [language, setLanguageState] = useState<Language>("en")

  // Load language preference from localStorage on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem("language") as Language
    if (savedLanguage && Object.keys(languageNames).includes(savedLanguage)) {
      setLanguageState(savedLanguage)
    }
  }, [])

  // Save language preference to localStorage when it changes
  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage)
    localStorage.setItem("language", newLanguage)

    // Update HTML lang attribute for accessibility
    document.documentElement.lang = newLanguage

    // For RTL languages (if needed in the future)
    // document.documentElement.dir = newLanguage === 'ar' ? 'rtl' : 'ltr'
  }

  // Translation function
  const t = (key: string): string => {
    return translations[language][key] || translations.en[key] || key
  }

  // Provide language context to children
  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

// Custom hook to use language context
export const useLanguage = () => useContext(LanguageContext)
