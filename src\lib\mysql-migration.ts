/**
 * MySQL Migration Service
 *
 * This service migrates data from the JSON database to MySQL dtms_eeu_db database.
 */

import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import {
  createDatabase,
  executeQuery,
  testConnection,
  getDatabaseInfo,
  initializePool
} from './mysql-connection';

// Import JSON database service to get existing data
import { DatabaseService } from './db/db-service';

/**
 * Initialize MySQL schema
 */
export async function initializeMySQLSchema(): Promise<void> {
  try {
    console.log('🔄 Initializing MySQL schema for dtms_eeu_db...');

    // Create database first
    await createDatabase();

    // Initialize connection pool
    initializePool();

    // Test connection
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to MySQL database');
    }

    // Read and execute schema
    const schemaPath = path.join(process.cwd(), 'lib', 'mysql-schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');

    // Split schema into individual statements
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 Executing ${statements.length} schema statements...`);

    for (const statement of statements) {
      if (statement.trim()) {
        await executeQuery(statement);
      }
    }

    console.log('✅ MySQL schema initialized successfully');

  } catch (error) {
    console.error('❌ Error initializing MySQL schema:', error);
    throw error;
  }
}

/**
 * Migrate users data
 */
export async function migrateUsers(): Promise<void> {
  try {
    console.log('🔄 Migrating users to MySQL...');

    const dbService = new DatabaseService();
    await dbService.initialize();

    const users = dbService.users.getAll();
    console.log(`📊 Found ${users.length} users to migrate`);

    for (const user of users) {
      await executeQuery(`
        INSERT INTO users (
          id, created_at, updated_at, email, password_hash, salt,
          first_name, last_name, role, is_active, last_login,
          region_id, service_center_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          updated_at = VALUES(updated_at),
          email = VALUES(email),
          first_name = VALUES(first_name),
          last_name = VALUES(last_name),
          role = VALUES(role),
          is_active = VALUES(is_active)
      `, [
        user.id,
        user.createdAt,
        user.updatedAt,
        user.email,
        user.passwordHash,
        user.salt,
        user.firstName,
        user.lastName,
        user.role,
        user.isActive,
        user.lastLogin || null,
        user.regionId || null,
        user.serviceCenterId || null
      ]);
    }

    console.log('✅ Users migrated successfully');

  } catch (error) {
    console.error('❌ Error migrating users:', error);
    throw error;
  }
}

/**
 * Migrate regions data
 */
export async function migrateRegions(): Promise<void> {
  try {
    console.log('🔄 Migrating regions to MySQL...');

    const dbService = new DatabaseService();
    await dbService.initialize();

    const regions = dbService.regions.getAll();
    console.log(`📊 Found ${regions.length} regions to migrate`);

    for (const region of regions) {
      await executeQuery(`
        INSERT INTO regions (
          id, created_at, updated_at, name, code, description,
          coordinates_lat, coordinates_lng, population, area_km2
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          updated_at = VALUES(updated_at),
          name = VALUES(name),
          description = VALUES(description),
          coordinates_lat = VALUES(coordinates_lat),
          coordinates_lng = VALUES(coordinates_lng),
          population = VALUES(population),
          area_km2 = VALUES(area_km2)
      `, [
        region.id,
        region.createdAt,
        region.updatedAt,
        region.name,
        region.code,
        region.description || null,
        region.coordinates.lat,
        region.coordinates.lng,
        region.population || 0,
        region.areaKm2 || 0
      ]);
    }

    console.log('✅ Regions migrated successfully');

  } catch (error) {
    console.error('❌ Error migrating regions:', error);
    throw error;
  }
}

/**
 * Migrate service centers data
 */
export async function migrateServiceCenters(): Promise<void> {
  try {
    console.log('🔄 Migrating service centers to MySQL...');

    const dbService = new DatabaseService();
    await dbService.initialize();

    const serviceCenters = dbService.serviceCenters.getAll();
    console.log(`📊 Found ${serviceCenters.length} service centers to migrate`);

    for (const center of serviceCenters) {
      await executeQuery(`
        INSERT INTO service_centers (
          id, created_at, updated_at, name, code, region_id,
          address, coordinates_lat, coordinates_lng, contact_phone,
          contact_email, manager_name, capacity
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          updated_at = VALUES(updated_at),
          name = VALUES(name),
          address = VALUES(address),
          coordinates_lat = VALUES(coordinates_lat),
          coordinates_lng = VALUES(coordinates_lng),
          contact_phone = VALUES(contact_phone),
          contact_email = VALUES(contact_email),
          manager_name = VALUES(manager_name),
          capacity = VALUES(capacity)
      `, [
        center.id,
        center.createdAt,
        center.updatedAt,
        center.name,
        center.code,
        center.regionId,
        center.address || null,
        center.coordinates.lat,
        center.coordinates.lng,
        center.contactPhone || null,
        center.contactEmail || null,
        center.managerName || null,
        center.capacity || 0
      ]);
    }

    console.log('✅ Service centers migrated successfully');

  } catch (error) {
    console.error('❌ Error migrating service centers:', error);
    throw error;
  }
}

/**
 * Migrate transformers data
 */
export async function migrateTransformers(): Promise<void> {
  try {
    console.log('🔄 Migrating transformers to MySQL...');

    const dbService = new DatabaseService();
    await dbService.initialize();

    const transformers = dbService.transformers.getAll();
    console.log(`📊 Found ${transformers.length} transformers to migrate`);

    for (const transformer of transformers) {
      await executeQuery(`
        INSERT INTO transformers (
          id, created_at, updated_at, serial_number, name, status,
          type, manufacturer, model, manufacture_date, installation_date,
          last_maintenance_date, next_maintenance_date, capacity,
          voltage_primary, voltage_secondary, region_id, service_center_id,
          location_address, location_lat, location_lng, temperature,
          load_percentage, oil_level, health_index, tags
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          updated_at = VALUES(updated_at),
          name = VALUES(name),
          status = VALUES(status),
          type = VALUES(type),
          manufacturer = VALUES(manufacturer),
          model = VALUES(model),
          temperature = VALUES(temperature),
          load_percentage = VALUES(load_percentage),
          oil_level = VALUES(oil_level),
          health_index = VALUES(health_index)
      `, [
        transformer.id,
        transformer.createdAt,
        transformer.updatedAt,
        transformer.serialNumber,
        transformer.name,
        transformer.status,
        transformer.type,
        transformer.manufacturer,
        transformer.model,
        transformer.manufactureDate || null,
        transformer.installationDate || null,
        transformer.lastMaintenanceDate || null,
        transformer.nextMaintenanceDate || null,
        transformer.capacity,
        transformer.voltage.primary,
        transformer.voltage.secondary,
        transformer.regionId,
        transformer.serviceCenterId,
        transformer.location.address || null,
        transformer.location.coordinates.lat,
        transformer.location.coordinates.lng,
        transformer.metrics.temperature,
        transformer.metrics.loadPercentage,
        transformer.metrics.oilLevel,
        transformer.metrics.healthIndex,
        JSON.stringify(transformer.tags || [])
      ]);
    }

    console.log('✅ Transformers migrated successfully');

  } catch (error) {
    console.error('❌ Error migrating transformers:', error);
    throw error;
  }
}

/**
 * Migrate maintenance records data
 */
export async function migrateMaintenanceRecords(): Promise<void> {
  try {
    console.log('🔄 Migrating maintenance records to MySQL...');

    const dbService = new DatabaseService();
    await dbService.initialize();

    const records = dbService.maintenance.getAll();
    console.log(`📊 Found ${records.length} maintenance records to migrate`);

    for (const record of records) {
      await executeQuery(`
        INSERT INTO maintenance_records (
          id, created_at, updated_at, transformer_id, type, status,
          priority, scheduled_date, completed_date, technician_id,
          description, work_performed, parts_used, cost, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          updated_at = VALUES(updated_at),
          status = VALUES(status),
          completed_date = VALUES(completed_date),
          work_performed = VALUES(work_performed),
          cost = VALUES(cost),
          notes = VALUES(notes)
      `, [
        record.id,
        record.createdAt,
        record.updatedAt,
        record.transformerId,
        record.type,
        record.status,
        record.priority,
        record.scheduledDate,
        record.completedDate || null,
        record.technicianId || null,
        record.description || null,
        record.workPerformed || null,
        JSON.stringify(record.partsUsed || []),
        record.cost || 0,
        record.notes || null
      ]);
    }

    console.log('✅ Maintenance records migrated successfully');

  } catch (error) {
    console.error('❌ Error migrating maintenance records:', error);
    throw error;
  }
}

/**
 * Migrate alerts data
 */
export async function migrateAlerts(): Promise<void> {
  try {
    console.log('🔄 Migrating alerts to MySQL...');

    const dbService = new DatabaseService();
    await dbService.initialize();

    const alerts = dbService.alerts.getAll();
    console.log(`📊 Found ${alerts.length} alerts to migrate`);

    for (const alert of alerts) {
      await executeQuery(`
        INSERT INTO alerts (
          id, created_at, updated_at, transformer_id, type, severity,
          title, message, is_resolved, resolved_at, resolved_by, resolution_notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          updated_at = VALUES(updated_at),
          is_resolved = VALUES(is_resolved),
          resolved_at = VALUES(resolved_at),
          resolved_by = VALUES(resolved_by),
          resolution_notes = VALUES(resolution_notes)
      `, [
        alert.id,
        alert.createdAt,
        alert.updatedAt,
        alert.transformerId || null,
        alert.type,
        alert.severity,
        alert.title,
        alert.message,
        alert.isResolved,
        alert.resolvedAt || null,
        alert.resolvedBy || null,
        alert.resolutionNotes || null
      ]);
    }

    console.log('✅ Alerts migrated successfully');

  } catch (error) {
    console.error('❌ Error migrating alerts:', error);
    throw error;
  }
}

/**
 * Migrate outages data
 */
export async function migrateOutages(): Promise<void> {
  try {
    console.log('🔄 Migrating outages to MySQL...');

    const dbService = new DatabaseService();
    await dbService.initialize();

    const outages = dbService.outages.getAll();
    console.log(`📊 Found ${outages.length} outages to migrate`);

    for (const outage of outages) {
      await executeQuery(`
        INSERT INTO outages (
          id, created_at, updated_at, transformer_id, region_id, title,
          description, status, severity, start_time, end_time,
          affected_customers, cause, resolution
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          updated_at = VALUES(updated_at),
          status = VALUES(status),
          end_time = VALUES(end_time),
          affected_customers = VALUES(affected_customers),
          resolution = VALUES(resolution)
      `, [
        outage.id,
        outage.createdAt,
        outage.updatedAt,
        outage.transformerId || null,
        outage.regionId || null,
        outage.title,
        outage.description || null,
        outage.status,
        outage.severity,
        outage.startTime,
        outage.endTime || null,
        outage.affectedCustomers || 0,
        outage.cause || null,
        outage.resolution || null
      ]);
    }

    console.log('✅ Outages migrated successfully');

  } catch (error) {
    console.error('❌ Error migrating outages:', error);
    throw error;
  }
}

/**
 * Migrate weather alerts data
 */
export async function migrateWeatherAlerts(): Promise<void> {
  try {
    console.log('🔄 Migrating weather alerts to MySQL...');

    const dbService = new DatabaseService();
    await dbService.initialize();

    const weatherAlerts = dbService.weatherAlerts.getAll();
    console.log(`📊 Found ${weatherAlerts.length} weather alerts to migrate`);

    for (const alert of weatherAlerts) {
      await executeQuery(`
        INSERT INTO weather_alerts (
          id, created_at, updated_at, region_id, type, severity,
          title, description, is_active, start_time, end_time, affected_areas
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          updated_at = VALUES(updated_at),
          is_active = VALUES(is_active),
          end_time = VALUES(end_time)
      `, [
        alert.id,
        alert.createdAt,
        alert.updatedAt,
        alert.regionId || null,
        alert.type,
        alert.severity,
        alert.title,
        alert.description || null,
        alert.isActive,
        alert.startTime,
        alert.endTime || null,
        JSON.stringify(alert.affectedAreas || [])
      ]);
    }

    console.log('✅ Weather alerts migrated successfully');

  } catch (error) {
    console.error('❌ Error migrating weather alerts:', error);
    throw error;
  }
}

/**
 * Main migration function - migrates all data to MySQL
 */
export async function migrateAllDataToMySQL(): Promise<void> {
  try {
    console.log('🚀 Starting complete data migration to MySQL dtms_eeu_db...');
    console.log('📊 This will migrate all data from JSON database to MySQL');

    const startTime = Date.now();

    // Step 1: Initialize MySQL schema
    await initializeMySQLSchema();

    // Step 2: Migrate data in dependency order
    console.log('📦 Starting data migration in dependency order...');

    await migrateUsers();
    await migrateRegions();
    await migrateServiceCenters();
    await migrateTransformers();
    await migrateMaintenanceRecords();
    await migrateAlerts();
    await migrateOutages();
    await migrateWeatherAlerts();

    // Step 3: Get final database info
    const dbInfo = await getDatabaseInfo();

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log('✅ Complete data migration successful!');
    console.log(`⏱️  Migration completed in ${duration.toFixed(2)} seconds`);
    console.log('📊 Final database statistics:');
    console.log(`   Database: ${dbInfo.database}`);
    console.log(`   MySQL Version: ${dbInfo.version}`);
    console.log(`   Tables: ${dbInfo.tables.length}`);

    dbInfo.tables.forEach(table => {
      console.log(`   - ${table.name}: ${table.rows} rows`);
    });

    console.log('🎉 All data successfully migrated to MySQL dtms_eeu_db!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

/**
 * Verify migration by comparing record counts
 */
export async function verifyMigration(): Promise<boolean> {
  try {
    console.log('🔍 Verifying migration...');

    // Initialize JSON database
    const dbService = new DatabaseService();
    await dbService.initialize();

    // Get counts from JSON database
    const jsonCounts = {
      users: dbService.users.getAll().length,
      regions: dbService.regions.getAll().length,
      serviceCenters: dbService.serviceCenters.getAll().length,
      transformers: dbService.transformers.getAll().length,
      maintenance: dbService.maintenance.getAll().length,
      alerts: dbService.alerts.getAll().length,
      outages: dbService.outages.getAll().length,
      weatherAlerts: dbService.weatherAlerts.getAll().length
    };

    // Get counts from MySQL database
    const mysqlCounts = {
      users: (await executeQuery<any[]>('SELECT COUNT(*) as count FROM users'))[0].count,
      regions: (await executeQuery<any[]>('SELECT COUNT(*) as count FROM regions'))[0].count,
      serviceCenters: (await executeQuery<any[]>('SELECT COUNT(*) as count FROM service_centers'))[0].count,
      transformers: (await executeQuery<any[]>('SELECT COUNT(*) as count FROM transformers'))[0].count,
      maintenance: (await executeQuery<any[]>('SELECT COUNT(*) as count FROM maintenance_records'))[0].count,
      alerts: (await executeQuery<any[]>('SELECT COUNT(*) as count FROM alerts'))[0].count,
      outages: (await executeQuery<any[]>('SELECT COUNT(*) as count FROM outages'))[0].count,
      weatherAlerts: (await executeQuery<any[]>('SELECT COUNT(*) as count FROM weather_alerts'))[0].count
    };

    console.log('📊 Migration verification results:');
    console.log('Table                | JSON DB | MySQL DB | Status');
    console.log('---------------------|---------|----------|--------');

    let allMatch = true;

    Object.keys(jsonCounts).forEach(table => {
      const jsonCount = jsonCounts[table as keyof typeof jsonCounts];
      const mysqlCount = mysqlCounts[table as keyof typeof mysqlCounts];
      const status = jsonCount === mysqlCount ? '✅ Match' : '❌ Mismatch';

      if (jsonCount !== mysqlCount) {
        allMatch = false;
      }

      console.log(`${table.padEnd(20)} | ${jsonCount.toString().padStart(7)} | ${mysqlCount.toString().padStart(8)} | ${status}`);
    });

    if (allMatch) {
      console.log('✅ Migration verification successful - all record counts match!');
    } else {
      console.log('❌ Migration verification failed - some record counts do not match!');
    }

    return allMatch;

  } catch (error) {
    console.error('❌ Error verifying migration:', error);
    return false;
  }
}
