import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Import database connection utility
    const { executeQuery } = await import('../../../lib/mysql-connection')

    // Test connection first
    const connectionTest = await executeQuery('SELECT 1 as test')
    console.log('✅ Database connection verified')

    // Get table schema
    const schema = await executeQuery(`
      DESCRIBE app_transformers
    `)

    console.log('📋 Table schema:', schema)

    return NextResponse.json({
      success: true,
      schema: schema
    })

  } catch (error) {
    console.error('❌ Error checking schema:', error)
    return NextResponse.json(
      {
        error: 'Failed to check schema',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
