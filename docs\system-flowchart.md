# EEU-DTMS System Architecture & User Role Flowchart

## System Overview

Ethiopian Electric Utility Distribution Transformer Management System (EEU-DTMS) - Comprehensive flowchart illustrating system structure, user roles, and interactions.

```mermaid
graph TB
    %% System Entry Point
    START([System Access]) --> AUTH{Authentication}
    AUTH -->|Valid Credentials| ROLE_CHECK{Role Verification}
    AUTH -->|Invalid| LOGIN_FAIL[Access Denied]

    %% Role-Based Access Control
    ROLE_CHECK --> SUPER_ADMIN[Super Admin]
    ROLE_CHECK --> NAT_ASSET[National Asset Manager]
    ROLE_CHECK --> NAT_MAINT[National Maintenance Manager]
    ROLE_CHECK --> REG_ADMIN[Regional Admin]
    ROLE_CHECK --> REG_ASSET[Regional Asset Manager]
    ROLE_CHECK --> REG_MAINT[Regional Maintenance Engineer]
    ROLE_CHECK --> SC_MANAGER[Service Center Manager]
    ROLE_CHECK --> FIELD_TECH[Field Technician]
    ROLE_CHECK --> CUSTOMER_SVC[Customer Service Agent]
    ROLE_CHECK --> AUDIT_COMP[Audit & Compliance Officer]

    %% Super Admin Capabilities
    SUPER_ADMIN --> SA_DASH[Dashboard Analytics]
    SUPER_ADMIN --> SA_TRANS[All Transformer Operations]
    SUPER_ADMIN --> SA_MAINT[All Maintenance Operations]
    SUPER_ADMIN --> SA_SMART[Smart Meter Management]
    SUPER_ADMIN --> SA_REPORTS[Comprehensive Reports]
    SUPER_ADMIN --> SA_ALERTS[Alert Management]
    SUPER_ADMIN --> SA_SETTINGS[System Settings]
    SUPER_ADMIN --> SA_USERS[User Management]
    SUPER_ADMIN --> SA_PERF[Performance Monitoring]
    SUPER_ADMIN --> SA_WEATHER[Weather Integration]

    %% National Asset Manager
    NAT_ASSET --> NA_DASH[National Dashboard]
    NAT_ASSET --> NA_TRANS[Transformer Overview]
    NAT_ASSET --> NA_ANALYTICS[Asset Analytics]
    NAT_ASSET --> NA_REPORTS[Asset Reports]
    NAT_ASSET --> NA_PERF[Performance Analysis]
    NAT_ASSET --> NA_SMART[Smart Meter Analytics]

    %% National Maintenance Manager
    NAT_MAINT --> NM_DASH[Maintenance Dashboard]
    NAT_MAINT --> NM_SCHED[Schedule Management]
    NAT_MAINT --> NM_WORK[Work Order Management]
    NAT_MAINT --> NM_PREV[Preventive Maintenance]
    NAT_MAINT --> NM_REPORTS[Maintenance Reports]
    NAT_MAINT --> NM_ALERTS[Maintenance Alerts]

    %% Regional Admin
    REG_ADMIN --> RA_DASH[Regional Dashboard]
    REG_ADMIN --> RA_TRANS[Regional Transformers]
    REG_ADMIN --> RA_MAINT[Regional Maintenance]
    REG_ADMIN --> RA_USERS[Regional User Management]
    REG_ADMIN --> RA_REPORTS[Regional Reports]
    REG_ADMIN --> RA_ALERTS[Regional Alerts]
    REG_ADMIN --> RA_SETTINGS[Regional Settings]

    %% Regional Asset Manager
    REG_ASSET --> RAM_DASH[Asset Dashboard]
    REG_ASSET --> RAM_TRANS[Transformer Management]
    REG_ASSET --> RAM_MAP[Transformer Map View]
    REG_ASSET --> RAM_ANALYTICS[Asset Analytics]
    REG_ASSET --> RAM_REPORTS[Asset Reports]
    REG_ASSET --> RAM_PERF[Performance Monitoring]

    %% Regional Maintenance Engineer
    REG_MAINT --> RME_DASH[Maintenance Dashboard]
    REG_MAINT --> RME_SCHED[Task Scheduling]
    REG_MAINT --> RME_WORK[Work Orders]
    REG_MAINT --> RME_INSPECT[Inspections]
    REG_MAINT --> RME_PARTS[Parts & Inventory]
    REG_MAINT --> RME_TECH[Technician Management]
    REG_MAINT --> RME_ALERTS[Maintenance Alerts]

    %% Service Center Manager
    SC_MANAGER --> SCM_DASH[Service Center Dashboard]
    SC_MANAGER --> SCM_TRANS[Local Transformers]
    SC_MANAGER --> SCM_MAINT[Local Maintenance]
    SC_MANAGER --> SCM_SMART[Local Smart Meters]
    SC_MANAGER --> SCM_WORK[Work Order Management]
    SC_MANAGER --> SCM_REPORTS[Local Reports]
    SC_MANAGER --> SCM_ALERTS[Local Alerts]

    %% Field Technician
    FIELD_TECH --> FT_DASH[Technician Dashboard]
    FIELD_TECH --> FT_TASKS[Assigned Tasks]
    FIELD_TECH --> FT_WORK[Work Orders]
    FIELD_TECH --> FT_INSPECT[Field Inspections]
    FIELD_TECH --> FT_MAINT[Maintenance Execution]
    FIELD_TECH --> FT_ALERTS[Field Alerts]
    FIELD_TECH --> FT_WEATHER[Weather Conditions]

    %% Customer Service Agent
    CUSTOMER_SVC --> CS_DASH[Customer Dashboard]
    CUSTOMER_SVC --> CS_TRANS[Transformer Status]
    CUSTOMER_SVC --> CS_SMART[Smart Meter Data]
    CUSTOMER_SVC --> CS_REPORTS[Customer Reports]
    CUSTOMER_SVC --> CS_ALERTS[Service Alerts]

    %% Audit & Compliance Officer
    AUDIT_COMP --> AC_DASH[Audit Dashboard]
    AUDIT_COMP --> AC_REPORTS[Compliance Reports]
    AUDIT_COMP --> AC_MAINT[Maintenance Audit]
    AUDIT_COMP --> AC_ALERTS[Compliance Alerts]
    AUDIT_COMP --> AC_SETTINGS[Audit Settings]

    %% Core System Modules
    subgraph CORE_MODULES[Core System Modules]
        DASHBOARD_MOD[Dashboard Module]
        TRANSFORMER_MOD[Transformer Module]
        MAINTENANCE_MOD[Maintenance Module]
        SMART_METER_MOD[Smart Meter Module]
        REPORTS_MOD[Reports Module]
        ALERTS_MOD[Alerts Module]
        SETTINGS_MOD[Settings Module]
        PERFORMANCE_MOD[Performance Module]
        WEATHER_MOD[Weather Module]
    end

    %% Database Layer
    subgraph DATABASE[Database Layer]
        TRANS_DB[(Transformers DB)]
        MAINT_DB[(Maintenance DB)]
        USERS_DB[(Users DB)]
        ALERTS_DB[(Alerts DB)]
        SMART_DB[(Smart Meters DB)]
        PERF_DB[(Performance DB)]
        WEATHER_DB[(Weather DB)]
    end

    %% External Integrations
    subgraph EXTERNAL[External Systems]
        WEATHER_API[Weather API]
        SMART_METERS[Smart Meter Network]
        GIS_SYSTEM[GIS Mapping]
        NOTIFICATION[Notification Service]
    end

    %% Module Interactions
    DASHBOARD_MOD --> TRANS_DB
    DASHBOARD_MOD --> MAINT_DB
    DASHBOARD_MOD --> ALERTS_DB
    DASHBOARD_MOD --> PERF_DB

    TRANSFORMER_MOD --> TRANS_DB
    TRANSFORMER_MOD --> ALERTS_DB
    TRANSFORMER_MOD --> PERF_DB
    TRANSFORMER_MOD --> GIS_SYSTEM

    MAINTENANCE_MOD --> MAINT_DB
    MAINTENANCE_MOD --> TRANS_DB
    MAINTENANCE_MOD --> USERS_DB
    MAINTENANCE_MOD --> ALERTS_DB

    SMART_METER_MOD --> SMART_DB
    SMART_METER_MOD --> TRANS_DB
    SMART_METER_MOD --> SMART_METERS

    REPORTS_MOD --> TRANS_DB
    REPORTS_MOD --> MAINT_DB
    REPORTS_MOD --> SMART_DB
    REPORTS_MOD --> PERF_DB

    ALERTS_MOD --> ALERTS_DB
    ALERTS_MOD --> NOTIFICATION
    ALERTS_MOD --> USERS_DB

    PERFORMANCE_MOD --> PERF_DB
    PERFORMANCE_MOD --> TRANS_DB

    WEATHER_MOD --> WEATHER_DB
    WEATHER_MOD --> WEATHER_API

    %% User Access Flows
    SA_DASH --> DASHBOARD_MOD
    SA_TRANS --> TRANSFORMER_MOD
    SA_MAINT --> MAINTENANCE_MOD
    SA_SMART --> SMART_METER_MOD
    SA_REPORTS --> REPORTS_MOD
    SA_ALERTS --> ALERTS_MOD
    SA_SETTINGS --> SETTINGS_MOD
    SA_PERF --> PERFORMANCE_MOD
    SA_WEATHER --> WEATHER_MOD

    NA_DASH --> DASHBOARD_MOD
    NA_TRANS --> TRANSFORMER_MOD
    NA_ANALYTICS --> REPORTS_MOD
    NA_REPORTS --> REPORTS_MOD
    NA_PERF --> PERFORMANCE_MOD
    NA_SMART --> SMART_METER_MOD

    NM_DASH --> DASHBOARD_MOD
    NM_SCHED --> MAINTENANCE_MOD
    NM_WORK --> MAINTENANCE_MOD
    NM_PREV --> MAINTENANCE_MOD
    NM_REPORTS --> REPORTS_MOD
    NM_ALERTS --> ALERTS_MOD

    RA_DASH --> DASHBOARD_MOD
    RA_TRANS --> TRANSFORMER_MOD
    RA_MAINT --> MAINTENANCE_MOD
    RA_REPORTS --> REPORTS_MOD
    RA_ALERTS --> ALERTS_MOD
    RA_SETTINGS --> SETTINGS_MOD

    RAM_DASH --> DASHBOARD_MOD
    RAM_TRANS --> TRANSFORMER_MOD
    RAM_MAP --> TRANSFORMER_MOD
    RAM_ANALYTICS --> REPORTS_MOD
    RAM_REPORTS --> REPORTS_MOD
    RAM_PERF --> PERFORMANCE_MOD

    RME_DASH --> DASHBOARD_MOD
    RME_SCHED --> MAINTENANCE_MOD
    RME_WORK --> MAINTENANCE_MOD
    RME_INSPECT --> MAINTENANCE_MOD
    RME_PARTS --> MAINTENANCE_MOD
    RME_TECH --> MAINTENANCE_MOD
    RME_ALERTS --> ALERTS_MOD

    SCM_DASH --> DASHBOARD_MOD
    SCM_TRANS --> TRANSFORMER_MOD
    SCM_MAINT --> MAINTENANCE_MOD
    SCM_SMART --> SMART_METER_MOD
    SCM_WORK --> MAINTENANCE_MOD
    SCM_REPORTS --> REPORTS_MOD
    SCM_ALERTS --> ALERTS_MOD

    FT_DASH --> DASHBOARD_MOD
    FT_TASKS --> MAINTENANCE_MOD
    FT_WORK --> MAINTENANCE_MOD
    FT_INSPECT --> MAINTENANCE_MOD
    FT_MAINT --> MAINTENANCE_MOD
    FT_ALERTS --> ALERTS_MOD
    FT_WEATHER --> WEATHER_MOD

    CS_DASH --> DASHBOARD_MOD
    CS_TRANS --> TRANSFORMER_MOD
    CS_SMART --> SMART_METER_MOD
    CS_REPORTS --> REPORTS_MOD
    CS_ALERTS --> ALERTS_MOD

    AC_DASH --> DASHBOARD_MOD
    AC_REPORTS --> REPORTS_MOD
    AC_MAINT --> MAINTENANCE_MOD
    AC_ALERTS --> ALERTS_MOD
    AC_SETTINGS --> SETTINGS_MOD

    %% Styling
    classDef userRole fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef systemModule fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef database fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef external fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef functionality fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class SUPER_ADMIN,NAT_ASSET,NAT_MAINT,REG_ADMIN,REG_ASSET,REG_MAINT,SC_MANAGER,FIELD_TECH,CUSTOMER_SVC,AUDIT_COMP userRole
    class DASHBOARD_MOD,TRANSFORMER_MOD,MAINTENANCE_MOD,SMART_METER_MOD,REPORTS_MOD,ALERTS_MOD,SETTINGS_MOD,PERFORMANCE_MOD,WEATHER_MOD systemModule
    class TRANS_DB,MAINT_DB,USERS_DB,ALERTS_DB,SMART_DB,PERF_DB,WEATHER_DB database
    class WEATHER_API,SMART_METERS,GIS_SYSTEM,NOTIFICATION external
```

## User Role Hierarchy & Permissions

### 🔐 Access Level Matrix

| Role                              | Dashboard        | Transformers     | Maintenance       | Smart Meters     | Reports                | Alerts            | Settings          | Performance   | Weather     |
| --------------------------------- | ---------------- | ---------------- | ----------------- | ---------------- | ---------------------- | ----------------- | ----------------- | ------------- | ----------- |
| **Super Admin**                   | ✅ Full          | ✅ Full          | ✅ Full           | ✅ Full          | ✅ Full                | ✅ Full           | ✅ Full           | ✅ Full       | ✅ Full     |
| **National Asset Manager**        | ✅ National      | ✅ View/Edit     | ❌ View Only      | ✅ Analytics     | ✅ Asset Reports       | ✅ View           | ✅ Limited        | ✅ Full       | ✅ View     |
| **National Maintenance Manager**  | ✅ National      | ✅ View Only     | ✅ Full           | ❌ Limited       | ✅ Maintenance Reports | ✅ Maintenance    | ❌ Limited        | ✅ View       | ✅ View     |
| **Regional Admin**                | ✅ Regional      | ✅ Regional      | ✅ Regional       | ✅ Regional      | ✅ Regional Reports    | ✅ Regional       | ✅ Regional       | ✅ Regional   | ✅ Regional |
| **Regional Asset Manager**        | ✅ Regional      | ✅ Full Regional | ✅ View Only      | ✅ Regional      | ✅ Asset Reports       | ✅ View           | ❌ Limited        | ✅ Regional   | ✅ View     |
| **Regional Maintenance Engineer** | ✅ Regional      | ✅ View Only     | ✅ Full Regional  | ❌ Limited       | ✅ Maintenance Reports | ✅ Maintenance    | ❌ Limited        | ✅ View       | ✅ View     |
| **Service Center Manager**        | ✅ Local         | ✅ Local         | ✅ Local          | ✅ Local         | ✅ Local Reports       | ✅ Local          | ❌ Limited        | ✅ Local      | ✅ View     |
| **Field Technician**              | ✅ Personal      | ✅ Assigned      | ✅ Assigned Tasks | ❌ Limited       | ❌ Limited             | ✅ Field Alerts   | ❌ None           | ❌ Limited    | ✅ View     |
| **Customer Service Agent**        | ✅ Customer View | ✅ Status Only   | ❌ None           | ✅ Customer Data | ✅ Customer Reports    | ✅ Service Alerts | ❌ None           | ❌ None       | ❌ None     |
| **Audit & Compliance Officer**    | ✅ Audit View    | ❌ Audit Only    | ✅ Audit Only     | ❌ Audit Only    | ✅ Compliance Reports  | ✅ Compliance     | ✅ Audit Settings | ❌ Audit Only | ❌ None     |

### 🔄 System Workflow Patterns

#### 1. **Maintenance Workflow**

```
Alert Generated → Work Order Created → Technician Assigned → Task Executed → Completion Verified → Report Generated
```

#### 2. **Asset Management Workflow**

```
Transformer Registered → Performance Monitored → Maintenance Scheduled → Status Updated → Analytics Generated
```

#### 3. **Alert Management Workflow**

```
Condition Detected → Alert Triggered → Notification Sent → Assignment Made → Resolution Tracked → Closure Verified
```

#### 4. **Reporting Workflow**

```
Data Collected → Analysis Performed → Report Generated → Distribution Made → Archive Stored
```

## Key System Interactions

### 📊 **Data Flow Patterns**

- **Real-time Monitoring**: Smart meters → Database → Dashboard updates
- **Predictive Maintenance**: Performance data → Analytics → Maintenance scheduling
- **Alert Propagation**: System events → Alert engine → User notifications
- **Reporting Pipeline**: Multiple data sources → Report engine → User interfaces

### 🔗 **Integration Points**

- **Weather Integration**: External weather API → System database → Maintenance planning
- **GIS Integration**: Geographic data → Transformer mapping → Location-based services
- **Smart Meter Network**: IoT devices → Data collection → Real-time monitoring
- **Notification Service**: System events → External service → User notifications

## Technical Architecture & Data Flow

```mermaid
graph TB
    %% Frontend Layer
    subgraph FRONTEND[Frontend Layer - Next.js]
        UI_DASH[Dashboard UI]
        UI_TRANS[Transformer UI]
        UI_MAINT[Maintenance UI]
        UI_SMART[Smart Meter UI]
        UI_REPORTS[Reports UI]
        UI_ALERTS[Alerts UI]
        UI_SETTINGS[Settings UI]
    end

    %% API Layer
    subgraph API_LAYER[API Layer - Next.js API Routes]
        API_DASH[/api/dashboard/*]
        API_TRANS[/api/transformers/*]
        API_MAINT[/api/maintenance/*]
        API_SMART[/api/smart-meters/*]
        API_REPORTS[/api/reports/*]
        API_ALERTS[/api/alerts/*]
        API_SETTINGS[/api/settings/*]
        API_AUTH[/api/auth/*]
    end

    %% Business Logic Layer
    subgraph BUSINESS_LOGIC[Business Logic Layer]
        AUTH_SERVICE[Authentication Service]
        ROLE_SERVICE[Role Management Service]
        DATA_SERVICE[Data Processing Service]
        ANALYTICS_SERVICE[Analytics Service]
        NOTIFICATION_SERVICE[Notification Service]
        EXPORT_SERVICE[Export Service]
    end

    %% Database Layer
    subgraph DATABASE_LAYER[Database Layer - MySQL]
        USERS_TABLE[app_users]
        ROLES_TABLE[app_user_roles]
        REGIONS_TABLE[app_regions]
        SERVICE_CENTERS_TABLE[app_service_centers]
        TRANSFORMERS_TABLE[app_transformers]
        MAINTENANCE_TABLE[app_maintenance_schedules]
        WORK_ORDERS_TABLE[app_work_orders]
        ALERTS_TABLE[app_alerts]
        SMART_METERS_TABLE[app_smart_meters]
        PERFORMANCE_TABLE[app_performance_metrics]
        WEATHER_TABLE[app_weather_data]
    end

    %% External Services
    subgraph EXTERNAL_SERVICES[External Services]
        WEATHER_SERVICE[Weather API Service]
        SMS_SERVICE[SMS Gateway]
        EMAIL_SERVICE[Email Service]
        GIS_SERVICE[GIS Mapping Service]
        IOT_GATEWAY[IoT Gateway]
    end

    %% UI to API Connections
    UI_DASH --> API_DASH
    UI_TRANS --> API_TRANS
    UI_MAINT --> API_MAINT
    UI_SMART --> API_SMART
    UI_REPORTS --> API_REPORTS
    UI_ALERTS --> API_ALERTS
    UI_SETTINGS --> API_SETTINGS

    %% API to Business Logic
    API_DASH --> DATA_SERVICE
    API_DASH --> ANALYTICS_SERVICE
    API_TRANS --> DATA_SERVICE
    API_TRANS --> ANALYTICS_SERVICE
    API_MAINT --> DATA_SERVICE
    API_MAINT --> NOTIFICATION_SERVICE
    API_SMART --> DATA_SERVICE
    API_REPORTS --> ANALYTICS_SERVICE
    API_REPORTS --> EXPORT_SERVICE
    API_ALERTS --> NOTIFICATION_SERVICE
    API_SETTINGS --> ROLE_SERVICE
    API_AUTH --> AUTH_SERVICE

    %% Business Logic to Database
    AUTH_SERVICE --> USERS_TABLE
    AUTH_SERVICE --> ROLES_TABLE
    ROLE_SERVICE --> USERS_TABLE
    ROLE_SERVICE --> ROLES_TABLE
    DATA_SERVICE --> TRANSFORMERS_TABLE
    DATA_SERVICE --> MAINTENANCE_TABLE
    DATA_SERVICE --> WORK_ORDERS_TABLE
    DATA_SERVICE --> SMART_METERS_TABLE
    DATA_SERVICE --> PERFORMANCE_TABLE
    ANALYTICS_SERVICE --> TRANSFORMERS_TABLE
    ANALYTICS_SERVICE --> MAINTENANCE_TABLE
    ANALYTICS_SERVICE --> PERFORMANCE_TABLE
    ANALYTICS_SERVICE --> ALERTS_TABLE
    NOTIFICATION_SERVICE --> ALERTS_TABLE
    NOTIFICATION_SERVICE --> USERS_TABLE

    %% External Service Integrations
    NOTIFICATION_SERVICE --> SMS_SERVICE
    NOTIFICATION_SERVICE --> EMAIL_SERVICE
    DATA_SERVICE --> WEATHER_SERVICE
    DATA_SERVICE --> GIS_SERVICE
    DATA_SERVICE --> IOT_GATEWAY
    WEATHER_SERVICE --> WEATHER_TABLE
    IOT_GATEWAY --> SMART_METERS_TABLE

    %% Database Relationships
    USERS_TABLE -.-> ROLES_TABLE
    USERS_TABLE -.-> REGIONS_TABLE
    TRANSFORMERS_TABLE -.-> REGIONS_TABLE
    TRANSFORMERS_TABLE -.-> SERVICE_CENTERS_TABLE
    MAINTENANCE_TABLE -.-> TRANSFORMERS_TABLE
    MAINTENANCE_TABLE -.-> USERS_TABLE
    WORK_ORDERS_TABLE -.-> TRANSFORMERS_TABLE
    WORK_ORDERS_TABLE -.-> USERS_TABLE
    ALERTS_TABLE -.-> TRANSFORMERS_TABLE
    SMART_METERS_TABLE -.-> TRANSFORMERS_TABLE
    PERFORMANCE_TABLE -.-> TRANSFORMERS_TABLE

    %% Styling
    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef api fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef business fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef database fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef external fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class UI_DASH,UI_TRANS,UI_MAINT,UI_SMART,UI_REPORTS,UI_ALERTS,UI_SETTINGS frontend
    class API_DASH,API_TRANS,API_MAINT,API_SMART,API_REPORTS,API_ALERTS,API_SETTINGS,API_AUTH api
    class AUTH_SERVICE,ROLE_SERVICE,DATA_SERVICE,ANALYTICS_SERVICE,NOTIFICATION_SERVICE,EXPORT_SERVICE business
    class USERS_TABLE,ROLES_TABLE,REGIONS_TABLE,SERVICE_CENTERS_TABLE,TRANSFORMERS_TABLE,MAINTENANCE_TABLE,WORK_ORDERS_TABLE,ALERTS_TABLE,SMART_METERS_TABLE,PERFORMANCE_TABLE,WEATHER_TABLE database
    class WEATHER_SERVICE,SMS_SERVICE,EMAIL_SERVICE,GIS_SERVICE,IOT_GATEWAY external
```

## System Component Interactions

### 🔄 **Real-time Data Flow**

#### **Smart Meter Data Pipeline**

```mermaid
sequenceDiagram
    participant SM as Smart Meters
    participant IOT as IoT Gateway
    participant API as API Layer
    participant DB as Database
    participant UI as User Interface
    participant ALERT as Alert System

    SM->>IOT: Send meter readings
    IOT->>API: POST /api/smart-meters/data
    API->>DB: Store meter data
    API->>UI: Real-time update

    alt Critical Reading
        API->>ALERT: Trigger alert
        ALERT->>DB: Store alert
        ALERT->>UI: Show notification
    end
```

#### **Maintenance Workflow**

```mermaid
sequenceDiagram
    participant USER as User
    participant UI as Frontend
    participant API as API Layer
    participant BL as Business Logic
    participant DB as Database
    participant EXT as External Services

    USER->>UI: Schedule maintenance
    UI->>API: POST /api/maintenance/scheduled-tasks
    API->>BL: Process request
    BL->>DB: Create maintenance record
    BL->>EXT: Send notifications
    EXT->>USER: SMS/Email notification
    DB->>UI: Update task list
```

### 📊 **Analytics & Reporting Pipeline**

#### **Report Generation Flow**

```mermaid
flowchart LR
    A[Data Sources] --> B[Data Aggregation]
    B --> C[Analytics Engine]
    C --> D[Report Generation]
    D --> E[Export Service]
    E --> F[User Download]

    A1[Transformers] --> B
    A2[Maintenance] --> B
    A3[Smart Meters] --> B
    A4[Performance] --> B
    A5[Alerts] --> B

    D --> D1[PDF Reports]
    D --> D2[Excel Reports]
    D --> D3[CSV Reports]
    D --> D4[Dashboard Views]
```

This comprehensive flowchart illustrates the complete system architecture, showing how different user roles interact with various system modules and how data flows through the entire EEU-DTMS ecosystem.
