import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') || ''
    const region = searchParams.get('region') || ''
    const search = searchParams.get('search') || ''
    const realTime = searchParams.get('realTime') === 'true'
    const offset = (page - 1) * limit

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    // Build WHERE clause
    let whereConditions = []
    let queryParams = []

    if (status) {
      whereConditions.push('sm.status = ?')
      queryParams.push(status)
    }

    if (region) {
      whereConditions.push('sm.region_id = ?')
      queryParams.push(region)
    }

    if (search) {
      whereConditions.push('(sm.meter_id LIKE ? OR sm.customer_name LIKE ? OR sm.location LIKE ?)')
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`)
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : ''

    // Get smart meters with real-time data
    const smartMeters = await executeQuery(`
      SELECT 
        sm.*,
        r.name as region_name,
        sc.name as service_center_name,
        t.name as transformer_name,
        smd.current_reading,
        smd.voltage,
        smd.current,
        smd.power_factor,
        smd.frequency,
        smd.total_consumption,
        smd.daily_consumption,
        smd.monthly_consumption,
        smd.last_reading_time,
        smd.signal_strength,
        smd.battery_level,
        CASE 
          WHEN smd.last_reading_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'offline'
          WHEN smd.signal_strength < 20 THEN 'poor_signal'
          WHEN smd.battery_level < 20 THEN 'low_battery'
          ELSE 'online'
        END as connection_status,
        TIMESTAMPDIFF(MINUTE, smd.last_reading_time, NOW()) as minutes_since_last_reading
      FROM app_smart_meters sm
      LEFT JOIN app_regions r ON sm.region_id = r.id
      LEFT JOIN app_service_centers sc ON sm.service_center_id = sc.id
      LEFT JOIN app_transformers t ON sm.transformer_id = t.id
      LEFT JOIN app_smart_meter_data smd ON sm.id = smd.meter_id 
        AND smd.reading_time = (
          SELECT MAX(reading_time) 
          FROM app_smart_meter_data 
          WHERE meter_id = sm.id
        )
      ${whereClause}
      ORDER BY sm.created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset])

    // Get total count
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total
      FROM app_smart_meters sm
      LEFT JOIN app_regions r ON sm.region_id = r.id
      ${whereClause}
    `, queryParams)

    const total = totalResult[0]?.total || 0

    // Get summary statistics
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_meters,
        SUM(CASE WHEN sm.status = 'active' THEN 1 ELSE 0 END) as active_meters,
        SUM(CASE WHEN sm.status = 'inactive' THEN 1 ELSE 0 END) as inactive_meters,
        SUM(CASE WHEN sm.status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_meters,
        SUM(CASE WHEN smd.last_reading_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 ELSE 0 END) as offline_meters,
        SUM(CASE WHEN smd.signal_strength < 20 THEN 1 ELSE 0 END) as poor_signal_meters,
        SUM(CASE WHEN smd.battery_level < 20 THEN 1 ELSE 0 END) as low_battery_meters,
        AVG(smd.current_reading) as avg_current_reading,
        SUM(smd.daily_consumption) as total_daily_consumption,
        AVG(smd.signal_strength) as avg_signal_strength,
        AVG(smd.battery_level) as avg_battery_level
      FROM app_smart_meters sm
      LEFT JOIN app_smart_meter_data smd ON sm.id = smd.meter_id 
        AND smd.reading_time = (
          SELECT MAX(reading_time) 
          FROM app_smart_meter_data 
          WHERE meter_id = sm.id
        )
      ${whereClause}
    `, queryParams)

    // Get real-time alerts
    const alerts = await executeQuery(`
      SELECT 
        sma.id,
        sma.meter_id,
        sma.alert_type,
        sma.message,
        sma.severity,
        sma.created_at,
        sm.meter_id as meter_identifier,
        sm.customer_name,
        sm.location
      FROM app_smart_meter_alerts sma
      LEFT JOIN app_smart_meters sm ON sma.meter_id = sm.id
      WHERE sma.status = 'active'
        AND sma.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      ORDER BY sma.created_at DESC
      LIMIT 20
    `)

    // Get consumption trends (last 24 hours)
    const consumptionTrends = await executeQuery(`
      SELECT 
        DATE_FORMAT(smd.reading_time, '%H:00') as hour,
        AVG(smd.current_reading) as avg_consumption,
        SUM(smd.daily_consumption) as total_consumption,
        COUNT(DISTINCT smd.meter_id) as active_meters
      FROM app_smart_meter_data smd
      LEFT JOIN app_smart_meters sm ON smd.meter_id = sm.id
      WHERE smd.reading_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ${whereClause.replace('sm.', 'sm.')}
      GROUP BY DATE_FORMAT(smd.reading_time, '%H:00')
      ORDER BY hour
    `, queryParams)

    return NextResponse.json({
      success: true,
      data: {
        meters: smartMeters.map((meter: any) => ({
          ...meter,
          current_reading: Math.round(meter.current_reading || 0),
          daily_consumption: Math.round(meter.daily_consumption || 0),
          monthly_consumption: Math.round(meter.monthly_consumption || 0),
          signal_strength: Math.round(meter.signal_strength || 0),
          battery_level: Math.round(meter.battery_level || 0),
          health_score: Math.round(((meter.signal_strength || 0) + (meter.battery_level || 0)) / 2)
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        statistics: {
          ...stats[0],
          avg_current_reading: Math.round(stats[0]?.avg_current_reading || 0),
          total_daily_consumption: Math.round(stats[0]?.total_daily_consumption || 0),
          avg_signal_strength: Math.round(stats[0]?.avg_signal_strength || 0),
          avg_battery_level: Math.round(stats[0]?.avg_battery_level || 0),
          online_percentage: stats[0]?.total_meters > 0 ? 
            Math.round(((stats[0].total_meters - stats[0].offline_meters) / stats[0].total_meters) * 100) : 0
        },
        alerts,
        trends: consumptionTrends,
        lastUpdated: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('❌ Error fetching smart meter monitoring data:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch smart meter monitoring data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, meterId, data } = body

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    switch (action) {
      case 'send_command':
        // Send command to smart meter
        await executeQuery(`
          INSERT INTO app_smart_meter_commands 
          (meter_id, command_type, command_data, status, created_at)
          VALUES (?, ?, ?, 'pending', NOW())
        `, [meterId, data.commandType, JSON.stringify(data.commandData)])
        break

      case 'update_configuration':
        await executeQuery(`
          UPDATE app_smart_meters 
          SET reading_interval = ?, reporting_interval = ?, updated_at = NOW()
          WHERE id = ?
        `, [data.readingInterval, data.reportingInterval, meterId])
        break

      case 'reset_meter':
        await executeQuery(`
          INSERT INTO app_smart_meter_commands 
          (meter_id, command_type, command_data, status, created_at)
          VALUES (?, 'reset', '{}', 'pending', NOW())
        `, [meterId])
        break

      case 'calibrate_meter':
        await executeQuery(`
          INSERT INTO app_smart_meter_commands 
          (meter_id, command_type, command_data, status, created_at)
          VALUES (?, 'calibrate', ?, 'pending', NOW())
        `, [meterId, JSON.stringify(data.calibrationData)])
        break

      case 'generate_report':
        const reportData = await executeQuery(`
          SELECT 
            sm.meter_id,
            sm.customer_name,
            sm.location,
            AVG(smd.current_reading) as avg_consumption,
            MAX(smd.current_reading) as peak_consumption,
            MIN(smd.current_reading) as min_consumption,
            SUM(smd.daily_consumption) as total_consumption,
            COUNT(smd.id) as reading_count
          FROM app_smart_meters sm
          LEFT JOIN app_smart_meter_data smd ON sm.id = smd.meter_id
          WHERE sm.id = ?
            AND smd.reading_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
          GROUP BY sm.id, sm.meter_id, sm.customer_name, sm.location
        `, [meterId])

        return NextResponse.json({
          success: true,
          report: reportData[0],
          message: 'Monitoring report generated successfully'
        })

      case 'bulk_command':
        const { meterIds, commandType, commandData } = data
        
        for (const id of meterIds) {
          await executeQuery(`
            INSERT INTO app_smart_meter_commands 
            (meter_id, command_type, command_data, status, created_at)
            VALUES (?, ?, ?, 'pending', NOW())
          `, [id, commandType, JSON.stringify(commandData)])
        }
        break

      case 'simulate_reading':
        // Simulate meter reading for testing
        await executeQuery(`
          INSERT INTO app_smart_meter_data 
          (meter_id, current_reading, voltage, current, power_factor, frequency, 
           daily_consumption, signal_strength, battery_level, reading_time)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        `, [
          meterId,
          data.currentReading || Math.random() * 1000,
          data.voltage || 220 + (Math.random() * 20 - 10),
          data.current || Math.random() * 50,
          data.powerFactor || 0.8 + (Math.random() * 0.2),
          data.frequency || 50 + (Math.random() * 2 - 1),
          data.dailyConsumption || Math.random() * 100,
          data.signalStrength || 50 + Math.random() * 50,
          data.batteryLevel || 70 + Math.random() * 30
        ])
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: 'Action completed successfully'
    })

  } catch (error) {
    console.error('❌ Error processing smart meter monitoring action:', error)
    return NextResponse.json(
      {
        error: 'Failed to process action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
