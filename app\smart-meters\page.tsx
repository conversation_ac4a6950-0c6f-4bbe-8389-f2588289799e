"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Wifi,
  WifiOff,
  Zap,
  Activity,
  TrendingUp,
  TrendingDown,
  MapPin,
  Clock,
  Battery,
  Signal,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Search,
  Filter,
  Download,
  Plus,
  Settings,
  Eye,
  Edit,
  Trash2,
  Radio,
  Gauge,
  BarChart3,
  Pie<PERSON>hart,
  LineChart
} from 'lucide-react'
import {
  LineChart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON>ontainer
} from 'recharts'
import { MainLayout } from "@/src/components/layout/main-layout"

// Mock smart meter data
const mockSmartMeters = [
  {
    id: 'SM-AA-001',
    location: 'Lideta, Addis Ababa',
    transformerId: 'T-AA-001',
    status: 'online',
    batteryLevel: 85,
    signalStrength: 92,
    lastReading: '2024-02-12T14:30:00Z',
    currentUsage: 245.8,
    voltage: 220.5,
    current: 1.12,
    powerFactor: 0.95,
    frequency: 50.1,
    temperature: 28.5,
    installDate: '2023-06-15',
    firmwareVersion: '2.1.4',
    communicationProtocol: 'LoRaWAN'
  },
  {
    id: 'SM-OR-045',
    location: 'Sebeta, Oromia',
    transformerId: 'T-OR-045',
    status: 'online',
    batteryLevel: 72,
    signalStrength: 78,
    lastReading: '2024-02-12T14:28:00Z',
    currentUsage: 189.3,
    voltage: 218.2,
    current: 0.87,
    powerFactor: 0.92,
    frequency: 49.9,
    temperature: 31.2,
    installDate: '2023-08-22',
    firmwareVersion: '2.1.3',
    communicationProtocol: 'LoRaWAN'
  },
  {
    id: 'SM-AM-023',
    location: 'Bahir Dar, Amhara',
    transformerId: 'T-AM-023',
    status: 'offline',
    batteryLevel: 15,
    signalStrength: 0,
    lastReading: '2024-02-11T09:15:00Z',
    currentUsage: 0,
    voltage: 0,
    current: 0,
    powerFactor: 0,
    frequency: 0,
    temperature: 0,
    installDate: '2023-05-10',
    firmwareVersion: '2.0.8',
    communicationProtocol: 'LoRaWAN'
  }
]

const smartMeterStats = {
  total: 1247,
  online: 1156,
  offline: 67,
  lowBattery: 24,
  weakSignal: 45,
  totalUsage: 2847.6,
  avgUsage: 228.4,
  peakUsage: 456.8
}

const usageData = Array.from({ length: 24 }, (_, i) => ({
  hour: `${i}:00`,
  usage: 150 + Math.random() * 200,
  voltage: 215 + Math.random() * 10,
  current: 0.5 + Math.random() * 1.5
}))

const statusColors = {
  online: 'bg-green-100 text-green-800 border-green-200',
  offline: 'bg-red-100 text-red-800 border-red-200',
  maintenance: 'bg-yellow-100 text-yellow-800 border-yellow-200'
}

const getSignalIcon = (strength: number) => {
  if (strength >= 80) return <Signal className="h-4 w-4 text-green-500" />
  if (strength >= 60) return <Signal className="h-4 w-4 text-yellow-500" />
  if (strength >= 40) return <Signal className="h-4 w-4 text-orange-500" />
  return <Signal className="h-4 w-4 text-red-500" />
}

const getBatteryIcon = (level: number) => {
  if (level >= 80) return <Battery className="h-4 w-4 text-green-500" />
  if (level >= 50) return <Battery className="h-4 w-4 text-yellow-500" />
  if (level >= 20) return <Battery className="h-4 w-4 text-orange-500" />
  return <Battery className="h-4 w-4 text-red-500" />
}

export default function SmartMetersPage() {
  const [meters, setMeters] = useState([])
  const [meterStats, setMeterStats] = useState(smartMeterStats)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedRegion, setSelectedRegion] = useState('all')

  // Load smart meters data from database
  useEffect(() => {
    const loadSmartMetersData = async () => {
      try {
        console.log('📡 Loading smart meters data from MySQL...')
        const response = await fetch('/api/smart-meters')

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            const metersData = data.data || []
            setMeters(metersData.length > 0 ? metersData : mockSmartMeters)

            // Calculate statistics from meters data
            if (metersData.length > 0) {
              const stats = {
                total: metersData.length,
                online: metersData.filter((m: any) => m.status === 'online').length,
                offline: metersData.filter((m: any) => m.status === 'offline').length,
                lowBattery: metersData.filter((m: any) => m.batteryLevel < 20).length,
                weakSignal: metersData.filter((m: any) => m.signalStrength < 40).length,
                totalUsage: metersData.reduce((sum: number, m: any) => sum + (m.currentUsage || 0), 0),
                avgUsage: metersData.length > 0 ? metersData.reduce((sum: number, m: any) => sum + (m.currentUsage || 0), 0) / metersData.length : 0,
                peakUsage: Math.max(...metersData.map((m: any) => m.currentUsage || 0))
              }
              setMeterStats(stats)
            }

            console.log('✅ Smart meters data loaded successfully')
          } else {
            throw new Error(data.error || 'Failed to load smart meters data')
          }
        } else {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
      } catch (error) {
        console.error('❌ Error loading smart meters data:', error)
        // Fallback to mock data
        setMeters(mockSmartMeters)
      } finally {
        setLoading(false)
      }
    }

    loadSmartMetersData()
  }, [])

  const filteredMeters = meters.filter(meter => {
    const matchesSearch = meter.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         meter.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         meter.transformerId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || meter.status === selectedStatus
    const matchesRegion = selectedRegion === 'all' || meter.location.includes(selectedRegion)

    return matchesSearch && matchesStatus && matchesRegion
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading smart meters...</p>
        </div>
      </div>
    )
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "regional_admin",
        "regional_asset_manager",
        "service_center_manager"
      ]}
    >
      <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Smart Meters Management</h1>
              <p className="text-muted-foreground">
                Monitor and manage smart meter network across Ethiopian Electric Utility
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Meter Settings
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Meter
              </Button>
            </div>
          </div>

          {/* Smart Meter Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Meters</CardTitle>
                <Wifi className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{meterStats.total.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {meterStats.online} online
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Online Status</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {meterStats.total > 0 ? ((meterStats.online / meterStats.total) * 100).toFixed(1) : 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {meterStats.offline} offline
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
                <Zap className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{meterStats.totalUsage.toFixed(1)} kWh</div>
                <p className="text-xs text-muted-foreground">
                  Avg: {meterStats.avgUsage.toFixed(1)} kWh
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Alerts</CardTitle>
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {meterStats.lowBattery + meterStats.weakSignal}
                </div>
                <p className="text-xs text-muted-foreground">
                  {meterStats.lowBattery} low battery, {meterStats.weakSignal} weak signal
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="meters">Meter List</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Usage Trends Chart */}
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle>Usage Trends (24h)</CardTitle>
                    <CardDescription>Real-time energy consumption monitoring</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <RechartsLineChart data={usageData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="hour" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="usage" stroke="#3b82f6" name="Usage (kWh)" />
                        <Line type="monotone" dataKey="voltage" stroke="#10b981" name="Voltage (V)" />
                        <Line type="monotone" dataKey="current" stroke="#f59e0b" name="Current (A)" />
                      </RechartsLineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Network Status */}
                <Card>
                  <CardHeader>
                    <CardTitle>Network Status</CardTitle>
                    <CardDescription>Communication network health</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">LoRaWAN Coverage</span>
                        <span className="text-sm text-green-600 font-medium">98.5%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Data Transmission</span>
                        <span className="text-sm text-green-600 font-medium">Normal</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Gateway Status</span>
                        <span className="text-sm text-green-600 font-medium">Online</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Packet Loss</span>
                        <span className="text-sm text-yellow-600 font-medium">0.8%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Regional Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Regional Distribution</CardTitle>
                  <CardDescription>Smart meter deployment across regions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    {[
                      { region: 'Addis Ababa', count: 387, online: 375, percentage: 96.9 },
                      { region: 'Oromia', count: 298, online: 285, percentage: 95.6 },
                      { region: 'Amhara', count: 234, online: 220, percentage: 94.0 },
                      { region: 'Tigray', count: 178, online: 165, percentage: 92.7 },
                      { region: 'SNNPR', count: 150, online: 142, percentage: 94.7 }
                    ].map((region) => (
                      <Card key={region.region} className="text-center">
                        <CardContent className="pt-4">
                          <h3 className="font-semibold text-sm">{region.region}</h3>
                          <div className="text-2xl font-bold mt-2">{region.count}</div>
                          <div className="text-xs text-muted-foreground">
                            {region.online} online ({region.percentage}%)
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="meters" className="space-y-4">
              {/* Filters */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <Input
                        placeholder="Search meters..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="max-w-sm"
                      />
                    </div>
                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="border rounded-md px-3 py-2"
                    >
                      <option value="all">All Status</option>
                      <option value="online">Online</option>
                      <option value="offline">Offline</option>
                      <option value="maintenance">Maintenance</option>
                    </select>
                    <select
                      value={selectedRegion}
                      onChange={(e) => setSelectedRegion(e.target.value)}
                      className="border rounded-md px-3 py-2"
                    >
                      <option value="all">All Regions</option>
                      <option value="Addis Ababa">Addis Ababa</option>
                      <option value="Oromia">Oromia</option>
                      <option value="Amhara">Amhara</option>
                      <option value="Tigray">Tigray</option>
                      <option value="SNNPR">SNNPR</option>
                    </select>
                    <Button variant="outline">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Meters Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredMeters.map((meter) => (
                  <Card key={meter.id}>
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-lg">{meter.id}</CardTitle>
                          <CardDescription>{meter.location}</CardDescription>
                        </div>
                        <Badge className={statusColors[meter.status as keyof typeof statusColors]}>
                          {meter.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Transformer:</span>
                          <span className="text-sm font-medium">{meter.transformerId}</span>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Current Usage:</span>
                          <span className="text-sm font-medium">{meter.currentUsage.toFixed(1)} kWh</span>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Voltage:</span>
                          <span className="text-sm font-medium">{meter.voltage.toFixed(1)} V</span>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Battery:</span>
                          <div className="flex items-center gap-2">
                            {getBatteryIcon(meter.batteryLevel)}
                            <span className="text-sm font-medium">{meter.batteryLevel}%</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Signal:</span>
                          <div className="flex items-center gap-2">
                            {getSignalIcon(meter.signalStrength)}
                            <span className="text-sm font-medium">{meter.signalStrength}%</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Last Reading:</span>
                          <span className="text-sm font-medium">
                            {new Date(meter.lastReading).toLocaleString()}
                          </span>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Firmware:</span>
                          <span className="text-sm font-medium">{meter.firmwareVersion}</span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 mt-4">
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-1" />
                          Details
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4 mr-1" />
                          Configure
                        </Button>
                        <Button size="sm" variant="outline">
                          <Activity className="h-4 w-4 mr-1" />
                          Monitor
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Usage Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Usage Distribution</CardTitle>
                    <CardDescription>Energy consumption patterns</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={Array.from({ length: 7 }, (_, i) => ({
                        day: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][i],
                        usage: 1800 + Math.random() * 600,
                        peak: 2200 + Math.random() * 400
                      }))}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="day" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="usage" fill="#3b82f6" name="Average Usage" />
                        <Bar dataKey="peak" fill="#10b981" name="Peak Usage" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Communication Quality */}
                <Card>
                  <CardHeader>
                    <CardTitle>Communication Quality</CardTitle>
                    <CardDescription>Network performance metrics</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Signal Strength</span>
                          <span>85%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Data Transmission Rate</span>
                          <span>92%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Battery Health</span>
                          <span>78%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '78%' }}></div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Firmware Updates</span>
                          <span>95%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-purple-500 h-2 rounded-full" style={{ width: '95%' }}></div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Performance Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                  <CardDescription>Key performance indicators for smart meter network</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">99.2%</div>
                      <p className="text-sm text-muted-foreground">Uptime</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">1.2s</div>
                      <p className="text-sm text-muted-foreground">Avg Response Time</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">0.3%</div>
                      <p className="text-sm text-muted-foreground">Data Loss Rate</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">15min</div>
                      <p className="text-sm text-muted-foreground">Reading Interval</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="maintenance" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Maintenance Schedule */}
                <Card>
                  <CardHeader>
                    <CardTitle>Maintenance Schedule</CardTitle>
                    <CardDescription>Upcoming maintenance activities</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { id: 'SM-AA-001', task: 'Battery Replacement', date: '2024-02-20', priority: 'High' },
                        { id: 'SM-OR-045', task: 'Firmware Update', date: '2024-02-22', priority: 'Medium' },
                        { id: 'SM-AM-023', task: 'Signal Booster Install', date: '2024-02-25', priority: 'High' }
                      ].map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <p className="font-medium">{item.id}</p>
                            <p className="text-sm text-muted-foreground">{item.task}</p>
                          </div>
                          <div className="text-right">
                            <Badge variant={item.priority === 'High' ? 'destructive' : 'secondary'}>
                              {item.priority}
                            </Badge>
                            <p className="text-sm text-muted-foreground mt-1">{item.date}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Health Status */}
                <Card>
                  <CardHeader>
                    <CardTitle>Health Status</CardTitle>
                    <CardDescription>Overall network health indicators</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Meters Requiring Attention</span>
                        <span className="text-sm text-red-600 font-medium">24</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Low Battery Alerts</span>
                        <span className="text-sm text-yellow-600 font-medium">18</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Communication Issues</span>
                        <span className="text-sm text-orange-600 font-medium">6</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Firmware Updates Pending</span>
                        <span className="text-sm text-blue-600 font-medium">12</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
      </div>
    </MainLayout>
  )
}