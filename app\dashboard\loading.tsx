import { MainLayout } from "@/src/components/layout/main-layout"

export default function DashboardLoading() {
  return (
    <MainLayout>
      <div className="flex flex-col gap-4">
        <div className="mb-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <div className="h-8 w-48 bg-muted animate-pulse rounded-md mb-2"></div>
              <div className="h-4 w-64 bg-muted animate-pulse rounded-md"></div>
            </div>
            <div className="flex flex-wrap gap-2">
              <div className="h-9 w-24 bg-muted animate-pulse rounded-md"></div>
              <div className="h-9 w-24 bg-muted animate-pulse rounded-md"></div>
              <div className="h-9 w-24 bg-muted animate-pulse rounded-md"></div>
            </div>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="h-28 bg-muted animate-pulse rounded-lg"></div>
          ))}
        </div>

        <div className="h-10 w-full bg-muted animate-pulse rounded-md mt-4 mb-2"></div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <div className="lg:col-span-4 h-80 bg-muted animate-pulse rounded-lg"></div>
          <div className="lg:col-span-3 h-80 bg-muted animate-pulse rounded-lg"></div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <div className="h-64 bg-muted animate-pulse rounded-lg"></div>
          <div className="h-64 bg-muted animate-pulse rounded-lg"></div>
          <div className="h-64 bg-muted animate-pulse rounded-lg"></div>
        </div>
      </div>
    </MainLayout>
  )
}
