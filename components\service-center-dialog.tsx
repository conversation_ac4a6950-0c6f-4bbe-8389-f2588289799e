"use client"

import type React from "react"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Switch } from "@/src/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import type { UserRole } from "@/src/types/auth"

interface ServiceCenterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  serviceCenter: any | null
  onSave: (serviceCenter: any) => void
  regions: { id: string; name: string }[]
  currentUserRole?: UserRole
  currentUserRegionId?: string
}

export function ServiceCenterDialog({
  open,
  onOpenChange,
  serviceCenter,
  onSave,
  regions,
  currentUserRole,
  currentUserRegionId,
}: ServiceCenterDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    regionId: "",
    address: "",
    managerName: "",
    contactPhone: "",
    status: true,
  })

  // Reset form when dialog opens or service center changes
  useEffect(() => {
    if (serviceCenter) {
      setFormData({
        name: serviceCenter.name || "",
        regionId: serviceCenter.regionId || "",
        address: serviceCenter.address || "",
        managerName: serviceCenter.managerName || "",
        contactPhone: serviceCenter.contactPhone || "",
        status: serviceCenter.status === "active",
      })
    } else {
      setFormData({
        name: "",
        regionId: currentUserRegionId || "",
        address: "",
        managerName: "",
        contactPhone: "",
        status: true,
      })
    }
  }, [serviceCenter, open, currentUserRegionId])

  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Find the region name based on the selected regionId
    const selectedRegion = regions.find((region) => region.id === formData.regionId)

    onSave({
      id: serviceCenter?.id || `sc-${Date.now()}`,
      name: formData.name,
      regionId: formData.regionId,
      regionName: selectedRegion?.name || "",
      address: formData.address,
      managerName: formData.managerName,
      contactPhone: formData.contactPhone,
      status: formData.status ? "active" : "inactive",
      transformers: serviceCenter?.transformers || 0,
      technicians: serviceCenter?.technicians || 0,
    })
  }

  // Get available regions based on current user's role
  const getAvailableRegions = () => {
    if (currentUserRole === "super_admin" || currentUserRole === "national_asset_manager") {
      return regions
    } else if (currentUserRole === "regional_admin" && currentUserRegionId) {
      return regions.filter((region) => region.id === currentUserRegionId)
    }
    return []
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>{serviceCenter ? "Edit Service Center" : "Add New Service Center"}</DialogTitle>
            <DialogDescription>
              {serviceCenter
                ? "Update service center details and status."
                : "Create a new service center for the Ethiopia Electric Utility."}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Service Center Name</Label>
              <Input id="name" value={formData.name} onChange={(e) => handleChange("name", e.target.value)} required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="region">Region</Label>
              <Select
                value={formData.regionId}
                onValueChange={(value) => handleChange("regionId", value)}
                disabled={currentUserRole !== "super_admin" && currentUserRole !== "national_asset_manager"}
              >
                <SelectTrigger id="region">
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  {getAvailableRegions().map((region) => (
                    <SelectItem key={region.id} value={region.id}>
                      {region.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                value={formData.address}
                onChange={(e) => handleChange("address", e.target.value)}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="managerName">Manager Name</Label>
                <Input
                  id="managerName"
                  value={formData.managerName}
                  onChange={(e) => handleChange("managerName", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactPhone">Contact Phone</Label>
                <Input
                  id="contactPhone"
                  value={formData.contactPhone}
                  onChange={(e) => handleChange("contactPhone", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="status"
                checked={formData.status}
                onCheckedChange={(checked) => handleChange("status", checked)}
              />
              <Label htmlFor="status">Active Service Center</Label>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">{serviceCenter ? "Update Service Center" : "Create Service Center"}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
