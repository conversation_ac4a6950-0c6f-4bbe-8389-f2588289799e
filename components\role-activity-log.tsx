"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { 
  RefreshCw, 
  Clock, 
  Filter, 
  X, 
  <PERSON>r<PERSON>he<PERSON>, 
  UserX, 
  FileText, 
  Edit, 
  Trash, 
  Plus,
  Shield,
  Calendar
} from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useToast } from "@/src/components/ui/use-toast"
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/src/components/ui/tooltip"
import { format, parseISO, subDays } from "date-fns"
import type { RoleDefinition } from "@/src/types/auth"

interface RoleActivity {
  id: string
  timestamp: string
  action: "create" | "update" | "delete" | "assign" | "unassign"
  roleName: string
  roleId: string
  performedBy: string
  details?: string
}

interface RoleActivityLogProps {
  roles: RoleDefinition[]
}

export function RoleActivityLog({ roles }: RoleActivityLogProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [actionFilter, setActionFilter] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  // Generate mock activity data
  const generateMockActivities = (): RoleActivity[] => {
    const activities: RoleActivity[] = []
    const users = ["Abebe Kebede", "Tigist Haile", "Dawit Tadesse", "Hiwot Gebre"]
    const now = new Date()
    
    // Create activities for each role
    roles.forEach((role, index) => {
      // Create role
      activities.push({
        id: `activity-${activities.length + 1}`,
        timestamp: subDays(now, 30 + Math.floor(Math.random() * 60)).toISOString(),
        action: "create",
        roleName: role.name,
        roleId: role.id,
        performedBy: users[Math.floor(Math.random() * users.length)],
        details: `Created new role: ${role.name}`
      })
      
      // Update role (for some roles)
      if (index % 3 === 0) {
        activities.push({
          id: `activity-${activities.length + 1}`,
          timestamp: subDays(now, 10 + Math.floor(Math.random() * 20)).toISOString(),
          action: "update",
          roleName: role.name,
          roleId: role.id,
          performedBy: users[Math.floor(Math.random() * users.length)],
          details: `Updated permissions for ${role.name}`
        })
      }
      
      // Assign role to users (multiple times for some roles)
      const assignCount = 1 + Math.floor(Math.random() * 3)
      for (let i = 0; i < assignCount; i++) {
        activities.push({
          id: `activity-${activities.length + 1}`,
          timestamp: subDays(now, Math.floor(Math.random() * 30)).toISOString(),
          action: "assign",
          roleName: role.name,
          roleId: role.id,
          performedBy: users[Math.floor(Math.random() * users.length)],
          details: `Assigned ${role.name} to ${users[Math.floor(Math.random() * users.length)]}`
        })
      }
      
      // Unassign role (for some roles)
      if (index % 5 === 0) {
        activities.push({
          id: `activity-${activities.length + 1}`,
          timestamp: subDays(now, Math.floor(Math.random() * 15)).toISOString(),
          action: "unassign",
          roleName: role.name,
          roleId: role.id,
          performedBy: users[Math.floor(Math.random() * users.length)],
          details: `Removed ${role.name} from ${users[Math.floor(Math.random() * users.length)]}`
        })
      }
      
      // Delete role (only for custom roles)
      if (!role.isSystemRole && index % 2 === 0) {
        activities.push({
          id: `activity-${activities.length + 1}`,
          timestamp: subDays(now, Math.floor(Math.random() * 10)).toISOString(),
          action: "delete",
          roleName: role.name,
          roleId: role.id,
          performedBy: users[Math.floor(Math.random() * users.length)],
          details: `Deleted role: ${role.name}`
        })
      }
    })
    
    // Sort by timestamp (newest first)
    return activities.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )
  }

  const activities = generateMockActivities()

  // Filter activities based on search query and action filter
  const filteredActivities = activities.filter((activity) => {
    const matchesSearch =
      activity.roleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.performedBy.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (activity.details && activity.details.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesAction = actionFilter === "all" || activity.action === actionFilter

    return matchesSearch && matchesAction
  })

  const handleClearFilters = () => {
    setSearchQuery("")
    setActionFilter("all")
  }

  const handleRefresh = () => {
    setIsLoading(true)
    
    // In a real app, this would fetch fresh data from the API
    setTimeout(() => {
      toast({
        title: "Activity log refreshed",
        description: "Activity log has been refreshed successfully.",
      })
      setIsLoading(false)
    }, 500)
  }

  // Get icon based on activity type
  const getActivityIcon = (action: string) => {
    switch (action) {
      case "create":
        return <Plus className="h-4 w-4 text-green-500" />
      case "update":
        return <Edit className="h-4 w-4 text-amber-500" />
      case "delete":
        return <Trash className="h-4 w-4 text-red-500" />
      case "assign":
        return <UserCheck className="h-4 w-4 text-blue-500" />
      case "unassign":
        return <UserX className="h-4 w-4 text-slate-500" />
      default:
        return <Shield className="h-4 w-4 text-gray-500" />
    }
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), "MMM d, yyyy 'at' h:mm a")
    } catch (error) {
      return dateString
    }
  }

  return (
    <TooltipProvider>
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Clock className="mr-2 h-5 w-5 text-muted-foreground" />
                Role Activity Log
              </CardTitle>
              <CardDescription>Track changes to roles and assignments</CardDescription>
            </div>
            <Badge variant="outline" className="ml-2">
              {filteredActivities.length} of {activities.length} activities
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row mb-4">
            <div className="relative flex-1">
              <Input
                type="search"
                placeholder="Search activities..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full rounded-l-none"
                  onClick={() => setSearchQuery("")}
                  aria-label="Clear search"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              <Select value={actionFilter} onValueChange={setActionFilter}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Action Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Actions</SelectItem>
                  <SelectItem value="create">Create</SelectItem>
                  <SelectItem value="update">Update</SelectItem>
                  <SelectItem value="delete">Delete</SelectItem>
                  <SelectItem value="assign">Assign</SelectItem>
                  <SelectItem value="unassign">Unassign</SelectItem>
                </SelectContent>
              </Select>
              
              {(searchQuery || actionFilter !== "all") && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={handleClearFilters}>
                      <X className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Clear all filters</p>
                  </TooltipContent>
                </Tooltip>
              )}
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleRefresh} disabled={isLoading}>
                    <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Refresh activity log</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Action</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Performed By</TableHead>
                  <TableHead>Details</TableHead>
                  <TableHead>Date & Time</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredActivities.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      No activities found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredActivities.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getActivityIcon(activity.action)}
                          <span className="capitalize">{activity.action}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          {activity.roleName}
                        </Badge>
                      </TableCell>
                      <TableCell>{activity.performedBy}</TableCell>
                      <TableCell>{activity.details}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          {formatDate(activity.timestamp)}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  )
}
