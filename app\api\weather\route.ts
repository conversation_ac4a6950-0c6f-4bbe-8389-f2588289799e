import { NextResponse } from "next/server"

// OpenWeatherMap API key would typically be stored in environment variables
const API_KEY = process.env.OPENWEATHER_API_KEY || "demo_key"

// Mock weather data for Ethiopia when API is not available
const mockWeatherData = {
  "Addis Ababa": {
    temperature: 22,
    condition: "Partly Cloudy",
    humidity: 45,
    windSpeed: 10,
    icon: "partly-cloudy"
  },
  "Amhara": {
    temperature: 24,
    condition: "Sunny",
    humidity: 40,
    windSpeed: 8,
    icon: "sunny"
  },
  "Oromia": {
    temperature: 26,
    condition: "Clear",
    humidity: 35,
    windSpeed: 12,
    icon: "clear"
  },
  "SNNPR": {
    temperature: 25,
    condition: "Cloudy",
    humidity: 50,
    windSpeed: 15,
    icon: "cloudy"
  },
  "Tigray": {
    temperature: 28,
    condition: "Hot",
    humidity: 30,
    windSpeed: 18,
    icon: "hot"
  },
  "Somali": {
    temperature: 32,
    condition: "Hot",
    humidity: 25,
    windSpeed: 20,
    icon: "hot"
  }
};

// Helper function to get region from coordinates
function getRegionFromCoordinates(lat: number, lon: number): string {
  // This is a very simplified approach
  // In a real application, you would use a geolocation service or database

  // Approximate coordinates for regions in Ethiopia
  const regions = [
    { name: "Addis Ababa", lat: 9.0222, lon: 38.7468 },
    { name: "Amhara", lat: 11.5742, lon: 37.3614 },
    { name: "Oromia", lat: 8.5400, lon: 39.2700 },
    { name: "SNNPR", lat: 7.0504, lon: 38.4955 },
    { name: "Tigray", lat: 13.4967, lon: 39.4697 },
    { name: "Somali", lat: 9.3151, lon: 42.1254 }
  ];

  // Find the closest region (very approximate)
  let closestRegion = regions[0];
  let minDistance = Number.MAX_VALUE;

  for (const region of regions) {
    const distance = Math.sqrt(
      Math.pow(region.lat - lat, 2) +
      Math.pow(region.lon - lon, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestRegion = region;
    }
  }

  return closestRegion.name;
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)

  // Check if coordinates are provided
  const lat = searchParams.get("lat")
  const lon = searchParams.get("lon")

  // If coordinates are provided, use them; otherwise, use location name
  let apiUrl: string
  let useMock = false

  if (lat && lon) {
    apiUrl = `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&units=metric&appid=${API_KEY}`
  } else {
    const location = searchParams.get("location") || "Addis Ababa,et"
    apiUrl = `https://api.openweathermap.org/data/2.5/weather?q=${location}&units=metric&appid=${API_KEY}`
  }

  try {
    // Try to fetch from OpenWeatherMap API
    const currentWeatherResponse = await fetch(apiUrl)

    // If API call is successful, return the data
    if (currentWeatherResponse.ok) {
      const currentWeather = await currentWeatherResponse.json()

      // Fetch forecast data (5 day / 3 hour forecast)
      let forecastData = null

      try {
        let forecastUrl: string

        if (lat && lon) {
          forecastUrl = `https://api.openweathermap.org/data/2.5/forecast?lat=${lat}&lon=${lon}&units=metric&appid=${API_KEY}`
        } else {
          const location = searchParams.get("location") || "Addis Ababa,et"
          forecastUrl = `https://api.openweathermap.org/data/2.5/forecast?q=${location}&units=metric&appid=${API_KEY}`
        }

        const forecastResponse = await fetch(forecastUrl)

        if (forecastResponse.ok) {
          forecastData = await forecastResponse.json()
        }
      } catch (forecastError) {
        console.error("Forecast API error:", forecastError)
      }

      // Return the API data
      return NextResponse.json({
        current: currentWeather,
        forecast: forecastData,
        timestamp: new Date().toISOString(),
      })
    } else {
      // If API call fails, use mock data
      useMock = true
    }
  } catch (error) {
    console.error("Weather API error:", error)
    useMock = true
  }

  // If we need to use mock data
  if (useMock) {
    // Get coordinates
    const latitude = parseFloat(lat || '9.0222')
    const longitude = parseFloat(lon || '38.7468')

    // Get region from coordinates
    const region = getRegionFromCoordinates(latitude, longitude)

    // Get weather data for the region
    const weatherData = mockWeatherData[region as keyof typeof mockWeatherData] || mockWeatherData["Addis Ababa"]

    // Add some randomness to make it more realistic
    const randomOffset = Math.random() * 4 - 2 // -2 to +2
    const temperature = Math.round(weatherData.temperature + randomOffset)

    // Return mock data in a format similar to OpenWeatherMap
    return NextResponse.json({
      current: {
        name: region,
        coord: { lat: latitude, lon: longitude },
        main: {
          temp: temperature,
          humidity: weatherData.humidity
        },
        weather: [
          {
            main: weatherData.condition,
            description: weatherData.condition,
            icon: weatherData.icon
          }
        ],
        wind: {
          speed: weatherData.windSpeed
        }
      },
      forecast: {
        list: Array(5).fill(null).map((_, i) => ({
          dt: Math.floor(Date.now() / 1000) + i * 86400,
          main: {
            temp: temperature + (Math.random() * 6 - 3),
            humidity: weatherData.humidity + (Math.random() * 10 - 5)
          },
          weather: [
            {
              main: weatherData.condition,
              description: weatherData.condition,
              icon: weatherData.icon
            }
          ],
          wind: {
            speed: weatherData.windSpeed + (Math.random() * 4 - 2)
          }
        }))
      },
      timestamp: new Date().toISOString(),
      source: "mock"
    })
  }
}
