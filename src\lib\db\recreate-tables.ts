import mysql from 'mysql2/promise'

// Database configuration
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: 'dtms_eeu_db',
  multipleStatements: true
}

export async function recreateTablesOnly() {
  let connection: mysql.Connection | null = null

  try {
    console.log('🔄 Connecting to dtms_eeu_db database...')

    // Connect directly to the database
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to dtms_eeu_db database')

    // First, disable foreign key checks to avoid dependency issues
    console.log('🔄 Disabling foreign key checks...')
    await connection.query('SET FOREIGN_KEY_CHECKS = 0')

    // Get all tables in the database
    console.log('🔄 Getting list of all tables...')
    const [tables] = await connection.query('SHOW TABLES')
    const tableList = (tables as any[]).map(row => Object.values(row)[0])
    console.log(`📋 Found ${tableList.length} tables to drop`)

    // Drop all existing tables
    console.log('🔄 Dropping all existing tables...')
    for (const tableName of tableList) {
      try {
        // First try to discard tablespace if it exists
        try {
          await connection.query(`ALTER TABLE ${tableName} DISCARD TABLESPACE`)
          console.log(`🗑️ Discarded tablespace for: ${tableName}`)
        } catch (discardError) {
          // Ignore discard errors - table might not have separate tablespace
        }

        // Then drop the table
        await connection.query(`DROP TABLE IF EXISTS ${tableName}`)
        console.log(`🗑️ Dropped table: ${tableName}`)
      } catch (error) {
        console.log(`⚠️ Could not drop table ${tableName}:`, error)
        // Try force drop
        try {
          await connection.query(`DROP TABLE ${tableName}`)
          console.log(`🗑️ Force dropped table: ${tableName}`)
        } catch (forceError) {
          console.log(`❌ Failed to force drop ${tableName}:`, forceError)
        }
      }
    }

    // Re-enable foreign key checks
    console.log('🔄 Re-enabling foreign key checks...')
    await connection.query('SET FOREIGN_KEY_CHECKS = 1')

    console.log('✅ All tables dropped')

    // Create tables with explicit InnoDB engine
    console.log('🔄 Creating tables...')

    // Regions table
    await connection.query(`
      CREATE TABLE app_regions (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(10) NOT NULL UNIQUE,
        population INT,
        area_km2 DECIMAL(10,2),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `)
    console.log('✅ Created app_regions table')

    // Service Centers table
    await connection.query(`
      CREATE TABLE app_service_centers (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(20) NOT NULL UNIQUE,
        region_id INT,
        address TEXT,
        phone VARCHAR(20),
        email VARCHAR(100),
        manager_name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES app_regions(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `)
    console.log('✅ Created app_service_centers table')

    // Users table
    await connection.query(`
      CREATE TABLE app_users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        phone VARCHAR(20),
        role ENUM('super_admin', 'national_asset_manager', 'national_maintenance_manager',
                 'regional_admin', 'regional_asset_manager', 'regional_maintenance_engineer',
                 'service_center_manager', 'field_technician', 'customer_service_agent',
                 'audit_compliance_officer') NOT NULL,
        region_id INT,
        service_center_id INT,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES app_regions(id),
        FOREIGN KEY (service_center_id) REFERENCES app_service_centers(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `)
    console.log('✅ Created app_users table')

    // Transformers table
    await connection.query(`
      CREATE TABLE app_transformers (
        id INT PRIMARY KEY AUTO_INCREMENT,
        serial_number VARCHAR(50) NOT NULL UNIQUE,
        name VARCHAR(100) NOT NULL,
        type ENUM('distribution', 'power', 'instrument', 'auto') NOT NULL,
        capacity_kva DECIMAL(10,2) NOT NULL,
        voltage_primary DECIMAL(10,2) NOT NULL,
        voltage_secondary DECIMAL(10,2) NOT NULL,
        manufacturer VARCHAR(100),
        model VARCHAR(100),
        year_manufactured YEAR,
        installation_date DATE,
        location_name VARCHAR(200),
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        region_id INT NOT NULL,
        service_center_id INT,
        status ENUM('operational', 'warning', 'maintenance', 'critical', 'burnt') DEFAULT 'operational',
        efficiency_rating DECIMAL(5,2) DEFAULT 95.0,
        load_factor DECIMAL(5,2) DEFAULT 75.0,
        temperature DECIMAL(5,2),
        oil_level DECIMAL(5,2),
        last_maintenance DATE,
        next_maintenance DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES app_regions(id),
        FOREIGN KEY (service_center_id) REFERENCES app_service_centers(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `)
    console.log('✅ Created app_transformers table')

    // Alerts table
    await connection.query(`
      CREATE TABLE app_alerts (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        type ENUM('temperature', 'voltage', 'load', 'maintenance', 'communication', 'weather', 'security') NOT NULL,
        status ENUM('active', 'investigating', 'resolved', 'monitoring') DEFAULT 'active',
        priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        created_by INT,
        assigned_to INT,
        resolved_at TIMESTAMP NULL,
        resolved_by INT,
        is_resolved BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES app_transformers(id),
        FOREIGN KEY (created_by) REFERENCES app_users(id),
        FOREIGN KEY (assigned_to) REFERENCES app_users(id),
        FOREIGN KEY (resolved_by) REFERENCES app_users(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `)
    console.log('✅ Created app_alerts table')

    // Maintenance Schedules table
    await connection.query(`
      CREATE TABLE app_maintenance_schedules (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT NOT NULL,
        type ENUM('routine', 'preventive', 'corrective', 'emergency', 'scheduled') NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        scheduled_date DATE NOT NULL,
        estimated_duration INT,
        priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        status ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'postponed') DEFAULT 'scheduled',
        technician_id INT,
        supervisor_id INT,
        cost_estimate DECIMAL(10,2),
        actual_cost DECIMAL(10,2),
        started_at TIMESTAMP NULL,
        completed_at TIMESTAMP NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES app_transformers(id),
        FOREIGN KEY (technician_id) REFERENCES app_users(id),
        FOREIGN KEY (supervisor_id) REFERENCES app_users(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `)
    console.log('✅ Created app_maintenance_schedules table')

    // Notifications table
    await connection.query(`
      CREATE TABLE app_notifications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
        recipient_id INT,
        sender_id INT,
        is_read BOOLEAN DEFAULT FALSE,
        read_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (recipient_id) REFERENCES app_users(id),
        FOREIGN KEY (sender_id) REFERENCES app_users(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `)
    console.log('✅ Created app_notifications table')

    // Performance Metrics table
    await connection.query(`
      CREATE TABLE app_performance_metrics (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT,
        metric_type ENUM('power_generation', 'efficiency', 'load_factor', 'temperature', 'voltage') NOT NULL,
        value DECIMAL(10,4) NOT NULL,
        unit VARCHAR(20),
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES app_transformers(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `)
    console.log('✅ Created app_performance_metrics table')

    // Weather Data table
    await connection.query(`
      CREATE TABLE app_weather_data (
        id INT PRIMARY KEY AUTO_INCREMENT,
        region_id INT NOT NULL,
        temperature DECIMAL(5,2),
        humidity DECIMAL(5,2),
        wind_speed DECIMAL(5,2),
        weather_condition VARCHAR(50),
        risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES app_regions(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `)
    console.log('✅ Created app_weather_data table')

    console.log('✅ All tables created successfully')

    return {
      success: true,
      message: 'Tables recreated successfully'
    }

  } catch (error) {
    console.error('❌ Table recreation failed:', error)
    return {
      success: false,
      message: `Table recreation failed: ${error}`,
      error
    }
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}
