const mysql = require('mysql2/promise');

async function checkTables() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'dtms_eeu_db'
    });
    
    console.log('🔍 Checking available tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    
    console.log('\n📋 Available tables:');
    tables.forEach((table, i) => {
      const tableName = Object.values(table)[0];
      console.log(`  ${i+1}. ${tableName}`);
    });
    
    // Check for transformer-related tables
    const transformerTables = tables.filter(table => {
      const tableName = Object.values(table)[0];
      return tableName.includes('transformer') || tableName.includes('history') || tableName.includes('inspection') || tableName.includes('megger');
    });
    
    console.log('\n🔧 Transformer-related tables:');
    if (transformerTables.length > 0) {
      transformerTables.forEach((table, i) => {
        const tableName = Object.values(table)[0];
        console.log(`  ${i+1}. ${tableName}`);
      });
    } else {
      console.log('  No specific transformer history/inspection tables found');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Error checking tables:', error);
  }
}

checkTables();
