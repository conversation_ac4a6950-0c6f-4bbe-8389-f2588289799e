"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Download, Search, Edit, Trash, Plus, Building, MapPin } from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useAuth } from "@/src/features/auth/context/auth-context"
import { useToast } from "@/src/components/ui/use-toast"
import { ServiceCenterDialog } from "@/components/service-center-dialog"

export function ServiceCentersManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [regionFilter, setRegionFilter] = useState("all")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedServiceCenter, setSelectedServiceCenter] = useState<any | null>(null)
  const { user: currentUser } = useAuth()
  const { toast } = useToast()

  // Mock service centers data - in a real app, this would come from an API
  const serviceCenters = [
    {
      id: "sc-001",
      name: "Bole Service Center",
      regionId: "region-001",
      regionName: "Addis Ababa",
      address: "Bole Road, Addis Ababa",
      managerName: "Abebe Kebede",
      contactPhone: "+251911234567",
      transformers: 245,
      technicians: 12,
      status: "active",
    },
    {
      id: "sc-002",
      name: "Kirkos Service Center",
      regionId: "region-001",
      regionName: "Addis Ababa",
      address: "Kirkos Sub-city, Addis Ababa",
      managerName: "Tigist Haile",
      contactPhone: "+251922345678",
      transformers: 198,
      technicians: 8,
      status: "active",
    },
    {
      id: "sc-003",
      name: "Adama Service Center",
      regionId: "region-002",
      regionName: "Oromia",
      address: "Main Street, Adama",
      managerName: "Dawit Mengistu",
      contactPhone: "+251933456789",
      transformers: 156,
      technicians: 6,
      status: "active",
    },
    {
      id: "sc-004",
      name: "Bahir Dar Service Center",
      regionId: "region-003",
      regionName: "Amhara",
      address: "Lake Tana Road, Bahir Dar",
      managerName: "Hiwot Tadesse",
      contactPhone: "+251944567890",
      transformers: 132,
      technicians: 5,
      status: "active",
    },
    {
      id: "sc-005",
      name: "Mekelle Service Center",
      regionId: "region-004",
      regionName: "Tigray",
      address: "Central Avenue, Mekelle",
      managerName: "Solomon Bekele",
      contactPhone: "+251955678901",
      transformers: 87,
      technicians: 4,
      status: "inactive",
    },
    {
      id: "sc-006",
      name: "Hawassa Service Center",
      regionId: "region-006",
      regionName: "Sidama",
      address: "Lake View Road, Hawassa",
      managerName: "Meron Alemu",
      contactPhone: "+251966789012",
      transformers: 112,
      technicians: 5,
      status: "active",
    },
  ]

  // Get unique regions for the filter
  const regions = [
    { id: "region-001", name: "Addis Ababa" },
    { id: "region-002", name: "Oromia" },
    { id: "region-003", name: "Amhara" },
    { id: "region-004", name: "Tigray" },
    { id: "region-005", name: "SNNPR" },
    { id: "region-006", name: "Sidama" },
  ]

  // Filter service centers based on search query and region filter
  const filteredServiceCenters = serviceCenters.filter((sc) => {
    const matchesSearch =
      sc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sc.managerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sc.address.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesRegion = regionFilter === "all" || sc.regionId === regionFilter

    // Access control - only show service centers the current user has permission to manage
    let hasAccess = false
    if (currentUser?.role === "super_admin" || currentUser?.role === "national_asset_manager") {
      hasAccess = true
    } else if (currentUser?.role === "regional_admin" && currentUser?.regionId) {
      hasAccess = sc.regionId === currentUser.regionId
    } else if (currentUser?.role === "service_center_manager" && currentUser?.serviceCenterId) {
      hasAccess = sc.id === currentUser.serviceCenterId
    }

    return matchesSearch && matchesRegion && hasAccess
  })

  const handleAddServiceCenter = () => {
    setSelectedServiceCenter(null)
    setIsDialogOpen(true)
  }

  const handleEditServiceCenter = (serviceCenter: any) => {
    setSelectedServiceCenter(serviceCenter)
    setIsDialogOpen(true)
  }

  const handleDeleteServiceCenter = (serviceCenter: any) => {
    // In a real app, this would call an API to delete the service center
    toast({
      title: "Service Center deleted",
      description: `${serviceCenter.name} has been deleted.`,
    })
  }

  const handleServiceCenterSave = (serviceCenter: any) => {
    // In a real app, this would call an API to save the service center
    toast({
      title: selectedServiceCenter ? "Service Center updated" : "Service Center created",
      description: `${serviceCenter.name} has been ${selectedServiceCenter ? "updated" : "created"}.`,
    })
    setIsDialogOpen(false)
  }

  // Check if user has permission to manage service centers
  const canManageServiceCenters =
    currentUser?.role === "super_admin" ||
    currentUser?.role === "national_asset_manager" ||
    currentUser?.role === "regional_admin"

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Service Center Management</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          {canManageServiceCenters && (
            <Button size="sm" onClick={handleAddServiceCenter}>
              <Plus className="mr-2 h-4 w-4" />
              Add Service Center
            </Button>
          )}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Service Centers</CardTitle>
          <CardDescription>Manage service centers, staff, and local assets</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search service centers..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={regionFilter} onValueChange={setRegionFilter}>
                <SelectTrigger className="w-[160px]">
                  <MapPin className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Regions</SelectItem>
                  {regions.map((region) => (
                    <SelectItem key={region.id} value={region.id}>
                      {region.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Service Center</TableHead>
                  <TableHead>Region</TableHead>
                  <TableHead>Manager</TableHead>
                  <TableHead>Transformers</TableHead>
                  <TableHead>Technicians</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredServiceCenters.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No service centers found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredServiceCenters.map((sc) => (
                    <TableRow key={sc.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Building className="h-4 w-4 text-teal-600" />
                          <div>
                            <p className="font-medium">{sc.name}</p>
                            <p className="text-xs text-muted-foreground">{sc.address}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{sc.regionName}</TableCell>
                      <TableCell>
                        <div>
                          <p>{sc.managerName}</p>
                          <p className="text-xs text-muted-foreground">{sc.contactPhone}</p>
                        </div>
                      </TableCell>
                      <TableCell>{sc.transformers}</TableCell>
                      <TableCell>{sc.technicians}</TableCell>
                      <TableCell>
                        <Badge
                          variant={sc.status === "active" ? "default" : "outline"}
                          className={sc.status === "active" ? "bg-green-500 hover:bg-green-600" : ""}
                        >
                          {sc.status === "active" ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleEditServiceCenter(sc)}>
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          {canManageServiceCenters && (
                            <Button variant="ghost" size="icon" onClick={() => handleDeleteServiceCenter(sc)}>
                              <Trash className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <ServiceCenterDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        serviceCenter={selectedServiceCenter}
        onSave={handleServiceCenterSave}
        regions={regions}
        currentUserRole={currentUser?.role}
        currentUserRegionId={currentUser?.regionId}
      />
    </div>
  )
}
