"use client"

import { useState, useEffect } from 'react'
import { MainLayout } from "@/src/components/layout/main-layout"
import { TransformerHistoryCard } from '@/components/transformers/TransformerHistoryCard'
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import {
  Search,
  Filter,
  Plus,
  FileText,
  ClipboardList,
  Download,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Calendar,
  MapPin,
  Zap
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/src/components/ui/dropdown-menu'

export default function TransformerHistoryCardsPage() {
  const [transformers, setTransformers] = useState<any[]>([])
  const [filteredTransformers, setFilteredTransformers] = useState<any[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [regionFilter, setRegionFilter] = useState('all')
  const [isLoading, setIsLoading] = useState(true)
  const [showHistoryCard, setShowHistoryCard] = useState(false)
  const [selectedTransformerId, setSelectedTransformerId] = useState<string | null>(null)

  // Fetch transformers data
  const fetchTransformers = async () => {
    setIsLoading(true)
    try {
      console.log('🔄 Fetching transformers for history cards...')
      const response = await fetch('/api/mysql/transformers', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('✅ Transformers fetched successfully:', data.transformers?.length || 0)

      const transformedData = data.transformers?.map((transformer: any) => ({
        id: transformer.id,
        serialNumber: transformer.serial_number || transformer.serialNumber,
        manufacturer: transformer.manufacturer,
        model: transformer.model,
        type: transformer.type,
        ratingKVA: transformer.capacity || transformer.ratingKVA,
        primaryVoltage: transformer.voltage_primary || transformer.primaryVoltage,
        secondaryVoltage: transformer.voltage_secondary || transformer.secondaryVoltage,
        installationDate: transformer.installation_date || transformer.installationDate,
        status: transformer.status,
        location: {
          region: transformer.region_name || transformer.location?.region || 'Addis Ababa',
          serviceCenter: transformer.service_center_name || transformer.location?.serviceCenter || 'Central',
          address: transformer.location_address || transformer.location?.address || 'Addis Ababa, Ethiopia'
        },
        lastMaintenance: transformer.last_maintenance_date || transformer.lastMaintenance || '2024-01-01',
        nextMaintenance: transformer.next_maintenance_date || transformer.nextMaintenance || '2024-07-01'
      })) || []

      setTransformers(transformedData)
      setFilteredTransformers(transformedData)
    } catch (error) {
      console.error('❌ Error fetching transformers:', error)
      setTransformers([])
      setFilteredTransformers([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchTransformers()
  }, [])

  // Apply filters and search
  useEffect(() => {
    let filtered = [...transformers]

    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(transformer =>
        transformer.serialNumber.toLowerCase().includes(query) ||
        transformer.manufacturer.toLowerCase().includes(query) ||
        transformer.model.toLowerCase().includes(query) ||
        transformer.location.address.toLowerCase().includes(query)
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(transformer => transformer.status === statusFilter)
    }

    if (regionFilter !== 'all') {
      filtered = filtered.filter(transformer => transformer.location.region === regionFilter)
    }

    setFilteredTransformers(filtered)
  }, [transformers, searchQuery, statusFilter, regionFilter])

  const handleOpenHistoryCard = (transformerId: string) => {
    setSelectedTransformerId(transformerId)
    setShowHistoryCard(true)
  }

  const handleCloseHistoryCard = () => {
    setShowHistoryCard(false)
    setSelectedTransformerId(null)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'operational':
        return <Badge className="bg-green-100 text-green-800">Operational</Badge>
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800">Warning</Badge>
      case 'maintenance':
        return <Badge className="bg-blue-100 text-blue-800">Maintenance</Badge>
      case 'critical':
        return <Badge className="bg-red-100 text-red-800">Critical</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>
    }
  }

  if (isLoading) {
    return (
      <MainLayout
        allowedRoles={[
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician"
        ]}
        requiredPermissions={[{ resource: "transformers", action: "read" }]}
      >
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "national_maintenance_manager",
        "regional_admin",
        "regional_asset_manager",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician"
      ]}
      requiredPermissions={[{ resource: "transformers", action: "read" }]}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Transformer History Cards</h1>
            <p className="text-muted-foreground">
              Ethiopian Electric Utility Distribution Transformer History Card Management
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export All Cards
            </Button>
            <Button variant="outline" onClick={fetchTransformers}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Transformers</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredTransformers.length}</div>
              <p className="text-xs text-muted-foreground">
                Available for history cards
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Operational</CardTitle>
              <FileText className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {filteredTransformers.filter(t => t.status === 'operational').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Active transformers
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Maintenance Due</CardTitle>
              <ClipboardList className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {filteredTransformers.filter(t => new Date(t.nextMaintenance) < new Date()).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Require inspection
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Regions</CardTitle>
              <MapPin className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {new Set(filteredTransformers.map(t => t.location.region)).size}
              </div>
              <p className="text-xs text-muted-foreground">
                Coverage areas
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg border p-4 mb-6">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search transformers..."
                  className="pl-9 pr-4 py-2 border rounded-md w-64 focus:outline-none focus:ring-1 focus:ring-green-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search size={16} className="absolute left-3 top-3 text-gray-400" />
              </div>

              <select
                className="border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="operational">Operational</option>
                <option value="warning">Warning</option>
                <option value="maintenance">Maintenance</option>
                <option value="critical">Critical</option>
              </select>

              <select
                className="border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                value={regionFilter}
                onChange={(e) => setRegionFilter(e.target.value)}
              >
                <option value="all">All Regions</option>
                <option value="Addis Ababa">Addis Ababa</option>
                <option value="Oromia">Oromia</option>
                <option value="Amhara">Amhara</option>
                <option value="Tigray">Tigray</option>
                <option value="SNNP">SNNP</option>
              </select>
            </div>
          </div>
        </div>

        {/* Transformers List */}
        <Card>
          <CardHeader>
            <CardTitle>Transformer History Cards</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transformer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Maintenance
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredTransformers.map((transformer) => (
                    <tr key={transformer.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{transformer.serialNumber}</div>
                        <div className="text-xs text-gray-500">{transformer.manufacturer} - {transformer.ratingKVA} kVA</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{transformer.location.region}</div>
                        <div className="text-xs text-gray-500">{transformer.location.serviceCenter}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(transformer.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transformer.lastMaintenance}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleOpenHistoryCard(transformer.id)}>
                              <FileText className="h-4 w-4 mr-2" />
                              Open History Card
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Download className="h-4 w-4 mr-2" />
                              Export Card
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredTransformers.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No transformers found matching your filters</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* History Card Dialog */}
        {showHistoryCard && selectedTransformerId && (
          <TransformerHistoryCard
            transformerId={selectedTransformerId}
            onClose={handleCloseHistoryCard}
          />
        )}
      </div>
    </MainLayout>
  )
}
