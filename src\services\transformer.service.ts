/**
 * Transformer Service
 * Clean data access layer for transformer operations
 * Separates business logic from API routes
 */

import { executeQuery, getConnection } from '@/src/lib/database'
import { 
  Transformer, 
  FilterOptions, 
  DashboardSummary,
  TransformerStatus,
  PaginatedResponse 
} from '@/src/types'

/**
 * Get all transformers with optional filtering and pagination
 */
export async function getTransformers(
  filters: FilterOptions = {},
  page: number = 1,
  limit: number = 50
): Promise<PaginatedResponse<Transformer>> {
  try {
    // Build the base query
    let query = `
      SELECT 
        t.*,
        r.name as region_name,
        r.code as region_code,
        sc.name as service_center_name,
        sc.code as service_center_code
      FROM dtms_transformers t
      LEFT JOIN dtms_regions r ON t.region_id = r.id
      LEFT JOIN dtms_service_centers sc ON t.service_center_id = sc.id
      WHERE 1=1
    `
    
    const params: any[] = []
    
    // Apply filters dynamically
    query = applyFilters(query, filters, params)
    
    // Add pagination
    const offset = (page - 1) * limit
    query += ` LIMIT ? OFFSET ?`
    params.push(limit, offset)
    
    // Execute query
    const transformers = await executeQuery<Transformer>(query, params)
    
    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM dtms_transformers t
      LEFT JOIN dtms_regions r ON t.region_id = r.id
      LEFT JOIN dtms_service_centers sc ON t.service_center_id = sc.id
      WHERE 1=1
      ${getFilterConditions(filters)}
    `
    
    const countParams = getFilterParams(filters)
    const [{ total }] = await executeQuery<{ total: number }>(countQuery, countParams)
    
    return {
      success: true,
      data: transformers,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Error fetching transformers:', error)
    throw new Error('Failed to fetch transformers')
  }
}

/**
 * Get a single transformer by ID
 */
export async function getTransformerById(id: string): Promise<Transformer | null> {
  try {
    const query = `
      SELECT 
        t.*,
        r.name as region_name,
        r.code as region_code,
        sc.name as service_center_name,
        sc.code as service_center_code
      FROM dtms_transformers t
      LEFT JOIN dtms_regions r ON t.region_id = r.id
      LEFT JOIN dtms_service_centers sc ON t.service_center_id = sc.id
      WHERE t.id = ?
    `
    
    const transformers = await executeQuery<Transformer>(query, [id])
    return transformers[0] || null
  } catch (error) {
    console.error('Error fetching transformer:', error)
    throw new Error('Failed to fetch transformer')
  }
}

/**
 * Update transformer status
 */
export async function updateTransformerStatus(
  id: string, 
  status: TransformerStatus
): Promise<boolean> {
  try {
    const query = `
      UPDATE dtms_transformers 
      SET status = ?, updated_at = NOW() 
      WHERE id = ?
    `
    
    await executeQuery(query, [status, id])
    return true
  } catch (error) {
    console.error('Error updating transformer status:', error)
    throw new Error('Failed to update transformer status')
  }
}

/**
 * Get dashboard summary statistics
 */
export async function getDashboardSummary(
  filters: FilterOptions = {}
): Promise<DashboardSummary> {
  try {
    // Build filtered query for summary
    let query = `
      SELECT 
        COUNT(*) as totalTransformers,
        SUM(CASE WHEN t.status = 'operational' THEN 1 ELSE 0 END) as operationalCount,
        SUM(CASE WHEN t.status = 'warning' THEN 1 ELSE 0 END) as warningCount,
        SUM(CASE WHEN t.status = 'critical' THEN 1 ELSE 0 END) as criticalCount,
        SUM(CASE WHEN t.status = 'maintenance' THEN 1 ELSE 0 END) as maintenanceCount,
        AVG(COALESCE(t.efficiency_rating, 0)) as avgEfficiency,
        AVG(COALESCE(t.load_factor, 0)) as avgLoadFactor,
        AVG(COALESCE(t.temperature, 0)) as avgTemperature,
        SUM(COALESCE(t.asset_value, 0)) as totalAssetValue
      FROM dtms_transformers t
      LEFT JOIN dtms_regions r ON t.region_id = r.id
      LEFT JOIN dtms_service_centers sc ON t.service_center_id = sc.id
      WHERE 1=1
      ${getFilterConditions(filters)}
    `
    
    const params = getFilterParams(filters)
    const [summary] = await executeQuery<DashboardSummary>(query, params)
    
    // Get additional metrics
    const alertQuery = `
      SELECT COUNT(*) as activeAlerts
      FROM dtms_alerts a
      JOIN dtms_transformers t ON a.transformer_id = t.id
      WHERE a.status = 'active'
    `
    
    const maintenanceQuery = `
      SELECT COUNT(*) as pendingMaintenance
      FROM dtms_maintenance_schedules m
      JOIN dtms_transformers t ON m.transformer_id = t.id
      WHERE m.status = 'scheduled'
    `
    
    const [{ activeAlerts }] = await executeQuery<{ activeAlerts: number }>(alertQuery)
    const [{ pendingMaintenance }] = await executeQuery<{ pendingMaintenance: number }>(maintenanceQuery)
    
    return {
      ...summary,
      activeAlerts: activeAlerts || 0,
      pendingMaintenance: pendingMaintenance || 0
    }
  } catch (error) {
    console.error('Error fetching dashboard summary:', error)
    throw new Error('Failed to fetch dashboard summary')
  }
}

/**
 * Helper function to apply filters to query
 */
function applyFilters(query: string, filters: FilterOptions, params: any[]): string {
  if (filters.regions?.length) {
    query += ` AND r.code IN (${filters.regions.map(() => '?').join(',')})`
    params.push(...filters.regions)
  }
  
  if (filters.serviceCenters?.length) {
    query += ` AND sc.code IN (${filters.serviceCenters.map(() => '?').join(',')})`
    params.push(...filters.serviceCenters)
  }
  
  if (filters.types?.length) {
    query += ` AND t.type IN (${filters.types.map(() => '?').join(',')})`
    params.push(...filters.types)
  }
  
  if (filters.statuses?.length) {
    query += ` AND t.status IN (${filters.statuses.map(() => '?').join(',')})`
    params.push(...filters.statuses)
  }
  
  if (filters.manufacturers?.length) {
    query += ` AND t.manufacturer IN (${filters.manufacturers.map(() => '?').join(',')})`
    params.push(...filters.manufacturers)
  }
  
  if (filters.capacityRange) {
    query += ` AND t.capacity_kva BETWEEN ? AND ?`
    params.push(filters.capacityRange[0], filters.capacityRange[1])
  }
  
  if (filters.efficiencyRange) {
    query += ` AND t.efficiency_rating BETWEEN ? AND ?`
    params.push(filters.efficiencyRange[0], filters.efficiencyRange[1])
  }
  
  if (filters.search) {
    query += ` AND (
      t.name LIKE ? OR 
      t.serial_number LIKE ? OR 
      t.location_name LIKE ? OR
      r.name LIKE ? OR
      sc.name LIKE ?
    )`
    const searchPattern = `%${filters.search}%`
    params.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern)
  }
  
  return query
}

/**
 * Helper function to get filter conditions for count queries
 */
function getFilterConditions(filters: FilterOptions): string {
  let conditions = ''
  
  if (filters.regions?.length) {
    conditions += ` AND r.code IN (${filters.regions.map(() => '?').join(',')})`
  }
  
  if (filters.serviceCenters?.length) {
    conditions += ` AND sc.code IN (${filters.serviceCenters.map(() => '?').join(',')})`
  }
  
  // Add other filter conditions...
  
  return conditions
}

/**
 * Helper function to get filter parameters
 */
function getFilterParams(filters: FilterOptions): any[] {
  const params: any[] = []
  
  if (filters.regions?.length) {
    params.push(...filters.regions)
  }
  
  if (filters.serviceCenters?.length) {
    params.push(...filters.serviceCenters)
  }
  
  // Add other filter parameters...
  
  return params
}
