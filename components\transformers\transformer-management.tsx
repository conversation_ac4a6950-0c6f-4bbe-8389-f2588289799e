"use client"

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Zap,
  Power,
  PowerOff,
  Wrench,
  Play,
  Pause,
  Square,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  MapPin,
  Gauge,
  ThermometerSun,
  Battery,
  Activity,
  TrendingUp,
  TrendingDown,
  Eye,
  Edit,
  Trash2,
  Copy,
  Share2,
  Download,
  Upload,
  Settings,
  MoreVertical,
  Filter,
  Search,
  RefreshCw,
  Bell,
  BellOff,
  Star,
  StarOff,
  Bookmark,
  History,
  Target,
  Layers,
  Grid,
  List,
  Users,
  Building,
  Factory,
  Shield,
  ShieldAlert,
  ShieldCheck,
  Cpu,
  HardDrive,
  Network,
  Wifi,
  WifiOff
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { toast } from 'sonner'

export interface Transformer {
  id: string
  name: string
  serialNumber: string
  type: 'Distribution' | 'Power' | 'Instrument' | 'Auto'
  status: 'operational' | 'warning' | 'critical' | 'maintenance' | 'offline'
  location: {
    region: string
    serviceCenter: string
    coordinates: { lat: number; lng: number }
    address: string
  }
  specifications: {
    capacity: number // kVA
    voltage: { primary: number; secondary: number }
    phases: number
    frequency: number
    manufacturer: string
    model: string
    yearInstalled: number
  }
  performance: {
    efficiency: number
    loadFactor: number
    temperature: number
    vibration: number
    oilLevel: number
    powerFactor: number
  }
  maintenance: {
    lastMaintenance: Date
    nextMaintenance: Date
    maintenanceType: string
    technician: string
    notes: string
  }
  alerts: Array<{
    id: string
    type: string
    severity: 'low' | 'medium' | 'high' | 'critical'
    message: string
    timestamp: Date
    acknowledged: boolean
  }>
  isStarred: boolean
  isMonitored: boolean
  lastUpdated: Date
}

interface TransformerManagementProps {
  transformers: Transformer[]
  onTransformerUpdate: (id: string, updates: Partial<Transformer>) => void
  onBulkOperation: (ids: string[], operation: string, params?: any) => void
  onScheduleMaintenance: (id: string, schedule: any) => void
  className?: string
}

export function TransformerManagement({
  transformers,
  onTransformerUpdate,
  onBulkOperation,
  onScheduleMaintenance,
  className = ''
}: TransformerManagementProps) {
  const [selectedTransformers, setSelectedTransformers] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'table'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string[]>([])
  const [selectedTransformer, setSelectedTransformer] = useState<Transformer | null>(null)
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [showMaintenanceDialog, setShowMaintenanceDialog] = useState(false)
  const [showBulkDialog, setShowBulkDialog] = useState(false)
  const [bulkOperation, setBulkOperation] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'bg-green-100 text-green-800 border-green-200'
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'maintenance': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'offline': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational': return <CheckCircle className="h-4 w-4" />
      case 'warning': return <AlertTriangle className="h-4 w-4" />
      case 'critical': return <AlertTriangle className="h-4 w-4" />
      case 'maintenance': return <Wrench className="h-4 w-4" />
      case 'offline': return <PowerOff className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  const filteredTransformers = transformers.filter(transformer => {
    const matchesSearch = transformer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         transformer.serialNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         transformer.location.region.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter.length === 0 || statusFilter.includes(transformer.status)
    
    return matchesSearch && matchesStatus
  })

  const handleSelectTransformer = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedTransformers([...selectedTransformers, id])
    } else {
      setSelectedTransformers(selectedTransformers.filter(t => t !== id))
    }
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedTransformers(filteredTransformers.map(t => t.id))
    } else {
      setSelectedTransformers([])
    }
  }

  const handleSingleOperation = useCallback(async (transformer: Transformer, operation: string) => {
    setIsLoading(true)
    try {
      switch (operation) {
        case 'start':
          onTransformerUpdate(transformer.id, { status: 'operational' })
          toast.success(`${transformer.name} started successfully`)
          break
        case 'stop':
          onTransformerUpdate(transformer.id, { status: 'offline' })
          toast.success(`${transformer.name} stopped successfully`)
          break
        case 'restart':
          onTransformerUpdate(transformer.id, { status: 'operational', lastUpdated: new Date() })
          toast.success(`${transformer.name} restarted successfully`)
          break
        case 'maintenance':
          onTransformerUpdate(transformer.id, { status: 'maintenance' })
          toast.success(`${transformer.name} set to maintenance mode`)
          break
        case 'star':
          onTransformerUpdate(transformer.id, { isStarred: !transformer.isStarred })
          toast.success(transformer.isStarred ? 'Removed from favorites' : 'Added to favorites')
          break
        case 'monitor':
          onTransformerUpdate(transformer.id, { isMonitored: !transformer.isMonitored })
          toast.success(transformer.isMonitored ? 'Monitoring disabled' : 'Monitoring enabled')
          break
      }
    } catch (error) {
      toast.error(`Operation failed: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }, [onTransformerUpdate])

  const handleBulkOperation = useCallback(async () => {
    if (selectedTransformers.length === 0) {
      toast.error('No transformers selected')
      return
    }

    setIsLoading(true)
    try {
      await onBulkOperation(selectedTransformers, bulkOperation)
      toast.success(`Bulk operation completed for ${selectedTransformers.length} transformers`)
      setSelectedTransformers([])
      setShowBulkDialog(false)
    } catch (error) {
      toast.error(`Bulk operation failed: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }, [selectedTransformers, bulkOperation, onBulkOperation])

  const TransformerCard = ({ transformer }: { transformer: Transformer }) => (
    <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-300 border-0 bg-white/95 backdrop-blur-sm">
      <div className={`absolute top-0 left-0 w-full h-1 ${
        transformer.status === 'operational' ? 'bg-green-500' :
        transformer.status === 'warning' ? 'bg-yellow-500' :
        transformer.status === 'critical' ? 'bg-red-500' :
        transformer.status === 'maintenance' ? 'bg-blue-500' : 'bg-gray-500'
      }`}></div>
      
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Checkbox
              checked={selectedTransformers.includes(transformer.id)}
              onCheckedChange={(checked) => handleSelectTransformer(transformer.id, checked as boolean)}
            />
            <div>
              <CardTitle className="text-lg font-semibold">{transformer.name}</CardTitle>
              <p className="text-sm text-gray-500">{transformer.serialNumber}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            {transformer.isStarred && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
            {transformer.isMonitored && <Bell className="h-4 w-4 text-blue-500" />}
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => {
                  setSelectedTransformer(transformer)
                  setShowDetailDialog(true)
                }}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={() => handleSingleOperation(transformer, 'start')}>
                  <Play className="mr-2 h-4 w-4" />
                  Start
                </DropdownMenuItem>
                
                <DropdownMenuItem onClick={() => handleSingleOperation(transformer, 'stop')}>
                  <Square className="mr-2 h-4 w-4" />
                  Stop
                </DropdownMenuItem>
                
                <DropdownMenuItem onClick={() => handleSingleOperation(transformer, 'restart')}>
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Restart
                </DropdownMenuItem>
                
                <DropdownMenuItem onClick={() => handleSingleOperation(transformer, 'maintenance')}>
                  <Wrench className="mr-2 h-4 w-4" />
                  Maintenance Mode
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={() => handleSingleOperation(transformer, 'star')}>
                  {transformer.isStarred ? <StarOff className="mr-2 h-4 w-4" /> : <Star className="mr-2 h-4 w-4" />}
                  {transformer.isStarred ? 'Remove Favorite' : 'Add Favorite'}
                </DropdownMenuItem>
                
                <DropdownMenuItem onClick={() => handleSingleOperation(transformer, 'monitor')}>
                  {transformer.isMonitored ? <BellOff className="mr-2 h-4 w-4" /> : <Bell className="mr-2 h-4 w-4" />}
                  {transformer.isMonitored ? 'Disable Monitoring' : 'Enable Monitoring'}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Status and Type */}
        <div className="flex items-center justify-between">
          <Badge className={getStatusColor(transformer.status)}>
            {getStatusIcon(transformer.status)}
            <span className="ml-1">{transformer.status.toUpperCase()}</span>
          </Badge>
          <Badge variant="outline">{transformer.type}</Badge>
        </div>

        {/* Location */}
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <MapPin className="h-4 w-4" />
          <span>{transformer.location.region} - {transformer.location.serviceCenter}</span>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Gauge className="h-4 w-4 text-blue-500" />
            <span>Efficiency: {transformer.performance.efficiency.toFixed(1)}%</span>
          </div>
          <div className="flex items-center gap-2">
            <ThermometerSun className="h-4 w-4 text-orange-500" />
            <span>Temp: {transformer.performance.temperature.toFixed(1)}°C</span>
          </div>
          <div className="flex items-center gap-2">
            <Battery className="h-4 w-4 text-green-500" />
            <span>Load: {transformer.performance.loadFactor.toFixed(1)}%</span>
          </div>
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-purple-500" />
            <span>Capacity: {transformer.specifications.capacity} kVA</span>
          </div>
        </div>

        {/* Alerts */}
        {transformer.alerts.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-2">
            <div className="flex items-center gap-2 text-sm text-red-800">
              <AlertTriangle className="h-4 w-4" />
              <span>{transformer.alerts.length} active alert{transformer.alerts.length > 1 ? 's' : ''}</span>
            </div>
          </div>
        )}

        {/* Last Updated */}
        <div className="flex items-center gap-2 text-xs text-gray-400">
          <Clock className="h-3 w-3" />
          <span>Updated {transformer.lastUpdated.toLocaleTimeString()}</span>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header and Controls */}
      <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold text-gray-900">Transformer Management</CardTitle>
              <p className="text-gray-600">Monitor and control {transformers.length} transformers across the network</p>
            </div>
            
            <div className="flex items-center gap-2">
              {/* View Mode Toggle */}
              <div className="flex items-center border rounded-lg">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'table' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <Table className="h-4 w-4" />
                </Button>
              </div>

              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex items-center gap-4 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search transformers by name, serial number, or location..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Status Filter
                  {statusFilter.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {statusFilter.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuCheckboxItem
                  checked={statusFilter.includes('operational')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setStatusFilter([...statusFilter, 'operational'])
                    } else {
                      setStatusFilter(statusFilter.filter(s => s !== 'operational'))
                    }
                  }}
                >
                  Operational
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter.includes('warning')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setStatusFilter([...statusFilter, 'warning'])
                    } else {
                      setStatusFilter(statusFilter.filter(s => s !== 'warning'))
                    }
                  }}
                >
                  Warning
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter.includes('critical')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setStatusFilter([...statusFilter, 'critical'])
                    } else {
                      setStatusFilter(statusFilter.filter(s => s !== 'critical'))
                    }
                  }}
                >
                  Critical
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter.includes('maintenance')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setStatusFilter([...statusFilter, 'maintenance'])
                    } else {
                      setStatusFilter(statusFilter.filter(s => s !== 'maintenance'))
                    }
                  }}
                >
                  Maintenance
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter.includes('offline')}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setStatusFilter([...statusFilter, 'offline'])
                    } else {
                      setStatusFilter(statusFilter.filter(s => s !== 'offline'))
                    }
                  }}
                >
                  Offline
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Bulk Operations */}
          {selectedTransformers.length > 0 && (
            <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedTransformers.length === filteredTransformers.length}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm font-medium">
                  {selectedTransformers.length} transformer{selectedTransformers.length > 1 ? 's' : ''} selected
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setBulkOperation('start')
                    setShowBulkDialog(true)
                  }}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setBulkOperation('stop')
                    setShowBulkDialog(true)
                  }}
                >
                  <Square className="h-4 w-4 mr-2" />
                  Stop All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setBulkOperation('maintenance')
                    setShowBulkDialog(true)
                  }}
                >
                  <Wrench className="h-4 w-4 mr-2" />
                  Maintenance
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedTransformers([])}
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Transformers Display */}
      {viewMode === 'grid' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredTransformers.map((transformer) => (
            <TransformerCard key={transformer.id} transformer={transformer} />
          ))}
        </div>
      )}

      {filteredTransformers.length === 0 && (
        <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
          <CardContent className="p-12 text-center">
            <Zap className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Transformers Found</h3>
            <p className="text-gray-600">
              {searchQuery || statusFilter.length > 0 
                ? 'No transformers match your current filters. Try adjusting your search criteria.'
                : 'No transformers are currently available in the system.'
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Bulk Operation Confirmation Dialog */}
      <Dialog open={showBulkDialog} onOpenChange={setShowBulkDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Bulk Operation</DialogTitle>
            <DialogDescription>
              Are you sure you want to {bulkOperation} {selectedTransformers.length} transformer{selectedTransformers.length > 1 ? 's' : ''}?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleBulkOperation} disabled={isLoading}>
              {isLoading ? 'Processing...' : 'Confirm'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
