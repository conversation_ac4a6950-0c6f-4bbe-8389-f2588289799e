"use client"

import type React from "react"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Image from "next/image"
import { useAuth } from "@/src/features/auth/context/auth-context"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Input } from "@/src/components/ui/input"
import { Button } from "@/src/components/ui/button"
import { Label } from "@/src/components/ui/label"
import { AuthProvider } from "@/src/features/auth/context/auth-context"
import { Footer } from "@/src/components/layout/footer"

function LoginContent() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const { login, isLoading, error } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const returnUrl = searchParams?.get("returnUrl") || "/"

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Attempting login with:", email, password)

    try {
      await login(email, password)
      console.log("Login successful, redirecting to dashboard")
      // Always redirect to dashboard after login
      router.push("/dashboard")
    } catch (err) {
      console.error("Login failed:", err)
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-slate-50 dark:bg-slate-900">
      <div className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-2 text-center">
            <div className="flex justify-center">
              <div className="rounded-full p-3">
                <Image
                  src="/eeu-logo-vector.svg"
                  alt="Ethiopian Electric Utility Logo"
                  width={80}
                  height={80}
                  className="h-20 w-20"
                />
              </div>
            </div>
            <CardTitle className="text-2xl">Ethiopia Electric Utility</CardTitle>
            <CardDescription>Distribution Transformer Management System</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              {error && (
                <div className="rounded-md bg-red-50 p-3 text-sm text-red-600 dark:bg-red-900/20 dark:text-red-400">
                  {error}
                </div>
              )}
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <a href="#" className="text-xs text-primary hover:underline">
                    Forgot password?
                  </a>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>
            </CardFooter>
          </form>
          <div className="p-4 text-center text-sm text-muted-foreground">
            <div className="mb-2 text-xs font-medium text-blue-600">
              Demo Accounts (Password: demo123)
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              Click to auto-fill email:
              <div className="mt-1 grid grid-cols-2 gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs"
                  onClick={() => {
                    setEmail("<EMAIL>")
                    setPassword("demo123")
                  }}
                >
                  Super Admin
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs"
                  onClick={() => {
                    setEmail("<EMAIL>")
                    setPassword("demo123")
                  }}
                >
                  Asset Manager
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs"
                  onClick={() => {
                    setEmail("<EMAIL>")
                    setPassword("demo123")
                  }}
                >
                  Regional Admin
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs"
                  onClick={() => {
                    setEmail("<EMAIL>")
                    setPassword("demo123")
                  }}
                >
                  Service Manager
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs"
                  onClick={() => {
                    setEmail("<EMAIL>")
                    setPassword("demo123")
                  }}
                >
                  Field Technician
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs"
                  onClick={() => {
                    setEmail("<EMAIL>")
                    setPassword("demo123")
                  }}
                >
                  Customer Service
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </div>
      <Footer className="mt-auto" />
    </div>
  )
}

export default function LoginPage() {
  return (
    <AuthProvider>
      <LoginContent />
    </AuthProvider>
  )
}
