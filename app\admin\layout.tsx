import { Metadata } from 'next'
import Link from 'next/link'
import { Database, Settings, Shield, Users } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Admin',
  description: 'Administration tools for the application',
}

/**
 * Admin Layout
 * 
 * This layout provides a consistent structure for all admin pages.
 */
export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex">
            <Link href="/admin" className="mr-6 flex items-center space-x-2">
              <Shield className="h-6 w-6" />
              <span className="font-bold">Admin</span>
            </Link>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link
                href="/admin/database"
                className="transition-colors hover:text-foreground/80 text-foreground/60"
              >
                <div className="flex items-center gap-1">
                  <Database className="h-4 w-4" />
                  <span>Database</span>
                </div>
              </Link>
              <Link
                href="/admin/users"
                className="transition-colors hover:text-foreground/80 text-foreground/60"
              >
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>Users</span>
                </div>
              </Link>
              <Link
                href="/admin/settings"
                className="transition-colors hover:text-foreground/80 text-foreground/60"
              >
                <div className="flex items-center gap-1">
                  <Settings className="h-4 w-4" />
                  <span>Settings</span>
                </div>
              </Link>
            </nav>
          </div>
          <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
            <div className="w-full flex-1 md:w-auto md:flex-none">
              <Link href="/dashboard" className="text-sm text-muted-foreground hover:text-foreground">
                Return to Dashboard
              </Link>
            </div>
          </div>
        </div>
      </header>
      <main className="flex-1">{children}</main>
    </div>
  )
}
