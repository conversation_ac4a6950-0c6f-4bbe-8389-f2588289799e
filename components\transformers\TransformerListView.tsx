'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  Search,
  Download,
  Plus,
  Eye,
  Filter,
  ChevronDown,
  RefreshCw
} from 'lucide-react'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import { Input } from '@/src/components/ui/input'

interface Transformer {
  id: string
  code: string
  name: string
  location: string
  district: string
  kvaRating: number
  installation: string
  status: 'Active' | 'Maintenance' | 'Failed' | 'Retired'
}

const TransformerListView = () => {
  const router = useRouter()
  const [transformers, setTransformers] = useState<Transformer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('All Status')

  // Load transformers from API
  useEffect(() => {
    const loadTransformers = async () => {
      try {
        console.log('🔄 Loading transformers from API...')
        const response = await fetch('/api/mysql/transformers')

        if (response.ok) {
          const data = await response.json()
          console.log('📊 API Response:', data)

          if (data.success && data.data && data.data.transformers) {
            // Transform the data to match our interface
            const transformedData = data.data.transformers.map((t: any) => ({
              id: t.id.toString(),
              code: t.serialNumber || t.serial_number || `DT-${t.id}`,
              name: t.name || `${t.manufacturer} Transformer`,
              location: t.location?.address || t.location_name || 'Unknown Location',
              district: (t.location?.address || t.location_name || '').split(',')[0] || 'Unknown District',
              kvaRating: t.capacity || t.capacity_kva || 0,
              installation: t.installationDate || t.installation_date || '2023-01-01',
              status: t.status === 'operational' ? 'Active' :
                     t.status === 'maintenance' ? 'Maintenance' :
                     t.status === 'critical' || t.status === 'burnt' ? 'Failed' :
                     t.status === 'warning' ? 'Maintenance' : 'Active'
            }))

            console.log(`✅ Transformed ${transformedData.length} transformers`)
            setTransformers(transformedData)
          } else {
            console.warn('⚠️ Invalid API response structure:', data)
          }
        } else {
          console.error('❌ API request failed:', response.status, response.statusText)
        }
      } catch (error) {
        console.error('❌ Error loading transformers:', error)
      } finally {
        setLoading(false)
      }
    }

    loadTransformers()
  }, [])

  // Filter transformers based on search and status
  const filteredTransformers = transformers.filter(transformer => {
    const matchesSearch = transformer.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transformer.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'All Status' || transformer.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const handleViewTransformer = (transformerId: string) => {
    router.push(`/transformers/${transformerId}`)
  }

  const handleExport = () => {
    console.log('Exporting transformer data...')
    // Implement export functionality
  }

  const handleAddTransformer = () => {
    router.push('/transformers/new')
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Active': return 'default'
      case 'Maintenance': return 'secondary'
      case 'Failed': return 'destructive'
      case 'Retired': return 'outline'
      default: return 'outline'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800'
      case 'Maintenance': return 'bg-yellow-100 text-yellow-800'
      case 'Failed': return 'bg-red-100 text-red-800'
      case 'Retired': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Transformers</h1>
          <p className="text-gray-600 mt-1">Manage your transformer inventory</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={() => window.location.reload()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={handleAddTransformer} className="bg-orange-500 hover:bg-orange-600">
            <Plus className="h-4 w-4 mr-2" />
            Add Transformer
          </Button>
        </div>
      </div>

      {/* Debug Info */}
      <div className="mb-4 p-3 bg-blue-50 rounded-lg text-sm">
        <strong>Debug Info:</strong> Loading: {loading ? 'Yes' : 'No'} |
        Total Transformers: {transformers.length} |
        Filtered: {filteredTransformers.length} |
        Search: "{searchTerm}" |
        Status Filter: {statusFilter}
      </div>

      {/* Search and Filters */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search transformer code, location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <div className="relative">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="All Status">All Status</option>
              <option value="Active">Active</option>
              <option value="Maintenance">Maintenance</option>
              <option value="Failed">Failed</option>
              <option value="Retired">Retired</option>
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Sort
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Transformer Code</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Location</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">KVA Rating</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Installation</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Action</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={6} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                      <span className="ml-2">Loading transformers...</span>
                    </div>
                  </td>
                </tr>
              ) : filteredTransformers.length === 0 ? (
                <tr>
                  <td colSpan={6} className="text-center py-8 text-gray-500">
                    No transformers found
                  </td>
                </tr>
              ) : (
                filteredTransformers.map((transformer) => (
                  <tr key={transformer.id} className="hover:bg-gray-50">
                    <td className="py-4 px-6">
                      <div>
                        <div className="font-medium text-gray-900">{transformer.code}</div>
                        <div className="text-sm text-blue-600">{transformer.name}</div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div>
                        <div className="text-gray-900">{transformer.district}</div>
                        <div className="text-sm text-gray-500">{transformer.location}</div>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-gray-900">{transformer.kvaRating}</td>
                    <td className="py-4 px-6 text-gray-900">{transformer.installation}</td>
                    <td className="py-4 px-6">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transformer.status)}`}>
                        {transformer.status}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewTransformer(transformer.id)}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default TransformerListView
