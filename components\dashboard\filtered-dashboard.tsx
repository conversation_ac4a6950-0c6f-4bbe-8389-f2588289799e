"use client"

import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ComprehensiveFilterPanel } from "@/components/filters/comprehensive-filter-panel"
import { useDashboardFilters } from "@/hooks/use-dashboard-filters"
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'
import {
  Filter,
  Download,
  RefreshCw,
  MapPin,
  Zap,
  AlertTriangle,
  Wrench,
  TrendingUp,
  Activity,
  DollarSign,
  Users,
  Building,
  Gauge,
  BarChart3,
  Settings
} from 'lucide-react'

const COLORS = {
  operational: '#22c55e',
  warning: '#f59e0b',
  critical: '#ef4444',
  maintenance: '#3b82f6',
  burnt: '#6b7280',
  offline: '#9ca3af'
}

export function FilteredDashboard() {
  const {
    filters,
    filteredData,
    isLoading,
    error,
    updateFilters,
    resetFilters,
    getActiveFilterCount,
    saveFiltersToStorage,
    loadFiltersFromStorage
  } = useDashboardFilters()

  const [showFilters, setShowFilters] = useState(true)

  // Prepare chart data
  const statusData = filteredData ? [
    { name: 'Operational', value: filteredData.summary.operationalCount, color: COLORS.operational },
    { name: 'Warning', value: filteredData.summary.warningCount, color: COLORS.warning },
    { name: 'Critical', value: filteredData.summary.criticalCount, color: COLORS.critical },
    { name: 'Maintenance', value: filteredData.summary.maintenanceCount, color: COLORS.maintenance }
  ].filter(item => item.value > 0) : []

  const regionalData = filteredData?.regions.map(region => ({
    name: region.name,
    transformers: region.transformer_count || 0,
    code: region.code
  })) || []

  const performanceData = filteredData?.transformers.map(transformer => ({
    name: transformer.name.substring(0, 20) + '...',
    efficiency: parseFloat(transformer.efficiency_rating) || 0,
    loadFactor: parseFloat(transformer.load_factor) || 0,
    temperature: parseFloat(transformer.temperature) || 0
  })) || []

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Dashboard</h3>
            <p>{error}</p>
            <Button onClick={() => window.location.reload()} className="mt-4">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="min-h-screen modern-dashboard-bg">
      {/* Modern Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-40">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                EEU Digital Transformer Management
              </h1>
              <p className="text-gray-600 mt-1">
                Real-time monitoring and analytics for Ethiopian Electric Utility
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="relative"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {getActiveFilterCount() > 0 && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                    {getActiveFilterCount()}
                  </Badge>
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={saveFiltersToStorage}
                disabled={getActiveFilterCount() === 0}
              >
                Save Filters
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={loadFiltersFromStorage}
              >
                Load Filters
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-6 space-y-6">

      {/* Filter Panel */}
      {showFilters && (
        <ComprehensiveFilterPanel
          onFiltersChange={updateFilters}
          onReset={resetFilters}
        />
      )}

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              <span>Loading filtered data...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dashboard Content */}
      {filteredData && (
        <>
          {/* Modern Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-500 border-0 bg-white/95 backdrop-blur-sm">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Total Transformers</CardTitle>
                <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Zap className="h-5 w-5" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900 mb-1">{filteredData.summary.totalTransformers}</div>
                <p className="text-sm text-gray-500">
                  {filteredData.summary.operationalCount} operational
                </p>
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-500 border-0 bg-white/95 backdrop-blur-sm">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-500 to-teal-500"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">System Availability</CardTitle>
                <div className="p-3 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-500 text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Activity className="h-5 w-5" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  {filteredData.summary.totalTransformers > 0
                    ? ((filteredData.summary.operationalCount / filteredData.summary.totalTransformers) * 100).toFixed(1)
                    : 0}%
                </div>
                <p className="text-sm text-gray-500">
                  {filteredData.summary.criticalCount} critical issues
                </p>
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-500 border-0 bg-white/95 backdrop-blur-sm">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-indigo-500"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Average Efficiency</CardTitle>
                <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-500 text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Gauge className="h-5 w-5" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  {filteredData.summary.avgEfficiency.toFixed(1)}%
                </div>
                <p className="text-sm text-gray-500">
                  Load factor: {filteredData.summary.avgLoadFactor.toFixed(1)}%
                </p>
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-500 border-0 bg-white/95 backdrop-blur-sm">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-red-500"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Total Asset Value</CardTitle>
                <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500 to-red-500 text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="h-5 w-5" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900 mb-1">
                  ${filteredData.summary.totalAssetValue.toLocaleString()}
                </div>
                <p className="text-sm text-gray-500">
                  {filteredData.summary.activeAlerts} active alerts
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Modern Charts and Analytics */}
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5 lg:w-auto lg:grid-cols-5 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <TabsTrigger value="overview" className="modern-tab-trigger">
                <BarChart3 className="h-4 w-4 mr-2" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="regional" className="modern-tab-trigger">
                <MapPin className="h-4 w-4 mr-2" />
                Regional
              </TabsTrigger>
              <TabsTrigger value="performance" className="modern-tab-trigger">
                <Activity className="h-4 w-4 mr-2" />
                Performance
              </TabsTrigger>
              <TabsTrigger value="maintenance" className="modern-tab-trigger">
                <Settings className="h-4 w-4 mr-2" />
                Maintenance
              </TabsTrigger>
              <TabsTrigger value="alerts" className="modern-tab-trigger">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Alerts
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Status Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Transformer Status Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={statusData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {statusData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Regional Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Regional Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={regionalData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="code" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="transformers" fill="#3b82f6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="regional" className="space-y-4">
              <div className="grid grid-cols-1 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Regional Performance Comparison</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={400}>
                      <BarChart data={regionalData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="transformers" fill="#22c55e" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <div className="grid grid-cols-1 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Transformer Performance Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={400}>
                      <AreaChart data={performanceData.slice(0, 10)}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Area type="monotone" dataKey="efficiency" stackId="1" stroke="#22c55e" fill="#22c55e" />
                        <Area type="monotone" dataKey="loadFactor" stackId="2" stroke="#3b82f6" fill="#3b82f6" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="maintenance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Maintenance Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Pending Maintenance</span>
                      <Badge variant="outline">{filteredData.summary.pendingMaintenance}</Badge>
                    </div>
                    <div className="space-y-2">
                      {filteredData.maintenance.slice(0, 5).map((maintenance: any) => (
                        <div key={maintenance.id} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <p className="font-medium">{maintenance.title}</p>
                            <p className="text-sm text-muted-foreground">{maintenance.transformer_name}</p>
                          </div>
                          <Badge variant={
                            maintenance.priority === 'critical' ? 'destructive' :
                            maintenance.priority === 'high' ? 'default' : 'secondary'
                          }>
                            {maintenance.priority}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="alerts" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Active Alerts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Active Alerts</span>
                      <Badge variant="outline">{filteredData.summary.activeAlerts}</Badge>
                    </div>
                    <div className="space-y-2">
                      {filteredData.alerts.filter((alert: any) => alert.status === 'active').slice(0, 5).map((alert: any) => (
                        <div key={alert.id} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <p className="font-medium">{alert.title}</p>
                            <p className="text-sm text-muted-foreground">{alert.transformer_name}</p>
                          </div>
                          <Badge variant={
                            alert.severity === 'critical' ? 'destructive' :
                            alert.severity === 'high' ? 'default' : 'secondary'
                          }>
                            {alert.severity}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}
