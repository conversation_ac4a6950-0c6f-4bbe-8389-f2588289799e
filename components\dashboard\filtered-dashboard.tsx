"use client"

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ComprehensiveFilterPanel } from "@/components/filters/comprehensive-filter-panel"
import { useDashboardFilters } from "@/hooks/use-dashboard-filters"
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'
import { 
  Filter, 
  Download, 
  RefreshCw, 
  MapPin, 
  Zap, 
  AlertTriangle, 
  Wrench,
  TrendingUp,
  Activity,
  DollarSign,
  Users,
  Building,
  Gauge
} from 'lucide-react'

const COLORS = {
  operational: '#22c55e',
  warning: '#f59e0b',
  critical: '#ef4444',
  maintenance: '#3b82f6',
  burnt: '#6b7280',
  offline: '#9ca3af'
}

export function FilteredDashboard() {
  const {
    filters,
    filteredData,
    isLoading,
    error,
    updateFilters,
    resetFilters,
    getActiveFilterCount,
    saveFiltersToStorage,
    loadFiltersFromStorage
  } = useDashboardFilters()

  const [showFilters, setShowFilters] = useState(true)

  // Prepare chart data
  const statusData = filteredData ? [
    { name: 'Operational', value: filteredData.summary.operationalCount, color: COLORS.operational },
    { name: 'Warning', value: filteredData.summary.warningCount, color: COLORS.warning },
    { name: 'Critical', value: filteredData.summary.criticalCount, color: COLORS.critical },
    { name: 'Maintenance', value: filteredData.summary.maintenanceCount, color: COLORS.maintenance }
  ].filter(item => item.value > 0) : []

  const regionalData = filteredData?.regions.map(region => ({
    name: region.name,
    transformers: region.transformer_count || 0,
    code: region.code
  })) || []

  const performanceData = filteredData?.transformers.map(transformer => ({
    name: transformer.name.substring(0, 20) + '...',
    efficiency: parseFloat(transformer.efficiency_rating) || 0,
    loadFactor: parseFloat(transformer.load_factor) || 0,
    temperature: parseFloat(transformer.temperature) || 0
  })) || []

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Dashboard</h3>
            <p>{error}</p>
            <Button onClick={() => window.location.reload()} className="mt-4">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">EEU Transformer Management Dashboard</h1>
          <p className="text-muted-foreground">
            Comprehensive monitoring and management of Ethiopian Electric Utility transformers
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
            {getActiveFilterCount() > 0 && (
              <Badge variant="secondary">{getActiveFilterCount()}</Badge>
            )}
          </Button>
          <Button
            variant="outline"
            onClick={saveFiltersToStorage}
            disabled={getActiveFilterCount() === 0}
          >
            Save Filters
          </Button>
          <Button
            variant="outline"
            onClick={loadFiltersFromStorage}
          >
            Load Filters
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <ComprehensiveFilterPanel
          onFiltersChange={updateFilters}
          onReset={resetFilters}
        />
      )}

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              <span>Loading filtered data...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dashboard Content */}
      {filteredData && (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Transformers</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{filteredData.summary.totalTransformers}</div>
                <p className="text-xs text-muted-foreground">
                  {filteredData.summary.operationalCount} operational
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">System Availability</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {filteredData.summary.totalTransformers > 0 
                    ? ((filteredData.summary.operationalCount / filteredData.summary.totalTransformers) * 100).toFixed(1)
                    : 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {filteredData.summary.criticalCount} critical issues
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Efficiency</CardTitle>
                <Gauge className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {filteredData.summary.avgEfficiency.toFixed(1)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Load factor: {filteredData.summary.avgLoadFactor.toFixed(1)}%
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Asset Value</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${filteredData.summary.totalAssetValue.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  {filteredData.summary.activeAlerts} active alerts
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Charts and Analytics */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="regional">Regional Analysis</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
              <TabsTrigger value="alerts">Alerts</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Status Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Transformer Status Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={statusData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {statusData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Regional Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Regional Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={regionalData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="code" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="transformers" fill="#3b82f6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="regional" className="space-y-4">
              <div className="grid grid-cols-1 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Regional Performance Comparison</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={400}>
                      <BarChart data={regionalData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="transformers" fill="#22c55e" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <div className="grid grid-cols-1 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Transformer Performance Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={400}>
                      <AreaChart data={performanceData.slice(0, 10)}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Area type="monotone" dataKey="efficiency" stackId="1" stroke="#22c55e" fill="#22c55e" />
                        <Area type="monotone" dataKey="loadFactor" stackId="2" stroke="#3b82f6" fill="#3b82f6" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="maintenance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Maintenance Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Pending Maintenance</span>
                      <Badge variant="outline">{filteredData.summary.pendingMaintenance}</Badge>
                    </div>
                    <div className="space-y-2">
                      {filteredData.maintenance.slice(0, 5).map((maintenance: any) => (
                        <div key={maintenance.id} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <p className="font-medium">{maintenance.title}</p>
                            <p className="text-sm text-muted-foreground">{maintenance.transformer_name}</p>
                          </div>
                          <Badge variant={
                            maintenance.priority === 'critical' ? 'destructive' :
                            maintenance.priority === 'high' ? 'default' : 'secondary'
                          }>
                            {maintenance.priority}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="alerts" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Active Alerts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Active Alerts</span>
                      <Badge variant="outline">{filteredData.summary.activeAlerts}</Badge>
                    </div>
                    <div className="space-y-2">
                      {filteredData.alerts.filter((alert: any) => alert.status === 'active').slice(0, 5).map((alert: any) => (
                        <div key={alert.id} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <p className="font-medium">{alert.title}</p>
                            <p className="text-sm text-muted-foreground">{alert.transformer_name}</p>
                          </div>
                          <Badge variant={
                            alert.severity === 'critical' ? 'destructive' :
                            alert.severity === 'high' ? 'default' : 'secondary'
                          }>
                            {alert.severity}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}
