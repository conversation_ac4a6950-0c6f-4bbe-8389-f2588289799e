"use client"

import { useEffect, useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { Button } from "@/src/components/ui/button"
import { serviceFactory } from "@/src/services/service-factory"
import type { Transformer } from "@/src/types/transformer"
import { Skeleton } from "@/src/components/ui/skeleton"
import { AlertCircle, Eye, FileText, Wrench, Calendar } from "lucide-react"
import Link from "next/link"
import { useToast } from "@/src/components/ui/use-toast"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/src/components/ui/dropdown-menu"

interface TransformersListProps {
  searchQuery?: string
  statusFilter?: string
  regionFilter?: string
  refreshTrigger?: number
}

export function TransformersList({
  searchQuery = "",
  statusFilter = "all",
  regionFilter = "all",
  refreshTrigger = 0
}: TransformersListProps) {
  const [transformers, setTransformers] = useState<Transformer[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    const fetchTransformers = async () => {
      try {
        setLoading(true)
        const transformerService = serviceFactory.getTransformerService()

        // Get all transformers first
        let data = await transformerService.getAllTransformers()

        // Apply search filter if provided
        if (searchQuery) {
          data = data.filter(transformer =>
            transformer.serialNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
            transformer.manufacturer.toLowerCase().includes(searchQuery.toLowerCase()) ||
            transformer.model?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            transformer.type?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            transformer.location?.region?.toLowerCase().includes(searchQuery.toLowerCase())
          )
        }

        // Apply status filter if needed
        if (statusFilter !== "all") {
          data = data.filter(transformer =>
            transformer.status.toLowerCase() === statusFilter.toLowerCase()
          )
        }

        // Apply region filter if needed
        if (regionFilter !== "all") {
          data = data.filter(transformer =>
            transformer.location?.region?.toLowerCase().includes(regionFilter.toLowerCase())
          )
        }

        setTransformers(data)
        setError(null)
      } catch (err) {
        setError("Failed to load transformers")
        console.error("Error loading transformers:", err)
      } finally {
        setLoading(false)
      }
    }

    fetchTransformers()
  }, [searchQuery, statusFilter, regionFilter, refreshTrigger])

  // Handle scheduling maintenance
  const handleScheduleMaintenance = (transformer: Transformer) => {
    // In a real app, this would open a dialog to schedule maintenance
    toast({
      title: "Maintenance scheduled",
      description: `Maintenance has been scheduled for transformer ${transformer.serialNumber}.`,
    })
  }

  // Handle generating report
  const handleGenerateReport = (transformer: Transformer) => {
    // In a real app, this would generate a PDF report
    toast({
      title: "Report generated",
      description: `Report has been generated for transformer ${transformer.serialNumber}.`,
    })
  }

  if (loading) {
    return (
      <div className="w-full">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Serial Number</TableHead>
                <TableHead>Manufacturer</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Capacity</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array(5)
                .fill(0)
                .map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Skeleton className="h-4 w-32" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-20" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-16" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-28" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-6 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-9 w-28 ml-auto" />
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-[200px] text-center">
        <AlertCircle className="h-8 w-8 text-destructive mb-2" />
        <p className="text-muted-foreground">{error}</p>
      </div>
    )
  }

  if (transformers.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-[200px] text-center">
        <p className="text-muted-foreground">No transformers found</p>
      </div>
    )
  }

  return (
    <div className="w-full">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Serial Number</TableHead>
              <TableHead>Manufacturer</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Capacity</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transformers.map((transformer) => (
              <TableRow key={transformer.id}>
                <TableCell className="font-medium">{transformer.serialNumber}</TableCell>
                <TableCell>{transformer.manufacturer}</TableCell>
                <TableCell>{transformer.type}</TableCell>
                <TableCell>{transformer.capacity} kVA</TableCell>
                <TableCell>{transformer.location?.region || "Unknown"}</TableCell>
                <TableCell>
                  <Badge
                    className={
                      transformer.status === "Operational"
                        ? "bg-green-500 hover:bg-green-600"
                        : transformer.status === "Maintenance"
                          ? "bg-yellow-500 hover:bg-yellow-600"
                          : "bg-red-500 hover:bg-red-600"
                    }
                  >
                    {transformer.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button variant="ghost" size="icon" asChild>
                      <Link href={`/transformers/${transformer.id}`}>
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">View</span>
                      </Link>
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleGenerateReport(transformer)}
                    >
                      <FileText className="h-4 w-4" />
                      <span className="sr-only">Report</span>
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <Wrench className="h-4 w-4" />
                          <span className="sr-only">Maintenance</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleScheduleMaintenance(transformer)}>
                          <Calendar className="mr-2 h-4 w-4" />
                          Schedule Maintenance
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/transformers/${transformer.id}?tab=maintenance`}>
                            <FileText className="mr-2 h-4 w-4" />
                            View Maintenance History
                          </Link>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
