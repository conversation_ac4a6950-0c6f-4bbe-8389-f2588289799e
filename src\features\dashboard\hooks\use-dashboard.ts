import { useState, useEffect, useCallback } from 'react'
import { useToast } from '@/src/components/ui/use-toast'
import type { DashboardData, DashboardStats } from '@/src/types/dashboard'

interface UseDashboardOptions {
  autoRefresh?: boolean
  refreshInterval?: number
}

export function useDashboard(initialData?: DashboardData, options: UseDashboardOptions = {}) {
  const { autoRefresh = false, refreshInterval = 30000 } = options
  const { toast } = useToast()

  // State
  const [data, setData] = useState<DashboardData>(initialData || {})
  const [stats, setStats] = useState<DashboardStats>({
    totalTransformers: 0,
    healthyPercentage: 0,
    activeAlerts: 0,
    scheduledMaintenance: 0,
    criticalIssues: 0,
    systemUptime: 0,
    operationalCount: 0,
    warningCount: 0,
    maintenanceCount: 0,
    criticalCount: 0,
    offlineCount: 0
  })
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState(new Date())
  const [realTimeEnabled, setRealTimeEnabled] = useState(autoRefresh)
  const [refreshing, setRefreshing] = useState(false)
  const [region, setRegion] = useState("all")
  const [timeRange, setTimeRange] = useState("30d")

  // Generate mock data fallback
  const generateMockData = useCallback((): DashboardData => {
    const currentTime = new Date()
    
    const mockTransformers = Array.from({ length: 72 }, (_, i) => ({
      id: `transformer-${i + 1}`,
      name: `Transformer ${i + 1}`,
      location: `Location ${i + 1}`,
      status: ['operational', 'warning', 'maintenance', 'critical'][Math.floor(Math.random() * 4)],
      capacity: Math.floor(Math.random() * 1000) + 100,
      load: Math.floor(Math.random() * 100),
      temperature: Math.floor(Math.random() * 50) + 20,
      lastMaintenance: new Date(currentTime.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    }))

    const statusCounts = mockTransformers.reduce((acc, t) => {
      acc[t.status] = (acc[t.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const mockAlerts = Array.from({ length: 15 }, (_, i) => ({
      id: `alert-${i + 1}`,
      title: `System Alert ${i + 1}`,
      description: `Alert description for transformer monitoring system ${i + 1}`,
      severity: ['critical', 'warning', 'info'][Math.floor(Math.random() * 3)],
      created_at: new Date(currentTime.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      status: 'active'
    }))

    const mockMaintenance = Array.from({ length: 10 }, (_, i) => ({
      id: `maintenance-${i + 1}`,
      type: 'Preventive Maintenance',
      description: `Scheduled maintenance for transformer ${i + 1}`,
      scheduled_date: new Date(currentTime.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000),
      priority: ['high', 'normal', 'low'][Math.floor(Math.random() * 3)],
      status: 'scheduled'
    }))

    return {
      transformerStatistics: {
        total: mockTransformers.length,
        byStatus: statusCounts,
        byRegion: {
          'addis-ababa': Math.floor(mockTransformers.length * 0.3),
          'oromia': Math.floor(mockTransformers.length * 0.25),
          'amhara': Math.floor(mockTransformers.length * 0.2),
          'tigray': Math.floor(mockTransformers.length * 0.15),
          'snnp': Math.floor(mockTransformers.length * 0.1)
        },
        healthyPercentage: Math.round((statusCounts.operational || 0) / mockTransformers.length * 100)
      },
      recentAlerts: mockAlerts.slice(0, 8),
      upcomingMaintenance: mockMaintenance.slice(0, 8),
      filteredTransformers: mockTransformers,
      alertStatistics: {
        total: mockAlerts.length,
        critical: mockAlerts.filter(a => a.severity === 'critical').length,
        warning: mockAlerts.filter(a => a.severity === 'warning').length,
        info: mockAlerts.filter(a => a.severity === 'info').length,
        unresolved: mockAlerts.filter(a => a.status === 'active').length
      },
      maintenanceStatistics: {
        scheduled: mockMaintenance.filter(m => m.status === 'scheduled').length,
        completed: Math.floor(Math.random() * 20),
        overdue: Math.floor(Math.random() * 5),
        total: mockMaintenance.length + 25
      }
    }
  }, [])

  // Fetch dashboard data
  const fetchData = useCallback(async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        region,
        timeRange,
        realTime: realTimeEnabled.toString(),
        analytics: 'true',
        weather: 'true'
      })

      const response = await fetch(`/api/mysql/dashboard?${params}`)
      const result = await response.json()

      if (result.success && result.data) {
        setData(result.data)
        
        const transformerStats = result.data.transformerStatistics || {}
        const byStatus = transformerStats.byStatus || {}

        setStats({
          totalTransformers: transformerStats.total || 0,
          healthyPercentage: transformerStats.healthyPercentage || 0,
          activeAlerts: result.data.alertStatistics?.unresolved || 0,
          scheduledMaintenance: result.data.maintenanceStatistics?.scheduled || 0,
          criticalIssues: result.data.alertStatistics?.critical || 0,
          systemUptime: 99.9,
          operationalCount: byStatus.operational || 0,
          warningCount: byStatus.warning || 0,
          maintenanceCount: byStatus.maintenance || 0,
          criticalCount: byStatus.critical || 0,
          offlineCount: byStatus.offline || byStatus.burnt || 0
        })

        setLastUpdated(new Date())
      } else {
        // Fallback to mock data
        const mockData = generateMockData()
        setData(mockData)
        
        const transformerStats = mockData.transformerStatistics
        const byStatus = transformerStats.byStatus

        setStats({
          totalTransformers: transformerStats.total,
          healthyPercentage: transformerStats.healthyPercentage,
          activeAlerts: mockData.alertStatistics.unresolved,
          scheduledMaintenance: mockData.maintenanceStatistics.scheduled,
          criticalIssues: mockData.alertStatistics.critical,
          systemUptime: 99.9,
          operationalCount: byStatus.operational || 0,
          warningCount: byStatus.warning || 0,
          maintenanceCount: byStatus.maintenance || 0,
          criticalCount: byStatus.critical || 0,
          offlineCount: byStatus.offline || 0
        })

        setLastUpdated(new Date())
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      
      // Use mock data as fallback
      const mockData = generateMockData()
      setData(mockData)
      
      const transformerStats = mockData.transformerStatistics
      const byStatus = transformerStats.byStatus

      setStats({
        totalTransformers: transformerStats.total,
        healthyPercentage: transformerStats.healthyPercentage,
        activeAlerts: mockData.alertStatistics.unresolved,
        scheduledMaintenance: mockData.maintenanceStatistics.scheduled,
        criticalIssues: mockData.alertStatistics.critical,
        systemUptime: 99.9,
        operationalCount: byStatus.operational || 0,
        warningCount: byStatus.warning || 0,
        maintenanceCount: byStatus.maintenance || 0,
        criticalCount: byStatus.critical || 0,
        offlineCount: byStatus.offline || 0
      })

      setLastUpdated(new Date())
    } finally {
      setIsLoading(false)
    }
  }, [region, timeRange, realTimeEnabled, generateMockData])

  // Execute dashboard actions
  const executeAction = useCallback(async (action: string, payload?: any) => {
    try {
      const response = await fetch('/api/mysql/dashboard/actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, payload }),
      })

      const result = await response.json()

      if (result.success) {
        // Refresh data after successful action
        await fetchData()
        return result
      } else {
        throw new Error(result.message || 'Action failed')
      }
    } catch (error) {
      console.error(`Error executing action ${action}:`, error)
      throw error
    }
  }, [fetchData])

  // Refresh data
  const refresh = useCallback(async () => {
    setRefreshing(true)
    await fetchData()
    setTimeout(() => setRefreshing(false), 1000)
  }, [fetchData])

  // Toggle real-time updates
  const toggleRealTime = useCallback(() => {
    setRealTimeEnabled(prev => !prev)
  }, [])

  // Effects
  useEffect(() => {
    fetchData()
  }, [fetchData])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (realTimeEnabled) {
      interval = setInterval(fetchData, refreshInterval)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [realTimeEnabled, fetchData, refreshInterval])

  return {
    data,
    stats,
    isLoading,
    lastUpdated,
    realTimeEnabled,
    refreshing,
    region,
    timeRange,
    setRegion,
    setTimeRange,
    toggleRealTime,
    refresh,
    executeAction,
    fetchData
  }
}
