"use client"

import { MainLayout } from "../../src/components/layout/main-layout"
import { FilteredDashboard } from "../../components/dashboard/filtered-dashboard"

export default function DashboardPage() {
  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "national_maintenance_manager",
        "regional_admin",
        "regional_asset_manager",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician",
        "customer_service_agent",
        "audit_compliance_officer"
      ]}
      requiredPermissions={[{ resource: "dashboard", action: "read" }]}
    >
      <FilteredDashboard />
    </MainLayout>
  )
}
