"use client"

import React from 'react'
import { MainLayout } from "../../src/components/layout/main-layout"
import { DashboardSummaryCards } from "@/src/components/dashboard/dashboard-summary"
import { useDashboardData } from "@/src/hooks/use-dashboard-data"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { RefreshCw, AlertTriangle } from 'lucide-react'

/**
 * Clean Dashboard Page
 * Refactored for better performance and maintainability
 */
export default function DashboardPage() {
  const { data, isLoading, error, refetch } = useDashboardData()

  const handleRefresh = async () => {
    await refetch()
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "national_maintenance_manager",
        "regional_admin",
        "regional_asset_manager",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician",
        "customer_service_agent",
        "audit_compliance_officer"
      ]}
      requiredPermissions={[{ resource: "dashboard", action: "read" }]}
    >
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              EEU Digital Transformer Management
            </h1>
            <p className="text-gray-600 mt-1">
              Real-time monitoring and analytics dashboard
            </p>
          </div>

          <Button
            onClick={handleRefresh}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Error State */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-800">
                <AlertTriangle className="h-5 w-5" />
                <span className="font-medium">Error loading dashboard data</span>
              </div>
              <p className="text-red-600 mt-1">{error}</p>
              <Button
                onClick={handleRefresh}
                variant="outline"
                size="sm"
                className="mt-3"
              >
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Dashboard Summary */}
        {data?.summary && (
          <DashboardSummaryCards
            summary={data.summary}
            isLoading={isLoading}
            className="mb-6"
          />
        )}

        {/* Additional Dashboard Content */}
        {data && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Transformers Overview */}
            <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Transformers Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Total Transformers:</span>
                    <span className="font-semibold">{data.transformers.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Regions Covered:</span>
                    <span className="font-semibold">{data.regions.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Service Centers:</span>
                    <span className="font-semibold">{data.serviceCenters.length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Alerts */}
            <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Recent Alerts</CardTitle>
              </CardHeader>
              <CardContent>
                {data.alerts.length > 0 ? (
                  <div className="space-y-2">
                    {data.alerts.slice(0, 5).map((alert) => (
                      <div key={alert.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">{alert.title}</span>
                        <span className={`text-xs px-2 py-1 rounded ${
                          alert.severity === 'critical' ? 'bg-red-100 text-red-800' :
                          alert.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                          alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {alert.severity}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">No active alerts</p>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Loading State */}
        {isLoading && !data && (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading dashboard data...</span>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
