# EEU Transformer Management System

## Project Structure

This project follows a modern, scalable architecture with clear separation of concerns:

### 📁 Directory Structure

```
src/
├── components/          # Reusable UI components
├── features/           # Feature-based modules
├── shared/            # Shared utilities and services
├── styles/            # Global styles and themes
└── assets/            # Static assets
```

### 🎯 Key Features

- **Modern Architecture**: Feature-based organization
- **Type Safety**: Comprehensive TypeScript coverage
- **Reusable Components**: Shared UI component library
- **Consistent Styling**: Tailwind CSS with custom themes
- **Performance Optimized**: Code splitting and lazy loading

### 🚀 Getting Started

1. Install dependencies: `npm install`
2. Run development server: `npm run dev`
3. Open [http://localhost:3002](http://localhost:3002)

### 📖 Documentation

- [Setup Guide](./docs/SETUP.md)
- [API Documentation](./docs/API.md)
- [Deployment Guide](./docs/DEPLOYMENT.md)
- [Contributing Guidelines](./docs/CONTRIBUTING.md)

### 🛠️ Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run test` - Run tests
- `npm run lint` - Lint code
- `npm run type-check` - Type checking

### 📝 License

Ethiopian Electric Utility - Internal Use Only
