"use client"

import { useState, useEffect } from 'react'
import { AlertTriangle, AlertCircle, Bell, Info, CheckCircle, ChevronRight } from 'lucide-react'
import { AlertsListWidget as AlertsListWidgetType } from '../../../types/dashboard-widgets'

interface AlertsListWidgetProps {
  widget: AlertsListWidgetType
  isEditing: boolean
}

export default function AlertsListWidget({ widget, isEditing }: AlertsListWidgetProps) {
  const [data, setData] = useState({
    alerts: [
      {
        id: 'alert-001',
        title: 'Transformer Overheating',
        message: 'Transformer TR-001 temperature exceeding threshold (85°C)',
        category: 'transformer',
        severity: 'critical',
        status: 'active',
        source: 'sensor',
        createdAt: '2023-06-14T08:30:00Z',
        entity: {
          type: 'transformer',
          id: 'tr-001',
          name: 'Bole Sub-station T1'
        },
        metadata: {
          regionId: 'region-001',
          serviceCenterId: 'sc-001',
          readings: {
            temperature: 85,
            threshold: 75
          }
        }
      },
      {
        id: 'alert-002',
        title: 'Maintenance Overdue',
        message: 'Scheduled maintenance for TR-023 is overdue by 4 days',
        category: 'maintenance',
        severity: 'high',
        status: 'active',
        source: 'system',
        createdAt: '2023-06-13T14:15:00Z',
        entity: {
          type: 'transformer',
          id: 'tr-023',
          name: 'Yeka Sub-station T1'
        },
        metadata: {
          regionId: 'region-001',
          serviceCenterId: 'sc-003',
          maintenanceId: 'maint-004'
        }
      },
      {
        id: 'alert-003',
        title: 'Oil Level Low',
        message: 'Transformer TR-042 oil level below minimum threshold (45%)',
        category: 'transformer',
        severity: 'high',
        status: 'active',
        source: 'sensor',
        createdAt: '2023-06-14T09:45:00Z',
        entity: {
          type: 'transformer',
          id: 'tr-042',
          name: 'Arada Sub-station T2'
        },
        metadata: {
          regionId: 'region-001',
          serviceCenterId: 'sc-004',
          readings: {
            oilLevel: 45,
            threshold: 50
          }
        }
      },
      {
        id: 'alert-004',
        title: 'Weather Warning',
        message: 'Heavy rainfall expected in Addis Ababa region in the next 24 hours',
        category: 'weather',
        severity: 'medium',
        status: 'active',
        source: 'weather-service',
        createdAt: '2023-06-14T07:00:00Z',
        entity: {
          type: 'region',
          id: 'region-001',
          name: 'Addis Ababa'
        },
        metadata: {
          forecast: {
            rainfall: 50, // mm
            windSpeed: 25, // km/h
            alertType: 'heavy-rain'
          }
        }
      },
      {
        id: 'alert-005',
        title: 'Load Imbalance',
        message: 'Transformer TR-015 showing phase load imbalance (>20%)',
        category: 'transformer',
        severity: 'medium',
        status: 'active',
        source: 'sensor',
        createdAt: '2023-06-14T10:15:00Z',
        entity: {
          type: 'transformer',
          id: 'tr-015',
          name: 'Kirkos Sub-station T3'
        },
        metadata: {
          regionId: 'region-001',
          serviceCenterId: 'sc-002',
          readings: {
            loadImbalance: 22,
            threshold: 20
          }
        }
      }
    ],
    summary: {
      total: 22,
      bySeverity: {
        info: 5,
        low: 4,
        medium: 6,
        high: 5,
        critical: 2
      },
      byCategory: {
        transformer: 12,
        maintenance: 4,
        outage: 2,
        weather: 3,
        system: 1
      },
      byStatus: {
        active: 18,
        acknowledged: 3,
        resolved: 1
      }
    }
  })
  
  const [isLoading, setIsLoading] = useState(false)
  const [filter, setFilter] = useState<'all' | 'critical' | 'high' | 'medium'>('all')
  
  // Fetch data
  useEffect(() => {
    // In a real implementation, this would fetch from the API
    // For now, we're using mock data initialized above
  }, [widget.id])
  
  // Refresh data
  const refreshData = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }
  
  // Format date
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.round(diffMs / 60000)
    
    if (diffMins < 1) return 'just now'
    if (diffMins < 60) return `${diffMins}m ago`
    
    const diffHours = Math.floor(diffMins / 60)
    if (diffHours < 24) return `${diffHours}h ago`
    
    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays}d ago`
  }
  
  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'info':
        return 'bg-blue-100 text-blue-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'critical':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
  
  // Get severity icon
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'info':
        return <Info size={14} className="text-blue-500" />
      case 'low':
        return <CheckCircle size={14} className="text-green-500" />
      case 'medium':
        return <AlertCircle size={14} className="text-yellow-500" />
      case 'high':
        return <AlertCircle size={14} className="text-orange-500" />
      case 'critical':
        return <AlertTriangle size={14} className="text-red-500" />
      default:
        return <Bell size={14} className="text-gray-500" />
    }
  }
  
  // Filter alerts
  const filteredAlerts = data.alerts.filter(alert => {
    if (filter === 'all') return true
    return alert.severity === filter
  })
  
  return (
    <div>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center space-x-2">
          <button
            className={`px-2 py-0.5 rounded text-xs ${filter === 'all' ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setFilter('all')}
          >
            All
          </button>
          
          <button
            className={`px-2 py-0.5 rounded text-xs ${filter === 'critical' ? 'bg-red-100 text-red-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setFilter('critical')}
          >
            Critical
          </button>
          
          <button
            className={`px-2 py-0.5 rounded text-xs ${filter === 'high' ? 'bg-orange-100 text-orange-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setFilter('high')}
          >
            High
          </button>
        </div>
        
        <div className="text-xs text-gray-500">
          {data.summary.total} total
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex items-center justify-center h-48">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-green-500"></div>
        </div>
      ) : (
        <div>
          {/* Alerts list */}
          <div className="space-y-2 max-h-64 overflow-y-auto pr-1">
            {filteredAlerts.length > 0 ? (
              filteredAlerts.map(alert => (
                <div key={alert.id} className="bg-gray-50 rounded-lg p-2 text-sm">
                  <div className="flex items-start">
                    <div className="mt-0.5 mr-2">
                      {getSeverityIcon(alert.severity)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div className="font-medium text-sm">{alert.title}</div>
                        <span className={`text-xs rounded-full px-1.5 py-0.5 ${getSeverityColor(alert.severity)}`}>
                          {alert.severity}
                        </span>
                      </div>
                      <div className="text-xs text-gray-600 mt-0.5">{alert.message}</div>
                      <div className="flex items-center justify-between mt-1">
                        <div className="text-xs text-gray-500">
                          {alert.entity.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatTimeAgo(alert.createdAt)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                No alerts match the selected filter
              </div>
            )}
          </div>
          
          <div className="mt-3 text-center">
            <a href="/alerts" className="text-green-600 text-sm hover:underline inline-flex items-center">
              View all alerts
              <ChevronRight size={14} className="ml-1" />
            </a>
          </div>
        </div>
      )}
    </div>
  )
}
