"use client"

import { UnifiedMap } from "@/components/unified-map"
import { mapService } from "@/src/services/map-service"
import { Transformer } from "@/src/types/transformer"

interface TransformerLocationMapProps {
  transformer: Transformer
}

export function TransformerLocationMap({ transformer }: TransformerLocationMapProps) {
  // Use the standardized map service function to convert transformer to map location
  const location = mapService.transformerToMapLocation(transformer)

  return (
    <UnifiedMap
      locations={[location]}
      height="100%"
      clustered={false}
      showControls={true}
      showLegend={false}
      interactive={true}
      initialZoom={15}
    />
  )
}
