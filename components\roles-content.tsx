"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/src/components/ui/tabs"
import { RolesManagement } from "@/components/roles-management"
import { RoleAnalytics } from "@/components/role-analytics"
import { RoleAssignment } from "@/components/role-assignment"
import { RoleActivityLog } from "@/components/role-activity-log"
import { Shield, Users, BarChart3, Clock } from "lucide-react"
import type { RoleDefinition } from "@/src/types/auth"

export function RolesContent() {
  const [activeTab, setActiveTab] = useState("roles")

  // Mock roles data - in a real app, this would come from an API
  const roles: RoleDefinition[] = [
    {
      id: "role-001",
      name: "Super Admin",
      description: "Full system control (users, roles, settings)",
      organizationalLevel: "head_office",
      permissions: [
        { resource: "users", action: "create" },
        { resource: "users", action: "read" },
        { resource: "users", action: "update" },
        { resource: "users", action: "delete" },
        { resource: "roles", action: "create" },
        { resource: "roles", action: "read" },
        { resource: "roles", action: "update" },
        { resource: "roles", action: "delete" },
        { resource: "transformers", action: "create" },
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "transformers", action: "delete" },
        { resource: "transformers", action: "approve" },
        { resource: "maintenance", action: "create" },
        { resource: "maintenance", action: "read" },
        { resource: "maintenance", action: "update" },
        { resource: "maintenance", action: "delete" },
        { resource: "maintenance", action: "approve" },
        { resource: "maintenance", action: "assign" },
        { resource: "reports", action: "export" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-002",
      name: "National Asset Manager",
      description: "Access nationwide transformer inventory",
      organizationalLevel: "head_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "transformers", action: "approve" },
        { resource: "reports", action: "export" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-003",
      name: "National Maintenance Manager",
      description: "National maintenance planning",
      organizationalLevel: "head_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "maintenance", action: "create" },
        { resource: "maintenance", action: "read" },
        { resource: "maintenance", action: "update" },
        { resource: "maintenance", action: "approve" },
        { resource: "reports", action: "export" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-004",
      name: "Regional Admin",
      description: "Manage regional users & service centers",
      organizationalLevel: "regional_office",
      permissions: [
        { resource: "users", action: "create" },
        { resource: "users", action: "read" },
        { resource: "users", action: "update" },
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "maintenance", action: "assign" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-005",
      name: "Regional Asset Manager",
      description: "Manage regional assets",
      organizationalLevel: "regional_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "transformers", action: "approve" },
        { resource: "reports", action: "export" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-006",
      name: "Regional Maintenance Engineer",
      description: "Monitor regional transformer health",
      organizationalLevel: "regional_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "maintenance", action: "create" },
        { resource: "maintenance", action: "read" },
        { resource: "maintenance", action: "update" },
        { resource: "maintenance", action: "approve" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-007",
      name: "Service Center Manager",
      description: "Oversee daily operations",
      organizationalLevel: "service_center",
      permissions: [
        { resource: "users", action: "read" },
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "maintenance", action: "create" },
        { resource: "maintenance", action: "read" },
        { resource: "maintenance", action: "update" },
        { resource: "maintenance", action: "assign" },
        { resource: "maintenance", action: "approve" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-008",
      name: "Field Technician",
      description: "Conduct inspections & repairs",
      organizationalLevel: "service_center",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "maintenance", action: "read" },
        { resource: "maintenance", action: "update" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-009",
      name: "Customer Service Agent",
      description: "Log customer complaints/requests",
      organizationalLevel: "service_center",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "outages", action: "read" },
        { resource: "customer_requests", action: "create" },
        { resource: "customer_requests", action: "read" },
        { resource: "customer_requests", action: "update" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-010",
      name: "Audit & Compliance Officer",
      description: "Review logs and monitor compliance",
      organizationalLevel: "head_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "maintenance", action: "read" },
        { resource: "reports", action: "export" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-011",
      name: "Custom Role",
      description: "Custom role for specific needs",
      organizationalLevel: "regional_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "maintenance", action: "read" },
      ],
      isSystemRole: false,
    },
  ]

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Role Management</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="assignment" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Assignment
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Activity Log
          </TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-4">
          <RolesManagement />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <RoleAnalytics roles={roles} />
        </TabsContent>

        <TabsContent value="assignment" className="space-y-4">
          <RoleAssignment roles={roles} />
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <RoleActivityLog roles={roles} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
