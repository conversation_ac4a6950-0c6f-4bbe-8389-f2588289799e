"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { FileText, Loader2 } from "lucide-react"
import type { Transformer, InspectionRecord, MaintenanceRecord, TestResult } from "@/src/types/transformer"
import { jsPDF } from "jspdf"
// Import jspdf-autotable directly with require to avoid bundling issues
const autoTable = require('jspdf-autotable')
import { toast } from "@/src/components/ui/use-toast"

interface PDFExportButtonProps {
  transformer: Transformer
  inspections: InspectionRecord[]
  maintenances: MaintenanceRecord[]
  testResults: TestResult[]
  className?: string
}

export function PDFExportButton({
  transformer,
  inspections,
  maintenances,
  testResults,
  className,
}: PDFExportButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false)

  const generatePDF = async () => {
    setIsGenerating(true)

    try {
      // Create a new PDF document
      const doc = new jsPDF()
      const pageWidth = doc.internal.pageSize.getWidth()

      // Instead of trying to add the SVG directly, we'll create a colored rectangle to represent the logo
      // In a production environment, you would use a base64 encoded image or a server-side approach

      // Draw a green and orange circle to represent the EEU logo
      const logoSize = 20
      const logoX = (pageWidth - logoSize) / 2
      const logoY = 10

      // Draw orange circle (background)
      doc.setFillColor(249, 168, 37) // EEU orange color
      doc.circle(logoX + logoSize/2, logoY + logoSize/2, logoSize/2, 'F')

      // Draw green overlay
      doc.setFillColor(76, 175, 80) // EEU green color
      doc.rect(logoX, logoY + logoSize/4, logoSize, logoSize/2, 'F')

      // Add a simple vertical line to represent the power pole
      doc.setFillColor(255, 255, 255) // White
      doc.rect(logoX + logoSize/2 - 1, logoY + 5, 2, logoSize - 10, 'F')

      // Add a horizontal line to represent the cross beam
      doc.rect(logoX + 5, logoY + logoSize/2 - 1, logoSize - 10, 2, 'F')

      // Add header text
      doc.setFontSize(18)
      doc.setTextColor(76, 175, 80) // EEU green color
      doc.text("Ethiopian Electric Utility", pageWidth / 2, 40, { align: "center" })
      doc.setTextColor(0, 0, 0)
      doc.setFontSize(14)
      doc.text("Transformer Details Report", pageWidth / 2, 47, { align: "center" })
      doc.setFontSize(12)
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, pageWidth / 2, 53, { align: "center" })

      // Add transformer basic info
      doc.setFontSize(14)
      doc.text(`Transformer: ${transformer.transformerCode}`, 14, 65)
      doc.setFontSize(10)
      doc.text(`Status: ${transformer.status}`, 14, 71)
      doc.text(`Location: ${transformer.location.specificLocation}`, 14, 77)
      doc.text(`Substation: ${transformer.substationName} / Feeder: ${transformer.feederName}`, 14, 83)

      // Add technical specifications
      doc.setFontSize(12)
      doc.text("Technical Specifications", 14, 95)

      const techSpecsData = [
        ["Card No.", transformer.cardNo],
        ["KVA Rating", transformer.kvaRating],
        ["Primary Service Voltage", transformer.primaryServiceVoltage],
        ["Manufacturer", transformer.manufacturer],
        ["Manufacturing Year", transformer.manufacturingYear],
        ["Serial Number", transformer.serialNumber],
        ["Tag Number", transformer.tagNumber],
        ["Installation Date", new Date(transformer.installationDate).toLocaleDateString()],
        ["Construction Type", transformer.constructionType],
        ["Customer Type", transformer.customerType],
      ]

      if (transformer.lastChangingDate) {
        techSpecsData.push(
          ["Last Changing Date", new Date(transformer.lastChangingDate).toLocaleDateString()],
          ["Changing Reason", transformer.changingReason || "Not specified"],
        )
      }

      autoTable(doc, {
        startY: 100,
        head: [["Property", "Value"]],
        body: techSpecsData,
        theme: "striped",
        headStyles: { fillColor: [76, 175, 80] }, // EEU green color
      })

      // Add location details
      let currentY = (doc as any).lastAutoTable.finalY + 15
      doc.setFontSize(12)
      doc.text("Location Details", 14, currentY)

      const locationData = [
        [
          "GPS Coordinates",
          `${transformer.location.gpsCoordinates.latitude.toFixed(6)}, ${transformer.location.gpsCoordinates.longitude.toFixed(
            6,
          )}`,
        ],
        ["Region", transformer.location.region],
        ["District", transformer.location.district],
      ]

      if (transformer.location.subCity) {
        locationData.push(["Sub City", transformer.location.subCity])
      }

      if (transformer.location.kebele) {
        locationData.push(["Kebele", transformer.location.kebele])
      }

      locationData.push(["Specific Location", transformer.location.specificLocation])

      autoTable(doc, {
        startY: currentY + 5,
        head: [["Property", "Value"]],
        body: locationData,
        theme: "striped",
        headStyles: { fillColor: [76, 175, 80] }, // EEU green color
      })

      // Add maintenance records
      currentY = (doc as any).lastAutoTable.finalY + 15
      if (currentY > 250) {
        doc.addPage()
        currentY = 20
      }

      doc.setFontSize(12)
      doc.text("Maintenance Records", 14, currentY)

      if (maintenances.length > 0) {
        const maintenanceData = maintenances.map((maintenance) => [
          new Date(maintenance.date).toLocaleDateString(),
          maintenance.description,
          maintenance.type,
          maintenance.status,
          maintenance.technician,
          maintenance.notes || "",
        ])

        autoTable(doc, {
          startY: currentY + 5,
          head: [["Date", "Description", "Type", "Status", "Technician", "Notes"]],
          body: maintenanceData,
          theme: "striped",
          headStyles: { fillColor: [76, 175, 80] }, // EEU green color
        })
      } else {
        doc.setFontSize(10)
        doc.text("No maintenance records found", 14, currentY + 10)
      }

      // Add inspection records
      currentY = (doc as any).lastAutoTable ? (doc as any).lastAutoTable.finalY + 15 : currentY + 20
      if (currentY > 250) {
        doc.addPage()
        currentY = 20
      }

      doc.setFontSize(12)
      doc.text("Inspection Records", 14, currentY)

      if (inspections.length > 0) {
        const inspectionData = inspections.map((inspection) => [
          new Date(inspection.date).toLocaleDateString(),
          inspection.inspectorName,
          `L. Arrester: ${inspection.components.lArrester}, Links: ${inspection.components.links}, Fuse: ${inspection.components.dropOutFuse}, Ground: ${inspection.components.ground}`,
          `Bushings: ${inspection.conditions.bushings}, Oil: ${inspection.conditions.oilLevel}, Leakage: ${inspection.conditions.leakage}`,
          inspection.remarks || "",
        ])

        autoTable(doc, {
          startY: currentY + 5,
          head: [["Date", "Inspector", "Components", "Conditions", "Remarks"]],
          body: inspectionData,
          theme: "striped",
          headStyles: { fillColor: [76, 175, 80] }, // EEU green color
        })
      } else {
        doc.setFontSize(10)
        doc.text("No inspection records found", 14, currentY + 10)
      }

      // Add test results
      currentY = (doc as any).lastAutoTable ? (doc as any).lastAutoTable.finalY + 15 : currentY + 20
      if (currentY > 250) {
        doc.addPage()
        currentY = 20
      }

      doc.setFontSize(12)
      doc.text("Test Results", 14, currentY)

      if (testResults.length > 0) {
        const testResultsData = testResults.map((test) => [
          new Date(test.date).toLocaleDateString(),
          `R-S: ${test.meggerResults.rToS}, S-T: ${test.meggerResults.sToT}, T-R: ${test.meggerResults.tToR}, HT-G: ${test.meggerResults.htToGround}, LT-G: ${test.meggerResults.ltToGround}`,
          `${test.oilInsulation} kV / ${test.oilLevel}`,
          test.conclusion,
        ])

        autoTable(doc, {
          startY: currentY + 5,
          head: [["Date", "Megger Results (MΩ)", "Oil Condition", "Conclusion"]],
          body: testResultsData,
          theme: "striped",
          headStyles: { fillColor: [76, 175, 80] }, // EEU green color
        })
      } else {
        doc.setFontSize(10)
        doc.text("No test results found", 14, currentY + 10)
      }

      // Add footer
      const pageCount = doc.getNumberOfPages()
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i)

        // Add small logo to footer using shapes
        const footerLogoSize = 8
        const footerLogoX = 14
        const footerLogoY = doc.internal.pageSize.getHeight() - 15

        // Draw orange circle (background)
        doc.setFillColor(249, 168, 37) // EEU orange color
        doc.circle(footerLogoX + footerLogoSize/2, footerLogoY + footerLogoSize/2, footerLogoSize/2, 'F')

        // Draw green overlay
        doc.setFillColor(76, 175, 80) // EEU green color
        doc.rect(footerLogoX, footerLogoY + footerLogoSize/4, footerLogoSize, footerLogoSize/2, 'F')

        // Add a simple vertical line to represent the power pole
        doc.setFillColor(255, 255, 255) // White
        doc.rect(footerLogoX + footerLogoSize/2 - 0.5, footerLogoY + 2, 1, footerLogoSize - 4, 'F')

        // Add a horizontal line to represent the cross beam
        doc.rect(footerLogoX + 2, footerLogoY + footerLogoSize/2 - 0.5, footerLogoSize - 4, 1, 'F')

        // Add footer text
        doc.setFontSize(8)
        doc.setTextColor(76, 175, 80) // EEU green color
        doc.text(
          `Ethiopian Electric Utility - Distribution Transformer Management System - Page ${i} of ${pageCount}`,
          pageWidth / 2,
          doc.internal.pageSize.getHeight() - 10,
          { align: "center" },
        )
      }

      // Save the PDF
      doc.save(`Transformer_${transformer.transformerCode}_Report.pdf`)
      toast({
        title: "PDF Generated",
        description: "The transformer report has been successfully generated and downloaded.",
      })
    } catch (error) {
      console.error("Error generating PDF:", error)
      toast({
        title: "Error",
        description: "Failed to generate PDF report. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <Button variant="outline" size="sm" onClick={generatePDF} disabled={isGenerating} className={className}>
      {isGenerating ? (
        <>
          <Loader2 className="h-4 w-4 mr-1 animate-spin" /> Generating...
        </>
      ) : (
        <>
          <FileText className="h-4 w-4 mr-1" /> Export PDF
        </>
      )}
    </Button>
  )
}
