"use client"

import React, { useState, useEffect } from "react"
import { But<PERSON> } from '@/src/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/src/components/ui/tabs"
import { Badge } from '@/src/components/ui/badge'
import { useToast } from '@/src/components/ui/use-toast'
import {
  FileText,
  Printer,
  Download,
  Save,
  X,
  Plus,
  Trash2,
  Calendar,
  MapPin,
  Zap,
  ClipboardList,
  TestTube,
  AlertTriangle,
  CheckCircle,
  Eye,
  Edit
} from 'lucide-react'

interface UnifiedTransformerManagementProps {
  transformerId?: string
  onClose?: () => void
  initialTab?: 'inventory' | 'inspection' | 'megger-test'
}

export function UnifiedTransformerManagement({
  transformerId,
  onClose,
  initialTab = 'inventory'
}: UnifiedTransformerManagementProps) {
  const [activeTab, setActiveTab] = useState(initialTab)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Unified form data structure based on the three forms
  const [formData, setFormData] = useState({
    // Basic Information (Form 1)
    cardNo: '',
    substationName: '',
    feederName: '',
    transformerCode: '',
    kvaRating: '',
    primaryServiceVoltage: '',
    region: '',
    district: '',
    specificLocation: '',
    gpsLocation: '',
    manufacturer: '',
    yearOfManufacturing: '',
    serialNo: '',
    installationDate: '',
    changingDate: '',
    changingReason: '',
    dateFailure: '',
    reasonFailure: '',
    responsiblePerson: '',
    signature: '',

    // Customer Information
    customerType: '',
    customerName: '',
    subCity: '',
    kebele: '',

    // Construction and Delivery
    constructionType: {
      pole: false,
      mounted: false,
      concrete: false,
      beam: false,
      plinth: false,
      ground: false
    },
    deliveryDate: '',
    workshopDelivered: '',
    signatureDelivery: '',

    // Inspection Records (Form 1 - Table)
    inspections: [],

    // Component Inspection (Form 2)
    componentInspections: {
      earthResistance: { defective: false, trafoCode: '', qty: 0 },
      insulationResistance: { defective: false, trafoCode: '', qty: 0 },
      oilLevel: { defective: false, trafoCode: '', qty: 0 },
      oilTemperature: { defective: false, trafoCode: '', qty: 0 },
      unusualNoises: { defective: false, trafoCode: '', qty: 0 },
      misalignedBushing: { defective: false, trafoCode: '', qty: 0 },
      silicaGelColor: { defective: false, trafoCode: '', qty: 0 },
      lightningArrestor: { defective: false, trafoCode: '', qty: 0 },
      poleCondition: { defective: false, trafoCode: '', qty: 0 },
      connectionTightness: { defective: false, trafoCode: '', qty: 0 }
    },

    // Megger Test Results (Form 3)
    meggerTest: {
      region: '',
      district: '',
      serviceType: '',
      workOrderNo: '',
      specificLocation: '',
      gpsLocation: '',
      manufacturer: '',
      capacityKVA: '',
      serialNo: '',

      // Test measurements
      boxOne: {
        cableSize: '',
        fuseRating: ''
      },
      boxTwo: {
        cableSize: '',
        fuseRating: ''
      },
      linearRating: '',
      oilLevel: '',
      groundConnection: '',
      silicaGel: '',

      // Test Results
      testResults: {
        rs: '',
        st: '',
        tr: ''
      },
      ohmicValue: '',
      htToGround: '',
      ltToGround: '',
      oilInsulation: '',

      // Approval chain
      inspectedBy: {
        name: '',
        signature: '',
        date: ''
      },
      checkedBy: {
        name: '',
        signature: '',
        date: ''
      },
      approvedBy: {
        name: '',
        signature: '',
        date: ''
      },

      // Results and conclusions
      remarks: '',
      conclusion: '',
      faultType: '',
      silicaGel: '',
      isolationLink: ''
    }
  })

  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  // Load existing data
  const loadTransformerData = async () => {
    if (!transformerId) {
      setIsLoading(false)
      return
    }

    try {
      console.log('🔄 Loading transformer data:', transformerId)

      // Load from multiple endpoints
      const [historyResponse, inventoryResponse] = await Promise.all([
        fetch(`/api/mysql/transformer-history-cards?transformerId=${transformerId}`),
        fetch(`/api/mysql/transformers/${transformerId}`)
      ])

      if (historyResponse.ok) {
        const historyData = await historyResponse.json()
        if (historyData.success && historyData.historyCards?.length > 0) {
          const card = historyData.historyCards[0]
          // Populate form data from history card
          setFormData(prev => ({
            ...prev,
            cardNo: card.card_no || '',
            substationName: card.substation_name || '',
            feederName: card.feeder_name || '',
            transformerCode: card.transformer_code || '',
            kvaRating: card.kva_rating?.toString() || '',
            primaryServiceVoltage: card.primary_voltage || '',
            region: card.region || '',
            district: card.district || '',
            specificLocation: card.specific_location || '',
            gpsLocation: card.gps_location || '',
            manufacturer: card.manufacturer || '',
            yearOfManufacturing: card.year_of_manufacturing?.toString() || '',
            serialNo: card.serial_no || '',
            installationDate: card.installation_date || '',
            changingDate: card.changing_date || '',
            changingReason: card.changing_reason || '',
            responsiblePerson: card.responsible_person || '',
            signature: card.signature || '',
            customerType: card.customer_type || '',
            customerName: card.customer_name || '',
            subCity: card.sub_city || '',
            kebele: card.kebele || '',
            deliveryDate: card.delivery_date || '',
            inspections: card.inspections || [],
          }))
        }
      }

      if (inventoryResponse.ok) {
        const inventoryData = await inventoryResponse.json()
        if (inventoryData.success && inventoryData.transformer) {
          const transformer = inventoryData.transformer
          // Fill in any missing data from inventory
          setFormData(prev => ({
            ...prev,
            transformerCode: prev.transformerCode || transformer.serial_number || '',
            manufacturer: prev.manufacturer || transformer.manufacturer || '',
            kvaRating: prev.kvaRating || transformer.capacity?.toString() || '',
          }))
        }
      }

      console.log('✅ Transformer data loaded successfully')
    } catch (error) {
      console.error('❌ Error loading transformer data:', error)
      toast({
        variant: 'destructive',
        title: 'Loading Error',
        description: 'Failed to load transformer data. Please try again.',
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadTransformerData()
  }, [transformerId])

  // Form validation
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    // Basic validation
    if (!formData.cardNo) errors.cardNo = 'Card number is required'
    if (!formData.substationName) errors.substationName = 'Substation name is required'
    if (!formData.transformerCode) errors.transformerCode = 'Transformer code is required'
    if (!formData.kvaRating) errors.kvaRating = 'KVA rating is required'

    // GPS validation
    if (formData.gpsLocation && !/^-?\d+\.?\d*,-?\d+\.?\d*$/.test(formData.gpsLocation)) {
      errors.gpsLocation = 'Invalid GPS format (lat,lng)'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle input changes
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  // Handle nested object changes
  const handleNestedChange = (section: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [field]: value
      }
    }))
  }

  // Handle component inspection changes
  const handleComponentChange = (component: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      componentInspections: {
        ...prev.componentInspections,
        [component]: {
          ...prev.componentInspections[component as keyof typeof prev.componentInspections],
          [field]: value
        }
      }
    }))
  }

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg p-8 flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500 mb-4"></div>
          <p className="text-gray-600">Loading transformer data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-7xl max-h-[95vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b p-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-6 w-6 text-green-600" />
            <h2 className="text-xl font-bold">Distribution Transformer Management</h2>
            <Badge variant="outline" className="ml-2">
              {transformerId ? `ID: ${transformerId}` : 'New Record'}
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => window.print()}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="inventory" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Inventory & Basic Info
              </TabsTrigger>
              <TabsTrigger value="inspection" className="flex items-center gap-2">
                <ClipboardList className="h-4 w-4" />
                Component Inspection
              </TabsTrigger>
              <TabsTrigger value="megger-test" className="flex items-center gap-2">
                <TestTube className="h-4 w-4" />
                Megger Test Results
              </TabsTrigger>
            </TabsList>

            <TabsContent value="inventory" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Distribution Transformer History Card
                  </CardTitle>
                  <CardDescription>
                    Basic transformer information and installation details
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Basic Information Row 1 */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Card No. <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.cardNo}
                        onChange={(e) => handleInputChange('cardNo', e.target.value)}
                        className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 ${
                          formErrors.cardNo
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-green-500'
                        }`}
                        placeholder="Enter card number"
                      />
                      {formErrors.cardNo && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.cardNo}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Date of Failure (if exists)</label>
                      <input
                        type="date"
                        value={formData.dateFailure}
                        onChange={(e) => handleInputChange('dateFailure', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Reason of Failure (if exists)</label>
                      <input
                        type="text"
                        value={formData.reasonFailure}
                        onChange={(e) => handleInputChange('reasonFailure', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter failure reason"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Responsible Person</label>
                      <input
                        type="text"
                        value={formData.responsiblePerson}
                        onChange={(e) => handleInputChange('responsiblePerson', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter responsible person"
                      />
                    </div>
                  </div>

                  {/* Basic Information Row 2 */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Substation Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.substationName}
                        onChange={(e) => handleInputChange('substationName', e.target.value)}
                        className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 ${
                          formErrors.substationName
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-green-500'
                        }`}
                        placeholder="Enter substation name"
                      />
                      {formErrors.substationName && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.substationName}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Region</label>
                      <select
                        value={formData.region}
                        onChange={(e) => handleInputChange('region', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <option value="">Select Region</option>
                        <option value="Addis Ababa">Addis Ababa</option>
                        <option value="Oromia">Oromia</option>
                        <option value="Amhara">Amhara</option>
                        <option value="Tigray">Tigray</option>
                        <option value="SNNP">SNNP</option>
                        <option value="Somali">Somali</option>
                        <option value="Afar">Afar</option>
                        <option value="Benishangul-Gumuz">Benishangul-Gumuz</option>
                        <option value="Gambela">Gambela</option>
                        <option value="Harari">Harari</option>
                        <option value="Dire Dawa">Dire Dawa</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Signature</label>
                      <input
                        type="text"
                        value={formData.signature}
                        onChange={(e) => handleInputChange('signature', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter signature"
                      />
                    </div>
                  </div>

                  {/* Feeder and Location */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Feeder Name</label>
                      <input
                        type="text"
                        value={formData.feederName}
                        onChange={(e) => handleInputChange('feederName', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter feeder name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">District Name</label>
                      <input
                        type="text"
                        value={formData.district}
                        onChange={(e) => handleInputChange('district', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter district name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Specific Location</label>
                      <input
                        type="text"
                        value={formData.specificLocation}
                        onChange={(e) => handleInputChange('specificLocation', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter specific location"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">GPS Location</label>
                      <input
                        type="text"
                        value={formData.gpsLocation}
                        onChange={(e) => handleInputChange('gpsLocation', e.target.value)}
                        className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 ${
                          formErrors.gpsLocation
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-green-500'
                        }`}
                        placeholder="lat,lng (e.g., 9.0192,38.7525)"
                      />
                      {formErrors.gpsLocation && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.gpsLocation}</p>
                      )}
                    </div>
                  </div>

                  {/* Transformer Specifications */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Transformer Code <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.transformerCode}
                        onChange={(e) => handleInputChange('transformerCode', e.target.value)}
                        className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 ${
                          formErrors.transformerCode
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-green-500'
                        }`}
                        placeholder="Enter transformer code"
                      />
                      {formErrors.transformerCode && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.transformerCode}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">
                        KVA Rating <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        value={formData.kvaRating}
                        onChange={(e) => handleInputChange('kvaRating', e.target.value)}
                        className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 ${
                          formErrors.kvaRating
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-green-500'
                        }`}
                        placeholder="Enter KVA rating"
                      />
                      {formErrors.kvaRating && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.kvaRating}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Primary Service Voltage</label>
                      <input
                        type="text"
                        value={formData.primaryServiceVoltage}
                        onChange={(e) => handleInputChange('primaryServiceVoltage', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="e.g., 15kV/400V"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Manufacturer</label>
                      <select
                        value={formData.manufacturer}
                        onChange={(e) => handleInputChange('manufacturer', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <option value="">Select Manufacturer</option>
                        <option value="ABB">ABB</option>
                        <option value="Siemens">Siemens</option>
                        <option value="Schneider Electric">Schneider Electric</option>
                        <option value="General Electric">General Electric</option>
                        <option value="Eaton">Eaton</option>
                        <option value="Hyundai">Hyundai</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                  </div>

                  {/* Manufacturing and Installation Details */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Year of Manufacturing</label>
                      <input
                        type="number"
                        value={formData.yearOfManufacturing}
                        onChange={(e) => handleInputChange('yearOfManufacturing', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="e.g., 2023"
                        min="1900"
                        max={new Date().getFullYear()}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Serial No./Tag No.</label>
                      <input
                        type="text"
                        value={formData.serialNo}
                        onChange={(e) => handleInputChange('serialNo', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter serial number"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Installation Date</label>
                      <input
                        type="date"
                        value={formData.installationDate}
                        onChange={(e) => handleInputChange('installationDate', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Changing Date</label>
                      <input
                        type="date"
                        value={formData.changingDate}
                        onChange={(e) => handleInputChange('changingDate', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      />
                    </div>
                  </div>

                  {/* Customer Information */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Customer Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Customer Type</label>
                        <select
                          value={formData.customerType}
                          onChange={(e) => handleInputChange('customerType', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        >
                          <option value="">Select Customer Type</option>
                          <option value="Residential">Residential</option>
                          <option value="Commercial">Commercial</option>
                          <option value="Industrial">Industrial</option>
                          <option value="Government">Government</option>
                          <option value="Agricultural">Agricultural</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">Name of Customer</label>
                        <input
                          type="text"
                          value={formData.customerName}
                          onChange={(e) => handleInputChange('customerName', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="Enter customer name"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">Sub City</label>
                        <input
                          type="text"
                          value={formData.subCity}
                          onChange={(e) => handleInputChange('subCity', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="Enter sub city"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">Kebele</label>
                        <input
                          type="text"
                          value={formData.kebele}
                          onChange={(e) => handleInputChange('kebele', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="Enter kebele"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Construction Type */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Construction Type</h3>
                    <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                      {Object.entries(formData.constructionType).map(([key, value]) => (
                        <div key={key} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={key}
                            checked={value}
                            onChange={(e) => handleNestedChange('constructionType', key, e.target.checked)}
                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                          />
                          <label htmlFor={key} className="text-sm font-medium capitalize">
                            {key}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Delivery Information */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Delivery Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Delivery Date to Electric Workshop</label>
                        <input
                          type="date"
                          value={formData.deliveryDate}
                          onChange={(e) => handleInputChange('deliveryDate', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">Delivered by</label>
                        <input
                          type="text"
                          value={formData.workshopDelivered}
                          onChange={(e) => handleInputChange('workshopDelivered', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="Enter deliverer name"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">Signature</label>
                        <input
                          type="text"
                          value={formData.signatureDelivery}
                          onChange={(e) => handleInputChange('signatureDelivery', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="Enter signature"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Changing Reason */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Additional Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Changing Reason</label>
                        <textarea
                          value={formData.changingReason}
                          onChange={(e) => handleInputChange('changingReason', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="Enter reason for changing transformer"
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="inspection" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ClipboardList className="h-5 w-5" />
                    Component Inspection for Quantity of Defective Network Components
                  </CardTitle>
                  <CardDescription>
                    Detailed inspection checklist for transformer components
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border border-gray-300 px-4 py-3 text-left font-medium">Component</th>
                          <th className="border border-gray-300 px-4 py-3 text-left font-medium">Inspection For</th>
                          <th className="border border-gray-300 px-4 py-3 text-center font-medium">Defective</th>
                          <th className="border border-gray-300 px-4 py-3 text-center font-medium">Trafo Code</th>
                          <th className="border border-gray-300 px-4 py-3 text-center font-medium">Qty</th>
                        </tr>
                      </thead>
                      <tbody>
                        {/* Earth Resistance */}
                        <tr>
                          <td className="border border-gray-300 px-4 py-3 font-medium" rowSpan={2}>
                            Distribution Transformers
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            Earth resistance beyond the acceptable value
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={formData.componentInspections.earthResistance.defective}
                              onChange={(e) => handleComponentChange('earthResistance', 'defective', e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="text"
                              value={formData.componentInspections.earthResistance.trafoCode}
                              onChange={(e) => handleComponentChange('earthResistance', 'trafoCode', e.target.value)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              placeholder="Code"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="number"
                              value={formData.componentInspections.earthResistance.qty}
                              onChange={(e) => handleComponentChange('earthResistance', 'qty', parseInt(e.target.value) || 0)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              min="0"
                            />
                          </td>
                        </tr>

                        {/* Insulation Resistance */}
                        <tr>
                          <td className="border border-gray-300 px-4 py-3">
                            Insulation resistance below the acceptable value
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={formData.componentInspections.insulationResistance.defective}
                              onChange={(e) => handleComponentChange('insulationResistance', 'defective', e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="text"
                              value={formData.componentInspections.insulationResistance.trafoCode}
                              onChange={(e) => handleComponentChange('insulationResistance', 'trafoCode', e.target.value)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              placeholder="Code"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="number"
                              value={formData.componentInspections.insulationResistance.qty}
                              onChange={(e) => handleComponentChange('insulationResistance', 'qty', parseInt(e.target.value) || 0)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              min="0"
                            />
                          </td>
                        </tr>

                        {/* Oil Level */}
                        <tr>
                          <td className="border border-gray-300 px-4 py-3 font-medium" rowSpan={8}>
                            Distribution Transformers
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            Oil level below the normal value
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={formData.componentInspections.oilLevel.defective}
                              onChange={(e) => handleComponentChange('oilLevel', 'defective', e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="text"
                              value={formData.componentInspections.oilLevel.trafoCode}
                              onChange={(e) => handleComponentChange('oilLevel', 'trafoCode', e.target.value)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              placeholder="Code"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="number"
                              value={formData.componentInspections.oilLevel.qty}
                              onChange={(e) => handleComponentChange('oilLevel', 'qty', parseInt(e.target.value) || 0)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              min="0"
                            />
                          </td>
                        </tr>

                        {/* Oil Temperature */}
                        <tr>
                          <td className="border border-gray-300 px-4 py-3">
                            Temperature of oil is above the acceptable value
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={formData.componentInspections.oilTemperature.defective}
                              onChange={(e) => handleComponentChange('oilTemperature', 'defective', e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="text"
                              value={formData.componentInspections.oilTemperature.trafoCode}
                              onChange={(e) => handleComponentChange('oilTemperature', 'trafoCode', e.target.value)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              placeholder="Code"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="number"
                              value={formData.componentInspections.oilTemperature.qty}
                              onChange={(e) => handleComponentChange('oilTemperature', 'qty', parseInt(e.target.value) || 0)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              min="0"
                            />
                          </td>
                        </tr>

                        {/* Unusual Noises */}
                        <tr>
                          <td className="border border-gray-300 px-4 py-3">
                            Un usual noises
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={formData.componentInspections.unusualNoises.defective}
                              onChange={(e) => handleComponentChange('unusualNoises', 'defective', e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="text"
                              value={formData.componentInspections.unusualNoises.trafoCode}
                              onChange={(e) => handleComponentChange('unusualNoises', 'trafoCode', e.target.value)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              placeholder="Code"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="number"
                              value={formData.componentInspections.unusualNoises.qty}
                              onChange={(e) => handleComponentChange('unusualNoises', 'qty', parseInt(e.target.value) || 0)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              min="0"
                            />
                          </td>
                        </tr>

                        {/* Misaligned Bushing */}
                        <tr>
                          <td className="border border-gray-300 px-4 py-3">
                            Misaligned bushing are horn gap
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={formData.componentInspections.misalignedBushing.defective}
                              onChange={(e) => handleComponentChange('misalignedBushing', 'defective', e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="text"
                              value={formData.componentInspections.misalignedBushing.trafoCode}
                              onChange={(e) => handleComponentChange('misalignedBushing', 'trafoCode', e.target.value)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              placeholder="Code"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="number"
                              value={formData.componentInspections.misalignedBushing.qty}
                              onChange={(e) => handleComponentChange('misalignedBushing', 'qty', parseInt(e.target.value) || 0)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              min="0"
                            />
                          </td>
                        </tr>

                        {/* Changed Silica Gel Color */}
                        <tr>
                          <td className="border border-gray-300 px-4 py-3">
                            Changed silica gel colour
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={formData.componentInspections.silicaGelColor.defective}
                              onChange={(e) => handleComponentChange('silicaGelColor', 'defective', e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="text"
                              value={formData.componentInspections.silicaGelColor.trafoCode}
                              onChange={(e) => handleComponentChange('silicaGelColor', 'trafoCode', e.target.value)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              placeholder="Code"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="number"
                              value={formData.componentInspections.silicaGelColor.qty}
                              onChange={(e) => handleComponentChange('silicaGelColor', 'qty', parseInt(e.target.value) || 0)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              min="0"
                            />
                          </td>
                        </tr>

                        {/* Lightning Arrestor */}
                        <tr>
                          <td className="border border-gray-300 px-4 py-3">
                            Cracked or damaged lighting arrestor
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={formData.componentInspections.lightningArrestor.defective}
                              onChange={(e) => handleComponentChange('lightningArrestor', 'defective', e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="text"
                              value={formData.componentInspections.lightningArrestor.trafoCode}
                              onChange={(e) => handleComponentChange('lightningArrestor', 'trafoCode', e.target.value)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              placeholder="Code"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="number"
                              value={formData.componentInspections.lightningArrestor.qty}
                              onChange={(e) => handleComponentChange('lightningArrestor', 'qty', parseInt(e.target.value) || 0)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              min="0"
                            />
                          </td>
                        </tr>

                        {/* Pole Condition */}
                        <tr>
                          <td className="border border-gray-300 px-4 py-3">
                            Check for pole condition & proper supporting and level of the transformer
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={formData.componentInspections.poleCondition.defective}
                              onChange={(e) => handleComponentChange('poleCondition', 'defective', e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="text"
                              value={formData.componentInspections.poleCondition.trafoCode}
                              onChange={(e) => handleComponentChange('poleCondition', 'trafoCode', e.target.value)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              placeholder="Code"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="number"
                              value={formData.componentInspections.poleCondition.qty}
                              onChange={(e) => handleComponentChange('poleCondition', 'qty', parseInt(e.target.value) || 0)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              min="0"
                            />
                          </td>
                        </tr>

                        {/* Connection Tightness */}
                        <tr>
                          <td className="border border-gray-300 px-4 py-3">
                            Check for tightness of connections including transformer base
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center">
                            <input
                              type="checkbox"
                              checked={formData.componentInspections.connectionTightness.defective}
                              onChange={(e) => handleComponentChange('connectionTightness', 'defective', e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="text"
                              value={formData.componentInspections.connectionTightness.trafoCode}
                              onChange={(e) => handleComponentChange('connectionTightness', 'trafoCode', e.target.value)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              placeholder="Code"
                            />
                          </td>
                          <td className="border border-gray-300 px-4 py-3">
                            <input
                              type="number"
                              value={formData.componentInspections.connectionTightness.qty}
                              onChange={(e) => handleComponentChange('connectionTightness', 'qty', parseInt(e.target.value) || 0)}
                              className="w-full border-0 focus:outline-none focus:ring-1 focus:ring-green-500 rounded px-2 py-1"
                              min="0"
                            />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="megger-test" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TestTube className="h-5 w-5" />
                    Damaged Transformer With Its Test Results
                  </CardTitle>
                  <CardDescription>
                    Megger test measurements and electrical testing results
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Basic Test Information */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Region</label>
                      <input
                        type="text"
                        value={formData.meggerTest.region}
                        onChange={(e) => handleNestedChange('meggerTest', 'region', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter region"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">District/CS</label>
                      <input
                        type="text"
                        value={formData.meggerTest.district}
                        onChange={(e) => handleNestedChange('meggerTest', 'district', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter district"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Type of Service</label>
                      <select
                        value={formData.meggerTest.serviceType}
                        onChange={(e) => handleNestedChange('meggerTest', 'serviceType', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <option value="">Select Service Type</option>
                        <option value="Maintenance">Maintenance</option>
                        <option value="Repair">Repair</option>
                        <option value="Installation">Installation</option>
                        <option value="Testing">Testing</option>
                        <option value="Inspection">Inspection</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Work Order No.</label>
                      <input
                        type="text"
                        value={formData.meggerTest.workOrderNo}
                        onChange={(e) => handleNestedChange('meggerTest', 'workOrderNo', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter work order number"
                      />
                    </div>
                  </div>

                  {/* Location and Transformer Details */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Specific Location</label>
                      <input
                        type="text"
                        value={formData.meggerTest.specificLocation}
                        onChange={(e) => handleNestedChange('meggerTest', 'specificLocation', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter specific location"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">GPS Location</label>
                      <input
                        type="text"
                        value={formData.meggerTest.gpsLocation}
                        onChange={(e) => handleNestedChange('meggerTest', 'gpsLocation', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="lat,lng"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Manufacturer Type</label>
                      <input
                        type="text"
                        value={formData.meggerTest.manufacturer}
                        onChange={(e) => handleNestedChange('meggerTest', 'manufacturer', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter manufacturer"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Capacity/KVA</label>
                      <input
                        type="number"
                        value={formData.meggerTest.capacityKVA}
                        onChange={(e) => handleNestedChange('meggerTest', 'capacityKVA', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter capacity"
                      />
                    </div>
                  </div>

                  {/* Serial Number and Fuse Information */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Serial No.</label>
                      <input
                        type="text"
                        value={formData.meggerTest.serialNo}
                        onChange={(e) => handleNestedChange('meggerTest', 'serialNo', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter serial number"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Box One - Cable Size</label>
                      <input
                        type="text"
                        value={formData.meggerTest.boxOne.cableSize}
                        onChange={(e) => handleNestedChange('meggerTest', 'boxOne', { ...formData.meggerTest.boxOne, cableSize: e.target.value })}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Cable size"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Box One - Fuse Rating</label>
                      <input
                        type="text"
                        value={formData.meggerTest.boxOne.fuseRating}
                        onChange={(e) => handleNestedChange('meggerTest', 'boxOne', { ...formData.meggerTest.boxOne, fuseRating: e.target.value })}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Fuse rating"
                      />
                    </div>
                  </div>

                  {/* Box Two and Additional Measurements */}
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Box Two - Cable Size</label>
                      <input
                        type="text"
                        value={formData.meggerTest.boxTwo.cableSize}
                        onChange={(e) => handleNestedChange('meggerTest', 'boxTwo', { ...formData.meggerTest.boxTwo, cableSize: e.target.value })}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Cable size"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Box Two - Fuse Rating</label>
                      <input
                        type="text"
                        value={formData.meggerTest.boxTwo.fuseRating}
                        onChange={(e) => handleNestedChange('meggerTest', 'boxTwo', { ...formData.meggerTest.boxTwo, fuseRating: e.target.value })}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Fuse rating"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Linear Rating</label>
                      <input
                        type="text"
                        value={formData.meggerTest.linearRating}
                        onChange={(e) => handleNestedChange('meggerTest', 'linearRating', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Linear rating"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Oil Level</label>
                      <select
                        value={formData.meggerTest.oilLevel}
                        onChange={(e) => handleNestedChange('meggerTest', 'oilLevel', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <option value="">Select Level</option>
                        <option value="Normal">Normal</option>
                        <option value="Low">Low</option>
                        <option value="High">High</option>
                        <option value="Critical">Critical</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Ground Connection</label>
                      <select
                        value={formData.meggerTest.groundConnection}
                        onChange={(e) => handleNestedChange('meggerTest', 'groundConnection', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <option value="">Select Status</option>
                        <option value="Good">Good</option>
                        <option value="Fair">Fair</option>
                        <option value="Poor">Poor</option>
                        <option value="Disconnected">Disconnected</option>
                      </select>
                    </div>
                  </div>

                  {/* Test Results Section */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                      <TestTube className="h-5 w-5" />
                      Test Results (Megger)
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">R-S (Ω)</label>
                        <input
                          type="number"
                          step="0.01"
                          value={formData.meggerTest.testResults.rs}
                          onChange={(e) => handleNestedChange('meggerTest', 'testResults', { ...formData.meggerTest.testResults, rs: e.target.value })}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="0.00"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">S-T (Ω)</label>
                        <input
                          type="number"
                          step="0.01"
                          value={formData.meggerTest.testResults.st}
                          onChange={(e) => handleNestedChange('meggerTest', 'testResults', { ...formData.meggerTest.testResults, st: e.target.value })}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="0.00"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">T-R (Ω)</label>
                        <input
                          type="number"
                          step="0.01"
                          value={formData.meggerTest.testResults.tr}
                          onChange={(e) => handleNestedChange('meggerTest', 'testResults', { ...formData.meggerTest.testResults, tr: e.target.value })}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="0.00"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Ohmic Value</label>
                        <input
                          type="number"
                          step="0.01"
                          value={formData.meggerTest.ohmicValue}
                          onChange={(e) => handleNestedChange('meggerTest', 'ohmicValue', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="0.00"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">HT to Ground</label>
                        <input
                          type="number"
                          step="0.01"
                          value={formData.meggerTest.htToGround}
                          onChange={(e) => handleNestedChange('meggerTest', 'htToGround', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="0.00"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">LT to Ground</label>
                        <input
                          type="number"
                          step="0.01"
                          value={formData.meggerTest.ltToGround}
                          onChange={(e) => handleNestedChange('meggerTest', 'ltToGround', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="0.00"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">Oil Insulation & Oil Level</label>
                        <input
                          type="text"
                          value={formData.meggerTest.oilInsulation}
                          onChange={(e) => handleNestedChange('meggerTest', 'oilInsulation', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="Enter oil condition"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Silica Gel and Remarks */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Silica Gel Condition</label>
                      <select
                        value={formData.meggerTest.silicaGel}
                        onChange={(e) => handleNestedChange('meggerTest', 'silicaGel', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <option value="">Select Condition</option>
                        <option value="Blue">Blue (Good)</option>
                        <option value="Pink">Pink (Needs Change)</option>
                        <option value="White">White (Saturated)</option>
                        <option value="Mixed">Mixed Colors</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Isolatin Link or Drop out Fuse</label>
                      <select
                        value={formData.meggerTest.isolationLink}
                        onChange={(e) => handleNestedChange('meggerTest', 'isolationLink', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <option value="">Select Status</option>
                        <option value="Good">Good</option>
                        <option value="Damaged">Damaged</option>
                        <option value="Missing">Missing</option>
                        <option value="Needs Replacement">Needs Replacement</option>
                      </select>
                    </div>
                  </div>

                  {/* Remarks and Conclusion */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Test Results & Conclusion</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Remark or Final Result or Conclusion</label>
                        <textarea
                          value={formData.meggerTest.remarks}
                          onChange={(e) => handleNestedChange('meggerTest', 'remarks', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="Enter test remarks and conclusions"
                          rows={4}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-1">Type of Fault &/or Conclusion</label>
                        <textarea
                          value={formData.meggerTest.faultType}
                          onChange={(e) => handleNestedChange('meggerTest', 'faultType', e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                          placeholder="Describe fault type or final conclusion"
                          rows={4}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Approval Chain */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Approval Chain</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* Inspected By */}
                      <div className="border rounded-lg p-4 bg-gray-50">
                        <h4 className="font-medium mb-3 text-center">Inspected by</h4>
                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium mb-1">Name</label>
                            <input
                              type="text"
                              value={formData.meggerTest.inspectedBy.name}
                              onChange={(e) => handleNestedChange('meggerTest', 'inspectedBy', {
                                ...formData.meggerTest.inspectedBy,
                                name: e.target.value
                              })}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                              placeholder="Inspector name"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-1">Signature</label>
                            <input
                              type="text"
                              value={formData.meggerTest.inspectedBy.signature}
                              onChange={(e) => handleNestedChange('meggerTest', 'inspectedBy', {
                                ...formData.meggerTest.inspectedBy,
                                signature: e.target.value
                              })}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                              placeholder="Signature"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-1">Date</label>
                            <input
                              type="date"
                              value={formData.meggerTest.inspectedBy.date}
                              onChange={(e) => handleNestedChange('meggerTest', 'inspectedBy', {
                                ...formData.meggerTest.inspectedBy,
                                date: e.target.value
                              })}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Checked By */}
                      <div className="border rounded-lg p-4 bg-blue-50">
                        <h4 className="font-medium mb-3 text-center">Checked by</h4>
                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium mb-1">Name</label>
                            <input
                              type="text"
                              value={formData.meggerTest.checkedBy.name}
                              onChange={(e) => handleNestedChange('meggerTest', 'checkedBy', {
                                ...formData.meggerTest.checkedBy,
                                name: e.target.value
                              })}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                              placeholder="Checker name"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-1">Signature</label>
                            <input
                              type="text"
                              value={formData.meggerTest.checkedBy.signature}
                              onChange={(e) => handleNestedChange('meggerTest', 'checkedBy', {
                                ...formData.meggerTest.checkedBy,
                                signature: e.target.value
                              })}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                              placeholder="Signature"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-1">Date</label>
                            <input
                              type="date"
                              value={formData.meggerTest.checkedBy.date}
                              onChange={(e) => handleNestedChange('meggerTest', 'checkedBy', {
                                ...formData.meggerTest.checkedBy,
                                date: e.target.value
                              })}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Approved By */}
                      <div className="border rounded-lg p-4 bg-green-50">
                        <h4 className="font-medium mb-3 text-center">Approved by</h4>
                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium mb-1">Name</label>
                            <input
                              type="text"
                              value={formData.meggerTest.approvedBy.name}
                              onChange={(e) => handleNestedChange('meggerTest', 'approvedBy', {
                                ...formData.meggerTest.approvedBy,
                                name: e.target.value
                              })}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                              placeholder="Approver name"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-1">Signature</label>
                            <input
                              type="text"
                              value={formData.meggerTest.approvedBy.signature}
                              onChange={(e) => handleNestedChange('meggerTest', 'approvedBy', {
                                ...formData.meggerTest.approvedBy,
                                signature: e.target.value
                              })}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                              placeholder="Signature"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-1">Date</label>
                            <input
                              type="date"
                              value={formData.meggerTest.approvedBy.date}
                              onChange={(e) => handleNestedChange('meggerTest', 'approvedBy', {
                                ...formData.meggerTest.approvedBy,
                                date: e.target.value
                              })}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Note */}
                  <div className="border-t pt-6">
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <p className="text-sm text-yellow-800 font-medium">
                        <strong>NB:</strong> This should be attached with the history card of the transformer and technical inspection/enforcement/report
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Save and Action Buttons */}
            <div className="flex justify-between items-center pt-6 border-t">
              <div className="flex items-center space-x-2">
                <Badge variant={validateForm() ? "default" : "destructive"}>
                  {validateForm() ? "Form Valid" : "Form Invalid"}
                </Badge>
                {Object.keys(formErrors).length > 0 && (
                  <span className="text-sm text-red-600">
                    {Object.keys(formErrors).length} error(s) found
                  </span>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    if (validateForm()) {
                      // Handle save logic here
                      console.log('Saving unified transformer data:', formData)
                      toast({
                        title: 'Success',
                        description: 'Transformer data saved successfully!',
                      })
                    } else {
                      toast({
                        variant: 'destructive',
                        title: 'Validation Error',
                        description: 'Please fix the form errors before saving.',
                      })
                    }
                  }}
                  disabled={isSubmitting}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSubmitting ? 'Saving...' : 'Save All Data'}
                </Button>
              </div>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
