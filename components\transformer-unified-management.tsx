'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import { Input } from '@/src/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/src/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/src/components/ui/table'
import { Checkbox } from '@/src/components/ui/checkbox'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/src/components/ui/dialog'
import { Label } from '@/src/components/ui/label'
import { Textarea } from '@/src/components/ui/textarea'
import {
  Search,
  Filter,
  RefreshCw,
  Download,
  Edit,
  Trash2,
  Plus,
  <PERSON>tings,
  Zap,
  MapPin,
  Calendar,
  Alert<PERSON>riangle,
  CheckCircle,
  Clock,
  MoreHorizontal
} from 'lucide-react'
import { toast } from 'sonner'

interface Transformer {
  id: number
  name: string
  serial_number: string
  status: string
  location: string
  kva_rating: number
  voltage_primary: number
  voltage_secondary: number
  load_percentage: number
  efficiency: number
  region_name: string
  service_center_name: string
  transformer_name: string
  next_maintenance: string
  last_maintenance: string
  active_alerts: number
  uptime_percentage: number
  efficiency_rating: number
  health_score: number
}

interface TransformerData {
  transformers: Transformer[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  statistics: {
    total: number
    operational: number
    maintenance: number
    faulty: number
    offline: number
    avg_load: number
    avg_efficiency: number
  }
}

export default function TransformerUnifiedManagement() {
  const [data, setData] = useState<TransformerData | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [selectedTransformers, setSelectedTransformers] = useState<number[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [regionFilter, setRegionFilter] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [showMaintenanceDialog, setShowMaintenanceDialog] = useState(false)
  const [selectedTransformer, setSelectedTransformer] = useState<Transformer | null>(null)

  const fetchTransformers = async () => {
    try {
      setRefreshing(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        search: searchQuery,
        status: statusFilter,
        region: regionFilter
      })

      const response = await fetch(`/api/transformers/unified-management?${params}`)
      const result = await response.json()

      if (result.success) {
        setData(result.data)
      } else {
        toast.error('Failed to fetch transformer data')
      }
    } catch (error) {
      console.error('Error fetching transformers:', error)
      toast.error('Error loading transformer data')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = async () => {
    await fetchTransformers()
    toast.success('Transformer data refreshed')
  }

  const handleStatusUpdate = async (transformerId: number, newStatus: string) => {
    try {
      const response = await fetch('/api/transformers/unified-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update_status',
          transformerId,
          data: { status: newStatus }
        })
      })

      const result = await response.json()
      if (result.success) {
        toast.success('Transformer status updated')
        await fetchTransformers()
      } else {
        toast.error('Failed to update status')
      }
    } catch (error) {
      toast.error('Error updating transformer status')
    }
  }

  const handleScheduleMaintenance = async (transformerId: number, maintenanceData: any) => {
    try {
      const response = await fetch('/api/transformers/unified-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'schedule_maintenance',
          transformerId,
          data: maintenanceData
        })
      })

      const result = await response.json()
      if (result.success) {
        toast.success('Maintenance scheduled successfully')
        setShowMaintenanceDialog(false)
        await fetchTransformers()
      } else {
        toast.error('Failed to schedule maintenance')
      }
    } catch (error) {
      toast.error('Error scheduling maintenance')
    }
  }

  const handleBulkAction = async (action: string, actionData: any) => {
    try {
      const response = await fetch('/api/transformers/unified-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'bulk_action',
          data: {
            transformerIds: selectedTransformers,
            bulkAction: action,
            bulkData: actionData
          }
        })
      })

      const result = await response.json()
      if (result.success) {
        toast.success(`Bulk ${action} completed successfully`)
        setSelectedTransformers([])
        setShowBulkActions(false)
        await fetchTransformers()
      } else {
        toast.error(`Failed to perform bulk ${action}`)
      }
    } catch (error) {
      toast.error(`Error performing bulk ${action}`)
    }
  }

  const handleExport = async () => {
    try {
      const response = await fetch('/api/transformers/unified-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'export_data' })
      })

      const result = await response.json()
      if (result.success) {
        // Create and download CSV
        const csvContent = [
          ['Name', 'Serial Number', 'Status', 'Location', 'KVA Rating', 'Load %', 'Efficiency %', 'Region'],
          ...result.data.map((t: any) => [
            t.name, t.serial_number, t.status, t.location,
            t.kva_rating, t.load_percentage, t.efficiency, t.region
          ])
        ].map(row => row.join(',')).join('\n')

        const blob = new Blob([csvContent], { type: 'text/csv' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'transformers_export.csv'
        a.click()
        window.URL.revokeObjectURL(url)

        toast.success('Data exported successfully')
      }
    } catch (error) {
      toast.error('Failed to export data')
    }
  }

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      operational: "secondary",
      maintenance: "outline",
      faulty: "destructive",
      offline: "destructive"
    }
    return <Badge variant={variants[status] || "default"}>{status}</Badge>
  }

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  useEffect(() => {
    fetchTransformers()
  }, [currentPage, searchQuery, statusFilter, regionFilter])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No transformer data available</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Transformer Unified Management</h1>
          <p className="text-muted-foreground">
            Comprehensive transformer monitoring and management system
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Transformer
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.statistics.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Operational</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.statistics.operational}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((data.statistics.operational / data.statistics.total) * 100)}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance</CardTitle>
            <Settings className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.statistics.maintenance}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Faulty</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.statistics.faulty}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Load</CardTitle>
            <Zap className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(data.statistics.avg_load)}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters & Search</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search transformers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Statuses</SelectItem>
                  <SelectItem value="operational">Operational</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                  <SelectItem value="faulty">Faulty</SelectItem>
                  <SelectItem value="offline">Offline</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Region</Label>
              <Select value={regionFilter} onValueChange={setRegionFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Regions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Regions</SelectItem>
                  <SelectItem value="1">Addis Ababa</SelectItem>
                  <SelectItem value="2">Oromia</SelectItem>
                  <SelectItem value="3">Amhara</SelectItem>
                  <SelectItem value="4">SNNPR</SelectItem>
                  <SelectItem value="5">Tigray</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Actions</Label>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchQuery('')
                    setStatusFilter('')
                    setRegionFilter('')
                  }}
                >
                  <Filter className="mr-2 h-4 w-4" />
                  Clear
                </Button>
                {selectedTransformers.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowBulkActions(!showBulkActions)}
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Bulk Actions
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions Panel */}
      {showBulkActions && selectedTransformers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Bulk Actions ({selectedTransformers.length} selected)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('update_status', { status: 'maintenance' })}
              >
                Set to Maintenance
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('schedule_maintenance', {
                  scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                  maintenanceType: 'preventive',
                  priority: 'medium',
                  description: 'Bulk scheduled maintenance'
                })}
              >
                Schedule Maintenance
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedTransformers([])}
              >
                Clear Selection
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transformers Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Transformers</CardTitle>
          <CardDescription>
            {data.pagination.total} total transformers, showing page {data.pagination.page} of {data.pagination.totalPages}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedTransformers.length === data.transformers.length}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedTransformers(data.transformers.map(t => t.id))
                      } else {
                        setSelectedTransformers([])
                      }
                    }}
                  />
                </TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Load</TableHead>
                <TableHead>Health</TableHead>
                <TableHead>Alerts</TableHead>
                <TableHead>Last Maintenance</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.transformers.map((transformer) => (
                <TableRow key={transformer.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedTransformers.includes(transformer.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedTransformers([...selectedTransformers, transformer.id])
                        } else {
                          setSelectedTransformers(selectedTransformers.filter(id => id !== transformer.id))
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{transformer.name}</div>
                      <div className="text-sm text-muted-foreground">{transformer.serial_number}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(transformer.status)}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      <span className="text-sm">{transformer.location}</span>
                    </div>
                    <div className="text-xs text-muted-foreground">{transformer.region_name}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="text-sm font-medium">{transformer.load_percentage}%</div>
                      <div className={`text-xs ${transformer.load_percentage > 90 ? 'text-red-600' : transformer.load_percentage > 70 ? 'text-yellow-600' : 'text-green-600'}`}>
                        {transformer.load_percentage > 90 ? 'High' : transformer.load_percentage > 70 ? 'Medium' : 'Normal'}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className={`text-sm font-medium ${getHealthScoreColor(transformer.health_score)}`}>
                      {transformer.health_score}%
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {transformer.uptime_percentage}% uptime
                    </div>
                  </TableCell>
                  <TableCell>
                    {transformer.active_alerts > 0 ? (
                      <Badge variant="destructive">{transformer.active_alerts}</Badge>
                    ) : (
                      <Badge variant="secondary">0</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <span className="text-sm">{transformer.last_maintenance}</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Next: {transformer.next_maintenance}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedTransformer(transformer)
                          setShowMaintenanceDialog(true)
                        }}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          // Navigate to transformer details
                          window.location.href = `/transformers/${transformer.id}`
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {((data.pagination.page - 1) * data.pagination.limit) + 1} to {Math.min(data.pagination.page * data.pagination.limit, data.pagination.total)} of {data.pagination.total} transformers
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {data.pagination.page} of {data.pagination.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(data.pagination.totalPages, currentPage + 1))}
                disabled={currentPage === data.pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Schedule Maintenance Dialog */}
      <Dialog open={showMaintenanceDialog} onOpenChange={setShowMaintenanceDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Schedule Maintenance</DialogTitle>
            <DialogDescription>
              Schedule maintenance for {selectedTransformer?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Maintenance Type</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="preventive">Preventive</SelectItem>
                  <SelectItem value="corrective">Corrective</SelectItem>
                  <SelectItem value="predictive">Predictive</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Priority</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Scheduled Date</Label>
              <Input type="date" />
            </div>
            <div className="space-y-2">
              <Label>Description</Label>
              <Textarea placeholder="Maintenance description..." />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowMaintenanceDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => {
                // Handle maintenance scheduling
                setShowMaintenanceDialog(false)
                toast.success('Maintenance scheduled successfully')
              }}>
                Schedule
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
