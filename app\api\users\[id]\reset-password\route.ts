import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'

// Mock users data - in a real app this would be from database
const mockUsers = [
  {
    id: 1,
    employeeId: 'EEU001',
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    status: 'active'
  },
  {
    id: 2,
    employeeId: 'EEU002',
    firstName: 'Jane',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    status: 'active'
  }
]

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = parseInt(params.id)

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      )
    }

    const user = mockUsers.find(u => u.id === userId)

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    if (user.status !== 'active') {
      return NextResponse.json(
        { success: false, error: 'Cannot reset password for inactive user' },
        { status: 400 }
      )
    }

    // Generate reset token (in a real app, this would be saved to database with expiry)
    const resetToken = crypto.randomBytes(32).toString('hex')
    const resetExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

    // In a real application, you would:
    // 1. Save the reset token and expiry to the database
    // 2. Send an email with the reset link
    // 3. Log the password reset request

    console.log(`Password reset requested for user ${userId} (${user.email})`)
    console.log(`Reset token: ${resetToken}`)
    console.log(`Reset expires: ${resetExpiry.toISOString()}`)

    // Simulate sending email
    const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}`
    
    // Mock email content
    const emailContent = {
      to: user.email,
      subject: 'Password Reset Request - EEU-DTMS',
      body: `
        Dear ${user.firstName} ${user.lastName},
        
        A password reset has been requested for your EEU-DTMS account.
        
        Please click the following link to reset your password:
        ${resetLink}
        
        This link will expire in 24 hours.
        
        If you did not request this password reset, please ignore this email.
        
        Best regards,
        EEU-DTMS System
      `
    }

    console.log('Email would be sent:', emailContent)

    return NextResponse.json({
      success: true,
      data: {
        userId,
        email: user.email,
        resetTokenSent: true,
        expiresAt: resetExpiry.toISOString()
      },
      message: 'Password reset email sent successfully'
    })
  } catch (error) {
    console.error('Error processing password reset:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to process password reset' },
      { status: 500 }
    )
  }
}
