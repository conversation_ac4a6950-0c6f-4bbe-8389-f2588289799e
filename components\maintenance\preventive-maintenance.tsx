"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Progress } from "@/src/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/src/components/ui/table"
import {
  Calendar,
  Clock,
  Shield,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  BarChart3,
  Settings,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  MapPin,
  User,
  Wrench,
  Activity
} from 'lucide-react'
import { format, addMonths, differenceInDays } from 'date-fns'
import { maintenanceDatabaseService } from '@/src/services/maintenance-database-service'

interface PreventiveMaintenanceSchedule {
  id: string
  transformerId: string
  transformerLocation: string
  maintenanceType: string
  frequency: 'monthly' | 'quarterly' | 'semi-annual' | 'annual'
  lastPerformed: string
  nextDue: string
  status: 'current' | 'due' | 'overdue'
  assignedTechnician: string
  estimatedDuration: number
  priority: 'low' | 'medium' | 'high'
  checklist: {
    id: string
    task: string
    completed: boolean
    notes?: string
  }[]
}

interface PreventiveMaintenanceProps {
  className?: string
}

export function PreventiveMaintenance({ className }: PreventiveMaintenanceProps) {
  const [schedules, setSchedules] = useState<PreventiveMaintenanceSchedule[]>([])
  const [filteredSchedules, setFilteredSchedules] = useState<PreventiveMaintenanceSchedule[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    frequency: 'all'
  })

  // Mock data for development
  const mockSchedules: PreventiveMaintenanceSchedule[] = [
    {
      id: 'PM-001',
      transformerId: 'T-AA-001',
      transformerLocation: 'Addis Ababa - Sector 1',
      maintenanceType: 'Oil Analysis & Testing',
      frequency: 'quarterly',
      lastPerformed: '2024-01-15T10:00:00Z',
      nextDue: '2024-04-15T10:00:00Z',
      status: 'due',
      assignedTechnician: 'John Doe',
      estimatedDuration: 4,
      priority: 'high',
      checklist: [
        { id: '1', task: 'Visual inspection of transformer', completed: false },
        { id: '2', task: 'Oil sample collection', completed: false },
        { id: '3', task: 'Dissolved gas analysis', completed: false },
        { id: '4', task: 'Moisture content test', completed: false },
        { id: '5', task: 'Dielectric strength test', completed: false }
      ]
    },
    {
      id: 'PM-002',
      transformerId: 'T-OR-045',
      transformerLocation: 'Oromia - Industrial Zone',
      maintenanceType: 'Thermal Imaging Inspection',
      frequency: 'monthly',
      lastPerformed: '2024-02-01T09:00:00Z',
      nextDue: '2024-03-01T09:00:00Z',
      status: 'overdue',
      assignedTechnician: 'Jane Smith',
      estimatedDuration: 2,
      priority: 'high',
      checklist: [
        { id: '1', task: 'Thermal imaging of bushings', completed: false },
        { id: '2', task: 'Temperature monitoring', completed: false },
        { id: '3', task: 'Hot spot identification', completed: false },
        { id: '4', task: 'Report generation', completed: false }
      ]
    },
    {
      id: 'PM-003',
      transformerId: 'T-AM-023',
      transformerLocation: 'Amhara - Distribution Center',
      maintenanceType: 'Annual Comprehensive Inspection',
      frequency: 'annual',
      lastPerformed: '2023-12-10T08:00:00Z',
      nextDue: '2024-12-10T08:00:00Z',
      status: 'current',
      assignedTechnician: 'Bob Johnson',
      estimatedDuration: 8,
      priority: 'medium',
      checklist: [
        { id: '1', task: 'Complete visual inspection', completed: true },
        { id: '2', task: 'Electrical testing', completed: true },
        { id: '3', task: 'Oil analysis', completed: false },
        { id: '4', task: 'Cooling system check', completed: false },
        { id: '5', task: 'Protection system test', completed: false },
        { id: '6', task: 'Documentation update', completed: false }
      ]
    }
  ]

  // Load preventive maintenance schedules
  const loadSchedules = async () => {
    try {
      setIsLoading(true)
      // Fetch preventive maintenance records from database
      const maintenanceRecords = await maintenanceDatabaseService.getMaintenanceRecords({
        type: ['preventive']
      })

      // Convert to preventive maintenance schedules format
      const preventiveSchedules = maintenanceRecords.map(record => ({
        id: record.id,
        transformerId: record.transformerId,
        transformerLocation: `Location for ${record.transformerId}`,
        maintenanceType: record.title,
        frequency: 'quarterly' as const,
        lastPerformed: record.createdAt,
        nextDue: record.scheduledDate,
        status: record.status === 'scheduled' ? 'due' as const : 'current' as const,
        assignedTechnician: record.assignedTo,
        estimatedDuration: record.estimatedDuration || 4,
        priority: record.priority as 'low' | 'medium' | 'high',
        checklist: [
          { id: '1', task: 'Visual inspection of transformer', completed: false },
          { id: '2', task: 'Oil sample collection', completed: false },
          { id: '3', task: 'Performance testing', completed: false },
          { id: '4', task: 'Documentation update', completed: false }
        ]
      }))

      // Fallback to mock data if no records found
      const finalSchedules = preventiveSchedules.length > 0 ? preventiveSchedules : mockSchedules
      setSchedules(finalSchedules)
      setFilteredSchedules(finalSchedules)
    } catch (error) {
      console.error('Error loading preventive maintenance schedules:', error)
      // Use mock data as fallback
      setSchedules(mockSchedules)
      setFilteredSchedules(mockSchedules)
    } finally {
      setIsLoading(false)
    }
  }

  // Apply filters
  const applyFilters = () => {
    let filtered = [...schedules]

    // Search filter
    if (filters.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(schedule =>
        schedule.transformerId.toLowerCase().includes(search) ||
        schedule.maintenanceType.toLowerCase().includes(search) ||
        schedule.transformerLocation.toLowerCase().includes(search)
      )
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(schedule => schedule.status === filters.status)
    }

    // Frequency filter
    if (filters.frequency !== 'all') {
      filtered = filtered.filter(schedule => schedule.frequency === filters.frequency)
    }

    setFilteredSchedules(filtered)
  }

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current': return 'bg-green-100 text-green-800'
      case 'due': return 'bg-yellow-100 text-yellow-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'high': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get days until due
  const getDaysUntilDue = (nextDue: string) => {
    return differenceInDays(new Date(nextDue), new Date())
  }

  // Calculate completion percentage
  const getCompletionPercentage = (checklist: any[]) => {
    const completed = checklist.filter(item => item.completed).length
    return Math.round((completed / checklist.length) * 100)
  }

  // Handle preventive maintenance actions
  const handleStartMaintenance = async (scheduleId: string) => {
    try {
      // Create a new maintenance record from the schedule
      const schedule = schedules.find(s => s.id === scheduleId)
      if (!schedule) return

      await maintenanceDatabaseService.createMaintenanceRecord({
        transformerId: schedule.transformerId,
        type: 'preventive',
        title: schedule.maintenanceType,
        description: `Preventive maintenance: ${schedule.maintenanceType}`,
        scheduledDate: new Date().toISOString(),
        priority: schedule.priority,
        assignedTo: schedule.assignedTechnician,
        estimatedDuration: schedule.estimatedDuration
      })

      // Refresh schedules
      await loadSchedules()
    } catch (error) {
      console.error('Error starting preventive maintenance:', error)
    }
  }

  const handleRescheduleTask = async (scheduleId: string, newDate: string) => {
    try {
      // Update the schedule with new date
      setSchedules(prev => prev.map(schedule =>
        schedule.id === scheduleId
          ? { ...schedule, nextDue: newDate }
          : schedule
      ))
    } catch (error) {
      console.error('Error rescheduling task:', error)
    }
  }

  const handleCompleteChecklist = async (scheduleId: string, checklistId: string) => {
    try {
      // Update checklist item completion
      setSchedules(prev => prev.map(schedule =>
        schedule.id === scheduleId
          ? {
              ...schedule,
              checklist: schedule.checklist.map(item =>
                item.id === checklistId
                  ? { ...item, completed: !item.completed }
                  : item
              )
            }
          : schedule
      ))
    } catch (error) {
      console.error('Error updating checklist:', error)
    }
  }

  const handleCreateSchedule = async () => {
    // This would open a create schedule dialog
    console.log('Create new preventive maintenance schedule')
  }

  useEffect(() => {
    loadSchedules()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [filters, schedules])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Preventive Maintenance</h2>
          <p className="text-muted-foreground">
            Proactive maintenance scheduling and tracking
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={loadSchedules}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={handleCreateSchedule}>
            <Plus className="h-4 w-4 mr-2" />
            Create Schedule
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Schedules</p>
                <p className="text-2xl font-bold">{schedules.length}</p>
              </div>
              <Shield className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Due Soon</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {schedules.filter(s => s.status === 'due').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Overdue</p>
                <p className="text-2xl font-bold text-red-600">
                  {schedules.filter(s => s.status === 'overdue').length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Compliance Rate</p>
                <p className="text-2xl font-bold text-green-600">
                  {Math.round((schedules.filter(s => s.status === 'current').length / schedules.length) * 100)}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="schedules">Schedules</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Search */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Search</label>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search schedules..."
                      value={filters.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>

                {/* Status Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Status</label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => handleFilterChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="current">Current</SelectItem>
                      <SelectItem value="due">Due</SelectItem>
                      <SelectItem value="overdue">Overdue</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Frequency Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Frequency</label>
                  <Select
                    value={filters.frequency}
                    onValueChange={(value) => handleFilterChange('frequency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All frequencies" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Frequencies</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                      <SelectItem value="semi-annual">Semi-Annual</SelectItem>
                      <SelectItem value="annual">Annual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Schedules Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredSchedules.map((schedule) => {
              const daysUntilDue = getDaysUntilDue(schedule.nextDue)
              const completionPercentage = getCompletionPercentage(schedule.checklist)

              return (
                <Card key={schedule.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{schedule.maintenanceType}</CardTitle>
                        <CardDescription className="flex items-center gap-2 mt-1">
                          <MapPin className="h-4 w-4" />
                          {schedule.transformerId} - {schedule.transformerLocation}
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Badge className={getStatusColor(schedule.status)}>
                          {schedule.status.charAt(0).toUpperCase() + schedule.status.slice(1)}
                        </Badge>
                        <Badge className={getPriorityColor(schedule.priority)}>
                          {schedule.priority.charAt(0).toUpperCase() + schedule.priority.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Schedule Info */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Frequency</p>
                        <p className="font-medium capitalize">{schedule.frequency}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Next Due</p>
                        <p className="font-medium">
                          {format(new Date(schedule.nextDue), 'MMM d, yyyy')}
                          {daysUntilDue <= 0 ? (
                            <span className="text-red-600 ml-1">({Math.abs(daysUntilDue)} days overdue)</span>
                          ) : (
                            <span className="text-muted-foreground ml-1">({daysUntilDue} days)</span>
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Assigned To</p>
                        <p className="font-medium flex items-center gap-1">
                          <User className="h-4 w-4" />
                          {schedule.assignedTechnician}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Duration</p>
                        <p className="font-medium flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {schedule.estimatedDuration}h
                        </p>
                      </div>
                    </div>

                    {/* Progress */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Progress</span>
                        <span className="font-medium">{completionPercentage}%</span>
                      </div>
                      <Progress value={completionPercentage} className="h-2" />
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2 pt-2">
                      <Button size="sm" variant="outline">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        View Checklist
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleRescheduleTask(schedule.id, new Date().toISOString())}
                      >
                        <Calendar className="h-4 w-4 mr-2" />
                        Reschedule
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleStartMaintenance(schedule.id)}
                      >
                        <Wrench className="h-4 w-4 mr-2" />
                        Start Maintenance
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="schedules" className="space-y-4">
          {/* Schedules Table */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Transformer</TableHead>
                    <TableHead>Maintenance Type</TableHead>
                    <TableHead>Frequency</TableHead>
                    <TableHead>Next Due</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Assigned To</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSchedules.map((schedule) => (
                    <TableRow key={schedule.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{schedule.transformerId}</div>
                          <div className="text-sm text-muted-foreground">{schedule.transformerLocation}</div>
                        </div>
                      </TableCell>
                      <TableCell>{schedule.maintenanceType}</TableCell>
                      <TableCell className="capitalize">{schedule.frequency}</TableCell>
                      <TableCell>
                        <div>
                          <div>{format(new Date(schedule.nextDue), 'MMM d, yyyy')}</div>
                          <div className="text-sm text-muted-foreground">
                            {getDaysUntilDue(schedule.nextDue) <= 0 ? (
                              <span className="text-red-600">
                                {Math.abs(getDaysUntilDue(schedule.nextDue))} days overdue
                              </span>
                            ) : (
                              <span>{getDaysUntilDue(schedule.nextDue)} days remaining</span>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(schedule.status)}>
                          {schedule.status.charAt(0).toUpperCase() + schedule.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Progress value={getCompletionPercentage(schedule.checklist)} className="h-2" />
                          <div className="text-xs text-muted-foreground">
                            {getCompletionPercentage(schedule.checklist)}%
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{schedule.assignedTechnician}</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Preventive Maintenance Analytics
              </CardTitle>
              <CardDescription>
                Performance metrics and compliance tracking
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  Analytics Dashboard
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Comprehensive analytics for preventive maintenance performance,
                  compliance rates, and trend analysis.
                </p>
                <div className="text-sm text-gray-500">
                  Coming soon - Advanced analytics features
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
