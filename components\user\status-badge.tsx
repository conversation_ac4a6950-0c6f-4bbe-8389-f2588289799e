"use client"

import { Badge } from "@/src/components/ui/badge"
import { UserStatus } from "@/src/types/user-management"
import { UserCheck, UserX } from "lucide-react"

interface StatusBadgeProps {
  status: UserStatus | string
  showIcon?: boolean
  size?: "sm" | "md" | "lg"
}

export function StatusBadge({ status, showIcon = false, size = "md" }: StatusBadgeProps) {
  const isActive = status === "Active"
  
  const sizeClasses = {
    sm: "text-xs py-0 px-1.5",
    md: "text-sm py-0.5 px-2",
    lg: "text-base py-1 px-2.5"
  }
  
  return (
    <Badge
      className={`
        ${isActive ? "bg-green-500 hover:bg-green-600" : "bg-slate-500 hover:bg-slate-600"}
        ${sizeClasses[size]}
        ${showIcon ? "flex items-center gap-1" : ""}
      `}
      aria-label={`User status: ${status}`}
    >
      {showIcon && (
        isActive ? 
          <UserCheck className="h-3 w-3" /> : 
          <UserX className="h-3 w-3" />
      )}
      {status}
    </Badge>
  )
}
