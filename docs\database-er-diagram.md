# 🗄️ EEU-DTMS Entity-Relationship (ER) Diagram

## 📊 **Database Architecture Overview**

The Entity-Relationship (ER) diagram for the Ethiopian Electric Utility - Digital Transformer Management System (EEU-DTMS) illustrates the comprehensive database structure that supports all system functionalities.

## 🏗️ **Core Entities and Relationships**

### **🔌 Primary Entities**

#### **1. TRANSFORMERS** (Central Entity)

```sql
TRANSFORMERS
├── id (PK) - VARCHAR(50)
├── name - VARCHAR(255)
├── serial_number - VARCHAR(100) UNIQUE
├── manufacturer - VARCHAR(100)
├── model - VARCHAR(100)
├── type - ENUM('distribution', 'power', 'instrument')
├── voltage_primary - DECIMAL(10,2)
├── voltage_secondary - DECIMAL(10,2)
├── capacity_mva - DECIMAL(10,2)
├── frequency - DECIMAL(5,2)
├── installation_date - DATE
├── commissioning_date - DATE
├── location_id (FK) - INT
├── region_id (FK) - INT
├── service_center_id (FK) - INT
├── status - ENUM('operational', 'maintenance', 'offline', 'decommissioned')
├── condition - ENUM('excellent', 'good', 'fair', 'poor', 'critical')
├── load_percentage - DECIMAL(5,2)
├── efficiency - DECIMAL(5,2)
├── temperature - DECIMAL(5,2)
├── oil_level - DECIMAL(5,2)
├── oil_temperature - DECIMAL(5,2)
├── tap_position - INT
├── last_maintenance_date - DATE
├── next_maintenance_date - DATE
├── warranty_expiry - DATE
├── cost - DECIMAL(15,2)
├── coordinates_lat - DECIMAL(10,8)
├── coordinates_lng - DECIMAL(11,8)
├── created_at - TIMESTAMP
├── updated_at - TIMESTAMP
└── created_by (FK) - INT
```

#### **2. LOCATIONS**

```sql
LOCATIONS
├── id (PK) - INT AUTO_INCREMENT
├── name - VARCHAR(255)
├── address - TEXT
├── city - VARCHAR(100)
├── region_id (FK) - INT
├── postal_code - VARCHAR(20)
├── coordinates_lat - DECIMAL(10,8)
├── coordinates_lng - DECIMAL(11,8)
├── elevation - DECIMAL(8,2)
├── location_type - ENUM('substation', 'distribution_center', 'industrial', 'residential')
├── access_level - ENUM('public', 'restricted', 'high_security')
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **3. REGIONS**

```sql
REGIONS
├── id (PK) - INT AUTO_INCREMENT
├── name - VARCHAR(100)
├── code - VARCHAR(10) UNIQUE
├── description - TEXT
├── area_km2 - DECIMAL(12,2)
├── population - INT
├── regional_manager_id (FK) - INT
├── headquarters_location - VARCHAR(255)
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **4. SERVICE_CENTERS**

```sql
SERVICE_CENTERS
├── id (PK) - INT AUTO_INCREMENT
├── name - VARCHAR(255)
├── code - VARCHAR(20) UNIQUE
├── region_id (FK) - INT
├── location_id (FK) - INT
├── manager_id (FK) - INT
├── contact_phone - VARCHAR(20)
├── contact_email - VARCHAR(255)
├── operating_hours - VARCHAR(100)
├── service_area_radius - DECIMAL(8,2)
├── staff_count - INT
├── equipment_inventory - JSON
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

### **👥 User Management Entities**

#### **5. USERS**

```sql
USERS
├── id (PK) - INT AUTO_INCREMENT
├── employee_id - VARCHAR(50) UNIQUE
├── first_name - VARCHAR(100)
├── last_name - VARCHAR(100)
├── email - VARCHAR(255) UNIQUE
├── phone - VARCHAR(20)
├── password_hash - VARCHAR(255)
├── role_id (FK) - INT
├── department_id (FK) - INT
├── region_id (FK) - INT
├── service_center_id (FK) - INT
├── supervisor_id (FK) - INT
├── hire_date - DATE
├── status - ENUM('active', 'inactive', 'suspended')
├── last_login - TIMESTAMP
├── profile_image - VARCHAR(255)
├── preferences - JSON
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **6. ROLES**

```sql
ROLES
├── id (PK) - INT AUTO_INCREMENT
├── name - VARCHAR(100) UNIQUE
├── display_name - VARCHAR(100)
├── description - TEXT
├── level - INT
├── permissions - JSON
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **7. DEPARTMENTS**

```sql
DEPARTMENTS
├── id (PK) - INT AUTO_INCREMENT
├── name - VARCHAR(100)
├── code - VARCHAR(20) UNIQUE
├── description - TEXT
├── head_id (FK) - INT
├── parent_department_id (FK) - INT
├── budget - DECIMAL(15,2)
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

### **🔧 Maintenance Management Entities**

#### **8. MAINTENANCE_SCHEDULES**

```sql
MAINTENANCE_SCHEDULES
├── id (PK) - VARCHAR(50)
├── transformer_id (FK) - VARCHAR(50)
├── maintenance_type - ENUM('preventive', 'corrective', 'emergency', 'inspection')
├── priority - ENUM('low', 'medium', 'high', 'critical')
├── status - ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'postponed')
├── scheduled_date - DATE
├── estimated_duration - INT (hours)
├── assigned_technician_id (FK) - INT
├── assigned_team_id (FK) - INT
├── description - TEXT
├── required_parts - JSON
├── required_tools - JSON
├── safety_requirements - TEXT
├── completion_date - DATE
├── actual_duration - INT (hours)
├── completion_notes - TEXT
├── cost - DECIMAL(10,2)
├── next_maintenance_date - DATE
├── created_by (FK) - INT
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **9. MAINTENANCE_TEAMS**

```sql
MAINTENANCE_TEAMS
├── id (PK) - INT AUTO_INCREMENT
├── name - VARCHAR(100)
├── team_leader_id (FK) - INT
├── service_center_id (FK) - INT
├── specialization - VARCHAR(100)
├── members - JSON
├── equipment - JSON
├── availability_status - ENUM('available', 'busy', 'offline')
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **10. MAINTENANCE_RECORDS**

```sql
MAINTENANCE_RECORDS
├── id (PK) - VARCHAR(50)
├── maintenance_schedule_id (FK) - VARCHAR(50)
├── transformer_id (FK) - VARCHAR(50)
├── technician_id (FK) - INT
├── work_performed - TEXT
├── parts_used - JSON
├── measurements_before - JSON
├── measurements_after - JSON
├── issues_found - TEXT
├── recommendations - TEXT
├── photos - JSON
├── documents - JSON
├── quality_check_passed - BOOLEAN
├── supervisor_approval - BOOLEAN
├── approved_by (FK) - INT
├── approval_date - TIMESTAMP
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

### **📊 Monitoring & Analytics Entities**

#### **11. PERFORMANCE_METRICS**

```sql
PERFORMANCE_METRICS
├── id (PK) - BIGINT AUTO_INCREMENT
├── transformer_id (FK) - VARCHAR(50)
├── metric_type - ENUM('load', 'temperature', 'voltage', 'current', 'power_factor', 'efficiency')
├── value - DECIMAL(10,4)
├── unit - VARCHAR(20)
├── timestamp - TIMESTAMP
├── source - ENUM('manual', 'sensor', 'calculated')
├── quality_flag - ENUM('good', 'questionable', 'bad')
├── created_at - TIMESTAMP
└── INDEX(transformer_id, timestamp)
```

#### **12. SMART_METERS**

```sql
SMART_METERS
├── id (PK) - VARCHAR(50)
├── serial_number - VARCHAR(100) UNIQUE
├── transformer_id (FK) - VARCHAR(50)
├── meter_type - ENUM('energy', 'demand', 'power_quality')
├── manufacturer - VARCHAR(100)
├── model - VARCHAR(100)
├── installation_date - DATE
├── last_reading_date - TIMESTAMP
├── communication_protocol - VARCHAR(50)
├── ip_address - VARCHAR(45)
├── status - ENUM('active', 'inactive', 'faulty')
├── calibration_date - DATE
├── next_calibration_date - DATE
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **13. METER_READINGS**

```sql
METER_READINGS
├── id (PK) - BIGINT AUTO_INCREMENT
├── smart_meter_id (FK) - VARCHAR(50)
├── reading_type - ENUM('energy_consumed', 'demand', 'voltage', 'current', 'power_factor')
├── value - DECIMAL(15,4)
├── unit - VARCHAR(20)
├── timestamp - TIMESTAMP
├── quality_code - VARCHAR(10)
├── created_at - TIMESTAMP
└── INDEX(smart_meter_id, timestamp)
```

### **🚨 Alert & Notification Entities**

#### **14. ALERTS**

```sql
ALERTS
├── id (PK) - VARCHAR(50)
├── transformer_id (FK) - VARCHAR(50)
├── alert_type - ENUM('temperature', 'overload', 'voltage', 'maintenance_due', 'fault', 'security')
├── severity - ENUM('low', 'medium', 'high', 'critical')
├── status - ENUM('active', 'acknowledged', 'resolved', 'false_positive')
├── title - VARCHAR(255)
├── description - TEXT
├── trigger_value - DECIMAL(10,4)
├── threshold_value - DECIMAL(10,4)
├── location_id (FK) - INT
├── detected_at - TIMESTAMP
├── acknowledged_at - TIMESTAMP
├── acknowledged_by (FK) - INT
├── resolved_at - TIMESTAMP
├── resolved_by (FK) - INT
├── resolution_notes - TEXT
├── escalation_level - INT
├── notification_sent - BOOLEAN
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **15. NOTIFICATIONS**

```sql
NOTIFICATIONS
├── id (PK) - BIGINT AUTO_INCREMENT
├── user_id (FK) - INT
├── alert_id (FK) - VARCHAR(50)
├── notification_type - ENUM('email', 'sms', 'push', 'in_app')
├── title - VARCHAR(255)
├── message - TEXT
├── status - ENUM('pending', 'sent', 'delivered', 'failed')
├── sent_at - TIMESTAMP
├── delivered_at - TIMESTAMP
├── read_at - TIMESTAMP
├── priority - ENUM('low', 'medium', 'high', 'urgent')
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

### **📋 Inventory & Parts Management**

#### **16. INVENTORY_ITEMS**

```sql
INVENTORY_ITEMS
├── id (PK) - VARCHAR(50)
├── part_number - VARCHAR(100) UNIQUE
├── name - VARCHAR(255)
├── description - TEXT
├── category - VARCHAR(100)
├── manufacturer - VARCHAR(100)
├── model - VARCHAR(100)
├── specifications - JSON
├── unit_of_measure - VARCHAR(20)
├── unit_cost - DECIMAL(10,2)
├── minimum_stock_level - INT
├── maximum_stock_level - INT
├── reorder_point - INT
├── lead_time_days - INT
├── storage_requirements - TEXT
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **17. INVENTORY_STOCK**

```sql
INVENTORY_STOCK
├── id (PK) - BIGINT AUTO_INCREMENT
├── inventory_item_id (FK) - VARCHAR(50)
├── service_center_id (FK) - INT
├── quantity_available - INT
├── quantity_reserved - INT
├── quantity_on_order - INT
├── last_updated - TIMESTAMP
├── location_in_warehouse - VARCHAR(100)
├── batch_number - VARCHAR(100)
├── expiry_date - DATE
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

### **📊 Reporting & Analytics**

#### **18. REPORTS**

```sql
REPORTS
├── id (PK) - VARCHAR(50)
├── name - VARCHAR(255)
├── description - TEXT
├── report_type - ENUM('performance', 'maintenance', 'financial', 'compliance', 'custom')
├── parameters - JSON
├── schedule - VARCHAR(100)
├── format - ENUM('pdf', 'excel', 'csv', 'json')
├── recipients - JSON
├── created_by (FK) - INT
├── last_generated - TIMESTAMP
├── next_generation - TIMESTAMP
├── status - ENUM('active', 'inactive', 'draft')
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **19. REPORT_EXECUTIONS**

```sql
REPORT_EXECUTIONS
├── id (PK) - BIGINT AUTO_INCREMENT
├── report_id (FK) - VARCHAR(50)
├── executed_by (FK) - INT
├── execution_time - TIMESTAMP
├── parameters_used - JSON
├── status - ENUM('running', 'completed', 'failed')
├── file_path - VARCHAR(500)
├── file_size - BIGINT
├── execution_duration - INT (seconds)
├── error_message - TEXT
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

## 🔗 **Key Relationships**

### **Primary Relationships:**

1. **TRANSFORMERS** ↔ **LOCATIONS** (Many-to-One)
2. **TRANSFORMERS** ↔ **REGIONS** (Many-to-One)
3. **TRANSFORMERS** ↔ **SERVICE_CENTERS** (Many-to-One)
4. **TRANSFORMERS** ↔ **MAINTENANCE_SCHEDULES** (One-to-Many)
5. **TRANSFORMERS** ↔ **PERFORMANCE_METRICS** (One-to-Many)
6. **TRANSFORMERS** ↔ **SMART_METERS** (One-to-Many)
7. **TRANSFORMERS** ↔ **ALERTS** (One-to-Many)

### **User Management Relationships:**

8. **USERS** ↔ **ROLES** (Many-to-One)
9. **USERS** ↔ **DEPARTMENTS** (Many-to-One)
10. **USERS** ↔ **REGIONS** (Many-to-One)
11. **USERS** ↔ **SERVICE_CENTERS** (Many-to-One)

### **Maintenance Relationships:**

12. **MAINTENANCE_SCHEDULES** ↔ **USERS** (Many-to-One) [Technician]
13. **MAINTENANCE_SCHEDULES** ↔ **MAINTENANCE_TEAMS** (Many-to-One)
14. **MAINTENANCE_SCHEDULES** ↔ **MAINTENANCE_RECORDS** (One-to-Many)

### **Monitoring Relationships:**

15. **SMART_METERS** ↔ **METER_READINGS** (One-to-Many)
16. **ALERTS** ↔ **NOTIFICATIONS** (One-to-Many)
17. **ALERTS** ↔ **USERS** (Many-to-One) [Acknowledged/Resolved by]

### **Inventory Relationships:**

18. **INVENTORY_ITEMS** ↔ **INVENTORY_STOCK** (One-to-Many)
19. **SERVICE_CENTERS** ↔ **INVENTORY_STOCK** (One-to-Many)

## 📈 **Database Indexes for Performance**

```sql
-- Primary performance indexes
CREATE INDEX idx_transformers_location ON TRANSFORMERS(location_id);
CREATE INDEX idx_transformers_region ON TRANSFORMERS(region_id);
CREATE INDEX idx_transformers_status ON TRANSFORMERS(status);
CREATE INDEX idx_performance_metrics_transformer_time ON PERFORMANCE_METRICS(transformer_id, timestamp);
CREATE INDEX idx_alerts_transformer_status ON ALERTS(transformer_id, status);
CREATE INDEX idx_maintenance_schedules_date ON MAINTENANCE_SCHEDULES(scheduled_date);
CREATE INDEX idx_meter_readings_meter_time ON METER_READINGS(smart_meter_id, timestamp);
```

### **📋 Inventory & Parts Management**

#### **16. INVENTORY_ITEMS**

```sql
INVENTORY_ITEMS
├── id (PK) - VARCHAR(50)
├── part_number - VARCHAR(100) UNIQUE
├── name - VARCHAR(255)
├── description - TEXT
├── category - VARCHAR(100)
├── manufacturer - VARCHAR(100)
├── model - VARCHAR(100)
├── specifications - JSON
├── unit_of_measure - VARCHAR(20)
├── unit_cost - DECIMAL(10,2)
├── minimum_stock_level - INT
├── maximum_stock_level - INT
├── reorder_point - INT
├── lead_time_days - INT
├── storage_requirements - TEXT
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **17. INVENTORY_STOCK**

```sql
INVENTORY_STOCK
├── id (PK) - BIGINT AUTO_INCREMENT
├── inventory_item_id (FK) - VARCHAR(50)
├── service_center_id (FK) - INT
├── quantity_available - INT
├── quantity_reserved - INT
├── quantity_on_order - INT
├── last_updated - TIMESTAMP
├── location_in_warehouse - VARCHAR(100)
├── batch_number - VARCHAR(100)
├── expiry_date - DATE
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

### **📊 Reporting & Analytics**

#### **18. REPORTS**

```sql
REPORTS
├── id (PK) - VARCHAR(50)
├── name - VARCHAR(255)
├── description - TEXT
├── report_type - ENUM('performance', 'maintenance', 'financial', 'compliance', 'custom')
├── parameters - JSON
├── schedule - VARCHAR(100)
├── format - ENUM('pdf', 'excel', 'csv', 'json')
├── recipients - JSON
├── created_by (FK) - INT
├── last_generated - TIMESTAMP
├── next_generation - TIMESTAMP
├── status - ENUM('active', 'inactive', 'draft')
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

#### **19. REPORT_EXECUTIONS**

```sql
REPORT_EXECUTIONS
├── id (PK) - BIGINT AUTO_INCREMENT
├── report_id (FK) - VARCHAR(50)
├── executed_by (FK) - INT
├── execution_time - TIMESTAMP
├── parameters_used - JSON
├── status - ENUM('running', 'completed', 'failed')
├── file_path - VARCHAR(500)
├── file_size - BIGINT
├── execution_duration - INT (seconds)
├── error_message - TEXT
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

### **🌤️ Weather Integration**

#### **20. WEATHER_DATA**

```sql
WEATHER_DATA
├── id (PK) - BIGINT AUTO_INCREMENT
├── location_id (FK) - INT
├── temperature - DECIMAL(5,2)
├── humidity - DECIMAL(5,2)
├── wind_speed - DECIMAL(5,2)
├── wind_direction - VARCHAR(10)
├── pressure - DECIMAL(8,2)
├── visibility - DECIMAL(5,2)
├── uv_index - DECIMAL(3,1)
├── condition - VARCHAR(100)
├── description - TEXT
├── timestamp - TIMESTAMP
├── source - VARCHAR(100)
├── created_at - TIMESTAMP
└── INDEX(location_id, timestamp)
```

#### **21. WEATHER_ALERTS**

```sql
WEATHER_ALERTS
├── id (PK) - VARCHAR(50)
├── alert_type - ENUM('severe_weather', 'high_wind', 'lightning', 'temperature_extreme')
├── severity - ENUM('low', 'medium', 'high', 'critical')
├── title - VARCHAR(255)
├── description - TEXT
├── location_id (FK) - INT
├── start_time - TIMESTAMP
├── end_time - TIMESTAMP
├── affected_transformers - JSON
├── status - ENUM('active', 'expired', 'cancelled')
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

### **🔐 Security & Audit**

#### **22. AUDIT_LOGS**

```sql
AUDIT_LOGS
├── id (PK) - BIGINT AUTO_INCREMENT
├── user_id (FK) - INT
├── action - VARCHAR(100)
├── entity_type - VARCHAR(100)
├── entity_id - VARCHAR(100)
├── old_values - JSON
├── new_values - JSON
├── ip_address - VARCHAR(45)
├── user_agent - TEXT
├── timestamp - TIMESTAMP
├── session_id - VARCHAR(255)
└── INDEX(user_id, timestamp)
```

#### **23. USER_SESSIONS**

```sql
USER_SESSIONS
├── id (PK) - VARCHAR(255)
├── user_id (FK) - INT
├── ip_address - VARCHAR(45)
├── user_agent - TEXT
├── login_time - TIMESTAMP
├── last_activity - TIMESTAMP
├── logout_time - TIMESTAMP
├── status - ENUM('active', 'expired', 'terminated')
├── created_at - TIMESTAMP
└── updated_at - TIMESTAMP
```

## 🔗 **Extended Relationships**

### **Inventory Relationships:**

18. **INVENTORY_ITEMS** ↔ **INVENTORY_STOCK** (One-to-Many)
19. **SERVICE_CENTERS** ↔ **INVENTORY_STOCK** (One-to-Many)
20. **MAINTENANCE_RECORDS** ↔ **INVENTORY_ITEMS** (Many-to-Many) [Parts Used]

### **Weather Relationships:**

21. **LOCATIONS** ↔ **WEATHER_DATA** (One-to-Many)
22. **LOCATIONS** ↔ **WEATHER_ALERTS** (One-to-Many)
23. **WEATHER_ALERTS** ↔ **TRANSFORMERS** (Many-to-Many) [Affected Transformers]

### **Reporting Relationships:**

24. **REPORTS** ↔ **REPORT_EXECUTIONS** (One-to-Many)
25. **USERS** ↔ **REPORTS** (One-to-Many) [Created By]
26. **USERS** ↔ **REPORT_EXECUTIONS** (One-to-Many) [Executed By]

### **Security Relationships:**

27. **USERS** ↔ **AUDIT_LOGS** (One-to-Many)
28. **USERS** ↔ **USER_SESSIONS** (One-to-Many)

## 📊 **Visual ER Diagram Representation**

```
                    ┌─────────────┐
                    │   REGIONS   │
                    │     (3)     │
                    └──────┬──────┘
                           │
                    ┌──────▼──────┐
                    │  LOCATIONS  │
                    │     (2)     │
                    └──────┬──────┘
                           │
    ┌─────────────┐       │       ┌─────────────────┐
    │    USERS    │       │       │ SERVICE_CENTERS │
    │     (5)     │       │       │       (4)       │
    └──────┬──────┘       │       └─────────┬───────┘
           │              │                 │
           │       ┌──────▼──────┐          │
           │       │TRANSFORMERS │◄─────────┘
           │       │     (1)     │
           │       └──────┬──────┘
           │              │
           │              ├─────────────────────────────┐
           │              │                             │
           │       ┌──────▼──────┐              ┌──────▼──────┐
           │       │PERFORMANCE_ │              │SMART_METERS │
           │       │  METRICS    │              │    (12)     │
           │       │    (11)     │              └──────┬──────┘
           │       └─────────────┘                     │
           │                                    ┌──────▼──────┐
           │                                    │METER_       │
           │                                    │READINGS     │
           │                                    │    (13)     │
           │                                    └─────────────┘
           │
    ┌──────▼──────┐
    │MAINTENANCE_ │
    │ SCHEDULES   │
    │     (8)     │
    └──────┬──────┘
           │
    ┌──────▼──────┐
    │MAINTENANCE_ │
    │  RECORDS    │
    │    (10)     │
    └─────────────┘
```

## 🎯 **Database Design Principles**

### **✅ Normalization:**

- **3NF Compliance**: All tables follow Third Normal Form
- **Referential Integrity**: Foreign key constraints ensure data consistency
- **Data Redundancy**: Minimized through proper normalization

### **✅ Performance Optimization:**

- **Strategic Indexing**: Key performance indexes on frequently queried columns
- **Partitioning**: Large tables (PERFORMANCE_METRICS, METER_READINGS) can be partitioned by date
- **Caching Strategy**: Frequently accessed data cached at application level

### **✅ Scalability:**

- **Horizontal Scaling**: Database design supports sharding by region
- **Vertical Scaling**: Optimized for high-performance hardware
- **Archive Strategy**: Historical data archival for long-term storage

### **✅ Security:**

- **Access Control**: Role-based permissions at database level
- **Audit Trail**: Comprehensive logging of all data changes
- **Data Encryption**: Sensitive data encrypted at rest and in transit

This comprehensive ER diagram provides the foundation for the EEU-DTMS database architecture, ensuring efficient data management, relationships, and scalability for the transformer management system.
