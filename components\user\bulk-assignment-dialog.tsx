"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Label } from "@/src/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/src/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { ScrollArea } from "@/src/components/ui/scroll-area"
import {
  Building, MapPin, Briefcase, AlertTriangle, Users
} from "lucide-react"
import { User } from "@/src/types/user-management"
import { useToast } from "@/src/components/ui/use-toast"

interface BulkAssignmentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedUsers: User[]
  onAssign: (
    userIds: string[],
    assignment: {
      organizationalLevel: string
      regionId?: string
      serviceCenterId?: string
    }
  ) => void
  getRegions: () => { id: string, name: string }[]
  getServiceCenters: (regionId?: string) => { id: string, name: string }[]
}

export function BulkAssignmentDialog({
  open,
  onOpenChange,
  selectedUsers,
  onAssign,
  getRegions,
  getServiceCenters
}: BulkAssignmentDialogProps) {
  const { toast } = useToast()
  const [organizationalLevel, setOrganizationalLevel] = useState<string>("head_office")
  const [regionId, setRegionId] = useState<string | undefined>(undefined)
  const [serviceCenterId, setServiceCenterId] = useState<string | undefined>(undefined)

  // Handle level change
  const handleLevelChange = (value: string) => {
    setOrganizationalLevel(value)

    if (value === "head_office") {
      setRegionId(undefined)
      setServiceCenterId(undefined)
    }
  }

  // Handle region change
  const handleRegionChange = (value: string) => {
    setRegionId(value)
    setServiceCenterId(undefined)
  }

  // Handle save
  const handleSave = () => {
    if (selectedUsers.length === 0) return

    // Validate form
    if (organizationalLevel !== "head_office" && !regionId) {
      toast({
        title: "Validation Error",
        description: "Please select a region.",
        variant: "destructive"
      })
      return
    }

    if (organizationalLevel === "service_center" && !serviceCenterId) {
      toast({
        title: "Validation Error",
        description: "Please select a service center.",
        variant: "destructive"
      })
      return
    }

    onAssign(
      selectedUsers.map(user => user.id),
      {
        organizationalLevel,
        regionId,
        serviceCenterId
      }
    )

    onOpenChange(false)
  }

  const regions = getRegions()
  const serviceCenters = regionId ? getServiceCenters(regionId) : []

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            Bulk Assignment
          </DialogTitle>
          <DialogDescription>
            Assign {selectedUsers.length} users to an organizational unit
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="flex items-center p-3 border rounded-md bg-blue-50 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
            <Users className="h-4 w-4 mr-2 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium">Selected Users: {selectedUsers.length}</p>
              <p>The following users will be assigned to the selected organizational unit.</p>
            </div>
          </div>

          <ScrollArea className="h-[100px] rounded-md border p-2">
            <div className="space-y-1">
              {selectedUsers.map(user => (
                <div key={user.id} className="text-sm flex items-center">
                  <span className="font-medium mr-2">{user.name}</span>
                  <span className="text-muted-foreground">({user.employeeId})</span>
                </div>
              ))}
            </div>
          </ScrollArea>

          <div className="space-y-4">
            <Label>Organizational Level</Label>
            <RadioGroup
              value={organizationalLevel}
              onValueChange={handleLevelChange}
              className="flex flex-col space-y-3"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="head_office" id="bulk_head_office" />
                <Label htmlFor="bulk_head_office" className="flex items-center cursor-pointer">
                  <Building className="mr-2 h-4 w-4 text-blue-500" />
                  Head Office
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="regional_office" id="bulk_regional_office" />
                <Label htmlFor="bulk_regional_office" className="flex items-center cursor-pointer">
                  <MapPin className="mr-2 h-4 w-4 text-green-500" />
                  Regional Office
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="service_center" id="bulk_service_center" />
                <Label htmlFor="bulk_service_center" className="flex items-center cursor-pointer">
                  <Briefcase className="mr-2 h-4 w-4 text-amber-500" />
                  Service Center
                </Label>
              </div>
            </RadioGroup>
          </div>

          {organizationalLevel !== "head_office" && (
            <div className="space-y-4">
              <Label htmlFor="bulk_region">Region</Label>
              <Select
                value={regionId}
                onValueChange={handleRegionChange}
              >
                <SelectTrigger id="bulk_region">
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  {regions.map(region => (
                    <SelectItem key={region.id} value={region.id}>
                      {region.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {organizationalLevel === "service_center" && regionId && (
            <div className="space-y-4">
              <Label htmlFor="bulk_serviceCenter">Service Center</Label>
              <Select
                value={serviceCenterId}
                onValueChange={setServiceCenterId}
              >
                <SelectTrigger id="bulk_serviceCenter">
                  <SelectValue placeholder="Select service center" />
                </SelectTrigger>
                <SelectContent>
                  {serviceCenters.map(sc => (
                    <SelectItem key={sc.id} value={sc.id}>
                      {sc.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="flex items-center p-3 border rounded-md bg-amber-50 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400">
            <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0" />
            <div className="text-sm">
              <p>This action will change the assignment for all selected users. Previous assignments will be overwritten.</p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Assign Users
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
