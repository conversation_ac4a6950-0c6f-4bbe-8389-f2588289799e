/**
 * Enhanced Maintenance Service for EEU Transformer Management System
 */

export interface MaintenanceRecord {
  id: string
  transformerId: string
  type: 'preventive' | 'corrective' | 'emergency' | 'inspection'
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'critical'
  scheduledDate: string
  completedDate?: string
  technician: string
  description: string
  notes?: string
  cost?: number
  duration?: number
  parts?: string[]
  photos?: string[]
  createdAt: string
  updatedAt: string
}

export interface MaintenanceSchedule {
  id: string
  transformerId: string
  maintenanceType: string
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'annually'
  nextDue: string
  lastCompleted?: string
  isActive: boolean
}

class EnhancedMaintenanceService {
  private baseUrl = '/api/maintenance'
  private listeners: (() => void)[] = []
  private eventSource: EventSource | null = null

  // Get all maintenance records
  async getMaintenanceRecords(filters?: {
    transformerId?: string
    type?: string
    status?: string
    priority?: string
    dateFrom?: string
    dateTo?: string
  }): Promise<MaintenanceRecord[]> {
    try {
      const params = new URLSearchParams()
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, value)
        })
      }

      const response = await fetch(`${this.baseUrl}?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch maintenance records')
      }

      const data = await response.json()
      return data.success ? data.data : []
    } catch (error) {
      console.error('Error fetching maintenance records:', error)
      // Return mock data for development
      return this.getMockMaintenanceRecords()
    }
  }

  // Get maintenance record by ID
  async getMaintenanceRecord(id: string): Promise<MaintenanceRecord | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch maintenance record')
      }

      const data = await response.json()
      return data.success ? data.data : null
    } catch (error) {
      console.error('Error fetching maintenance record:', error)
      return null
    }
  }

  // Create new maintenance record
  async createMaintenanceRecord(record: Omit<MaintenanceRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<MaintenanceRecord> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(record),
      })

      if (!response.ok) {
        throw new Error('Failed to create maintenance record')
      }

      const data = await response.json()
      return data.data
    } catch (error) {
      console.error('Error creating maintenance record:', error)
      throw error
    }
  }

  // Update maintenance record
  async updateMaintenanceRecord(id: string, updates: Partial<MaintenanceRecord>): Promise<MaintenanceRecord> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })

      if (!response.ok) {
        throw new Error('Failed to update maintenance record')
      }

      const data = await response.json()
      return data.data
    } catch (error) {
      console.error('Error updating maintenance record:', error)
      throw error
    }
  }

  // Delete maintenance record
  async deleteMaintenanceRecord(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete maintenance record')
      }
    } catch (error) {
      console.error('Error deleting maintenance record:', error)
      throw error
    }
  }

  // Get maintenance schedules
  async getMaintenanceSchedules(transformerId?: string): Promise<MaintenanceSchedule[]> {
    try {
      const params = transformerId ? `?transformerId=${transformerId}` : ''
      const response = await fetch(`${this.baseUrl}/schedules${params}`)

      if (!response.ok) {
        throw new Error('Failed to fetch maintenance schedules')
      }

      const data = await response.json()
      return data.success ? data.data : []
    } catch (error) {
      console.error('Error fetching maintenance schedules:', error)
      return []
    }
  }

  // Get maintenance statistics
  async getMaintenanceStatistics(): Promise<{
    total: number
    byStatus: Record<string, number>
    byType: Record<string, number>
    byPriority: Record<string, number>
    avgCost: number
    avgDuration: number
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/statistics`)

      if (!response.ok) {
        throw new Error('Failed to fetch maintenance statistics')
      }

      const data = await response.json()
      return data.success ? data.data : this.getMockStatistics()
    } catch (error) {
      console.error('Error fetching maintenance statistics:', error)
      return this.getMockStatistics()
    }
  }

  // Schedule maintenance
  async scheduleMaintenance(schedule: Omit<MaintenanceSchedule, 'id'>): Promise<MaintenanceSchedule> {
    try {
      const response = await fetch(`${this.baseUrl}/schedules`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(schedule),
      })

      if (!response.ok) {
        throw new Error('Failed to schedule maintenance')
      }

      const data = await response.json()
      return data.data
    } catch (error) {
      console.error('Error scheduling maintenance:', error)
      throw error
    }
  }

  // Get upcoming maintenance
  async getUpcomingMaintenance(days: number = 30): Promise<MaintenanceRecord[]> {
    const endDate = new Date()
    endDate.setDate(endDate.getDate() + days)

    return this.getMaintenanceRecords({
      status: 'scheduled',
      dateTo: endDate.toISOString().split('T')[0]
    })
  }

  // Get overdue maintenance
  async getOverdueMaintenance(): Promise<MaintenanceRecord[]> {
    const today = new Date().toISOString().split('T')[0]

    return this.getMaintenanceRecords({
      status: 'scheduled',
      dateTo: today
    })
  }

  // Mock data for development
  private getMockMaintenanceRecords(): MaintenanceRecord[] {
    return [
      {
        id: 'M001',
        transformerId: 'T001',
        type: 'preventive',
        status: 'completed',
        priority: 'medium',
        scheduledDate: '2024-01-15',
        completedDate: '2024-01-15',
        technician: 'John Doe',
        description: 'Routine inspection and oil change',
        notes: 'All systems normal',
        cost: 1500,
        duration: 4,
        parts: ['Oil filter', 'Transformer oil'],
        createdAt: '2024-01-10T00:00:00Z',
        updatedAt: '2024-01-15T00:00:00Z'
      },
      {
        id: 'M002',
        transformerId: 'T002',
        type: 'corrective',
        status: 'in_progress',
        priority: 'high',
        scheduledDate: '2024-02-01',
        technician: 'Jane Smith',
        description: 'Repair cooling system',
        notes: 'Cooling fan replacement required',
        cost: 2500,
        duration: 6,
        parts: ['Cooling fan', 'Temperature sensor'],
        createdAt: '2024-01-25T00:00:00Z',
        updatedAt: '2024-02-01T00:00:00Z'
      }
    ]
  }

  private getMockStatistics() {
    return {
      total: 150,
      byStatus: {
        scheduled: 45,
        in_progress: 12,
        completed: 88,
        cancelled: 5
      },
      byType: {
        preventive: 90,
        corrective: 35,
        emergency: 15,
        inspection: 10
      },
      byPriority: {
        low: 60,
        medium: 55,
        high: 25,
        critical: 10
      },
      avgCost: 1800,
      avgDuration: 4.5
    }
  }

  // Real-time listener methods (disabled for performance)
  addListener(callback: () => void): () => void {
    // Disabled for performance - return no-op unsubscribe function
    return () => {}
  }

  private initializeEventSource() {
    // Disable EventSource for now to prevent 404 errors
    console.log('EventSource disabled - using mock data only')
    return
  }

  private startPolling() {
    // Disable polling to prevent unnecessary requests
    console.log('Polling disabled - using mock data only')
    return
  }

  // Stop real-time updates (disabled for performance)
  stopRealTimeUpdates() {
    // No-op - real-time updates are disabled
  }

  // Cleanup method
  cleanup() {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
    this.listeners = []
  }

  // Enhanced methods for compatibility with maintenance context
  async getAllMaintenanceRecordsEnhanced() {
    return this.getMaintenanceRecords()
  }

  async getUpcomingMaintenanceRecordsEnhanced() {
    return this.getUpcomingMaintenance()
  }

  async getInProgressMaintenanceRecordsEnhanced() {
    return this.getMaintenanceRecords({ status: 'in_progress' })
  }

  async getCompletedMaintenanceRecordsEnhanced() {
    return this.getMaintenanceRecords({ status: 'completed' })
  }

  async getMaintenanceRecordsByTransformerIdEnhanced(transformerId: string) {
    return this.getMaintenanceRecords({ transformerId })
  }

  getMaintenanceStatisticsEnhanced() {
    return this.getMaintenanceStatistics()
  }

  async getMaintenanceMapLocations() {
    // Return empty array for now - can be implemented later
    return []
  }
}

// Export singleton instance
export const enhancedMaintenanceService = new EnhancedMaintenanceService()

// Export class for testing
export { EnhancedMaintenanceService }
