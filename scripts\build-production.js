/**
 * Production build script
 * 
 * This script builds the application for production with optimizations
 * and performs post-build checks to ensure the build is ready for deployment.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Log with timestamp and color
function log(message, color = colors.reset) {
  const timestamp = new Date().toISOString().replace(/T/, ' ').replace(/\..+/, '');
  console.log(`${colors.dim}[${timestamp}]${colors.reset} ${color}${message}${colors.reset}`);
}

// Execute a command and return its output
function execute(command, options = {}) {
  log(`Executing: ${command}`, colors.cyan);
  try {
    return execSync(command, { 
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
      ...options
    });
  } catch (error) {
    log(`Error executing command: ${command}`, colors.red);
    log(error.message, colors.red);
    if (options.exitOnError !== false) {
      process.exit(1);
    }
    throw error;
  }
}

// Check if a directory exists
function directoryExists(dirPath) {
  try {
    return fs.statSync(dirPath).isDirectory();
  } catch (err) {
    return false;
  }
}

// Main build function
async function buildProduction() {
  const startTime = Date.now();
  
  log('Starting production build process...', colors.bright + colors.blue);
  
  // Step 1: Clean previous build
  log('Step 1: Cleaning previous build...', colors.yellow);
  if (directoryExists('.next')) {
    execute('rmdir /s /q .next', { exitOnError: false, silent: true });
  }
  
  // Step 2: Install dependencies
  log('Step 2: Checking dependencies...', colors.yellow);
  execute('npm ci', { exitOnError: false });
  
  // Step 3: Run linting
  log('Step 3: Running linter...', colors.yellow);
  try {
    execute('npm run lint', { exitOnError: false });
  } catch (error) {
    log('Linting found issues, but continuing with build...', colors.yellow);
  }
  
  // Step 4: Build the application
  log('Step 4: Building application...', colors.yellow);
  execute('npm run build');
  
  // Step 5: Verify build output
  log('Step 5: Verifying build output...', colors.yellow);
  if (!directoryExists('.next')) {
    log('Build failed: .next directory not found', colors.red);
    process.exit(1);
  }
  
  // Step 6: Run post-build checks
  log('Step 6: Running post-build checks...', colors.yellow);
  
  // Check for large JavaScript files
  log('Checking for large JavaScript files...', colors.dim);
  const jsDir = path.join('.next', 'static', 'chunks');
  if (directoryExists(jsDir)) {
    const files = fs.readdirSync(jsDir)
      .filter(file => file.endsWith('.js'))
      .map(file => {
        const filePath = path.join(jsDir, file);
        const stats = fs.statSync(filePath);
        return { 
          name: file, 
          size: stats.size, 
          sizeFormatted: (stats.size / 1024 / 1024).toFixed(2) + ' MB' 
        };
      })
      .sort((a, b) => b.size - a.size);
    
    log('Largest JavaScript files:', colors.dim);
    files.slice(0, 5).forEach(file => {
      const color = file.size > 500000 ? colors.yellow : colors.green;
      log(`  ${file.name}: ${file.sizeFormatted}`, color);
    });
  }
  
  // Calculate total build time
  const buildTime = ((Date.now() - startTime) / 1000).toFixed(2);
  log(`Build completed in ${buildTime} seconds`, colors.green);
  
  log('Production build completed successfully!', colors.bright + colors.green);
  log('To test the production build locally, run:', colors.bright);
  log('  npm run start', colors.cyan);
  log('To deploy to production, follow your deployment process.', colors.bright);
}

// Run the build process
buildProduction().catch(error => {
  log('Build failed with error:', colors.red);
  console.error(error);
  process.exit(1);
});
