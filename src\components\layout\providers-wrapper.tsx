"use client"

import React from 'react'
import { SidebarProvider } from '@/src/components/layout/sidebar-provider'
import { AuthProvider } from '@/src/features/auth/context/auth-context'
import { LanguageProvider } from '@/src/contexts/language-context'
import { ThemeProvider } from '@/components/theme-provider'
import { ToastProvider } from '@/components/ui/toast'

interface ProvidersWrapperProps {
  children: React.ReactNode
}

export default function ProvidersWrapper({ children }: ProvidersWrapperProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <LanguageProvider>
        <AuthProvider>
          <SidebarProvider>
            <ToastProvider>
              {children}
            </ToastProvider>
          </SidebarProvider>
        </AuthProvider>
      </LanguageProvider>
    </ThemeProvider>
  )
}
