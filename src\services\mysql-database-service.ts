"use client"

/**
 * MySQL Database Service (Client-side)
 *
 * This service provides a unified interface to access MySQL database data
 * through API routes. It replaces the JSON database usage throughout the application.
 */

/**
 * Make API request with error handling
 */
async function apiRequest<T>(endpoint: string): Promise<T | null> {
  try {
    const response = await fetch(endpoint);
    const result = await response.json();

    if (!response.ok) {
      if (result.fallback) {
        console.warn(`⚠️ MySQL API failed, fallback recommended: ${result.error}`);
        return null; // Signal fallback needed
      }
      throw new Error(result.error || 'API request failed');
    }

    return result.data;
  } catch (error) {
    console.error(`❌ API request failed for ${endpoint}:`, error);
    return null; // Signal fallback needed
  }
}

export class MySQLDatabaseService {

  /**
   * Get all transformers
   */
  static async getTransformers() {
    try {
      const transformers = await apiRequest<any[]>('/api/mysql/transformers');
      return transformers || [];
    } catch (error) {
      console.error('Error fetching transformers:', error);
      return [];
    }
  }

  /**
   * Get transformer statistics
   */
  static async getTransformerStatistics() {
    try {
      const statistics = await apiRequest<any>('/api/mysql/statistics');
      return statistics || {
        total: 0,
        byStatus: {},
        byRegion: {},
        maintenance: { scheduled: 0, overdue: 0, completed: 0, total: 0 },
        alerts: { critical: 0, warning: 0, info: 0, total: 0 }
      };
    } catch (error) {
      console.error('Error fetching transformer statistics:', error);
      return {
        total: 0,
        byStatus: {},
        byRegion: {},
        maintenance: { scheduled: 0, overdue: 0, completed: 0, total: 0 },
        alerts: { critical: 0, warning: 0, info: 0, total: 0 }
      };
    }
  }

  /**
   * Get all alerts (placeholder - implement API route if needed)
   */
  static async getAlerts() {
    // TODO: Implement API route for alerts
    return [];
  }

  /**
   * Get recent alerts (placeholder - implement API route if needed)
   */
  static async getRecentAlerts(limit: number = 10) {
    // TODO: Implement API route for recent alerts
    return [];
  }

  /**
   * Search transformers (placeholder - implement API route if needed)
   */
  static async searchTransformers(query: string) {
    // TODO: Implement API route for transformer search
    return [];
  }

  /**
   * Get dashboard data
   */
  static async getDashboardData() {
    try {
      const dashboardData = await apiRequest<any>('/api/mysql/dashboard');
      return dashboardData || {
        transformerStatistics: {
          total: 0,
          byStatus: {},
          byRegion: {},
          maintenance: { scheduled: 0, overdue: 0, completed: 0, total: 0 },
          alerts: { critical: 0, warning: 0, info: 0, total: 0 }
        },
        recentAlerts: [],
        activeOutages: [],
        weatherAlerts: [],
        isLoading: false
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      return {
        transformerStatistics: {
          total: 0,
          byStatus: {},
          byRegion: {},
          maintenance: { scheduled: 0, overdue: 0, completed: 0, total: 0 },
          alerts: { critical: 0, warning: 0, info: 0, total: 0 }
        },
        recentAlerts: [],
        activeOutages: [],
        weatherAlerts: [],
        isLoading: false
      };
    }
  }

  /**
   * Test connection
   */
  static async testConnection(): Promise<boolean> {
    try {
      const response = await fetch('/api/mysql/dashboard');
      return response.ok;
    } catch (error) {
      console.error('MySQL connection test failed:', error);
      return false;
    }
  }
}

export default MySQLDatabaseService;
