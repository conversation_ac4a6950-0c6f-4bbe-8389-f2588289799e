"use client"

import { Search, Filter, Shield, UserCheck, UserX, X, CreditCard, Building, User, Mail, Clock, AlertTriangle } from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Button } from "@/src/components/ui/button"
import { UserFilters } from "@/src/types/user-management"

interface UserFilterBarProps {
  filters: UserFilters
  onFilterChange: (key: keyof UserFilters, value: string) => void
}

export function UserFilterBar({ filters, onFilterChange }: UserFilterBarProps) {
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFilterChange('searchQuery', e.target.value)
  }

  const handleClearSearch = () => {
    onFilterChange('searchQuery', '')
  }

  const showSearchClear = filters.searchQuery.length > 0

  return (
    <div className="flex flex-col gap-4 sm:flex-row mb-4">
      <div className="relative flex-1 flex">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder={`Search by ${filters.searchType === 'employeeId' ? 'employee ID' : filters.searchType}...`}
            className="pl-8 rounded-r-none"
            value={filters.searchQuery}
            onChange={handleSearchChange}
            aria-label={`Search by ${filters.searchType}`}
          />
          {showSearchClear && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-0 h-full rounded-l-none"
              onClick={handleClearSearch}
              aria-label="Clear search"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        <Select
          value={filters.searchType}
          onValueChange={(value) => onFilterChange('searchType', value as 'name' | 'email' | 'employeeId')}
        >
          <SelectTrigger className="w-[130px] rounded-l-none border-l-0" aria-label="Search type">
            <SelectValue placeholder="Search by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name">
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4" />
                Name
              </div>
            </SelectItem>
            <SelectItem value="email">
              <div className="flex items-center">
                <Mail className="mr-2 h-4 w-4" />
                Email
              </div>
            </SelectItem>
            <SelectItem value="employeeId">
              <div className="flex items-center">
                <CreditCard className="mr-2 h-4 w-4" />
                Employee ID
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex gap-2 flex-wrap">
        <Select
          value={filters.role}
          onValueChange={(value) => onFilterChange('role', value)}
        >
          <SelectTrigger className="w-[160px]" aria-label="Filter by role">
            <Shield className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="administrator">Administrator</SelectItem>
            <SelectItem value="manager">Manager</SelectItem>
            <SelectItem value="supervisor">Supervisor</SelectItem>
            <SelectItem value="technician">Technician</SelectItem>
            <SelectItem value="operator">Operator</SelectItem>
            <SelectItem value="viewer">Viewer</SelectItem>
          </SelectContent>
        </Select>
        <Select
          value={filters.status}
          onValueChange={(value) => onFilterChange('status', value)}
        >
          <SelectTrigger className="w-[160px]" aria-label="Filter by status">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">
              <div className="flex items-center">
                All Status
              </div>
            </SelectItem>
            <SelectItem value="active">
              <div className="flex items-center">
                <UserCheck className="mr-2 h-4 w-4 text-green-500" />
                Active
              </div>
            </SelectItem>
            <SelectItem value="inactive">
              <div className="flex items-center">
                <UserX className="mr-2 h-4 w-4 text-slate-500" />
                Inactive
              </div>
            </SelectItem>
            <SelectItem value="pending">
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4 text-orange-500" />
                Pending
              </div>
            </SelectItem>
            <SelectItem value="suspended">
              <div className="flex items-center">
                <AlertTriangle className="mr-2 h-4 w-4 text-red-500" />
                Suspended
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
        <Select
          value={filters.department}
          onValueChange={(value) => onFilterChange('department', value)}
        >
          <SelectTrigger className="w-[160px]" aria-label="Filter by department">
            <Building className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Department" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            <SelectItem value="it">IT</SelectItem>
            <SelectItem value="maintenance">Maintenance</SelectItem>
            <SelectItem value="operations">Operations</SelectItem>
            <SelectItem value="management">Management</SelectItem>
            <SelectItem value="customer service">Customer Service</SelectItem>
            <SelectItem value="finance">Finance</SelectItem>
            <SelectItem value="hr">HR</SelectItem>
            <SelectItem value="audit">Audit</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
