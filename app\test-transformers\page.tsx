'use client'

import { useState, useEffect } from 'react'

export default function TestTransformersPage() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('🔄 Test: Fetching transformers...')
        const response = await fetch('/api/mysql/transformers')
        
        console.log('📊 Test: Response status:', response.status)
        console.log('📊 Test: Response headers:', Object.fromEntries(response.headers.entries()))
        
        if (response.ok) {
          const result = await response.json()
          console.log('📊 Test: Full response:', result)
          console.log('📊 Test: Data structure:', {
            success: result.success,
            count: result.count,
            hasData: !!result.data,
            hasTransformers: !!(result.data && result.data.transformers),
            transformersLength: result.data?.transformers?.length || 0
          })
          setData(result)
        } else {
          const errorText = await response.text()
          console.error('❌ Test: Error response:', errorText)
          setError(`API request failed: ${response.status} - ${errorText}`)
        }
      } catch (err) {
        console.error('❌ Test: Error:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">🔄 Testing Transformers API...</h1>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4 text-red-600">❌ Test Failed</h1>
        <div className="bg-red-50 p-4 rounded-lg">
          <p className="text-red-800">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">🧪 Transformers API Test Results</h1>
      
      <div className="space-y-6">
        {/* Summary */}
        <div className="bg-green-50 p-4 rounded-lg">
          <h2 className="font-bold text-green-800 mb-2">✅ API Response Summary</h2>
          <ul className="text-green-700 space-y-1">
            <li>Success: {data?.success ? '✅ Yes' : '❌ No'}</li>
            <li>Count: {data?.count || 'N/A'}</li>
            <li>Source: {data?.source || 'N/A'}</li>
            <li>Has Data Object: {data?.data ? '✅ Yes' : '❌ No'}</li>
            <li>Has Transformers Array: {data?.data?.transformers ? '✅ Yes' : '❌ No'}</li>
            <li>Transformers Count: {data?.data?.transformers?.length || 0}</li>
          </ul>
        </div>

        {/* First Transformer */}
        {data?.data?.transformers && data.data.transformers.length > 0 && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <h2 className="font-bold text-blue-800 mb-2">📋 First Transformer Sample</h2>
            <pre className="bg-white p-3 rounded text-xs overflow-auto max-h-64 border">
              {JSON.stringify(data.data.transformers[0], null, 2)}
            </pre>
          </div>
        )}

        {/* Transformation Test */}
        {data?.data?.transformers && data.data.transformers.length > 0 && (
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h2 className="font-bold text-yellow-800 mb-2">🔄 Transformation Test</h2>
            {(() => {
              const t = data.data.transformers[0]
              const transformed = {
                id: t.id?.toString(),
                code: t.serialNumber || t.serial_number || `DT-${t.id}`,
                name: t.name || `${t.manufacturer} Transformer`,
                location: t.location?.address || t.location_name || 'Unknown Location',
                district: (t.location?.address || t.location_name || '').split(',')[0] || 'Unknown District',
                kvaRating: t.capacity || t.capacity_kva || 0,
                installation: t.installationDate || t.installation_date || '2023-01-01',
                status: t.status === 'operational' ? 'Active' :
                       t.status === 'maintenance' ? 'Maintenance' :
                       t.status === 'critical' || t.status === 'burnt' ? 'Failed' :
                       t.status === 'warning' ? 'Maintenance' : 'Active'
              }
              
              return (
                <pre className="bg-white p-3 rounded text-xs overflow-auto max-h-64 border">
                  {JSON.stringify(transformed, null, 2)}
                </pre>
              )
            })()}
          </div>
        )}

        {/* Raw Response */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="font-bold text-gray-800 mb-2">📄 Raw Response (truncated)</h2>
          <pre className="bg-white p-3 rounded text-xs overflow-auto max-h-32 border">
            {JSON.stringify(data, null, 2).substring(0, 1000)}...
          </pre>
        </div>
      </div>
    </div>
  )
}
