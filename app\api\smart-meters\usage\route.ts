import { NextResponse } from "next/server"
import { smartMeterService } from "@/src/services/smart-meter-service"

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  
  // Check for timeframe parameter
  const timeframe = searchParams.get("timeframe") as "daily" | "monthly" || "monthly"
  
  // Get usage data
  const usageData = await smartMeterService.getUsageData(timeframe)
  
  return NextResponse.json(usageData)
}
