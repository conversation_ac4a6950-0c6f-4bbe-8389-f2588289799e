"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/src/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/src/components/ui/dialog"
import {
  Bar<PERSON>hart3,
  <PERSON><PERSON>hart,
  Pie<PERSON>hart,
  AreaChart,
  ScatterChart,
  Radar,
  Grid3X3,
  Calendar,
  Download,
  Play,
  Clock,
  MoreHorizontal,
  Trash2,
  Edit,
  Co<PERSON>,
  Share2,
  Mail
} from "lucide-react"
import { format } from "date-fns"
import { useReports, CustomReportConfig } from "@/src/contexts/reports-context"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"

export function SavedReports() {
  const {
    customReports,
    deleteCustomReport,
    generateReport,
    scheduleReport,
    exportReport,
    selectCustomReport
  } = useReports()

  const [searchQuery, setSearchQuery] = useState("")
  const [selectedReport, setSelectedReport] = useState<CustomReportConfig | null>(null)
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)
  const [scheduleConfig, setScheduleConfig] = useState({
    frequency: "monthly",
    day: 1,
    time: "08:00",
    recipients: [""]
  })

  const filteredReports = customReports.filter(report =>
    report.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    report.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getChartIcon = (type: string) => {
    switch (type) {
      case "bar": return <BarChart3 className="h-4 w-4" />
      case "line": return <LineChart className="h-4 w-4" />
      case "pie": return <PieChart className="h-4 w-4" />
      case "area": return <AreaChart className="h-4 w-4" />
      case "scatter": return <ScatterChart className="h-4 w-4" />
      case "radar": return <Radar className="h-4 w-4" />
      case "heatmap": return <Grid3X3 className="h-4 w-4" />
      default: return <BarChart3 className="h-4 w-4" />
    }
  }

  const handleGenerateReport = (report: CustomReportConfig) => {
    generateReport("custom", {
      metrics: report.metrics,
      chartType: report.chartType,
      dateRange: report.dateRange,
      filters: report.filters
    })

    selectCustomReport(report.id)
  }

  const handleExportReport = (report: CustomReportConfig, format: "pdf" | "excel" | "csv" | "json") => {
    exportReport(format)
  }

  const handleOpenScheduleDialog = (report: CustomReportConfig) => {
    setSelectedReport(report)

    if (report.schedule) {
      setScheduleConfig({
        frequency: report.schedule.frequency,
        day: report.schedule.day || 1,
        time: report.schedule.time || "08:00",
        recipients: report.schedule.recipients || [""]
      })
    } else {
      setScheduleConfig({
        frequency: "monthly",
        day: 1,
        time: "08:00",
        recipients: [""]
      })
    }

    setIsScheduleDialogOpen(true)
  }

  const handleScheduleReport = () => {
    if (selectedReport) {
      scheduleReport(selectedReport.id, scheduleConfig)
      setIsScheduleDialogOpen(false)
    }
  }

  const handleAddRecipient = () => {
    setScheduleConfig(prev => ({
      ...prev,
      recipients: [...prev.recipients, ""]
    }))
  }

  const handleRemoveRecipient = (index: number) => {
    setScheduleConfig(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }))
  }

  const handleUpdateRecipient = (index: number, value: string) => {
    setScheduleConfig(prev => ({
      ...prev,
      recipients: prev.recipients.map((r, i) => i === index ? value : r)
    }))
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle>Saved Reports</CardTitle>
            <CardDescription>View and manage your saved custom reports</CardDescription>
          </div>
          <div className="w-full sm:w-auto">
            <Input
              placeholder="Search reports..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full sm:w-[300px]"
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {filteredReports.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No saved reports found</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Report Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Last Run</TableHead>
                <TableHead>Schedule</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReports.map((report) => (
                <TableRow key={report.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{report.name}</div>
                      <div className="text-sm text-muted-foreground">{report.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {getChartIcon(report.chartType)}
                      <span className="capitalize">{report.chartType}</span>
                    </div>
                  </TableCell>
                  <TableCell>{format(new Date(report.createdAt), "MMM d, yyyy")}</TableCell>
                  <TableCell>
                    {report.lastRun ? format(new Date(report.lastRun), "MMM d, yyyy") : "Never"}
                  </TableCell>
                  <TableCell>
                    {report.schedule ? (
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="capitalize">{report.schedule.frequency}</span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">Not scheduled</span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleGenerateReport(report)}
                      >
                        <Play className="h-4 w-4" />
                      </Button>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleGenerateReport(report)}>
                            <Play className="mr-2 h-4 w-4" />
                            Generate Report
                          </DropdownMenuItem>

                          <DropdownMenuSeparator />
                          <DropdownMenuLabel>Export</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleExportReport(report, "pdf")}>
                            <Download className="mr-2 h-4 w-4" />
                            Export as PDF
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleExportReport(report, "excel")}>
                            <Download className="mr-2 h-4 w-4" />
                            Export as Excel
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleExportReport(report, "csv")}>
                            <Download className="mr-2 h-4 w-4" />
                            Export as CSV
                          </DropdownMenuItem>

                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleOpenScheduleDialog(report)}>
                            <Clock className="mr-2 h-4 w-4" />
                            Schedule Report
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Report
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Share2 className="mr-2 h-4 w-4" />
                            Share Report
                          </DropdownMenuItem>

                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => deleteCustomReport(report.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Report
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Schedule Report</DialogTitle>
              <DialogDescription>
                Configure when this report should be automatically generated and sent
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="schedule-frequency">Frequency</Label>
                <Select
                  value={scheduleConfig.frequency}
                  onValueChange={(value) => setScheduleConfig(prev => ({ ...prev, frequency: value as any }))}
                >
                  <SelectTrigger id="schedule-frequency">
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {scheduleConfig.frequency !== "daily" && (
                <div className="grid gap-2">
                  <Label htmlFor="schedule-day">
                    {scheduleConfig.frequency === "weekly" ? "Day of Week" : "Day of Month"}
                  </Label>
                  <Select
                    value={scheduleConfig.day.toString()}
                    onValueChange={(value) => setScheduleConfig(prev => ({ ...prev, day: parseInt(value) }))}
                  >
                    <SelectTrigger id="schedule-day">
                      <SelectValue placeholder="Select day" />
                    </SelectTrigger>
                    <SelectContent>
                      {scheduleConfig.frequency === "weekly" ? (
                        <>
                          <SelectItem value="1">Monday</SelectItem>
                          <SelectItem value="2">Tuesday</SelectItem>
                          <SelectItem value="3">Wednesday</SelectItem>
                          <SelectItem value="4">Thursday</SelectItem>
                          <SelectItem value="5">Friday</SelectItem>
                          <SelectItem value="6">Saturday</SelectItem>
                          <SelectItem value="7">Sunday</SelectItem>
                        </>
                      ) : (
                        Array.from({ length: 31 }, (_, i) => (
                          <SelectItem key={i + 1} value={(i + 1).toString()}>{i + 1}</SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="grid gap-2">
                <Label htmlFor="schedule-time">Time</Label>
                <Input
                  id="schedule-time"
                  type="time"
                  value={scheduleConfig.time}
                  onChange={(e) => setScheduleConfig(prev => ({ ...prev, time: e.target.value }))}
                />
              </div>

              <div className="grid gap-2">
                <div className="flex items-center justify-between">
                  <Label>Recipients</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleAddRecipient}
                  >
                    Add Recipient
                  </Button>
                </div>

                <div className="space-y-2">
                  {scheduleConfig.recipients.map((recipient, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Email address"
                        value={recipient}
                        onChange={(e) => handleUpdateRecipient(index, e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveRecipient(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsScheduleDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleScheduleReport}>
                Save Schedule
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}
