'use client'

import { useState, useEffect } from 'react'

export default function TransformerIds() {
  const [transformers, setTransformers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('/api/mysql/transformers')
        if (response.ok) {
          const data = await response.json()
          if (data.success && data.data?.transformers) {
            setTransformers(data.data.transformers.slice(0, 10)) // First 10 transformers
          }
        }
      } catch (error) {
        console.error('Error:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return <div className="p-6">Loading...</div>
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">First 10 Transformer IDs</h1>
      <div className="space-y-2">
        {transformers.map((t, index) => (
          <div key={index} className="p-3 bg-gray-100 rounded">
            <div className="font-bold">ID: {t.id}</div>
            <div>Serial: {t.serial_number}</div>
            <div>Name: {t.name}</div>
            <div>
              <a 
                href={`/transformers/${t.id}`} 
                className="text-blue-600 hover:underline"
                target="_blank"
              >
                View Details →
              </a>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
