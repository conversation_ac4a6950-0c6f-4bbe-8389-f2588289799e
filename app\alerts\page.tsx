"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  AlertTriangle,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  Bell,
  BellOff,
  Filter,
  Search,
  Download,
  RefreshCw,
  MapPin,
  Zap,
  User,
  Calendar,
  Eye,
  EyeOff,
  Archive,
  Trash2,
  Settings,
  TrendingUp,
  BarChart3
} from 'lucide-react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { MainLayout } from "@/src/components/layout/main-layout"

// Mock alert data
const mockAlerts = [
  {
    id: 1,
    type: 'critical',
    title: 'Transformer Overheating',
    message: 'Transformer T-AA-001 temperature exceeded 85°C threshold',
    transformerId: 'T-AA-001',
    location: 'Lideta, Addis Ababa',
    timestamp: '2024-02-12T14:30:00Z',
    status: 'active',
    priority: 'critical',
    assignedTo: 'John Doe',
    category: 'temperature',
    acknowledged: false,
    resolved: false
  },
  {
    id: 2,
    type: 'warning',
    title: 'High Load Factor',
    message: 'Transformer T-OR-045 load factor at 92% of capacity',
    transformerId: 'T-OR-045',
    location: 'Sebeta, Oromia',
    timestamp: '2024-02-12T13:15:00Z',
    status: 'active',
    priority: 'high',
    assignedTo: 'Jane Smith',
    category: 'load',
    acknowledged: true,
    resolved: false
  },
  {
    id: 3,
    type: 'info',
    title: 'Maintenance Completed',
    message: 'Scheduled maintenance completed for T-AM-023',
    transformerId: 'T-AM-023',
    location: 'Bahir Dar, Amhara',
    timestamp: '2024-02-12T11:00:00Z',
    status: 'resolved',
    priority: 'low',
    assignedTo: 'Bob Johnson',
    category: 'maintenance',
    acknowledged: true,
    resolved: true
  },
  {
    id: 4,
    type: 'warning',
    title: 'Voltage Fluctuation',
    message: 'Voltage instability detected on T-TG-012',
    transformerId: 'T-TG-012',
    location: 'Mekelle, Tigray',
    timestamp: '2024-02-12T10:45:00Z',
    status: 'investigating',
    priority: 'medium',
    assignedTo: 'Alice Brown',
    category: 'voltage',
    acknowledged: true,
    resolved: false
  }
]

const alertStats = {
  total: 156,
  active: 23,
  critical: 5,
  warning: 12,
  info: 6,
  resolved: 133,
  acknowledged: 18,
  unacknowledged: 5
}

const alertTrends = Array.from({ length: 24 }, (_, i) => ({
  hour: `${i}:00`,
  critical: Math.floor(Math.random() * 3),
  warning: Math.floor(Math.random() * 8),
  info: Math.floor(Math.random() * 5)
}))

const alertIcons = {
  critical: <XCircle className="h-5 w-5 text-red-500" />,
  warning: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
  info: <Info className="h-5 w-5 text-blue-500" />
}

const priorityColors = {
  critical: 'bg-red-100 text-red-800 border-red-200',
  high: 'bg-orange-100 text-orange-800 border-orange-200',
  medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  low: 'bg-green-100 text-green-800 border-green-200'
}

const statusColors = {
  active: 'bg-red-100 text-red-800 border-red-200',
  investigating: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  resolved: 'bg-green-100 text-green-800 border-green-200'
}

export default function AlertsPage() {
  const [alerts, setAlerts] = useState([])
  const [alertStats, setAlertStats] = useState({
    total: 0,
    active: 0,
    critical: 0,
    warning: 0,
    info: 0,
    resolved: 0,
    acknowledged: 0,
    unacknowledged: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedPriority, setSelectedPriority] = useState('all')

  // Load alerts from database
  useEffect(() => {
    const loadAlertsData = async () => {
      try {
        console.log('🚨 Loading alerts data from MySQL...')
        const response = await fetch('/api/mysql/alerts')

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            const alertsData = data.data || []
            setAlerts(alertsData)

            // Calculate statistics from alerts data
            const stats = {
              total: alertsData.length,
              active: alertsData.filter((a: any) => a.status === 'active').length,
              critical: alertsData.filter((a: any) => a.severity === 'critical').length,
              warning: alertsData.filter((a: any) => a.severity === 'high' || a.severity === 'medium').length,
              info: alertsData.filter((a: any) => a.severity === 'low').length,
              resolved: alertsData.filter((a: any) => a.isResolved).length,
              acknowledged: alertsData.filter((a: any) => a.status !== 'active').length,
              unacknowledged: alertsData.filter((a: any) => a.status === 'active').length
            }
            setAlertStats(stats)

            console.log('✅ Alerts data loaded successfully')
          } else {
            throw new Error(data.error || 'Failed to load alerts data')
          }
        } else {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
      } catch (error) {
        console.error('❌ Error loading alerts data:', error)
        // Fallback to mock data
        setAlerts(mockAlerts)
        setAlertStats({
          total: 156,
          active: 23,
          critical: 5,
          warning: 12,
          info: 6,
          resolved: 133,
          acknowledged: 18,
          unacknowledged: 5
        })
      } finally {
        setLoading(false)
      }
    }

    loadAlertsData()
  }, [])

  const handleAcknowledge = (alertId: number) => {
    setAlerts(prev => prev.map(alert =>
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ))
  }

  const handleResolve = (alertId: number) => {
    setAlerts(prev => prev.map(alert =>
      alert.id === alertId ? { ...alert, resolved: true, status: 'resolved' } : alert
    ))
  }

  const filteredAlerts = alerts.filter((alert: any) => {
    const matchesSearch = (alert.title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (alert.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (alert.transformerSerial || '').toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || alert.severity === selectedType
    const matchesStatus = selectedStatus === 'all' || alert.status === selectedStatus
    const matchesPriority = selectedPriority === 'all' || alert.priority === selectedPriority

    return matchesSearch && matchesType && matchesStatus && matchesPriority
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading alerts...</p>
        </div>
      </div>
    )
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_maintenance_manager",
        "regional_admin",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician"
      ]}
    >
      <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Alert Management</h1>
              <p className="text-muted-foreground">
                Monitor and manage system alerts and notifications
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Alert Settings
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button>
                <Bell className="h-4 w-4 mr-2" />
                Create Alert
              </Button>
            </div>
          </div>

          {/* Alert Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Alerts</CardTitle>
                <Bell className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{alertStats.total}</div>
                <p className="text-xs text-muted-foreground">
                  {alertStats.active} active alerts
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Critical</CardTitle>
                <XCircle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{alertStats.critical}</div>
                <p className="text-xs text-muted-foreground">
                  Require immediate attention
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Warnings</CardTitle>
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{alertStats.warning}</div>
                <p className="text-xs text-muted-foreground">
                  Need monitoring
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Unacknowledged</CardTitle>
                <BellOff className="h-4 w-4 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{alertStats.unacknowledged}</div>
                <p className="text-xs text-muted-foreground">
                  Awaiting acknowledgment
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="active" className="space-y-4">
            <TabsList>
              <TabsTrigger value="active">Active Alerts</TabsTrigger>
              <TabsTrigger value="all">All Alerts</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="space-y-4">
              {/* Filters */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <Input
                        placeholder="Search alerts..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="max-w-sm"
                      />
                    </div>
                    <select
                      value={selectedType}
                      onChange={(e) => setSelectedType(e.target.value)}
                      className="border rounded-md px-3 py-2"
                    >
                      <option value="all">All Types</option>
                      <option value="critical">Critical</option>
                      <option value="warning">Warning</option>
                      <option value="info">Info</option>
                    </select>
                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="border rounded-md px-3 py-2"
                    >
                      <option value="all">All Status</option>
                      <option value="active">Active</option>
                      <option value="investigating">Investigating</option>
                      <option value="resolved">Resolved</option>
                    </select>
                    <Button variant="outline">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Alert List */}
              <div className="space-y-4">
                {filteredAlerts.map((alert: any) => (
                  <Card key={alert.id} className={`${alert.status === 'active' ? 'border-l-4 border-l-red-500' : ''}`}>
                    <CardContent className="pt-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          <div className="flex-shrink-0">
                            {alert.severity === 'critical' ? <XCircle className="h-5 w-5 text-red-500" /> :
                             alert.severity === 'high' ? <AlertTriangle className="h-5 w-5 text-yellow-500" /> :
                             <Info className="h-5 w-5 text-blue-500" />}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold">{alert.title}</h3>
                              <Badge className={priorityColors[alert.priority as keyof typeof priorityColors] || 'bg-gray-100 text-gray-800'}>
                                {alert.priority}
                              </Badge>
                              <Badge className={statusColors[alert.status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
                                {alert.status}
                              </Badge>
                              {alert.status === 'active' && (
                                <Badge variant="destructive">Active</Badge>
                              )}
                            </div>
                            <p className="text-muted-foreground mb-3">{alert.description}</p>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Zap className="h-3 w-3" />
                                <span>{alert.transformerSerial || 'N/A'}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                <span>{alert.regionName || alert.serviceCenterName || 'Unknown'}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                <span>{alert.assignedTo ? `User ${alert.assignedTo}` : 'Unassigned'}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>{new Date(alert.createdAt).toLocaleString()}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {alert.status === 'active' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAcknowledge(alert.id)}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Acknowledge
                            </Button>
                          )}
                          {!alert.isResolved && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleResolve(alert.id)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              Resolve
                            </Button>
                          )}
                          <Button variant="outline" size="sm">
                            <Archive className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="all" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>All Alerts</CardTitle>
                  <CardDescription>Complete alert history and management</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-muted-foreground">
                    <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Complete alert history table would be displayed here</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Alert Trends */}
                <Card>
                  <CardHeader>
                    <CardTitle>Alert Trends (24h)</CardTitle>
                    <CardDescription>Alert frequency over the last 24 hours</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <AreaChart data={alertTrends}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="hour" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Area type="monotone" dataKey="critical" stackId="1" stroke="#ef4444" fill="#ef4444" fillOpacity={0.6} />
                        <Area type="monotone" dataKey="warning" stackId="1" stroke="#f59e0b" fill="#f59e0b" fillOpacity={0.6} />
                        <Area type="monotone" dataKey="info" stackId="1" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Alert Categories */}
                <Card>
                  <CardHeader>
                    <CardTitle>Alert Categories</CardTitle>
                    <CardDescription>Distribution by alert category</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Temperature</span>
                        <span className="text-sm text-muted-foreground">35%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Load</span>
                        <span className="text-sm text-muted-foreground">28%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Voltage</span>
                        <span className="text-sm text-muted-foreground">22%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Maintenance</span>
                        <span className="text-sm text-muted-foreground">15%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Response Time Analytics */}
              <Card>
                <CardHeader>
                  <CardTitle>Response Time Analytics</CardTitle>
                  <CardDescription>Average response and resolution times</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">4.2 min</div>
                      <p className="text-sm text-muted-foreground">Avg. Acknowledgment Time</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">18.5 min</div>
                      <p className="text-sm text-muted-foreground">Avg. Response Time</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">2.3 hrs</div>
                      <p className="text-sm text-muted-foreground">Avg. Resolution Time</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
      </div>
    </MainLayout>
  )
}