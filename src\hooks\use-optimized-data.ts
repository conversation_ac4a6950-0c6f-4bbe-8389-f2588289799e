/**
 * Optimized Data Fetching Hook
 * Provides caching, lazy loading, and performance optimization for data fetching
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { toast } from 'sonner'

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

interface OptimizedDataOptions {
  cacheKey?: string
  cacheTTL?: number // Time to live in milliseconds
  enableCache?: boolean
  lazy?: boolean
  retryAttempts?: number
  retryDelay?: number
  onError?: (error: Error) => void
  onSuccess?: (data: any) => void
}

interface OptimizedDataState<T> {
  data: T | null
  loading: boolean
  error: Error | null
  refreshing: boolean
  lastFetched: number | null
}

// Global cache store
const cache = new Map<string, CacheEntry<any>>()

// Cache cleanup interval
setInterval(() => {
  const now = Date.now()
  for (const [key, entry] of cache.entries()) {
    if (now - entry.timestamp > entry.ttl) {
      cache.delete(key)
    }
  }
}, 60000) // Cleanup every minute

export function useOptimizedData<T>(
  fetchFn: () => Promise<T>,
  options: OptimizedDataOptions = {}
) {
  const {
    cacheKey,
    cacheTTL = 5 * 60 * 1000, // 5 minutes default
    enableCache = true,
    lazy = false,
    retryAttempts = 3,
    retryDelay = 1000,
    onError,
    onSuccess
  } = options

  const [state, setState] = useState<OptimizedDataState<T>>({
    data: null,
    loading: !lazy,
    error: null,
    refreshing: false,
    lastFetched: null
  })

  const abortControllerRef = useRef<AbortController | null>(null)
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Get data from cache
  const getCachedData = useCallback((): T | null => {
    if (!enableCache || !cacheKey) return null
    
    const cached = cache.get(cacheKey)
    if (!cached) return null
    
    const now = Date.now()
    if (now - cached.timestamp > cached.ttl) {
      cache.delete(cacheKey)
      return null
    }
    
    return cached.data
  }, [cacheKey, enableCache])

  // Set data in cache
  const setCachedData = useCallback((data: T) => {
    if (!enableCache || !cacheKey) return
    
    cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      ttl: cacheTTL
    })
  }, [cacheKey, enableCache, cacheTTL])

  // Fetch data with retry logic
  const fetchWithRetry = useCallback(async (
    attempt: number = 1,
    isRefresh: boolean = false
  ): Promise<void> => {
    try {
      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController()

      // Check cache first (unless refreshing)
      if (!isRefresh) {
        const cachedData = getCachedData()
        if (cachedData) {
          setState(prev => ({
            ...prev,
            data: cachedData,
            loading: false,
            error: null,
            lastFetched: Date.now()
          }))
          onSuccess?.(cachedData)
          return
        }
      }

      // Set loading state
      setState(prev => ({
        ...prev,
        loading: !isRefresh,
        refreshing: isRefresh,
        error: null
      }))

      // Fetch data
      const data = await fetchFn()

      // Cache the data
      setCachedData(data)

      // Update state
      setState(prev => ({
        ...prev,
        data,
        loading: false,
        refreshing: false,
        error: null,
        lastFetched: Date.now()
      }))

      onSuccess?.(data)

    } catch (error) {
      const err = error as Error

      // Don't handle aborted requests
      if (err.name === 'AbortError') return

      console.error(`Fetch attempt ${attempt} failed:`, err)

      // Retry logic
      if (attempt < retryAttempts) {
        retryTimeoutRef.current = setTimeout(() => {
          fetchWithRetry(attempt + 1, isRefresh)
        }, retryDelay * attempt) // Exponential backoff
        return
      }

      // Final failure
      setState(prev => ({
        ...prev,
        loading: false,
        refreshing: false,
        error: err
      }))

      onError?.(err)
      toast.error(`Failed to load data: ${err.message}`)
    }
  }, [fetchFn, getCachedData, setCachedData, retryAttempts, retryDelay, onError, onSuccess])

  // Initial fetch
  useEffect(() => {
    if (!lazy) {
      fetchWithRetry()
    }

    // Cleanup on unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
    }
  }, [lazy, fetchWithRetry])

  // Manual fetch function
  const fetch = useCallback(() => {
    return fetchWithRetry(1, false)
  }, [fetchWithRetry])

  // Refresh function (bypasses cache)
  const refresh = useCallback(() => {
    return fetchWithRetry(1, true)
  }, [fetchWithRetry])

  // Clear cache function
  const clearCache = useCallback(() => {
    if (cacheKey) {
      cache.delete(cacheKey)
    }
  }, [cacheKey])

  // Prefetch function (for preloading data)
  const prefetch = useCallback(async () => {
    if (getCachedData()) return // Already cached
    
    try {
      const data = await fetchFn()
      setCachedData(data)
    } catch (error) {
      console.warn('Prefetch failed:', error)
    }
  }, [fetchFn, getCachedData, setCachedData])

  return {
    ...state,
    fetch,
    refresh,
    clearCache,
    prefetch,
    isCached: !!getCachedData()
  }
}

// Hook for optimized component mounting
export function useOptimizedMount(callback: () => void, delay: number = 0) {
  useEffect(() => {
    if (delay === 0) {
      callback()
    } else {
      const timeout = setTimeout(callback, delay)
      return () => clearTimeout(timeout)
    }
  }, [callback, delay])
}

// Hook for debounced values
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Hook for intersection observer (lazy loading)
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [elementRef, hasIntersected, options])

  return { isIntersecting, hasIntersected }
}

// Global cache management
export const cacheManager = {
  clear: () => cache.clear(),
  delete: (key: string) => cache.delete(key),
  has: (key: string) => cache.has(key),
  size: () => cache.size,
  keys: () => Array.from(cache.keys()),
  clearExpired: () => {
    const now = Date.now()
    for (const [key, entry] of cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        cache.delete(key)
      }
    }
  }
}
