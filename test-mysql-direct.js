/**
 * Direct MySQL Connection Test
 * Tests the MySQL database connection without Next.js middleware
 */

const mysql = require('mysql2/promise');

// Database configuration (using default values from .env.local)
const DB_CONFIG = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  charset: 'utf8mb4'
};

async function testMySQLConnection() {
  console.log('🔄 Testing MySQL Connection...');
  console.log('📋 Configuration:');
  console.log(`   Host: ${DB_CONFIG.host}`);
  console.log(`   Port: ${DB_CONFIG.port}`);
  console.log(`   User: ${DB_CONFIG.user}`);
  console.log(`   Database: ${DB_CONFIG.database}`);
  console.log('');

  try {
    // Test 1: Connect without database to check MySQL server
    console.log('🔍 Test 1: Checking MySQL server connection...');
    const tempConfig = { ...DB_CONFIG };
    delete tempConfig.database;

    const tempConnection = await mysql.createConnection(tempConfig);
    console.log('✅ MySQL server connection successful');

    // Check MySQL version
    const [versionResult] = await tempConnection.execute('SELECT VERSION() as version');
    console.log(`📊 MySQL Version: ${versionResult[0].version}`);

    await tempConnection.end();

    // Test 2: Check if database exists
    console.log('\n🔍 Test 2: Checking if database exists...');
    const tempConnection2 = await mysql.createConnection(tempConfig);
    const [databases] = await tempConnection2.execute(`SHOW DATABASES LIKE '${DB_CONFIG.database}'`);

    if (databases.length > 0) {
      console.log(`✅ Database '${DB_CONFIG.database}' exists`);
    } else {
      console.log(`⚠️  Database '${DB_CONFIG.database}' does not exist`);
      console.log('🔧 Creating database...');
      await tempConnection2.execute(`CREATE DATABASE IF NOT EXISTS \`${DB_CONFIG.database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
      console.log(`✅ Database '${DB_CONFIG.database}' created`);
    }

    await tempConnection2.end();

    // Test 3: Connect to the specific database
    console.log('\n🔍 Test 3: Connecting to the application database...');
    const connection = await mysql.createConnection(DB_CONFIG);
    console.log(`✅ Connected to database '${DB_CONFIG.database}'`);

    // Test 4: Check tables
    console.log('\n🔍 Test 4: Checking database tables...');
    const [tables] = await connection.execute('SHOW TABLES');

    if (tables.length > 0) {
      console.log(`📊 Found ${tables.length} tables:`);
      tables.forEach((table, index) => {
        const tableName = Object.values(table)[0];
        console.log(`   ${index + 1}. ${tableName}`);
      });
    } else {
      console.log('⚠️  No tables found in database');
    }

    // Test 5: Simple query test
    console.log('\n🔍 Test 5: Testing simple query...');
    const [result] = await connection.execute('SELECT 1 as test, NOW() as time_now');
    console.log('✅ Query test successful');
    console.log(`📊 Result: test=${result[0].test}, time=${result[0].time_now}`);

    await connection.end();

    console.log('\n🎉 All MySQL connection tests passed!');
    console.log('✅ MySQL database is ready for the application');

  } catch (error) {
    console.error('\n❌ MySQL connection test failed:');
    console.error('Error:', error.message);

    if (error.code) {
      console.error('Error Code:', error.code);
    }

    if (error.errno) {
      console.error('Error Number:', error.errno);
    }

    // Provide helpful suggestions based on common errors
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Suggestions:');
      console.log('   1. Make sure MySQL server is running');
      console.log('   2. Check if the port 3306 is correct');
      console.log('   3. Verify the host address');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Suggestions:');
      console.log('   1. Check username and password');
      console.log('   2. Verify user has proper permissions');
      console.log('   3. Make sure user can connect from this host');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 Suggestions:');
      console.log('   1. Database might not exist');
      console.log('   2. Check database name spelling');
      console.log('   3. Verify user has access to this database');
    }

    process.exit(1);
  }
}

// Run the test
testMySQLConnection();
