"use client"

import React, { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Checkbox } from "@/src/components/ui/checkbox"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/src/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem
} from "@/src/components/ui/dropdown-menu"
import { 
  Search, 
  Filter, 
  Download, 
  Upload, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  MapPin,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings,
  RefreshCw,
  ChevronDown,
  ArrowUpD<PERSON>,
  <PERSON><PERSON><PERSON>,
  ArrowDown
} from 'lucide-react'

interface Transformer {
  id: string
  name: string
  type: string
  capacity: string
  voltage: string
  status: 'Operational' | 'Warning' | 'Critical' | 'Maintenance' | 'Offline'
  location: {
    region: string
    serviceCenter: string
    address: string
    coordinates: { lat: number; lng: number }
  }
  specifications: {
    manufacturer: string
    model: string
    serialNumber: string
    yearInstalled: number
    lastMaintenance: string
    nextMaintenance: string
  }
  performance: {
    efficiency: number
    loadFactor: number
    temperature: number
    vibration: string
  }
  lastUpdated: string
}

type SortField = keyof Transformer | 'location.region' | 'specifications.manufacturer' | 'performance.efficiency'
type SortDirection = 'asc' | 'desc'

export function AdvancedTransformerTable() {
  const [transformers, setTransformers] = useState<Transformer[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedTransformers, setSelectedTransformers] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string[]>(['all'])
  const [regionFilter, setRegionFilter] = useState<string[]>(['all'])
  const [sortField, setSortField] = useState<SortField>('name')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [showBulkActions, setShowBulkActions] = useState(false)

  useEffect(() => {
    loadTransformers()
  }, [])

  const loadTransformers = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockTransformers: Transformer[] = Array.from({ length: 50 }, (_, i) => ({
        id: `T${String(i + 1).padStart(3, '0')}`,
        name: `Transformer ${String(i + 1).padStart(3, '0')}`,
        type: ['Distribution', 'Power', 'Instrument'][i % 3],
        capacity: ['100 kVA', '250 kVA', '500 kVA', '1000 kVA'][i % 4],
        voltage: ['11/0.4 kV', '33/11 kV', '132/33 kV'][i % 3],
        status: ['Operational', 'Warning', 'Critical', 'Maintenance', 'Offline'][i % 5] as any,
        location: {
          region: ['Addis Ababa', 'Oromia', 'Amhara', 'Tigray', 'SNNPR'][i % 5],
          serviceCenter: `Service Center ${i % 10 + 1}`,
          address: `Address ${i + 1}`,
          coordinates: { lat: 9.0192 + (i * 0.01), lng: 38.7525 + (i * 0.01) }
        },
        specifications: {
          manufacturer: ['ABB', 'Siemens', 'Schneider', 'GE'][i % 4],
          model: `Model-${i + 1}`,
          serialNumber: `SN${String(i + 1).padStart(6, '0')}`,
          yearInstalled: 2015 + (i % 8),
          lastMaintenance: new Date(Date.now() - (i * 30 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
          nextMaintenance: new Date(Date.now() + ((30 - i) * 24 * 60 * 60 * 1000)).toISOString().split('T')[0]
        },
        performance: {
          efficiency: 90 + (i % 10),
          loadFactor: 0.6 + (i % 4) * 0.1,
          temperature: 35 + (i % 20),
          vibration: ['Normal', 'Elevated', 'High'][i % 3]
        },
        lastUpdated: new Date(Date.now() - (i * 60 * 60 * 1000)).toISOString()
      }))
      
      setTransformers(mockTransformers)
    } catch (error) {
      console.error('Failed to load transformers:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filtering and sorting logic
  const filteredAndSortedTransformers = useMemo(() => {
    let filtered = transformers.filter(transformer => {
      const matchesSearch = searchTerm === '' || 
        transformer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transformer.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transformer.location.region.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transformer.specifications.manufacturer.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesStatus = statusFilter.includes('all') || statusFilter.includes(transformer.status)
      const matchesRegion = regionFilter.includes('all') || regionFilter.includes(transformer.location.region)

      return matchesSearch && matchesStatus && matchesRegion
    })

    // Sorting
    filtered.sort((a, b) => {
      let aValue: any
      let bValue: any

      if (sortField.includes('.')) {
        const [parent, child] = sortField.split('.')
        aValue = (a as any)[parent][child]
        bValue = (b as any)[parent][child]
      } else {
        aValue = a[sortField as keyof Transformer]
        bValue = b[sortField as keyof Transformer]
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [transformers, searchTerm, statusFilter, regionFilter, sortField, sortDirection])

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedTransformers.length / itemsPerPage)
  const paginatedTransformers = filteredAndSortedTransformers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTransformers(paginatedTransformers.map(t => t.id))
    } else {
      setSelectedTransformers([])
    }
  }

  const handleSelectTransformer = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedTransformers([...selectedTransformers, id])
    } else {
      setSelectedTransformers(selectedTransformers.filter(tid => tid !== id))
    }
  }

  const handleBulkAction = async (action: string) => {
    console.log(`Performing ${action} on transformers:`, selectedTransformers)
    // Implement bulk actions here
    setSelectedTransformers([])
    setShowBulkActions(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Operational': return 'bg-green-100 text-green-800 border-green-200'
      case 'Warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'Maintenance': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'Offline': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Operational': return <CheckCircle className="h-3 w-3" />
      case 'Warning': return <AlertTriangle className="h-3 w-3" />
      case 'Critical': return <AlertTriangle className="h-3 w-3" />
      case 'Maintenance': return <Clock className="h-3 w-3" />
      case 'Offline': return <Zap className="h-3 w-3" />
      default: return <CheckCircle className="h-3 w-3" />
    }
  }

  const SortButton = ({ field, children }: { field: SortField; children: React.ReactNode }) => (
    <Button
      variant="ghost"
      size="sm"
      className="h-8 p-0 font-medium"
      onClick={() => handleSort(field)}
    >
      {children}
      {sortField === field ? (
        sortDirection === 'asc' ? <ArrowUp className="ml-1 h-3 w-3" /> : <ArrowDown className="ml-1 h-3 w-3" />
      ) : (
        <ArrowUpDown className="ml-1 h-3 w-3" />
      )}
    </Button>
  )

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Transformer Management
            </CardTitle>
            <CardDescription>
              Advanced transformer monitoring and management system
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-1" />
              Import
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
            <Button variant="outline" size="sm" onClick={loadTransformers}>
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search transformers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-1" />
                Status
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuCheckboxItem
                checked={statusFilter.includes('all')}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setStatusFilter(['all'])
                  }
                }}
              >
                All Status
              </DropdownMenuCheckboxItem>
              {['Operational', 'Warning', 'Critical', 'Maintenance', 'Offline'].map(status => (
                <DropdownMenuCheckboxItem
                  key={status}
                  checked={statusFilter.includes(status)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setStatusFilter(prev => prev.filter(s => s !== 'all').concat(status))
                    } else {
                      setStatusFilter(prev => prev.filter(s => s !== status))
                    }
                  }}
                >
                  {status}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MapPin className="h-4 w-4 mr-1" />
                Region
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuCheckboxItem
                checked={regionFilter.includes('all')}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setRegionFilter(['all'])
                  }
                }}
              >
                All Regions
              </DropdownMenuCheckboxItem>
              {['Addis Ababa', 'Oromia', 'Amhara', 'Tigray', 'SNNPR'].map(region => (
                <DropdownMenuCheckboxItem
                  key={region}
                  checked={regionFilter.includes(region)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setRegionFilter(prev => prev.filter(r => r !== 'all').concat(region))
                    } else {
                      setRegionFilter(prev => prev.filter(r => r !== region))
                    }
                  }}
                >
                  {region}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Bulk Actions */}
        {selectedTransformers.length > 0 && (
          <div className="flex items-center gap-2 mb-4 p-3 bg-muted rounded-lg">
            <span className="text-sm font-medium">
              {selectedTransformers.length} transformer(s) selected
            </span>
            <Button variant="outline" size="sm" onClick={() => handleBulkAction('update-status')}>
              Update Status
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleBulkAction('schedule-maintenance')}>
              Schedule Maintenance
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleBulkAction('export-selected')}>
              Export Selected
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setSelectedTransformers([])}
            >
              Clear Selection
            </Button>
          </div>
        )}

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedTransformers.length === paginatedTransformers.length && paginatedTransformers.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>
                  <SortButton field="id">ID</SortButton>
                </TableHead>
                <TableHead>
                  <SortButton field="name">Name</SortButton>
                </TableHead>
                <TableHead>
                  <SortButton field="type">Type</SortButton>
                </TableHead>
                <TableHead>
                  <SortButton field="status">Status</SortButton>
                </TableHead>
                <TableHead>
                  <SortButton field="location.region">Region</SortButton>
                </TableHead>
                <TableHead>
                  <SortButton field="specifications.manufacturer">Manufacturer</SortButton>
                </TableHead>
                <TableHead>
                  <SortButton field="performance.efficiency">Efficiency</SortButton>
                </TableHead>
                <TableHead className="w-12">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                Array.from({ length: itemsPerPage }).map((_, i) => (
                  <TableRow key={i}>
                    <TableCell colSpan={9} className="text-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                      Loading transformers...
                    </TableCell>
                  </TableRow>
                ))
              ) : paginatedTransformers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                    No transformers found matching your criteria
                  </TableCell>
                </TableRow>
              ) : (
                paginatedTransformers.map((transformer) => (
                  <TableRow key={transformer.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedTransformers.includes(transformer.id)}
                        onCheckedChange={(checked) => handleSelectTransformer(transformer.id, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell className="font-mono text-sm">{transformer.id}</TableCell>
                    <TableCell className="font-medium">{transformer.name}</TableCell>
                    <TableCell>{transformer.type}</TableCell>
                    <TableCell>
                      <Badge className={`${getStatusColor(transformer.status)} flex items-center gap-1 w-fit`}>
                        {getStatusIcon(transformer.status)}
                        {transformer.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{transformer.location.region}</TableCell>
                    <TableCell>{transformer.specifications.manufacturer}</TableCell>
                    <TableCell>{transformer.performance.efficiency}%</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Settings className="h-4 w-4 mr-2" />
                            Configure
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-muted-foreground">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredAndSortedTransformers.length)} of {filteredAndSortedTransformers.length} transformers
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="text-sm">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
