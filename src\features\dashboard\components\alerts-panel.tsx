"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Alert, AlertDescription } from "@/src/components/ui/alert"
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  CheckCircle, 
  Clock,
  MapPin,
  Zap,
  Eye,
  MoreHorizontal
} from "lucide-react"

export function AlertsPanel() {
  const alerts = [
    {
      id: 1,
      type: "critical",
      title: "Transformer T-001 Overheating",
      description: "Temperature exceeded 85°C threshold",
      location: "Addis Ababa - Bole Substation",
      timestamp: "2 minutes ago",
      status: "active"
    },
    {
      id: 2,
      type: "warning",
      title: "Maintenance Due",
      description: "Scheduled maintenance for T-045 is overdue",
      location: "Dire Dawa - Industrial Zone",
      timestamp: "1 hour ago",
      status: "pending"
    },
    {
      id: 3,
      type: "info",
      title: "Load Balancing Optimized",
      description: "Automatic load redistribution completed",
      location: "Bahir Dar - Central Grid",
      timestamp: "3 hours ago",
      status: "resolved"
    },
    {
      id: 4,
      type: "warning",
      title: "Voltage Fluctuation Detected",
      description: "Irregular voltage patterns in sector 7",
      location: "Mekelle - North Distribution",
      timestamp: "5 hours ago",
      status: "investigating"
    }
  ]

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "critical":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case "warning":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case "info":
        return <Info className="h-4 w-4 text-blue-500" />
      default:
        return <CheckCircle className="h-4 w-4 text-green-500" />
    }
  }

  const getAlertBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="destructive">Active</Badge>
      case "pending":
        return <Badge variant="secondary">Pending</Badge>
      case "investigating":
        return <Badge variant="outline">Investigating</Badge>
      case "resolved":
        return <Badge variant="outline" className="text-green-600">Resolved</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            System Alerts
          </div>
          <Badge variant="outline">
            {alerts.filter(alert => alert.status === 'active').length} Active
          </Badge>
        </CardTitle>
        <CardDescription>
          Real-time system alerts and notifications
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {alerts.map((alert) => (
            <div key={alert.id} className="border rounded-lg p-4 space-y-2">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  {getAlertIcon(alert.type)}
                  <h4 className="font-medium">{alert.title}</h4>
                </div>
                <div className="flex items-center gap-2">
                  {getAlertBadge(alert.status)}
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <p className="text-sm text-muted-foreground">
                {alert.description}
              </p>
              
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {alert.location}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {alert.timestamp}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View All Alerts
            </Button>
            <div className="text-sm text-muted-foreground">
              Last updated: Just now
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
