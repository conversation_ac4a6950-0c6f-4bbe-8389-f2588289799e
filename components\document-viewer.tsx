"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { <PERSON>alog, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import {
  Download, X, ChevronLeft, ChevronRight, ZoomIn, ZoomOut,
  Maximize, Minimize, Printer, Share2, Bookmark, Star,
  Search, Copy, Info
} from "lucide-react"
import { useToast } from "@/src/components/ui/use-toast"
import type { Document } from "@/src/types/document"
import { documentationService } from "@/src/services/documentation-service"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/src/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu"

interface DocumentViewerProps {
  document: Document | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function DocumentViewer({ document, open, onOpenChange }: DocumentViewerProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [zoom, setZoom] = useState(100)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isFavorite, setIsFavorite] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [showInfo, setShowInfo] = useState(false)
  const documentRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  useEffect(() => {
    if (document && open) {
      setIsLoading(true)
      setCurrentPage(1)
      setTotalPages(document.pages || 1)

      // Check if document is in favorites or bookmarks
      const favorites = JSON.parse(localStorage.getItem("eeu_doc_favorites") || "[]")
      const bookmarks = JSON.parse(localStorage.getItem("eeu_doc_bookmarks") || "[]")

      setIsFavorite(favorites.includes(document.id))
      setIsBookmarked(bookmarks.includes(document.id))

      // Simulate loading the document
      const timer = setTimeout(() => {
        setIsLoading(false)
        // Increment view count
        if (document.id) {
          documentationService.incrementViewCount(document.id)
            .catch(error => console.error("Error incrementing view count:", error))
        }
      }, 1500)

      return () => clearTimeout(timer)
    }
  }, [document, open])

  // Add keyboard navigation
  useEffect(() => {
    if (!open) return

    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger if user is typing in an input
      if (e.target instanceof HTMLInputElement) return

      switch (e.key) {
        case "ArrowRight":
          handleNextPage()
          break
        case "ArrowLeft":
          handlePreviousPage()
          break
        case "f":
          if (e.ctrlKey) {
            e.preventDefault()
            setIsSearchOpen(true)
          }
          break
        case "+":
        case "=":
          if (e.ctrlKey) {
            e.preventDefault()
            handleZoomIn()
          }
          break
        case "-":
          if (e.ctrlKey) {
            e.preventDefault()
            handleZoomOut()
          }
          break
        case "0":
          if (e.ctrlKey) {
            e.preventDefault()
            setZoom(100)
          }
          break
        case "p":
          if (e.ctrlKey) {
            e.preventDefault()
            handlePrint()
          }
          break
        case "Escape":
          if (isSearchOpen) {
            setIsSearchOpen(false)
          } else if (isFullscreen) {
            setIsFullscreen(false)
          }
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [open, currentPage, totalPages, zoom, isFullscreen, isSearchOpen])

  const handleDownload = async () => {
    if (!document) return

    try {
      // Increment download count
      await documentationService.incrementDownloadCount(document.id)

      toast({
        title: "Download started",
        description: `${document.title} is being downloaded.`,
      })
    } catch (error) {
      console.error("Error downloading document:", error)
      toast({
        title: "Download failed",
        description: "There was an error downloading the document. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1)
    }
  }

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
    }
  }

  const handleZoomIn = () => {
    if (zoom < 200) {
      setZoom(zoom + 25)
    }
  }

  const handleZoomOut = () => {
    if (zoom > 50) {
      setZoom(zoom - 25)
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const handlePrint = useCallback(() => {
    if (!document) return

    toast({
      title: "Printing document",
      description: `Preparing ${document.title} for printing...`,
    })

    // In a real implementation, this would trigger the print dialog
    setTimeout(() => {
      window.print()
    }, 500)
  }, [document, toast])

  const handleShare = useCallback(() => {
    if (!document) return

    // Check if Web Share API is available
    if (navigator.share) {
      navigator.share({
        title: document.title,
        text: document.description,
        url: document.url,
      })
      .then(() => {
        toast({
          title: "Document shared",
          description: "The document has been shared successfully.",
        })
      })
      .catch((error) => {
        console.error("Error sharing document:", error)
        // Fallback to copy link if sharing fails
        handleCopyLink()
      })
    } else {
      // Fallback for browsers that don't support Web Share API
      handleCopyLink()
    }
  }, [document, toast])

  const handleCopyLink = useCallback(() => {
    if (!document) return

    navigator.clipboard.writeText(document.url)
      .then(() => {
        toast({
          title: "Link copied",
          description: "Document link has been copied to clipboard.",
        })
      })
      .catch((error) => {
        console.error("Error copying link:", error)
        toast({
          title: "Failed to copy link",
          description: "Please try again or share manually.",
          variant: "destructive",
        })
      })
  }, [document, toast])

  const toggleFavorite = useCallback(() => {
    if (!document) return

    const favorites = JSON.parse(localStorage.getItem("eeu_doc_favorites") || "[]")

    if (isFavorite) {
      // Remove from favorites
      const updatedFavorites = favorites.filter((id: string) => id !== document.id)
      localStorage.setItem("eeu_doc_favorites", JSON.stringify(updatedFavorites))
      setIsFavorite(false)
      toast({
        title: "Removed from favorites",
        description: `${document.title} has been removed from your favorites.`,
      })
    } else {
      // Add to favorites
      favorites.push(document.id)
      localStorage.setItem("eeu_doc_favorites", JSON.stringify(favorites))
      setIsFavorite(true)
      toast({
        title: "Added to favorites",
        description: `${document.title} has been added to your favorites.`,
      })
    }
  }, [document, isFavorite, toast])

  const toggleBookmark = useCallback(() => {
    if (!document) return

    const bookmarks = JSON.parse(localStorage.getItem("eeu_doc_bookmarks") || "[]")

    if (isBookmarked) {
      // Remove bookmark
      const updatedBookmarks = bookmarks.filter((id: string) => id !== document.id)
      localStorage.setItem("eeu_doc_bookmarks", JSON.stringify(updatedBookmarks))
      setIsBookmarked(false)
      toast({
        title: "Bookmark removed",
        description: `Bookmark for ${document.title} has been removed.`,
      })
    } else {
      // Add bookmark
      bookmarks.push(document.id)
      localStorage.setItem("eeu_doc_bookmarks", JSON.stringify(bookmarks))
      setIsBookmarked(true)
      toast({
        title: "Page bookmarked",
        description: `${document.title} has been bookmarked at page ${currentPage}.`,
      })
    }
  }, [document, isBookmarked, currentPage, toast])

  const toggleInfo = useCallback(() => {
    setShowInfo(!showInfo)
  }, [showInfo])

  const handleSearch = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    if (!searchQuery.trim()) return

    toast({
      title: "Search results",
      description: `Found 3 matches for "${searchQuery}" in document.`,
    })

    // In a real implementation, this would highlight search results in the document
  }, [searchQuery, toast])

  if (!document) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={`${isFullscreen ? 'max-w-full h-screen m-0 rounded-none' : 'max-w-4xl'} flex flex-col`}>
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <DialogTitle>{document.title}</DialogTitle>
            <DialogDescription>
              {document.category} • {document.language} • {document.pages} pages • Version {document.version}
            </DialogDescription>
          </div>
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={() => setIsSearchOpen(!isSearchOpen)}>
                    <Search className="h-4 w-4" />
                    <span className="sr-only">Search</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Search (Ctrl+F)</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handlePrint}>
                    <Printer className="h-4 w-4" />
                    <span className="sr-only">Print</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Print (Ctrl+P)</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleDownload}>
                    <Download className="h-4 w-4" />
                    <span className="sr-only">Download</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Download</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon">
                        <Share2 className="h-4 w-4" />
                        <span className="sr-only">Share</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={handleShare}>Share document</DropdownMenuItem>
                      <DropdownMenuItem onClick={handleCopyLink}>Copy link</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Share</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={toggleBookmark}
                    className={isBookmarked ? "text-blue-500" : ""}
                  >
                    <Bookmark className={`h-4 w-4 ${isBookmarked ? "fill-current" : ""}`} />
                    <span className="sr-only">{isBookmarked ? "Remove Bookmark" : "Bookmark"}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isBookmarked ? "Remove Bookmark" : "Add Bookmark"}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={toggleFavorite}
                    className={isFavorite ? "text-yellow-500" : ""}
                  >
                    <Star className={`h-4 w-4 ${isFavorite ? "fill-current" : ""}`} />
                    <span className="sr-only">{isFavorite ? "Remove from Favorites" : "Add to Favorites"}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isFavorite ? "Remove from Favorites" : "Add to Favorites"}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={toggleInfo}>
                    <Info className="h-4 w-4" />
                    <span className="sr-only">Document Info</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Document Info</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={toggleFullscreen}>
                    {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
                    <span className="sr-only">{isFullscreen ? "Exit Fullscreen" : "Fullscreen"}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isFullscreen ? "Exit Fullscreen" : "Fullscreen"}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={() => onOpenChange(false)}>
                    <X className="h-4 w-4" />
                    <span className="sr-only">Close</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Close</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </DialogHeader>

        {/* Search bar */}
        {isSearchOpen && (
          <div className="flex items-center gap-2 p-2 bg-muted/20 rounded-md">
            <form onSubmit={handleSearch} className="flex w-full gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search in document..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  autoFocus
                />
              </div>
              <Button type="submit" size="sm">Find</Button>
              <Button type="button" variant="outline" size="sm" onClick={() => setIsSearchOpen(false)}>
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </Button>
            </form>
          </div>
        )}

        <div className="flex-1 overflow-auto bg-muted/20 rounded-md my-4 relative">
          {isLoading ? (
            <div className="flex h-full w-full items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <>
              {/* Document info panel */}
              {showInfo && (
                <div className="absolute top-4 right-4 z-10 bg-white dark:bg-slate-900 shadow-lg rounded-md p-4 w-72 border">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-semibold">Document Information</h3>
                    <Button variant="ghost" size="icon" onClick={toggleInfo} className="h-6 w-6">
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">ID:</span>
                      <span>{document.id}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Author:</span>
                      <span>{document.author}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Version:</span>
                      <span>{document.version}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Last Updated:</span>
                      <span>{document.lastUpdated}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">File Size:</span>
                      <span>{document.fileSize}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">File Type:</span>
                      <span>{document.fileType}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Views:</span>
                      <span>{document.viewCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Downloads:</span>
                      <span>{document.downloadCount}</span>
                    </div>
                    <div className="pt-2">
                      <span className="text-muted-foreground">Tags:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {document.tags.map((tag) => (
                          <span key={tag} className="bg-muted px-2 py-0.5 rounded-full text-xs">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div
                className="h-full flex items-center justify-center p-4"
                style={{ transform: `scale(${zoom / 100})`, transition: 'transform 0.2s ease-in-out' }}
                ref={documentRef}
              >
                {/* Placeholder for document content */}
                <div className="bg-white shadow-lg p-8 w-[595px] h-[842px] flex flex-col">
                  <div className="text-2xl font-bold mb-4">{document.title}</div>
                  <div className="text-sm text-muted-foreground mb-2">Page {currentPage} of {totalPages}</div>
                  <div className="border-t pt-4 mb-4">
                    <h2 className="text-xl font-semibold mb-2">
                      {currentPage === 1 ? 'Introduction' : `Chapter ${currentPage - 1}`}
                    </h2>
                    <p className="mb-4">
                      {document.description}
                    </p>
                    <p className="mb-4">
                      This is placeholder content for page {currentPage} of the document. In a real implementation,
                      this would display the actual PDF content or document data.
                    </p>
                    <p>
                      Document ID: {document.id}<br />
                      Author: {document.author}<br />
                      Last Updated: {document.lastUpdated}<br />
                      File Size: {document.fileSize}
                    </p>
                  </div>
                  <div className="mt-auto text-center text-sm text-muted-foreground">
                    Ethiopia Electric Utility • {document.category} Documentation
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm" onClick={handleZoomOut} disabled={zoom <= 50}>
                    <ZoomOut className="h-4 w-4 mr-1" />
                    Zoom Out
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Zoom Out (Ctrl+-)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <span className="text-sm">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 font-normal"
                onClick={() => setZoom(100)}
              >
                {zoom}%
              </Button>
            </span>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm" onClick={handleZoomIn} disabled={zoom >= 200}>
                    <ZoomIn className="h-4 w-4 mr-1" />
                    Zoom In
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Zoom In (Ctrl++)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm" onClick={handlePreviousPage} disabled={currentPage <= 1}>
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Previous Page (Left Arrow)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <span className="text-sm">Page {currentPage} of {totalPages}</span>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm" onClick={handleNextPage} disabled={currentPage >= totalPages}>
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Next Page (Right Arrow)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}