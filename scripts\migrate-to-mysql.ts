#!/usr/bin/env tsx

/**
 * MySQL Migration Script
 * 
 * This script migrates all data from the JSON database to MySQL dtms_eeu_db database.
 * 
 * Usage:
 *   npm run migrate:mysql
 *   or
 *   npx tsx scripts/migrate-to-mysql.ts
 */

import { migrateAllDataToMySQL, verifyMigration } from '../lib/mysql-migration';
import { testConnection, getDatabaseInfo } from '../lib/mysql-connection';

async function main() {
  console.log('🚀 MySQL Migration Script for dtms_eeu_db');
  console.log('==========================================');
  console.log('');
  
  try {
    // Step 1: Test MySQL connection
    console.log('🔍 Step 1: Testing MySQL connection...');
    const isConnected = await testConnection();
    
    if (!isConnected) {
      console.error('❌ Cannot connect to MySQL database!');
      console.error('Please ensure:');
      console.error('  1. MySQL server is running');
      console.error('  2. Database credentials are correct in .env.local');
      console.error('  3. Database dtms_eeu_db exists or can be created');
      process.exit(1);
    }
    
    console.log('✅ MySQL connection successful');
    console.log('');
    
    // Step 2: Show database info
    console.log('📊 Step 2: Database information...');
    try {
      const dbInfo = await getDatabaseInfo();
      console.log(`   Database: ${dbInfo.database}`);
      console.log(`   Host: ${dbInfo.host}:${dbInfo.port}`);
      console.log(`   MySQL Version: ${dbInfo.version}`);
      console.log(`   Existing Tables: ${dbInfo.tables.length}`);
      
      if (dbInfo.tables.length > 0) {
        console.log('   Current tables:');
        dbInfo.tables.forEach(table => {
          console.log(`     - ${table.name}: ${table.rows} rows`);
        });
      }
    } catch (error) {
      console.log('   Database dtms_eeu_db will be created during migration');
    }
    console.log('');
    
    // Step 3: Confirm migration
    console.log('⚠️  Step 3: Migration confirmation');
    console.log('This will:');
    console.log('  1. Create/update MySQL schema in dtms_eeu_db');
    console.log('  2. Migrate all data from JSON database to MySQL');
    console.log('  3. Preserve existing data (uses INSERT ... ON DUPLICATE KEY UPDATE)');
    console.log('');
    
    // For automated execution, skip confirmation
    const args = process.argv.slice(2);
    const skipConfirmation = args.includes('--yes') || args.includes('-y');
    
    if (!skipConfirmation) {
      // In a real scenario, you might want to add readline for confirmation
      console.log('🔄 Proceeding with migration (use --yes to skip this message)...');
    }
    
    console.log('');
    
    // Step 4: Run migration
    console.log('🚀 Step 4: Running migration...');
    await migrateAllDataToMySQL();
    console.log('');
    
    // Step 5: Verify migration
    console.log('🔍 Step 5: Verifying migration...');
    const isVerified = await verifyMigration();
    console.log('');
    
    if (isVerified) {
      console.log('🎉 Migration completed successfully!');
      console.log('');
      console.log('✅ All data has been migrated to MySQL dtms_eeu_db');
      console.log('✅ Migration verification passed');
      console.log('');
      console.log('Next steps:');
      console.log('  1. Update your application to use MySQL database');
      console.log('  2. Test the application with MySQL data');
      console.log('  3. Consider backing up the MySQL database');
      console.log('');
      
      // Show final database statistics
      const finalDbInfo = await getDatabaseInfo();
      console.log('📊 Final database statistics:');
      console.log(`   Database: ${finalDbInfo.database}`);
      console.log(`   Tables: ${finalDbInfo.tables.length}`);
      console.log(`   Total records: ${finalDbInfo.tables.reduce((sum, table) => sum + table.rows, 0)}`);
      
    } else {
      console.log('❌ Migration verification failed!');
      console.log('Please check the logs above for details.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.error('');
    console.error('Troubleshooting:');
    console.error('  1. Check MySQL server is running');
    console.error('  2. Verify database credentials in .env.local');
    console.error('  3. Ensure MySQL user has CREATE/INSERT/UPDATE permissions');
    console.error('  4. Check MySQL error logs for more details');
    process.exit(1);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { main as runMigration };
