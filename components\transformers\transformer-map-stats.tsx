"use client"

import React from 'react'
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Zap,
  MapPin,
  TrendingUp,
  TrendingDown,
  Gauge,
  Thermometer,
  Battery,
  Settings
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Badge } from "@/src/components/ui/badge"
import { Progress } from "@/src/components/ui/progress"

interface TransformerMapStatsProps {
  totalCount: number
  filteredCount: number
  statusCounts: {
    operational: number
    warning: number
    maintenance: number
    critical: number
    burnt: number
    offline: number
  }
  regionCounts: Record<string, number>
  averageHealth: number
  maintenanceDue: number
  criticalAlerts: number
  isVisible: boolean
  onToggleVisibility: () => void
}

export function TransformerMapStats({
  totalCount,
  filteredCount,
  statusCounts,
  regionCounts,
  averageHealth,
  maintenanceDue,
  criticalAlerts,
  isVisible,
  onToggleVisibility
}: TransformerMapStatsProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return '#10b981'
      case 'warning': return '#f59e0b'
      case 'maintenance': return '#3b82f6'
      case 'critical': return '#ef4444'
      case 'burnt': return '#7c2d12'
      case 'offline': return '#6b7280'
      default: return '#6b7280'
    }
  }

  const getHealthColor = (health: number) => {
    if (health >= 80) return 'text-green-600'
    if (health >= 60) return 'text-yellow-600'
    if (health >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const getHealthBgColor = (health: number) => {
    if (health >= 80) return 'bg-green-100'
    if (health >= 60) return 'bg-yellow-100'
    if (health >= 40) return 'bg-orange-100'
    return 'bg-red-100'
  }

  const operationalPercentage = totalCount > 0 ? (statusCounts.operational / totalCount) * 100 : 0
  const criticalPercentage = totalCount > 0 ? ((statusCounts.critical + statusCounts.burnt) / totalCount) * 100 : 0

  if (!isVisible) {
    return (
      <div className="absolute top-4 right-4 z-[1000]">
        <Card className="w-64 shadow-lg cursor-pointer" onClick={onToggleVisibility}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Quick Stats</span>
              </div>
              <Badge variant="outline" className="text-xs">
                {filteredCount}/{totalCount}
              </Badge>
            </div>
            <div className="grid grid-cols-3 gap-2 mt-2">
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">{statusCounts.operational}</div>
                <div className="text-xs text-muted-foreground">Operational</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-red-600">{statusCounts.critical + statusCounts.burnt}</div>
                <div className="text-xs text-muted-foreground">Critical</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">{statusCounts.maintenance}</div>
                <div className="text-xs text-muted-foreground">Maintenance</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="absolute top-4 right-4 z-[1000] w-80 max-h-[calc(100vh-2rem)] overflow-y-auto">
      <div className="space-y-4">
        {/* Overview Card */}
        <Card className="shadow-xl border-2">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-600" />
                Transformer Overview
              </CardTitle>
              <button
                onClick={onToggleVisibility}
                className="text-muted-foreground hover:text-foreground"
              >
                ✕
              </button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Total Count */}
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Total Transformers</span>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-blue-600">{totalCount}</div>
                {filteredCount !== totalCount && (
                  <div className="text-xs text-muted-foreground">
                    {filteredCount} filtered
                  </div>
                )}
              </div>
            </div>

            {/* Health Overview */}
            <div className={`flex items-center justify-between p-3 rounded-lg ${getHealthBgColor(averageHealth)}`}>
              <div className="flex items-center gap-2">
                <Gauge className={`h-4 w-4 ${getHealthColor(averageHealth)}`} />
                <span className="text-sm font-medium">Average Health</span>
              </div>
              <div className="text-right">
                <div className={`text-lg font-bold ${getHealthColor(averageHealth)}`}>
                  {averageHealth.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">
                  System Health
                </div>
              </div>
            </div>

            {/* Operational Status */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Operational Status</span>
                <span className="text-xs text-muted-foreground">
                  {operationalPercentage.toFixed(1)}% operational
                </span>
              </div>
              <Progress value={operationalPercentage} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Status Breakdown */}
        <Card className="shadow-xl border-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              Status Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {Object.entries(statusCounts).map(([status, count]) => (
              <div key={status} className="flex items-center justify-between p-2 rounded hover:bg-gray-50">
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: getStatusColor(status) }}
                  />
                  <span className="text-sm capitalize">{status}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">{count}</span>
                  <span className="text-xs text-muted-foreground">
                    ({totalCount > 0 ? ((count / totalCount) * 100).toFixed(1) : 0}%)
                  </span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Alerts & Maintenance */}
        <Card className="shadow-xl border-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              Alerts & Maintenance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* Critical Alerts */}
            <div className="flex items-center justify-between p-2 bg-red-50 rounded">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium">Critical Alerts</span>
              </div>
              <Badge variant="destructive" className="text-xs">
                {criticalAlerts}
              </Badge>
            </div>

            {/* Maintenance Due */}
            <div className="flex items-center justify-between p-2 bg-yellow-50 rounded">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium">Maintenance Due</span>
              </div>
              <Badge variant="secondary" className="text-xs">
                {maintenanceDue}
              </Badge>
            </div>

            {/* Performance Indicator */}
            <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Performance</span>
              </div>
              <div className="flex items-center gap-1">
                {operationalPercentage >= 90 ? (
                  <TrendingUp className="h-3 w-3 text-green-600" />
                ) : operationalPercentage >= 70 ? (
                  <Activity className="h-3 w-3 text-yellow-600" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-600" />
                )}
                <span className="text-xs font-medium">
                  {operationalPercentage >= 90 ? 'Excellent' : 
                   operationalPercentage >= 70 ? 'Good' : 'Needs Attention'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Regional Distribution */}
        <Card className="shadow-xl border-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <MapPin className="h-4 w-4 text-purple-600" />
              Regional Distribution
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 max-h-48 overflow-y-auto">
            {Object.entries(regionCounts)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 8)
              .map(([region, count]) => (
                <div key={region} className="flex items-center justify-between p-1">
                  <span className="text-sm">{region}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-gray-200 rounded-full h-1.5">
                      <div
                        className="bg-purple-600 h-1.5 rounded-full"
                        style={{ 
                          width: `${totalCount > 0 ? (count / Math.max(...Object.values(regionCounts))) * 100 : 0}%` 
                        }}
                      />
                    </div>
                    <span className="text-xs font-medium w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
