import { NextRequest, NextResponse } from 'next/server'
import { getConnection } from '@/lib/mysql-connection'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Extract filter parameters
    const regions = searchParams.get('regions')?.split(',').filter(Boolean) || []
    const serviceCenters = searchParams.get('serviceCenters')?.split(',').filter(Boolean) || []
    const types = searchParams.get('types')?.split(',').filter(Boolean) || []
    const statuses = searchParams.get('statuses')?.split(',').filter(Boolean) || []
    const manufacturers = searchParams.get('manufacturers')?.split(',').filter(Boolean) || []
    const criticalities = searchParams.get('criticalities')?.split(',').filter(Boolean) || []
    const customerTypes = searchParams.get('customerTypes')?.split(',').filter(Boolean) || []
    
    // Range filters
    const capacityMin = parseFloat(searchParams.get('capacityMin') || '0')
    const capacityMax = parseFloat(searchParams.get('capacityMax') || '2000')
    const efficiencyMin = parseFloat(searchParams.get('efficiencyMin') || '90')
    const efficiencyMax = parseFloat(searchParams.get('efficiencyMax') || '100')
    const temperatureMin = parseFloat(searchParams.get('temperatureMin') || '0')
    const temperatureMax = parseFloat(searchParams.get('temperatureMax') || '100')
    const loadFactorMin = parseFloat(searchParams.get('loadFactorMin') || '0')
    const loadFactorMax = parseFloat(searchParams.get('loadFactorMax') || '100')
    const assetValueMin = parseFloat(searchParams.get('assetValueMin') || '0')
    const assetValueMax = parseFloat(searchParams.get('assetValueMax') || '500000')
    
    // Date filters
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    
    // Maintenance filters
    const maintenanceTypes = searchParams.get('maintenanceTypes')?.split(',').filter(Boolean) || []
    const maintenanceStatuses = searchParams.get('maintenanceStatuses')?.split(',').filter(Boolean) || []
    const maintenancePriorities = searchParams.get('maintenancePriorities')?.split(',').filter(Boolean) || []
    
    // Alert filters
    const alertSeverities = searchParams.get('alertSeverities')?.split(',').filter(Boolean) || []
    const alertTypes = searchParams.get('alertTypes')?.split(',').filter(Boolean) || []
    const alertStatuses = searchParams.get('alertStatuses')?.split(',').filter(Boolean) || []
    
    // Search
    const search = searchParams.get('search') || ''

    const connection = await getConnection()

    // Build transformer query with filters
    let transformerQuery = `
      SELECT 
        t.*,
        r.name as region_name,
        r.code as region_code,
        sc.name as service_center_name,
        sc.code as service_center_code
      FROM dtms_transformers t
      LEFT JOIN dtms_regions r ON t.region_id = r.id
      LEFT JOIN dtms_service_centers sc ON t.service_center_id = sc.id
      WHERE 1=1
    `
    const transformerParams: any[] = []

    // Apply filters
    if (regions.length > 0) {
      transformerQuery += ` AND r.code IN (${regions.map(() => '?').join(',')})`
      transformerParams.push(...regions)
    }

    if (serviceCenters.length > 0) {
      transformerQuery += ` AND sc.code IN (${serviceCenters.map(() => '?').join(',')})`
      transformerParams.push(...serviceCenters)
    }

    if (types.length > 0) {
      transformerQuery += ` AND t.type IN (${types.map(() => '?').join(',')})`
      transformerParams.push(...types)
    }

    if (statuses.length > 0) {
      transformerQuery += ` AND t.status IN (${statuses.map(() => '?').join(',')})`
      transformerParams.push(...statuses)
    }

    if (manufacturers.length > 0) {
      transformerQuery += ` AND t.manufacturer IN (${manufacturers.map(() => '?').join(',')})`
      transformerParams.push(...manufacturers)
    }

    if (criticalities.length > 0) {
      transformerQuery += ` AND t.criticality IN (${criticalities.map(() => '?').join(',')})`
      transformerParams.push(...criticalities)
    }

    if (customerTypes.length > 0) {
      transformerQuery += ` AND t.customer_type IN (${customerTypes.map(() => '?').join(',')})`
      transformerParams.push(...customerTypes)
    }

    // Range filters
    transformerQuery += ` AND t.capacity_kva BETWEEN ? AND ?`
    transformerParams.push(capacityMin, capacityMax)

    transformerQuery += ` AND t.efficiency_rating BETWEEN ? AND ?`
    transformerParams.push(efficiencyMin, efficiencyMax)

    transformerQuery += ` AND t.temperature BETWEEN ? AND ?`
    transformerParams.push(temperatureMin, temperatureMax)

    transformerQuery += ` AND t.load_factor BETWEEN ? AND ?`
    transformerParams.push(loadFactorMin, loadFactorMax)

    transformerQuery += ` AND t.asset_value BETWEEN ? AND ?`
    transformerParams.push(assetValueMin, assetValueMax)

    // Date filters
    if (dateFrom) {
      transformerQuery += ` AND t.installation_date >= ?`
      transformerParams.push(dateFrom)
    }

    if (dateTo) {
      transformerQuery += ` AND t.installation_date <= ?`
      transformerParams.push(dateTo)
    }

    // Search filter
    if (search) {
      transformerQuery += ` AND (
        t.name LIKE ? OR 
        t.serial_number LIKE ? OR 
        t.location_name LIKE ? OR
        r.name LIKE ? OR
        sc.name LIKE ?
      )`
      const searchPattern = `%${search}%`
      transformerParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern)
    }

    transformerQuery += ` ORDER BY t.name`

    // Execute transformer query
    const [transformers] = await connection.execute(transformerQuery, transformerParams)

    // Get transformer IDs for related data
    const transformerIds = (transformers as any[]).map(t => t.id)

    let alerts: any[] = []
    let maintenance: any[] = []
    let performance: any[] = []

    if (transformerIds.length > 0) {
      // Build alerts query
      let alertQuery = `
        SELECT 
          a.*,
          t.name as transformer_name,
          t.serial_number as transformer_serial
        FROM dtms_alerts a
        LEFT JOIN dtms_transformers t ON a.transformer_id = t.id
        WHERE a.transformer_id IN (${transformerIds.map(() => '?').join(',')})
      `
      const alertParams = [...transformerIds]

      if (alertSeverities.length > 0) {
        alertQuery += ` AND a.severity IN (${alertSeverities.map(() => '?').join(',')})`
        alertParams.push(...alertSeverities)
      }

      if (alertTypes.length > 0) {
        alertQuery += ` AND a.type IN (${alertTypes.map(() => '?').join(',')})`
        alertParams.push(...alertTypes)
      }

      if (alertStatuses.length > 0) {
        alertQuery += ` AND a.status IN (${alertStatuses.map(() => '?').join(',')})`
        alertParams.push(...alertStatuses)
      }

      alertQuery += ` ORDER BY a.created_at DESC`

      // Build maintenance query
      let maintenanceQuery = `
        SELECT 
          m.*,
          t.name as transformer_name,
          t.serial_number as transformer_serial
        FROM dtms_maintenance_schedules m
        LEFT JOIN dtms_transformers t ON m.transformer_id = t.id
        WHERE m.transformer_id IN (${transformerIds.map(() => '?').join(',')})
      `
      const maintenanceParams = [...transformerIds]

      if (maintenanceTypes.length > 0) {
        maintenanceQuery += ` AND m.type IN (${maintenanceTypes.map(() => '?').join(',')})`
        maintenanceParams.push(...maintenanceTypes)
      }

      if (maintenanceStatuses.length > 0) {
        maintenanceQuery += ` AND m.status IN (${maintenanceStatuses.map(() => '?').join(',')})`
        maintenanceParams.push(...maintenanceStatuses)
      }

      if (maintenancePriorities.length > 0) {
        maintenanceQuery += ` AND m.priority IN (${maintenancePriorities.map(() => '?').join(',')})`
        maintenanceParams.push(...maintenancePriorities)
      }

      maintenanceQuery += ` ORDER BY m.scheduled_date DESC`

      // Build performance query
      const performanceQuery = `
        SELECT 
          p.*,
          t.name as transformer_name,
          t.serial_number as transformer_serial
        FROM dtms_performance_metrics p
        LEFT JOIN dtms_transformers t ON p.transformer_id = t.id
        WHERE p.transformer_id IN (${transformerIds.map(() => '?').join(',')})
        ORDER BY p.recorded_at DESC
        LIMIT 1000
      `

      // Execute queries
      try {
        [alerts] = await connection.execute(alertQuery, alertParams)
      } catch (error) {
        console.error('Error fetching alerts:', error)
        alerts = []
      }

      try {
        [maintenance] = await connection.execute(maintenanceQuery, maintenanceParams)
      } catch (error) {
        console.error('Error fetching maintenance:', error)
        maintenance = []
      }

      try {
        [performance] = await connection.execute(performanceQuery, transformerIds)
      } catch (error) {
        console.error('Error fetching performance:', error)
        performance = []
      }
    }

    // Get regions and service centers data
    const [regionsData] = await connection.execute(`
      SELECT r.*, COUNT(t.id) as transformer_count
      FROM dtms_regions r
      LEFT JOIN dtms_transformers t ON r.id = t.region_id
      GROUP BY r.id
      ORDER BY r.name
    `)

    const [serviceCentersData] = await connection.execute(`
      SELECT sc.*, COUNT(t.id) as transformer_count
      FROM dtms_service_centers sc
      LEFT JOIN dtms_transformers t ON sc.id = t.service_center_id
      GROUP BY sc.id
      ORDER BY sc.name
    `)

    // Calculate summary statistics
    const transformerArray = transformers as any[]
    const alertArray = alerts as any[]
    const maintenanceArray = maintenance as any[]

    const summary = {
      totalTransformers: transformerArray.length,
      operationalCount: transformerArray.filter(t => t.status === 'operational').length,
      warningCount: transformerArray.filter(t => t.status === 'warning').length,
      criticalCount: transformerArray.filter(t => t.status === 'critical').length,
      maintenanceCount: transformerArray.filter(t => t.status === 'maintenance').length,
      avgEfficiency: transformerArray.length > 0 
        ? transformerArray.reduce((sum, t) => sum + (parseFloat(t.efficiency_rating) || 0), 0) / transformerArray.length
        : 0,
      avgLoadFactor: transformerArray.length > 0
        ? transformerArray.reduce((sum, t) => sum + (parseFloat(t.load_factor) || 0), 0) / transformerArray.length
        : 0,
      avgTemperature: transformerArray.length > 0
        ? transformerArray.reduce((sum, t) => sum + (parseFloat(t.temperature) || 0), 0) / transformerArray.length
        : 0,
      totalAssetValue: transformerArray.reduce((sum, t) => sum + (parseFloat(t.asset_value) || 0), 0),
      activeAlerts: alertArray.filter(a => a.status === 'active').length,
      pendingMaintenance: maintenanceArray.filter(m => m.status === 'scheduled').length
    }

    await connection.end()

    return NextResponse.json({
      transformers: transformerArray,
      alerts: alertArray,
      maintenance: maintenanceArray,
      performance: performance,
      regions: regionsData,
      serviceCenters: serviceCentersData,
      summary
    })

  } catch (error) {
    console.error('Error fetching filtered data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch filtered data' },
      { status: 500 }
    )
  }
}
