"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/src/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Button } from "@/src/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { transformerService } from "@/src/services/transformer-service"
import type { Transformer, TransformerStatistics } from "@/src/types/transformer"
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line
} from "recharts"
import {
  Download,
  FileText,
  Filter,
  Refresh<PERSON><PERSON>,
  Flame,
  Bar<PERSON>hart2,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  Map,
  Calendar,
  Clock
} from "lucide-react"
import { format } from "date-fns"
import { Badge } from "@/src/components/ui/badge"
import { useToast } from "@/src/components/ui/use-toast"
import { ChartContainer, ChartTooltipContent } from "@/src/components/ui/chart"

export function BurntTransformersReport() {
  const [statistics, setStatistics] = useState<TransformerStatistics | null>(null)
  const [burntTransformers, setBurntTransformers] = useState<Transformer[]>([])
  const [loading, setLoading] = useState(true)
  const [regionFilter, setRegionFilter] = useState("all")
  const [manufacturerFilter, setManufacturerFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [voltageLevelFilter, setVoltageLevelFilter] = useState("all")
  const [yearFilter, setYearFilter] = useState("all")
  const [serviceCenterFilter, setServiceCenterFilter] = useState("all")
  const [failureCauseFilter, setFailureCauseFilter] = useState("all")
  const [verificationMethodFilter, setVerificationMethodFilter] = useState("all")
  const [workshopReferralFilter, setWorkshopReferralFilter] = useState("all")
  const [replacementStatusFilter, setReplacementStatusFilter] = useState("all")
  const [meggerTestResultFilter, setMeggerTestResultFilter] = useState("all")
  const [abnormalityPriorityFilter, setAbnormalityPriorityFilter] = useState("all")
  const [lastUpdated, setLastUpdated] = useState(new Date())
  const { toast } = useToast()

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658', '#8dd1e1', '#a4de6c', '#d0ed57'];

  // Fetch data
  const fetchData = async () => {
    setLoading(true)
    try {
      // Get statistics
      const stats = await transformerService.getTransformerStatistics()
      setStatistics(stats)

      // Get burnt transformers with filters
      let transformers = await transformerService.getBurntTransformers()

      // Apply filters
      if (regionFilter !== "all") {
        transformers = transformers.filter(t => t.location.region === regionFilter)
      }
      if (manufacturerFilter !== "all") {
        transformers = transformers.filter(t => t.manufacturer === manufacturerFilter)
      }
      if (typeFilter !== "all") {
        transformers = transformers.filter(t => t.type === typeFilter)
      }
      if (voltageLevelFilter !== "all") {
        transformers = transformers.filter(t => t.voltageRating && t.voltageRating.includes(voltageLevelFilter))
      }
      if (yearFilter !== "all") {
        transformers = transformers.filter(t => t.manufacturingYear === yearFilter)
      }
      if (serviceCenterFilter !== "all") {
        transformers = transformers.filter(t => t.location.serviceCenter === serviceCenterFilter)
      }
      if (failureCauseFilter !== "all") {
        transformers = transformers.filter(t => t.failureCause && t.failureCause.includes(failureCauseFilter))
      }

      // Apply workflow-related filters
      if (verificationMethodFilter !== "all") {
        transformers = transformers.filter(t =>
          t.failureVerificationMethod &&
          t.failureVerificationMethod === verificationMethodFilter
        )
      }

      if (workshopReferralFilter !== "all") {
        if (workshopReferralFilter === "referred") {
          transformers = transformers.filter(t => t.workshopReferral === true)
        } else if (workshopReferralFilter === "notReferred") {
          transformers = transformers.filter(t => t.workshopReferral === false)
        }
      }

      if (replacementStatusFilter !== "all") {
        if (replacementStatusFilter === "replaced") {
          transformers = transformers.filter(t => !!t.replacementDate)
        } else if (replacementStatusFilter === "notReplaced") {
          transformers = transformers.filter(t => !t.replacementDate)
        }
      }

      if (meggerTestResultFilter !== "all") {
        transformers = transformers.filter(t =>
          t.meggerTests &&
          t.meggerTests.length > 0 &&
          t.meggerTests[t.meggerTests.length - 1].result === meggerTestResultFilter
        )
      }

      if (abnormalityPriorityFilter !== "all") {
        transformers = transformers.filter(t =>
          t.abnormalityReports &&
          t.abnormalityReports.length > 0 &&
          t.abnormalityReports[t.abnormalityReports.length - 1].priority === abnormalityPriorityFilter
        )
      }

      setBurntTransformers(transformers)
      setLastUpdated(new Date())
    } catch (error) {
      console.error("Error fetching data:", error)
      toast({
        title: "Error",
        description: "Failed to fetch transformer data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Initial data fetch
  useEffect(() => {
    fetchData()
  }, [])

  // Fetch data when filters change
  useEffect(() => {
    fetchData()
  }, [
    regionFilter,
    manufacturerFilter,
    typeFilter,
    voltageLevelFilter,
    yearFilter,
    serviceCenterFilter,
    failureCauseFilter,
    verificationMethodFilter,
    workshopReferralFilter,
    replacementStatusFilter,
    meggerTestResultFilter,
    abnormalityPriorityFilter
  ])

  // Prepare chart data
  const prepareRegionChartData = () => {
    if (!statistics) return []
    return Object.entries(statistics.burntTransformers.byRegion).map(([name, value]) => ({
      name,
      value
    }))
  }

  const prepareManufacturerChartData = () => {
    if (!statistics) return []
    return Object.entries(statistics.burntTransformers.byManufacturer).map(([name, value]) => ({
      name,
      value
    }))
  }

  const prepareTypeChartData = () => {
    if (!statistics) return []
    return Object.entries(statistics.burntTransformers.byType).map(([name, value]) => ({
      name,
      value
    }))
  }

  const prepareVoltageLevelChartData = () => {
    if (!statistics) return []
    return Object.entries(statistics.burntTransformers.byVoltageLevel).map(([name, value]) => ({
      name,
      value
    }))
  }

  const prepareYearChartData = () => {
    if (!statistics) return []
    return Object.entries(statistics.burntTransformers.byYear)
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([name, value]) => ({
        name,
        value
      }))
  }

  const prepareServiceCenterChartData = () => {
    if (!statistics) return []
    return Object.entries(statistics.burntTransformers.byServiceCenter).map(([name, value]) => ({
      name,
      value
    }))
  }

  const prepareFailureCauseChartData = () => {
    if (!statistics) return []
    return Object.entries(statistics.burntTransformers.byFailureCause).map(([name, value]) => ({
      name,
      value
    }))
  }

  const prepareVerificationMethodChartData = () => {
    if (!statistics) return []
    return Object.entries(statistics.burntTransformers.byVerificationMethod).map(([name, value]) => ({
      name,
      value
    }))
  }

  const prepareWorkshopReferralChartData = () => {
    if (!statistics) return []
    return [
      { name: "Referred to Workshop", value: statistics.burntTransformers.byWorkshopReferral.referred },
      { name: "Not Referred", value: statistics.burntTransformers.byWorkshopReferral.notReferred }
    ]
  }

  const prepareReplacementStatusChartData = () => {
    if (!statistics) return []
    return [
      { name: "Replaced", value: statistics.burntTransformers.byReplacementStatus.replaced },
      { name: "Not Replaced", value: statistics.burntTransformers.byReplacementStatus.notReplaced }
    ]
  }

  const prepareMeggerTestResultChartData = () => {
    if (!statistics) return []
    return Object.entries(statistics.burntTransformers.byMeggerTestResult).map(([name, value]) => ({
      name,
      value
    }))
  }

  const prepareAbnormalityPriorityChartData = () => {
    if (!statistics) return []
    return Object.entries(statistics.burntTransformers.byAbnormalityReportPriority).map(([name, value]) => ({
      name,
      value
    }))
  }

  // Export data to CSV
  const exportToCSV = () => {
    if (!burntTransformers.length) {
      toast({
        title: "No data to export",
        description: "There are no burnt transformers matching your filters",
        variant: "destructive",
      })
      return
    }

    // Create CSV header
    const headers = [
      "ID",
      "Serial Number",
      "Manufacturer",
      "Model",
      "Type",
      "Capacity",
      "Voltage Rating",
      "Installation Date",
      "Region",
      "Service Center",
      "Failure Date",
      "Failure Cause",
      "Manufacturing Year",
      "Failure Verified By",
      "Verification Method",
      "Workshop Referral",
      "Workshop Referral Date",
      "Workshop Referral Number",
      "Replacement Date",
      "Abnormality Report Date",
      "Abnormality Reported By",
      "Abnormality Priority",
      "Megger Test Date",
      "Megger Test Result",
      "Megger Test Recommendation"
    ].join(",")

    // Create CSV rows
    const rows = burntTransformers.map(transformer => [
      transformer.id,
      transformer.serialNumber,
      transformer.manufacturer,
      transformer.model,
      transformer.type,
      transformer.capacity,
      transformer.voltageRating || "",
      transformer.installationDate,
      transformer.location.region,
      transformer.location.serviceCenter || "",
      transformer.failureDate || "",
      transformer.failureCause || "",
      transformer.manufacturingYear || "",
      transformer.failureVerifiedBy || "",
      transformer.failureVerificationMethod || "",
      transformer.workshopReferral !== undefined ? (transformer.workshopReferral ? "Yes" : "No") : "",
      transformer.workshopReferralDate || "",
      transformer.workshopReferralNumber || "",
      transformer.replacementDate || "",
      transformer.abnormalityReports && transformer.abnormalityReports.length > 0 ? transformer.abnormalityReports[0].reportDate : "",
      transformer.abnormalityReports && transformer.abnormalityReports.length > 0 ? transformer.abnormalityReports[0].reportedBy : "",
      transformer.abnormalityReports && transformer.abnormalityReports.length > 0 ? transformer.abnormalityReports[0].priority : "",
      transformer.meggerTests && transformer.meggerTests.length > 0 ? transformer.meggerTests[0].testDate : "",
      transformer.meggerTests && transformer.meggerTests.length > 0 ? transformer.meggerTests[0].result : "",
      transformer.meggerTests && transformer.meggerTests.length > 0 ? transformer.meggerTests[0].recommendation : ""
    ].join(","))

    // Combine header and rows
    const csv = [headers, ...rows].join("\n")

    // Create download link
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `burnt_transformers_report_${format(new Date(), "yyyy-MM-dd")}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: "Export successful",
      description: "Burnt transformers report has been exported to CSV",
    })
  }

  // Render loading state
  if (loading && !statistics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Burnt Transformers Report</CardTitle>
          <CardDescription>Loading report data...</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[400px]">
          <div className="flex flex-col items-center gap-2">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="text-sm text-muted-foreground">Loading report data...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="text-2xl font-bold">Burnt Transformers Report</CardTitle>
            <CardDescription>
              Analysis of burnt transformers across the network
            </CardDescription>
            <div className="flex items-center mt-1 text-xs text-muted-foreground">
              <Clock className="h-3 w-3 mr-1" />
              <span>Last updated: {format(lastUpdated, "MMM d, yyyy h:mm a")}</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={fetchData} disabled={loading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={exportToCSV}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card className="bg-red-50 dark:bg-red-900/10 border-red-200 dark:border-red-900/20">
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm font-medium text-red-700 dark:text-red-400">Total Burnt Transformers</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="text-2xl font-bold text-red-700 dark:text-red-400">
                  {statistics?.burntTransformers.total || 0}
                </div>
                <p className="text-xs text-red-600 dark:text-red-400/80">
                  {statistics ? `${((statistics.burntTransformers.total / statistics.total) * 100).toFixed(1)}% of all transformers` : "Loading..."}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm font-medium">Top Failure Cause</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                {statistics && Object.entries(statistics.burntTransformers.byFailureCause).length > 0 ? (
                  <>
                    <div className="text-2xl font-bold">
                      {Object.entries(statistics.burntTransformers.byFailureCause)
                        .sort((a, b) => b[1] - a[1])[0][0]}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {Object.entries(statistics.burntTransformers.byFailureCause)
                        .sort((a, b) => b[1] - a[1])[0][1]} transformers affected
                    </p>
                  </>
                ) : (
                  <div className="text-sm text-muted-foreground">No data available</div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm font-medium">Most Affected Region</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                {statistics && Object.entries(statistics.burntTransformers.byRegion).length > 0 ? (
                  <>
                    <div className="text-2xl font-bold">
                      {Object.entries(statistics.burntTransformers.byRegion)
                        .sort((a, b) => b[1] - a[1])[0][0]}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {Object.entries(statistics.burntTransformers.byRegion)
                        .sort((a, b) => b[1] - a[1])[0][1]} transformers affected
                    </p>
                  </>
                ) : (
                  <div className="text-sm text-muted-foreground">No data available</div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card className="bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-900/20">
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-400">Abnormality Reports</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="text-2xl font-bold text-blue-700 dark:text-blue-400">
                  {statistics?.abnormalityReports.total || 0}
                </div>
                <p className="text-xs text-blue-600 dark:text-blue-400/80">
                  From service center engineers
                </p>
              </CardContent>
            </Card>

            <Card className="bg-amber-50 dark:bg-amber-900/10 border-amber-200 dark:border-amber-900/20">
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm font-medium text-amber-700 dark:text-amber-400">Megger Tests</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="text-2xl font-bold text-amber-700 dark:text-amber-400">
                  {statistics?.meggerTests.total || 0}
                </div>
                <p className="text-xs text-amber-600 dark:text-amber-400/80">
                  Performed by switchgear teams
                </p>
              </CardContent>
            </Card>

            <Card className="bg-green-50 dark:bg-green-900/10 border-green-200 dark:border-green-900/20">
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm font-medium text-green-700 dark:text-green-400">Workshop Referrals</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="text-2xl font-bold text-green-700 dark:text-green-400">
                  {statistics?.burntTransformers.byWorkshopReferral.referred || 0}
                </div>
                <p className="text-xs text-green-600 dark:text-green-400/80">
                  {statistics ? `${((statistics.burntTransformers.byWorkshopReferral.referred / statistics.burntTransformers.total) * 100).toFixed(1)}% of burnt transformers` : "Loading..."}
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="w-full md:w-1/4 space-y-4">
              <div>
                <label className="text-sm font-medium">Region</label>
                <Select value={regionFilter} onValueChange={setRegionFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Regions</SelectItem>
                    {statistics && Object.keys(statistics.byRegion).map(region => (
                      <SelectItem key={region} value={region}>{region}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Manufacturer</label>
                <Select value={manufacturerFilter} onValueChange={setManufacturerFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select manufacturer" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Manufacturers</SelectItem>
                    {statistics && Object.keys(statistics.byManufacturer).map(manufacturer => (
                      <SelectItem key={manufacturer} value={manufacturer}>{manufacturer}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Type</label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {statistics && Object.keys(statistics.byType).map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Voltage Level</label>
                <Select value={voltageLevelFilter} onValueChange={setVoltageLevelFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select voltage level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Voltage Levels</SelectItem>
                    {statistics && Object.keys(statistics.byVoltageLevel).map(voltageLevel => (
                      <SelectItem key={voltageLevel} value={voltageLevel}>{voltageLevel}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Manufacturing Year</label>
                <Select value={yearFilter} onValueChange={setYearFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Years</SelectItem>
                    {statistics && Object.keys(statistics.byYear).map(year => (
                      <SelectItem key={year} value={year}>{year}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Service Center</label>
                <Select value={serviceCenterFilter} onValueChange={setServiceCenterFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select service center" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Service Centers</SelectItem>
                    {statistics && Object.keys(statistics.byServiceCenter).map(serviceCenter => (
                      <SelectItem key={serviceCenter} value={serviceCenter}>{serviceCenter}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Failure Cause</label>
                <Select value={failureCauseFilter} onValueChange={setFailureCauseFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select failure cause" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Failure Causes</SelectItem>
                    {statistics && statistics.burntTransformers.byFailureCause &&
                      Object.keys(statistics.burntTransformers.byFailureCause).map(cause => (
                        <SelectItem key={cause} value={cause}>{cause}</SelectItem>
                      ))
                    }
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Verification Method</label>
                <Select value={verificationMethodFilter} onValueChange={setVerificationMethodFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select verification method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Methods</SelectItem>
                    {statistics && statistics.burntTransformers.byVerificationMethod &&
                      Object.keys(statistics.burntTransformers.byVerificationMethod).map(method => (
                        <SelectItem key={method} value={method}>{method}</SelectItem>
                      ))
                    }
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Workshop Referral</label>
                <Select value={workshopReferralFilter} onValueChange={setWorkshopReferralFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select referral status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Referral Status</SelectItem>
                    <SelectItem value="referred">Referred to Workshop</SelectItem>
                    <SelectItem value="notReferred">Not Referred</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Replacement Status</label>
                <Select value={replacementStatusFilter} onValueChange={setReplacementStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select replacement status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Replacement Status</SelectItem>
                    <SelectItem value="replaced">Replaced</SelectItem>
                    <SelectItem value="notReplaced">Not Replaced</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Megger Test Result</label>
                <Select value={meggerTestResultFilter} onValueChange={setMeggerTestResultFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select test result" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Test Results</SelectItem>
                    {statistics && statistics.burntTransformers.byMeggerTestResult &&
                      Object.keys(statistics.burntTransformers.byMeggerTestResult).map(result => (
                        <SelectItem key={result} value={result}>{result}</SelectItem>
                      ))
                    }
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Abnormality Priority</label>
                <Select value={abnormalityPriorityFilter} onValueChange={setAbnormalityPriorityFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priorities</SelectItem>
                    {statistics && statistics.burntTransformers.byAbnormalityReportPriority &&
                      Object.keys(statistics.burntTransformers.byAbnormalityReportPriority).map(priority => (
                        <SelectItem key={priority} value={priority}>{priority}</SelectItem>
                      ))
                    }
                  </SelectContent>
                </Select>
              </div>

              <Button
                variant="outline"
                className="w-full"
                onClick={() => {
                  setRegionFilter("all")
                  setManufacturerFilter("all")
                  setTypeFilter("all")
                  setVoltageLevelFilter("all")
                  setYearFilter("all")
                  setServiceCenterFilter("all")
                  setFailureCauseFilter("all")
                  setVerificationMethodFilter("all")
                  setWorkshopReferralFilter("all")
                  setReplacementStatusFilter("all")
                  setMeggerTestResultFilter("all")
                  setAbnormalityPriorityFilter("all")
                }}
              >
                <Filter className="mr-2 h-4 w-4" />
                Reset Filters
              </Button>
            </div>

            <div className="w-full md:w-3/4">
              <Tabs defaultValue="charts">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="charts" className="flex items-center gap-1">
                    <BarChart2 className="h-4 w-4" />
                    <span>Charts</span>
                  </TabsTrigger>
                  <TabsTrigger value="list" className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    <span>List View</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="charts" className="space-y-4 mt-4">
                  <Tabs defaultValue="region">
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="region">By Region</TabsTrigger>
                      <TabsTrigger value="manufacturer">By Manufacturer</TabsTrigger>
                      <TabsTrigger value="type">By Type</TabsTrigger>
                      <TabsTrigger value="cause">By Failure Cause</TabsTrigger>
                    </TabsList>

                    <div className="mt-2">
                      <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="verification">By Verification</TabsTrigger>
                        <TabsTrigger value="workshop">Workshop Referral</TabsTrigger>
                        <TabsTrigger value="replacement">Replacement Status</TabsTrigger>
                        <TabsTrigger value="workflow">Workflow Analysis</TabsTrigger>
                      </TabsList>
                    </div>

                    <TabsContent value="region" className="mt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Burnt Transformers by Region</CardTitle>
                          <CardDescription>Distribution of burnt transformers across different regions</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[400px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={prepareRegionChartData()}
                                margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                                <XAxis dataKey="name" angle={-45} textAnchor="end" height={70} />
                                <YAxis />
                                <Tooltip formatter={(value) => [`${value} transformers`, "Count"]} />
                                <Legend />
                                <Bar dataKey="value" name="Burnt Transformers" fill="#ef4444" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="manufacturer" className="mt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Burnt Transformers by Manufacturer</CardTitle>
                          <CardDescription>Distribution of burnt transformers by manufacturer</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[400px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={prepareManufacturerChartData()}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={true}
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                  outerRadius={150}
                                  fill="#8884d8"
                                  dataKey="value"
                                  nameKey="name"
                                >
                                  {prepareManufacturerChartData().map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                  ))}
                                </Pie>
                                <Tooltip formatter={(value) => [`${value} transformers`, "Count"]} />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="type" className="mt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Burnt Transformers by Type</CardTitle>
                          <CardDescription>Distribution of burnt transformers by transformer type</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[400px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={prepareTypeChartData()}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={true}
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                  outerRadius={150}
                                  fill="#8884d8"
                                  dataKey="value"
                                  nameKey="name"
                                >
                                  {prepareTypeChartData().map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                  ))}
                                </Pie>
                                <Tooltip formatter={(value) => [`${value} transformers`, "Count"]} />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="cause" className="mt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Burnt Transformers by Failure Cause</CardTitle>
                          <CardDescription>Distribution of burnt transformers by failure cause</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[400px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart
                                data={prepareFailureCauseChartData()}
                                margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                                <XAxis dataKey="name" angle={-45} textAnchor="end" height={70} />
                                <YAxis />
                                <Tooltip formatter={(value) => [`${value} transformers`, "Count"]} />
                                <Legend />
                                <Bar dataKey="value" name="Burnt Transformers" fill="#ef4444" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="verification" className="mt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Burnt Transformers by Verification Method</CardTitle>
                          <CardDescription>Distribution of burnt transformers by verification method</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[400px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={prepareVerificationMethodChartData()}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={true}
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                  outerRadius={150}
                                  fill="#8884d8"
                                  dataKey="value"
                                  nameKey="name"
                                >
                                  {prepareVerificationMethodChartData().map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                  ))}
                                </Pie>
                                <Tooltip formatter={(value) => [`${value} transformers`, "Count"]} />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="workshop" className="mt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Burnt Transformers by Workshop Referral</CardTitle>
                          <CardDescription>Distribution of burnt transformers by workshop referral status</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[400px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={prepareWorkshopReferralChartData()}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={true}
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                  outerRadius={150}
                                  fill="#8884d8"
                                  dataKey="value"
                                  nameKey="name"
                                >
                                  <Cell fill="#ef4444" />
                                  <Cell fill="#3b82f6" />
                                </Pie>
                                <Tooltip formatter={(value) => [`${value} transformers`, "Count"]} />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="replacement" className="mt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Burnt Transformers by Replacement Status</CardTitle>
                          <CardDescription>Distribution of burnt transformers by replacement status</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[400px]">
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={prepareReplacementStatusChartData()}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={true}
                                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                  outerRadius={150}
                                  fill="#8884d8"
                                  dataKey="value"
                                  nameKey="name"
                                >
                                  <Cell fill="#22c55e" />
                                  <Cell fill="#f97316" />
                                </Pie>
                                <Tooltip formatter={(value) => [`${value} transformers`, "Count"]} />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="workflow" className="mt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Burnt Transformer Workflow Analysis</CardTitle>
                          <CardDescription>Analysis of the transformer failure reporting workflow</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <h3 className="text-lg font-medium mb-2">Abnormality Report Priority</h3>
                              <div className="h-[250px]">
                                <ResponsiveContainer width="100%" height="100%">
                                  <PieChart>
                                    <Pie
                                      data={prepareAbnormalityPriorityChartData()}
                                      cx="50%"
                                      cy="50%"
                                      labelLine={true}
                                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                      outerRadius={80}
                                      fill="#8884d8"
                                      dataKey="value"
                                      nameKey="name"
                                    >
                                      {prepareAbnormalityPriorityChartData().map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                      ))}
                                    </Pie>
                                    <Tooltip formatter={(value) => [`${value} transformers`, "Count"]} />
                                    <Legend />
                                  </PieChart>
                                </ResponsiveContainer>
                              </div>
                            </div>

                            <div>
                              <h3 className="text-lg font-medium mb-2">Megger Test Results</h3>
                              <div className="h-[250px]">
                                <ResponsiveContainer width="100%" height="100%">
                                  <PieChart>
                                    <Pie
                                      data={prepareMeggerTestResultChartData()}
                                      cx="50%"
                                      cy="50%"
                                      labelLine={true}
                                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                      outerRadius={80}
                                      fill="#8884d8"
                                      dataKey="value"
                                      nameKey="name"
                                    >
                                      {prepareMeggerTestResultChartData().map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                      ))}
                                    </Pie>
                                    <Tooltip formatter={(value) => [`${value} transformers`, "Count"]} />
                                    <Legend />
                                  </PieChart>
                                </ResponsiveContainer>
                              </div>
                            </div>
                          </div>

                          <div className="mt-6 p-4 bg-muted rounded-lg">
                            <h3 className="text-lg font-medium mb-2">Workflow Summary</h3>
                            <p className="text-sm mb-2">
                              The burnt transformer reporting workflow starts with service center engineers reporting abnormalities to regional switchgear offices.
                              The switchgear team then performs megger testing on the transformer to determine if it is burnt or needs maintenance at the EEU workshop.
                            </p>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                              <div className="bg-background p-3 rounded-md shadow-sm">
                                <div className="font-medium">Abnormality Reports</div>
                                <div className="text-2xl font-bold">{statistics?.abnormalityReports.total || 0}</div>
                              </div>
                              <div className="bg-background p-3 rounded-md shadow-sm">
                                <div className="font-medium">Megger Tests</div>
                                <div className="text-2xl font-bold">{statistics?.meggerTests.total || 0}</div>
                              </div>
                              <div className="bg-background p-3 rounded-md shadow-sm">
                                <div className="font-medium">Workshop Referrals</div>
                                <div className="text-2xl font-bold">{statistics?.burntTransformers.byWorkshopReferral.referred || 0}</div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>
                </TabsContent>

                <TabsContent value="list" className="mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Burnt Transformers List</CardTitle>
                      <CardDescription>
                        {burntTransformers.length} burnt transformers found
                        {(regionFilter !== "all" ||
                          manufacturerFilter !== "all" ||
                          typeFilter !== "all" ||
                          voltageLevelFilter !== "all" ||
                          yearFilter !== "all" ||
                          serviceCenterFilter !== "all" ||
                          failureCauseFilter !== "all") && " (filtered)"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Serial Number</TableHead>
                              <TableHead>Manufacturer</TableHead>
                              <TableHead>Type</TableHead>
                              <TableHead>Capacity</TableHead>
                              <TableHead>Region</TableHead>
                              <TableHead>Failure Date</TableHead>
                              <TableHead>Failure Cause</TableHead>
                              <TableHead>Verification</TableHead>
                              <TableHead>Workshop</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {burntTransformers.length === 0 ? (
                              <TableRow>
                                <TableCell colSpan={7} className="text-center py-4 text-muted-foreground">
                                  No burnt transformers found matching your filters
                                </TableCell>
                              </TableRow>
                            ) : (
                              burntTransformers.map((transformer) => (
                                <TableRow key={transformer.id}>
                                  <TableCell className="font-medium">{transformer.serialNumber}</TableCell>
                                  <TableCell>{transformer.manufacturer}</TableCell>
                                  <TableCell>{transformer.type}</TableCell>
                                  <TableCell>{transformer.capacity} kVA</TableCell>
                                  <TableCell>{transformer.location.region}</TableCell>
                                  <TableCell>{transformer.failureDate || "Unknown"}</TableCell>
                                  <TableCell>
                                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                                      {transformer.failureCause || "Unknown"}
                                    </Badge>
                                  </TableCell>
                                  <TableCell>
                                    {transformer.failureVerificationMethod ? (
                                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                        {transformer.failureVerificationMethod}
                                      </Badge>
                                    ) : (
                                      "Unknown"
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    {transformer.workshopReferral !== undefined ? (
                                      transformer.workshopReferral ? (
                                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                          Referred
                                        </Badge>
                                      ) : (
                                        <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                                          Not Referred
                                        </Badge>
                                      )
                                    ) : (
                                      "Unknown"
                                    )}
                                  </TableCell>
                                </TableRow>
                              ))
                            )}
                          </TableBody>
                        </Table>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <div className="text-sm text-muted-foreground">
                        Showing {burntTransformers.length} of {statistics?.burntTransformers.total || 0} burnt transformers
                      </div>
                      <Button variant="outline" size="sm" onClick={exportToCSV}>
                        <Download className="mr-2 h-4 w-4" />
                        Export to CSV
                      </Button>
                    </CardFooter>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
