"use client"

import React, { useState } from 'react'
import { 
  Layers, 
  Download, 
  Share2, 
  Maximize, 
  Minimize, 
  RefreshCw,
  Settings,
  Map,
  Satellite,
  Navigation,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Target,
  Grid,
  Ruler,
  Camera,
  FileText,
  BarChart3
} from 'lucide-react'
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Card, CardContent } from "@/src/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/src/components/ui/tooltip"

interface TransformerMapToolbarProps {
  mapStyle: string
  onMapStyleChange: (style: string) => void
  isFullscreen: boolean
  onToggleFullscreen: () => void
  onRefresh: () => void
  onExport: () => void
  onShare: () => void
  onZoomIn: () => void
  onZoomOut: () => void
  onResetView: () => void
  onCenterOnUser: () => void
  showGrid: boolean
  onToggleGrid: () => void
  showMeasurement: boolean
  onToggleMeasurement: () => void
  clusteredView: boolean
  onToggleClustered: () => void
  selectedCount: number
}

const mapStyleOptions = [
  { value: 'satellite', label: 'Satellite', icon: Satellite },
  { value: 'streets', label: 'Streets', icon: Map },
  { value: 'terrain', label: 'Terrain', icon: Grid },
  { value: 'hybrid', label: 'Hybrid', icon: Layers }
]

export function TransformerMapToolbar({
  mapStyle,
  onMapStyleChange,
  isFullscreen,
  onToggleFullscreen,
  onRefresh,
  onExport,
  onShare,
  onZoomIn,
  onZoomOut,
  onResetView,
  onCenterOnUser,
  showGrid,
  onToggleGrid,
  showMeasurement,
  onToggleMeasurement,
  clusteredView,
  onToggleClustered,
  selectedCount
}: TransformerMapToolbarProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  return (
    <TooltipProvider>
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-[1000]">
        <Card className="shadow-xl border-2">
          <CardContent className="p-2">
            <div className="flex items-center gap-2">
              {/* Map Style Selector */}
              <Select value={mapStyle} onValueChange={onMapStyleChange}>
                <SelectTrigger className="w-32 h-8 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {mapStyleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <option.icon className="h-3 w-3" />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="h-6 w-px bg-gray-300" />

              {/* Zoom Controls */}
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={onZoomIn} className="h-8 w-8">
                      <ZoomIn className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Zoom In</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={onZoomOut} className="h-8 w-8">
                      <ZoomOut className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Zoom Out</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={onResetView} className="h-8 w-8">
                      <RotateCcw className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Reset View</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={onCenterOnUser} className="h-8 w-8">
                      <Target className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Center on Location</p>
                  </TooltipContent>
                </Tooltip>
              </div>

              <div className="h-6 w-px bg-gray-300" />

              {/* View Options */}
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={clusteredView ? "default" : "outline"}
                      size="icon"
                      onClick={onToggleClustered}
                      className="h-8 w-8"
                    >
                      <Grid className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{clusteredView ? 'Disable' : 'Enable'} Clustering</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={showGrid ? "default" : "outline"}
                      size="icon"
                      onClick={onToggleGrid}
                      className="h-8 w-8"
                    >
                      <Layers className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{showGrid ? 'Hide' : 'Show'} Grid</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={showMeasurement ? "default" : "outline"}
                      size="icon"
                      onClick={onToggleMeasurement}
                      className="h-8 w-8"
                    >
                      <Ruler className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{showMeasurement ? 'Disable' : 'Enable'} Measurement</p>
                  </TooltipContent>
                </Tooltip>
              </div>

              <div className="h-6 w-px bg-gray-300" />

              {/* Action Buttons */}
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={onRefresh} className="h-8 w-8">
                      <RefreshCw className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Refresh Data</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={onExport} className="h-8 w-8">
                      <Download className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Export Map</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={onShare} className="h-8 w-8">
                      <Share2 className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Share Map</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={onToggleFullscreen}
                      className="h-8 w-8"
                    >
                      {isFullscreen ? <Minimize className="h-3 w-3" /> : <Maximize className="h-3 w-3" />}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isFullscreen ? 'Exit' : 'Enter'} Fullscreen</p>
                  </TooltipContent>
                </Tooltip>
              </div>

              {/* Advanced Options Toggle */}
              <div className="h-6 w-px bg-gray-300" />
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={showAdvanced ? "default" : "outline"}
                    size="icon"
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    className="h-8 w-8"
                  >
                    <Settings className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Advanced Options</p>
                </TooltipContent>
              </Tooltip>

              {/* Selection Count */}
              {selectedCount > 0 && (
                <>
                  <div className="h-6 w-px bg-gray-300" />
                  <Badge variant="secondary" className="text-xs">
                    {selectedCount} selected
                  </Badge>
                </>
              )}
            </div>

            {/* Advanced Options Panel */}
            {showAdvanced && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <div className="flex items-center gap-2 text-xs">
                  <Button variant="ghost" size="sm" className="h-6 text-xs">
                    <Camera className="h-3 w-3 mr-1" />
                    Screenshot
                  </Button>
                  <Button variant="ghost" size="sm" className="h-6 text-xs">
                    <FileText className="h-3 w-3 mr-1" />
                    Generate Report
                  </Button>
                  <Button variant="ghost" size="sm" className="h-6 text-xs">
                    <BarChart3 className="h-3 w-3 mr-1" />
                    Analytics
                  </Button>
                  <Button variant="ghost" size="sm" className="h-6 text-xs">
                    <Navigation className="h-3 w-3 mr-1" />
                    Navigation
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  )
}
