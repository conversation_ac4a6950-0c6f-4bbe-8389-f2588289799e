"use client"

import React, { useState } from "react"
import { But<PERSON> } from '@/src/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Badge } from '@/src/components/ui/badge'
import { useToast } from '@/components/ui/toast'
import {
  FileText,
  Printer,
  Download,
  Plus,
  Calendar,
  MapPin,
  Zap,
  CheckCircle,
  AlertTriangle,
  X
} from 'lucide-react'

interface TransformerHistoryCardProps {
  transformerId?: string
  onClose?: () => void
}

export function TransformerHistoryCard({ transformerId, onClose }: TransformerHistoryCardProps) {
  const [activeTab, setActiveTab] = useState('main-record')
  const [isLoading, setIsLoading] = useState(true)
  const { addToast } = useToast()
  const [formData, setFormData] = useState({
    // Main Record Data
    cardNo: '',
    substationName: '',
    feederName: '',
    transformerCode: '',
    kvaRating: '',
    primaryVoltage: '',
    region: '',
    district: '',
    specificLocation: '',
    gpsLocation: '',
    manufacturer: '',
    yearOfManufacturing: '',
    serialNo: '',
    installationDate: '',
    changingDate: '',
    changingReason: '',
    customerType: '',
    customerName: '',
    subCity: '',
    kebele: '',
    constructionType: '',
    deliveryDate: '',
    responsiblePerson: '',
    signature: '',

    // Inspection Data
    inspections: [],

    // Test Results Data
    testResults: {
      region: '',
      district: '',
      serviceType: '',
      workOrder: '',
      specificLocation: '',
      gpsLocation: '',
      manufacturer: '',
      voltageLevel: '',
      capacity: '',
      serialNo: '',
      ohmicValue: '',
      htToGround: '',
      ltToGround: '',
      oilInsulation: '',
      inspectedBy: '',
      checkedBy: '',
      approvedBy: '',
      date: '',
      remarks: ''
    }
  })

  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Load existing history card data
  const loadHistoryCardData = async () => {
    if (!transformerId) {
      setIsLoading(false)
      return
    }

    try {
      console.log('🔄 Loading history card data for transformer:', transformerId)
      const response = await fetch(`/api/mysql/transformer-history-cards?transformerId=${transformerId}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success && data.historyCards && data.historyCards.length > 0) {
        const historyCard = data.historyCards[0] // Get the most recent history card

        // Populate form data
        setFormData(prev => ({
          ...prev,
          cardNo: historyCard.card_no || '',
          substationName: historyCard.substation_name || '',
          feederName: historyCard.feeder_name || '',
          transformerCode: historyCard.transformer_code || '',
          kvaRating: historyCard.kva_rating?.toString() || '',
          primaryVoltage: historyCard.primary_voltage || '',
          region: historyCard.region || '',
          district: historyCard.district || '',
          specificLocation: historyCard.specific_location || '',
          gpsLocation: historyCard.gps_location || '',
          manufacturer: historyCard.manufacturer || '',
          yearOfManufacturing: historyCard.year_of_manufacturing?.toString() || '',
          serialNo: historyCard.serial_no || '',
          installationDate: historyCard.installation_date || '',
          changingDate: historyCard.changing_date || '',
          changingReason: historyCard.changing_reason || '',
          customerType: historyCard.customer_type || '',
          customerName: historyCard.customer_name || '',
          subCity: historyCard.sub_city || '',
          kebele: historyCard.kebele || '',
          constructionType: historyCard.construction_type || '',
          deliveryDate: historyCard.delivery_date || '',
          responsiblePerson: historyCard.responsible_person || '',
          signature: historyCard.signature || '',
          inspections: historyCard.inspections || [],
          testResults: {
            ...prev.testResults,
            ...(historyCard.meggerTests && historyCard.meggerTests.length > 0 ? {
              region: historyCard.meggerTests[0].region || '',
              district: historyCard.meggerTests[0].district || '',
              serviceType: historyCard.meggerTests[0].service_type || '',
              workOrder: historyCard.meggerTests[0].work_order_no || '',
              specificLocation: historyCard.meggerTests[0].specific_location || '',
              gpsLocation: historyCard.meggerTests[0].gps_location || '',
              manufacturer: historyCard.meggerTests[0].manufacturer || '',
              voltageLevel: historyCard.meggerTests[0].voltage_level || '',
              capacity: historyCard.meggerTests[0].capacity_kva?.toString() || '',
              serialNo: historyCard.meggerTests[0].serial_no || '',
              ohmicValue: historyCard.meggerTests[0].rs_ohmic_value?.toString() || '',
              htToGround: historyCard.meggerTests[0].ht_to_ground_value?.toString() || '',
              ltToGround: historyCard.meggerTests[0].lt_to_ground_value?.toString() || '',
              oilInsulation: historyCard.meggerTests[0].oil_insulation_condition || '',
              inspectedBy: historyCard.meggerTests[0].inspected_by || '',
              checkedBy: historyCard.meggerTests[0].checked_by || '',
              approvedBy: historyCard.meggerTests[0].approved_by || '',
              date: historyCard.meggerTests[0].test_date || '',
              remarks: historyCard.meggerTests[0].remarks || ''
            } : {})
          }
        }))

        console.log('✅ History card data loaded successfully')
      } else {
        // No existing history card, generate new card number
        const newCardNo = `HC-${Date.now().toString().slice(-6)}-2024`
        setFormData(prev => ({
          ...prev,
          cardNo: newCardNo
        }))
        console.log('ℹ️ No existing history card found, created new card number:', newCardNo)
      }
    } catch (error) {
      console.error('❌ Error loading history card data:', error)
      // Generate new card number on error
      const newCardNo = `HC-${Date.now().toString().slice(-6)}-2024`
      setFormData(prev => ({
        ...prev,
        cardNo: newCardNo
      }))
    } finally {
      setIsLoading(false)
    }
  }

  // Load data on component mount
  React.useEffect(() => {
    loadHistoryCardData()
  }, [transformerId])

  // Form validation functions
  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'cardNo':
        return !value ? 'Card number is required' : ''
      case 'substationName':
        return !value ? 'Substation name is required' : ''
      case 'transformerCode':
        return !value ? 'Transformer code is required' : ''
      case 'kvaRating':
        return !value ? 'KVA rating is required' : isNaN(Number(value)) ? 'Must be a valid number' : ''
      case 'gpsLocation':
        return value && !/^-?\d+\.?\d*,-?\d+\.?\d*$/.test(value) ? 'Invalid GPS format (lat,lng)' : ''
      default:
        return ''
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }

    // Validate field on change
    const error = validateField(field, value)
    if (error) {
      setFormErrors(prev => ({
        ...prev,
        [field]: error
      }))
    }
  }

  const handleTestResultChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      testResults: {
        ...prev.testResults,
        [field]: value
      }
    }))

    // Clear error when user starts typing
    const errorKey = `testResults.${field}`
    if (formErrors[errorKey]) {
      setFormErrors(prev => ({
        ...prev,
        [errorKey]: ''
      }))
    }
  }

  const addInspectionRecord = () => {
    const newInspection = {
      id: Date.now(),
      inspectionDate: '',
      inspector: '',
      arrestor: '',
      dropout: '',
      bushing: '',
      ground: '',
      leakage: '',
      oilLevel: '',
      cableRating: '',
      transformerLoad: '',
      remarks: '',
      signature: ''
    }

    setFormData(prev => ({
      ...prev,
      inspections: [...prev.inspections, newInspection]
    }))
  }

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    // Validate main record fields
    const requiredFields = ['cardNo', 'substationName', 'transformerCode', 'kvaRating']
    requiredFields.forEach(field => {
      const error = validateField(field, formData[field as keyof typeof formData] as string)
      if (error) {
        errors[field] = error
      }
    })

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSave = async () => {
    if (!validateForm()) {
      setActiveTab('main-record') // Switch to main record tab if validation fails
      return
    }

    setIsSubmitting(true)
    try {
      console.log('💾 Saving transformer history card...', formData)

      const payload = {
        transformerId,
        cardNo: formData.cardNo,
        substationName: formData.substationName,
        feederName: formData.feederName,
        transformerCode: formData.transformerCode,
        kvaRating: parseFloat(formData.kvaRating) || 0,
        primaryVoltage: formData.primaryVoltage,
        region: formData.region,
        district: formData.district,
        specificLocation: formData.specificLocation,
        gpsLocation: formData.gpsLocation,
        manufacturer: formData.manufacturer,
        yearOfManufacturing: formData.yearOfManufacturing ? parseInt(formData.yearOfManufacturing) : null,
        serialNo: formData.serialNo,
        installationDate: formData.installationDate || null,
        changingDate: formData.changingDate || null,
        changingReason: formData.changingReason,
        customerType: formData.customerType,
        customerName: formData.customerName,
        subCity: formData.subCity,
        kebele: formData.kebele,
        constructionType: formData.constructionType,
        deliveryDate: formData.deliveryDate || null,
        responsiblePerson: formData.responsiblePerson,
        signature: formData.signature,
        inspections: formData.inspections,
        testResults: formData.testResults
      }

      const response = await fetch('/api/mysql/transformer-history-cards', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        console.log('✅ History card saved successfully')
        addToast({
          type: 'success',
          title: 'History Card Saved',
          message: 'Transformer history card has been saved successfully.',
          duration: 4000
        })
        onClose?.()
      } else {
        throw new Error(result.error || 'Failed to save history card')
      }
    } catch (error) {
      console.error('❌ Error saving history card:', error)
      addToast({
        type: 'error',
        title: 'Save Failed',
        message: 'Failed to save history card. Please check your data and try again.',
        duration: 6000
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const printForm = () => {
    window.print()
  }

  const exportForm = () => {
    const dataStr = JSON.stringify(formData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `transformer-history-card-${transformerId || 'new'}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg p-8 flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500 mb-4"></div>
          <p className="text-gray-600">Loading transformer history card...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-6xl max-h-[95vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b p-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-6 w-6 text-green-600" />
            <h2 className="text-xl font-bold">Distribution Transformer History Card</h2>
            <Badge variant="outline">Ethiopian Electric Utility</Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={printForm}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button variant="outline" size="sm" onClick={exportForm}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            {onClose && (
              <Button variant="outline" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        <div className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="main-record">Main Record</TabsTrigger>
              <TabsTrigger value="inspection">Inspection Details</TabsTrigger>
              <TabsTrigger value="test-results">Megger Test Results</TabsTrigger>
            </TabsList>

            <TabsContent value="main-record" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>Transformer Information</span>
                  </CardTitle>
                  <CardDescription>
                    Basic transformer details and location information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Card No. <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.cardNo}
                        onChange={(e) => handleInputChange('cardNo', e.target.value)}
                        className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 ${
                          formErrors.cardNo
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-green-500'
                        }`}
                        placeholder="Enter card number"
                      />
                      {formErrors.cardNo && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.cardNo}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Substation Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.substationName}
                        onChange={(e) => handleInputChange('substationName', e.target.value)}
                        className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 ${
                          formErrors.substationName
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-green-500'
                        }`}
                        placeholder="Enter substation name"
                      />
                      {formErrors.substationName && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.substationName}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Feeder Name</label>
                      <input
                        type="text"
                        value={formData.feederName}
                        onChange={(e) => handleInputChange('feederName', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter feeder name"
                      />
                    </div>
                  </div>

                  {/* Transformer Specifications */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Transformer Code <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.transformerCode}
                        onChange={(e) => handleInputChange('transformerCode', e.target.value)}
                        className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 ${
                          formErrors.transformerCode
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-green-500'
                        }`}
                        placeholder="Enter transformer code"
                      />
                      {formErrors.transformerCode && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.transformerCode}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        KVA Rating <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        value={formData.kvaRating}
                        onChange={(e) => handleInputChange('kvaRating', e.target.value)}
                        className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 ${
                          formErrors.kvaRating
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-green-500'
                        }`}
                        placeholder="Enter KVA rating"
                      />
                      {formErrors.kvaRating && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.kvaRating}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Primary Service Voltage</label>
                      <input
                        type="text"
                        value={formData.primaryVoltage}
                        onChange={(e) => handleInputChange('primaryVoltage', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter primary voltage"
                      />
                    </div>
                  </div>

                  {/* Location Information */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Region</label>
                      <select
                        value={formData.region}
                        onChange={(e) => handleInputChange('region', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <option value="">Select region</option>
                        <option value="Addis Ababa">Addis Ababa</option>
                        <option value="Oromia">Oromia</option>
                        <option value="Amhara">Amhara</option>
                        <option value="Tigray">Tigray</option>
                        <option value="SNNP">SNNP</option>
                        <option value="Somali">Somali</option>
                        <option value="Afar">Afar</option>
                        <option value="Benishangul-Gumuz">Benishangul-Gumuz</option>
                        <option value="Gambela">Gambela</option>
                        <option value="Harari">Harari</option>
                        <option value="Dire Dawa">Dire Dawa</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">District Name</label>
                      <input
                        type="text"
                        value={formData.district}
                        onChange={(e) => handleInputChange('district', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter district name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Specific Location</label>
                      <input
                        type="text"
                        value={formData.specificLocation}
                        onChange={(e) => handleInputChange('specificLocation', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter specific location"
                      />
                    </div>
                  </div>

                  {/* GPS and Manufacturer */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">GPS Location</label>
                      <input
                        type="text"
                        value={formData.gpsLocation}
                        onChange={(e) => handleInputChange('gpsLocation', e.target.value)}
                        className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 ${
                          formErrors.gpsLocation
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-green-500'
                        }`}
                        placeholder="Enter GPS coordinates (lat,lng)"
                      />
                      {formErrors.gpsLocation && (
                        <p className="text-red-500 text-xs mt-1">{formErrors.gpsLocation}</p>
                      )}
                      <p className="text-gray-500 text-xs mt-1">Format: latitude,longitude (e.g., 9.0192,38.7525)</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Manufacturer</label>
                      <select
                        value={formData.manufacturer}
                        onChange={(e) => handleInputChange('manufacturer', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <option value="">Select manufacturer</option>
                        <option value="ABB">ABB</option>
                        <option value="Siemens">Siemens</option>
                        <option value="Schneider Electric">Schneider Electric</option>
                        <option value="General Electric">General Electric</option>
                        <option value="Hyundai">Hyundai</option>
                        <option value="TBEA">TBEA</option>
                        <option value="Crompton Greaves">Crompton Greaves</option>
                      </select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="inspection" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5" />
                      <span>Inspection Records</span>
                    </div>
                    <Button onClick={addInspectionRecord} size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Inspection
                    </Button>
                  </CardTitle>
                  <CardDescription>
                    Detailed inspection history and component status tracking
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Inspection Table */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full border-collapse border border-gray-300">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">No.</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Inspection Date</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Inspector</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Arrestor</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Dropout Fuse</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Bushing</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Ground</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Oil Level</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Leakage</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Cable Rating</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Transformer Load</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Remarks</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Inspector Name</th>
                          <th className="border border-gray-300 px-2 py-1 text-xs font-medium text-gray-500 uppercase">Signature</th>
                        </tr>
                      </thead>
                      <tbody>
                        {Array.from({ length: 10 }, (_, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="border border-gray-300 px-2 py-1 text-center text-sm">{index + 1}</td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="date"
                                className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Inspector"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <select className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500">
                                <option value="">-</option>
                                <option value="OK">OK</option>
                                <option value="Faulty">Faulty</option>
                              </select>
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <select className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500">
                                <option value="">-</option>
                                <option value="OK">OK</option>
                                <option value="Faulty">Faulty</option>
                              </select>
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <select className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500">
                                <option value="">-</option>
                                <option value="OK">OK</option>
                                <option value="Faulty">Faulty</option>
                              </select>
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <select className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500">
                                <option value="">-</option>
                                <option value="OK">OK</option>
                                <option value="Faulty">Faulty</option>
                              </select>
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <select className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500">
                                <option value="">-</option>
                                <option value="Normal">Normal</option>
                                <option value="Low">Low</option>
                                <option value="High">High</option>
                              </select>
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <select className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500">
                                <option value="">-</option>
                                <option value="None">None</option>
                                <option value="Minor">Minor</option>
                                <option value="Major">Major</option>
                              </select>
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Rating"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Load %"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Remarks"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Name"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-xs focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Signature"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Component Inspection Checklist */}
                  <div className="mt-8">
                    <h4 className="text-lg font-semibold mb-4">Component Inspection Checklist</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="border rounded-lg p-4">
                          <h5 className="font-medium mb-3">Earth Resistance</h5>
                          <div className="space-y-2">
                            <label className="flex items-center space-x-2">
                              <input type="checkbox" className="rounded" />
                              <span className="text-sm">Beyond acceptable value</span>
                            </label>
                            <div className="grid grid-cols-2 gap-2">
                              <input
                                type="text"
                                placeholder="Trafo code"
                                className="border rounded px-2 py-1 text-xs"
                              />
                              <input
                                type="text"
                                placeholder="Qty"
                                className="border rounded px-2 py-1 text-xs"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="border rounded-lg p-4">
                          <h5 className="font-medium mb-3">Insulation Resistance</h5>
                          <div className="space-y-2">
                            <label className="flex items-center space-x-2">
                              <input type="checkbox" className="rounded" />
                              <span className="text-sm">Below acceptable value</span>
                            </label>
                            <div className="grid grid-cols-2 gap-2">
                              <input
                                type="text"
                                placeholder="Trafo code"
                                className="border rounded px-2 py-1 text-xs"
                              />
                              <input
                                type="text"
                                placeholder="Qty"
                                className="border rounded px-2 py-1 text-xs"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="border rounded-lg p-4">
                          <h5 className="font-medium mb-3">Oil Level</h5>
                          <div className="space-y-2">
                            <label className="flex items-center space-x-2">
                              <input type="checkbox" className="rounded" />
                              <span className="text-sm">Below normal value</span>
                            </label>
                            <div className="grid grid-cols-2 gap-2">
                              <input
                                type="text"
                                placeholder="Trafo code"
                                className="border rounded px-2 py-1 text-xs"
                              />
                              <input
                                type="text"
                                placeholder="Qty"
                                className="border rounded px-2 py-1 text-xs"
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="border rounded-lg p-4">
                          <h5 className="font-medium mb-3">Oil Temperature</h5>
                          <div className="space-y-2">
                            <label className="flex items-center space-x-2">
                              <input type="checkbox" className="rounded" />
                              <span className="text-sm">Above acceptable value</span>
                            </label>
                            <div className="grid grid-cols-2 gap-2">
                              <input
                                type="text"
                                placeholder="Trafo code"
                                className="border rounded px-2 py-1 text-xs"
                              />
                              <input
                                type="text"
                                placeholder="Qty"
                                className="border rounded px-2 py-1 text-xs"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="border rounded-lg p-4">
                          <h5 className="font-medium mb-3">Unusual Noises</h5>
                          <div className="space-y-2">
                            <label className="flex items-center space-x-2">
                              <input type="checkbox" className="rounded" />
                              <span className="text-sm">Detected</span>
                            </label>
                            <div className="grid grid-cols-2 gap-2">
                              <input
                                type="text"
                                placeholder="Trafo code"
                                className="border rounded px-2 py-1 text-xs"
                              />
                              <input
                                type="text"
                                placeholder="Qty"
                                className="border rounded px-2 py-1 text-xs"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="border rounded-lg p-4">
                          <h5 className="font-medium mb-3">Bushing Condition</h5>
                          <div className="space-y-2">
                            <label className="flex items-center space-x-2">
                              <input type="checkbox" className="rounded" />
                              <span className="text-sm">Misaligned/damaged</span>
                            </label>
                            <div className="grid grid-cols-2 gap-2">
                              <input
                                type="text"
                                placeholder="Trafo code"
                                className="border rounded px-2 py-1 text-xs"
                              />
                              <input
                                type="text"
                                placeholder="Qty"
                                className="border rounded px-2 py-1 text-xs"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="test-results" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Zap className="h-5 w-5" />
                    <span>Megger Test Results</span>
                  </CardTitle>
                  <CardDescription>
                    Electrical testing and insulation resistance measurements
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Test Information */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Region</label>
                      <input
                        type="text"
                        value={formData.testResults.region || ''}
                        onChange={(e) => handleTestResultChange('region', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter region"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">District/CS</label>
                      <input
                        type="text"
                        value={formData.testResults.district || ''}
                        onChange={(e) => handleTestResultChange('district', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter district"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Type of Service</label>
                      <select
                        value={formData.testResults.serviceType || ''}
                        onChange={(e) => handleTestResultChange('serviceType', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <option value="">Select service type</option>
                        <option value="Residential">Residential</option>
                        <option value="Commercial">Commercial</option>
                        <option value="Industrial">Industrial</option>
                        <option value="Agricultural">Agricultural</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Work Order No.</label>
                      <input
                        type="text"
                        value={formData.testResults.workOrder}
                        onChange={(e) => handleTestResultChange('workOrder', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter work order number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Specific Location</label>
                      <input
                        type="text"
                        value={formData.testResults.specificLocation}
                        onChange={(e) => handleTestResultChange('specificLocation', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter specific location"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">GPS Location</label>
                      <input
                        type="text"
                        value={formData.testResults.gpsLocation}
                        onChange={(e) => handleTestResultChange('gpsLocation', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter GPS coordinates"
                      />
                    </div>
                  </div>

                  {/* Transformer Details */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Manufacturer Type</label>
                      <input
                        type="text"
                        value={formData.testResults.manufacturer}
                        onChange={(e) => handleTestResultChange('manufacturer', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter manufacturer"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Incoming/Outgoing kV Level</label>
                      <input
                        type="text"
                        value={formData.testResults.voltageLevel || ''}
                        onChange={(e) => handleTestResultChange('voltageLevel', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter voltage level"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Capacity/kVA</label>
                      <input
                        type="text"
                        value={formData.testResults.capacity}
                        onChange={(e) => handleTestResultChange('capacity', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter capacity"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Serial No.</label>
                      <input
                        type="text"
                        value={formData.testResults.serialNo}
                        onChange={(e) => handleTestResultChange('serialNo', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                        placeholder="Enter serial number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Test Date</label>
                      <input
                        type="date"
                        value={formData.testResults.date}
                        onChange={(e) => handleTestResultChange('date', e.target.value)}
                        className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      />
                    </div>
                  </div>

                  {/* Test Results Table */}
                  <div className="mt-8">
                    <h4 className="text-lg font-semibold mb-4">Test Results (Megger)</h4>
                    <div className="overflow-x-auto">
                      <table className="min-w-full border-collapse border border-gray-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-500">Test Type</th>
                            <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-500">Box One</th>
                            <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-500">Box Two</th>
                            <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-500">Box Two</th>
                            <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-500">Linear</th>
                            <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-500">Oil Level</th>
                            <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-500">Ground</th>
                            <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-500">Silica Gel</th>
                            <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-500">Insulation</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td className="border border-gray-300 px-4 py-2 font-medium">Cable</td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Value"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Value"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Value"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Value"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Level"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Value"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Condition"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Value"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td className="border border-gray-300 px-4 py-2 font-medium">Fuse Rating</td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Size"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Size"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Size"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">
                              <input
                                type="text"
                                className="w-full border-0 text-sm focus:outline-none focus:ring-1 focus:ring-green-500"
                                placeholder="Rating"
                              />
                            </td>
                            <td className="border border-gray-300 px-2 py-1">-</td>
                            <td className="border border-gray-300 px-2 py-1">-</td>
                            <td className="border border-gray-300 px-2 py-1">-</td>
                            <td className="border border-gray-300 px-2 py-1">-</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* Additional Test Results */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h5 className="font-medium">Resistance Measurements</h5>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium mb-1">R-S (Ohmic Value)</label>
                          <input
                            type="text"
                            value={formData.testResults.ohmicValue}
                            onChange={(e) => handleTestResultChange('ohmicValue', e.target.value)}
                            className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                            placeholder="Enter ohmic value"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">HT to Ground</label>
                          <input
                            type="text"
                            value={formData.testResults.htToGround}
                            onChange={(e) => handleTestResultChange('htToGround', e.target.value)}
                            className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                            placeholder="Enter HT to ground value"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">LT to Ground</label>
                          <input
                            type="text"
                            value={formData.testResults.ltToGround}
                            onChange={(e) => handleTestResultChange('ltToGround', e.target.value)}
                            className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                            placeholder="Enter LT to ground value"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">Oil Insulation & Oil Level</label>
                          <input
                            type="text"
                            value={formData.testResults.oilInsulation}
                            onChange={(e) => handleTestResultChange('oilInsulation', e.target.value)}
                            className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                            placeholder="Enter oil condition"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h5 className="font-medium">Inspection & Approval</h5>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium mb-1">Inspected by</label>
                          <input
                            type="text"
                            value={formData.testResults.inspectedBy}
                            onChange={(e) => handleTestResultChange('inspectedBy', e.target.value)}
                            className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                            placeholder="Inspector name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">Checked by</label>
                          <input
                            type="text"
                            value={formData.testResults.checkedBy}
                            onChange={(e) => handleTestResultChange('checkedBy', e.target.value)}
                            className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                            placeholder="Checker name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">Approved by</label>
                          <input
                            type="text"
                            value={formData.testResults.approvedBy}
                            onChange={(e) => handleTestResultChange('approvedBy', e.target.value)}
                            className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                            placeholder="Approver name"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Remarks */}
                  <div>
                    <label className="block text-sm font-medium mb-1">Remarks or Final Result of Conclusion</label>
                    <textarea
                      value={formData.testResults.remarks}
                      onChange={(e) => handleTestResultChange('remarks', e.target.value)}
                      className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                      rows={4}
                      placeholder="Enter test conclusions and recommendations..."
                    />
                  </div>

                  {/* Note */}
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <p className="text-sm text-yellow-800">
                      <strong>Note:</strong> This should be attached with the history card of the transformer and technical inspection/enforcement report.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2 mt-6 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700 disabled:opacity-50"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                'Save History Card'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
