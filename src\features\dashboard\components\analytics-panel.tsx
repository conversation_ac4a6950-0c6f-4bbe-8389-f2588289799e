"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Progress } from "@/src/components/ui/progress"
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Zap,
  Gauge,
  Eye,
  Download
} from "lucide-react"

export function AnalyticsPanel() {
  const metrics = [
    {
      title: "System Efficiency",
      value: 94.2,
      unit: "%",
      change: +2.1,
      trend: "up",
      description: "Overall system performance"
    },
    {
      title: "Load Factor",
      value: 78.5,
      unit: "%",
      change: -1.3,
      trend: "down",
      description: "Average load utilization"
    },
    {
      title: "Uptime",
      value: 99.7,
      unit: "%",
      change: +0.2,
      trend: "up",
      description: "System availability"
    },
    {
      title: "Energy Loss",
      value: 3.8,
      unit: "%",
      change: -0.5,
      trend: "down",
      description: "Transmission losses"
    }
  ]

  const performanceData = [
    { region: "Addis Ababa", efficiency: 96, load: 85, status: "excellent" },
    { region: "Dire Dawa", efficiency: 92, load: 78, status: "good" },
    { region: "Bahir Dar", efficiency: 89, load: 72, status: "good" },
    { region: "Mekelle", efficiency: 87, load: 68, status: "fair" },
    { region: "Hawassa", efficiency: 94, load: 81, status: "excellent" }
  ]

  const getTrendIcon = (trend: string) => {
    return trend === "up" ? (
      <TrendingUp className="h-4 w-4 text-green-500" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-500" />
    )
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "excellent":
        return <Badge variant="outline" className="text-green-600">Excellent</Badge>
      case "good":
        return <Badge variant="outline" className="text-blue-600">Good</Badge>
      case "fair":
        return <Badge variant="outline" className="text-yellow-600">Fair</Badge>
      case "poor":
        return <Badge variant="outline" className="text-red-600">Poor</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance Analytics
          </div>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </CardTitle>
        <CardDescription>
          Real-time system performance metrics and insights
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Key Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {metrics.map((metric, index) => (
            <div key={index} className="border rounded-lg p-3 space-y-2">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium">{metric.title}</div>
                {getTrendIcon(metric.trend)}
              </div>
              <div className="text-2xl font-bold">
                {metric.value}{metric.unit}
              </div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <span className={metric.trend === "up" ? "text-green-600" : "text-red-600"}>
                  {metric.change > 0 ? "+" : ""}{metric.change}%
                </span>
                <span>vs last period</span>
              </div>
              <div className="text-xs text-muted-foreground">
                {metric.description}
              </div>
            </div>
          ))}
        </div>

        {/* Regional Performance */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Gauge className="h-4 w-4" />
            Regional Performance
          </h4>
          
          {performanceData.map((region, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <h5 className="font-medium">{region.region}</h5>
                {getStatusBadge(region.status)}
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Efficiency</span>
                    <span>{region.efficiency}%</span>
                  </div>
                  <Progress value={region.efficiency} className="h-2" />
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Load Factor</span>
                    <span>{region.load}%</span>
                  </div>
                  <Progress value={region.load} className="h-2" />
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              Detailed Analytics
            </Button>
            <div className="text-sm text-muted-foreground">
              Updated: 2 minutes ago
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
