'use client'

import { useParams } from 'next/navigation'
import SimpleTransformerDetail from '@/components/transformers/SimpleTransformerDetail'
import { MainLayout } from '@/src/components/layout/main-layout'

export default function TransformerDetailPage() {
  const params = useParams()

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "national_maintenance_manager",
        "regional_admin",
        "regional_asset_manager",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician"
      ]}
      requiredPermissions={[{ resource: "transformers", action: "read" }]}
    >
      <SimpleTransformerDetail transformerId={params.id as string} />
    </MainLayout>
  )
}