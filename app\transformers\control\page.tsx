"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Settings,
  Power,
  Zap,
  Activity,
  TrendingUp,
  TrendingDown,
  Clock,
  MapPin,
  Eye,
  Edit,
  RefreshCw,
  Download,
  Upload,
  Search,
  Filter,
  Bell,
  Target,
  BarChart3,
  PieChart,
  Thermometer,
  Gauge,
  Radio,
  Wifi,
  Battery,
  Cpu,
  HardDrive,
  Monitor,
  Layers,
  Lock,
  Unlock,
  Key,
  Play,
  Pause,
  Square,
  RotateCcw,
  RotateCw,
  ArrowUp,
  ArrowDown,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Sliders,
  ToggleLeft,
  ToggleRight
} from 'lucide-react'
import {
  <PERSON><PERSON><PERSON> as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  Pie<PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts'
import { TransformerLayout } from '@/components/transformer/TransformerLayout'
import { ProtectedRoute } from "@/src/components/layout/protected-route"
import ProvidersWrapper from "@/components/providers-wrapper"

// Mock control system data
const mockControlSystems = [
  {
    id: 'CTRL-AA-001',
    transformerId: 'T-AA-001',
    transformerName: 'Lideta Primary Transformer',
    location: 'Lideta Substation, Addis Ababa',
    controlType: 'SCADA Integrated',
    status: 'online',
    mode: 'automatic',
    lastUpdate: '2024-02-15T14:30:00Z',
    controlParameters: {
      voltage: { current: 132.5, setpoint: 132.0, min: 125.0, max: 138.0, unit: 'kV' },
      tapPosition: { current: 8, setpoint: 8, min: 1, max: 16, unit: 'position' },
      loadCurrent: { current: 245.8, setpoint: 250.0, min: 0, max: 400, unit: 'A' },
      temperature: { current: 65.2, setpoint: 70.0, min: 0, max: 85, unit: '°C' },
      powerFactor: { current: 0.95, setpoint: 0.98, min: 0.8, max: 1.0, unit: 'pf' }
    },
    controlActions: [
      { timestamp: '2024-02-15T14:25:00Z', action: 'Tap position adjusted to 8', operator: 'Auto', result: 'success' },
      { timestamp: '2024-02-15T13:45:00Z', action: 'Voltage regulation enabled', operator: 'John Doe', result: 'success' },
      { timestamp: '2024-02-15T12:30:00Z', action: 'Load transfer initiated', operator: 'Auto', result: 'success' }
    ],
    alarms: [
      { timestamp: '2024-02-15T14:20:00Z', severity: 'warning', message: 'Voltage slightly above setpoint', acknowledged: false },
      { timestamp: '2024-02-15T13:15:00Z', severity: 'info', message: 'Automatic tap change completed', acknowledged: true }
    ],
    capabilities: {
      voltageRegulation: true,
      loadTapChanger: true,
      loadTransfer: true,
      remoteControl: true,
      automaticControl: true,
      manualOverride: true
    }
  },
  {
    id: 'CTRL-OR-045',
    transformerId: 'T-OR-045',
    transformerName: 'Sebeta Distribution Transformer',
    location: 'Sebeta Substation, Oromia',
    controlType: 'Local Control Panel',
    status: 'online',
    mode: 'manual',
    lastUpdate: '2024-02-15T14:28:00Z',
    controlParameters: {
      voltage: { current: 11.2, setpoint: 11.0, min: 10.5, max: 11.5, unit: 'kV' },
      tapPosition: { current: 5, setpoint: 5, min: 1, max: 9, unit: 'position' },
      loadCurrent: { current: 156.3, setpoint: 160.0, min: 0, max: 200, unit: 'A' },
      temperature: { current: 58.7, setpoint: 65.0, min: 0, max: 80, unit: '°C' },
      powerFactor: { current: 0.92, setpoint: 0.95, min: 0.8, max: 1.0, unit: 'pf' }
    },
    controlActions: [
      { timestamp: '2024-02-15T14:15:00Z', action: 'Manual tap adjustment to position 5', operator: 'Jane Smith', result: 'success' },
      { timestamp: '2024-02-15T13:30:00Z', action: 'Load monitoring enabled', operator: 'Jane Smith', result: 'success' }
    ],
    alarms: [
      { timestamp: '2024-02-15T14:10:00Z', severity: 'info', message: 'Manual control mode active', acknowledged: true }
    ],
    capabilities: {
      voltageRegulation: true,
      loadTapChanger: true,
      loadTransfer: false,
      remoteControl: false,
      automaticControl: false,
      manualOverride: true
    }
  },
  {
    id: 'CTRL-AM-023',
    transformerId: 'T-AM-023',
    transformerName: 'Bahir Dar Distribution Transformer',
    location: 'Bahir Dar Substation, Amhara',
    controlType: 'Smart Control System',
    status: 'warning',
    mode: 'automatic',
    lastUpdate: '2024-02-15T14:25:00Z',
    controlParameters: {
      voltage: { current: 33.8, setpoint: 33.0, min: 31.5, max: 34.5, unit: 'kV' },
      tapPosition: { current: 12, setpoint: 10, min: 1, max: 20, unit: 'position' },
      loadCurrent: { current: 89.4, setpoint: 95.0, min: 0, max: 150, unit: 'A' },
      temperature: { current: 72.1, setpoint: 70.0, min: 0, max: 85, unit: '°C' },
      powerFactor: { current: 0.88, setpoint: 0.95, min: 0.8, max: 1.0, unit: 'pf' }
    },
    controlActions: [
      { timestamp: '2024-02-15T14:20:00Z', action: 'Automatic voltage correction attempted', operator: 'Auto', result: 'warning' },
      { timestamp: '2024-02-15T14:00:00Z', action: 'Temperature alarm triggered', operator: 'System', result: 'alarm' }
    ],
    alarms: [
      { timestamp: '2024-02-15T14:20:00Z', severity: 'warning', message: 'Tap position deviation from setpoint', acknowledged: false },
      { timestamp: '2024-02-15T14:00:00Z', severity: 'warning', message: 'Temperature above normal operating range', acknowledged: false }
    ],
    capabilities: {
      voltageRegulation: true,
      loadTapChanger: true,
      loadTransfer: true,
      remoteControl: true,
      automaticControl: true,
      manualOverride: true
    }
  }
]

const controlStats = {
  totalSystems: 1247,
  online: 1198,
  offline: 23,
  warning: 26,
  automaticMode: 892,
  manualMode: 306,
  avgResponseTime: 0.15,
  controlReliability: 98.7
}

const controlTypes = [
  { name: 'SCADA Integrated', count: 456, efficiency: 98.5 },
  { name: 'Smart Control', count: 387, efficiency: 97.8 },
  { name: 'Local Control', count: 298, efficiency: 96.2 },
  { name: 'Remote Control', count: 106, efficiency: 99.1 }
]

const statusColors = {
  online: 'bg-green-100 text-green-800 border-green-200',
  offline: 'bg-red-100 text-red-800 border-red-200',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  maintenance: 'bg-blue-100 text-blue-800 border-blue-200'
}

const modeColors = {
  automatic: 'bg-blue-100 text-blue-800 border-blue-200',
  manual: 'bg-purple-100 text-purple-800 border-purple-200',
  remote: 'bg-green-100 text-green-800 border-green-200',
  local: 'bg-orange-100 text-orange-800 border-orange-200'
}

const alarmColors = {
  critical: 'bg-red-100 text-red-800 border-red-200',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  info: 'bg-blue-100 text-blue-800 border-blue-200',
  success: 'bg-green-100 text-green-800 border-green-200'
}

export default function TransformerControlPage() {
  const [controlData, setControlData] = useState(mockControlSystems)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedMode, setSelectedMode] = useState('all')

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  const filteredData = controlData.filter(system => {
    const matchesSearch = system.transformerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         system.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         system.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || system.status === selectedStatus
    const matchesMode = selectedMode === 'all' || system.mode === selectedMode

    return matchesSearch && matchesStatus && matchesMode
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading control systems...</p>
        </div>
      </div>
    )
  }

  return (
    <ProvidersWrapper>
      <ProtectedRoute
        allowedRoles={[
          "super_admin",
          "national_control_engineer",
          "regional_admin",
          "regional_control_engineer",
          "service_center_manager",
          "control_operator"
        ]}
      >
        <TransformerLayout>
          <div className="space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Control Systems</h1>
                <p className="text-muted-foreground">
                  Advanced control and automation systems for Ethiopian Electric Utility transformers
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </Button>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </Button>
                <Button>
                  <Power className="h-4 w-4 mr-2" />
                  Control Panel
                </Button>
              </div>
            </div>

            {/* Control Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Systems</CardTitle>
                  <Settings className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{controlStats.totalSystems}</div>
                  <p className="text-xs text-muted-foreground">
                    {controlStats.online} online
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Automatic Mode</CardTitle>
                  <Cpu className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">{controlStats.automaticMode}</div>
                  <p className="text-xs text-muted-foreground">
                    {Math.round((controlStats.automaticMode / controlStats.totalSystems) * 100)}% automated
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Response Time</CardTitle>
                  <Zap className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{controlStats.avgResponseTime}s</div>
                  <p className="text-xs text-muted-foreground">
                    Average response
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Reliability</CardTitle>
                  <Target className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">{controlStats.controlReliability}%</div>
                  <p className="text-xs text-muted-foreground">
                    Control reliability
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Main Control Interface */}
            <Tabs defaultValue="systems" className="space-y-4">
              <TabsList>
                <TabsTrigger value="systems">Control Systems</TabsTrigger>
                <TabsTrigger value="operations">Operations</TabsTrigger>
                <TabsTrigger value="automation">Automation</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
              </TabsList>

              <TabsContent value="systems" className="space-y-4">
                {/* Filters */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-4">
                      <div className="flex-1">
                        <Input
                          placeholder="Search control systems..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="max-w-sm"
                        />
                      </div>
                      <select
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Status</option>
                        <option value="online">Online</option>
                        <option value="offline">Offline</option>
                        <option value="warning">Warning</option>
                        <option value="maintenance">Maintenance</option>
                      </select>
                      <select
                        value={selectedMode}
                        onChange={(e) => setSelectedMode(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Modes</option>
                        <option value="automatic">Automatic</option>
                        <option value="manual">Manual</option>
                        <option value="remote">Remote</option>
                        <option value="local">Local</option>
                      </select>
                    </div>
                  </CardContent>
                </Card>

                {/* Control Systems List */}
                <div className="space-y-4">
                  {filteredData.map((system) => (
                    <Card key={system.id} className="hover:shadow-lg transition-shadow">
                      <CardContent className="pt-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold text-lg">{system.transformerName}</h3>
                              <Badge className={statusColors[system.status as keyof typeof statusColors]}>
                                {system.status}
                              </Badge>
                              <Badge className={modeColors[system.mode as keyof typeof modeColors]}>
                                {system.mode}
                              </Badge>
                              <Badge variant="outline">
                                {system.controlType}
                              </Badge>
                            </div>

                            <div className="flex items-center gap-2 mb-3">
                              <span className="text-sm font-medium text-blue-600">{system.id}</span>
                              <span className="text-sm text-muted-foreground">•</span>
                              <span className="text-sm text-muted-foreground">{system.location}</span>
                            </div>

                            {/* Control Parameters */}
                            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                              {Object.entries(system.controlParameters).map(([key, param]) => (
                                <div key={key} className="p-3 border rounded-lg">
                                  <div className="text-xs text-muted-foreground capitalize mb-1">{key.replace(/([A-Z])/g, ' $1')}</div>
                                  <div className="text-lg font-bold">{param.current} {param.unit}</div>
                                  <div className="text-xs text-muted-foreground">
                                    Target: {param.setpoint} {param.unit}
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                                    <div
                                      className={`h-1 rounded-full ${
                                        Math.abs(param.current - param.setpoint) / param.setpoint < 0.05 ? 'bg-green-500' :
                                        Math.abs(param.current - param.setpoint) / param.setpoint < 0.1 ? 'bg-yellow-500' :
                                        'bg-red-500'
                                      }`}
                                      style={{
                                        width: `${Math.min(100, (param.current / param.max) * 100)}%`
                                      }}
                                    ></div>
                                  </div>
                                </div>
                              ))}
                            </div>

                            {/* Control Capabilities */}
                            <div className="mb-4">
                              <Label className="text-sm font-medium">Control Capabilities:</Label>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {Object.entries(system.capabilities).map(([capability, enabled]) => (
                                  <Badge
                                    key={capability}
                                    variant={enabled ? "default" : "secondary"}
                                    className={enabled ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}
                                  >
                                    {capability.replace(/([A-Z])/g, ' $1').toLowerCase()}
                                  </Badge>
                                ))}
                              </div>
                            </div>

                            {/* Recent Actions */}
                            <div className="mb-4">
                              <Label className="text-sm font-medium">Recent Actions:</Label>
                              <div className="space-y-1 mt-2">
                                {system.controlActions.slice(0, 2).map((action, index) => (
                                  <div key={index} className="flex items-start gap-2 text-sm">
                                    <Badge
                                      className={
                                        action.result === 'success' ? 'bg-green-100 text-green-800' :
                                        action.result === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-red-100 text-red-800'
                                      }
                                      variant="outline"
                                    >
                                      {action.result}
                                    </Badge>
                                    <div className="flex-1">
                                      <div>{action.action}</div>
                                      <div className="text-xs text-muted-foreground">
                                        by {action.operator} • {new Date(action.timestamp).toLocaleString()}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>

                            {/* Active Alarms */}
                            {system.alarms.filter(alarm => !alarm.acknowledged).length > 0 && (
                              <div>
                                <Label className="text-sm font-medium">Active Alarms:</Label>
                                <div className="space-y-1 mt-2">
                                  {system.alarms.filter(alarm => !alarm.acknowledged).map((alarm, index) => (
                                    <div key={index} className="flex items-start gap-2 text-sm">
                                      <Badge className={alarmColors[alarm.severity as keyof typeof alarmColors]} variant="outline">
                                        {alarm.severity}
                                      </Badge>
                                      <div className="flex-1">
                                        <div>{alarm.message}</div>
                                        <div className="text-xs text-muted-foreground">
                                          {new Date(alarm.timestamp).toLocaleString()}
                                        </div>
                                      </div>
                                      <Button size="sm" variant="outline">
                                        Acknowledge
                                      </Button>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="flex flex-col gap-2 ml-4">
                            <Button size="sm" variant="outline">
                              <Eye className="h-3 w-3 mr-1" />
                              Monitor
                            </Button>
                            <Button size="sm" variant="outline">
                              <Settings className="h-3 w-3 mr-1" />
                              Configure
                            </Button>
                            <Button size="sm">
                              <Power className="h-3 w-3 mr-1" />
                              Control
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="operations" className="space-y-4">
                {/* Real-time Control Operations */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>System Status Overview</CardTitle>
                      <CardDescription>Real-time status of all control systems</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsPieChart>
                          <Pie
                            data={[
                              { name: 'Online', value: controlStats.online, fill: '#10b981' },
                              { name: 'Warning', value: controlStats.warning, fill: '#f59e0b' },
                              { name: 'Offline', value: controlStats.offline, fill: '#ef4444' }
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            dataKey="value"
                          />
                          <Tooltip />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Control Mode Distribution</CardTitle>
                      <CardDescription>Distribution by control mode</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={[
                          { mode: 'Automatic', count: controlStats.automaticMode, efficiency: 98.5 },
                          { mode: 'Manual', count: controlStats.manualMode, efficiency: 96.2 },
                          { mode: 'Remote', count: 49, efficiency: 99.1 }
                        ]}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="mode" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="count" fill="#3b82f6" name="Count" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Live Control Operations */}
                <Card>
                  <CardHeader>
                    <CardTitle>Live Control Operations</CardTitle>
                    <CardDescription>Real-time control actions and system responses</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {[
                        { time: '14:32:15', system: 'CTRL-AA-001', operation: 'Automatic tap adjustment completed', status: 'success' },
                        { time: '14:28:42', system: 'CTRL-OR-045', operation: 'Manual voltage regulation activated', status: 'success' },
                        { time: '14:25:18', system: 'CTRL-AM-023', operation: 'Temperature control override engaged', status: 'warning' },
                        { time: '14:22:05', system: 'CTRL-TI-012', operation: 'Load transfer sequence initiated', status: 'in_progress' },
                        { time: '14:18:33', system: 'CTRL-SN-008', operation: 'Remote control session established', status: 'success' }
                      ].map((operation, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${
                              operation.status === 'success' ? 'bg-green-500' :
                              operation.status === 'warning' ? 'bg-yellow-500' :
                              operation.status === 'in_progress' ? 'bg-blue-500' :
                              'bg-red-500'
                            }`}></div>
                            <div>
                              <div className="font-medium">{operation.system}</div>
                              <div className="text-sm text-muted-foreground">{operation.operation}</div>
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground">{operation.time}</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </TransformerLayout>
      </ProtectedRoute>
    </ProvidersWrapper>
  )
}
