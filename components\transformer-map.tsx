"use client"

import { useEffect, useRef, useState } from "react"
import { useRouter } from "next/navigation"
import { Al<PERSON><PERSON>riangle, CheckCircle2, Clock, Maximize2 } from "lucide-react"
import { Button } from "@/src/components/ui/button"

export function TransformerMap() {
  const mapRef = useRef(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const router = useRouter()

  useEffect(() => {
    // Simulate map loading
    const timer = setTimeout(() => {
      setMapLoaded(true)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="relative h-full w-full bg-slate-100 dark:bg-slate-800">
      {!mapLoaded ? (
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-center">
            <div className="mb-2 h-8 w-8 animate-spin rounded-full border-4 border-teal-600 border-t-transparent mx-auto"></div>
            <p className="text-sm text-muted-foreground">Loading map...</p>
          </div>
        </div>
      ) : (
        <>
          <div className="absolute inset-0 bg-[url('/placeholder.svg?height=500&width=800')] bg-cover bg-center opacity-50"></div>
          <div className="absolute inset-0">
            {/* Map markers */}
            <div
              className="absolute left-[20%] top-[30%] flex h-6 w-6 items-center justify-center rounded-full bg-green-100 ring-2 ring-white dark:bg-green-900/20"
              title="Operational"
            >
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            </div>
            <div
              className="absolute left-[35%] top-[45%] flex h-6 w-6 items-center justify-center rounded-full bg-green-100 ring-2 ring-white dark:bg-green-900/20"
              title="Operational"
            >
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            </div>
            <div
              className="absolute left-[50%] top-[25%] flex h-6 w-6 items-center justify-center rounded-full bg-yellow-100 ring-2 ring-white dark:bg-yellow-900/20"
              title="Maintenance"
            >
              <Clock className="h-4 w-4 text-yellow-600" />
            </div>
            <div
              className="absolute left-[65%] top-[55%] flex h-6 w-6 items-center justify-center rounded-full bg-green-100 ring-2 ring-white dark:bg-green-900/20"
              title="Operational"
            >
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            </div>
            <div
              className="absolute left-[75%] top-[35%] flex h-6 w-6 items-center justify-center rounded-full bg-red-100 ring-2 ring-white dark:bg-red-900/20"
              title="Critical"
            >
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </div>
          </div>
          <div className="absolute bottom-4 left-4 rounded-md bg-white/90 p-2 shadow-md dark:bg-slate-800/90">
            <div className="text-xs font-medium">Legend</div>
            <div className="mt-1 flex flex-col gap-1">
              <div className="flex items-center gap-1">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
                  <CheckCircle2 className="h-3 w-3 text-green-600" />
                </div>
                <span className="text-xs">Operational</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/20">
                  <Clock className="h-3 w-3 text-yellow-600" />
                </div>
                <span className="text-xs">Maintenance</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                  <AlertTriangle className="h-3 w-3 text-red-600" />
                </div>
                <span className="text-xs">Critical</span>
              </div>
            </div>
          </div>
          <div className="absolute bottom-4 right-4">
            <Button
              variant="default"
              size="sm"
              className="flex items-center gap-1 bg-white/90 text-black hover:bg-white dark:bg-slate-800/90 dark:text-white dark:hover:bg-slate-700/90"
              onClick={() => router.push('/transformers/map')}
            >
              <Maximize2 className="h-4 w-4" />
              <span>Full Map</span>
            </Button>
          </div>
        </>
      )}
    </div>
  )
}
