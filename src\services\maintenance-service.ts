"use client"

// Define the maintenance record type
export interface MaintenanceRecord {
  id: string
  transformerId: string
  transformerSerialNumber: string
  type: string
  description: string
  scheduledDate: string
  status: "Scheduled" | "In Progress" | "Completed" | "Cancelled" | "Urgent"
  assignedTo: string
  priority: "low" | "medium" | "high"
  notes?: string
}

// Extended maintenance record with location information
export interface ExtendedMaintenanceRecord extends MaintenanceRecord {
  location: string
}

// Mock data for maintenance records
const mockMaintenanceRecords: ExtendedMaintenanceRecord[] = [
  {
    id: "maint-001",
    transformerId: "tr-001",
    transformerSerialNumber: "ETH-TR-10045",
    type: "Preventive Maintenance",
    description: "Routine inspection and oil testing",
    scheduledDate: "2025-05-15",
    status: "Scheduled",
    assignedTo: "Abebe Kebede",
    priority: "medium",
    location: "Addis Ababa - Bole",
    notes: "Check for oil leaks and inspect bushings"
  },
  {
    id: "maint-002",
    transformerId: "tr-002",
    transformerSerialNumber: "ETH-TR-10089",
    type: "Corrective Maintenance",
    description: "Replace damaged bushing",
    scheduledDate: "2025-05-10",
    status: "In Progress",
    assignedTo: "Tigist Haile",
    priority: "high",
    location: "Addis Ababa - Kirkos",
    notes: "Bushing damaged due to lightning strike"
  },
  {
    id: "maint-003",
    transformerId: "tr-003",
    transformerSerialNumber: "ETH-TR-10122",
    type: "Emergency Repair",
    description: "Fix oil leak and replace gasket",
    scheduledDate: "2025-05-05",
    status: "Completed",
    assignedTo: "Solomon Tesfaye",
    priority: "high",
    location: "Oromia - Adama",
    notes: "Severe oil leak detected during routine inspection"
  },
  {
    id: "maint-004",
    transformerId: "tr-004",
    transformerSerialNumber: "ETH-TR-10156",
    type: "Condition-Based Maintenance",
    description: "Replace cooling fans",
    scheduledDate: "2025-05-20",
    status: "Scheduled",
    assignedTo: "Hiwot Mengistu",
    priority: "medium",
    location: "Amhara - Bahir Dar",
    notes: "Cooling fans showing signs of wear"
  },
  {
    id: "maint-005",
    transformerId: "tr-005",
    transformerSerialNumber: "ETH-TR-10201",
    type: "Preventive Maintenance",
    description: "Annual inspection and testing",
    scheduledDate: "2025-05-25",
    status: "Scheduled",
    assignedTo: "Daniel Gebre",
    priority: "low",
    location: "SNNPR - Hawassa",
    notes: "Regular annual maintenance"
  },
  {
    id: "maint-006",
    transformerId: "tr-006",
    transformerSerialNumber: "ETH-TR-10245",
    type: "Corrective Maintenance",
    description: "Replace temperature gauge",
    scheduledDate: "2025-05-12",
    status: "Scheduled",
    assignedTo: "Meron Tadesse",
    priority: "medium",
    location: "Tigray - Mekelle",
    notes: "Temperature gauge giving inaccurate readings"
  },
  {
    id: "maint-007",
    transformerId: "tr-007",
    transformerSerialNumber: "ETH-TR-10289",
    type: "Emergency Repair",
    description: "Fix connection issue",
    scheduledDate: "2025-05-03",
    status: "Completed",
    assignedTo: "Yonas Bekele",
    priority: "high",
    location: "Addis Ababa - Arada",
    notes: "Connection issue causing intermittent power outages"
  },
  {
    id: "maint-008",
    transformerId: "tr-008",
    transformerSerialNumber: "ETH-TR-10334",
    type: "Preventive Maintenance",
    description: "Oil testing and filtration",
    scheduledDate: "2025-05-30",
    status: "Scheduled",
    assignedTo: "Sara Alemu",
    priority: "low",
    location: "Oromia - Jimma",
    notes: "Regular oil maintenance"
  }
];

// Function to get all maintenance records
export function getAllMaintenanceRecords(): ExtendedMaintenanceRecord[] {
  return [...mockMaintenanceRecords];
}

// Function to get upcoming maintenance records
export function getUpcomingMaintenanceRecords(): ExtendedMaintenanceRecord[] {
  return mockMaintenanceRecords.filter(record => record.status === "Scheduled");
}

// Function to get in-progress maintenance records
export function getInProgressMaintenanceRecords(): ExtendedMaintenanceRecord[] {
  return mockMaintenanceRecords.filter(record => record.status === "In Progress");
}

// Function to get completed maintenance records
export function getCompletedMaintenanceRecords(): ExtendedMaintenanceRecord[] {
  return mockMaintenanceRecords.filter(record => record.status === "Completed");
}

// Function to mark a maintenance record as completed
export function markMaintenanceAsCompleted(id: string): void {
  const record = mockMaintenanceRecords.find(r => r.id === id);
  if (record) {
    record.status = "Completed";
  }
}

// Function to cancel a maintenance record
export function cancelMaintenance(id: string): void {
  const record = mockMaintenanceRecords.find(r => r.id === id);
  if (record) {
    record.status = "Cancelled";
  }
}

// Function to update a maintenance record
export function updateMaintenanceRecord(id: string, updates: Partial<ExtendedMaintenanceRecord>): void {
  const record = mockMaintenanceRecords.find(r => r.id === id);
  if (record) {
    Object.assign(record, updates);
  }
}

// Function to get a maintenance record by ID
export function getMaintenanceRecordById(id: string): ExtendedMaintenanceRecord | undefined {
  return mockMaintenanceRecords.find(r => r.id === id);
}

// Function to add a new maintenance record
export function addMaintenanceRecord(record: Omit<ExtendedMaintenanceRecord, 'id'>): ExtendedMaintenanceRecord {
  const newId = `maint-${String(mockMaintenanceRecords.length + 1).padStart(3, '0')}`;
  const newRecord = { ...record, id: newId };
  mockMaintenanceRecords.push(newRecord as ExtendedMaintenanceRecord);
  return newRecord as ExtendedMaintenanceRecord;
}

// Function to get maintenance records by transformer ID
export function getMaintenanceRecordsByTransformerId(transformerId: string): ExtendedMaintenanceRecord[] {
  return mockMaintenanceRecords.filter(record => record.transformerId === transformerId);
}

// Function to get maintenance records by assigned technician
export function getMaintenanceRecordsByTechnician(technicianName: string): ExtendedMaintenanceRecord[] {
  return mockMaintenanceRecords.filter(record => record.assignedTo === technicianName);
}

// Function to get maintenance records by date range
export function getMaintenanceRecordsByDateRange(startDate: string, endDate: string): ExtendedMaintenanceRecord[] {
  return mockMaintenanceRecords.filter(record => {
    const recordDate = new Date(record.scheduledDate);
    const start = new Date(startDate);
    const end = new Date(endDate);
    return recordDate >= start && recordDate <= end;
  });
}

// Function to get maintenance records by type
export function getMaintenanceRecordsByType(type: string): ExtendedMaintenanceRecord[] {
  return mockMaintenanceRecords.filter(record => record.type === type);
}

// Function to get maintenance records by priority
export function getMaintenanceRecordsByPriority(priority: "low" | "medium" | "high"): ExtendedMaintenanceRecord[] {
  return mockMaintenanceRecords.filter(record => record.priority === priority);
}

// Function to get maintenance records by location
export function getMaintenanceRecordsByLocation(location: string): ExtendedMaintenanceRecord[] {
  return mockMaintenanceRecords.filter(record => record.location.includes(location));
}

// Function to filter maintenance records by type
export function filterMaintenanceRecordsByType(records: ExtendedMaintenanceRecord[], type: string): ExtendedMaintenanceRecord[] {
  if (type === "all") {
    return records;
  }

  const typeMap: Record<string, string> = {
    "routine": "Preventive Maintenance",
    "repair": "Corrective Maintenance",
    "oil": "Oil Sampling",
    "full": "Condition-Based Maintenance",
    "emergency": "Emergency Repair"
  };

  const targetType = typeMap[type] || type;

  return records.filter(record => record.type.includes(targetType));
}

// Function to search maintenance records
export function searchMaintenanceRecords(records: ExtendedMaintenanceRecord[], query: string): ExtendedMaintenanceRecord[] {
  if (!query || query.trim() === "") {
    return records;
  }

  const lowerQuery = query.toLowerCase();

  return records.filter(record =>
    record.transformerSerialNumber.toLowerCase().includes(lowerQuery) ||
    record.type.toLowerCase().includes(lowerQuery) ||
    record.description.toLowerCase().includes(lowerQuery) ||
    record.assignedTo.toLowerCase().includes(lowerQuery) ||
    record.location.toLowerCase().includes(lowerQuery) ||
    (record.notes && record.notes.toLowerCase().includes(lowerQuery))
  );
}

// Function to get maintenance statistics
export function getMaintenanceStatistics() {
  const records = mockMaintenanceRecords;

  return {
    total: records.length,
    scheduled: records.filter(r => r.status === "Scheduled").length,
    inProgress: records.filter(r => r.status === "In Progress").length,
    completed: records.filter(r => r.status === "Completed").length,
    cancelled: records.filter(r => r.status === "Cancelled").length,
    urgent: records.filter(r => r.status === "Urgent").length,
    highPriority: records.filter(r => r.priority === "high").length,
    mediumPriority: records.filter(r => r.priority === "medium").length,
    lowPriority: records.filter(r => r.priority === "low").length,
    preventive: records.filter(r => r.type.includes("Preventive")).length,
    corrective: records.filter(r => r.type.includes("Corrective")).length,
    emergency: records.filter(r => r.type.includes("Emergency")).length,
    conditionBased: records.filter(r => r.type.includes("Condition-Based")).length
  };
}

// Export a maintenance service object with all functions
export const maintenanceService = {
  getAllMaintenanceRecords,
  getUpcomingMaintenanceRecords,
  getInProgressMaintenanceRecords,
  getCompletedMaintenanceRecords,
  markMaintenanceAsCompleted,
  cancelMaintenance,
  updateMaintenanceRecord,
  getMaintenanceRecordById,
  addMaintenanceRecord,
  getMaintenanceRecordsByTransformerId,
  getMaintenanceRecordsByTechnician,
  getMaintenanceRecordsByDateRange,
  getMaintenanceRecordsByType,
  getMaintenanceRecordsByPriority,
  getMaintenanceRecordsByLocation,
  filterMaintenanceRecordsByType,
  searchMaintenanceRecords,
  getMaintenanceStatistics
};
