"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import {
  CheckCircle,
  Eye,
  MoreHorizontal,
  UserCheck,
  AlertTriangle,
  Thermometer,
  Battery,
  Zap,
  AlertCircle,
  MessageSquare,
  User,
  ExternalLink
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu"
import { useAlerts } from "@/src/contexts/alert-context"
import { AlertDetailDialog } from "./alert-detail-dialog"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/src/components/ui/tooltip"

interface AlertsTableProps {
  resolved?: boolean
}

export function AlertsTable({ resolved = false }: AlertsTableProps) {
  const router = useRouter()
  const { filteredAlerts, acknowledgeAlert, resolveAlert, assignAlert } = useAlerts()
  const [selectedAlertId, setSelectedAlertId] = useState<string | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)

  // Mock current user ID
  const currentUserId = "Current User"

  // Get alerts based on resolved status
  const displayAlerts = resolved
    ? filteredAlerts.filter(alert => alert.status === "Resolved")
    : filteredAlerts.filter(alert => alert.status !== "Resolved")

  // Navigate to transformer details page
  const navigateToTransformer = (transformerId: string) => {
    // Use the transformer ID directly (e.g., "tr-001")
    router.push(`/transformers/${transformerId}`)
  }

  // Get icon based on alert type
  const getAlertIcon = (type: string) => {
    switch (type) {
      case "Temperature": return Thermometer
      case "Oil Level": return Battery
      case "Load": return Zap
      case "Connection": return AlertCircle
      default: return AlertTriangle
    }
  }

  const handleViewDetails = (alertId: string) => {
    setSelectedAlertId(alertId)
    setIsDetailDialogOpen(true)
  }

  const handleAcknowledge = (alertId: string) => {
    acknowledgeAlert(alertId, currentUserId)
  }

  const handleResolve = (alertId: string) => {
    resolveAlert(alertId, currentUserId, "Resolved via quick action")
  }

  const handleAssign = (alertId: string, userId: string) => {
    assignAlert(alertId, userId)
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Transformer</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Severity</TableHead>
              <TableHead>Timestamp</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayAlerts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No alerts found.
                </TableCell>
              </TableRow>
            ) : (
              displayAlerts.map((alert) => {
                const AlertIcon = getAlertIcon(alert.type)
                return (
                  <TableRow
                    key={alert.id}
                    className="group cursor-pointer hover:bg-muted/50"
                    onClick={() => navigateToTransformer(alert.transformer)}
                  >
                    <TableCell className="font-medium">{alert.id}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span>{alert.transformer}</span>
                        <span className="text-xs text-muted-foreground hidden group-hover:inline">
                          ({alert.location})
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <AlertIcon className={
                          alert.severity === "Critical" ? "h-4 w-4 text-red-500" :
                          alert.severity === "High" ? "h-4 w-4 text-orange-500" :
                          alert.severity === "Medium" ? "h-4 w-4 text-yellow-500" :
                          "h-4 w-4 text-blue-500"
                        } />
                        <span>{alert.type}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        className={
                          alert.severity === "Critical"
                            ? "bg-red-500 hover:bg-red-600"
                            : alert.severity === "High"
                              ? "bg-orange-500 hover:bg-orange-600"
                              : alert.severity === "Medium"
                                ? "bg-yellow-500 hover:bg-yellow-600"
                                : "bg-blue-500 hover:bg-blue-600"
                        }
                      >
                        {alert.severity}
                      </Badge>
                    </TableCell>
                    <TableCell>{alert.timestamp}</TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={
                          alert.status === "Active" ? "border-red-500 text-red-500" :
                          alert.status === "Acknowledged" ? "border-blue-500 text-blue-500" :
                          alert.status === "In Progress" ? "border-yellow-500 text-yellow-500" :
                          "border-green-500 text-green-500"
                        }
                      >
                        {alert.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <TooltipProvider>
                        <div className="flex justify-end gap-1" onClick={(e) => e.stopPropagation()}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleViewDetails(alert.id);
                                }}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>View details</p>
                            </TooltipContent>
                          </Tooltip>

                          {!resolved && alert.status === "Active" && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleAcknowledge(alert.id);
                                  }}
                                >
                                  <UserCheck className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Acknowledge</p>
                              </TooltipContent>
                            </Tooltip>
                          )}

                          {!resolved && alert.status !== "Resolved" && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleResolve(alert.id);
                                  }}
                                >
                                  <CheckCircle className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Resolve</p>
                              </TooltipContent>
                            </Tooltip>
                          )}

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                handleViewDetails(alert.id);
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Alert Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                navigateToTransformer(alert.transformer);
                              }}>
                                <ExternalLink className="mr-2 h-4 w-4" />
                                View Transformer
                              </DropdownMenuItem>

                              {!resolved && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuLabel>Assign To</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={(e) => {
                                    e.stopPropagation();
                                    handleAssign(alert.id, "user1");
                                  }}>
                                    <User className="mr-2 h-4 w-4" />
                                    John Doe
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={(e) => {
                                    e.stopPropagation();
                                    handleAssign(alert.id, "user2");
                                  }}>
                                    <User className="mr-2 h-4 w-4" />
                                    Jane Smith
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={(e) => {
                                    e.stopPropagation();
                                    handleAssign(alert.id, "team1");
                                  }}>
                                    <User className="mr-2 h-4 w-4" />
                                    Tech Team Alpha
                                  </DropdownMenuItem>
                                </>
                              )}

                              {alert.status !== "Resolved" && !resolved && (
                                <>
                                  <DropdownMenuSeparator />
                                  {alert.status === "Active" && (
                                    <DropdownMenuItem onClick={(e) => {
                                      e.stopPropagation();
                                      handleAcknowledge(alert.id);
                                    }}>
                                      <UserCheck className="mr-2 h-4 w-4" />
                                      Acknowledge
                                    </DropdownMenuItem>
                                  )}
                                  <DropdownMenuItem onClick={(e) => {
                                    e.stopPropagation();
                                    handleResolve(alert.id);
                                  }}>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Resolve
                                  </DropdownMenuItem>
                                </>
                              )}

                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                                <MessageSquare className="mr-2 h-4 w-4" />
                                Add Note
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TooltipProvider>
                    </TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>

      {selectedAlertId && (
        <AlertDetailDialog
          open={isDetailDialogOpen}
          onOpenChange={setIsDetailDialogOpen}
          alertId={selectedAlertId}
        />
      )}
    </>
  )
}
