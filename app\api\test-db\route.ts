import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    console.log('Testing database connection...')
    
    // Import database connection utility
    const { testConnection, executeQuery } = await import('../../../lib/mysql-connection')
    
    // Test connection
    const isConnected = await testConnection()
    
    if (isConnected) {
      // Try a simple query
      const result = await executeQuery('SELECT 1 as test')
      
      return NextResponse.json({
        success: true,
        message: 'Database connection successful',
        result: result
      })
    } else {
      return NextResponse.json({
        success: false,
        message: 'Database connection failed'
      }, { status: 500 })
    }
    
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json({
      success: false,
      message: 'Database test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
