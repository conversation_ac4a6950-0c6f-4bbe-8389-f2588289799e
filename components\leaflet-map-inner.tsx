"use client"

import { useEffect, useState } from "react"
import { MapLocation } from "@/src/services/map-service"
import { mapService } from "@/src/services/map-service"

interface LeafletMapInnerProps {
  locations: MapLocation[]
  height?: string
  onMarkerClick?: (location: MapLocation) => void
  clustered?: boolean
}

export function LeafletMapInner({
  locations,
  height = "500px",
  onMarkerClick,
  clustered = true
}: LeafletMapInnerProps) {
  const [isClient, setIsClient] = useState(false)
  const [mapComponents, setMapComponents] = useState<any>(null)

  // Set isClient to true when component mounts and load Leaflet
  useEffect(() => {
    const loadLeaflet = async () => {
      try {
        // Import Leaflet CSS
        if (!document.querySelector('link[href*="leaflet.css"]')) {
          const link = document.createElement('link')
          link.rel = 'stylesheet'
          link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'
          link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY='
          link.crossOrigin = ''
          document.head.appendChild(link)
        }

        // Import React Leaflet components
        const reactLeaflet = await import('react-leaflet')
        
        setMapComponents({
          MapContainer: reactLeaflet.MapContainer,
          TileLayer: reactLeaflet.TileLayer,
          Marker: reactLeaflet.Marker,
          Popup: reactLeaflet.Popup
        })
        
        setIsClient(true)
      } catch (error) {
        console.error('Error loading Leaflet:', error)
      }
    }

    loadLeaflet()
  }, [])

  // Custom icon function
  const createCustomIcon = (status: string) => {
    if (typeof window === 'undefined' || !window.L) return null

    try {
      const L = window.L || require('leaflet')
      
      return L.divIcon({
        className: 'custom-marker',
        html: `<div style="
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background-color: ${mapService.getStatusColor(status)};
          border: 2px solid white;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        "></div>`,
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      })
    } catch (error) {
      console.error('Error creating custom icon:', error)
      return null
    }
  }

  // Loading state
  if (!isClient || !mapComponents) {
    return (
      <div
        style={{
          height,
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
          borderRadius: '0.5rem'
        }}
      >
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
          <p className="text-sm text-muted-foreground">Loading map...</p>
        </div>
      </div>
    )
  }

  // Calculate center
  const center = locations.length > 0
    ? [locations[0].latitude, locations[0].longitude]
    : [9.0222, 38.7468] // Default to Addis Ababa

  const { MapContainer, TileLayer, Marker, Popup } = mapComponents

  return (
    <div style={{ height, width: "100%" }}>
      <MapContainer
        center={center as [number, number]}
        zoom={6}
        style={{ height: "100%", width: "100%", borderRadius: "0.5rem" }}
        scrollWheelZoom={true}
        zoomControl={true}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* Display all markers */}
        {locations.map((location) => {
          const icon = createCustomIcon(location.status)
          
          return (
            <Marker
              key={location.id}
              position={[location.latitude, location.longitude]}
              icon={icon}
              eventHandlers={{
                click: () => onMarkerClick && onMarkerClick(location),
              }}
            >
              <Popup>
                <div style={{ minWidth: '200px' }}>
                  <h3 style={{ 
                    fontWeight: 'bold', 
                    marginBottom: '8px',
                    fontSize: '16px',
                    color: '#1f2937'
                  }}>
                    {location.title}
                  </h3>
                  <p style={{ 
                    margin: '0 0 8px 0',
                    fontSize: '14px',
                    color: '#6b7280'
                  }}>
                    {location.description}
                  </p>
                  <div style={{ 
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    marginTop: '8px'
                  }}>
                    <span style={{ fontSize: '14px', color: '#374151' }}>Status:</span>
                    <span style={{ 
                      color: mapService.getStatusColor(location.status),
                      fontWeight: 'bold',
                      fontSize: '14px',
                      textTransform: 'capitalize'
                    }}>
                      {location.status}
                    </span>
                  </div>
                  {location.data && (
                    <div style={{ 
                      marginTop: '12px',
                      padding: '8px',
                      backgroundColor: '#f9fafb',
                      borderRadius: '4px',
                      fontSize: '12px'
                    }}>
                      <div>ID: {location.data.id}</div>
                      {location.data.capacity && (
                        <div>Capacity: {location.data.capacity} kVA</div>
                      )}
                      {location.data.manufacturer && (
                        <div>Manufacturer: {location.data.manufacturer}</div>
                      )}
                    </div>
                  )}
                </div>
              </Popup>
            </Marker>
          )
        })}
      </MapContainer>
    </div>
  )
}

// Declare global Leaflet for TypeScript
declare global {
  interface Window {
    L: any
  }
}
