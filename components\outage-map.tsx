"use client"

import { useEffect, useRef, useState } from "react"
import { AlertTriangle } from "lucide-react"
import { Badge } from "@/src/components/ui/badge"

export function OutageMap() {
  const mapRef = useRef(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [outages, setOutages] = useState([
    {
      id: "OUT-1024",
      location: "Addis Ababa - Bole District",
      affected: 1250,
      status: "Active",
      startTime: "Apr 27, 2025 08:24 AM",
      estimatedResolution: "Apr 27, 2025 12:30 PM",
      cause: "Equipment Failure",
      position: { left: "30%", top: "40%" },
    },
    {
      id: "OUT-1025",
      location: "Dire Dawa - Central",
      affected: 850,
      status: "Active",
      startTime: "Apr 27, 2025 09:15 AM",
      estimatedResolution: "Apr 27, 2025 02:00 PM",
      cause: "Weather Related",
      position: { left: "65%", top: "35%" },
    },
    {
      id: "OUT-1026",
      location: "Bahir Dar - Lakeside",
      affected: 320,
      status: "Active",
      startTime: "Apr 27, 2025 07:30 AM",
      estimatedResolution: "Apr 27, 2025 11:00 AM",
      cause: "Scheduled Maintenance",
      position: { left: "25%", top: "25%" },
    },
    {
      id: "OUT-1027",
      location: "Hawassa - Downtown",
      affected: 560,
      status: "Active",
      startTime: "Apr 27, 2025 10:10 AM",
      estimatedResolution: "Apr 27, 2025 03:30 PM",
      cause: "Equipment Failure",
      position: { left: "45%", top: "60%" },
    },
  ])
  const [selectedOutage, setSelectedOutage] = useState<string | null>(null)

  useEffect(() => {
    // Simulate map loading
    const timer = setTimeout(() => {
      setMapLoaded(true)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="relative h-full w-full bg-slate-100 dark:bg-slate-800">
      {!mapLoaded ? (
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-center">
            <div className="mb-2 h-8 w-8 animate-spin rounded-full border-4 border-teal-600 border-t-transparent mx-auto"></div>
            <p className="text-sm text-muted-foreground">Loading map...</p>
          </div>
        </div>
      ) : (
        <>
          <div className="absolute inset-0 bg-[url('/placeholder.svg?height=500&width=800&text=Ethiopia+Map')] bg-cover bg-center opacity-50"></div>
          <div className="absolute inset-0">
            {/* Outage markers */}
            {outages.map((outage) => (
              <div
                key={outage.id}
                className={`absolute flex h-8 w-8 items-center justify-center rounded-full ${
                  outage.cause === "Equipment Failure"
                    ? "bg-red-100 ring-2 ring-red-500 dark:bg-red-900/20"
                    : outage.cause === "Weather Related"
                      ? "bg-orange-100 ring-2 ring-orange-500 dark:bg-orange-900/20"
                      : "bg-blue-100 ring-2 ring-blue-500 dark:bg-blue-900/20"
                } cursor-pointer transition-all hover:scale-110 ${
                  selectedOutage === outage.id ? "scale-125 z-10" : ""
                }`}
                style={{ left: outage.position.left, top: outage.position.top }}
                title={outage.location}
                onClick={() => setSelectedOutage(outage.id === selectedOutage ? null : outage.id)}
              >
                <AlertTriangle
                  className={`h-4 w-4 ${
                    outage.cause === "Equipment Failure"
                      ? "text-red-600"
                      : outage.cause === "Weather Related"
                        ? "text-orange-600"
                        : "text-blue-600"
                  }`}
                />
              </div>
            ))}
          </div>

          {/* Outage details panel */}
          {selectedOutage && (
            <div className="absolute bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-80 rounded-md bg-white/95 p-4 shadow-md dark:bg-slate-800/95">
              {outages
                .filter((outage) => outage.id === selectedOutage)
                .map((outage) => (
                  <div key={outage.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{outage.location}</h3>
                      <Badge
                        className={
                          outage.cause === "Equipment Failure"
                            ? "bg-red-500"
                            : outage.cause === "Weather Related"
                              ? "bg-orange-500"
                              : "bg-blue-500"
                        }
                      >
                        {outage.cause}
                      </Badge>
                    </div>
                    <div className="space-y-1 text-sm">
                      <p>
                        <span className="font-medium">Outage ID:</span> {outage.id}
                      </p>
                      <p>
                        <span className="font-medium">Affected Customers:</span> {outage.affected.toLocaleString()}
                      </p>
                      <p>
                        <span className="font-medium">Start Time:</span> {outage.startTime}
                      </p>
                      <p>
                        <span className="font-medium">Est. Resolution:</span> {outage.estimatedResolution}
                      </p>
                    </div>
                  </div>
                ))}
            </div>
          )}

          <div className="absolute bottom-4 left-4 rounded-md bg-white/90 p-2 shadow-md dark:bg-slate-800/90">
            <div className="text-xs font-medium">Legend</div>
            <div className="mt-1 flex flex-col gap-1">
              <div className="flex items-center gap-1">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                  <AlertTriangle className="h-3 w-3 text-red-600" />
                </div>
                <span className="text-xs">Equipment Failure</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
                  <AlertTriangle className="h-3 w-3 text-orange-600" />
                </div>
                <span className="text-xs">Weather Related</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
                  <AlertTriangle className="h-3 w-3 text-blue-600" />
                </div>
                <span className="text-xs">Scheduled Maintenance</span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
