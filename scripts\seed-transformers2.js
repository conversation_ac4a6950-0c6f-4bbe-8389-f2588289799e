// Seed demo data into app_transformers2
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    // Get region and service center ids
    const [regions] = await connection.execute('SELECT id FROM app_regions2');
    const [centers] = await connection.execute('SELECT id FROM app_service_centers2');
    const regionId = regions[0]?.id || 1;
    const centerId = centers[0]?.id || 1;
    // Insert demo transformers
    await connection.execute(
      `INSERT INTO app_transformers2 (serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary, manufacturer, model, year_manufactured, installation_date, location_name, latitude, longitude, region_id, service_center_id, status, efficiency_rating, load_factor, temperature, oil_level, last_maintenance, next_maintenance)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      ['TRF-001', 'Transformer 1', 'distribution', 500, 15000, 400, 'ABB', 'TX500', 2018, '2018-06-01', 'Bole Substation', 9.0123, 38.7612, regionId, centerId, 'operational', 97.5, 80.0, 45.2, 90.0, '2024-01-01', '2025-01-01']
    );
    await connection.execute(
      `INSERT INTO app_transformers2 (serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary, manufacturer, model, year_manufactured, installation_date, location_name, latitude, longitude, region_id, service_center_id, status, efficiency_rating, load_factor, temperature, oil_level, last_maintenance, next_maintenance)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      ['TRF-002', 'Transformer 2', 'power', 2000, 33000, 400, 'Siemens', 'PX2000', 2020, '2020-09-15', 'Adama Main', 8.5406, 39.2695, regionId, centerId, 'maintenance', 95.0, 70.0, 50.1, 85.0, '2024-02-01', '2025-02-01']
    );
    console.log('✅ Seeded app_transformers2');
  } catch (err) {
    console.error('❌ Error seeding app_transformers2:', err);
  } finally {
    await connection.end();
  }
})();
