"use client"

import { useState, useEffect } from "react"
import { Line, LineChart, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis, AreaChart, Area, BarChart, Bar } from "recharts"
import { ChartContainer, ChartTooltipContent } from "@/src/components/ui/chart"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Button } from "@/src/components/ui/button"
import { Download, Calendar, Filter, BarChart3, LineChart as LineChartIcon, PieChart } from "lucide-react"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { DatePickerWithRange } from "@/src/components/ui/date-range-picker"
import { DateRange } from "react-day-picker"
import { format, subMonths, startOfMonth, endOfMonth } from "date-fns"
import { SmartMeterUsageData } from "@/src/types/smart-meter"

export function SmartMeterUsageChart() {
  const [chartData, setChartData] = useState<SmartMeterUsageData[]>([])
  const [loading, setLoading] = useState(true)
  const [timeframe, setTimeframe] = useState("6months")
  const [region, setRegion] = useState("all")
  const [chartType, setChartType] = useState<"line" | "area" | "bar">("line")
  const [date, setDate] = useState<DateRange | undefined>({
    from: subMonths(new Date(), 6),
    to: new Date(),
  })
  const [summaryData, setSummaryData] = useState({
    residential: { value: 380, change: 5.5 },
    commercial: { value: 620, change: 4.8 },
    industrial: { value: 980, change: 3.2 }
  })

  // Fetch usage data from API
  useEffect(() => {
    const fetchUsageData = async () => {
      try {
        setLoading(true)

        // Build query parameters
        const params = new URLSearchParams()
        params.append("timeframe", timeframe === "daily" ? "daily" : "monthly")

        const response = await fetch(`/api/smart-meters/usage?${params.toString()}`)

        if (response.ok) {
          const data = await response.json()
          setChartData(data)

          // Calculate summary data
          if (data.length > 0) {
            const latest = data[data.length - 1]
            const previous = data.length > 1 ? data[data.length - 2] : { residential: 0, commercial: 0, industrial: 0 }

            const calculateChange = (current: number, previous: number) => {
              return previous === 0 ? 0 : ((current - previous) / previous) * 100
            }

            setSummaryData({
              residential: {
                value: Math.round(latest.residential),
                change: parseFloat(calculateChange(latest.residential, previous.residential).toFixed(1))
              },
              commercial: {
                value: Math.round(latest.commercial),
                change: parseFloat(calculateChange(latest.commercial, previous.commercial).toFixed(1))
              },
              industrial: {
                value: Math.round(latest.industrial),
                change: parseFloat(calculateChange(latest.industrial, previous.industrial).toFixed(1))
              }
            })
          }
        } else {
          console.error("Failed to fetch usage data")
        }
      } catch (error) {
        console.error("Error fetching usage data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchUsageData()
  }, [timeframe])

  // Handle export data
  const handleExportData = () => {
    // Create CSV content
    const headers = ["Date", "Residential (kWh)", "Commercial (kWh)", "Industrial (kWh)", "Total (kWh)"]
    const csvRows = [headers.join(",")]

    chartData.forEach(item => {
      const total = item.residential + item.commercial + item.industrial
      const row = [
        item.timestamp,
        item.residential.toFixed(2),
        item.commercial.toFixed(2),
        item.industrial.toFixed(2),
        total.toFixed(2)
      ]
      csvRows.push(row.join(","))
    })

    const csvContent = csvRows.join("\n")

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `smart-meter-usage-${format(new Date(), "yyyy-MM-dd")}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Format date for display
  const formatDate = (dateStr: string) => {
    if (dateStr.includes("-")) {
      // If it's a full date (YYYY-MM-DD)
      return format(new Date(dateStr), "MMM d, yyyy")
    } else {
      // If it's just a month (YYYY-MM)
      return format(new Date(dateStr + "-01"), "MMM yyyy")
    }
  }

  return (
    <div className="space-y-4">
      <Tabs defaultValue="usage" className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <TabsList>
            <TabsTrigger value="usage">Usage Trends</TabsTrigger>
            <TabsTrigger value="comparison">Regional Comparison</TabsTrigger>
            <TabsTrigger value="hourly">Hourly Patterns</TabsTrigger>
          </TabsList>
          <div className="flex items-center gap-2">
            <Select value={chartType} onValueChange={(value: "line" | "area" | "bar") => setChartType(value)}>
              <SelectTrigger className="w-[130px]">
                {chartType === "line" ? (
                  <LineChartIcon className="mr-2 h-4 w-4" />
                ) : chartType === "area" ? (
                  <AreaChart className="mr-2 h-4 w-4" />
                ) : (
                  <BarChart3 className="mr-2 h-4 w-4" />
                )}
                <SelectValue placeholder="Chart Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="line">Line Chart</SelectItem>
                <SelectItem value="area">Area Chart</SelectItem>
                <SelectItem value="bar">Bar Chart</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" onClick={handleExportData}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        <TabsContent value="usage" className="space-y-4">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <Calendar className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily (Last 30 Days)</SelectItem>
                  <SelectItem value="1month">Last Month</SelectItem>
                  <SelectItem value="3months">Last 3 Months</SelectItem>
                  <SelectItem value="6months">Last 6 Months</SelectItem>
                  <SelectItem value="1year">Last Year</SelectItem>
                </SelectContent>
              </Select>
              <Select value={region} onValueChange={setRegion}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Regions</SelectItem>
                  <SelectItem value="addis">Addis Ababa</SelectItem>
                  <SelectItem value="dire">Dire Dawa</SelectItem>
                  <SelectItem value="amhara">Amhara</SelectItem>
                  <SelectItem value="oromia">Oromia</SelectItem>
                  <SelectItem value="snnpr">SNNPR</SelectItem>
                  <SelectItem value="tigray">Tigray</SelectItem>
                  <SelectItem value="somali">Somali</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DatePickerWithRange date={date} setDate={setDate} />
          </div>

          {loading ? (
            <div className="h-[400px] flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="h-[400px]">
              <ChartContainer
                config={{
                  residential: {
                    label: "Residential",
                    color: "hsl(143, 85%, 40%)",
                  },
                  commercial: {
                    label: "Commercial",
                    color: "hsl(220, 85%, 60%)",
                  },
                  industrial: {
                    label: "Industrial",
                    color: "hsl(40, 95%, 55%)",
                  },
                }}
              >
                <ResponsiveContainer width="100%" height="100%">
                  {chartType === "line" ? (
                    <LineChart
                      data={chartData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis
                        dataKey="timestamp"
                        tickFormatter={(value) => formatDate(value)}
                      />
                      <YAxis />
                      <Tooltip
                        content={<ChartTooltipContent />}
                        labelFormatter={(value) => formatDate(value)}
                      />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="residential"
                        stroke="var(--color-residential)"
                        strokeWidth={2}
                        activeDot={{ r: 8 }}
                      />
                      <Line type="monotone" dataKey="commercial" stroke="var(--color-commercial)" strokeWidth={2} />
                      <Line type="monotone" dataKey="industrial" stroke="var(--color-industrial)" strokeWidth={2} />
                    </LineChart>
                  ) : chartType === "area" ? (
                    <AreaChart
                      data={chartData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis
                        dataKey="timestamp"
                        tickFormatter={(value) => formatDate(value)}
                      />
                      <YAxis />
                      <Tooltip
                        content={<ChartTooltipContent />}
                        labelFormatter={(value) => formatDate(value)}
                      />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="residential"
                        stackId="1"
                        stroke="var(--color-residential)"
                        fill="var(--color-residential)"
                        fillOpacity={0.6}
                      />
                      <Area
                        type="monotone"
                        dataKey="commercial"
                        stackId="1"
                        stroke="var(--color-commercial)"
                        fill="var(--color-commercial)"
                        fillOpacity={0.6}
                      />
                      <Area
                        type="monotone"
                        dataKey="industrial"
                        stackId="1"
                        stroke="var(--color-industrial)"
                        fill="var(--color-industrial)"
                        fillOpacity={0.6}
                      />
                    </AreaChart>
                  ) : (
                    <BarChart
                      data={chartData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis
                        dataKey="timestamp"
                        tickFormatter={(value) => formatDate(value)}
                      />
                      <YAxis />
                      <Tooltip
                        content={<ChartTooltipContent />}
                        labelFormatter={(value) => formatDate(value)}
                      />
                      <Legend />
                      <Bar dataKey="residential" fill="var(--color-residential)" radius={[4, 4, 0, 0]} />
                      <Bar dataKey="commercial" fill="var(--color-commercial)" radius={[4, 4, 0, 0]} />
                      <Bar dataKey="industrial" fill="var(--color-industrial)" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  )}
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          )}
        </TabsContent>

        <TabsContent value="comparison" className="space-y-4">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <Calendar className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Select timeframe" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1month">Last Month</SelectItem>
                <SelectItem value="3months">Last 3 Months</SelectItem>
                <SelectItem value="6months">Last 6 Months</SelectItem>
                <SelectItem value="1year">Last Year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="h-[400px] flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="h-[400px]">
              <ChartContainer
                config={{
                  "Addis Ababa": {
                    label: "Addis Ababa",
                    color: "hsl(143, 85%, 40%)",
                  },
                  "Dire Dawa": {
                    label: "Dire Dawa",
                    color: "hsl(220, 85%, 60%)",
                  },
                  "Amhara": {
                    label: "Amhara",
                    color: "hsl(40, 95%, 55%)",
                  },
                  "Oromia": {
                    label: "Oromia",
                    color: "hsl(0, 85%, 60%)",
                  },
                  "SNNPR": {
                    label: "SNNPR",
                    color: "hsl(270, 85%, 60%)",
                  },
                }}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={[
                      { region: "Addis Ababa", residential: 420, commercial: 680, industrial: 950 },
                      { region: "Dire Dawa", residential: 380, commercial: 620, industrial: 850 },
                      { region: "Amhara", residential: 350, commercial: 580, industrial: 780 },
                      { region: "Oromia", residential: 410, commercial: 650, industrial: 920 },
                      { region: "SNNPR", residential: 370, commercial: 610, industrial: 830 },
                    ]}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="region" />
                    <YAxis />
                    <Tooltip content={<ChartTooltipContent />} />
                    <Legend />
                    <Bar dataKey="residential" name="Residential" fill="var(--color-Addis Ababa)" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="commercial" name="Commercial" fill="var(--color-Dire Dawa)" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="industrial" name="Industrial" fill="var(--color-Amhara)" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          )}
        </TabsContent>

        <TabsContent value="hourly" className="space-y-4">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <Select value={region} onValueChange={setRegion}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Select region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Regions</SelectItem>
                <SelectItem value="addis">Addis Ababa</SelectItem>
                <SelectItem value="dire">Dire Dawa</SelectItem>
                <SelectItem value="amhara">Amhara</SelectItem>
                <SelectItem value="oromia">Oromia</SelectItem>
                <SelectItem value="snnpr">SNNPR</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="h-[400px] flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="h-[400px]">
              <ChartContainer
                config={{
                  residential: {
                    label: "Residential",
                    color: "hsl(143, 85%, 40%)",
                  },
                  commercial: {
                    label: "Commercial",
                    color: "hsl(220, 85%, 60%)",
                  },
                  industrial: {
                    label: "Industrial",
                    color: "hsl(40, 95%, 55%)",
                  },
                }}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={[
                      { hour: "00:00", residential: 120, commercial: 280, industrial: 520 },
                      { hour: "02:00", residential: 100, commercial: 250, industrial: 500 },
                      { hour: "04:00", residential: 90, commercial: 230, industrial: 490 },
                      { hour: "06:00", residential: 110, commercial: 260, industrial: 510 },
                      { hour: "08:00", residential: 150, commercial: 350, industrial: 580 },
                      { hour: "10:00", residential: 200, commercial: 450, industrial: 650 },
                      { hour: "12:00", residential: 230, commercial: 520, industrial: 720 },
                      { hour: "14:00", residential: 250, commercial: 550, industrial: 750 },
                      { hour: "16:00", residential: 270, commercial: 580, industrial: 780 },
                      { hour: "18:00", residential: 320, commercial: 620, industrial: 820 },
                      { hour: "20:00", residential: 280, commercial: 550, industrial: 750 },
                      { hour: "22:00", residential: 180, commercial: 380, industrial: 600 },
                    ]}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip content={<ChartTooltipContent />} />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="residential"
                      stroke="var(--color-residential)"
                      strokeWidth={2}
                      activeDot={{ r: 8 }}
                    />
                    <Line type="monotone" dataKey="commercial" stroke="var(--color-commercial)" strokeWidth={2} />
                    <Line type="monotone" dataKey="industrial" stroke="var(--color-industrial)" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          )}
        </TabsContent>
      </Tabs>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
          <h3 className="font-medium text-green-700 dark:text-green-300">Residential Usage</h3>
          <p className="text-2xl font-bold">{summaryData.residential.value} kWh</p>
          <p className="text-sm text-muted-foreground">
            {summaryData.residential.change >= 0 ? "+" : ""}{summaryData.residential.change}% from last period
          </p>
        </div>
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <h3 className="font-medium text-blue-700 dark:text-blue-300">Commercial Usage</h3>
          <p className="text-2xl font-bold">{summaryData.commercial.value} kWh</p>
          <p className="text-sm text-muted-foreground">
            {summaryData.commercial.change >= 0 ? "+" : ""}{summaryData.commercial.change}% from last period
          </p>
        </div>
        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
          <h3 className="font-medium text-yellow-700 dark:text-yellow-300">Industrial Usage</h3>
          <p className="text-2xl font-bold">{summaryData.industrial.value} kWh</p>
          <p className="text-sm text-muted-foreground">
            {summaryData.industrial.change >= 0 ? "+" : ""}{summaryData.industrial.change}% from last period
          </p>
        </div>
      </div>
    </div>
  )
}
