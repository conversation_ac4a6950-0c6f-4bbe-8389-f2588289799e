"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Lightbulb,
  LightbulbOff,
  Zap,
  Clock,
  MapPin,
  Users,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Search,
  Filter,
  Download,
  Plus,
  Settings,
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  Calendar,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Activity,
  Timer,
  Wrench,
  User
} from 'lucide-react'
import {
  LineChart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON>ian<PERSON>rid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON>ontaine<PERSON>
} from 'recharts'
import { ProtectedRoute } from "@/src/components/layout/protected-route"
import ProvidersWrapper from "@/components/providers-wrapper"

// Mock outage data
const mockOutages = [
  {
    id: 'OUT-2024-001',
    title: 'Transformer Failure - Lideta District',
    description: 'Primary transformer failure causing widespread outage',
    status: 'active',
    priority: 'critical',
    affectedCustomers: 2847,
    location: 'Lideta, Addis Ababa',
    transformerId: 'T-AA-001',
    reportedAt: '2024-02-12T14:30:00Z',
    estimatedRestoration: '2024-02-12T18:00:00Z',
    actualRestoration: null,
    cause: 'equipment_failure',
    assignedTeam: 'Emergency Response Team A',
    assignedTechnician: 'John Doe',
    customerComplaints: 156,
    updates: [
      { time: '2024-02-12T14:30:00Z', message: 'Outage reported and confirmed', status: 'reported' },
      { time: '2024-02-12T14:45:00Z', message: 'Emergency team dispatched', status: 'investigating' },
      { time: '2024-02-12T15:15:00Z', message: 'Transformer failure confirmed', status: 'diagnosed' }
    ]
  },
  {
    id: 'OUT-2024-002',
    title: 'Planned Maintenance - Sebeta Substation',
    description: 'Scheduled maintenance on distribution lines',
    status: 'scheduled',
    priority: 'medium',
    affectedCustomers: 1245,
    location: 'Sebeta, Oromia',
    transformerId: 'T-OR-045',
    reportedAt: '2024-02-10T09:00:00Z',
    estimatedRestoration: '2024-02-15T16:00:00Z',
    actualRestoration: null,
    cause: 'planned_maintenance',
    assignedTeam: 'Maintenance Team B',
    assignedTechnician: 'Jane Smith',
    customerComplaints: 23,
    updates: [
      { time: '2024-02-10T09:00:00Z', message: 'Maintenance scheduled', status: 'scheduled' },
      { time: '2024-02-12T08:00:00Z', message: 'Customer notifications sent', status: 'notified' }
    ]
  },
  {
    id: 'OUT-2024-003',
    title: 'Weather-Related Outage - Bahir Dar',
    description: 'Power lines damaged by storm',
    status: 'resolved',
    priority: 'high',
    affectedCustomers: 892,
    location: 'Bahir Dar, Amhara',
    transformerId: 'T-AM-023',
    reportedAt: '2024-02-11T22:15:00Z',
    estimatedRestoration: '2024-02-12T06:00:00Z',
    actualRestoration: '2024-02-12T05:45:00Z',
    cause: 'weather',
    assignedTeam: 'Emergency Response Team C',
    assignedTechnician: 'Bob Johnson',
    customerComplaints: 67,
    updates: [
      { time: '2024-02-11T22:15:00Z', message: 'Storm damage reported', status: 'reported' },
      { time: '2024-02-11T23:00:00Z', message: 'Damage assessment in progress', status: 'investigating' },
      { time: '2024-02-12T02:30:00Z', message: 'Repair work started', status: 'repairing' },
      { time: '2024-02-12T05:45:00Z', message: 'Power restored', status: 'resolved' }
    ]
  }
]

const outageStats = {
  total: 156,
  active: 23,
  scheduled: 12,
  resolved: 121,
  critical: 5,
  high: 8,
  medium: 10,
  totalCustomersAffected: 45892,
  avgResolutionTime: '4.2 hours',
  mttr: '3.8 hours'
}

const statusColors = {
  active: 'bg-red-100 text-red-800 border-red-200',
  scheduled: 'bg-blue-100 text-blue-800 border-blue-200',
  investigating: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  repairing: 'bg-orange-100 text-orange-800 border-orange-200',
  resolved: 'bg-green-100 text-green-800 border-green-200'
}

const priorityColors = {
  critical: 'bg-red-100 text-red-800 border-red-200',
  high: 'bg-orange-100 text-orange-800 border-orange-200',
  medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  low: 'bg-green-100 text-green-800 border-green-200'
}

const causeIcons = {
  equipment_failure: <Zap className="h-4 w-4 text-red-500" />,
  weather: <AlertTriangle className="h-4 w-4 text-yellow-500" />,
  planned_maintenance: <Wrench className="h-4 w-4 text-blue-500" />,
  external_factor: <XCircle className="h-4 w-4 text-gray-500" />
}

export default function OutagesPage() {
  const [outages, setOutages] = useState(mockOutages)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedPriority, setSelectedPriority] = useState('all')
  const [selectedCause, setSelectedCause] = useState('all')

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  const filteredOutages = outages.filter(outage => {
    const matchesSearch = outage.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         outage.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         outage.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || outage.status === selectedStatus
    const matchesPriority = selectedPriority === 'all' || outage.priority === selectedPriority
    const matchesCause = selectedCause === 'all' || outage.cause === selectedCause

    return matchesSearch && matchesStatus && matchesPriority && matchesCause
  })

  const handleUpdateOutage = (outageId: string, newStatus: string) => {
    setOutages(prev => prev.map(outage =>
      outage.id === outageId ? {
        ...outage,
        status: newStatus,
        actualRestoration: newStatus === 'resolved' ? new Date().toISOString() : null
      } : outage
    ))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading outage data...</p>
        </div>
      </div>
    )
  }

  return (
    <ProvidersWrapper>
      <ProtectedRoute
        allowedRoles={[
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "customer_service_agent"
        ]}
        requiredPermissions={[{ resource: "outages", action: "read" }]}
      >
        <div className="space-y-6 p-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Outage Management</h1>
              <p className="text-muted-foreground">
                Monitor, manage, and resolve power outages across Ethiopian Electric Utility
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Outage Settings
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Report Outage
              </Button>
            </div>
          </div>

          {/* Outage Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Outages</CardTitle>
                <LightbulbOff className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{outageStats.total}</div>
                <p className="text-xs text-muted-foreground">
                  {outageStats.active} active, {outageStats.scheduled} scheduled
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Outages</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{outageStats.active}</div>
                <p className="text-xs text-muted-foreground">
                  {outageStats.critical} critical priority
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Affected Customers</CardTitle>
                <Users className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{outageStats.totalCustomersAffected.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  Currently without power
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Resolution Time</CardTitle>
                <Timer className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{outageStats.avgResolutionTime}</div>
                <p className="text-xs text-muted-foreground">
                  MTTR: {outageStats.mttr}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="active" className="space-y-4">
            <TabsList>
              <TabsTrigger value="active">Active Outages</TabsTrigger>
              <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="space-y-4">
              {/* Filters */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <Input
                        placeholder="Search outages..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="max-w-sm"
                      />
                    </div>
                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="border rounded-md px-3 py-2"
                    >
                      <option value="all">All Status</option>
                      <option value="active">Active</option>
                      <option value="investigating">Investigating</option>
                      <option value="repairing">Repairing</option>
                      <option value="resolved">Resolved</option>
                    </select>
                    <select
                      value={selectedPriority}
                      onChange={(e) => setSelectedPriority(e.target.value)}
                      className="border rounded-md px-3 py-2"
                    >
                      <option value="all">All Priority</option>
                      <option value="critical">Critical</option>
                      <option value="high">High</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                    </select>
                    <select
                      value={selectedCause}
                      onChange={(e) => setSelectedCause(e.target.value)}
                      className="border rounded-md px-3 py-2"
                    >
                      <option value="all">All Causes</option>
                      <option value="equipment_failure">Equipment Failure</option>
                      <option value="weather">Weather</option>
                      <option value="planned_maintenance">Planned Maintenance</option>
                      <option value="external_factor">External Factor</option>
                    </select>
                    <Button variant="outline">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Outages List */}
              <div className="space-y-4">
                {filteredOutages.map((outage) => (
                  <Card key={outage.id} className={`${outage.priority === 'critical' ? 'border-l-4 border-l-red-500' : ''}`}>
                    <CardContent className="pt-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          <div className="flex-shrink-0">
                            {causeIcons[outage.cause as keyof typeof causeIcons]}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold">{outage.title}</h3>
                              <Badge className={priorityColors[outage.priority as keyof typeof priorityColors]}>
                                {outage.priority}
                              </Badge>
                              <Badge className={statusColors[outage.status as keyof typeof statusColors]}>
                                {outage.status}
                              </Badge>
                            </div>
                            <p className="text-muted-foreground mb-3">{outage.description}</p>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                <span>{outage.location}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                <span>{outage.affectedCustomers.toLocaleString()} customers</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Zap className="h-3 w-3" />
                                <span>{outage.transformerId}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                <span>{outage.assignedTechnician}</span>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm mt-2">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>Reported: {new Date(outage.reportedAt).toLocaleString()}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Timer className="h-3 w-3" />
                                <span>ETA: {new Date(outage.estimatedRestoration).toLocaleString()}</span>
                              </div>
                              {outage.actualRestoration && (
                                <div className="flex items-center gap-1">
                                  <CheckCircle className="h-3 w-3" />
                                  <span>Restored: {new Date(outage.actualRestoration).toLocaleString()}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleUpdateOutage(outage.id, 'investigating')}
                            disabled={outage.status === 'resolved'}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                          {outage.status !== 'resolved' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUpdateOutage(outage.id, 'resolved')}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Mark Resolved
                            </Button>
                          )}
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-1" />
                            Update
                          </Button>
                        </div>
                      </div>

                      {/* Progress Timeline */}
                      <div className="mt-4 pt-4 border-t">
                        <h4 className="text-sm font-medium mb-3">Progress Updates</h4>
                        <div className="space-y-2">
                          {outage.updates.map((update, index) => (
                            <div key={index} className="flex items-start gap-3 text-sm">
                              <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <span className="font-medium">{update.message}</span>
                                  <span className="text-muted-foreground">
                                    {new Date(update.time).toLocaleString()}
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="scheduled" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Scheduled Maintenance Outages</CardTitle>
                  <CardDescription>Planned outages and maintenance windows</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {outages.filter(o => o.status === 'scheduled').map((outage) => (
                      <div key={outage.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-start gap-4">
                          <Wrench className="h-5 w-5 text-blue-500 mt-1" />
                          <div>
                            <h4 className="font-medium">{outage.title}</h4>
                            <p className="text-sm text-muted-foreground">{outage.description}</p>
                            <div className="flex items-center gap-4 text-xs text-muted-foreground mt-2">
                              <span>{outage.location}</span>
                              <span>{outage.affectedCustomers.toLocaleString()} customers</span>
                              <span>Team: {outage.assignedTeam}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className={statusColors[outage.status as keyof typeof statusColors]}>
                            {outage.status}
                          </Badge>
                          <p className="text-sm text-muted-foreground mt-1">
                            {new Date(outage.estimatedRestoration).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Outage History</CardTitle>
                  <CardDescription>Historical outage data and resolution patterns</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-muted-foreground">
                    <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Historical outage data table would be displayed here</p>
                    <p className="text-sm mt-2">Including resolution times, causes, and performance metrics</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Outage Trends */}
                <Card>
                  <CardHeader>
                    <CardTitle>Outage Trends (30 days)</CardTitle>
                    <CardDescription>Daily outage frequency and resolution times</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <RechartsLineChart data={Array.from({ length: 30 }, (_, i) => ({
                        day: i + 1,
                        outages: 2 + Math.floor(Math.random() * 8),
                        resolved: 1 + Math.floor(Math.random() * 6),
                        avgResolution: 2 + Math.random() * 6
                      }))}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="day" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="outages" stroke="#ef4444" name="New Outages" />
                        <Line type="monotone" dataKey="resolved" stroke="#10b981" name="Resolved" />
                        <Line type="monotone" dataKey="avgResolution" stroke="#3b82f6" name="Avg Resolution (hrs)" />
                      </RechartsLineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Outage Causes */}
                <Card>
                  <CardHeader>
                    <CardTitle>Outage Causes</CardTitle>
                    <CardDescription>Distribution of outage root causes</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={[
                            { name: 'Equipment Failure', value: 45, fill: '#ef4444' },
                            { name: 'Weather', value: 30, fill: '#f59e0b' },
                            { name: 'Planned Maintenance', value: 15, fill: '#3b82f6' },
                            { name: 'External Factors', value: 10, fill: '#6b7280' }
                          ]}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        />
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>

              {/* Regional Impact Analysis */}
              <Card>
                <CardHeader>
                  <CardTitle>Regional Impact Analysis</CardTitle>
                  <CardDescription>Outage frequency and customer impact by region</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={[
                      { region: 'Addis Ababa', outages: 45, customers: 125000, avgDuration: 3.2 },
                      { region: 'Oromia', outages: 38, customers: 98000, avgDuration: 4.1 },
                      { region: 'Amhara', outages: 32, customers: 87000, avgDuration: 3.8 },
                      { region: 'Tigray', outages: 28, customers: 65000, avgDuration: 4.5 },
                      { region: 'SNNPR', outages: 25, customers: 72000, avgDuration: 3.9 }
                    ]}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="region" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="outages" fill="#ef4444" name="Total Outages" />
                      <Bar dataKey="avgDuration" fill="#3b82f6" name="Avg Duration (hrs)" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Performance Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                  <CardDescription>Key performance indicators for outage management</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">96.8%</div>
                      <p className="text-sm text-muted-foreground">System Reliability</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">3.8 hrs</div>
                      <p className="text-sm text-muted-foreground">Mean Time to Repair</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">12 min</div>
                      <p className="text-sm text-muted-foreground">Avg Response Time</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">89%</div>
                      <p className="text-sm text-muted-foreground">Customer Satisfaction</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </ProtectedRoute>
    </ProvidersWrapper>
  )
}