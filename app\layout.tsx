import "../src/styles/globals.css"
import { Inter } from "next/font/google"
import type { Metadata } from "next"


const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Ethiopia Electric Utility - Transformer Management System",
  description: "Advanced distribution transformer management system for Ethiopia Electric Utility",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}
