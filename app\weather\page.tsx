"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Cloud,
  CloudRain,
  Sun,
  CloudSnow,
  Wind,
  Thermometer,
  Droplets,
  Eye,
  Gauge,
  Zap,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  MapPin,
  Clock,
  RefreshCw,
  Download,
  Settings,
  Activity,
  BarChart3,
  LineChart,
  Calendar,
  Umbrella,
  CloudLightning
} from 'lucide-react'
import {
  LineChart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { ProtectedRoute } from "@/src/components/layout/protected-route"
import ProvidersWrapper from "@/components/providers-wrapper"

// Mock weather data for Ethiopian regions
const mockWeatherData = [
  {
    region: 'Addis Ababa',
    current: {
      temperature: 22,
      humidity: 65,
      windSpeed: 12,
      pressure: 1013,
      visibility: 10,
      uvIndex: 6,
      condition: 'partly_cloudy',
      precipitation: 0
    },
    forecast: Array.from({ length: 7 }, (_, i) => ({
      date: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      high: 24 + Math.random() * 6,
      low: 12 + Math.random() * 4,
      condition: ['sunny', 'partly_cloudy', 'cloudy', 'rainy'][Math.floor(Math.random() * 4)],
      precipitation: Math.random() * 20,
      humidity: 50 + Math.random() * 30
    })),
    transformerImpact: {
      riskLevel: 'low',
      affectedTransformers: 2,
      recommendations: ['Monitor temperature sensors', 'Check cooling systems']
    }
  },
  {
    region: 'Oromia',
    current: {
      temperature: 28,
      humidity: 45,
      windSpeed: 8,
      pressure: 1015,
      visibility: 15,
      uvIndex: 8,
      condition: 'sunny',
      precipitation: 0
    },
    forecast: Array.from({ length: 7 }, (_, i) => ({
      date: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      high: 30 + Math.random() * 8,
      low: 18 + Math.random() * 6,
      condition: ['sunny', 'partly_cloudy', 'cloudy'][Math.floor(Math.random() * 3)],
      precipitation: Math.random() * 10,
      humidity: 40 + Math.random() * 25
    })),
    transformerImpact: {
      riskLevel: 'medium',
      affectedTransformers: 8,
      recommendations: ['Increase cooling capacity', 'Monitor load levels', 'Check insulation']
    }
  },
  {
    region: 'Amhara',
    current: {
      temperature: 18,
      humidity: 75,
      windSpeed: 15,
      pressure: 1010,
      visibility: 8,
      uvIndex: 4,
      condition: 'rainy',
      precipitation: 5.2
    },
    forecast: Array.from({ length: 7 }, (_, i) => ({
      date: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      high: 20 + Math.random() * 5,
      low: 10 + Math.random() * 4,
      condition: ['cloudy', 'rainy', 'partly_cloudy'][Math.floor(Math.random() * 3)],
      precipitation: Math.random() * 25,
      humidity: 60 + Math.random() * 25
    })),
    transformerImpact: {
      riskLevel: 'high',
      affectedTransformers: 15,
      recommendations: ['Check moisture protection', 'Inspect seals', 'Monitor insulation resistance', 'Prepare for lightning protection']
    }
  }
]

const weatherIcons = {
  sunny: <Sun className="h-6 w-6 text-yellow-500" />,
  partly_cloudy: <Cloud className="h-6 w-6 text-gray-500" />,
  cloudy: <Cloud className="h-6 w-6 text-gray-600" />,
  rainy: <CloudRain className="h-6 w-6 text-blue-500" />,
  stormy: <CloudLightning className="h-6 w-6 text-purple-500" />,
  snowy: <CloudSnow className="h-6 w-6 text-blue-300" />
}

const riskColors = {
  low: 'bg-green-100 text-green-800 border-green-200',
  medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  high: 'bg-red-100 text-red-800 border-red-200'
}

const weatherAlerts = [
  {
    id: 1,
    type: 'warning',
    title: 'Heavy Rain Expected',
    region: 'Amhara',
    description: 'Heavy rainfall expected in the next 6 hours. Monitor transformer moisture levels.',
    severity: 'high',
    validUntil: '2024-02-13T18:00:00Z',
    affectedTransformers: 15
  },
  {
    id: 2,
    type: 'advisory',
    title: 'High Temperature Alert',
    region: 'Oromia',
    description: 'Temperature expected to reach 35°C. Increase cooling system monitoring.',
    severity: 'medium',
    validUntil: '2024-02-13T20:00:00Z',
    affectedTransformers: 8
  }
]

export default function WeatherPage() {
  const [weatherData, setWeatherData] = useState(mockWeatherData)
  const [loading, setLoading] = useState(true)
  const [selectedRegion, setSelectedRegion] = useState('all')
  const [selectedTimeframe, setSelectedTimeframe] = useState('7d')

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  const filteredWeatherData = selectedRegion === 'all'
    ? weatherData
    : weatherData.filter(data => data.region === selectedRegion)

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading weather data...</p>
        </div>
      </div>
    )
  }

  return (
    <ProvidersWrapper>
      <ProtectedRoute
        allowedRoles={[
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager"
        ]}
      >
        <div className="space-y-6 p-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Weather Impact Analysis</h1>
              <p className="text-muted-foreground">
                Monitor weather conditions and their impact on transformer operations
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Alert Settings
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
              <Button>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Data
              </Button>
            </div>
          </div>

          {/* Weather Alerts */}
          {weatherAlerts.length > 0 && (
            <Card className="border-l-4 border-l-yellow-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                  Active Weather Alerts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {weatherAlerts.map((alert) => (
                    <div key={alert.id} className="flex items-start justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">{alert.title}</h4>
                          <Badge className={riskColors[alert.severity as keyof typeof riskColors]}>
                            {alert.severity}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{alert.description}</p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {alert.region}
                          </span>
                          <span className="flex items-center gap-1">
                            <Zap className="h-3 w-3" />
                            {alert.affectedTransformers} transformers
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Valid until {new Date(alert.validUntil).toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Regional Weather Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {weatherData.map((region) => (
              <Card key={region.region}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{region.region}</CardTitle>
                    {weatherIcons[region.current.condition as keyof typeof weatherIcons]}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Temperature:</span>
                      <span className="text-lg font-bold">{region.current.temperature}°C</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Humidity:</span>
                      <span className="text-sm font-medium">{region.current.humidity}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Wind Speed:</span>
                      <span className="text-sm font-medium">{region.current.windSpeed} km/h</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Precipitation:</span>
                      <span className="text-sm font-medium">{region.current.precipitation} mm</span>
                    </div>

                    <div className="pt-3 border-t">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Transformer Impact:</span>
                        <Badge className={riskColors[region.transformerImpact.riskLevel as keyof typeof riskColors]}>
                          {region.transformerImpact.riskLevel} risk
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {region.transformerImpact.affectedTransformers} transformers affected
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Main Content */}
          <Tabs defaultValue="current" className="space-y-4">
            <TabsList>
              <TabsTrigger value="current">Current Conditions</TabsTrigger>
              <TabsTrigger value="forecast">7-Day Forecast</TabsTrigger>
              <TabsTrigger value="impact">Impact Analysis</TabsTrigger>
              <TabsTrigger value="historical">Historical Data</TabsTrigger>
            </TabsList>

            <TabsContent value="current" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Temperature Trends */}
                <Card>
                  <CardHeader>
                    <CardTitle>Temperature Trends (24h)</CardTitle>
                    <CardDescription>Regional temperature monitoring</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <RechartsLineChart data={Array.from({ length: 24 }, (_, i) => ({
                        hour: `${i}:00`,
                        'Addis Ababa': 18 + Math.random() * 8,
                        'Oromia': 22 + Math.random() * 12,
                        'Amhara': 15 + Math.random() * 6
                      }))}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="hour" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="Addis Ababa" stroke="#3b82f6" />
                        <Line type="monotone" dataKey="Oromia" stroke="#10b981" />
                        <Line type="monotone" dataKey="Amhara" stroke="#f59e0b" />
                      </RechartsLineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Humidity & Precipitation */}
                <Card>
                  <CardHeader>
                    <CardTitle>Humidity & Precipitation</CardTitle>
                    <CardDescription>Moisture levels affecting transformer operations</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <AreaChart data={Array.from({ length: 24 }, (_, i) => ({
                        hour: `${i}:00`,
                        humidity: 40 + Math.random() * 40,
                        precipitation: Math.random() * 10
                      }))}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="hour" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Area type="monotone" dataKey="humidity" stackId="1" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                        <Area type="monotone" dataKey="precipitation" stackId="2" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>

              {/* Current Conditions Detail */}
              <Card>
                <CardHeader>
                  <CardTitle>Detailed Current Conditions</CardTitle>
                  <CardDescription>Comprehensive weather metrics for all regions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">Region</th>
                          <th className="text-left p-2">Temperature</th>
                          <th className="text-left p-2">Humidity</th>
                          <th className="text-left p-2">Wind</th>
                          <th className="text-left p-2">Pressure</th>
                          <th className="text-left p-2">Visibility</th>
                          <th className="text-left p-2">UV Index</th>
                          <th className="text-left p-2">Risk Level</th>
                        </tr>
                      </thead>
                      <tbody>
                        {weatherData.map((region) => (
                          <tr key={region.region} className="border-b">
                            <td className="p-2 font-medium">{region.region}</td>
                            <td className="p-2">{region.current.temperature}°C</td>
                            <td className="p-2">{region.current.humidity}%</td>
                            <td className="p-2">{region.current.windSpeed} km/h</td>
                            <td className="p-2">{region.current.pressure} hPa</td>
                            <td className="p-2">{region.current.visibility} km</td>
                            <td className="p-2">{region.current.uvIndex}</td>
                            <td className="p-2">
                              <Badge className={riskColors[region.transformerImpact.riskLevel as keyof typeof riskColors]}>
                                {region.transformerImpact.riskLevel}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="forecast" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {weatherData.map((region) => (
                  <Card key={region.region}>
                    <CardHeader>
                      <CardTitle>{region.region} - 7 Day Forecast</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {region.forecast.map((day, index) => (
                          <div key={index} className="flex items-center justify-between p-2 border rounded">
                            <div className="flex items-center gap-3">
                              {weatherIcons[day.condition as keyof typeof weatherIcons]}
                              <div>
                                <p className="text-sm font-medium">
                                  {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}
                                </p>
                                <p className="text-xs text-muted-foreground capitalize">{day.condition.replace('_', ' ')}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="text-sm font-medium">{day.high.toFixed(0)}° / {day.low.toFixed(0)}°</p>
                              <p className="text-xs text-muted-foreground">{day.precipitation.toFixed(1)}mm</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="impact" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Risk Assessment */}
                <Card>
                  <CardHeader>
                    <CardTitle>Risk Assessment</CardTitle>
                    <CardDescription>Weather-related risks to transformer operations</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {weatherData.map((region) => (
                        <div key={region.region} className="p-4 border rounded-lg">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-medium">{region.region}</h4>
                            <Badge className={riskColors[region.transformerImpact.riskLevel as keyof typeof riskColors]}>
                              {region.transformerImpact.riskLevel} risk
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-3">
                            {region.transformerImpact.affectedTransformers} transformers potentially affected
                          </p>
                          <div className="space-y-2">
                            <p className="text-sm font-medium">Recommendations:</p>
                            <ul className="text-sm text-muted-foreground space-y-1">
                              {region.transformerImpact.recommendations.map((rec, index) => (
                                <li key={index} className="flex items-start gap-2">
                                  <span className="text-xs">•</span>
                                  <span>{rec}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Impact Metrics */}
                <Card>
                  <CardHeader>
                    <CardTitle>Impact Metrics</CardTitle>
                    <CardDescription>Quantified weather impact on transformer performance</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Temperature Impact</span>
                          <span>Medium</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">15 transformers affected by high temperatures</p>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Moisture Impact</span>
                          <span>High</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-red-500 h-2 rounded-full" style={{ width: '80%' }}></div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">25 transformers at risk from moisture</p>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Wind Impact</span>
                          <span>Low</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-green-500 h-2 rounded-full" style={{ width: '25%' }}></div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">3 transformers affected by strong winds</p>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Lightning Risk</span>
                          <span>Medium</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-purple-500 h-2 rounded-full" style={{ width: '45%' }}></div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">12 transformers in storm-prone areas</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Transformer Status by Weather Condition */}
              <Card>
                <CardHeader>
                  <CardTitle>Transformer Status by Weather Condition</CardTitle>
                  <CardDescription>Real-time status of transformers under current weather conditions</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={[
                      { condition: 'Normal', operational: 1156, warning: 12, critical: 2 },
                      { condition: 'High Temp', operational: 45, warning: 8, critical: 3 },
                      { condition: 'High Humidity', operational: 32, warning: 15, critical: 8 },
                      { condition: 'Storm Risk', operational: 18, warning: 6, critical: 4 }
                    ]}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="condition" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="operational" fill="#10b981" name="Operational" />
                      <Bar dataKey="warning" fill="#f59e0b" name="Warning" />
                      <Bar dataKey="critical" fill="#ef4444" name="Critical" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="historical" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Historical Weather Data</CardTitle>
                  <CardDescription>Long-term weather patterns and transformer performance correlation</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-muted-foreground">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Historical weather data analysis would be displayed here</p>
                    <p className="text-sm mt-2">Including seasonal patterns, extreme weather events, and performance correlations</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </ProtectedRoute>
    </ProvidersWrapper>
  )
}