"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Badge } from '@/src/components/ui/badge'
import { Zap, CheckCircle, AlertTriangle, AlertCircle, BarChart2 } from 'lucide-react'

interface TransformerStatusData {
  total: number
  byStatus: {
    operational: number
    maintenance: number
    warning: number
    critical: number
  }
  byRegion: Array<{
    name: string
    count: number
    operational: number
    maintenance: number
    warning: number
    critical: number
  }>
}

export default function TransformerStatusWidget() {
  const [data, setData] = useState<TransformerStatusData>({
    total: 135,
    byStatus: {
      operational: 119,
      maintenance: 8,
      warning: 5,
      critical: 3
    },
    byRegion: [
      { name: 'Addis Ababa', count: 42, operational: 38, maintenance: 2, warning: 1, critical: 1 },
      { name: 'Oromia', count: 35, operational: 31, maintenance: 2, warning: 1, critical: 1 },
      { name: '<PERSON><PERSON>', count: 28, operational: 25, maintenance: 1, warning: 2, critical: 0 },
      { name: 'SNNPR', count: 20, operational: 17, maintenance: 2, warning: 0, critical: 1 },
      { name: 'Tigray', count: 10, operational: 8, maintenance: 1, warning: 1, critical: 0 }
    ]
  })
  
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Fetch transformer status data
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // In a real implementation, this would fetch from the API
        // For now, we're using mock data
        await new Promise(resolve => setTimeout(resolve, 1000))
      } catch (error) {
        console.error('Error fetching transformer status:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational':
        return 'text-green-600 bg-green-50'
      case 'maintenance':
        return 'text-blue-600 bg-blue-50'
      case 'warning':
        return 'text-orange-600 bg-orange-50'
      case 'critical':
        return 'text-red-600 bg-red-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational':
        return <CheckCircle className="h-4 w-4" />
      case 'maintenance':
        return <Zap className="h-4 w-4" />
      case 'warning':
        return <AlertCircle className="h-4 w-4" />
      case 'critical':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Zap className="h-4 w-4" />
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Transformer Status</CardTitle>
          <CardDescription>Loading transformer status data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-48">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Transformer Status
        </CardTitle>
        <CardDescription>
          Overview of transformer operational status across all regions
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Status Summary Cards */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold">{data.total}</div>
            <div className="text-sm text-muted-foreground">Total Transformers</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{data.byStatus.operational}</div>
            <div className="text-sm text-muted-foreground">Operational</div>
          </div>
        </div>

        {/* Status Breakdown */}
        <div className="space-y-3 mb-6">
          {Object.entries(data.byStatus).map(([status, count]) => (
            <div key={status} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getStatusIcon(status)}
                <span className="capitalize">{status}</span>
              </div>
              <Badge variant="secondary" className={getStatusColor(status)}>
                {count}
              </Badge>
            </div>
          ))}
        </div>

        {/* Regional Breakdown */}
        <div>
          <h4 className="font-medium mb-3">By Region</h4>
          <div className="space-y-2">
            {data.byRegion.map((region) => (
              <div key={region.name} className="flex items-center justify-between text-sm">
                <span>{region.name}</span>
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">{region.count} total</span>
                  <Badge variant="outline" className="text-green-600">
                    {region.operational} operational
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
