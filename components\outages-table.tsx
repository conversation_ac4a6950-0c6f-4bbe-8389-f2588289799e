import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { Button } from "@/src/components/ui/button"

interface OutagesTableProps {
  status?: "active" | "scheduled" | "resolved"
}

export function OutagesTable({ status = "active" }: OutagesTableProps) {
  const outages = [
    {
      id: "OUT-1024",
      location: "Addis Ababa - Bole District",
      affected: 1250,
      status: "Active",
      startTime: "Apr 27, 2025 08:24 AM",
      estimatedResolution: "Apr 27, 2025 12:30 PM",
      cause: "Equipment Failure",
      transformers: ["TRF-0945", "TRF-1024"],
    },
    {
      id: "OUT-1025",
      location: "Dire Dawa - Central",
      affected: 850,
      status: "Active",
      startTime: "Apr 27, 2025 09:15 AM",
      estimatedResolution: "Apr 27, 2025 02:00 PM",
      cause: "Weather Related",
      transformers: ["TRF-0872"],
    },
    {
      id: "OUT-1026",
      location: "Bahir Dar - Lakeside",
      affected: 320,
      status: "Scheduled",
      startTime: "Apr 28, 2025 07:30 AM",
      estimatedResolution: "Apr 28, 2025 11:00 AM",
      cause: "Scheduled Maintenance",
      transformers: ["TRF-1105"],
    },
    {
      id: "OUT-1027",
      location: "Hawassa - Downtown",
      affected: 560,
      status: "Active",
      startTime: "Apr 27, 2025 10:10 AM",
      estimatedResolution: "Apr 27, 2025 03:30 PM",
      cause: "Equipment Failure",
      transformers: ["TRF-1187"],
    },
    {
      id: "OUT-1023",
      location: "Mekelle - North District",
      affected: 780,
      status: "Resolved",
      startTime: "Apr 26, 2025 02:15 PM",
      estimatedResolution: "Apr 26, 2025 06:45 PM",
      cause: "Weather Related",
      transformers: ["TRF-0991"],
    },
  ]

  const filteredOutages = outages.filter((outage) => {
    if (status === "active") return outage.status === "Active"
    if (status === "scheduled") return outage.status === "Scheduled"
    if (status === "resolved") return outage.status === "Resolved"
    return true
  })

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>ID</TableHead>
          <TableHead>Location</TableHead>
          <TableHead>Affected Customers</TableHead>
          <TableHead>Start Time</TableHead>
          <TableHead>Est. Resolution</TableHead>
          <TableHead>Cause</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {filteredOutages.map((outage) => (
          <TableRow key={outage.id}>
            <TableCell className="font-medium">{outage.id}</TableCell>
            <TableCell>{outage.location}</TableCell>
            <TableCell>{outage.affected.toLocaleString()}</TableCell>
            <TableCell>{outage.startTime}</TableCell>
            <TableCell>{outage.estimatedResolution}</TableCell>
            <TableCell>
              <Badge
                className={
                  outage.cause === "Equipment Failure"
                    ? "bg-red-500 hover:bg-red-600"
                    : outage.cause === "Weather Related"
                      ? "bg-orange-500 hover:bg-orange-600"
                      : "bg-blue-500 hover:bg-blue-600"
                }
              >
                {outage.cause}
              </Badge>
            </TableCell>
            <TableCell className="text-right">
              <Button variant="ghost" size="sm">
                {status === "resolved" ? "Details" : "Update"}
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
