"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePicker } from "@/components/ui/date-picker"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Filter, 
  X, 
  Search, 
  MapPin, 
  Building, 
  Calendar, 
  Zap, 
  Thermometer,
  Gauge,
  AlertTriangle,
  Settings,
  RefreshCw
} from 'lucide-react'

interface FilterOptions {
  // Geographic Filters
  regions: string[]
  serviceCenters: string[]
  
  // Transformer Filters
  transformerTypes: string[]
  transformerStatuses: string[]
  manufacturers: string[]
  capacityRange: [number, number]
  efficiencyRange: [number, number]
  temperatureRange: [number, number]
  loadFactorRange: [number, number]
  
  // Date Filters
  dateRange: {
    from: Date | null
    to: Date | null
  }
  
  // Maintenance Filters
  maintenanceTypes: string[]
  maintenanceStatuses: string[]
  maintenancePriorities: string[]
  
  // Alert Filters
  alertSeverities: string[]
  alertTypes: string[]
  alertStatuses: string[]
  
  // Asset Filters
  criticalities: string[]
  customerTypes: string[]
  assetValueRange: [number, number]
  
  // Search
  searchQuery: string
}

interface ComprehensiveFilterPanelProps {
  onFiltersChange: (filters: FilterOptions) => void
  onReset: () => void
  className?: string
}

export function ComprehensiveFilterPanel({ onFiltersChange, onReset, className }: ComprehensiveFilterPanelProps) {
  const [filters, setFilters] = useState<FilterOptions>({
    regions: [],
    serviceCenters: [],
    transformerTypes: [],
    transformerStatuses: [],
    manufacturers: [],
    capacityRange: [0, 2000],
    efficiencyRange: [90, 100],
    temperatureRange: [0, 100],
    loadFactorRange: [0, 100],
    dateRange: { from: null, to: null },
    maintenanceTypes: [],
    maintenanceStatuses: [],
    maintenancePriorities: [],
    alertSeverities: [],
    alertTypes: [],
    alertStatuses: [],
    criticalities: [],
    customerTypes: [],
    assetValueRange: [0, 500000],
    searchQuery: ''
  })

  const [isExpanded, setIsExpanded] = useState(false)
  const [activeFiltersCount, setActiveFiltersCount] = useState(0)

  // Filter options data
  const filterData = {
    regions: [
      { value: 'AA', label: 'Addis Ababa' },
      { value: 'OR', label: 'Oromia' },
      { value: 'AM', label: 'Amhara' },
      { value: 'TI', label: 'Tigray' },
      { value: 'SN', label: 'SNNP' },
      { value: 'SO', label: 'Somali' },
      { value: 'AF', label: 'Afar' },
      { value: 'BG', label: 'Benishangul-Gumuz' }
    ],
    serviceCenters: [
      { value: 'AA-MAIN', label: 'Addis Ababa Main' },
      { value: 'OR-MAIN', label: 'Oromia Regional' },
      { value: 'AM-MAIN', label: 'Amhara Regional' },
      { value: 'TI-MAIN', label: 'Tigray Regional' },
      { value: 'SN-MAIN', label: 'SNNP Regional' }
    ],
    transformerTypes: [
      { value: 'distribution', label: 'Distribution' },
      { value: 'power', label: 'Power' },
      { value: 'instrument', label: 'Instrument' },
      { value: 'auto', label: 'Auto' }
    ],
    transformerStatuses: [
      { value: 'operational', label: 'Operational' },
      { value: 'warning', label: 'Warning' },
      { value: 'maintenance', label: 'Maintenance' },
      { value: 'critical', label: 'Critical' },
      { value: 'burnt', label: 'Burnt' },
      { value: 'offline', label: 'Offline' }
    ],
    manufacturers: [
      { value: 'Siemens', label: 'Siemens' },
      { value: 'ABB', label: 'ABB' },
      { value: 'Schneider Electric', label: 'Schneider Electric' },
      { value: 'GE', label: 'General Electric' }
    ],
    maintenanceTypes: [
      { value: 'routine', label: 'Routine' },
      { value: 'preventive', label: 'Preventive' },
      { value: 'corrective', label: 'Corrective' },
      { value: 'emergency', label: 'Emergency' },
      { value: 'seasonal', label: 'Seasonal' },
      { value: 'predictive', label: 'Predictive' }
    ],
    maintenanceStatuses: [
      { value: 'scheduled', label: 'Scheduled' },
      { value: 'in_progress', label: 'In Progress' },
      { value: 'completed', label: 'Completed' },
      { value: 'cancelled', label: 'Cancelled' },
      { value: 'postponed', label: 'Postponed' },
      { value: 'overdue', label: 'Overdue' }
    ],
    maintenancePriorities: [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'critical', label: 'Critical' }
    ],
    alertSeverities: [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'critical', label: 'Critical' }
    ],
    alertTypes: [
      { value: 'temperature', label: 'Temperature' },
      { value: 'voltage', label: 'Voltage' },
      { value: 'load', label: 'Load' },
      { value: 'maintenance', label: 'Maintenance' },
      { value: 'communication', label: 'Communication' },
      { value: 'weather', label: 'Weather' },
      { value: 'security', label: 'Security' },
      { value: 'performance', label: 'Performance' }
    ],
    alertStatuses: [
      { value: 'active', label: 'Active' },
      { value: 'investigating', label: 'Investigating' },
      { value: 'resolved', label: 'Resolved' },
      { value: 'monitoring', label: 'Monitoring' },
      { value: 'escalated', label: 'Escalated' }
    ],
    criticalities: [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'critical', label: 'Critical' }
    ],
    customerTypes: [
      { value: 'residential', label: 'Residential' },
      { value: 'commercial', label: 'Commercial' },
      { value: 'industrial', label: 'Industrial' },
      { value: 'agricultural', label: 'Agricultural' }
    ]
  }

  // Count active filters
  useEffect(() => {
    let count = 0
    if (filters.regions.length > 0) count++
    if (filters.serviceCenters.length > 0) count++
    if (filters.transformerTypes.length > 0) count++
    if (filters.transformerStatuses.length > 0) count++
    if (filters.manufacturers.length > 0) count++
    if (filters.maintenanceTypes.length > 0) count++
    if (filters.maintenanceStatuses.length > 0) count++
    if (filters.maintenancePriorities.length > 0) count++
    if (filters.alertSeverities.length > 0) count++
    if (filters.alertTypes.length > 0) count++
    if (filters.alertStatuses.length > 0) count++
    if (filters.criticalities.length > 0) count++
    if (filters.customerTypes.length > 0) count++
    if (filters.searchQuery.trim() !== '') count++
    if (filters.dateRange.from || filters.dateRange.to) count++
    
    setActiveFiltersCount(count)
  }, [filters])

  // Update filters and notify parent
  const updateFilters = (newFilters: Partial<FilterOptions>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    onFiltersChange(updatedFilters)
  }

  // Handle multi-select filters
  const handleMultiSelect = (filterKey: keyof FilterOptions, value: string) => {
    const currentValues = filters[filterKey] as string[]
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value]
    
    updateFilters({ [filterKey]: newValues })
  }

  // Handle range filters
  const handleRangeChange = (filterKey: keyof FilterOptions, values: number[]) => {
    updateFilters({ [filterKey]: [values[0], values[1]] })
  }

  // Reset all filters
  const handleReset = () => {
    const resetFilters: FilterOptions = {
      regions: [],
      serviceCenters: [],
      transformerTypes: [],
      transformerStatuses: [],
      manufacturers: [],
      capacityRange: [0, 2000],
      efficiencyRange: [90, 100],
      temperatureRange: [0, 100],
      loadFactorRange: [0, 100],
      dateRange: { from: null, to: null },
      maintenanceTypes: [],
      maintenanceStatuses: [],
      maintenancePriorities: [],
      alertSeverities: [],
      alertTypes: [],
      alertStatuses: [],
      criticalities: [],
      customerTypes: [],
      assetValueRange: [0, 500000],
      searchQuery: ''
    }
    setFilters(resetFilters)
    onReset()
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            <CardTitle className="text-lg">Advanced Filters</CardTitle>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount} active
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={activeFiltersCount === 0}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Reset
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Search */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Search
          </Label>
          <Input
            placeholder="Search transformers, locations, or serial numbers..."
            value={filters.searchQuery}
            onChange={(e) => updateFilters({ searchQuery: e.target.value })}
          />
        </div>

        {/* Geographic Filters */}
        <div className="space-y-4">
          <Label className="flex items-center gap-2 text-base font-semibold">
            <MapPin className="h-4 w-4" />
            Geographic Filters
          </Label>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Regions */}
            <div className="space-y-2">
              <Label>Regions</Label>
              <div className="grid grid-cols-2 gap-2">
                {filterData.regions.map((region) => (
                  <div key={region.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`region-${region.value}`}
                      checked={filters.regions.includes(region.value)}
                      onCheckedChange={() => handleMultiSelect('regions', region.value)}
                    />
                    <Label htmlFor={`region-${region.value}`} className="text-sm">
                      {region.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Service Centers */}
            <div className="space-y-2">
              <Label>Service Centers</Label>
              <div className="space-y-2">
                {filterData.serviceCenters.map((center) => (
                  <div key={center.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`center-${center.value}`}
                      checked={filters.serviceCenters.includes(center.value)}
                      onCheckedChange={() => handleMultiSelect('serviceCenters', center.value)}
                    />
                    <Label htmlFor={`center-${center.value}`} className="text-sm">
                      {center.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Transformer Filters */}
        <div className="space-y-4">
          <Label className="flex items-center gap-2 text-base font-semibold">
            <Zap className="h-4 w-4" />
            Transformer Filters
          </Label>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Transformer Types */}
            <div className="space-y-2">
              <Label>Types</Label>
              <div className="space-y-2">
                {filterData.transformerTypes.map((type) => (
                  <div key={type.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${type.value}`}
                      checked={filters.transformerTypes.includes(type.value)}
                      onCheckedChange={() => handleMultiSelect('transformerTypes', type.value)}
                    />
                    <Label htmlFor={`type-${type.value}`} className="text-sm">
                      {type.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Transformer Statuses */}
            <div className="space-y-2">
              <Label>Status</Label>
              <div className="space-y-2">
                {filterData.transformerStatuses.map((status) => (
                  <div key={status.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status.value}`}
                      checked={filters.transformerStatuses.includes(status.value)}
                      onCheckedChange={() => handleMultiSelect('transformerStatuses', status.value)}
                    />
                    <Label htmlFor={`status-${status.value}`} className="text-sm">
                      {status.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Manufacturers */}
            <div className="space-y-2">
              <Label>Manufacturers</Label>
              <div className="space-y-2">
                {filterData.manufacturers.map((manufacturer) => (
                  <div key={manufacturer.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`manufacturer-${manufacturer.value}`}
                      checked={filters.manufacturers.includes(manufacturer.value)}
                      onCheckedChange={() => handleMultiSelect('manufacturers', manufacturer.value)}
                    />
                    <Label htmlFor={`manufacturer-${manufacturer.value}`} className="text-sm">
                      {manufacturer.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {isExpanded && (
          <>
            <Separator />

            {/* Performance Ranges */}
            <div className="space-y-4">
              <Label className="flex items-center gap-2 text-base font-semibold">
                <Gauge className="h-4 w-4" />
                Performance Ranges
              </Label>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Capacity Range */}
                <div className="space-y-3">
                  <Label>Capacity (kVA): {filters.capacityRange[0]} - {filters.capacityRange[1]}</Label>
                  <Slider
                    value={filters.capacityRange}
                    onValueChange={(values) => handleRangeChange('capacityRange', values)}
                    max={2000}
                    min={0}
                    step={50}
                    className="w-full"
                  />
                </div>

                {/* Efficiency Range */}
                <div className="space-y-3">
                  <Label>Efficiency (%): {filters.efficiencyRange[0]} - {filters.efficiencyRange[1]}</Label>
                  <Slider
                    value={filters.efficiencyRange}
                    onValueChange={(values) => handleRangeChange('efficiencyRange', values)}
                    max={100}
                    min={90}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                {/* Temperature Range */}
                <div className="space-y-3">
                  <Label>Temperature (°C): {filters.temperatureRange[0]} - {filters.temperatureRange[1]}</Label>
                  <Slider
                    value={filters.temperatureRange}
                    onValueChange={(values) => handleRangeChange('temperatureRange', values)}
                    max={100}
                    min={0}
                    step={1}
                    className="w-full"
                  />
                </div>

                {/* Load Factor Range */}
                <div className="space-y-3">
                  <Label>Load Factor (%): {filters.loadFactorRange[0]} - {filters.loadFactorRange[1]}</Label>
                  <Slider
                    value={filters.loadFactorRange}
                    onValueChange={(values) => handleRangeChange('loadFactorRange', values)}
                    max={100}
                    min={0}
                    step={1}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Additional Filters */}
            <div className="space-y-4">
              <Label className="flex items-center gap-2 text-base font-semibold">
                <Settings className="h-4 w-4" />
                Additional Filters
              </Label>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Criticality */}
                <div className="space-y-2">
                  <Label>Criticality</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {filterData.criticalities.map((criticality) => (
                      <div key={criticality.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`criticality-${criticality.value}`}
                          checked={filters.criticalities.includes(criticality.value)}
                          onCheckedChange={() => handleMultiSelect('criticalities', criticality.value)}
                        />
                        <Label htmlFor={`criticality-${criticality.value}`} className="text-sm">
                          {criticality.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Customer Types */}
                <div className="space-y-2">
                  <Label>Customer Types</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {filterData.customerTypes.map((customerType) => (
                      <div key={customerType.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`customer-${customerType.value}`}
                          checked={filters.customerTypes.includes(customerType.value)}
                          onCheckedChange={() => handleMultiSelect('customerTypes', customerType.value)}
                        />
                        <Label htmlFor={`customer-${customerType.value}`} className="text-sm">
                          {customerType.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Asset Value Range */}
              <div className="space-y-3">
                <Label>Asset Value ($): ${filters.assetValueRange[0].toLocaleString()} - ${filters.assetValueRange[1].toLocaleString()}</Label>
                <Slider
                  value={filters.assetValueRange}
                  onValueChange={(values) => handleRangeChange('assetValueRange', values)}
                  max={500000}
                  min={0}
                  step={10000}
                  className="w-full"
                />
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
