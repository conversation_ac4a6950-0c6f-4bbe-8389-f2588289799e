"use client"

import { useState, useEffect } from "react"
import Image, { ImageProps } from "next/image"
import { cn } from "@/src/lib/utils"

interface OptimizedImageProps extends Omit<ImageProps, "onLoadingComplete"> {
  fallbackSrc?: string
  aspectRatio?: number
  containerClassName?: string
  fadeIn?: boolean
  loadingClassName?: string
  loadingComponent?: React.ReactNode
}

/**
 * A wrapper around next/image with additional features:
 * - Fade-in animation when loaded
 * - Fallback image when loading fails
 * - Aspect ratio preservation
 * - Loading state indicator
 */
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  fallbackSrc = "/static/images/image-placeholder.jpg",
  aspectRatio,
  className,
  containerClassName,
  fadeIn = true,
  loadingClassName,
  loadingComponent,
  priority = false,
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(!priority)
  const [error, setError] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Handle SSR
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Calculate aspect ratio styles
  const aspectRatioStyle = aspectRatio
    ? {
        aspectRatio: `${aspectRatio}`,
        objectFit: "cover" as const,
      }
    : {}

  // Handle image load
  const handleImageLoad = () => {
    setIsLoading(false)
  }

  // Handle image error
  const handleImageError = () => {
    setError(true)
    setIsLoading(false)
  }

  // If not client-side and not priority, show nothing (will be rendered on client)
  if (!isClient && !priority) {
    return null
  }

  return (
    <div
      className={cn(
        "relative overflow-hidden",
        aspectRatio && `aspect-[${aspectRatio}]`,
        containerClassName
      )}
      style={aspectRatioStyle}
    >
      {isLoading && (
        <div
          className={cn(
            "absolute inset-0 flex items-center justify-center bg-muted/30",
            loadingClassName
          )}
        >
          {loadingComponent || (
            <div className="h-8 w-8 rounded-full border-2 border-primary border-t-transparent animate-spin" />
          )}
        </div>
      )}

      <Image
        src={error ? fallbackSrc : src}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          "object-cover",
          fadeIn && "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100",
          className
        )}
        onLoad={handleImageLoad}
        onError={handleImageError}
        priority={priority}
        {...props}
      />
    </div>
  )
}

/**
 * A lightweight version of OptimizedImage for avatars and icons
 */
export function OptimizedAvatar({
  src,
  alt,
  size = 40,
  className,
  fallbackSrc = "/static/images/avatar-placeholder.jpg",
  ...props
}: Omit<OptimizedImageProps, "width" | "height"> & { size?: number }) {
  const [error, setError] = useState(false)

  return (
    <Image
      src={error ? fallbackSrc : src}
      alt={alt}
      width={size}
      height={size}
      className={cn("rounded-full object-cover", className)}
      onError={() => setError(true)}
      {...props}
    />
  )
}
