/**
 * Add Basic Sample Data
 * This script adds maintenance schedules and alerts to existing transformers
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function addBasicData() {
  let connection;
  
  try {
    console.log('🔌 Connecting to MySQL database...');
    connection = await mysql.createConnection(config);
    console.log('✅ Connected successfully');

    // First, check existing transformers
    console.log('📊 Checking existing transformers...');
    const [transformers] = await connection.execute('SELECT id, name FROM app_transformers LIMIT 5');
    console.log(`Found ${transformers.length} transformers:`);
    transformers.forEach(t => console.log(`  - ID: ${t.id}, Name: ${t.name}`));

    if (transformers.length === 0) {
      console.log('❌ No transformers found. Please add transformers first.');
      return;
    }

    // Add maintenance schedules
    console.log('\n🔧 Adding maintenance schedules...');
    
    const maintenanceSchedules = [
      {
        transformer_id: transformers[0].id,
        type: 'preventive',
        title: 'Quarterly Maintenance Check',
        description: 'Regular quarterly maintenance including oil check, temperature monitoring, and connection inspection.',
        scheduled_date: '2024-12-30',
        status: 'scheduled',
        priority: 'medium',
        estimated_duration: 4
      }
    ];

    if (transformers.length > 1) {
      maintenanceSchedules.push({
        transformer_id: transformers[1].id,
        type: 'routine',
        title: 'Monthly Inspection',
        description: 'Monthly routine inspection and cleaning.',
        scheduled_date: '2024-12-25',
        status: 'completed',
        priority: 'low',
        estimated_duration: 2
      });
    }

    for (const schedule of maintenanceSchedules) {
      await connection.execute(`
        INSERT INTO app_maintenance_schedules (
          transformer_id, type, title, description, scheduled_date,
          status, priority, estimated_duration, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        schedule.transformer_id, schedule.type, schedule.title,
        schedule.description, schedule.scheduled_date, schedule.status,
        schedule.priority, schedule.estimated_duration
      ]);
    }

    console.log(`✅ Added ${maintenanceSchedules.length} maintenance schedules`);

    // Add alerts
    console.log('\n🚨 Adding alerts...');
    
    const alerts = [
      {
        transformer_id: transformers[0].id,
        title: 'High Temperature Alert',
        description: 'Transformer temperature has exceeded 70°C. Immediate inspection recommended.',
        severity: 'high',
        type: 'temperature',
        priority: 'high',
        is_resolved: false
      }
    ];

    if (transformers.length > 1) {
      alerts.push({
        transformer_id: transformers[1].id,
        title: 'Overload Warning',
        description: 'Transformer is operating at high capacity, monitoring required.',
        severity: 'medium',
        type: 'load',
        priority: 'medium',
        is_resolved: false
      });
    }

    for (const alert of alerts) {
      await connection.execute(`
        INSERT INTO app_alerts (
          transformer_id, title, description, severity, type,
          priority, is_resolved, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        alert.transformer_id, alert.title, alert.description,
        alert.severity, alert.type, alert.priority, alert.is_resolved
      ]);
    }

    console.log(`✅ Added ${alerts.length} alerts`);

    // Show final counts
    console.log('\n📊 Final data summary:');
    const [tCount] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    const [mCount] = await connection.execute('SELECT COUNT(*) as count FROM app_maintenance_schedules');
    const [aCount] = await connection.execute('SELECT COUNT(*) as count FROM app_alerts');
    
    console.log(`  - Transformers: ${tCount[0].count}`);
    console.log(`  - Maintenance Schedules: ${mCount[0].count}`);
    console.log(`  - Alerts: ${aCount[0].count}`);
    
    console.log('\n🎉 Sample data setup complete! You can now test the dashboard.');
    
  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

addBasicData();
