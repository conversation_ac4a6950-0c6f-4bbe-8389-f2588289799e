"use client"

/**
 * Unified Dashboard Component - Reorganized
 * 
 * Modern, feature-based dashboard component with:
 * - Clean separation of concerns
 * - Optimized imports
 * - Better performance
 * - Consistent patterns
 */

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import { 
  Activity, AlertTriangle, BarChart2, Battery, Bell, Calendar,
  CheckCircle2, Clock, Download, Eye, Filter, Gauge, Globe,
  Lightbulb, MapPin, MoreVertical, Plus, RefreshCw, Search,
  Settings, Shield, Thermometer, TrendingDown, TrendingUp,
  Users, Wrench, Zap, ChevronDown, ExternalLink, FileText,
  Info, Layers, LineChart, Monitor, PieChart, Power, Database,
  Maximize2, Edit, Trash2, XCircle, CheckCircle, AlertCircle
} from "lucide-react"

// UI Components
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/src/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/src/components/ui/tabs"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Progress } from "@/src/components/ui/progress"
import { Skeleton } from "@/src/components/ui/skeleton"
import { useToast } from "@/src/components/ui/use-toast"

// Hooks and Utils
import { useAuth } from "@/src/features/auth/hooks/use-auth"
import { useDashboard } from "@/src/features/dashboard/hooks/use-dashboard"
import { format } from "date-fns"
import { cn } from "@/src/lib/utils"
import Link from "next/link"

// Feature Components
import { DashboardStats } from "./dashboard-stats"
import { QuickActions } from "./quick-actions"
import { AlertsPanel } from "./alerts-panel"
import { MaintenancePanel } from "./maintenance-panel"
import { AnalyticsPanel } from "./analytics-panel"
import { ReportsPanel } from "./reports-panel"

// Types
import type { DashboardData, DashboardStats as StatsType } from "@/src/types/dashboard"

export interface UnifiedDashboardProps {
  initialData?: DashboardData;
}

export function UnifiedDashboard({ initialData }: UnifiedDashboardProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()

  // Custom hook for dashboard logic
  const {
    data: dashboardData,
    stats,
    isLoading,
    lastUpdated,
    realTimeEnabled,
    refreshing,
    region,
    timeRange,
    setRegion,
    setTimeRange,
    toggleRealTime,
    refresh,
    executeAction
  } = useDashboard(initialData)

  // State for active tab
  const [activeTab, setActiveTab] = useState("overview")

  // Dialog states
  const [dialogs, setDialogs] = useState({
    addTransformer: false,
    scheduleMaintenance: false,
    generateReport: false,
    exportData: false
  })

  const openDialog = (dialogName: keyof typeof dialogs) => {
    setDialogs(prev => ({ ...prev, [dialogName]: true }))
  }

  const closeDialog = (dialogName: keyof typeof dialogs) => {
    setDialogs(prev => ({ ...prev, [dialogName]: false }))
  }

  // Action handlers
  const handleQuickAction = async (action: string, payload?: any) => {
    try {
      await executeAction(action, payload)
      toast({
        title: "Action Completed",
        description: `${action} completed successfully`,
      })
    } catch (error) {
      toast({
        title: "Action Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive",
      })
    }
  }

  // Role-based permissions
  const permissions = {
    canAddTransformer: ["super_admin", "national_asset_manager", "regional_asset_manager"].includes(user?.role || ""),
    canScheduleMaintenance: ["super_admin", "national_maintenance_manager", "regional_maintenance_engineer"].includes(user?.role || ""),
    canGenerateReports: ["super_admin", "national_asset_manager", "national_maintenance_manager"].includes(user?.role || ""),
    canExportData: ["super_admin", "national_asset_manager", "regional_admin"].includes(user?.role || "")
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Unified Dashboard</h1>
          <p className="text-muted-foreground">
            Comprehensive transformer management with real-time monitoring and advanced analytics
          </p>
          <div className="flex items-center mt-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4 mr-2" />
            <span>Last updated: {format(lastUpdated, "MMM d, yyyy h:mm a")}</span>
            {realTimeEnabled && (
              <Badge variant="outline" className="ml-2">
                <Activity className="h-3 w-3 mr-1" />
                Live
              </Badge>
            )}
          </div>
        </div>

        {/* Control Panel */}
        <div className="flex flex-wrap items-center gap-2">
          <Select value={region} onValueChange={setRegion}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Region" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Regions</SelectItem>
              <SelectItem value="addis">Addis Ababa</SelectItem>
              <SelectItem value="oromia">Oromia</SelectItem>
              <SelectItem value="amhara">Amhara</SelectItem>
              <SelectItem value="tigray">Tigray</SelectItem>
              <SelectItem value="snnpr">SNNPR</SelectItem>
            </SelectContent>
          </Select>

          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={toggleRealTime}
            className={cn(realTimeEnabled && "bg-green-50 border-green-200")}
          >
            <Activity className="h-4 w-4 mr-2" />
            {realTimeEnabled ? "Live" : "Static"}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            disabled={isLoading || refreshing}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", (isLoading || refreshing) && "animate-spin")} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Loading Progress */}
      {(isLoading || refreshing) && <Progress className="h-1" value={100} />}

      {/* Dashboard Stats */}
      <DashboardStats stats={stats} isLoading={isLoading} />

      {/* Quick Actions */}
      <QuickActions 
        permissions={permissions}
        onAction={handleQuickAction}
        onOpenDialog={openDialog}
      />

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="real-time">Real-time</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <AlertsPanel 
              alerts={dashboardData.recentAlerts || []}
              onAction={handleQuickAction}
              isLoading={isLoading}
            />
            <MaintenancePanel 
              maintenance={dashboardData.upcomingMaintenance || []}
              onAction={handleQuickAction}
              isLoading={isLoading}
            />
          </div>
        </TabsContent>

        <TabsContent value="real-time" className="space-y-6">
          {/* Real-time monitoring content */}
          <Card>
            <CardHeader>
              <CardTitle>Real-time Monitoring</CardTitle>
              <CardDescription>Live transformer status and performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Monitor className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Real-time monitoring panel will be displayed here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <AnalyticsPanel 
            data={dashboardData}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <AlertsPanel 
            alerts={dashboardData.recentAlerts || []}
            onAction={handleQuickAction}
            isLoading={isLoading}
            expanded={true}
          />
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <MaintenancePanel 
            maintenance={dashboardData.upcomingMaintenance || []}
            onAction={handleQuickAction}
            isLoading={isLoading}
            expanded={true}
          />
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <ReportsPanel 
            onAction={handleQuickAction}
            permissions={permissions}
          />
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      {/* Dialog components will be imported and used here */}
    </div>
  )
}
