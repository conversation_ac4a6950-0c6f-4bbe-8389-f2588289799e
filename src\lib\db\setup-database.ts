import { readFileSync } from 'fs'
import { join } from 'path'
import mysql from 'mysql2/promise'

// Database configuration
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  multipleStatements: true
}

export async function setupDatabase() {
  let connection: mysql.Connection | null = null

  try {
    console.log('🔄 Connecting to MySQL server...')

    // Connect to MySQL server (without specifying database)
    connection = await mysql.createConnection(dbConfig)

    console.log('✅ Connected to MySQL server')

    // Read and execute create tables script
    console.log('🔄 Creating database and tables...')
    const createTablesSQL = readFileSync(
      join(process.cwd(), 'src/lib/db/create-tables.sql'),
      'utf8'
    )

    // Split SQL into individual statements and execute them one by one
    const statements = createTablesSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement)
        } catch (error) {
          console.log(`⚠️ Statement: ${statement.substring(0, 100)}...`)
          console.log(`⚠️ Error: ${error}`)
          // Continue with other statements
        }
      }
    }
    console.log('✅ Database and tables created successfully')

    // Read and execute seed data script
    console.log('🔄 Inserting seed data...')
    const seedDataSQL = readFileSync(
      join(process.cwd(), 'src/lib/db/seed-data.sql'),
      'utf8'
    )

    // Split seed data SQL into individual statements
    const seedStatements = seedDataSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    for (const statement of seedStatements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement)
        } catch (error) {
          console.log(`⚠️ Seed statement: ${statement.substring(0, 100)}...`)
          console.log(`⚠️ Error: ${error}`)
          // Continue with other statements
        }
      }
    }
    console.log('✅ Seed data inserted successfully')

    // Verify the setup
    console.log('🔄 Verifying database setup...')

    // Switch to the created database
    await connection.execute('USE dtms_eeu_db')

    // Check table counts
    const [regions] = await connection.execute('SELECT COUNT(*) as count FROM app_regions')
    const [transformers] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers')
    const [alerts] = await connection.execute('SELECT COUNT(*) as count FROM app_alerts')
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM app_users')
    const [maintenance] = await connection.execute('SELECT COUNT(*) as count FROM app_maintenance_schedules')

    console.log('📊 Database verification:')
    console.log(`   - Regions: ${(regions as any)[0].count}`)
    console.log(`   - Transformers: ${(transformers as any)[0].count}`)
    console.log(`   - Alerts: ${(alerts as any)[0].count}`)
    console.log(`   - Users: ${(users as any)[0].count}`)
    console.log(`   - Maintenance Schedules: ${(maintenance as any)[0].count}`)

    console.log('🎉 Database setup completed successfully!')

    return {
      success: true,
      message: 'Database setup completed successfully',
      stats: {
        regions: (regions as any)[0].count,
        transformers: (transformers as any)[0].count,
        alerts: (alerts as any)[0].count,
        users: (users as any)[0].count,
        maintenance: (maintenance as any)[0].count
      }
    }

  } catch (error) {
    console.error('❌ Database setup failed:', error)
    return {
      success: false,
      message: `Database setup failed: ${error}`,
      error
    }
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}

export async function resetDatabase() {
  let connection: mysql.Connection | null = null

  try {
    console.log('🔄 Connecting to MySQL server for reset...')

    connection = await mysql.createConnection(dbConfig)

    console.log('🗑️ Dropping existing database...')
    await connection.execute('DROP DATABASE IF EXISTS dtms_eeu_db')

    console.log('✅ Database reset completed')

    // Now setup the database again
    await connection.end()
    return await setupDatabase()

  } catch (error) {
    console.error('❌ Database reset failed:', error)
    return {
      success: false,
      message: `Database reset failed: ${error}`,
      error
    }
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

export async function checkDatabaseStatus() {
  let connection: mysql.Connection | null = null

  try {
    connection = await mysql.createConnection({
      ...dbConfig,
      database: 'dtms_eeu_db'
    })

    // Check if all required tables exist
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = 'dtms_eeu_db'
    `)

    const tableNames = (tables as any[]).map(t => t.TABLE_NAME)
    const requiredTables = [
      'app_regions',
      'app_service_centers',
      'app_users',
      'app_transformers',
      'app_alerts',
      'app_maintenance_schedules',
      'app_notifications',
      'app_performance_metrics',
      'app_weather_data'
    ]

    const missingTables = requiredTables.filter(table => !tableNames.includes(table))

    if (missingTables.length > 0) {
      return {
        exists: false,
        message: `Missing tables: ${missingTables.join(', ')}`,
        missingTables
      }
    }

    // Check data counts
    const [regions] = await connection.execute('SELECT COUNT(*) as count FROM app_regions')
    const [transformers] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers')

    return {
      exists: true,
      message: 'Database is properly set up',
      stats: {
        regions: (regions as any)[0].count,
        transformers: (transformers as any)[0].count
      }
    }

  } catch (error) {
    return {
      exists: false,
      message: `Database check failed: ${error}`,
      error
    }
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

// CLI interface for running setup
if (require.main === module) {
  const command = process.argv[2]

  switch (command) {
    case 'setup':
      setupDatabase()
      break
    case 'reset':
      resetDatabase()
      break
    case 'check':
      checkDatabaseStatus().then(status => {
        console.log('Database Status:', status)
      })
      break
    default:
      console.log('Usage: npm run db:setup | npm run db:reset | npm run db:check')
      console.log('Or: node src/lib/db/setup-database.ts [setup|reset|check]')
  }
}
