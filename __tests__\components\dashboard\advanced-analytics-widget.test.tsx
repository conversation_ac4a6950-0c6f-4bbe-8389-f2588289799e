import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { AdvancedAnalyticsWidget } from '../../../components/dashboard/advanced-analytics-widget'

// Mock recharts components
jest.mock('recharts', () => ({
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  AreaChart: ({ children }: any) => <div data-testid="area-chart">{children}</div>,
  Area: () => <div data-testid="area" />,
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>
}))

describe('AdvancedAnalyticsWidget', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders loading state initially', () => {
    render(<AdvancedAnalyticsWidget />)

    expect(screen.getByText('Advanced Analytics')).toBeInTheDocument()
    // The component shows loading spinner initially
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('renders analytics data after loading', async () => {
    render(<AdvancedAnalyticsWidget />)

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Check if main content is rendered
    expect(screen.getByText('Advanced Analytics')).toBeInTheDocument()
  })

  it('handles basic functionality', async () => {
    render(<AdvancedAnalyticsWidget />)

    // Initially shows loading
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Check if main content is rendered
    expect(screen.getByText('Advanced Analytics')).toBeInTheDocument()
  })

  it('handles error state gracefully', async () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    render(<AdvancedAnalyticsWidget />)

    // Component should handle error gracefully and not crash
    expect(screen.getByText('Advanced Analytics')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })
})
