{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "ES6", "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/app/*": ["./app/*"], "@/lib/*": ["./lib/*"], "@/services/*": ["./services/*"], "@/contexts/*": ["./contexts/*"], "@/types/*": ["./types/*"], "@/hooks/*": ["./hooks/*"], "@/data/*": ["./data/*"], "@/public/*": ["./public/*"], "@/styles/*": ["./styles/*"], "@/src/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist", "build", ".next"]}