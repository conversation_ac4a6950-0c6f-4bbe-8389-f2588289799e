/**
 * Add Sample Data for Testing
 * This script adds basic sample data to test the application
 */

const mysql = require('mysql2/promise');

// Database configuration
const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function addSampleData() {
  let connection;
  
  try {
    console.log('🔌 Connecting to MySQL database...');
    connection = await mysql.createConnection(config);
    console.log('✅ Connected successfully');

    // Add sample transformers
    console.log('📊 Adding sample transformers...');
    
    const transformers = [
      {
        id: 1,
        serial_number: 'TRF-2024-001',
        name: 'Bole Main Transformer',
        status: 'operational',
        type: 'Distribution',
        manufacturer: 'Siemens',
        model: 'SIT-500',
        capacity: 500,
        primary_voltage: 33,
        secondary_voltage: 0.4,
        location: 'Bole Road, Addis Ababa',
        latitude: 9.0101,
        longitude: 38.7612,
        temperature: 65,
        load_percentage: 78,
        oil_level: 90,
        health_index: 85
      },
      {
        id: 2,
        serial_number: 'TRF-2024-002',
        name: 'Megenagna Transformer',
        status: 'warning',
        type: 'Distribution',
        manufacturer: 'ABB',
        model: 'ABB-300',
        capacity: 300,
        primary_voltage: 33,
        secondary_voltage: 0.4,
        location: 'Megenagna, Addis Ababa',
        latitude: 9.0299,
        longitude: 38.8079,
        temperature: 75,
        load_percentage: 92,
        oil_level: 80,
        health_index: 65
      },
      {
        id: 3,
        serial_number: 'TRF-2024-003',
        name: 'Jimma Central Transformer',
        status: 'operational',
        type: 'Distribution',
        manufacturer: 'Schneider Electric',
        model: 'SE-200',
        capacity: 200,
        primary_voltage: 33,
        secondary_voltage: 0.4,
        location: 'Jimma City Center',
        latitude: 7.6781,
        longitude: 36.8344,
        temperature: 60,
        load_percentage: 65,
        oil_level: 95,
        health_index: 90
      }
    ];

    // Insert transformers (using correct column names)
    for (const transformer of transformers) {
      await connection.execute(`
        INSERT IGNORE INTO app_transformers (
          id, serial_number, name, status, type, manufacturer, model,
          capacity_kva, voltage_primary, voltage_secondary, location_name,
          latitude, longitude, temperature, load_factor, oil_level, efficiency_rating,
          region_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        transformer.id, transformer.serial_number, transformer.name, transformer.status,
        transformer.type, transformer.manufacturer, transformer.model, transformer.capacity,
        transformer.primary_voltage, transformer.secondary_voltage, transformer.location,
        transformer.latitude, transformer.longitude, transformer.temperature,
        transformer.load_percentage, transformer.oil_level, transformer.health_index,
        1 // region_id (default to 1)
      ]);
    }

    // Add sample maintenance schedules
    console.log('🔧 Adding sample maintenance schedules...');
    
    const maintenanceSchedules = [
      {
        id: 1,
        transformer_id: 2,
        type: 'preventive',
        title: 'Quarterly Maintenance - Megenagna',
        description: 'Regular quarterly maintenance including oil check and temperature monitoring.',
        scheduled_date: '2024-12-30',
        status: 'scheduled',
        priority: 'medium',
        estimated_duration: 4
      },
      {
        id: 2,
        transformer_id: 3,
        type: 'routine',
        title: 'Monthly Inspection - Jimma',
        description: 'Monthly routine inspection and cleaning.',
        scheduled_date: '2024-12-25',
        status: 'completed',
        priority: 'low',
        estimated_duration: 2
      }
    ];

    for (const schedule of maintenanceSchedules) {
      await connection.execute(`
        INSERT IGNORE INTO app_maintenance_schedules (
          id, transformer_id, type, title, description, scheduled_date,
          status, priority, estimated_duration, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        schedule.id, schedule.transformer_id, schedule.type, schedule.title,
        schedule.description, schedule.scheduled_date, schedule.status,
        schedule.priority, schedule.estimated_duration
      ]);
    }

    // Add sample alerts
    console.log('🚨 Adding sample alerts...');
    
    const alerts = [
      {
        id: 1,
        transformer_id: 2,
        title: 'High Temperature Alert',
        message: 'Transformer temperature has exceeded 70°C. Immediate inspection recommended.',
        severity: 'high',
        type: 'temperature',
        is_read: false,
        is_resolved: false
      },
      {
        id: 2,
        transformer_id: 2,
        title: 'Overload Warning',
        message: 'Transformer is operating at 92% of capacity, exceeding the recommended 85% threshold.',
        severity: 'medium',
        type: 'load',
        is_read: true,
        is_resolved: false
      }
    ];

    for (const alert of alerts) {
      await connection.execute(`
        INSERT IGNORE INTO app_alerts (
          id, transformer_id, title, description, severity, type,
          priority, is_resolved, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        alert.id, alert.transformer_id, alert.title, alert.message,
        alert.severity, alert.type, alert.severity, alert.is_resolved
      ]);
    }

    console.log('✅ Sample data added successfully!');
    console.log('📊 Added 3 transformers, 2 maintenance schedules, and 2 alerts');
    
  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the script
addSampleData();
