"use client"

import { createContext, useContext, useState, useCallback, useMemo, useReducer, ReactNode } from "react"
import { useToast } from "@/src/components/ui/use-toast"
import { User, UserFilters, DialogState, UserCounts, UserFormData } from "@/src/types/user-management"

// Initial mock data
const initialUsers: User[] = [
  {
    id: "USR-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Administrator",
    department: "IT",
    status: "Active",
    lastActive: "Apr 27, 2025 10:24 AM",
    employeeId: "EMP-10025",
    position: "IT Director",
    organizationalLevel: "head_office",
    phoneNumber: "+251-911-123-456",
    hireDate: "2020-05-15",
    skills: ["Network Administration", "System Security", "Database Management"],
    certifications: ["CISSP", "PMP"],
    createdAt: "2020-05-15T08:00:00Z",
    createdBy: "System",
    updatedAt: "2025-04-27T10:24:00Z",
    updatedBy: "<PERSON>"
  },
  {
    id: "USR-002",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Technician",
    department: "Maintenance",
    status: "Active",
    lastActive: "Apr 27, 2025 09:15 AM",
    employeeId: "EMP-20134",
    position: "Senior Technician",
    organizationalLevel: "regional_office",
    regionId: "REG-001",
    phoneNumber: "+251-911-234-567",
    hireDate: "2021-03-10",
    skills: ["Transformer Maintenance", "Circuit Analysis", "Safety Protocols"],
    certifications: ["Electrical Safety", "High Voltage Operations"],
    createdAt: "2021-03-10T09:00:00Z",
    createdBy: "John Smith"
  },
  {
    id: "USR-003",
    name: "Michael Brown",
    email: "<EMAIL>",
    role: "Operator",
    department: "Operations",
    status: "Active",
    lastActive: "Apr 27, 2025 08:30 AM",
    employeeId: "EMP-30089",
    position: "Control Room Operator",
    organizationalLevel: "service_center",
    regionId: "REG-002",
    serviceCenterId: "SVC-005",
    phoneNumber: "+251-911-345-678",
    hireDate: "2022-01-20",
    skills: ["SCADA Systems", "Emergency Response", "Load Balancing"],
    certifications: ["Control Room Operations", "Emergency Management"],
    createdAt: "2022-01-20T10:30:00Z",
    createdBy: "Sarah Johnson"
  },
  {
    id: "USR-004",
    name: "Emily Davis",
    email: "<EMAIL>",
    role: "Administrator",
    department: "Management",
    status: "Active",
    lastActive: "Apr 26, 2025 04:45 PM",
    employeeId: "EMP-10078",
    position: "Operations Manager",
    organizationalLevel: "head_office",
    phoneNumber: "+251-911-456-789",
    hireDate: "2019-11-05",
    skills: ["Project Management", "Budget Planning", "Team Leadership"],
    certifications: ["MBA", "PMP"],
    createdAt: "2019-11-05T14:00:00Z",
    createdBy: "System"
  },
  {
    id: "USR-005",
    name: "David Wilson",
    email: "<EMAIL>",
    role: "Technician",
    department: "Maintenance",
    status: "Inactive",
    lastActive: "Apr 20, 2025 02:30 PM",
    employeeId: "EMP-20056",
    position: "Field Technician",
    organizationalLevel: "service_center",
    regionId: "REG-001",
    serviceCenterId: "SVC-002",
    phoneNumber: "+251-911-567-890",
    hireDate: "2021-07-15",
    skills: ["Equipment Repair", "Preventive Maintenance", "Troubleshooting"],
    certifications: ["Electrical Safety"],
    createdAt: "2021-07-15T09:30:00Z",
    createdBy: "Emily Davis"
  },
  {
    id: "USR-006",
    name: "Abebe Kebede",
    email: "<EMAIL>",
    role: "Manager",
    department: "Customer Service",
    status: "Active",
    lastActive: "Apr 27, 2025 11:45 AM",
    employeeId: "EMP-40012",
    position: "Customer Service Manager",
    organizationalLevel: "regional_office",
    regionId: "REG-003",
    phoneNumber: "+251-911-678-901",
    hireDate: "2020-09-01",
    skills: ["Customer Relations", "Complaint Resolution", "Team Management"],
    certifications: ["Customer Service Excellence"],
    createdAt: "2020-09-01T08:15:00Z",
    createdBy: "John Smith"
  },
  {
    id: "USR-007",
    name: "Tigist Haile",
    email: "<EMAIL>",
    role: "Supervisor",
    department: "Operations",
    status: "Active",
    lastActive: "Apr 27, 2025 10:30 AM",
    employeeId: "EMP-30045",
    position: "Shift Supervisor",
    organizationalLevel: "service_center",
    regionId: "REG-002",
    serviceCenterId: "SVC-004",
    phoneNumber: "+251-911-789-012",
    hireDate: "2021-05-20",
    skills: ["Team Leadership", "Scheduling", "Performance Management"],
    certifications: ["Supervisory Skills", "Safety Management"],
    createdAt: "2021-05-20T09:45:00Z",
    createdBy: "Emily Davis"
  },
  {
    id: "USR-008",
    name: "Solomon Tesfaye",
    email: "<EMAIL>",
    role: "Viewer",
    department: "Audit",
    status: "Pending",
    lastActive: "Apr 25, 2025 03:15 PM",
    employeeId: "EMP-50023",
    position: "Internal Auditor",
    organizationalLevel: "head_office",
    phoneNumber: "+251-911-890-123",
    hireDate: "2025-04-20",
    skills: ["Financial Auditing", "Compliance", "Risk Assessment"],
    certifications: ["Certified Internal Auditor"],
    createdAt: "2025-04-20T10:00:00Z",
    createdBy: "John Smith"
  }
]

// Initial filters
const initialFilters: UserFilters = {
  searchQuery: "",
  role: "all",
  status: "all",
  activeTab: "all",
  department: "all",
  location: "all",
  employeeId: "",
  searchType: "name"
}

// Initial dialog state
const initialDialogState: DialogState = {
  addUser: false,
  editUser: false,
  deleteUser: false,
  import: false,
  export: false,
  permissions: false,
  assignment: false,
  bulkAssignment: false,
  userDetails: false,
  activityLog: false
}

// Dialog reducer
type DialogAction =
  | { type: 'OPEN_DIALOG'; dialogName: keyof DialogState }
  | { type: 'CLOSE_DIALOG'; dialogName: keyof DialogState }
  | { type: 'CLOSE_ALL_DIALOGS' }

function dialogReducer(state: DialogState, action: DialogAction): DialogState {
  switch (action.type) {
    case 'OPEN_DIALOG':
      return { ...state, [action.dialogName]: true }
    case 'CLOSE_DIALOG':
      return { ...state, [action.dialogName]: false }
    case 'CLOSE_ALL_DIALOGS':
      return initialDialogState
    default:
      return state
  }
}

// Context type
interface UserContextType {
  // State
  users: User[]
  selectedUser: User | null
  filters: UserFilters
  dialogState: DialogState
  userCounts: UserCounts
  userActivities: UserActivity[]
  userAssignments: UserAssignment[]

  // Actions
  setSelectedUser: (user: User | null) => void
  updateFilter: (key: keyof UserFilters, value: string) => void
  openDialog: (dialogName: keyof DialogState, userData?: any) => void
  closeDialog: (dialogName: keyof DialogState) => void
  closeAllDialogs: () => void

  // User operations
  addUser: (userData: UserFormData) => void
  updateUser: (userData: UserFormData) => void
  deleteUser: (userId: string) => void
  toggleUserStatus: (userId: string) => void
  updateUserPermissions: (userId: string, permissions: UserPermission[]) => void

  // Assignment operations
  assignUser: (assignment: Omit<UserAssignment, 'assignedAt' | 'assignedBy'>) => void
  bulkAssignUsers: (userIds: string[], assignment: Omit<UserAssignment, 'userId' | 'assignedAt' | 'assignedBy'>) => void

  // Activity tracking
  logUserActivity: (activity: Omit<UserActivity, 'id' | 'timestamp'>) => void
  getUserActivities: (userId: string) => UserActivity[]

  // Computed values
  filteredUsers: User[]
  getDepartments: () => string[]
  getRegions: () => { id: string, name: string }[]
  getServiceCenters: (regionId?: string) => { id: string, name: string }[]
}

// Create context
const UserContext = createContext<UserContextType | undefined>(undefined)

// Provider component
interface UserProviderProps {
  children: ReactNode
}

export function UserProvider({ children }: UserProviderProps) {
  const { toast } = useToast()
  const [users, setUsers] = useState<User[]>(initialUsers)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [filters, setFilters] = useState<UserFilters>(initialFilters)
  const [dialogState, dispatch] = useReducer(dialogReducer, initialDialogState)
  const [userActivities, setUserActivities] = useState<UserActivity[]>([])
  const [userAssignments, setUserAssignments] = useState<UserAssignment[]>([])

  // Update a specific filter
  const updateFilter = useCallback((key: keyof UserFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }, [])

  // Dialog actions
  const openDialog = useCallback((dialogName: keyof DialogState, userData?: any) => {
    if (userData) {
      setSelectedUser(userData)
    }
    dispatch({ type: 'OPEN_DIALOG', dialogName })
  }, [])

  const closeDialog = useCallback((dialogName: keyof DialogState) => {
    dispatch({ type: 'CLOSE_DIALOG', dialogName })
  }, [])

  const closeAllDialogs = useCallback(() => {
    dispatch({ type: 'CLOSE_ALL_DIALOGS' })
  }, [])

  // Activity tracking
  const logUserActivity = useCallback((activity: Omit<UserActivity, 'id' | 'timestamp'>) => {
    const newActivity: UserActivity = {
      ...activity,
      id: `ACT-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      timestamp: new Date().toISOString()
    }

    setUserActivities(prev => [newActivity, ...prev])
    return newActivity
  }, [])

  const getUserActivities = useCallback((userId: string) => {
    return userActivities.filter(activity => activity.userId === userId)
  }, [userActivities])

  // Location data helpers
  const getDepartments = useCallback(() => {
    const departments = new Set(users.map(user => user.department))
    return Array.from(departments)
  }, [users])

  const getRegions = useCallback(() => {
    // In a real app, this would come from an API
    return [
      { id: "REG-001", name: "Addis Ababa" },
      { id: "REG-002", name: "Oromia" },
      { id: "REG-003", name: "Amhara" },
      { id: "REG-004", name: "Tigray" },
      { id: "REG-005", name: "SNNPR" }
    ]
  }, [])

  const getServiceCenters = useCallback((regionId?: string) => {
    // In a real app, this would come from an API
    const allServiceCenters = [
      { id: "SVC-001", name: "Bole Service Center", regionId: "REG-001" },
      { id: "SVC-002", name: "Megenagna Service Center", regionId: "REG-001" },
      { id: "SVC-003", name: "Adama Service Center", regionId: "REG-002" },
      { id: "SVC-004", name: "Bishoftu Service Center", regionId: "REG-002" },
      { id: "SVC-005", name: "Bahir Dar Service Center", regionId: "REG-003" },
      { id: "SVC-006", name: "Gondar Service Center", regionId: "REG-003" },
      { id: "SVC-007", name: "Mekelle Service Center", regionId: "REG-004" },
      { id: "SVC-008", name: "Hawassa Service Center", regionId: "REG-005" }
    ]

    if (regionId) {
      return allServiceCenters.filter(sc => sc.regionId === regionId)
    }

    return allServiceCenters
  }, [])

  // User operations
  const addUser = useCallback((userData: UserFormData) => {
    const now = new Date()
    const currentUser = "Current User" // In a real app, this would be the current user's name

    const newUser: User = {
      ...userData,
      id: userData.id || `USR-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      status: userData.status as UserStatus,
      role: userData.role as UserRole,
      lastActive: now.toLocaleString(),
      createdAt: now.toISOString(),
      createdBy: currentUser,
      updatedAt: now.toISOString(),
      updatedBy: currentUser,
      permissions: []
    }

    setUsers(prev => [...prev, newUser])

    // Log the activity
    logUserActivity({
      userId: newUser.id,
      userName: newUser.name,
      activityType: 'create',
      details: `User account created with role ${newUser.role}`
    })

    toast({
      title: "User Created",
      description: `${userData.name} has been successfully added.`,
    })

    return newUser
  }, [toast, logUserActivity])

  const updateUser = useCallback((userData: UserFormData) => {
    const now = new Date()
    const currentUser = "Current User" // In a real app, this would be the current user's name

    let updatedUser: User | null = null

    setUsers(prev =>
      prev.map(user => {
        if (user.id === userData.id) {
          updatedUser = {
            ...user,
            ...userData,
            status: userData.status as UserStatus,
            role: userData.role as UserRole,
            updatedAt: now.toISOString(),
            updatedBy: currentUser
          }
          return updatedUser
        }
        return user
      })
    )

    if (updatedUser) {
      // Log the activity
      logUserActivity({
        userId: updatedUser.id,
        userName: updatedUser.name,
        activityType: 'update',
        details: `User profile updated`
      })
    }

    toast({
      title: "User Updated",
      description: `${userData.name} has been successfully updated.`,
    })

    return updatedUser
  }, [toast, logUserActivity])

  const deleteUser = useCallback((userId: string) => {
    const userToDelete = users.find(user => user.id === userId)

    if (userToDelete) {
      setUsers(prev => prev.filter(user => user.id !== userId))

      // Log the activity before the user is removed
      logUserActivity({
        userId,
        userName: userToDelete.name,
        activityType: 'delete',
        details: `User account deleted`
      })

      toast({
        title: "User Deleted",
        description: `${userToDelete.name} has been successfully deleted.`,
      })
    }
  }, [users, toast, logUserActivity])

  const toggleUserStatus = useCallback((userId: string) => {
    setUsers(prev =>
      prev.map(user => {
        if (user.id === userId) {
          const newStatus = user.status === "Active" ? "Inactive" : "Active"

          toast({
            title: `User ${newStatus}`,
            description: `${user.name} has been set to ${newStatus}.`,
          })

          // Log the activity
          logUserActivity({
            userId,
            userName: user.name,
            activityType: 'update',
            details: `User status changed to ${newStatus}`
          })

          return {
            ...user,
            status: newStatus,
            updatedAt: new Date().toISOString()
          }
        }
        return user
      })
    )
  }, [toast, logUserActivity])

  // User permissions management
  const updateUserPermissions = useCallback((userId: string, permissions: UserPermission[]) => {
    setUsers(prev =>
      prev.map(user => {
        if (user.id === userId) {
          toast({
            title: "Permissions Updated",
            description: `Permissions for ${user.name} have been updated.`,
          })

          // Log the activity
          logUserActivity({
            userId,
            userName: user.name,
            activityType: 'permission_change',
            details: `User permissions updated (${permissions.length} permissions)`
          })

          return {
            ...user,
            permissions,
            updatedAt: new Date().toISOString()
          }
        }
        return user
      })
    )
  }, [toast, logUserActivity])

  // User assignment management
  const assignUser = useCallback((assignment: Omit<UserAssignment, 'assignedAt' | 'assignedBy'>) => {
    const { userId, organizationalLevel, regionId, serviceCenterId } = assignment
    const user = users.find(u => u.id === userId)

    if (!user) return

    // Create assignment record
    const newAssignment: UserAssignment = {
      ...assignment,
      assignedAt: new Date().toISOString(),
      assignedBy: "Current User", // In a real app, this would be the current user's ID
      previousAssignment: user.organizationalLevel ? {
        organizationalLevel: user.organizationalLevel,
        regionId: user.regionId,
        serviceCenterId: user.serviceCenterId
      } : undefined
    }

    setUserAssignments(prev => [...prev, newAssignment])

    // Update user record
    setUsers(prev =>
      prev.map(u => {
        if (u.id === userId) {
          // Log the activity
          logUserActivity({
            userId,
            userName: u.name,
            activityType: 'assignment_change',
            details: `User assigned to ${organizationalLevel}${regionId ? ` in region ${regionId}` : ''}${serviceCenterId ? ` at service center ${serviceCenterId}` : ''}`
          })

          return {
            ...u,
            organizationalLevel,
            regionId,
            serviceCenterId,
            updatedAt: new Date().toISOString()
          }
        }
        return u
      })
    )

    toast({
      title: "User Assigned",
      description: `${user.name} has been assigned to ${organizationalLevel}${regionId ? ` in region ${getRegions().find(r => r.id === regionId)?.name}` : ''}${serviceCenterId ? ` at ${getServiceCenters().find(sc => sc.id === serviceCenterId)?.name}` : ''}.`,
    })

    return newAssignment
  }, [users, toast, logUserActivity, getRegions, getServiceCenters])

  // Bulk assignment
  const bulkAssignUsers = useCallback((userIds: string[], assignment: Omit<UserAssignment, 'userId' | 'assignedAt' | 'assignedBy'>) => {
    const { organizationalLevel, regionId, serviceCenterId } = assignment
    const affectedUsers = users.filter(u => userIds.includes(u.id))

    if (affectedUsers.length === 0) return

    // Update all users
    setUsers(prev =>
      prev.map(user => {
        if (userIds.includes(user.id)) {
          // Create assignment record for each user
          const newAssignment: UserAssignment = {
            userId: user.id,
            ...assignment,
            assignedAt: new Date().toISOString(),
            assignedBy: "Current User", // In a real app, this would be the current user's ID
            previousAssignment: user.organizationalLevel ? {
              organizationalLevel: user.organizationalLevel,
              regionId: user.regionId,
              serviceCenterId: user.serviceCenterId
            } : undefined
          }

          setUserAssignments(prev => [...prev, newAssignment])

          // Log the activity for each user
          logUserActivity({
            userId: user.id,
            userName: user.name,
            activityType: 'assignment_change',
            details: `User assigned to ${organizationalLevel}${regionId ? ` in region ${regionId}` : ''}${serviceCenterId ? ` at service center ${serviceCenterId}` : ''} (bulk assignment)`
          })

          return {
            ...user,
            organizationalLevel,
            regionId,
            serviceCenterId,
            updatedAt: new Date().toISOString()
          }
        }
        return user
      })
    )

    toast({
      title: "Users Assigned",
      description: `${affectedUsers.length} users have been assigned to ${organizationalLevel}${regionId ? ` in region ${getRegions().find(r => r.id === regionId)?.name}` : ''}${serviceCenterId ? ` at ${getServiceCenters().find(sc => sc.id === serviceCenterId)?.name}` : ''}.`,
    })
  }, [users, toast, logUserActivity, getRegions, getServiceCenters])

  // Computed values
  const filteredUsers = useMemo(() => {
    return users.filter(user => {
      // Filter by role
      if (filters.role !== "all" && user.role.toLowerCase() !== filters.role.toLowerCase()) {
        return false
      }

      // Filter by status
      if (filters.status !== "all" && user.status.toLowerCase() !== filters.status.toLowerCase()) {
        return false
      }

      // Filter by department
      if (filters.department !== "all" && user.department.toLowerCase() !== filters.department.toLowerCase()) {
        return false
      }

      // Filter by location
      if (filters.location !== "all") {
        if (filters.location === "head_office" && user.organizationalLevel !== "head_office") {
          return false
        }

        if (filters.location.startsWith("region-") &&
            (user.regionId !== filters.location.replace("region-", "") || user.organizationalLevel === "head_office")) {
          return false
        }

        if (filters.location.startsWith("service-") &&
            user.serviceCenterId !== filters.location.replace("service-", "")) {
          return false
        }
      }

      // Filter by tab
      if (filters.activeTab !== "all") {
        const tabMapping: Record<string, string> = {
          "admins": "Administrator",
          "technicians": "Technician",
          "operators": "Operator",
          "managers": "Manager",
          "supervisors": "Supervisor",
          "viewers": "Viewer"
        }

        if (user.role !== tabMapping[filters.activeTab]) {
          return false
        }
      }

      // Filter by search query
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase()

        // Filter based on search type
        if (filters.searchType === 'employeeId') {
          return user.employeeId && user.employeeId.toLowerCase().includes(query);
        } else if (filters.searchType === 'email') {
          return user.email.toLowerCase().includes(query);
        } else if (filters.searchType === 'name') {
          return user.name.toLowerCase().includes(query);
        } else {
          // Default comprehensive search
          return (
            user.name.toLowerCase().includes(query) ||
            user.email.toLowerCase().includes(query) ||
            user.department.toLowerCase().includes(query) ||
            user.id.toLowerCase().includes(query) ||
            (user.employeeId && user.employeeId.toLowerCase().includes(query)) ||
            (user.position && user.position.toLowerCase().includes(query)) ||
            (user.phoneNumber && user.phoneNumber.toLowerCase().includes(query))
          );
        }
      }

      return true
    })
  }, [users, filters])

  // Calculate user counts
  const userCounts = useMemo(() => {
    return {
      all: users.length,
      administrators: users.filter(user => user.role === "Administrator").length,
      technicians: users.filter(user => user.role === "Technician").length,
      operators: users.filter(user => user.role === "Operator").length,
      managers: users.filter(user => user.role === "Manager").length,
      supervisors: users.filter(user => user.role === "Supervisor").length,
      viewers: users.filter(user => user.role === "Viewer").length,
      active: users.filter(user => user.status === "Active").length,
      inactive: users.filter(user => user.status === "Inactive").length,
      pending: users.filter(user => user.status === "Pending").length,
      suspended: users.filter(user => user.status === "Suspended").length
    }
  }, [users])

  const value = {
    // State
    users,
    selectedUser,
    filters,
    dialogState,
    userCounts,
    userActivities,
    userAssignments,

    // Actions
    setSelectedUser,
    updateFilter,
    openDialog,
    closeDialog,
    closeAllDialogs,

    // User operations
    addUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    updateUserPermissions,

    // Assignment operations
    assignUser,
    bulkAssignUsers,

    // Activity tracking
    logUserActivity,
    getUserActivities,

    // Computed values
    filteredUsers,
    getDepartments,
    getRegions,
    getServiceCenters
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

// Custom hook
export function useUserManagement() {
  const context = useContext(UserContext)

  if (context === undefined) {
    throw new Error('useUserManagement must be used within a UserProvider')
  }

  return context
}
