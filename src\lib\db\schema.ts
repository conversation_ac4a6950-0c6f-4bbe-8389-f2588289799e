/**
 * Database schema definitions
 * 
 * This file defines the types and interfaces for our JSON database structure.
 * It serves as a lightweight schema definition similar to what you might define
 * with an ORM like Prisma, but for our JSON-based database.
 */

// Base entity interface with common fields
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// User roles
export type UserRole = 
  | 'super_admin'
  | 'national_asset_manager'
  | 'national_maintenance_manager'
  | 'regional_admin'
  | 'regional_asset_manager'
  | 'regional_maintenance_engineer'
  | 'service_center_manager'
  | 'field_technician'
  | 'customer_service_agent'
  | 'audit_compliance_officer';

// User entity
export interface User extends BaseEntity {
  email: string;
  name: string;
  role: UserRole;
  region?: string;
  serviceCenter?: string;
  avatar?: string;
  isActive: boolean;
  lastLogin?: string;
  preferences?: {
    theme?: 'light' | 'dark' | 'system';
    language?: string;
    notifications?: boolean;
    dashboardLayout?: string;
  };
}

// Region entity
export interface Region extends BaseEntity {
  name: string;
  code: string;
  serviceCenters: number;
  transformers: number;
  coordinates: {
    lat: number;
    lng: number;
  };
}

// Service Center entity
export interface ServiceCenter extends BaseEntity {
  name: string;
  code: string;
  regionId: string;
  address: string;
  phone: string;
  email?: string;
  manager?: string;
  staff: number;
  transformers: number;
  coordinates: {
    lat: number;
    lng: number;
  };
}

// Transformer status types
export type TransformerStatus = 'operational' | 'warning' | 'critical' | 'offline' | 'maintenance';

// Transformer entity
export interface Transformer extends BaseEntity {
  serialNumber: string;
  name: string;
  status: TransformerStatus;
  type: string;
  manufacturer: string;
  model: string;
  manufactureDate: string;
  installationDate: string;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  capacity: number; // in kVA
  voltage: {
    primary: number; // in kV
    secondary: number; // in V
  };
  regionId: string;
  serviceCenterId: string;
  location: {
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  metrics: {
    temperature: number;
    loadPercentage: number;
    oilLevel: number;
    healthIndex: number;
  };
  tags: string[];
}

// Maintenance status types
export type MaintenanceStatus = 'scheduled' | 'in-progress' | 'completed' | 'cancelled' | 'overdue';

// Maintenance type
export type MaintenanceType = 'preventive' | 'corrective' | 'emergency' | 'inspection';

// Maintenance record entity
export interface MaintenanceRecord extends BaseEntity {
  transformerId: string;
  type: MaintenanceType;
  status: MaintenanceStatus;
  title: string;
  description: string;
  scheduledDate: string;
  completedDate?: string;
  assignedTo: string; // User ID
  reportedBy: string; // User ID
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedDuration: number; // in hours
  actualDuration?: number; // in hours
  cost?: number;
  parts?: {
    name: string;
    quantity: number;
    cost: number;
  }[];
  notes?: string;
  attachments?: {
    name: string;
    url: string;
    type: string;
    size: number;
  }[];
}

// Alert severity types
export type AlertSeverity = 'low' | 'medium' | 'high' | 'critical';

// Alert entity
export interface Alert extends BaseEntity {
  transformerId: string;
  title: string;
  message: string;
  severity: AlertSeverity;
  type: string;
  isRead: boolean;
  isResolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string; // User ID
  metadata?: Record<string, any>;
}

// Outage status types
export type OutageStatus = 'active' | 'scheduled' | 'resolved';

// Outage entity
export interface Outage extends BaseEntity {
  transformerId: string;
  status: OutageStatus;
  startTime: string;
  endTime?: string;
  cause?: string;
  affectedCustomers: number;
  description: string;
  reportedBy: string; // User ID
  resolvedBy?: string; // User ID
  maintenanceId?: string; // Related maintenance record ID
  notes?: string;
}

// Weather alert entity
export interface WeatherAlert extends BaseEntity {
  regionId: string;
  type: string;
  severity: AlertSeverity;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  affectedTransformers: string[]; // Array of transformer IDs
  source: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  isActive: boolean;
}

// Database schema interface
export interface DatabaseSchema {
  users: User[];
  regions: Region[];
  serviceCenters: ServiceCenter[];
  transformers: Transformer[];
  maintenanceRecords: MaintenanceRecord[];
  alerts: Alert[];
  outages: Outage[];
  weatherAlerts: WeatherAlert[];
  // Metadata about the database itself
  _metadata: {
    version: string;
    lastUpdated: string;
    recordCounts: {
      users: number;
      regions: number;
      serviceCenters: number;
      transformers: number;
      maintenanceRecords: number;
      alerts: number;
      outages: number;
      weatherAlerts: number;
    };
  };
}
