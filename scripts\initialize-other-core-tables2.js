// Create workaround tables for other core entities (alerts, maintenance, notifications, performance, weather, sensor readings)
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

const createAlerts2 = `CREATE TABLE IF NOT EXISTS app_alerts2 (
  id INT PRIMARY KEY AUTO_INCREMENT,
  transformer_id INT,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  type ENUM('temperature', 'voltage', 'load', 'maintenance', 'communication', 'weather', 'security') NOT NULL,
  status ENUM('active', 'investigating', 'resolved', 'monitoring') DEFAULT 'active',
  priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  created_by INT,
  assigned_to INT,
  resolved_at TIMESTAMP NULL,
  resolved_by INT,
  is_resolved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIG<PERSON> KEY (transformer_id) REFERENCES app_transformers2(id)
);`;

const createMaintenance2 = `CREATE TABLE IF NOT EXISTS app_maintenance_schedules2 (
  id INT PRIMARY KEY AUTO_INCREMENT,
  transformer_id INT NOT NULL,
  type ENUM('routine', 'preventive', 'corrective', 'emergency', 'scheduled') NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  scheduled_date DATE NOT NULL,
  estimated_duration INT,
  completion_days INT DEFAULT NULL,
  priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
  status ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'postponed') DEFAULT 'scheduled',
  technician_id INT,
  supervisor_id INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (transformer_id) REFERENCES app_transformers2(id)
);`;

const createNotifications2 = `CREATE TABLE IF NOT EXISTS app_notifications2 (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(200) NOT NULL,
  message TEXT NOT NULL,
  type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
  recipient_id INT,
  sender_id INT,
  is_read BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`;

const createPerformance2 = `CREATE TABLE IF NOT EXISTS app_performance_metrics2 (
  id INT PRIMARY KEY AUTO_INCREMENT,
  transformer_id INT NOT NULL,
  metric_type ENUM('power_generation', 'efficiency', 'load_factor', 'temperature', 'voltage') NOT NULL,
  value DECIMAL(10,4) NOT NULL,
  unit VARCHAR(20),
  recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (transformer_id) REFERENCES app_transformers2(id)
);`;

const createWeather2 = `CREATE TABLE IF NOT EXISTS app_weather_data2 (
  id INT PRIMARY KEY AUTO_INCREMENT,
  region_id INT NOT NULL,
  temperature DECIMAL(5,2),
  humidity DECIMAL(5,2),
  wind_speed DECIMAL(5,2),
  weather_condition VARCHAR(50),
  risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
  recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (region_id) REFERENCES app_regions2(id)
);`;

const createSensorReadings2 = `CREATE TABLE IF NOT EXISTS app_sensor_readings2 (
  id INT PRIMARY KEY AUTO_INCREMENT,
  transformer_id INT NOT NULL,
  sensor_type ENUM('temperature', 'voltage', 'current', 'oil_level') NOT NULL,
  reading_value DECIMAL(10,2) NOT NULL,
  recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (transformer_id) REFERENCES app_transformers2(id)
);`;

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    await connection.execute(createAlerts2);
    console.log('✅ Created app_alerts2 table');
    await connection.execute(createMaintenance2);
    console.log('✅ Created app_maintenance_schedules2 table');
    await connection.execute(createNotifications2);
    console.log('✅ Created app_notifications2 table');
    await connection.execute(createPerformance2);
    console.log('✅ Created app_performance_metrics2 table');
    await connection.execute(createWeather2);
    console.log('✅ Created app_weather_data2 table');
    await connection.execute(createSensorReadings2);
    console.log('✅ Created app_sensor_readings2 table');
  } catch (err) {
    console.error('❌ Error creating other core tables:', err);
  } finally {
    await connection.end();
  }
})();
