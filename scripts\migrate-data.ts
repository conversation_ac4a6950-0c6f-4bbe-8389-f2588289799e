#!/usr/bin/env tsx

/**
 * Data Migration Script
 * 
 * This script migrates all hardcoded mock data to the database.
 * It initializes the database with comprehensive seed data including
 * migrated transformer data from mock files.
 */

import { initializeDatabase } from '../lib/db/init-db';

async function migrateData() {
  console.log('🚀 Starting data migration...');
  console.log('📦 Migrating hardcoded mock data to database...');
  
  try {
    // Force initialization to ensure fresh data
    await initializeDatabase(true);
    
    console.log('✅ Data migration completed successfully!');
    console.log('📊 All mock data has been migrated to the database.');
    console.log('🔄 Application will now use database data instead of hardcoded mock data.');
    
  } catch (error) {
    console.error('❌ Data migration failed:', error);
    process.exit(1);
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateData();
}

export { migrateData };
