"use client"

import { useState, useEffect } from 'react'
import { Cloud, Sun, CloudRain, CloudLightning, Wind, Droplets, ThermometerSun, AlertTriangle, ChevronRight } from 'lucide-react'
import { WeatherImpactWidget as WeatherImpactWidgetType } from '../../../types/dashboard-widgets'

interface WeatherImpactWidgetProps {
  widget: WeatherImpactWidgetType
  isEditing: boolean
}

export default function WeatherImpactWidget({ widget, isEditing }: WeatherImpactWidgetProps) {
  const [data, setData] = useState({
    forecasts: [
      {
        id: 'forecast-001',
        regionId: 'region-001',
        regionName: 'Addis Ababa',
        location: {
          name: 'Addis Ababa',
          coordinates: {
            lat: 9.0222,
            lng: 38.7468
          }
        },
        timestamp: '2023-06-14T06:00:00Z',
        forecastDate: '2023-06-14T12:00:00Z',
        condition: 'heavy-rain',
        temperature: {
          min: 15,
          max: 22,
          average: 18
        },
        precipitation: {
          probability: 90,
          amount: 25,
          type: 'rain'
        },
        wind: {
          speed: 15,
          direction: 180,
          gusts: 25
        },
        humidity: 85,
        pressure: 1010,
        visibility: 5,
        uvIndex: 2,
        alerts: ['alert-004']
      },
      {
        id: 'forecast-002',
        regionId: 'region-001',
        regionName: 'Addis Ababa',
        location: {
          name: 'Addis Ababa',
          coordinates: {
            lat: 9.0222,
            lng: 38.7468
          }
        },
        timestamp: '2023-06-14T06:00:00Z',
        forecastDate: '2023-06-15T12:00:00Z',
        condition: 'rain',
        temperature: {
          min: 14,
          max: 20,
          average: 17
        },
        precipitation: {
          probability: 70,
          amount: 15,
          type: 'rain'
        },
        wind: {
          speed: 12,
          direction: 190,
          gusts: 20
        },
        humidity: 80,
        pressure: 1012,
        visibility: 8,
        uvIndex: 3,
        alerts: []
      },
      {
        id: 'forecast-003',
        regionId: 'region-001',
        regionName: 'Addis Ababa',
        location: {
          name: 'Addis Ababa',
          coordinates: {
            lat: 9.0222,
            lng: 38.7468
          }
        },
        timestamp: '2023-06-14T06:00:00Z',
        forecastDate: '2023-06-16T12:00:00Z',
        condition: 'partly-cloudy',
        temperature: {
          min: 16,
          max: 23,
          average: 19
        },
        precipitation: {
          probability: 30,
          amount: 0,
          type: 'none'
        },
        wind: {
          speed: 8,
          direction: 200,
          gusts: 12
        },
        humidity: 65,
        pressure: 1015,
        visibility: 10,
        uvIndex: 5,
        alerts: []
      },
      {
        id: 'forecast-004',
        regionId: 'region-001',
        regionName: 'Addis Ababa',
        location: {
          name: 'Addis Ababa',
          coordinates: {
            lat: 9.0222,
            lng: 38.7468
          }
        },
        timestamp: '2023-06-14T06:00:00Z',
        forecastDate: '2023-06-17T12:00:00Z',
        condition: 'clear',
        temperature: {
          min: 17,
          max: 25,
          average: 21
        },
        precipitation: {
          probability: 10,
          amount: 0,
          type: 'none'
        },
        wind: {
          speed: 5,
          direction: 220,
          gusts: 8
        },
        humidity: 55,
        pressure: 1018,
        visibility: 15,
        uvIndex: 7,
        alerts: []
      },
      {
        id: 'forecast-005',
        regionId: 'region-001',
        regionName: 'Addis Ababa',
        location: {
          name: 'Addis Ababa',
          coordinates: {
            lat: 9.0222,
            lng: 38.7468
          }
        },
        timestamp: '2023-06-14T06:00:00Z',
        forecastDate: '2023-06-18T12:00:00Z',
        condition: 'partly-cloudy',
        temperature: {
          min: 16,
          max: 24,
          average: 20
        },
        precipitation: {
          probability: 20,
          amount: 0,
          type: 'none'
        },
        wind: {
          speed: 7,
          direction: 210,
          gusts: 10
        },
        humidity: 60,
        pressure: 1016,
        visibility: 12,
        uvIndex: 6,
        alerts: []
      }
    ],
    alerts: [
      {
        id: 'alert-004',
        regionId: 'region-001',
        type: 'heavy-rain',
        severity: 'warning',
        title: 'Heavy Rainfall Warning',
        description: 'Heavy rainfall expected in Addis Ababa region with potential for localized flooding and reduced visibility.',
        issuedAt: '2023-06-14T05:30:00Z',
        validFrom: '2023-06-14T12:00:00Z',
        validTo: '2023-06-15T00:00:00Z',
        affectedAreas: [
          {
            name: 'Addis Ababa',
            coordinates: [
              { lat: 9.0222, lng: 38.7468 }
            ]
          }
        ],
        source: 'Ethiopian Meteorological Service',
        instructions: 'Avoid unnecessary travel. Ensure drainage systems are clear. Be prepared for potential power outages.',
        status: 'active'
      }
    ],
    impactAssessments: [
      {
        id: 'impact-001',
        alertId: 'alert-004',
        regionId: 'region-001',
        createdAt: '2023-06-14T07:00:00Z',
        updatedAt: '2023-06-14T07:00:00Z',
        riskLevel: 'moderate',
        potentialImpacts: [
          {
            category: 'transformers',
            description: 'Potential water ingress in outdoor transformers',
            severity: 'moderate',
            affectedAssets: ['tr-001', 'tr-015', 'tr-023']
          },
          {
            category: 'distribution-lines',
            description: 'Risk of short circuits due to water contact',
            severity: 'moderate',
            affectedAssets: ['line-005', 'line-008']
          }
        ],
        vulnerableLocations: [
          {
            name: 'Bole Sub-station',
            assetType: 'transformer',
            assetId: 'tr-001',
            riskFactors: ['flood-prone area', 'outdoor installation'],
            coordinates: {
              lat: 9.0222,
              lng: 38.7468
            }
          },
          {
            name: 'Kirkos Sub-station',
            assetType: 'transformer',
            assetId: 'tr-015',
            riskFactors: ['poor drainage', 'outdoor installation'],
            coordinates: {
              lat: 9.0092,
              lng: 38.7645
            }
          }
        ],
        estimatedOutageProbability: 35
      }
    ]
  })
  
  const [isLoading, setIsLoading] = useState(false)
  const [selectedRegion, setSelectedRegion] = useState<string>(
    widget.settings?.regions?.[0] || 'region-001'
  )
  
  // Fetch data
  useEffect(() => {
    // In a real implementation, this would fetch from the API
    // For now, we're using mock data initialized above
  }, [widget.id, selectedRegion])
  
  // Refresh data
  const refreshData = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      weekday: 'short',
      month: 'short', 
      day: 'numeric'
    })
  }
  
  // Get weather icon
  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'clear':
        return <Sun size={24} className="text-yellow-500" />
      case 'partly-cloudy':
        return <Cloud size={24} className="text-gray-500" />
      case 'cloudy':
        return <Cloud size={24} className="text-gray-500" />
      case 'rain':
        return <CloudRain size={24} className="text-blue-500" />
      case 'heavy-rain':
        return <CloudRain size={24} className="text-blue-700" />
      case 'thunderstorm':
        return <CloudLightning size={24} className="text-purple-500" />
      default:
        return <Cloud size={24} className="text-gray-500" />
    }
  }
  
  // Get alert severity color
  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case 'advisory':
        return 'bg-blue-100 text-blue-800'
      case 'watch':
        return 'bg-yellow-100 text-yellow-800'
      case 'warning':
        return 'bg-orange-100 text-orange-800'
      case 'emergency':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
  
  return (
    <div>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center space-x-2">
          <select
            className="appearance-none bg-white border rounded-md px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-green-500"
            value={selectedRegion}
            onChange={(e) => setSelectedRegion(e.target.value)}
          >
            <option value="region-001">Addis Ababa</option>
            <option value="region-002">Oromia</option>
            <option value="region-003">Amhara</option>
          </select>
        </div>
        
        <div className="text-xs text-gray-500">
          {new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex items-center justify-center h-48">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-green-500"></div>
        </div>
      ) : (
        <div>
          {/* Weather alerts */}
          {widget.settings?.showAlerts && data.alerts.length > 0 && (
            <div className="mb-3">
              <div className="bg-orange-50 rounded-lg p-2">
                {data.alerts.map(alert => (
                  <div key={alert.id} className="flex items-start">
                    <div className="mt-0.5 mr-2">
                      <AlertTriangle size={14} className="text-orange-500" />
                    </div>
                    <div>
                      <div className="flex items-center">
                        <div className="font-medium text-sm">{alert.title}</div>
                        <span className={`ml-2 text-xs rounded-full px-1.5 py-0.5 ${getAlertSeverityColor(alert.severity)}`}>
                          {alert.severity}
                        </span>
                      </div>
                      <div className="text-xs text-gray-600 mt-0.5">{alert.description}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        Valid: {new Date(alert.validFrom).toLocaleTimeString()} - {new Date(alert.validTo).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* 5-day forecast */}
          <div className="grid grid-cols-5 gap-1">
            {data.forecasts.map((forecast, index) => (
              <div key={forecast.id} className="bg-gray-50 rounded-lg p-2 text-center">
                <div className="text-xs font-medium mb-1">
                  {index === 0 ? 'Today' : formatDate(forecast.forecastDate)}
                </div>
                <div className="flex justify-center mb-1">
                  {getWeatherIcon(forecast.condition)}
                </div>
                {widget.settings?.showTemperature && (
                  <div className="flex items-center justify-center space-x-1 text-xs">
                    <ThermometerSun size={10} className="text-red-500" />
                    <span>{forecast.temperature.min}°-{forecast.temperature.max}°</span>
                  </div>
                )}
                {widget.settings?.showPrecipitation && forecast.precipitation.probability > 0 && (
                  <div className="flex items-center justify-center space-x-1 text-xs mt-1">
                    <Droplets size={10} className="text-blue-500" />
                    <span>{forecast.precipitation.probability}%</span>
                  </div>
                )}
                {widget.settings?.showWind && (
                  <div className="flex items-center justify-center space-x-1 text-xs mt-1">
                    <Wind size={10} className="text-gray-500" />
                    <span>{forecast.wind.speed} km/h</span>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {/* Impact assessment */}
          {data.impactAssessments.length > 0 && (
            <div className="mt-3">
              <h4 className="text-xs font-medium mb-1">Potential Impacts</h4>
              <div className="bg-blue-50 rounded-lg p-2">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs font-medium">Risk Level:</span>
                  <span className="text-xs bg-yellow-100 text-yellow-800 rounded-full px-1.5 py-0.5">
                    {data.impactAssessments[0].riskLevel}
                  </span>
                </div>
                <div className="text-xs text-gray-600">
                  {data.impactAssessments[0].potentialImpacts[0].description}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Outage probability: {data.impactAssessments[0].estimatedOutageProbability}%
                </div>
              </div>
            </div>
          )}
          
          <div className="mt-3 text-center">
            <a href="/weather" className="text-green-600 text-sm hover:underline inline-flex items-center">
              View detailed forecast
              <ChevronRight size={14} className="ml-1" />
            </a>
          </div>
        </div>
      )}
    </div>
  )
}
