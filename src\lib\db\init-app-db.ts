/**
 * Application Database Initialization
 * 
 * This module ensures the database is initialized when the application starts.
 */

import { initializeDatabase } from './init-db';

let isInitialized = false;

export async function ensureDbInitialized() {
  if (isInitialized) {
    return;
  }

  try {
    console.log('🔄 Initializing application database...');
    await initializeDatabase(false); // Don't force, only init if empty
    isInitialized = true;
    console.log('✅ Database initialization completed');
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    // Don't throw, let the app continue with empty data
  }
}

// Auto-initialize when this module is imported
if (typeof window === 'undefined') {
  // Only run on server side
  ensureDbInitialized();
}
