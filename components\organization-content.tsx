"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/src/components/ui/tabs"
import { RegionsManagement } from "@/components/regions-management"
import { ServiceCentersManagement } from "@/components/service-centers-management"
import { RolesManagement } from "@/components/roles-management"
import { useAuth } from "@/src/features/auth/context/auth-context"

export function OrganizationContent() {
  const { user } = useAuth()

  // Determine which tabs to show based on user role
  const showRegions = ["super_admin", "national_asset_manager"].includes(user?.role || "")
  const showServiceCenters = ["super_admin", "national_asset_manager", "regional_admin"].includes(user?.role || "")
  const showRoles = ["super_admin"].includes(user?.role || "")

  return (
    <div className="flex flex-col gap-4">
      <div className="mb-2">
        <h1 className="text-2xl font-bold tracking-tight">Organization Management</h1>
        <p className="text-muted-foreground">Manage the organizational structure of Ethiopia Electric Utility</p>
      </div>

      <Tabs defaultValue={showRegions ? "regions" : "service-centers"} className="space-y-4">
        <TabsList>
          {showRegions && <TabsTrigger value="regions">Regions</TabsTrigger>}
          {showServiceCenters && <TabsTrigger value="service-centers">Service Centers</TabsTrigger>}
          {showRoles && <TabsTrigger value="roles">Roles</TabsTrigger>}
        </TabsList>

        {showRegions && (
          <TabsContent value="regions" className="space-y-4">
            <RegionsManagement />
          </TabsContent>
        )}

        {showServiceCenters && (
          <TabsContent value="service-centers" className="space-y-4">
            <ServiceCentersManagement />
          </TabsContent>
        )}

        {showRoles && (
          <TabsContent value="roles" className="space-y-4">
            <RolesManagement />
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}
