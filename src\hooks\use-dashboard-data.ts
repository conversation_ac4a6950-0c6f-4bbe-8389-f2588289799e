/**
 * Dashboard Data Hook
 * Clean React hook for fetching dashboard data
 */

import { useState, useEffect, useCallback } from 'react'
import { DashboardData, FilterOptions, ApiResponse } from '@/src/types'

interface UseDashboardDataReturn {
  data: DashboardData | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  updateFilters: (filters: FilterOptions) => void
  filters: FilterOptions
}

/**
 * Custom hook for dashboard data management
 */
export function useDashboardData(initialFilters: FilterOptions = {}): UseDashboardDataReturn {
  const [data, setData] = useState<DashboardData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<FilterOptions>(initialFilters)

  /**
   * Fetch dashboard data from API
   */
  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Build query parameters
      const params = new URLSearchParams()
      
      // Add filter parameters
      if (filters.regions?.length) {
        params.set('regions', filters.regions.join(','))
      }
      
      if (filters.serviceCenters?.length) {
        params.set('serviceCenters', filters.serviceCenters.join(','))
      }
      
      if (filters.types?.length) {
        params.set('types', filters.types.join(','))
      }
      
      if (filters.statuses?.length) {
        params.set('statuses', filters.statuses.join(','))
      }
      
      if (filters.manufacturers?.length) {
        params.set('manufacturers', filters.manufacturers.join(','))
      }
      
      if (filters.capacityRange) {
        params.set('capacityMin', filters.capacityRange[0].toString())
        params.set('capacityMax', filters.capacityRange[1].toString())
      }
      
      if (filters.efficiencyRange) {
        params.set('efficiencyMin', filters.efficiencyRange[0].toString())
        params.set('efficiencyMax', filters.efficiencyRange[1].toString())
      }
      
      if (filters.temperatureRange) {
        params.set('temperatureMin', filters.temperatureRange[0].toString())
        params.set('temperatureMax', filters.temperatureRange[1].toString())
      }
      
      if (filters.loadFactorRange) {
        params.set('loadFactorMin', filters.loadFactorRange[0].toString())
        params.set('loadFactorMax', filters.loadFactorRange[1].toString())
      }
      
      if (filters.assetValueRange) {
        params.set('assetValueMin', filters.assetValueRange[0].toString())
        params.set('assetValueMax', filters.assetValueRange[1].toString())
      }
      
      if (filters.search) {
        params.set('search', filters.search)
      }

      // Fetch data
      const response = await fetch(`/api/dashboard?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result: ApiResponse<DashboardData> = await response.json()
      
      if (result.success && result.data) {
        setData(result.data)
      } else {
        throw new Error(result.error || 'Failed to fetch dashboard data')
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Dashboard data fetch error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [filters])

  /**
   * Update filters and refetch data
   */
  const updateFilters = useCallback((newFilters: FilterOptions) => {
    setFilters(newFilters)
  }, [])

  /**
   * Refetch data manually
   */
  const refetch = useCallback(async () => {
    await fetchData()
  }, [fetchData])

  // Fetch data when filters change
  useEffect(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    isLoading,
    error,
    refetch,
    updateFilters,
    filters
  }
}
