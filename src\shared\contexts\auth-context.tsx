"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import type { User, UserRole } from "@/src/types/auth"
import { serviceFactory } from "@/src/services/service-factory"

interface AuthContextType {
  user: User | null
  isLoading: boolean
  error: string | null
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  updateUserProfile?: (userData: Partial<User>) => void
  hasPermission: (resource: string, action: string) => boolean
  hasRole: (roles: UserRole | UserRole[]) => boolean
  canAccessRegion: (regionId: string) => boolean
  canAccessServiceCenter: (serviceCenterId: string) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Check if user is already logged in
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // In a real app, this would be an API call to verify the session
        const storedUser = localStorage.getItem("eeu_user")
        if (storedUser) {
          setUser(JSON.parse(storedUser))
        }
      } catch (err) {
        console.error("Authentication error:", err)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (email: string, password: string) => {
    console.log("Auth context login called with:", email, password)
    setIsLoading(true)
    setError(null)

    try {
      // Use the MySQL login API directly
      const response = await fetch('/api/auth/mysql-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();
      console.log("Login response data:", data);

      if (!response.ok) {
        throw new Error(data.error || 'Login failed');
      }

      const authenticatedUser = data.user;

      if (!authenticatedUser) {
        console.error("Authentication failed: No user returned")
        throw new Error("Invalid email or password")
      }

      // Store user in localStorage
      localStorage.setItem("eeu_user", JSON.stringify(authenticatedUser))
      console.log("User stored in localStorage")

      // Also set a cookie for server-side authentication (this is also set by the API)
      document.cookie = `eeu_user=true; path=/; max-age=${60 * 60 * 24 * 7}`; // 7 days
      console.log("Cookie set")

      setUser(authenticatedUser)
      console.log("User state updated")

      return authenticatedUser
    } catch (err) {
      setError("Invalid email or password")
      console.error("Login error:", err)
      throw err
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    // Remove from localStorage
    localStorage.removeItem("eeu_user")

    // Remove the cookie
    document.cookie = "eeu_user=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

    setUser(null)
  }

  const hasPermission = (resource: string, action: string) => {
    if (!user) return false

    // Super admin has all permissions
    if (user.role === "super_admin") return true

    return user.permissions.some((permission) => permission.resource === resource && permission.action === action)
  }

  const hasRole = (roles: UserRole | UserRole[]) => {
    if (!user) return false

    if (Array.isArray(roles)) {
      return roles.includes(user.role)
    }

    return user.role === roles
  }

  const canAccessRegion = (regionId: string) => {
    if (!user) return false

    // Super admin, national asset manager, and national maintenance manager can access all regions
    if (["super_admin", "national_asset_manager", "national_maintenance_manager"].includes(user.role)) {
      return true
    }

    // Regional roles can only access their assigned region
    return user.regionId === regionId
  }

  const canAccessServiceCenter = (serviceCenterId: string) => {
    if (!user) return false

    // Super admin and national roles can access all service centers
    if (["super_admin", "national_asset_manager", "national_maintenance_manager"].includes(user.role)) {
      return true
    }

    // Regional roles can access all service centers in their region
    // This would require an additional check against a service center database
    // For now, we'll just check if the user is assigned to this service center
    return user.serviceCenterId === serviceCenterId
  }

  const updateUserProfile = (userData: Partial<User>) => {
    if (!user) return

    try {
      // Update user data
      const updatedUser = { ...user, ...userData }

      // In a real app, this would be an API call to update the user profile
      // For demo purposes, we'll just update the local state and localStorage
      setUser(updatedUser)
      localStorage.setItem("eeu_user", JSON.stringify(updatedUser))

      return true
    } catch (error) {
      console.error("Error updating user profile:", error)
      return false
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        error,
        login,
        logout,
        updateUserProfile,
        hasPermission,
        hasRole,
        canAccessRegion,
        canAccessServiceCenter,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

// Mock user data for demonstration
function getMockUser(email: string): User {
  // This would be replaced with actual user data from your authentication system
  const mockUsers: Record<string, User> = {
    "<EMAIL>": {
      id: "user-001",
      name: "Admin User",
      email: "<EMAIL>",
      role: "super_admin",
      organizationalLevel: "head_office",
      permissions: [
        { resource: "users", action: "create" },
        { resource: "users", action: "read" },
        { resource: "users", action: "update" },
        { resource: "users", action: "delete" },
        // Super admin has all permissions
      ],
      isActive: true,
      lastLogin: new Date().toISOString(),
      avatar: "/placeholder.svg?height=40&width=40&text=AU",
      phone: "+251-911-123-456",
      department: "IT Administration",
      location: "Addis Ababa",
      bio: "System administrator responsible for managing the EEU Transformer Management System.",
      createdAt: "2023-01-01T00:00:00Z",
      updatedAt: new Date().toISOString(),
    },
    "<EMAIL>": {
      id: "user-002",
      name: "Asset Manager",
      email: "<EMAIL>",
      role: "national_asset_manager",
      organizationalLevel: "head_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "transformers", action: "approve" },
        { resource: "reports", action: "export" },
      ],
      isActive: true,
      lastLogin: new Date().toISOString(),
      avatar: "/placeholder.svg?height=40&width=40&text=AM",
      phone: "+251-911-234-567",
      department: "Asset Management",
      location: "Addis Ababa",
      bio: "National asset manager responsible for overseeing transformer assets across the country.",
      createdAt: "2023-01-15T00:00:00Z",
      updatedAt: new Date().toISOString(),
    },
    "<EMAIL>": {
      id: "user-003",
      name: "Regional Admin",
      email: "<EMAIL>",
      role: "regional_admin",
      organizationalLevel: "regional_office",
      regionId: "region-001", // Addis Ababa
      permissions: [
        { resource: "users", action: "create" },
        { resource: "users", action: "read" },
        { resource: "users", action: "update" },
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "maintenance", action: "assign" },
      ],
      isActive: true,
      lastLogin: new Date().toISOString(),
      avatar: "/placeholder.svg?height=40&width=40&text=RA",
      phone: "+251-911-345-678",
      department: "Regional Administration",
      location: "Addis Ababa",
      bio: "Regional administrator for the Addis Ababa region, managing local operations and staff.",
      createdAt: "2023-02-01T00:00:00Z",
      updatedAt: new Date().toISOString(),
    },
    "<EMAIL>": {
      id: "user-004",
      name: "Service Center Manager",
      email: "<EMAIL>",
      role: "service_center_manager",
      organizationalLevel: "service_center",
      regionId: "region-001", // Addis Ababa
      serviceCenterId: "sc-001", // Bole Service Center
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "maintenance", action: "assign" },
        { resource: "maintenance", action: "approve" },
        { resource: "reports", action: "export" },
      ],
      isActive: true,
      lastLogin: new Date().toISOString(),
      avatar: "/placeholder.svg?height=40&width=40&text=SM",
      phone: "+251-911-456-789",
      department: "Service Operations",
      location: "Bole, Addis Ababa",
      bio: "Service center manager for the Bole district, overseeing local maintenance and service operations.",
      createdAt: "2023-02-15T00:00:00Z",
      updatedAt: new Date().toISOString(),
    },
    "<EMAIL>": {
      id: "user-005",
      name: "Field Technician",
      email: "<EMAIL>",
      role: "field_technician",
      organizationalLevel: "service_center",
      regionId: "region-001", // Addis Ababa
      serviceCenterId: "sc-001", // Bole Service Center
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "maintenance", action: "update" },
      ],
      isActive: true,
      lastLogin: new Date().toISOString(),
      avatar: "/placeholder.svg?height=40&width=40&text=FT",
      phone: "+251-911-567-890",
      department: "Field Operations",
      location: "Bole, Addis Ababa",
      bio: "Field technician responsible for on-site maintenance and repair of transformers.",
      createdAt: "2023-03-01T00:00:00Z",
      updatedAt: new Date().toISOString(),
    },
    "<EMAIL>": {
      id: "user-006",
      name: "Customer Service",
      email: "<EMAIL>",
      role: "customer_service_agent",
      organizationalLevel: "service_center",
      regionId: "region-001", // Addis Ababa
      serviceCenterId: "sc-001", // Bole Service Center
      permissions: [
        { resource: "outages", action: "read" },
        { resource: "customer_requests", action: "create" },
        { resource: "customer_requests", action: "read" },
        { resource: "customer_requests", action: "update" },
      ],
      isActive: true,
      lastLogin: new Date().toISOString(),
      avatar: "/placeholder.svg?height=40&width=40&text=CS",
      phone: "+251-911-678-901",
      department: "Customer Support",
      location: "Bole, Addis Ababa",
      bio: "Customer service agent handling customer inquiries and service requests.",
      createdAt: "2023-03-15T00:00:00Z",
      updatedAt: new Date().toISOString(),
    },
  }

  return mockUsers[email] || mockUsers["<EMAIL>"]
}
