"use client"

import { Compo<PERSON>, <PERSON>rrorIn<PERSON>, ReactNode, useEffect } from "react"
import { <PERSON>ert<PERSON>ircle, <PERSON>fresh<PERSON><PERSON>, AlertTriangle } from "lucide-react"
import { But<PERSON> } from "@/src/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/src/components/ui/card"

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onReset?: () => void
  onError?: () => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  isChunkError?: boolean
}

/**
 * Error boundary component to catch errors in the component tree
 * and display a fallback UI instead of crashing the whole app
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Check if this is a chunk loading error
    const isChunkError = error.message?.includes('ChunkLoadError') ||
                         error.message?.includes('Loading chunk') ||
                         error.message?.includes('Failed to fetch dynamically imported module');

    return { hasError: true, error, isChunkError }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to an error reporting service
    console.error("Error caught by ErrorBoundary:", error, errorInfo)

    // Call the onError callback if provided
    this.props.onError?.()
  }

  resetErrorBoundary = () => {
    this.props.onReset?.()
    this.setState({ hasError: false, error: undefined, isChunkError: false })

    // If it was a chunk error, reload the page to ensure fresh chunks
    if (this.state.isChunkError) {
      window.location.reload()
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Special handling for chunk loading errors
      if (this.state.isChunkError) {
        return (
          <div className="flex flex-col items-center justify-center min-h-[50vh] p-6 text-center">
            <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg border border-red-200 dark:border-red-800 max-w-md">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-bold mb-2">Resource Loading Error</h2>
              <p className="text-muted-foreground mb-4">
                Failed to load necessary resources for this page. This could be due to a network issue or a temporary problem.
              </p>
              <p className="text-sm text-muted-foreground mb-6">
                Error details: {this.state.error?.message || "Unknown chunk loading error"}
              </p>
              <Button onClick={() => window.location.reload()} className="mx-auto">
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Page
              </Button>
            </div>
          </div>
        )
      }

      // Default error UI
      return (
        <Card className="w-full">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-destructive">
              <AlertCircle className="mr-2 h-5 w-5" />
              Something went wrong
            </CardTitle>
            <CardDescription>
              An error occurred while rendering this component
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground mb-4">
              {this.state.error?.message || "Unknown error"}
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={this.resetErrorBoundary} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try again
            </Button>
          </CardFooter>
        </Card>
      )
    }

    return this.props.children
  }
}

/**
 * Client-side error handler component that catches global errors
 * including chunk loading errors that might not be caught by the ErrorBoundary
 */
export function GlobalErrorHandler({ children }: { children: ReactNode }) {
  useEffect(() => {
    const handleChunkError = (event: ErrorEvent) => {
      // Check if this is a chunk loading error
      if (
        event.message?.includes('ChunkLoadError') ||
        event.message?.includes('Loading chunk') ||
        event.filename?.includes('_next/static/chunks')
      ) {
        console.error('Chunk loading error detected:', event);

        // Prevent default error handling
        event.preventDefault();

        // Reload the page after a short delay
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    };

    // Add event listener for errors
    window.addEventListener('error', handleChunkError);

    // Clean up
    return () => {
      window.removeEventListener('error', handleChunkError);
    };
  }, []);

  return <>{children}</>;
}
