"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/src/components/ui/card"

interface TransformerSpecificationsProps {
  transformer: any
}

export function TransformerSpecifications({ transformer }: TransformerSpecificationsProps) {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>General Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2 border-b py-2">
              <div className="font-medium">Serial Number</div>
              <div>{transformer.serialNumber}</div>
            </div>
            <div className="grid grid-cols-2 gap-2 border-b py-2">
              <div className="font-medium">Manufacturer</div>
              <div>{transformer.manufacturer}</div>
            </div>
            <div className="grid grid-cols-2 gap-2 border-b py-2">
              <div className="font-medium">Type</div>
              <div>{transformer.type}</div>
            </div>
            <div className="grid grid-cols-2 gap-2 border-b py-2">
              <div className="font-medium">Installation Date</div>
              <div>{transformer.installationDate}</div>
            </div>
            <div className="grid grid-cols-2 gap-2 border-b py-2">
              <div className="font-medium">Feeder Name</div>
              <div>{transformer.feederName}</div>
            </div>
            <div className="grid grid-cols-2 gap-2 py-2">
              <div className="font-medium">Substation</div>
              <div>{transformer.substationName}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Technical Specifications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2 border-b py-2">
              <div className="font-medium">Rated Capacity</div>
              <div>{transformer.capacity}</div>
            </div>
            <div className="grid grid-cols-2 gap-2 border-b py-2">
              <div className="font-medium">Primary Voltage</div>
              <div>{transformer.primaryVoltage}</div>
            </div>
            <div className="grid grid-cols-2 gap-2 border-b py-2">
              <div className="font-medium">Secondary Voltage</div>
              <div>{transformer.secondaryVoltage}</div>
            </div>
            <div className="grid grid-cols-2 gap-2 border-b py-2">
              <div className="font-medium">Impedance</div>
              <div>{transformer.impedance}</div>
            </div>
            <div className="grid grid-cols-2 gap-2 border-b py-2">
              <div className="font-medium">Cooling Type</div>
              <div>{transformer.coolingType}</div>
            </div>
            <div className="grid grid-cols-2 gap-2 py-2">
              <div className="font-medium">Oil Type</div>
              <div>{transformer.oilType}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Ethiopian Standards Compliance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="grid grid-cols-3 gap-2 border-b py-2">
              <div className="font-medium">Standard</div>
              <div className="font-medium">Description</div>
              <div className="font-medium">Compliance Status</div>
            </div>
            <div className="grid grid-cols-3 gap-2 border-b py-2">
              <div>ES ISO 5001:2018</div>
              <div>Energy Management Systems</div>
              <div className="text-success">Compliant</div>
            </div>
            <div className="grid grid-cols-3 gap-2 border-b py-2">
              <div>ES IEC 60076-1:2011</div>
              <div>Power Transformers - General</div>
              <div className="text-success">Compliant</div>
            </div>
            <div className="grid grid-cols-3 gap-2 border-b py-2">
              <div>ES IEC 60076-2:2011</div>
              <div>Power Transformers - Temperature Rise</div>
              <div className="text-success">Compliant</div>
            </div>
            <div className="grid grid-cols-3 gap-2 py-2">
              <div>ES IEC 60076-3:2013</div>
              <div>Power Transformers - Insulation Levels</div>
              <div className="text-success">Compliant</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
