import { NextRequest, NextResponse } from 'next/server'
import { getDashboardAnalytics } from '@/lib/mysql-connection';

export async function GET(request: NextRequest) {
  try {
    // Fetch comprehensive dashboard analytics data
    const analyticsData = await getDashboardAnalytics();

    return NextResponse.json({
      success: true,
      data: analyticsData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error fetching dashboard analytics:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch dashboard analytics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, filters } = body

    // Import database connection utility
    const { executeQuery } = await import('../../../../lib/mysql-connection')

    switch (action) {
      case 'refresh_metrics':
        // Trigger metrics recalculation
        await executeQuery(`
          INSERT INTO app_performance_metrics (transformer_id, uptime_percentage, efficiency_rating, load_factor, recorded_at)
          SELECT
            id,
            CASE WHEN status = 'operational' THEN 95 + (RAND() * 5) ELSE 0 END,
            efficiency,
            load_percentage,
            NOW()
          FROM app_transformers
        `)
        break

      case 'generate_report':
        // Generate analytics report
        const reportData = await executeQuery(`
          SELECT
            'Analytics Report' as title,
            NOW() as generated_at,
            COUNT(*) as total_transformers
          FROM app_transformers
        `)

        return NextResponse.json({
          success: true,
          report: reportData[0],
          message: 'Analytics report generated successfully'
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      message: 'Action completed successfully'
    })

  } catch (error) {
    console.error('❌ Error processing dashboard analytics action:', error)
    return NextResponse.json(
      {
        error: 'Failed to process action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
