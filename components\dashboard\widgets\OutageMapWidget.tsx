"use client"

import { useState, useEffect } from 'react'
import { Map, AlertCircle, AlertTriangle, Users, Zap, ChevronRight } from 'lucide-react'
import { OutageMapWidget as OutageMapWidgetType } from '../../../types/dashboard-widgets'

interface OutageMapWidgetProps {
  widget: OutageMapWidgetType
  isEditing: boolean
}

export default function OutageMapWidget({ widget, isEditing }: OutageMapWidgetProps) {
  const [data, setData] = useState({
    outages: [
      {
        id: 'outage-001',
        regionId: 'region-001',
        regionName: 'Addis Ababa',
        serviceCenterId: 'sc-001',
        serviceCenterName: 'Bole Service Center',
        affectedTransformers: ['tr-001', 'tr-002'],
        affectedCustomers: 1250,
        startTime: '2023-06-14T08:30:00Z',
        estimatedEndTime: '2023-06-14T14:30:00Z',
        status: 'active',
        severity: 'major',
        cause: {
          category: 'equipment-failure',
          description: 'Transformer overload due to high demand'
        },
        location: {
          lat: 9.0222,
          lng: 38.7468,
          address: 'Bole Sub-district, Addis Ababa'
        }
      },
      {
        id: 'outage-002',
        regionId: 'region-001',
        regionName: 'Addis Ababa',
        serviceCenterId: 'sc-002',
        serviceCenterName: 'Kirkos Service Center',
        affectedTransformers: ['tr-015'],
        affectedCustomers: 850,
        startTime: '2023-06-14T10:15:00Z',
        estimatedEndTime: '2023-06-14T16:00:00Z',
        status: 'active',
        severity: 'moderate',
        cause: {
          category: 'weather-related',
          description: 'Heavy rain causing equipment malfunction'
        },
        location: {
          lat: 9.0092,
          lng: 38.7645,
          address: 'Kirkos Sub-district, Addis Ababa'
        }
      },
      {
        id: 'outage-003',
        regionId: 'region-002',
        regionName: 'Oromia',
        serviceCenterId: 'sc-005',
        serviceCenterName: 'Adama Service Center',
        affectedTransformers: ['tr-042', 'tr-043', 'tr-044'],
        affectedCustomers: 2100,
        startTime: '2023-06-14T09:45:00Z',
        estimatedEndTime: '2023-06-14T18:00:00Z',
        status: 'active',
        severity: 'critical',
        cause: {
          category: 'equipment-failure',
          description: 'Substation failure affecting multiple transformers'
        },
        location: {
          lat: 8.5400,
          lng: 39.2700,
          address: 'Central Adama, Oromia'
        }
      },
      {
        id: 'outage-004',
        regionId: 'region-003',
        regionName: 'Amhara',
        serviceCenterId: 'sc-008',
        serviceCenterName: 'Bahir Dar Service Center',
        affectedTransformers: ['tr-078'],
        affectedCustomers: 750,
        startTime: '2023-06-14T11:30:00Z',
        estimatedEndTime: '2023-06-14T15:30:00Z',
        status: 'active',
        severity: 'minor',
        cause: {
          category: 'planned-maintenance',
          description: 'Scheduled maintenance for transformer upgrade'
        },
        location: {
          lat: 11.5742,
          lng: 37.3614,
          address: 'Downtown Bahir Dar, Amhara'
        }
      },
      {
        id: 'outage-005',
        regionId: 'region-001',
        regionName: 'Addis Ababa',
        serviceCenterId: 'sc-003',
        serviceCenterName: 'Yeka Service Center',
        affectedTransformers: ['tr-023'],
        affectedCustomers: 950,
        startTime: '2023-06-14T12:00:00Z',
        estimatedEndTime: '2023-06-14T17:00:00Z',
        status: 'active',
        severity: 'moderate',
        cause: {
          category: 'equipment-failure',
          description: 'Cable fault causing power interruption'
        },
        location: {
          lat: 9.0299,
          lng: 38.8079,
          address: 'Yeka Sub-district, Addis Ababa'
        }
      }
    ],
    summary: {
      total: 5,
      bySeverity: {
        minor: 1,
        moderate: 2,
        major: 1,
        critical: 1,
        emergency: 0
      },
      totalAffectedCustomers: 5900,
      totalAffectedTransformers: 8
    }
  })
  
  const [isLoading, setIsLoading] = useState(false)
  const [mapType, setMapType] = useState<'standard' | 'satellite' | 'hybrid'>(
    widget.settings?.mapType || 'standard'
  )
  
  // Fetch data
  useEffect(() => {
    // In a real implementation, this would fetch from the API
    // For now, we're using mock data initialized above
  }, [widget.id])
  
  // Refresh data
  const refreshData = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'minor':
        return 'bg-blue-100 text-blue-800'
      case 'moderate':
        return 'bg-yellow-100 text-yellow-800'
      case 'major':
        return 'bg-orange-100 text-orange-800'
      case 'critical':
        return 'bg-red-100 text-red-800'
      case 'emergency':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
  
  // Get severity icon
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'minor':
        return <AlertCircle size={14} className="text-blue-500" />
      case 'moderate':
        return <AlertCircle size={14} className="text-yellow-500" />
      case 'major':
        return <AlertCircle size={14} className="text-orange-500" />
      case 'critical':
        return <AlertTriangle size={14} className="text-red-500" />
      case 'emergency':
        return <AlertTriangle size={14} className="text-purple-500" />
      default:
        return <AlertCircle size={14} className="text-gray-500" />
    }
  }
  
  return (
    <div>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center space-x-2">
          <button
            className={`px-2 py-0.5 rounded text-xs ${mapType === 'standard' ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setMapType('standard')}
          >
            Standard
          </button>
          
          <button
            className={`px-2 py-0.5 rounded text-xs ${mapType === 'satellite' ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setMapType('satellite')}
          >
            Satellite
          </button>
          
          <button
            className={`px-2 py-0.5 rounded text-xs ${mapType === 'hybrid' ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'}`}
            onClick={() => setMapType('hybrid')}
          >
            Hybrid
          </button>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex items-center text-xs text-gray-500">
            <Users size={12} className="mr-1" />
            <span>{data.summary.totalAffectedCustomers.toLocaleString()}</span>
          </div>
          
          <div className="flex items-center text-xs text-gray-500">
            <Zap size={12} className="mr-1" />
            <span>{data.summary.totalAffectedTransformers}</span>
          </div>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-green-500"></div>
        </div>
      ) : (
        <div>
          {/* Map placeholder */}
          <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center mb-3">
            <div className="text-center">
              <Map size={32} className="mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500 text-sm">Map visualization would appear here</p>
              <p className="text-gray-400 text-xs mt-1">Showing {data.outages.length} active outages</p>
            </div>
          </div>
          
          {/* Outage list */}
          <div className="space-y-2 max-h-48 overflow-y-auto pr-1">
            {data.outages.map(outage => (
              <div key={outage.id} className="bg-gray-50 rounded-lg p-2 text-sm">
                <div className="flex items-start">
                  <div className="mt-0.5 mr-2">
                    {getSeverityIcon(outage.severity)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="font-medium text-sm">{outage.serviceCenterName}</div>
                      <span className={`text-xs rounded-full px-1.5 py-0.5 ${getSeverityColor(outage.severity)}`}>
                        {outage.severity}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500 mt-0.5">{outage.location.address}</div>
                    <div className="flex items-center justify-between mt-1">
                      <div className="text-xs text-gray-500">
                        Started: {formatDate(outage.startTime)}
                      </div>
                      <div className="flex items-center text-xs text-gray-500">
                        <Users size={12} className="mr-1" />
                        <span>{outage.affectedCustomers.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-3 text-center">
            <a href="/outages" className="text-green-600 text-sm hover:underline inline-flex items-center">
              View all outages
              <ChevronRight size={14} className="ml-1" />
            </a>
          </div>
        </div>
      )}
    </div>
  )
}
