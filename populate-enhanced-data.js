/**
 * Populate Enhanced Data for dtms_eeu_db
 *
 * This script populates all tables with comprehensive realistic sample data
 * for testing and demonstration of all dashboard functionalities.
 */

const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db'
};

let connection;

async function initConnection() {
  connection = await mysql.createConnection(config);
  console.log('✅ MySQL connection established');
}

async function executeQuery(query, params = []) {
  try {
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('❌ Query error:', error.message);
    console.error('Query:', query.substring(0, 100) + '...');
    throw error;
  }
}

// Helper function to generate random date within range
function randomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// Helper function to generate random number within range
function randomBetween(min, max) {
  return Math.random() * (max - min) + min;
}

async function populateServiceCenters() {
  console.log('🔄 Populating service centers...');

  try {
    // Get regions first
    const regions = await executeQuery('SELECT id, name, code, lat, lng FROM app_regions');

    const serviceCenters = [];

    // Create 2-3 service centers per region
    regions.forEach((region, regionIndex) => {
      const centersPerRegion = Math.floor(Math.random() * 2) + 2; // 2-3 centers

      for (let i = 0; i < centersPerRegion; i++) {
        serviceCenters.push({
          id: uuidv4(),
          name: `${region.name} Service Center ${i + 1}`,
          code: `${region.code}-SC${String(i + 1).padStart(2, '0')}`,
          regionId: region.id,
          address: `Service Center ${i + 1}, ${region.name}, Ethiopia`,
          lat: parseFloat(region.lat) + (Math.random() - 0.5) * 0.2,
          lng: parseFloat(region.lng) + (Math.random() - 0.5) * 0.2,
          contactPhone: `+251-${10 + regionIndex}-${100 + i}-${1000 + Math.floor(Math.random() * 9000)}`,
          contactEmail: `${region.code.toLowerCase()}.sc${i + 1}@eeu.gov.et`,
          managerName: `Manager ${regionIndex + 1}-${i + 1}`,
          capacity: 30 + Math.floor(Math.random() * 70), // 30-100
          operatingHours: JSON.stringify({
            monday: { open: '08:00', close: '17:00' },
            tuesday: { open: '08:00', close: '17:00' },
            wednesday: { open: '08:00', close: '17:00' },
            thursday: { open: '08:00', close: '17:00' },
            friday: { open: '08:00', close: '17:00' },
            saturday: { open: '08:00', close: '12:00' },
            sunday: { closed: true }
          }),
          facilities: JSON.stringify([
            'Workshop', 'Storage', 'Testing Lab', 'Vehicle Bay', 'Office Space'
          ])
        });
      }
    });

    for (const center of serviceCenters) {
      await executeQuery(`
        INSERT INTO app_service_centers (
          id, name, code, region_id, address, lat, lng, contact_phone,
          contact_email, manager_name, capacity, operating_hours, facilities, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        center.id, center.name, center.code, center.regionId, center.address,
        center.lat, center.lng, center.contactPhone, center.contactEmail,
        center.managerName, center.capacity, center.operatingHours, center.facilities, 1
      ]);
    }

    console.log(`✅ Inserted ${serviceCenters.length} service centers`);
    return serviceCenters;

  } catch (error) {
    console.error('❌ Service centers population failed:', error.message);
    throw error;
  }
}

async function populateTransformers() {
  console.log('🔄 Populating transformers...');

  try {
    // Get regions and service centers
    const regions = await executeQuery('SELECT id, name, code FROM app_regions');
    const serviceCenters = await executeQuery('SELECT id, region_id FROM app_service_centers');

    const transformers = [];
    const statuses = ['operational', 'warning', 'critical', 'maintenance', 'offline'];
    const types = ['Distribution', 'Power', 'Pad-mounted', 'Pole-mounted', 'Substation'];
    const manufacturers = ['ABB', 'Siemens', 'Schneider Electric', 'General Electric', 'Toshiba', 'Hyundai'];
    const capacities = [50, 100, 160, 250, 315, 400, 500, 630, 800, 1000, 1250, 1600];
    const primaryVoltages = [11, 15, 33, 66, 132];
    const secondaryVoltages = [400, 415, 433];

    // Create 8-12 transformers per region
    regions.forEach((region, regionIndex) => {
      const transformersPerRegion = Math.floor(Math.random() * 5) + 8; // 8-12 transformers
      const regionServiceCenters = serviceCenters.filter(sc => sc.region_id === region.id);

      for (let i = 0; i < transformersPerRegion; i++) {
        const serviceCenter = regionServiceCenters[Math.floor(Math.random() * regionServiceCenters.length)];
        const capacity = capacities[Math.floor(Math.random() * capacities.length)];
        const manufacturer = manufacturers[Math.floor(Math.random() * manufacturers.length)];
        const type = types[Math.floor(Math.random() * types.length)];
        const status = statuses[Math.floor(Math.random() * statuses.length)];

        // Generate realistic metrics based on status
        let temperature, loadPercentage, oilLevel, healthIndex, efficiency;

        switch (status) {
          case 'operational':
            temperature = randomBetween(35, 55);
            loadPercentage = randomBetween(40, 80);
            oilLevel = randomBetween(85, 100);
            healthIndex = randomBetween(80, 95);
            efficiency = randomBetween(94, 98);
            break;
          case 'warning':
            temperature = randomBetween(55, 70);
            loadPercentage = randomBetween(70, 90);
            oilLevel = randomBetween(70, 85);
            healthIndex = randomBetween(60, 80);
            efficiency = randomBetween(90, 94);
            break;
          case 'critical':
            temperature = randomBetween(70, 85);
            loadPercentage = randomBetween(85, 100);
            oilLevel = randomBetween(50, 70);
            healthIndex = randomBetween(30, 60);
            efficiency = randomBetween(85, 90);
            break;
          case 'maintenance':
            temperature = 0;
            loadPercentage = 0;
            oilLevel = randomBetween(60, 90);
            healthIndex = randomBetween(70, 85);
            efficiency = 0;
            break;
          case 'offline':
            temperature = 0;
            loadPercentage = 0;
            oilLevel = randomBetween(80, 100);
            healthIndex = randomBetween(50, 90);
            efficiency = 0;
            break;
        }

        const installationDate = randomDate(new Date(2015, 0, 1), new Date(2023, 11, 31));
        const lastMaintenanceDate = randomDate(new Date(2023, 0, 1), new Date());
        const nextMaintenanceDate = new Date(lastMaintenanceDate.getTime() + (180 * 24 * 60 * 60 * 1000)); // 6 months later

        transformers.push({
          id: uuidv4(),
          serialNumber: `EEU-${region.code}-${String(1000 + regionIndex * 100 + i).padStart(4, '0')}`,
          name: `${region.name} ${type} Transformer ${i + 1}`,
          status: status,
          type: type,
          manufacturer: manufacturer,
          model: `${manufacturer.split(' ')[0]}-${capacity}kVA-${Math.floor(Math.random() * 900) + 100}`,
          capacity: capacity,
          voltagePrimary: primaryVoltages[Math.floor(Math.random() * primaryVoltages.length)],
          voltageSecondary: secondaryVoltages[Math.floor(Math.random() * secondaryVoltages.length)],
          regionId: region.id,
          serviceCenterId: serviceCenter.id,
          locationAddress: `Location ${i + 1}, ${region.name}, Ethiopia`,
          lat: parseFloat(region.lat) + (Math.random() - 0.5) * 0.3,
          lng: parseFloat(region.lng) + (Math.random() - 0.5) * 0.3,
          temperature: temperature,
          loadPercentage: loadPercentage,
          oilLevel: oilLevel,
          healthIndex: healthIndex,
          efficiency: efficiency,
          powerFactor: randomBetween(0.85, 0.98),
          installationDate: installationDate.toISOString().split('T')[0],
          lastMaintenanceDate: lastMaintenanceDate.toISOString().split('T')[0],
          nextMaintenanceDate: nextMaintenanceDate.toISOString().split('T')[0],
          warrantyExpiry: new Date(installationDate.getTime() + (5 * 365 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
          cost: capacity * randomBetween(800, 1200), // Cost per kVA
          depreciationRate: randomBetween(3, 7),
          smartMeterEnabled: Math.random() > 0.3 ? 1 : 0,
          iotDeviceId: Math.random() > 0.3 ? `IOT-${region.code}-${i + 1}` : null,
          isCritical: capacity >= 500 ? 1 : 0,
          tags: JSON.stringify([type.toLowerCase(), manufacturer.toLowerCase().replace(' ', '_')]),
          specifications: JSON.stringify({
            cooling: 'ONAN',
            insulation: 'Mineral Oil',
            tapChanger: capacity >= 250 ? 'OLTC' : 'Manual',
            protection: ['Buchholz Relay', 'Temperature Monitor', 'Pressure Relief'],
            standards: ['IEC 60076', 'IEEE C57.12.00']
          })
        });
      }
    });

    for (const transformer of transformers) {
      await executeQuery(`
        INSERT INTO app_transformers (
          id, serial_number, name, status, type, manufacturer, model, capacity,
          voltage_primary, voltage_secondary, region_id, service_center_id,
          location_address, lat, lng, temperature, load_percentage, oil_level,
          health_index, efficiency, power_factor, installation_date,
          last_maintenance_date, next_maintenance_date, warranty_expiry,
          cost, depreciation_rate, smart_meter_enabled, iot_device_id,
          is_critical, tags, specifications
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        transformer.id, transformer.serialNumber, transformer.name, transformer.status,
        transformer.type, transformer.manufacturer, transformer.model, transformer.capacity,
        transformer.voltagePrimary, transformer.voltageSecondary, transformer.regionId,
        transformer.serviceCenterId, transformer.locationAddress, transformer.lat, transformer.lng,
        transformer.temperature, transformer.loadPercentage, transformer.oilLevel,
        transformer.healthIndex, transformer.efficiency, transformer.powerFactor,
        transformer.installationDate, transformer.lastMaintenanceDate, transformer.nextMaintenanceDate,
        transformer.warrantyExpiry, transformer.cost, transformer.depreciationRate,
        transformer.smartMeterEnabled, transformer.iotDeviceId, transformer.isCritical,
        transformer.tags, transformer.specifications
      ]);
    }

    console.log(`✅ Inserted ${transformers.length} transformers`);
    return transformers;

  } catch (error) {
    console.error('❌ Transformers population failed:', error.message);
    throw error;
  }
}

async function populateAlerts() {
  console.log('🔄 Populating alerts...');

  try {
    const transformers = await executeQuery('SELECT id, serial_number, status, region_id, service_center_id FROM app_transformers');
    const users = await executeQuery('SELECT id FROM app_users');

    const alerts = [];
    const alertTypes = ['temperature', 'load', 'oil_level', 'vibration', 'power_quality', 'maintenance_due', 'communication_loss'];
    const categories = ['operational', 'maintenance', 'safety', 'environmental', 'security'];
    const severities = ['low', 'medium', 'high', 'critical'];
    const priorities = ['low', 'medium', 'high', 'urgent'];

    // Generate 2-5 alerts per transformer
    transformers.forEach((transformer, index) => {
      const alertCount = Math.floor(Math.random() * 4) + 2; // 2-5 alerts

      for (let i = 0; i < alertCount; i++) {
        const alertType = alertTypes[Math.floor(Math.random() * alertTypes.length)];
        const category = categories[Math.floor(Math.random() * categories.length)];
        const severity = severities[Math.floor(Math.random() * severities.length)];
        const priority = priorities[Math.floor(Math.random() * priorities.length)];
        const isResolved = Math.random() > 0.3; // 70% resolved
        const isAcknowledged = isResolved || Math.random() > 0.5;

        const createdAt = randomDate(new Date(2024, 0, 1), new Date());
        const acknowledgedAt = isAcknowledged ? new Date(createdAt.getTime() + Math.random() * 3600000) : null; // Within 1 hour
        const resolvedAt = isResolved ? new Date((acknowledgedAt || createdAt).getTime() + Math.random() * 86400000) : null; // Within 24 hours

        alerts.push({
          id: uuidv4(),
          transformerId: transformer.id,
          regionId: transformer.region_id,
          serviceCenterId: transformer.service_center_id,
          type: alertType,
          category: category,
          severity: severity,
          priority: priority,
          title: `${alertType.charAt(0).toUpperCase() + alertType.slice(1).replace('_', ' ')} Alert - ${transformer.serial_number}`,
          message: `${severity.charAt(0).toUpperCase() + severity.slice(1)} ${alertType.replace('_', ' ')} alert detected on transformer ${transformer.serial_number}`,
          details: JSON.stringify({
            threshold_exceeded: alertType === 'temperature' ? '75°C' : alertType === 'load' ? '90%' : 'N/A',
            current_value: alertType === 'temperature' ? '78°C' : alertType === 'load' ? '92%' : 'N/A',
            duration: `${Math.floor(Math.random() * 120) + 5} minutes`,
            impact: severity === 'critical' ? 'High' : severity === 'high' ? 'Medium' : 'Low'
          }),
          source: Math.random() > 0.2 ? 'system' : 'manual',
          isResolved: isResolved ? 1 : 0,
          isAcknowledged: isAcknowledged ? 1 : 0,
          acknowledgedBy: isAcknowledged ? users[Math.floor(Math.random() * users.length)].id : null,
          acknowledgedAt: acknowledgedAt,
          resolvedBy: isResolved ? users[Math.floor(Math.random() * users.length)].id : null,
          resolvedAt: resolvedAt,
          resolutionNotes: isResolved ? 'Issue resolved through maintenance intervention' : null,
          escalationLevel: severity === 'critical' ? 2 : severity === 'high' ? 1 : 0,
          autoGenerated: Math.random() > 0.2 ? 1 : 0,
          createdAt: createdAt
        });
      }
    });

    for (const alert of alerts) {
      await executeQuery(`
        INSERT INTO app_alerts (
          id, transformer_id, region_id, service_center_id, type, category,
          severity, priority, title, message, details, source, is_resolved,
          is_acknowledged, acknowledged_by, acknowledged_at, resolved_by,
          resolved_at, resolution_notes, escalation_level, auto_generated, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        alert.id, alert.transformerId, alert.regionId, alert.serviceCenterId,
        alert.type, alert.category, alert.severity, alert.priority, alert.title,
        alert.message, alert.details, alert.source, alert.isResolved,
        alert.isAcknowledged, alert.acknowledgedBy, alert.acknowledgedAt,
        alert.resolvedBy, alert.resolvedAt, alert.resolutionNotes,
        alert.escalationLevel, alert.autoGenerated, alert.createdAt
      ]);
    }

    console.log(`✅ Inserted ${alerts.length} alerts`);
    return alerts;

  } catch (error) {
    console.error('❌ Alerts population failed:', error.message);
    throw error;
  }
}

async function populateMaintenanceSchedules() {
  console.log('🔄 Populating maintenance schedules...');

  try {
    const transformers = await executeQuery('SELECT id, serial_number, next_maintenance_date FROM app_transformers');
    const users = await executeQuery('SELECT id, role FROM app_users WHERE role IN ("technician", "maintenance_manager")');

    const maintenanceSchedules = [];
    const maintenanceTypes = ['preventive', 'corrective', 'emergency', 'inspection', 'testing'];
    const statuses = ['scheduled', 'in_progress', 'completed', 'cancelled', 'overdue'];
    const priorities = ['low', 'medium', 'high', 'critical'];

    // Generate 1-3 maintenance records per transformer
    transformers.forEach((transformer, index) => {
      const scheduleCount = Math.floor(Math.random() * 3) + 1; // 1-3 schedules

      for (let i = 0; i < scheduleCount; i++) {
        const maintenanceType = maintenanceTypes[Math.floor(Math.random() * maintenanceTypes.length)];
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const priority = priorities[Math.floor(Math.random() * priorities.length)];
        const technician = users[Math.floor(Math.random() * users.length)];
        const supervisor = users.find(u => u.role === 'maintenance_manager') || technician;

        const scheduledDate = randomDate(new Date(2024, 0, 1), new Date(2024, 11, 31));
        const isCompleted = status === 'completed';
        const isInProgress = status === 'in_progress';

        maintenanceSchedules.push({
          id: uuidv4(),
          transformerId: transformer.id,
          type: maintenanceType,
          status: status,
          priority: priority,
          title: `${maintenanceType.charAt(0).toUpperCase() + maintenanceType.slice(1)} Maintenance - ${transformer.serial_number}`,
          description: `Scheduled ${maintenanceType} maintenance for transformer ${transformer.serial_number}`,
          scheduledDate: scheduledDate.toISOString().split('T')[0],
          scheduledTime: `${String(Math.floor(Math.random() * 8) + 8).padStart(2, '0')}:00:00`,
          estimatedDuration: Math.floor(Math.random() * 360) + 120, // 2-8 hours
          actualStartTime: isCompleted || isInProgress ? scheduledDate : null,
          actualEndTime: isCompleted ? new Date(scheduledDate.getTime() + (Math.random() * 8 * 3600000)) : null,
          technicianId: technician.id,
          supervisorId: supervisor.id,
          teamMembers: JSON.stringify([
            { id: technician.id, role: 'Lead Technician' },
            { id: uuidv4(), role: 'Assistant Technician' }
          ]),
          requiredParts: JSON.stringify([
            { part: 'Oil Filter', quantity: 2, cost: 150 },
            { part: 'Gasket Set', quantity: 1, cost: 75 },
            { part: 'Insulation Oil', quantity: 500, unit: 'liters', cost: 1200 }
          ]),
          requiredTools: JSON.stringify([
            'Oil Testing Kit', 'Insulation Tester', 'Thermographic Camera', 'Multimeter'
          ]),
          safetyRequirements: JSON.stringify([
            'Lock-out Tag-out Procedure', 'PPE Required', 'Gas Detection', 'Fire Safety Equipment'
          ]),
          workInstructions: `1. Isolate transformer\n2. Test oil quality\n3. Inspect windings\n4. Check connections\n5. Perform insulation test\n6. Document findings`,
          completionNotes: isCompleted ? 'Maintenance completed successfully. All parameters within normal range.' : null,
          qualityCheckPassed: isCompleted ? (Math.random() > 0.1 ? 1 : 0) : null,
          cost: Math.floor(Math.random() * 3000) + 500,
          downtimeMinutes: isCompleted ? Math.floor(Math.random() * 240) + 60 : 0,
          createdBy: supervisor.id
        });
      }
    });

    for (const schedule of maintenanceSchedules) {
      await executeQuery(`
        INSERT INTO app_maintenance_schedules (
          id, transformer_id, type, status, priority, title, description,
          scheduled_date, scheduled_time, estimated_duration, actual_start_time,
          actual_end_time, technician_id, supervisor_id, team_members,
          required_parts, required_tools, safety_requirements, work_instructions,
          completion_notes, quality_check_passed, cost, downtime_minutes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        schedule.id, schedule.transformerId, schedule.type, schedule.status,
        schedule.priority, schedule.title, schedule.description, schedule.scheduledDate,
        schedule.scheduledTime, schedule.estimatedDuration, schedule.actualStartTime,
        schedule.actualEndTime, schedule.technicianId, schedule.supervisorId,
        schedule.teamMembers, schedule.requiredParts, schedule.requiredTools,
        schedule.safetyRequirements, schedule.workInstructions, schedule.completionNotes,
        schedule.qualityCheckPassed, schedule.cost, schedule.downtimeMinutes, schedule.createdBy
      ]);
    }

    console.log(`✅ Inserted ${maintenanceSchedules.length} maintenance schedules`);
    return maintenanceSchedules;

  } catch (error) {
    console.error('❌ Maintenance schedules population failed:', error.message);
    throw error;
  }
}

async function populateNotifications() {
  console.log('🔄 Populating notifications...');

  try {
    const users = await executeQuery('SELECT id FROM app_users');
    const transformers = await executeQuery('SELECT id, serial_number FROM app_transformers LIMIT 10');

    const notifications = [];
    const notificationTypes = ['alert', 'maintenance', 'system', 'reminder', 'update'];
    const categories = ['critical', 'warning', 'info', 'maintenance', 'system'];
    const priorities = ['low', 'normal', 'high', 'urgent'];

    // Generate 5-10 notifications per user
    users.forEach((user, userIndex) => {
      const notificationCount = Math.floor(Math.random() * 6) + 5; // 5-10 notifications

      for (let i = 0; i < notificationCount; i++) {
        const notificationType = notificationTypes[Math.floor(Math.random() * notificationTypes.length)];
        const category = categories[Math.floor(Math.random() * categories.length)];
        const priority = priorities[Math.floor(Math.random() * priorities.length)];
        const isRead = Math.random() > 0.4; // 60% read
        const transformer = transformers[Math.floor(Math.random() * transformers.length)];

        const createdAt = randomDate(new Date(2024, 0, 1), new Date());
        const readAt = isRead ? new Date(createdAt.getTime() + Math.random() * 86400000) : null;

        notifications.push({
          id: uuidv4(),
          userId: user.id,
          type: notificationType,
          category: category,
          title: `${notificationType.charAt(0).toUpperCase() + notificationType.slice(1)} Notification`,
          message: `${category.charAt(0).toUpperCase() + category.slice(1)} ${notificationType} notification for transformer ${transformer.serial_number}`,
          data: JSON.stringify({
            transformer_id: transformer.id,
            transformer_serial: transformer.serial_number,
            action_required: priority === 'urgent',
            estimated_resolution: '2 hours'
          }),
          priority: priority,
          isRead: isRead ? 1 : 0,
          isArchived: Math.random() > 0.8 ? 1 : 0,
          readAt: readAt,
          expiresAt: new Date(createdAt.getTime() + (7 * 24 * 60 * 60 * 1000)), // 7 days
          actionUrl: `/transformers/${transformer.id}`,
          actionLabel: 'View Details',
          senderId: users[Math.floor(Math.random() * users.length)].id,
          broadcast: Math.random() > 0.9 ? 1 : 0,
          createdAt: createdAt
        });
      }
    });

    for (const notification of notifications) {
      await executeQuery(`
        INSERT INTO app_notifications (
          id, user_id, type, category, title, message, data, priority,
          is_read, is_archived, read_at, expires_at, action_url,
          action_label, sender_id, broadcast, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        notification.id, notification.userId, notification.type, notification.category,
        notification.title, notification.message, notification.data, notification.priority,
        notification.isRead, notification.isArchived, notification.readAt, notification.expiresAt,
        notification.actionUrl, notification.actionLabel, notification.senderId,
        notification.broadcast, notification.createdAt
      ]);
    }

    console.log(`✅ Inserted ${notifications.length} notifications`);
    return notifications;

  } catch (error) {
    console.error('❌ Notifications population failed:', error.message);
    throw error;
  }
}

async function runDataPopulation() {
  console.log('🚀 Starting Enhanced Data Population for dtms_eeu_db');
  console.log('===================================================');

  const startTime = Date.now();

  try {
    await initConnection();

    // Populate data in order (respecting dependencies)
    await populateServiceCenters();
    await populateTransformers();
    await populateAlerts();
    await populateMaintenanceSchedules();
    await populateNotifications();

    // Verify population
    console.log('🔍 Verifying data population...');

    const tableNames = [
      'app_users', 'app_regions', 'app_service_centers', 'app_transformers',
      'app_alerts', 'app_maintenance_schedules', 'app_notifications'
    ];

    const counts = {};
    for (const tableName of tableNames) {
      try {
        const result = await executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`);
        counts[tableName] = result[0].count;
      } catch (error) {
        counts[tableName] = 'Error';
      }
    }

    console.log('📊 Data Population Results:');
    Object.entries(counts).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} records`);
    });

    // Calculate totals
    const totalRecords = Object.values(counts).reduce((sum, count) => {
      return sum + (typeof count === 'number' ? count : 0);
    }, 0);

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`\n⏱️  Data population completed in ${duration.toFixed(2)} seconds`);
    console.log(`📈 Total records inserted: ${totalRecords}`);
    console.log('🎉 Enhanced data population successful!');
    console.log('');
    console.log('✅ Database now contains comprehensive sample data:');
    console.log('   - 5 users with different roles and permissions');
    console.log('   - 7 Ethiopian regions with geographic data');
    console.log('   - Multiple service centers per region');
    console.log('   - 60+ transformers with realistic specifications');
    console.log('   - 200+ alerts with various severity levels');
    console.log('   - 100+ maintenance schedules and work orders');
    console.log('   - 30+ notifications for user engagement');
    console.log('');
    console.log('🔄 Ready for enhanced dashboard with full functionality!');

  } catch (error) {
    console.error('❌ Data population failed:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

runDataPopulation().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
