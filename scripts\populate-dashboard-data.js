/**
 * Populate Dashboard Data for EEU DTMS
 * This script adds sample data that will be displayed on the dashboard
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function populateDashboardData() {
  let connection;
  
  try {
    console.log('📊 POPULATING DASHBOARD DATA FOR EEU DTMS');
    console.log('=' .repeat(60));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // Clear existing data first
    console.log('\n🧹 Clearing existing data...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    await connection.execute('DELETE FROM app_alerts');
    await connection.execute('DELETE FROM app_maintenance_schedules');
    await connection.execute('DELETE FROM app_transformers');
    await connection.execute('ALTER TABLE app_transformers AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE app_maintenance_schedules AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE app_alerts AUTO_INCREMENT = 1');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    console.log('✅ Existing data cleared');

    // Add transformers with realistic Ethiopian data
    console.log('\n⚡ Adding Transformers...');
    const transformers = [
      {
        serial_number: 'EEU-AA-001',
        name: 'Bole Main Distribution Transformer',
        type: 'distribution',
        capacity_kva: 1000.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'Siemens',
        model: 'GEAFOL Cast Resin',
        year_manufactured: 2020,
        installation_date: '2020-06-15',
        location_name: 'Bole Road, Near EEU Headquarters',
        latitude: 9.02220000,
        longitude: 38.74680000,
        region_id: 1, // Addis Ababa
        status: 'operational',
        efficiency_rating: 98.50,
        load_factor: 75.00,
        temperature: 65.00,
        oil_level: 95.00,
        last_maintenance: '2024-09-15',
        next_maintenance: '2024-12-15'
      },
      {
        serial_number: 'EEU-AA-002',
        name: 'Megenagna Distribution Transformer',
        type: 'distribution',
        capacity_kva: 500.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'ABB',
        model: 'UniGear ZS1',
        year_manufactured: 2019,
        installation_date: '2019-08-20',
        location_name: 'Megenagna, Addis Ababa',
        latitude: 9.02990000,
        longitude: 38.80790000,
        region_id: 1, // Addis Ababa
        status: 'warning',
        efficiency_rating: 97.80,
        load_factor: 88.00,
        temperature: 72.00,
        oil_level: 85.00,
        last_maintenance: '2024-08-10',
        next_maintenance: '2024-11-10'
      },
      {
        serial_number: 'EEU-OR-001',
        name: 'Jimma Central Distribution Transformer',
        type: 'distribution',
        capacity_kva: 500.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'ABB',
        model: 'UniGear ZS1',
        year_manufactured: 2019,
        installation_date: '2019-08-20',
        location_name: 'Jimma City Center',
        latitude: 7.67810000,
        longitude: 36.83440000,
        region_id: 2, // Oromia
        status: 'operational',
        efficiency_rating: 97.80,
        load_factor: 68.00,
        temperature: 62.00,
        oil_level: 92.00,
        last_maintenance: '2024-10-05',
        next_maintenance: '2025-01-05'
      },
      {
        serial_number: 'EEU-AM-001',
        name: 'Bahir Dar Power Distribution Transformer',
        type: 'power',
        capacity_kva: 2000.00,
        voltage_primary: 132.00,
        voltage_secondary: 33.00,
        manufacturer: 'Schneider Electric',
        model: 'Trihal',
        year_manufactured: 2021,
        installation_date: '2021-03-10',
        location_name: 'Bahir Dar Industrial Zone',
        latitude: 11.59590000,
        longitude: 37.39060000,
        region_id: 3, // Amhara
        status: 'maintenance',
        efficiency_rating: 99.20,
        load_factor: 45.00,
        temperature: 58.00,
        oil_level: 98.00,
        last_maintenance: '2024-11-20',
        next_maintenance: '2024-12-20'
      },
      {
        serial_number: 'EEU-TI-001',
        name: 'Mekelle Central Transformer',
        type: 'distribution',
        capacity_kva: 630.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'ABB',
        model: 'UniGear ZS1',
        year_manufactured: 2017,
        installation_date: '2017-05-08',
        location_name: 'Mekelle City Center',
        latitude: 13.49670000,
        longitude: 39.47530000,
        region_id: 4, // Tigray
        status: 'critical',
        efficiency_rating: 94.20,
        load_factor: 95.00,
        temperature: 85.00,
        oil_level: 65.00,
        last_maintenance: '2024-07-15',
        next_maintenance: '2024-12-10'
      },
      {
        serial_number: 'EEU-SN-001',
        name: 'Hawassa Distribution Transformer',
        type: 'distribution',
        capacity_kva: 400.00,
        voltage_primary: 33.00,
        voltage_secondary: 0.40,
        manufacturer: 'Schneider Electric',
        model: 'Trihal',
        year_manufactured: 2020,
        installation_date: '2020-09-15',
        location_name: 'Hawassa City Center',
        latitude: 7.06210000,
        longitude: 38.47760000,
        region_id: 5, // SNNP
        status: 'operational',
        efficiency_rating: 98.00,
        load_factor: 65.00,
        temperature: 60.00,
        oil_level: 90.00,
        last_maintenance: '2024-09-01',
        next_maintenance: '2024-12-01'
      }
    ];

    for (const transformer of transformers) {
      await connection.execute(`
        INSERT INTO app_transformers (
          serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
          manufacturer, model, year_manufactured, installation_date, location_name,
          latitude, longitude, region_id, status, efficiency_rating, load_factor,
          temperature, oil_level, last_maintenance, next_maintenance
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        transformer.serial_number, transformer.name, transformer.type, transformer.capacity_kva,
        transformer.voltage_primary, transformer.voltage_secondary, transformer.manufacturer,
        transformer.model, transformer.year_manufactured, transformer.installation_date,
        transformer.location_name, transformer.latitude, transformer.longitude,
        transformer.region_id, transformer.status, transformer.efficiency_rating,
        transformer.load_factor, transformer.temperature, transformer.oil_level,
        transformer.last_maintenance, transformer.next_maintenance
      ]);
    }
    console.log(`✅ Added ${transformers.length} transformers`);

    // Add maintenance schedules
    console.log('\n🔧 Adding Maintenance Schedules...');
    const maintenanceSchedules = [
      {
        transformer_id: 1,
        type: 'routine',
        title: 'Monthly Visual Inspection - Bole Main',
        description: 'Regular monthly visual inspection and basic checks including oil level, temperature readings, and connection integrity.',
        scheduled_date: '2024-12-30',
        estimated_duration: 2,
        priority: 'medium',
        status: 'scheduled'
      },
      {
        transformer_id: 2,
        type: 'preventive',
        title: 'Quarterly Maintenance - Megenagna',
        description: 'Comprehensive quarterly electrical testing including insulation resistance, turns ratio, and oil analysis.',
        scheduled_date: '2024-12-25',
        estimated_duration: 8,
        priority: 'high',
        status: 'in_progress'
      },
      {
        transformer_id: 3,
        type: 'routine',
        title: 'Monthly Inspection - Jimma Central',
        description: 'Monthly routine inspection and cleaning of transformer and surrounding area.',
        scheduled_date: '2024-12-20',
        estimated_duration: 2,
        priority: 'low',
        status: 'completed'
      },
      {
        transformer_id: 4,
        type: 'corrective',
        title: 'Oil Level Restoration - Bahir Dar',
        description: 'Restore oil level and investigate potential leak sources.',
        scheduled_date: '2024-12-15',
        estimated_duration: 4,
        priority: 'high',
        status: 'scheduled'
      },
      {
        transformer_id: 5,
        type: 'emergency',
        title: 'Critical Temperature Issue - Mekelle',
        description: 'Emergency response to critical temperature alert and cooling system inspection.',
        scheduled_date: '2024-12-10',
        estimated_duration: 6,
        priority: 'critical',
        status: 'completed'
      },
      {
        transformer_id: 6,
        type: 'preventive',
        title: 'Annual Comprehensive Maintenance - Hawassa',
        description: 'Complete annual maintenance including oil change, gasket replacement, and full electrical testing.',
        scheduled_date: '2025-01-15',
        estimated_duration: 24,
        priority: 'high',
        status: 'scheduled'
      }
    ];

    for (const schedule of maintenanceSchedules) {
      await connection.execute(`
        INSERT INTO app_maintenance_schedules (
          transformer_id, type, title, description, scheduled_date, estimated_duration, priority, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        schedule.transformer_id, schedule.type, schedule.title, schedule.description,
        schedule.scheduled_date, schedule.estimated_duration, schedule.priority, schedule.status
      ]);
    }
    console.log(`✅ Added ${maintenanceSchedules.length} maintenance schedules`);

    // Add alerts
    console.log('\n🚨 Adding Alerts...');
    const alerts = [
      {
        transformer_id: 2,
        title: 'High Temperature Alert',
        description: 'Transformer temperature has exceeded 70°C threshold. Immediate inspection recommended to prevent equipment damage.',
        severity: 'high',
        type: 'temperature',
        priority: 'high',
        status: 'active',
        is_resolved: false
      },
      {
        transformer_id: 2,
        title: 'Overload Warning',
        description: 'Transformer is operating at 88% of capacity, exceeding the recommended 85% threshold for sustained operation.',
        severity: 'medium',
        type: 'load',
        priority: 'medium',
        status: 'active',
        is_resolved: false
      },
      {
        transformer_id: 5,
        title: 'Critical Temperature Alert',
        description: 'Transformer temperature reached critical level of 85°C. Emergency maintenance completed successfully.',
        severity: 'critical',
        type: 'temperature',
        priority: 'critical',
        status: 'resolved',
        is_resolved: true,
        resolved_at: '2024-12-10 14:30:00'
      },
      {
        transformer_id: 5,
        title: 'Low Oil Level Warning',
        description: 'Transformer oil level has dropped to 65%, below the recommended minimum of 80%. Schedule oil top-up.',
        severity: 'medium',
        type: 'maintenance',
        priority: 'medium',
        status: 'active',
        is_resolved: false
      },
      {
        transformer_id: 1,
        title: 'Routine Maintenance Due',
        description: 'Scheduled monthly maintenance is approaching. Please coordinate with maintenance team.',
        severity: 'low',
        type: 'maintenance',
        priority: 'low',
        status: 'active',
        is_resolved: false
      },
      {
        transformer_id: 6,
        title: 'Communication Test Alert',
        description: 'Testing alert system for critical infrastructure. System functioning normally.',
        severity: 'low',
        type: 'communication',
        priority: 'low',
        status: 'resolved',
        is_resolved: true,
        resolved_at: '2024-12-01 10:00:00'
      }
    ];

    for (const alert of alerts) {
      await connection.execute(`
        INSERT INTO app_alerts (
          transformer_id, title, description, severity, type, priority, status, is_resolved, resolved_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        alert.transformer_id, alert.title, alert.description, alert.severity,
        alert.type, alert.priority, alert.status, alert.is_resolved,
        alert.resolved_at || null
      ]);
    }
    console.log(`✅ Added ${alerts.length} alerts`);

    // Verify data was added
    const [transformerCount] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    const [maintenanceCount] = await connection.execute('SELECT COUNT(*) as count FROM app_maintenance_schedules');
    const [alertCount] = await connection.execute('SELECT COUNT(*) as count FROM app_alerts');
    const [regionCount] = await connection.execute('SELECT COUNT(*) as count FROM app_regions');

    console.log('\n' + '=' .repeat(60));
    console.log('🎉 DASHBOARD DATA POPULATION COMPLETED!');
    console.log('=' .repeat(60));
    
    console.log('\n📊 FINAL DATABASE SUMMARY:');
    console.log(`  ⚡ Transformers: ${transformerCount[0].count}`);
    console.log(`  🔧 Maintenance Schedules: ${maintenanceCount[0].count}`);
    console.log(`  🚨 Alerts: ${alertCount[0].count}`);
    console.log(`  🗺️  Regions: ${regionCount[0].count}`);
    
    // Show status distribution
    const [statusStats] = await connection.execute(`
      SELECT 
        status,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM app_transformers), 1) as percentage
      FROM app_transformers 
      GROUP BY status 
      ORDER BY count DESC
    `);
    
    console.log('\n📈 TRANSFORMER STATUS DISTRIBUTION:');
    statusStats.forEach(stat => {
      console.log(`  • ${stat.status}: ${stat.count} (${stat.percentage}%)`);
    });
    
    // Show alert summary
    const [alertStats] = await connection.execute(`
      SELECT 
        severity,
        COUNT(*) as total,
        SUM(CASE WHEN is_resolved = 0 OR is_resolved IS NULL THEN 1 ELSE 0 END) as active
      FROM app_alerts 
      GROUP BY severity 
      ORDER BY 
        CASE severity 
          WHEN 'critical' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'medium' THEN 3 
          WHEN 'low' THEN 4 
        END
    `);
    
    console.log('\n🚨 ALERT SUMMARY BY SEVERITY:');
    alertStats.forEach(stat => {
      console.log(`  • ${stat.severity}: ${stat.active} active / ${stat.total} total`);
    });
    
    console.log('\n🎯 DASHBOARD NOW DISPLAYS:');
    console.log('  • Real transformer data from 6 locations across Ethiopia');
    console.log('  • Live maintenance schedules with various statuses');
    console.log('  • Active and resolved alerts with different severities');
    console.log('  • Performance metrics and operational statistics');
    console.log('  • Regional distribution across Ethiopian states');
    
    console.log('\n🌟 Your dashboard is now populated with realistic data!');
    console.log('🔗 Refresh your browser at: http://localhost:3002');
    
  } catch (error) {
    console.error('❌ Error populating dashboard data:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { populateDashboardData };

// Run if called directly
if (require.main === module) {
  populateDashboardData();
}
