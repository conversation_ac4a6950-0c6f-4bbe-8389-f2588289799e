// Create views to map original table names to workaround tables
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

const views = [
  {
    name: 'app_regions',
    sql: 'CREATE OR REPLACE VIEW app_regions AS SELECT * FROM app_regions2'
  },
  {
    name: 'app_service_centers',
    sql: 'CREATE OR REPLACE VIEW app_service_centers AS SELECT * FROM app_service_centers2'
  },
  {
    name: 'app_transformers',
    sql: 'CREATE OR REPLACE VIEW app_transformers AS SELECT * FROM app_transformers2'
  }
];

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    for (const v of views) {
      await connection.execute(v.sql);
      console.log(`✅ Created view: ${v.name}`);
    }
  } catch (err) {
    console.error('❌ Error creating views:', err);
  } finally {
    await connection.end();
  }
})();
