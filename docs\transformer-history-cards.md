# Transformer History Cards Documentation

## Overview

The Transformer History Cards feature provides comprehensive record-keeping and management for Ethiopian Electric Utility distribution transformers. This system allows users to create, view, edit, and manage detailed history cards for each transformer, including inspection records and megger test results.

## Features

### 1. History Card Management
- **Create New History Cards**: Generate new history cards for transformers
- **View Existing Cards**: Browse and search through existing history cards
- **Edit Records**: Update transformer information and maintenance records
- **Export Data**: Export history cards in JSON format for backup or analysis

### 2. Comprehensive Data Tracking
- **Basic Information**: Card number, substation, feeder, transformer specifications
- **Location Data**: GPS coordinates, region, district, specific location
- **Technical Specifications**: KVA rating, voltage levels, manufacturer details
- **Installation Details**: Installation date, responsible personnel, customer information

### 3. Inspection Records
- **Component Status**: Track status of arrestors, fuses, bushings, grounding
- **Oil Monitoring**: Oil level, leakage status, temperature monitoring
- **Load Management**: Transformer load percentage tracking
- **Inspector Information**: Inspector names, signatures, dates

### 4. Megger Test Results
- **Electrical Testing**: Insulation resistance measurements
- **Test Values**: HT to ground, LT to ground, ohmic values
- **Oil Analysis**: Oil insulation condition assessment
- **Approval Chain**: Inspected by, checked by, approved by workflow

## Navigation

### Sidebar Access
The History Cards functionality is accessible through the main sidebar:
```
Transformers → History Cards
```

### Additional Pages
- **Inspection**: `/transformers/inspection` - Dedicated inspection management
- **Megger Tests**: `/transformers/megger-tests` - Electrical testing interface

## Database Schema

### Main Tables

#### transformer_history_cards
- Primary table storing main history card information
- Links to transformer records via transformer_id
- Stores basic transformer and location data

#### transformer_inspections
- Stores inspection records linked to history cards
- Tracks component status and inspector information
- Maintains inspection dates and findings

#### transformer_megger_tests
- Electrical test results and measurements
- Insulation resistance values and test conditions
- Approval workflow tracking

#### transformer_inspection_components
- Detailed component-level inspection data
- Defect tracking and quantity management
- Component-specific notes and observations

#### transformer_history_attachments
- File attachments (photos, documents, reports)
- Supports multiple file types and descriptions
- Links to specific history cards

## API Endpoints

### GET /api/mysql/transformer-history-cards
- Retrieve history cards for specific transformers
- Query parameters: `transformerId`, `cardId`
- Returns enriched data with inspections and tests

### POST /api/mysql/transformer-history-cards
- Create new history card records
- Accepts complete history card data including inspections
- Returns success status and new record ID

### PUT /api/mysql/transformer-history-cards
- Update existing history card information
- Requires history card ID in request body
- Supports partial updates

### DELETE /api/mysql/transformer-history-cards
- Remove history card records
- Query parameter: `id`
- Cascading deletes for related records

## User Interface

### History Cards List Page
- **Statistics Dashboard**: Overview of total transformers, operational status, maintenance due
- **Search and Filters**: Search by transformer details, filter by status and region
- **Action Buttons**: Open history card, view details, export data
- **Responsive Design**: Works on desktop and mobile devices

### History Card Form
- **Tabbed Interface**: Organized into Main Record, Inspection, and Test Results tabs
- **Form Validation**: Real-time validation with error messages
- **Auto-save**: Automatic saving with loading states
- **GPS Integration**: GPS coordinate validation and formatting

### Inspection Interface
- **Component Tracking**: Visual status indicators for transformer components
- **Health Index**: Graphical health percentage display
- **Inspection Scheduling**: Due date tracking and overdue alerts
- **Inspector Management**: Inspector assignment and signature capture

### Megger Test Interface
- **Test Result Entry**: Structured form for electrical test values
- **Pass/Fail Status**: Automatic result classification
- **Approval Workflow**: Multi-level approval process
- **Test History**: Historical test result tracking

## Validation Rules

### Required Fields
- Card Number (unique identifier)
- Substation Name
- Transformer Code
- KVA Rating (must be numeric)

### GPS Coordinates
- Format: latitude,longitude (e.g., 9.0192,38.7525)
- Validation: Regex pattern for coordinate format
- Optional field with format guidance

### Date Fields
- Installation Date, Changing Date, Delivery Date
- ISO date format (YYYY-MM-DD)
- Optional fields with date picker interface

## Security and Permissions

### Role-Based Access
- **Super Admin**: Full access to all features
- **Asset Managers**: Create and edit history cards
- **Maintenance Engineers**: View and update inspection records
- **Field Technicians**: Add inspection and test data
- **Service Center Managers**: Regional data access

### Data Protection
- Input sanitization and validation
- SQL injection prevention
- XSS protection in form inputs
- Secure file upload handling

## Installation and Setup

### Database Migration
```bash
node scripts/run-migration.js
```

### Required Dependencies
- MySQL 8.0+
- Node.js 18+
- React 18+
- Next.js 14+

### Environment Variables
```env
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=eeu_dtms
MYSQL_PORT=3306
```

## Usage Examples

### Creating a New History Card
1. Navigate to Transformers → History Cards
2. Click on a transformer's "Open History Card" action
3. Fill in the required fields (marked with red asterisk)
4. Add inspection records and test results as needed
5. Click "Save History Card" to store the data

### Viewing Inspection Status
1. Go to Transformers → Inspection
2. Use filters to find specific transformers
3. View health index and inspection status
4. Click "Inspect" to add new inspection records

### Running Megger Tests
1. Access Transformers → Megger Tests
2. Filter by test status (due, overdue, completed)
3. Click "Test" to enter new test results
4. Follow the approval workflow for test validation

## Troubleshooting

### Common Issues

#### Database Connection Errors
- Verify MySQL service is running
- Check environment variables
- Ensure database exists and is accessible

#### Form Validation Errors
- Check required fields are filled
- Verify GPS coordinate format
- Ensure numeric fields contain valid numbers

#### Save Operation Failures
- Check network connectivity
- Verify user permissions
- Review browser console for detailed errors

### Error Messages
- **"Card number is required"**: Fill in the card number field
- **"Invalid GPS format"**: Use latitude,longitude format
- **"Must be a valid number"**: Enter numeric values only
- **"Failed to save history card"**: Check data and try again

## Future Enhancements

### Planned Features
- **Photo Attachments**: Image upload for transformer conditions
- **QR Code Generation**: QR codes for quick transformer identification
- **Mobile App**: Dedicated mobile application for field technicians
- **Automated Reporting**: Scheduled maintenance and inspection reports
- **Integration APIs**: Third-party system integration capabilities

### Performance Optimizations
- **Database Indexing**: Optimized queries for large datasets
- **Caching Layer**: Redis caching for frequently accessed data
- **Pagination**: Efficient data loading for large transformer inventories
- **Background Processing**: Async operations for heavy computations

## Support and Maintenance

### Regular Maintenance
- Database backup and recovery procedures
- Performance monitoring and optimization
- Security updates and patches
- User training and documentation updates

### Contact Information
- **Technical Support**: EEU IT Department
- **System Administrator**: Worku Mesafint
- **Documentation**: Available in `/docs` directory
- **Issue Tracking**: GitHub Issues or internal ticketing system
