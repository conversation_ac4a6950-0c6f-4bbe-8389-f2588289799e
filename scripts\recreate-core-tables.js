// Recreate app_regions and app_service_centers tables
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

const createRegions = `CREATE TABLE IF NOT EXISTS app_regions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(10) NOT NULL UNIQUE,
  population INT,
  area_km2 DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);`;

const createServiceCenters = `CREATE TABLE IF NOT EXISTS app_service_centers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) NOT NULL UNIQUE,
  region_id INT,
  address TEXT,
  phone VARCHAR(20),
  email VARCHAR(100),
  manager_name VA<PERSON><PERSON><PERSON>(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (region_id) REFERENCES app_regions(id)
);`;

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    await connection.execute(createRegions);
    console.log('✅ Created app_regions table');
    await connection.execute(createServiceCenters);
    console.log('✅ Created app_service_centers table');
  } catch (err) {
    console.error('❌ Error creating tables:', err);
  } finally {
    await connection.end();
  }
})();
