"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import {
  Bell,
  Filter,
  CheckCircle,
  Download,
  Plus,
  Settings,
  RefreshCw,
  Calendar,
  MapPin,
  AlertTriangle,
  Thermometer,
  Battery,
  Zap,
  AlertCircle
} from "lucide-react"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { RecentAlerts } from "@/components/recent-alerts"
import { AlertsTable } from "@/components/alerts-table"
import { ReportAlertDialog } from "@/components/report-alert-dialog"
import { AlertConfigDialog } from "@/components/alert-config-dialog"
import { AlertProvider, use<PERSON><PERSON>ts } from "@/src/contexts/alert-context"
import { Badge } from "@/src/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover"
import { Calendar as CalendarComponent } from "@/src/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/src/lib/utils"

function AlertsContentInner() {
  const {
    updateFilter,
    filters,
    resetFilters,
    alertCounts,
    activeTab,
    setActiveTab,
    markAllAsRead,
    exportAlerts,
    configureAlertSettings
  } = useAlerts()

  const [isReportAlertOpen, setIsReportAlertOpen] = useState(false)
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false)

  // Mock current user ID
  const currentUserId = "Current User"

  const handleExport = (format: "csv" | "pdf" | "excel") => {
    exportAlerts(format)
  }

  const handleMarkAllAsRead = () => {
    markAllAsRead(currentUserId)
  }

  const handleConfigureAlerts = () => {
    setIsConfigDialogOpen(true)
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Alert Management</h1>
        <div className="flex flex-wrap items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleConfigureAlerts}>
            <Settings className="mr-2 h-4 w-4" />
            Configure Alerts
          </Button>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48 p-2">
              <div className="flex flex-col gap-2">
                <Button variant="ghost" size="sm" onClick={() => handleExport("csv")}>
                  Export as CSV
                </Button>
                <Button variant="ghost" size="sm" onClick={() => handleExport("excel")}>
                  Export as Excel
                </Button>
                <Button variant="ghost" size="sm" onClick={() => handleExport("pdf")}>
                  Export as PDF
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          <Button size="sm" onClick={() => setIsReportAlertOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Report Alert
          </Button>

          <Button size="sm" onClick={handleMarkAllAsRead}>
            <CheckCircle className="mr-2 h-4 w-4" />
            Mark All as Read
          </Button>

          <Button variant="outline" size="sm" onClick={resetFilters}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset Filters
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-transparent rounded-lg"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
            <CardTitle className="text-sm font-medium">Critical Alerts</CardTitle>
            <div className="rounded-full bg-red-100 p-1 dark:bg-red-900/20">
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-2xl font-bold">{alertCounts.critical}</div>
            <p className="text-xs text-muted-foreground">Requires immediate attention</p>
            {alertCounts.critical > 0 && (
              <Button variant="link" size="sm" className="p-0 h-auto mt-2 text-red-600" onClick={() => {
                updateFilter('severity', 'critical')
                setActiveTab('active')
              }}>
                View critical alerts
              </Button>
            )}
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-transparent rounded-lg"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
            <CardTitle className="text-sm font-medium">High Priority</CardTitle>
            <div className="rounded-full bg-orange-100 p-1 dark:bg-orange-900/20">
              <Thermometer className="h-4 w-4 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-2xl font-bold">{alertCounts.high}</div>
            <p className="text-xs text-muted-foreground">Requires attention soon</p>
            {alertCounts.high > 0 && (
              <Button variant="link" size="sm" className="p-0 h-auto mt-2 text-orange-600" onClick={() => {
                updateFilter('severity', 'high')
                setActiveTab('active')
              }}>
                View high priority alerts
              </Button>
            )}
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-transparent rounded-lg"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
            <CardTitle className="text-sm font-medium">Medium Priority</CardTitle>
            <div className="rounded-full bg-yellow-100 p-1 dark:bg-yellow-900/20">
              <Battery className="h-4 w-4 text-yellow-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-2xl font-bold">{alertCounts.medium}</div>
            <p className="text-xs text-muted-foreground">Requires monitoring</p>
            {alertCounts.medium > 0 && (
              <Button variant="link" size="sm" className="p-0 h-auto mt-2 text-yellow-600" onClick={() => {
                updateFilter('severity', 'medium')
                setActiveTab('active')
              }}>
                View medium priority alerts
              </Button>
            )}
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-transparent rounded-lg"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
            <CardTitle className="text-sm font-medium">Low Priority</CardTitle>
            <div className="rounded-full bg-blue-100 p-1 dark:bg-blue-900/20">
              <Zap className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-2xl font-bold">{alertCounts.low}</div>
            <p className="text-xs text-muted-foreground">For information only</p>
            {alertCounts.low > 0 && (
              <Button variant="link" size="sm" className="p-0 h-auto mt-2 text-blue-600" onClick={() => {
                updateFilter('severity', 'low')
                setActiveTab('active')
              }}>
                View low priority alerts
              </Button>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="active" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Active Alerts
            <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
              {alertCounts.active + alertCounts.acknowledged + alertCounts.inProgress}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="resolved" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Resolved
            <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
              {alertCounts.resolved}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="all" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            All Alerts
            <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
              {alertCounts.total}
            </Badge>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div>
                  <CardTitle>Active Alerts</CardTitle>
                  <CardDescription>Unresolved alerts requiring attention</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1 border-red-500 text-red-500">
                    <AlertTriangle className="h-3 w-3" />
                    <span>Active: {alertCounts.active}</span>
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1 border-blue-500 text-blue-500">
                    <CheckCircle className="h-3 w-3" />
                    <span>Acknowledged: {alertCounts.acknowledged}</span>
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1 border-yellow-500 text-yellow-500">
                    <RefreshCw className="h-3 w-3" />
                    <span>In Progress: {alertCounts.inProgress}</span>
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-4">
                <div className="relative flex-1">
                  <Input
                    type="search"
                    placeholder="Search alerts by ID, transformer, location, or message..."
                    value={filters.search}
                    onChange={(e) => updateFilter('search', e.target.value)}
                  />
                </div>
                <div className="flex flex-wrap gap-2">
                  <Select value={filters.severity} onValueChange={(value) => updateFilter('severity', value)}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Severity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Severities</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filters.type} onValueChange={(value) => updateFilter('type', value)}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="temperature">Temperature</SelectItem>
                      <SelectItem value="oil level">Oil Level</SelectItem>
                      <SelectItem value="load">Load</SelectItem>
                      <SelectItem value="connection">Connection</SelectItem>
                      <SelectItem value="voltage">Voltage</SelectItem>
                      <SelectItem value="physical">Physical</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filters.status} onValueChange={(value) => updateFilter('status', value)}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="acknowledged">Acknowledged</SelectItem>
                      <SelectItem value="in progress">In Progress</SelectItem>
                    </SelectContent>
                  </Select>

                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[160px] justify-start">
                        <Calendar className="mr-2 h-4 w-4" />
                        <span>Date Range</span>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="range"
                        selected={{
                          from: filters.dateRange.from,
                          to: filters.dateRange.to
                        }}
                        onSelect={(range) => updateFilter('dateRange', range || {})}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>

                  <Select value={filters.location} onValueChange={(value) => updateFilter('location', value)}>
                    <SelectTrigger className="w-[160px]">
                      <MapPin className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Locations</SelectItem>
                      <SelectItem value="West District">West District</SelectItem>
                      <SelectItem value="Central Area">Central Area</SelectItem>
                      <SelectItem value="East District">East District</SelectItem>
                      <SelectItem value="North Substation">North Substation</SelectItem>
                      <SelectItem value="South Region">South Region</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <RecentAlerts extended />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="resolved" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div>
                  <CardTitle>Resolved Alerts</CardTitle>
                  <CardDescription>Previously resolved alerts</CardDescription>
                </div>
                <Badge variant="outline" className="flex items-center gap-1 border-green-500 text-green-500 w-fit">
                  <CheckCircle className="h-3 w-3" />
                  <span>Resolved: {alertCounts.resolved}</span>
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-4">
                <div className="relative flex-1">
                  <Input
                    type="search"
                    placeholder="Search resolved alerts..."
                    value={filters.search}
                    onChange={(e) => updateFilter('search', e.target.value)}
                  />
                </div>
              </div>
              <AlertsTable resolved />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div>
                  <CardTitle>All Alerts</CardTitle>
                  <CardDescription>Complete history of all alerts</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1 border-red-500 text-red-500">
                    <AlertTriangle className="h-3 w-3" />
                    <span>Active: {alertCounts.active}</span>
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1 border-green-500 text-green-500">
                    <CheckCircle className="h-3 w-3" />
                    <span>Resolved: {alertCounts.resolved}</span>
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-4">
                <div className="relative flex-1">
                  <Input
                    type="search"
                    placeholder="Search all alerts..."
                    value={filters.search}
                    onChange={(e) => updateFilter('search', e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Select value={filters.severity} onValueChange={(value) => updateFilter('severity', value)}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Severity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Severities</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filters.status} onValueChange={(value) => updateFilter('status', value)}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="acknowledged">Acknowledged</SelectItem>
                      <SelectItem value="in progress">In Progress</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <AlertsTable />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <ReportAlertDialog open={isReportAlertOpen} onOpenChange={setIsReportAlertOpen} />
      <AlertConfigDialog open={isConfigDialogOpen} onOpenChange={setIsConfigDialogOpen} />
    </div>
  )
}

export function AlertsContent() {
  return (
    <AlertProvider>
      <AlertsContentInner />
    </AlertProvider>
  )
}
