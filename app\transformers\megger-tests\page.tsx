"use client"

import { useState, useEffect } from 'react'
import { MainLayout } from "@/src/components/layout/main-layout"
import { TransformerHistoryCard } from '@/components/transformers/TransformerHistoryCard'
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import {
  Search,
  Filter,
  Plus,
  Zap,
  Download,
  RefreshCw,
  Eye,
  Calendar,
  MapPin,
  AlertTriangle,
  CheckCircle,
  TestTube,
  Activity
} from 'lucide-react'

export default function TransformerMeggerTestsPage() {
  const [transformers, setTransformers] = useState<any[]>([])
  const [filteredTransformers, setFilteredTransformers] = useState<any[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [regionFilter, setRegionFilter] = useState('all')
  const [isLoading, setIsLoading] = useState(true)
  const [showHistoryCard, setShowHistoryCard] = useState(false)
  const [selectedTransformerId, setSelectedTransformerId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('test-results')

  // Fetch transformers data
  const fetchTransformers = async () => {
    setIsLoading(true)
    try {
      console.log('🔄 Fetching transformers for megger tests...')
      const response = await fetch('/api/mysql/transformers', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('✅ Transformers fetched successfully:', data.transformers?.length || 0)

      const transformedData = data.transformers?.map((transformer: any) => ({
        id: transformer.id,
        serialNumber: transformer.serial_number || transformer.serialNumber,
        manufacturer: transformer.manufacturer,
        model: transformer.model,
        type: transformer.type,
        ratingKVA: transformer.capacity || transformer.ratingKVA,
        status: transformer.status,
        location: {
          region: transformer.region_name || transformer.location?.region || 'Addis Ababa',
          serviceCenter: transformer.service_center_name || transformer.location?.serviceCenter || 'Central',
          address: transformer.location_address || transformer.location?.address || 'Addis Ababa, Ethiopia'
        },
        lastTest: transformer.last_test_date || '2024-01-10',
        nextTest: transformer.next_test_date || '2024-07-10',
        testStatus: transformer.test_status || 'due',
        insulationResistance: transformer.insulation_resistance || '500 MΩ',
        testResult: transformer.test_result || 'pass'
      })) || []

      setTransformers(transformedData)
      setFilteredTransformers(transformedData)
    } catch (error) {
      console.error('❌ Error fetching transformers:', error)
      setTransformers([])
      setFilteredTransformers([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchTransformers()
  }, [])

  // Apply filters and search
  useEffect(() => {
    let filtered = [...transformers]

    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(transformer =>
        transformer.serialNumber.toLowerCase().includes(query) ||
        transformer.manufacturer.toLowerCase().includes(query) ||
        transformer.location.address.toLowerCase().includes(query)
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(transformer => transformer.testStatus === statusFilter)
    }

    if (regionFilter !== 'all') {
      filtered = filtered.filter(transformer => transformer.location.region === regionFilter)
    }

    setFilteredTransformers(filtered)
  }, [transformers, searchQuery, statusFilter, regionFilter])

  const handleOpenTestForm = (transformerId: string) => {
    setSelectedTransformerId(transformerId)
    setActiveTab('test-results')
    setShowHistoryCard(true)
  }

  const handleCloseHistoryCard = () => {
    setShowHistoryCard(false)
    setSelectedTransformerId(null)
  }

  const getTestStatusBadge = (status: string) => {
    switch (status) {
      case 'pass':
        return <Badge className="bg-green-100 text-green-800">Pass</Badge>
      case 'fail':
        return <Badge className="bg-red-100 text-red-800">Fail</Badge>
      case 'due':
        return <Badge className="bg-yellow-100 text-yellow-800">Due</Badge>
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800">Overdue</Badge>
      case 'in-progress':
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>
    }
  }

  const getTestResultBadge = (result: string) => {
    switch (result) {
      case 'pass':
        return <Badge className="bg-green-100 text-green-800">✓ Pass</Badge>
      case 'fail':
        return <Badge className="bg-red-100 text-red-800">✗ Fail</Badge>
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800">⚠ Warning</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">Pending</Badge>
    }
  }

  if (isLoading) {
    return (
      <MainLayout
        allowedRoles={[
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician"
        ]}
        requiredPermissions={[{ resource: "transformers", action: "read" }]}
      >
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "national_maintenance_manager",
        "regional_admin",
        "regional_asset_manager",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician"
      ]}
      requiredPermissions={[{ resource: "transformers", action: "read" }]}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Transformer Megger Tests</h1>
            <p className="text-muted-foreground">
              Electrical testing and insulation resistance measurements for Ethiopian Electric Utility transformers
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button className="bg-green-600 hover:bg-green-700">
              <Plus className="h-4 w-4 mr-2" />
              New Test
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Results
            </Button>
            <Button variant="outline" onClick={fetchTransformers}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Transformers</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredTransformers.length}</div>
              <p className="text-xs text-muted-foreground">
                Requiring testing
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tests Due</CardTitle>
              <Calendar className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {filteredTransformers.filter(t => t.testStatus === 'due').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Scheduled tests
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed Tests</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {filteredTransformers.filter(t => t.testResult === 'fail').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Passed Tests</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {filteredTransformers.filter(t => t.testResult === 'pass').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Healthy transformers
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg border p-4 mb-6">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search transformers..."
                  className="pl-9 pr-4 py-2 border rounded-md w-64 focus:outline-none focus:ring-1 focus:ring-green-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search size={16} className="absolute left-3 top-3 text-gray-400" />
              </div>

              <select
                className="border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="due">Due</option>
                <option value="overdue">Overdue</option>
                <option value="pass">Passed</option>
                <option value="fail">Failed</option>
                <option value="in-progress">In Progress</option>
              </select>

              <select
                className="border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                value={regionFilter}
                onChange={(e) => setRegionFilter(e.target.value)}
              >
                <option value="all">All Regions</option>
                <option value="Addis Ababa">Addis Ababa</option>
                <option value="Oromia">Oromia</option>
                <option value="Amhara">Amhara</option>
                <option value="Tigray">Tigray</option>
                <option value="SNNP">SNNP</option>
              </select>
            </div>
          </div>
        </div>

        {/* Transformers List */}
        <Card>
          <CardHeader>
            <CardTitle>Transformer Megger Test Schedule</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transformer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Test Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Result
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Insulation Resistance
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Test
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredTransformers.map((transformer) => (
                    <tr key={transformer.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{transformer.serialNumber}</div>
                        <div className="text-xs text-gray-500">{transformer.manufacturer} - {transformer.ratingKVA} kVA</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{transformer.location.region}</div>
                        <div className="text-xs text-gray-500">{transformer.location.serviceCenter}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getTestStatusBadge(transformer.testStatus)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getTestResultBadge(transformer.testResult)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transformer.insulationResistance}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transformer.lastTest}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <Button
                            size="sm"
                            onClick={() => handleOpenTestForm(transformer.id)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <TestTube className="h-4 w-4 mr-1" />
                            Test
                          </Button>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredTransformers.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <TestTube className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No transformers found matching your filters</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Test Form Dialog */}
        {showHistoryCard && selectedTransformerId && (
          <TransformerHistoryCard
            transformerId={selectedTransformerId}
            onClose={handleCloseHistoryCard}
          />
        )}
      </div>
    </MainLayout>
  )
}
