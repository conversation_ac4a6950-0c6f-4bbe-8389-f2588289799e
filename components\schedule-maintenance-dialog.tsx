"use client"

import type React from "react"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Textarea } from "@/src/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useToast } from "@/src/components/ui/use-toast"
import { CalendarIcon, Clock } from "lucide-react"
import { Calendar } from "@/src/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/src/lib/utils"
import type { Transformer } from "@/src/types/transformer"
import { Checkbox } from "@/src/components/ui/checkbox"
import { useMaintenance } from "@/src/contexts/maintenance-context"
import { useTransformers } from "@/src/contexts/transformer-context"

interface ScheduleMaintenanceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  transformer?: Transformer
  onScheduled?: () => void
  preselectedTransformerId?: string
}

export function ScheduleMaintenanceDialog({
  open,
  onOpenChange,
  transformer,
  onScheduled,
  preselectedTransformerId
}: ScheduleMaintenanceDialogProps) {
  const [maintenanceType, setMaintenanceType] = useState("")
  const [priority, setPriority] = useState<"high" | "medium" | "low">("medium")
  const [description, setDescription] = useState("")
  const [scheduledDate, setScheduledDate] = useState<Date | undefined>(new Date())
  const [estimatedDuration, setEstimatedDuration] = useState("")
  const [durationUnit, setDurationUnit] = useState("hours")
  const [assignedTo, setAssignedTo] = useState("")
  const [notifyCustomers, setNotifyCustomers] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [transformerId, setTransformerId] = useState("")
  const [transformerSerialNumber, setTransformerSerialNumber] = useState("")
  const [location, setLocation] = useState("")

  // Get contexts
  const { toast } = useToast()
  const { scheduleNewMaintenance } = useMaintenance()
  const { transformers, getTransformerById } = useTransformers()

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setMaintenanceType("")
      setPriority("medium")
      setDescription("")
      setScheduledDate(new Date())
      setEstimatedDuration("")
      setDurationUnit("hours")
      setAssignedTo("")
      setNotifyCustomers(false)

      // Set transformer details if provided
      if (transformer) {
        setTransformerId(transformer.id)
        setTransformerSerialNumber(transformer.serialNumber)
        setLocation(transformer.location?.region || "")
      } else if (preselectedTransformerId) {
        // Load transformer details from context if ID is provided
        const loadTransformer = async () => {
          try {
            const transformerData = await getTransformerById(preselectedTransformerId)
            if (transformerData) {
              setTransformerId(transformerData.id)
              setTransformerSerialNumber(transformerData.serialNumber)
              setLocation(transformerData.location?.region || "")
            }
          } catch (error) {
            console.error("Error loading transformer details:", error)
          }
        }

        loadTransformer()
      } else {
        setTransformerId("")
        setTransformerSerialNumber("")
        setLocation("")
      }
    }
  }, [open, transformer, preselectedTransformerId, getTransformerById])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Format the maintenance type for display
      const formattedType = maintenanceType
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      // Calculate duration string
      const duration = `${estimatedDuration} ${durationUnit}`;

      // Get assigned technician name
      const technicianName = assignedTo === "tech1" ? "Meron Alemu" :
                           assignedTo === "tech2" ? "Dawit Bekele" :
                           assignedTo === "tech3" ? "Tigist Haile" :
                           assignedTo === "tech4" ? "Solomon Tadesse" :
                           assignedTo === "team1" ? "Maintenance Team A" :
                           assignedTo === "team2" ? "Maintenance Team B" : "Unassigned";

      // Create a new maintenance record
      const newRecord = {
        transformerId: transformerId || "tr-001", // Default if not provided
        transformerSerialNumber: transformerSerialNumber || "TRF-1000", // Default if not provided
        type: formattedType,
        description: description,
        scheduledDate: scheduledDate ? format(scheduledDate, "yyyy-MM-dd") : format(new Date(), "yyyy-MM-dd"),
        status: "Scheduled",
        assignedTo: technicianName,
        priority: priority,
        location: location || "Main Substation",
        notes: notifyCustomers ? "Customers have been notified about planned outage." : "",
      };

      // Add the maintenance record using the enhanced maintenance service
      await scheduleNewMaintenance(newRecord);

      // Show success message
      toast({
        title: "Maintenance scheduled successfully",
        description: `Maintenance for transformer ${transformerSerialNumber || "TRF-1000"} has been scheduled for ${scheduledDate ? format(scheduledDate, "PPP") : "the selected date"}.`,
      })

      // Close dialog and notify parent component
      onOpenChange(false)
      if (onScheduled) onScheduled()
    } catch (error) {
      console.error("Error scheduling maintenance:", error)
      toast({
        title: "Error scheduling maintenance",
        description: "There was an error scheduling the maintenance. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Schedule Maintenance</DialogTitle>
          <DialogDescription>
            Schedule maintenance for transformer {transformer?.serialNumber || ""}. Fill out the form below to create a
            maintenance task.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="maintenanceType" className="text-right">
                Maintenance Type
              </Label>
              <Select value={maintenanceType} onValueChange={setMaintenanceType} required>
                <SelectTrigger id="maintenanceType" className="col-span-3">
                  <SelectValue placeholder="Select maintenance type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="routine_inspection">Routine Inspection</SelectItem>
                  <SelectItem value="oil_sampling">Oil Sampling</SelectItem>
                  <SelectItem value="repair">Repair</SelectItem>
                  <SelectItem value="full_maintenance">Full Maintenance</SelectItem>
                  <SelectItem value="replacement">Component Replacement</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="priority" className="text-right">
                Priority
              </Label>
              <Select
                value={priority}
                onValueChange={(value) => setPriority(value as "high" | "medium" | "low")}
                required
              >
                <SelectTrigger id="priority" className="col-span-3">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="scheduledDate" className="text-right">
                Scheduled Date
              </Label>
              <div className="col-span-3">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !scheduledDate && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {scheduledDate ? format(scheduledDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={scheduledDate} onSelect={setScheduledDate} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="estimatedDuration" className="text-right">
                Est. Duration
              </Label>
              <div className="col-span-3 flex items-center gap-2">
                <Input
                  id="estimatedDuration"
                  value={estimatedDuration}
                  onChange={(e) => setEstimatedDuration(e.target.value)}
                  placeholder="e.g., 2"
                  className="w-20"
                  required
                />
                <Select value={durationUnit} onValueChange={setDurationUnit}>
                  <SelectTrigger className="w-[100px]">
                    <SelectValue placeholder="Unit" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hours">Hours</SelectItem>
                    <SelectItem value="days">Days</SelectItem>
                  </SelectContent>
                </Select>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="assignedTo" className="text-right">
                Assigned To
              </Label>
              <Select value={assignedTo} onValueChange={setAssignedTo} required>
                <SelectTrigger id="assignedTo" className="col-span-3">
                  <SelectValue placeholder="Select technician" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tech1">Meron Alemu</SelectItem>
                  <SelectItem value="tech2">Dawit Bekele</SelectItem>
                  <SelectItem value="tech3">Tigist Haile</SelectItem>
                  <SelectItem value="tech4">Solomon Tadesse</SelectItem>
                  <SelectItem value="team1">Maintenance Team A</SelectItem>
                  <SelectItem value="team2">Maintenance Team B</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="description"
                className="col-span-3"
                placeholder="Provide detailed description of the maintenance task"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                required
                rows={4}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="col-span-3 col-start-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="notifyCustomers"
                    checked={notifyCustomers}
                    onCheckedChange={(checked) => setNotifyCustomers(checked as boolean)}
                  />
                  <Label htmlFor="notifyCustomers">Notify affected customers about planned outage</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Scheduling..." : "Schedule Maintenance"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
