import { NextRequest, NextResponse } from 'next/server'
import { forceCleanupAndRecreate } from '../../../src/lib/db/force-cleanup'
import { simpleSeedDatabase } from '../../../src/lib/db/simple-seed'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API: Force cleanup requested...')
    
    // Step 1: Force cleanup and recreate tables
    console.log('🔄 Force cleaning and recreating tables...')
    const cleanupResult = await forceCleanupAndRecreate()
    
    if (!cleanupResult.success) {
      console.error('❌ API: Force cleanup failed:', cleanupResult.message)
      return NextResponse.json({
        success: false,
        message: cleanupResult.message,
        error: cleanupResult.error,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    console.log('✅ API: Force cleanup completed successfully')

    // Wait a moment for the tables to be fully committed
    console.log('⏳ Waiting for tables to be committed...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    // Step 2: Seed database with data
    console.log('🔄 Seeding database with Ethiopian data...')
    const seedResult = await simpleSeedDatabase()
    
    if (!seedResult.success) {
      console.error('❌ API: Database seeding failed:', seedResult.message)
      return NextResponse.json({
        success: false,
        message: `Tables recreated but seeding failed: ${seedResult.message}`,
        cleanupSuccess: true,
        seedSuccess: false,
        error: seedResult.error,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    console.log('✅ API: Database seeded successfully')

    return NextResponse.json({
      success: true,
      message: 'Force cleanup and database setup completed successfully',
      cleanupSuccess: true,
      seedSuccess: true,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ API: Force cleanup failed:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Force cleanup failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Force cleanup API is ready. Use POST to force cleanup and recreate tables.',
    timestamp: new Date().toISOString()
  })
}
