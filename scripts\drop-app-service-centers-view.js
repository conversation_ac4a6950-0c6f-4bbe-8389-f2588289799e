const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    const [rows] = await connection.execute("SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_dtms_eeu_db = 'app_service_centers'");
    if (rows.length > 0) {
      try {
        await connection.execute('DROP VIEW IF EXISTS app_service_centers');
        console.log("✅ Dropped view 'app_service_centers'.");
      } catch (err) {
        console.error('❌ Error dropping view app_service_centers:', err.message);
      }
    } else {
      console.log("'app_service_centers' is not a view.");
    }
  } finally {
    await connection.end();
  }
})();
