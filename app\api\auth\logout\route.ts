import { NextRequest, NextResponse } from 'next/server';
import { logoutUser } from '@/src/lib/auth/mysql-auth';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get('auth_token')?.value;

    if (!token) {
      return NextResponse.json(
        { message: 'Already logged out' },
        { status: 200 }
      );
    }

    // Logout user
    await logoutUser(token);

    // Clear auth cookie
    cookieStore.delete('auth_token');

    return NextResponse.json({
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'An error occurred during logout' },
      { status: 500 }
    );
  }
}
