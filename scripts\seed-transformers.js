/**
 * Transformer Data Seeding Script
 * Seeds comprehensive transformer data for Ethiopian Electric Utility
 */

const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db'
};

// Ethiopian regions and their coordinates
const regions = [
  { name: 'Addis Ababa', code: 'AA', lat: 9.0054, lng: 38.7636 },
  { name: '<PERSON><PERSON>', code: 'OR', lat: 8.5, lng: 39.5 },
  { name: '<PERSON><PERSON>', code: 'AM', lat: 11.5, lng: 37.5 },
  { name: 'SNNPR', code: 'SNNPR', lat: 7.0, lng: 37.5 },
  { name: '<PERSON><PERSON><PERSON>', code: 'T<PERSON>', lat: 14.0, lng: 38.5 },
  { name: 'Somali', code: 'S<PERSON>', lat: 6.5, lng: 44.0 },
  { name: '<PERSON><PERSON>', code: 'A<PERSON>', lat: 11.5, lng: 41.0 },
  { name: 'Benishangul-Gumuz', code: 'BG', lat: 10.5, lng: 35.0 },
  { name: 'Gambela', code: 'GA', lat: 8.0, lng: 34.5 },
  { name: 'Harari', code: 'HA', lat: 9.3, lng: 42.1 },
  { name: 'Dire Dawa', code: 'DD', lat: 9.6, lng: 41.9 }
];

// Transformer manufacturers and models
const manufacturers = [
  { name: 'ABB', models: ['ONAN-500', 'ONAN-1000', 'ONAF-2000', 'OFAF-5000'] },
  { name: 'Siemens', models: ['SIT-500', 'SIT-1000', 'SIT-2000', 'SIT-5000'] },
  { name: 'Schneider Electric', models: ['SE-500', 'SE-1000', 'SE-2000', 'SE-3000'] },
  { name: 'General Electric', models: ['GE-500', 'GE-1000', 'GE-2000', 'GE-4000'] },
  { name: 'Hyundai Heavy Industries', models: ['HHI-500', 'HHI-1000', 'HHI-1500'] },
  { name: 'TBEA', models: ['TBEA-500', 'TBEA-1000', 'TBEA-2000'] }
];

// Transformer types and capacities
const transformerTypes = [
  { type: 'Distribution', capacities: [100, 200, 315, 500, 630, 800, 1000] },
  { type: 'Power', capacities: [1600, 2500, 4000, 6300, 10000, 16000, 25000] },
  { type: 'Auto', capacities: [40000, 63000, 100000, 160000] },
  { type: 'Instrument', capacities: [50, 100, 200, 315] }
];

// Status options with weights (for realistic distribution)
const statusOptions = [
  { status: 'operational', weight: 70 },
  { status: 'warning', weight: 15 },
  { status: 'maintenance', weight: 10 },
  { status: 'critical', weight: 4 },
  { status: 'burnt', weight: 1 }
];

// Voltage levels (primary/secondary)
const voltageConfigurations = [
  { primary: 33.0, secondary: 0.4 },
  { primary: 15.0, secondary: 0.4 },
  { primary: 11.0, secondary: 0.4 },
  { primary: 132.0, secondary: 33.0 },
  { primary: 132.0, secondary: 15.0 },
  { primary: 230.0, secondary: 132.0 },
  { primary: 400.0, secondary: 230.0 }
];

// Helper functions
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getWeightedRandomStatus() {
  const totalWeight = statusOptions.reduce((sum, option) => sum + option.weight, 0);
  let random = Math.random() * totalWeight;

  for (const option of statusOptions) {
    random -= option.weight;
    if (random <= 0) {
      return option.status;
    }
  }
  return 'operational';
}

function getRandomRegionId() {
  return Math.floor(Math.random() * 10) + 1; // Regions with IDs 1-10
}

function getRandomServiceCenterId() {
  return Math.floor(Math.random() * 8) + 1; // Service centers with IDs 1-8
}

function generateRandomCoordinate(baseCoord, variance = 0.5) {
  return baseCoord + (Math.random() - 0.5) * variance;
}

function generateRandomDate(startYear = 2015, endYear = 2024) {
  const start = new Date(startYear, 0, 1);
  const end = new Date(endYear, 11, 31);
  const randomTime = start.getTime() + Math.random() * (end.getTime() - start.getTime());
  return new Date(randomTime).toISOString().split('T')[0];
}

function generateSerialNumber(manufacturer, year, index) {
  const manufacturerCode = manufacturer.substring(0, 3).toUpperCase();
  return `${manufacturerCode}-${year}-${String(index).padStart(4, '0')}`;
}

// Generate transformer data
function generateTransformers(count = 100) {
  const transformers = [];

  for (let i = 1; i <= count; i++) {
    const region = getRandomElement(regions);
    const manufacturer = getRandomElement(manufacturers);
    const model = getRandomElement(manufacturer.models);
    const transformerType = getRandomElement(transformerTypes);
    const capacity = getRandomElement(transformerType.capacities);
    const voltage = getRandomElement(voltageConfigurations);
    const status = getWeightedRandomStatus();
    const installationYear = 2015 + Math.floor(Math.random() * 10);
    const manufactureYear = installationYear - Math.floor(Math.random() * 3);

    const transformer = {
      serial_number: generateSerialNumber(manufacturer.name, manufactureYear, i),
      name: `${region.name} ${transformerType.type} Transformer ${i}`,
      type: transformerType.type.toLowerCase(),
      capacity_kva: capacity,
      voltage_primary: voltage.primary,
      voltage_secondary: voltage.secondary,
      manufacturer: manufacturer.name,
      model: model,
      year_manufactured: manufactureYear,
      installation_date: generateRandomDate(installationYear, installationYear + 1),
      location_name: `${region.name} District, Zone ${Math.floor(Math.random() * 10) + 1}, Kebele ${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`,
      latitude: generateRandomCoordinate(region.lat),
      longitude: generateRandomCoordinate(region.lng),
      region_id: getRandomRegionId(),
      service_center_id: getRandomServiceCenterId(),
      status: status,
      efficiency_rating: Math.round((Math.random() * 5 + 95) * 100) / 100, // 95-100%
      load_factor: Math.round((Math.random() * 30 + 70) * 100) / 100, // 70-100%
      temperature: Math.round((Math.random() * 40 + 30) * 100) / 100, // 30-70°C
      oil_level: Math.round((Math.random() * 20 + 80) * 100) / 100, // 80-100%
      last_maintenance: status === 'maintenance' ? generateRandomDate(2023, 2024) : generateRandomDate(2022, 2024),
      next_maintenance: generateRandomDate(2024, 2025),
      region_name: region.name,
      region_code: region.code
    };

    transformers.push(transformer);
  }

  return transformers;
}

// Main seeding function
async function seedTransformers() {
  let connection;

  try {
    console.log('🔄 Connecting to MySQL database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to MySQL database');

    // Check if app_transformers table exists
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'app_transformers'"
    );

    if (tables.length === 0) {
      console.log('❌ app_transformers table does not exist. Please run the migration first.');
      return;
    }

    // Check current transformer count
    const [currentCount] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    console.log(`📊 Current transformer count: ${currentCount[0].count}`);

    if (currentCount[0].count >= 150) {
      console.log('✅ Database already has sufficient transformer data');
      console.log('🔄 Adding additional demo transformers...');
    } else {
      // Clear existing transformer data (disable foreign key checks temporarily)
      console.log('🔄 Clearing existing transformer data...');
      await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
      await connection.execute('DELETE FROM app_transformers');
      await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
      console.log('✅ Existing data cleared');
    }

    // Generate transformer data
    console.log('🔄 Generating transformer data...');
    const transformers = generateTransformers(150); // Generate 150 transformers
    console.log(`✅ Generated ${transformers.length} transformers`);

    // Insert transformers
    console.log('🔄 Inserting transformers into database...');

    for (let i = 0; i < transformers.length; i++) {
      const transformer = transformers[i];

      try {
        await connection.execute(
          `INSERT IGNORE INTO app_transformers (
            serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
            manufacturer, model, year_manufactured, installation_date, location_name,
            latitude, longitude, region_id, service_center_id, status,
            efficiency_rating, load_factor, temperature, oil_level,
            last_maintenance, next_maintenance, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          transformer.serial_number,
          transformer.name,
          transformer.type,
          transformer.capacity_kva,
          transformer.voltage_primary,
          transformer.voltage_secondary,
          transformer.manufacturer,
          transformer.model,
          transformer.year_manufactured,
          transformer.installation_date,
          transformer.location_name,
          transformer.latitude,
          transformer.longitude,
          transformer.region_id,
          transformer.service_center_id,
          transformer.status,
          transformer.efficiency_rating,
          transformer.load_factor,
          transformer.temperature,
          transformer.oil_level,
          transformer.last_maintenance,
          transformer.next_maintenance
        ]);
      } catch (error) {
        console.error('Error inserting transformer:', error.message);
      }

      if ((i + 1) % 10 === 0) {
        console.log(`✅ Inserted ${i + 1}/${transformers.length} transformers`);
      }
    }

    console.log('🎉 Successfully seeded transformer data!');

    // Display summary
    const [summary] = await connection.execute(`
      SELECT
        status,
        COUNT(*) as count,
        AVG(capacity_kva) as avg_capacity,
        AVG(efficiency_rating) as avg_efficiency
      FROM app_transformers
      GROUP BY status
      ORDER BY count DESC
    `);

    console.log('\n📊 Seeding Summary:');
    console.log('Status Distribution:');
    summary.forEach(row => {
      console.log(`  ${row.status}: ${row.count} transformers (Avg Capacity: ${Math.round(row.avg_capacity)}kVA, Avg Efficiency: ${Math.round(row.avg_efficiency)}%)`);
    });

  } catch (error) {
    console.error('❌ Error seeding transformers:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the seeding script
if (require.main === module) {
  seedTransformers()
    .then(() => {
      console.log('✅ Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedTransformers, generateTransformers };
