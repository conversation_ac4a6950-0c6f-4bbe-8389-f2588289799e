"use client"

import { useEffect, useState } from "react"
import { Bar, BarChart, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"
import { ChartContainer, ChartTooltipContent } from "@/src/components/ui/chart"
import { dashboardService } from "@/src/services/dashboard-service"

// Default data structure (will be replaced with data from the database)
const defaultData = []

interface TransformerStatusChartProps {
  region?: string;
  timeRange?: string;
}

export function TransformerStatusChart({ region = 'all', timeRange = '30d' }: TransformerStatusChartProps) {
  const [chartData, setChartData] = useState(defaultData)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true)
      try {
        // Fetch data from the dashboard service
        const data = await dashboardService.getDashboardData(region, timeRange)

        // Transform the data for the chart
        let transformedData = []

        if (region !== 'all' && region !== '') {
          // If a specific region is selected, show detailed data for that region
          const regionData = data.regionalStats.find(r =>
            r.region.toLowerCase().includes(region.toLowerCase()) ||
            region.toLowerCase().includes(r.region.toLowerCase())
          )

          if (regionData) {
            const operational = Math.round(regionData.totalTransformers * (regionData.healthyPercentage / 100))
            const maintenance = Math.round(regionData.totalTransformers * (regionData.warningPercentage / 100))
            const critical = Math.round(regionData.totalTransformers * (regionData.criticalPercentage / 100))

            transformedData = [{
              name: regionData.region,
              operational,
              maintenance,
              critical
            }]
          }
        } else {
          // If 'all regions' is selected, show data for each region
          transformedData = data.regionalStats.map(regionData => {
            const operational = Math.round(regionData.totalTransformers * (regionData.healthyPercentage / 100))
            const maintenance = Math.round(regionData.totalTransformers * (regionData.warningPercentage / 100))
            const critical = Math.round(regionData.totalTransformers * (regionData.criticalPercentage / 100))

            return {
              name: regionData.region,
              operational,
              maintenance,
              critical
            }
          })
        }

        setChartData(transformedData.length > 0 ? transformedData : defaultData)
      } catch (error) {
        console.error("Error fetching chart data:", error)
        setChartData(defaultData)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [region, timeRange])

  return (
    <ChartContainer
      config={{
        operational: {
          label: "Operational",
          color: "hsl(143, 85%, 40%)",
        },
        maintenance: {
          label: "Maintenance",
          color: "hsl(40, 95%, 55%)",
        },
        critical: {
          label: "Critical",
          color: "hsl(0, 85%, 60%)",
        },
      }}
      className="h-[300px]"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip content={<ChartTooltipContent />} />
          <Legend />
          <Bar dataKey="operational" stackId="a" fill="var(--color-operational)" radius={[4, 4, 0, 0]} />
          <Bar dataKey="maintenance" stackId="a" fill="var(--color-maintenance)" radius={[4, 4, 0, 0]} />
          <Bar dataKey="critical" stackId="a" fill="var(--color-critical)" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
