'use client'

import { useState, useEffect } from 'react'

export default function ApiTest201() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('🔄 Testing API for transformer 201...')
        const response = await fetch('/api/mysql/transformers/201')
        console.log('📊 Response status:', response.status)
        
        if (response.ok) {
          const result = await response.json()
          console.log('📊 Full API response:', result)
          setData(result)
        } else {
          const errorText = await response.text()
          console.error('❌ Error response:', errorText)
          setData({ error: `${response.status}: ${errorText}` })
        }
      } catch (error) {
        console.error('❌ Fetch error:', error)
        setData({ error: error instanceof Error ? error.message : 'Unknown error' })
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return <div className="p-6">Loading...</div>
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">API Test for Transformer 201</h1>
      <div className="bg-gray-100 p-4 rounded">
        <pre className="text-xs overflow-auto max-h-96">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    </div>
  )
}
