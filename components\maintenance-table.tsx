"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { Button } from "@/src/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/src/components/ui/dropdown-menu"
import { Calendar, CheckCircle, FileEdit, MoreHorizontal, Pencil, X } from "lucide-react"
import Link from "next/link"
import { useToast } from "@/src/components/ui/use-toast"
import { format } from "date-fns"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/src/components/ui/dialog"
import { Label } from "@/src/components/ui/label"
import { Input } from "@/src/components/ui/input"
import { Textarea } from "@/src/components/ui/textarea"
import { Pop<PERSON>, <PERSON>over<PERSON>ontent, PopoverTrigger } from "@/src/components/ui/popover"
import { cn } from "@/src/lib/utils"
import { CalendarIcon } from "lucide-react"
import { Calendar as CalendarComponent } from "@/src/components/ui/calendar"

import type { ExtendedMaintenanceRecord } from "@/src/services/maintenance-service"
import {
  getAllMaintenanceRecords,
  getUpcomingMaintenanceRecords,
  getInProgressMaintenanceRecords,
  getCompletedMaintenanceRecords,
  markMaintenanceAsCompleted,
  cancelMaintenance,
  updateMaintenanceRecord
} from "@/src/services/maintenance-service"

interface MaintenanceTableProps {
  extended?: boolean
  records?: ExtendedMaintenanceRecord[]
  onRecordUpdate?: () => void
}

export function MaintenanceTable({ extended = false, records, onRecordUpdate }: MaintenanceTableProps) {
  const { toast } = useToast()
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false)
  const [isNotesDialogOpen, setIsNotesDialogOpen] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<ExtendedMaintenanceRecord | null>(null)
  const [newNotes, setNewNotes] = useState("")
  const [rescheduleDate, setRescheduleDate] = useState<Date | undefined>(undefined)
  const [editedRecord, setEditedRecord] = useState<Partial<ExtendedMaintenanceRecord>>({})

  // Use provided records or get all records if none provided
  const maintenanceRecords = records || getAllMaintenanceRecords()

  // Handle marking a maintenance record as completed
  const handleMarkAsCompleted = (record: ExtendedMaintenanceRecord) => {
    markMaintenanceAsCompleted(record.id)
    toast({
      title: "Maintenance marked as completed",
      description: `Maintenance for transformer ${record.transformerSerialNumber} has been marked as completed.`,
    })
    if (onRecordUpdate) onRecordUpdate()
  }

  // Handle cancelling a maintenance record
  const handleCancelMaintenance = (record: ExtendedMaintenanceRecord) => {
    cancelMaintenance(record.id)
    toast({
      title: "Maintenance cancelled",
      description: `Maintenance for transformer ${record.transformerSerialNumber} has been cancelled.`,
    })
    if (onRecordUpdate) onRecordUpdate()
  }

  // Handle opening the edit dialog
  const handleEditMaintenance = (record: ExtendedMaintenanceRecord) => {
    setSelectedRecord(record)
    setEditedRecord({
      type: record.type,
      description: record.description,
      assignedTo: record.assignedTo,
      priority: record.priority,
    })
    setIsEditDialogOpen(true)
  }

  // Handle saving edited maintenance
  const handleSaveEdit = () => {
    if (!selectedRecord) return

    updateMaintenanceRecord(selectedRecord.id, editedRecord)
    toast({
      title: "Maintenance updated",
      description: `Maintenance for transformer ${selectedRecord.transformerSerialNumber} has been updated.`,
    })
    setIsEditDialogOpen(false)
    if (onRecordUpdate) onRecordUpdate()
  }

  // Handle opening the reschedule dialog
  const handleOpenReschedule = (record: ExtendedMaintenanceRecord) => {
    setSelectedRecord(record)
    setRescheduleDate(new Date(record.scheduledDate))
    setIsRescheduleDialogOpen(true)
  }

  // Handle saving rescheduled date
  const handleSaveReschedule = () => {
    if (!selectedRecord || !rescheduleDate) return

    updateMaintenanceRecord(selectedRecord.id, {
      scheduledDate: format(rescheduleDate, "yyyy-MM-dd"),
    })
    toast({
      title: "Maintenance rescheduled",
      description: `Maintenance for transformer ${selectedRecord.transformerSerialNumber} has been rescheduled to ${format(rescheduleDate, "PPP")}.`,
    })
    setIsRescheduleDialogOpen(false)
    if (onRecordUpdate) onRecordUpdate()
  }

  // Handle opening the notes dialog
  const handleOpenNotesDialog = (record: ExtendedMaintenanceRecord) => {
    setSelectedRecord(record)
    setNewNotes(record.notes || "")
    setIsNotesDialogOpen(true)
  }

  // Handle saving notes
  const handleSaveNotes = () => {
    if (!selectedRecord) return

    updateMaintenanceRecord(selectedRecord.id, { notes: newNotes })
    toast({
      title: "Notes added",
      description: `Notes for maintenance of transformer ${selectedRecord.transformerSerialNumber} have been updated.`,
    })
    setIsNotesDialogOpen(false)
    if (onRecordUpdate) onRecordUpdate()
  }

  // Get badge color based on status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Scheduled":
        return <Badge className="bg-yellow-500 hover:bg-yellow-600">Scheduled</Badge>
      case "In Progress":
        return <Badge className="bg-blue-500 hover:bg-blue-600">In Progress</Badge>
      case "Completed":
        return <Badge className="bg-green-500 hover:bg-green-600">Completed</Badge>
      case "Cancelled":
        return <Badge className="bg-gray-500 hover:bg-gray-600">Cancelled</Badge>
      case "Urgent":
        return <Badge className="bg-red-500 hover:bg-red-600">Urgent</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Transformer ID</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Scheduled Date</TableHead>
            <TableHead>Status</TableHead>
            {extended && <TableHead>Assigned To</TableHead>}
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {maintenanceRecords.length === 0 ? (
            <TableRow>
              <TableCell colSpan={extended ? 7 : 6} className="text-center py-8 text-muted-foreground">
                No maintenance records found
              </TableCell>
            </TableRow>
          ) : (
            maintenanceRecords.map((record) => (
              <TableRow key={record.id}>
                <TableCell className="font-medium">
                  <Link href={`/transformers/${record.transformerId}`} className="hover:underline">
                    {record.transformerSerialNumber}
                  </Link>
                </TableCell>
                <TableCell>{record.location}</TableCell>
                <TableCell>{record.type}</TableCell>
                <TableCell>{format(new Date(record.scheduledDate), "MMM d, yyyy")}</TableCell>
                <TableCell>
                  {getStatusBadge(record.status)}
                </TableCell>
                {extended && <TableCell>{record.assignedTo}</TableCell>}
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {record.status !== "Completed" && record.status !== "Cancelled" && (
                        <DropdownMenuItem onClick={() => handleMarkAsCompleted(record)}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Mark as Completed
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => handleEditMaintenance(record)}>
                        <Pencil className="mr-2 h-4 w-4" />
                        Edit Maintenance
                      </DropdownMenuItem>
                      {record.status !== "Completed" && record.status !== "Cancelled" && (
                        <DropdownMenuItem onClick={() => handleOpenReschedule(record)}>
                          <Calendar className="mr-2 h-4 w-4" />
                          Reschedule
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => handleOpenNotesDialog(record)}>
                        <FileEdit className="mr-2 h-4 w-4" />
                        Add Notes
                      </DropdownMenuItem>
                      {record.status !== "Completed" && record.status !== "Cancelled" && (
                        <DropdownMenuItem onClick={() => handleCancelMaintenance(record)}>
                          <X className="mr-2 h-4 w-4" />
                          Cancel Maintenance
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {/* Edit Maintenance Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Maintenance</DialogTitle>
            <DialogDescription>
              Update maintenance details for transformer {selectedRecord?.transformerSerialNumber}.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="maintenanceType" className="text-right">
                Maintenance Type
              </Label>
              <Input
                id="maintenanceType"
                value={editedRecord.type || ""}
                onChange={(e) => setEditedRecord({ ...editedRecord, type: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Textarea
                id="description"
                value={editedRecord.description || ""}
                onChange={(e) => setEditedRecord({ ...editedRecord, description: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="assignedTo" className="text-right">
                Assigned To
              </Label>
              <Input
                id="assignedTo"
                value={editedRecord.assignedTo || ""}
                onChange={(e) => setEditedRecord({ ...editedRecord, assignedTo: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="priority" className="text-right">
                Priority
              </Label>
              <select
                id="priority"
                value={editedRecord.priority || "medium"}
                onChange={(e) => setEditedRecord({ ...editedRecord, priority: e.target.value as any })}
                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveEdit}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reschedule Dialog */}
      <Dialog open={isRescheduleDialogOpen} onOpenChange={setIsRescheduleDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Reschedule Maintenance</DialogTitle>
            <DialogDescription>
              Select a new date for the maintenance of transformer {selectedRecord?.transformerSerialNumber}.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="scheduledDate" className="text-right">
                New Date
              </Label>
              <div className="col-span-3">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !rescheduleDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {rescheduleDate ? format(rescheduleDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={rescheduleDate}
                      onSelect={setRescheduleDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRescheduleDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveReschedule}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Notes Dialog */}
      <Dialog open={isNotesDialogOpen} onOpenChange={setIsNotesDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add Notes</DialogTitle>
            <DialogDescription>
              Add notes for the maintenance of transformer {selectedRecord?.transformerSerialNumber}.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="notes"
                value={newNotes}
                onChange={(e) => setNewNotes(e.target.value)}
                className="col-span-3"
                rows={5}
                placeholder="Enter maintenance notes here..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsNotesDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveNotes}>Save Notes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
