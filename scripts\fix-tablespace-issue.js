// Fix tablespace issue for app_regions and app_service_centers
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

const createRegions = `CREATE TABLE IF NOT EXISTS app_regions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100),
  code VARCHAR(10),
  population INT,
  area_km2 DECIMAL(10,2),
  created_at DATETIME,
  updated_at DATETIME
);`;
const createServiceCenters = `CREATE TABLE IF NOT EXISTS app_service_centers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VA<PERSON>HA<PERSON>(100),
  code VARCHAR(20),
  region_id INT,
  address TEXT,
  phone VARCHAR(20),
  email VARCHAR(100),
  manager_name VARCHAR(100),
  created_at DATETIME,
  updated_at DATETIME
);`;

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    // Try to create minimal tables
    await connection.execute(createRegions);
    await connection.execute(createServiceCenters);
    // Discard tablespace
    await connection.execute('ALTER TABLE app_regions DISCARD TABLESPACE');
    await connection.execute('ALTER TABLE app_service_centers DISCARD TABLESPACE');
    // Drop the tables
    await connection.execute('DROP TABLE IF EXISTS app_regions');
    await connection.execute('DROP TABLE IF EXISTS app_service_centers');
    console.log('✅ Discarded tablespace and dropped tables.');
  } catch (err) {
    console.error('❌ Error during tablespace fix:', err);
  } finally {
    await connection.end();
  }
})();
