-- SQLite schema for EEU Transformer Management System

-- Create transformers table
CREATE TABLE IF NOT EXISTS transformers (
  id TEXT PRIMARY KEY,
  serial_number TEXT NOT NULL,
  manufacturer TEXT,
  model TEXT,
  type TEXT,
  capacity TEXT,
  voltage_rating TEXT,
  installation_date TEXT,
  last_maintenance_date TEXT,
  last_inspection_date TEXT,
  status TEXT,
  location_latitude TEXT,
  location_longitude TEXT,
  location_region TEXT,
  location_service_center TEXT,
  location_address TEXT,
  location_installation_site TEXT,
  manufacturing_year TEXT,
  primary_voltage TEXT,
  secondary_voltage TEXT,
  connection_type TEXT,
  phase_count TEXT,
  impedance TEXT,
  oil_type TEXT,
  cooling_type TEXT,
  weight TEXT,
  dimensions TEXT,
  temperature_rise TEXT,
  noise_level TEXT,
  feeder_name TEXT,
  substation_name TEXT,
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now'))
);

-- Create regions table
CREATE TABLE IF NOT EXISTS regions (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  code TEXT,
  latitude TEXT,
  longitude TEXT,
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now'))
);

-- Create service_centers table
CREATE TABLE IF NOT EXISTS service_centers (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  region TEXT NOT NULL,
  address TEXT,
  contact_person TEXT,
  phone TEXT,
  email TEXT,
  latitude TEXT,
  longitude TEXT,
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  FOREIGN KEY (region) REFERENCES regions(id)
);

-- Create maintenance_records table
CREATE TABLE IF NOT EXISTS maintenance_records (
  id TEXT PRIMARY KEY,
  transformer_id TEXT NOT NULL,
  date TEXT,
  type TEXT,
  description TEXT,
  performed_by TEXT,
  status TEXT,
  cost NUMERIC,
  findings TEXT,
  actions TEXT,
  next_scheduled_date TEXT,
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  FOREIGN KEY (transformer_id) REFERENCES transformers(id)
);

-- Create inspection_records table
CREATE TABLE IF NOT EXISTS inspection_records (
  id TEXT PRIMARY KEY,
  transformer_id TEXT NOT NULL,
  date TEXT,
  inspector TEXT,
  findings TEXT,
  recommendations TEXT,
  status TEXT,
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  FOREIGN KEY (transformer_id) REFERENCES transformers(id)
);

-- Create test_results table
CREATE TABLE IF NOT EXISTS test_results (
  id TEXT PRIMARY KEY,
  transformer_id TEXT NOT NULL,
  date TEXT,
  type TEXT,
  result TEXT,
  performed_by TEXT,
  parameters TEXT, -- JSON
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  FOREIGN KEY (transformer_id) REFERENCES transformers(id)
);

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  role TEXT,
  region TEXT,
  service_center TEXT,
  is_active INTEGER DEFAULT 1,
  last_login TEXT,
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  FOREIGN KEY (region) REFERENCES regions(id),
  FOREIGN KEY (service_center) REFERENCES service_centers(id)
);

-- Create alerts table
CREATE TABLE IF NOT EXISTS alerts (
  id TEXT PRIMARY KEY,
  transformer_id TEXT NOT NULL,
  type TEXT,
  severity TEXT,
  message TEXT,
  timestamp TEXT,
  is_acknowledged INTEGER DEFAULT 0,
  acknowledged_by TEXT,
  acknowledged_at TEXT,
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  FOREIGN KEY (transformer_id) REFERENCES transformers(id),
  FOREIGN KEY (acknowledged_by) REFERENCES users(id)
);

-- Create outages table
CREATE TABLE IF NOT EXISTS outages (
  id TEXT PRIMARY KEY,
  transformer_id TEXT NOT NULL,
  start_time TEXT,
  end_time TEXT,
  cause TEXT,
  description TEXT,
  status TEXT,
  reported_by TEXT,
  resolved_by TEXT,
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  FOREIGN KEY (transformer_id) REFERENCES transformers(id),
  FOREIGN KEY (reported_by) REFERENCES users(id),
  FOREIGN KEY (resolved_by) REFERENCES users(id)
);

-- Create weather_alerts table
CREATE TABLE IF NOT EXISTS weather_alerts (
  id TEXT PRIMARY KEY,
  region_id TEXT NOT NULL,
  type TEXT,
  severity TEXT,
  title TEXT,
  description TEXT,
  start_time TEXT,
  end_time TEXT,
  source TEXT,
  latitude TEXT,
  longitude TEXT,
  is_active INTEGER DEFAULT 1,
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  FOREIGN KEY (region_id) REFERENCES regions(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_transformers_region ON transformers(location_region);
CREATE INDEX IF NOT EXISTS idx_transformers_service_center ON transformers(location_service_center);
CREATE INDEX IF NOT EXISTS idx_transformers_status ON transformers(status);
CREATE INDEX IF NOT EXISTS idx_service_centers_region ON service_centers(region);
CREATE INDEX IF NOT EXISTS idx_maintenance_records_transformer ON maintenance_records(transformer_id);
CREATE INDEX IF NOT EXISTS idx_inspection_records_transformer ON inspection_records(transformer_id);
CREATE INDEX IF NOT EXISTS idx_test_results_transformer ON test_results(transformer_id);
CREATE INDEX IF NOT EXISTS idx_alerts_transformer ON alerts(transformer_id);
CREATE INDEX IF NOT EXISTS idx_outages_transformer ON outages(transformer_id);
CREATE INDEX IF NOT EXISTS idx_weather_alerts_region ON weather_alerts(region_id);
