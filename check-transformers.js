const mysql = require('mysql2/promise');

async function checkTransformers() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'dtms_eeu_db'
    });
    
    console.log('🔍 Checking current transformer data...');
    
    const [count] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    console.log(`📊 Current transformer count: ${count[0].count}`);
    
    if (count[0].count > 0) {
      const [sample] = await connection.execute('SELECT * FROM app_transformers LIMIT 5');
      console.log('\n📋 Sample transformers:');
      sample.forEach((t, i) => {
        console.log(`  ${i+1}. ${t.name} (${t.status}) - ${t.capacity_kva}kVA - ${t.manufacturer}`);
      });
      
      // Show status distribution
      const [statusDist] = await connection.execute(`
        SELECT status, COUNT(*) as count 
        FROM app_transformers 
        GROUP BY status 
        ORDER BY count DESC
      `);
      
      console.log('\n📈 Status Distribution:');
      statusDist.forEach(row => {
        console.log(`  ${row.status}: ${row.count} transformers`);
      });
    } else {
      console.log('⚠️  No transformers found in database');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Error checking transformers:', error);
  }
}

checkTransformers();
