/**
 * Base repository class
 * 
 * This class provides generic CRUD operations for any entity type.
 * It serves as a base class for specific entity repositories.
 */

import { 
  createEntity, 
  getEntityById, 
  updateEntity, 
  deleteEntity, 
  queryEntities, 
  countEntities 
} from '../db-utils';
import { BaseEntity, DatabaseSchema } from '../schema';

export class BaseRepository<T extends BaseEntity> {
  protected collection: keyof Omit<DatabaseSchema, '_metadata'>;
  
  constructor(collection: keyof Omit<DatabaseSchema, '_metadata'>) {
    this.collection = collection;
  }
  
  /**
   * Create a new entity
   */
  create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): T {
    return createEntity<T>(this.collection, data);
  }
  
  /**
   * Get an entity by ID
   */
  getById(id: string): T | null {
    return getEntityById<T>(this.collection, id);
  }
  
  /**
   * Update an entity
   */
  update(id: string, data: Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>): T | null {
    return updateEntity<T>(this.collection, id, data);
  }
  
  /**
   * Delete an entity
   */
  delete(id: string): boolean {
    return deleteEntity(this.collection, id);
  }
  
  /**
   * Find entities with filters
   */
  find(
    filters: Partial<Record<keyof T, any>> = {},
    options: {
      limit?: number;
      offset?: number;
      sortBy?: keyof T;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): T[] {
    return queryEntities<T>(this.collection, filters, options);
  }
  
  /**
   * Find one entity with filters
   */
  findOne(filters: Partial<Record<keyof T, any>> = {}): T | null {
    const results = this.find(filters, { limit: 1 });
    return results.length > 0 ? results[0] : null;
  }
  
  /**
   * Count entities with filters
   */
  count(filters: Partial<Record<keyof T, any>> = {}): number {
    return countEntities<T>(this.collection, filters);
  }
  
  /**
   * Get all entities
   */
  getAll(): T[] {
    return this.find();
  }
}
