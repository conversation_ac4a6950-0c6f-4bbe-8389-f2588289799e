"use client"

export interface Document {
  id: string
  title: string
  description: string
  category: string
  tags: string[]
  uploadedBy: string
  uploadDate: string
  lastModified: string
  fileSize: string
  fileType: string
  url: string
  thumbnailUrl?: string
  version: string
  status: "active" | "archived" | "draft"
  accessLevel: "public" | "internal" | "restricted"
  viewCount: number
  downloadCount: number
  relatedDocuments?: string[]
  metadata?: Record<string, any>
}

export interface DocumentCategory {
  id: string
  name: string
  description: string
  documentCount: number
}

export interface DocumentTag {
  id: string
  name: string
  documentCount: number
}

// Mock data for documents
const mockDocuments: Document[] = [
  {
    id: "doc-001",
    title: "Transformer Installation Guide",
    description: "Comprehensive guide for installing distribution transformers",
    category: "Technical Manuals",
    tags: ["installation", "technical", "guide"],
    uploadedBy: "John Doe",
    uploadDate: "2023-05-15",
    lastModified: "2023-05-15",
    fileSize: "2.5 MB",
    fileType: "PDF",
    url: "/documents/transformer-installation-guide.pdf",
    thumbnailUrl: "/document-thumbnails/transformer-installation-guide.jpg",
    version: "1.0",
    status: "active",
    accessLevel: "public",
    viewCount: 245,
    downloadCount: 120
  },
  {
    id: "doc-002",
    title: "Maintenance Procedures for ABB Transformers",
    description: "Standard maintenance procedures for ABB distribution transformers",
    category: "Maintenance",
    tags: ["maintenance", "ABB", "procedures"],
    uploadedBy: "Jane Smith",
    uploadDate: "2023-04-20",
    lastModified: "2023-04-22",
    fileSize: "3.2 MB",
    fileType: "PDF",
    url: "/documents/abb-maintenance-procedures.pdf",
    thumbnailUrl: "/document-thumbnails/abb-maintenance-procedures.jpg",
    version: "2.1",
    status: "active",
    accessLevel: "internal",
    viewCount: 189,
    downloadCount: 87
  },
  {
    id: "doc-003",
    title: "Troubleshooting Common Transformer Issues",
    description: "Guide for diagnosing and resolving common transformer problems",
    category: "Troubleshooting",
    tags: ["troubleshooting", "repair", "guide"],
    uploadedBy: "Michael Johnson",
    uploadDate: "2023-03-10",
    lastModified: "2023-03-15",
    fileSize: "1.8 MB",
    fileType: "PDF",
    url: "/documents/transformer-troubleshooting.pdf",
    thumbnailUrl: "/document-thumbnails/transformer-troubleshooting.jpg",
    version: "1.2",
    status: "active",
    accessLevel: "public",
    viewCount: 312,
    downloadCount: 156
  },
  {
    id: "doc-004",
    title: "Safety Protocols for Field Technicians",
    description: "Safety guidelines and protocols for field technicians working with transformers",
    category: "Safety",
    tags: ["safety", "protocols", "field work"],
    uploadedBy: "Sarah Williams",
    uploadDate: "2023-02-28",
    lastModified: "2023-02-28",
    fileSize: "1.5 MB",
    fileType: "PDF",
    url: "/documents/safety-protocols.pdf",
    thumbnailUrl: "/document-thumbnails/safety-protocols.jpg",
    version: "1.0",
    status: "active",
    accessLevel: "internal",
    viewCount: 278,
    downloadCount: 201
  },
  {
    id: "doc-005",
    title: "Transformer Specifications - Siemens Models",
    description: "Technical specifications for Siemens transformer models",
    category: "Specifications",
    tags: ["specifications", "Siemens", "technical"],
    uploadedBy: "David Brown",
    uploadDate: "2023-01-15",
    lastModified: "2023-01-20",
    fileSize: "4.2 MB",
    fileType: "PDF",
    url: "/documents/siemens-specifications.pdf",
    thumbnailUrl: "/document-thumbnails/siemens-specifications.jpg",
    version: "1.1",
    status: "active",
    accessLevel: "public",
    viewCount: 156,
    downloadCount: 89
  },
  {
    id: "doc-006",
    title: "Transformer Oil Testing Procedures",
    description: "Procedures for testing and analyzing transformer oil",
    category: "Testing",
    tags: ["oil testing", "procedures", "maintenance"],
    uploadedBy: "Emily Davis",
    uploadDate: "2022-12-10",
    lastModified: "2023-01-05",
    fileSize: "2.1 MB",
    fileType: "PDF",
    url: "/documents/oil-testing-procedures.pdf",
    thumbnailUrl: "/document-thumbnails/oil-testing-procedures.jpg",
    version: "2.0",
    status: "active",
    accessLevel: "internal",
    viewCount: 203,
    downloadCount: 112
  },
  {
    id: "doc-007",
    title: "Regulatory Compliance Guidelines",
    description: "Guidelines for ensuring regulatory compliance in transformer operations",
    category: "Compliance",
    tags: ["compliance", "regulations", "guidelines"],
    uploadedBy: "Robert Wilson",
    uploadDate: "2022-11-20",
    lastModified: "2022-11-20",
    fileSize: "1.9 MB",
    fileType: "PDF",
    url: "/documents/compliance-guidelines.pdf",
    thumbnailUrl: "/document-thumbnails/compliance-guidelines.jpg",
    version: "1.0",
    status: "active",
    accessLevel: "restricted",
    viewCount: 98,
    downloadCount: 45
  },
  {
    id: "doc-008",
    title: "Transformer Installation Checklist",
    description: "Checklist for ensuring proper transformer installation",
    category: "Checklists",
    tags: ["installation", "checklist", "procedures"],
    uploadedBy: "Jennifer Lee",
    uploadDate: "2022-10-05",
    lastModified: "2022-10-10",
    fileSize: "0.8 MB",
    fileType: "PDF",
    url: "/documents/installation-checklist.pdf",
    thumbnailUrl: "/document-thumbnails/installation-checklist.jpg",
    version: "1.1",
    status: "active",
    accessLevel: "public",
    viewCount: 267,
    downloadCount: 189
  }
];

// Mock data for categories
const mockCategories: DocumentCategory[] = [
  {
    id: "cat-001",
    name: "Technical Manuals",
    description: "Technical manuals and guides for transformers",
    documentCount: 12
  },
  {
    id: "cat-002",
    name: "Maintenance",
    description: "Maintenance procedures and guidelines",
    documentCount: 18
  },
  {
    id: "cat-003",
    name: "Troubleshooting",
    description: "Troubleshooting guides and solutions",
    documentCount: 9
  },
  {
    id: "cat-004",
    name: "Safety",
    description: "Safety protocols and guidelines",
    documentCount: 7
  },
  {
    id: "cat-005",
    name: "Specifications",
    description: "Technical specifications for different transformer models",
    documentCount: 15
  },
  {
    id: "cat-006",
    name: "Testing",
    description: "Testing procedures and protocols",
    documentCount: 11
  },
  {
    id: "cat-007",
    name: "Compliance",
    description: "Regulatory compliance documents",
    documentCount: 6
  },
  {
    id: "cat-008",
    name: "Checklists",
    description: "Checklists for various procedures",
    documentCount: 8
  }
];

// Mock data for tags
const mockTags: DocumentTag[] = [
  { id: "tag-001", name: "installation", documentCount: 15 },
  { id: "tag-002", name: "maintenance", documentCount: 22 },
  { id: "tag-003", name: "troubleshooting", documentCount: 14 },
  { id: "tag-004", name: "safety", documentCount: 9 },
  { id: "tag-005", name: "technical", documentCount: 28 },
  { id: "tag-006", name: "guide", documentCount: 17 },
  { id: "tag-007", name: "procedures", documentCount: 19 },
  { id: "tag-008", name: "ABB", documentCount: 8 },
  { id: "tag-009", name: "Siemens", documentCount: 7 },
  { id: "tag-010", name: "specifications", documentCount: 16 },
  { id: "tag-011", name: "oil testing", documentCount: 6 },
  { id: "tag-012", name: "compliance", documentCount: 5 },
  { id: "tag-013", name: "regulations", documentCount: 4 },
  { id: "tag-014", name: "checklist", documentCount: 10 },
  { id: "tag-015", name: "field work", documentCount: 12 }
];

// Documentation service class
class DocumentationService {
  // Get all documents
  async getAllDocuments(): Promise<Document[]> {
    return [...mockDocuments];
  }

  // Get document by ID
  async getDocumentById(id: string): Promise<Document | null> {
    const document = mockDocuments.find(d => d.id === id);
    return document || null;
  }

  // Search documents by query
  async searchDocuments(query: string): Promise<Document[]> {
    if (!query) return [...mockDocuments];
    
    const lowerQuery = query.toLowerCase();
    return mockDocuments.filter(d => 
      d.title.toLowerCase().includes(lowerQuery) ||
      d.description.toLowerCase().includes(lowerQuery) ||
      d.category.toLowerCase().includes(lowerQuery) ||
      d.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  // Filter documents by category
  async filterDocumentsByCategory(category: string): Promise<Document[]> {
    if (category === 'all') return [...mockDocuments];
    return mockDocuments.filter(d => d.category === category);
  }

  // Filter documents by tag
  async filterDocumentsByTag(tag: string): Promise<Document[]> {
    if (tag === 'all') return [...mockDocuments];
    return mockDocuments.filter(d => d.tags.includes(tag));
  }

  // Filter documents by access level
  async filterDocumentsByAccessLevel(accessLevel: string): Promise<Document[]> {
    if (accessLevel === 'all') return [...mockDocuments];
    return mockDocuments.filter(d => d.accessLevel === accessLevel);
  }

  // Filter documents by status
  async filterDocumentsByStatus(status: string): Promise<Document[]> {
    if (status === 'all') return [...mockDocuments];
    return mockDocuments.filter(d => d.status === status);
  }

  // Get all categories
  async getAllCategories(): Promise<DocumentCategory[]> {
    return [...mockCategories];
  }

  // Get all tags
  async getAllTags(): Promise<DocumentTag[]> {
    return [...mockTags];
  }

  // Add a new document
  async addDocument(document: Omit<Document, "id">): Promise<Document> {
    const newId = `doc-${String(mockDocuments.length + 1).padStart(3, '0')}`;
    const newDocument = { ...document, id: newId };
    mockDocuments.push(newDocument as Document);
    return newDocument as Document;
  }

  // Update a document
  async updateDocument(id: string, updates: Partial<Document>): Promise<Document | null> {
    const index = mockDocuments.findIndex(d => d.id === id);
    if (index === -1) return null;
    
    mockDocuments[index] = { ...mockDocuments[index], ...updates };
    return mockDocuments[index];
  }

  // Delete a document
  async deleteDocument(id: string): Promise<boolean> {
    const index = mockDocuments.findIndex(d => d.id === id);
    if (index === -1) return false;
    
    mockDocuments.splice(index, 1);
    return true;
  }

  // Increment view count
  async incrementViewCount(id: string): Promise<void> {
    const document = mockDocuments.find(d => d.id === id);
    if (document) {
      document.viewCount += 1;
    }
  }

  // Increment download count
  async incrementDownloadCount(id: string): Promise<void> {
    const document = mockDocuments.find(d => d.id === id);
    if (document) {
      document.downloadCount += 1;
    }
  }

  // Add a new category
  async addCategory(category: Omit<DocumentCategory, "id">): Promise<DocumentCategory> {
    const newId = `cat-${String(mockCategories.length + 1).padStart(3, '0')}`;
    const newCategory = { ...category, id: newId };
    mockCategories.push(newCategory as DocumentCategory);
    return newCategory as DocumentCategory;
  }

  // Update a category
  async updateCategory(id: string, updates: Partial<DocumentCategory>): Promise<DocumentCategory | null> {
    const index = mockCategories.findIndex(c => c.id === id);
    if (index === -1) return null;
    
    mockCategories[index] = { ...mockCategories[index], ...updates };
    return mockCategories[index];
  }

  // Delete a category
  async deleteCategory(id: string): Promise<boolean> {
    const index = mockCategories.findIndex(c => c.id === id);
    if (index === -1) return false;
    
    mockCategories.splice(index, 1);
    return true;
  }

  // Add a new tag
  async addTag(tag: Omit<DocumentTag, "id">): Promise<DocumentTag> {
    const newId = `tag-${String(mockTags.length + 1).padStart(3, '0')}`;
    const newTag = { ...tag, id: newId };
    mockTags.push(newTag as DocumentTag);
    return newTag as DocumentTag;
  }

  // Update a tag
  async updateTag(id: string, updates: Partial<DocumentTag>): Promise<DocumentTag | null> {
    const index = mockTags.findIndex(t => t.id === id);
    if (index === -1) return null;
    
    mockTags[index] = { ...mockTags[index], ...updates };
    return mockTags[index];
  }

  // Delete a tag
  async deleteTag(id: string): Promise<boolean> {
    const index = mockTags.findIndex(t => t.id === id);
    if (index === -1) return false;
    
    mockTags.splice(index, 1);
    return true;
  }
}

// Export singleton instance
export const documentationService = new DocumentationService();
