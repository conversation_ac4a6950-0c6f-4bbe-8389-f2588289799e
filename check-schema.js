const mysql = require('mysql2/promise');

async function checkSchema() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'dtms_eeu_db'
    });
    
    console.log('🔍 Checking app_transformers table schema...');
    const [columns] = await connection.execute('DESCRIBE app_transformers');
    
    console.log('\n📋 Available columns:');
    columns.forEach((col, i) => {
      console.log(`  ${i+1}. ${col.Field} (${col.Type})`);
    });
    
    // Test a simple query
    console.log('\n🔍 Testing simple query...');
    const [sample] = await connection.execute('SELECT * FROM app_transformers LIMIT 1');
    
    if (sample.length > 0) {
      console.log('\n📊 Sample record fields:');
      Object.keys(sample[0]).forEach((key, i) => {
        console.log(`  ${i+1}. ${key}: ${sample[0][key]}`);
      });
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Error checking schema:', error);
  }
}

checkSchema();
