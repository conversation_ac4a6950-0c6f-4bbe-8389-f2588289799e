/**
 * Fix and Populate Data for EEU DTMS
 * This script works with the existing database structure
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function fixAndPopulateData() {
  let connection;
  
  try {
    console.log('🔧 FIXING AND POPULATING DASHBOARD DATA');
    console.log('=' .repeat(50));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // Check which tables exist and their structure
    console.log('\n🔍 Checking database structure...');
    
    // Check if app_regions2 exists and has data
    try {
      const [regions2] = await connection.execute('SELECT * FROM app_regions2 LIMIT 5');
      console.log(`📊 app_regions2 has ${regions2.length} records`);
      
      if (regions2.length === 0) {
        console.log('🌱 Adding regions to app_regions2...');
        const regions = [
          { name: 'Addis Ababa', code: 'AA', population: 3500000, area_km2: 527.0 },
          { name: 'Oromia', code: 'OR', population: 37000000, area_km2: 353006.81 },
          { name: 'Amhara', code: 'AM', population: 21000000, area_km2: 154708.96 },
          { name: 'Tigray', code: 'TI', population: 5500000, area_km2: 50078.64 },
          { name: 'SNNP', code: 'SN', population: 20000000, area_km2: 105887.18 }
        ];
        
        for (const region of regions) {
          await connection.execute(`
            INSERT INTO app_regions2 (name, code, population, area_km2)
            VALUES (?, ?, ?, ?)
          `, [region.name, region.code, region.population, region.area_km2]);
        }
        console.log(`✅ Added ${regions.length} regions to app_regions2`);
      }
    } catch (error) {
      console.log('⚠️  app_regions2 table not found, using app_regions');
    }
    
    // Get current transformer count
    const [currentTransformers] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    console.log(`📊 Current transformers: ${currentTransformers[0].count}`);
    
    // Get the actual transformer IDs that exist
    const [existingTransformers] = await connection.execute('SELECT id, name FROM app_transformers ORDER BY id');
    console.log(`📋 Existing transformers:`);
    existingTransformers.forEach(t => console.log(`  • ID ${t.id}: ${t.name}`));
    
    // Add maintenance schedules for existing transformers
    if (existingTransformers.length > 0) {
      console.log('\n🔧 Adding Maintenance Schedules for existing transformers...');
      
      const maintenanceSchedules = [
        { transformer_id: existingTransformers[0].id, type: 'routine', title: `Monthly Inspection - ${existingTransformers[0].name}`, description: 'Regular monthly visual inspection and basic checks', scheduled_date: '2024-12-30', estimated_duration: 2, priority: 'medium', status: 'scheduled' },
        { transformer_id: existingTransformers[0].id, type: 'preventive', title: `Quarterly Maintenance - ${existingTransformers[0].name}`, description: 'Comprehensive quarterly electrical testing', scheduled_date: '2024-12-25', estimated_duration: 8, priority: 'high', status: 'in_progress' }
      ];
      
      if (existingTransformers.length > 1) {
        maintenanceSchedules.push(
          { transformer_id: existingTransformers[1].id, type: 'routine', title: `Monthly Check - ${existingTransformers[1].name}`, description: 'Monthly routine inspection and cleaning', scheduled_date: '2024-12-20', estimated_duration: 2, priority: 'low', status: 'completed' },
          { transformer_id: existingTransformers[1].id, type: 'corrective', title: `Temperature Investigation - ${existingTransformers[1].name}`, description: 'Investigate temperature readings', scheduled_date: '2024-12-15', estimated_duration: 4, priority: 'high', status: 'scheduled' }
        );
      }
      
      for (const schedule of maintenanceSchedules) {
        try {
          await connection.execute(`
            INSERT INTO app_maintenance_schedules (
              transformer_id, type, title, description, scheduled_date, estimated_duration, priority, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            schedule.transformer_id, schedule.type, schedule.title, schedule.description,
            schedule.scheduled_date, schedule.estimated_duration, schedule.priority, schedule.status
          ]);
          console.log(`  ✅ Added: ${schedule.title}`);
        } catch (error) {
          console.log(`  ⚠️  Skipped: ${schedule.title} (${error.message})`);
        }
      }
    }
    
    // Add alerts for existing transformers
    if (existingTransformers.length > 0) {
      console.log('\n🚨 Adding Alerts for existing transformers...');
      
      const alerts = [
        { transformer_id: existingTransformers[0].id, title: 'High Temperature Alert', description: 'Transformer temperature monitoring alert', severity: 'high', type: 'temperature', priority: 'high', status: 'active', is_resolved: false },
        { transformer_id: existingTransformers[0].id, title: 'Routine Maintenance Due', description: 'Scheduled maintenance approaching', severity: 'low', type: 'maintenance', priority: 'low', status: 'active', is_resolved: false }
      ];
      
      if (existingTransformers.length > 1) {
        alerts.push(
          { transformer_id: existingTransformers[1].id, title: 'Load Monitoring Alert', description: 'Transformer load factor monitoring', severity: 'medium', type: 'load', priority: 'medium', status: 'active', is_resolved: false },
          { transformer_id: existingTransformers[1].id, title: 'Communication Test', description: 'System communication test completed', severity: 'low', type: 'communication', priority: 'low', status: 'resolved', is_resolved: true, resolved_at: '2024-12-01 10:00:00' }
        );
      }
      
      for (const alert of alerts) {
        try {
          await connection.execute(`
            INSERT INTO app_alerts (
              transformer_id, title, description, severity, type, priority, status, is_resolved, resolved_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            alert.transformer_id, alert.title, alert.description, alert.severity,
            alert.type, alert.priority, alert.status, alert.is_resolved,
            alert.resolved_at || null
          ]);
          console.log(`  ✅ Added: ${alert.title}`);
        } catch (error) {
          console.log(`  ⚠️  Skipped: ${alert.title} (${error.message})`);
        }
      }
    }
    
    // Add more transformers if we have regions available
    try {
      const [availableRegions] = await connection.execute('SELECT id, name FROM app_regions2 LIMIT 5');
      if (availableRegions.length > 0 && existingTransformers.length < 4) {
        console.log('\n⚡ Adding more transformers...');
        
        const newTransformers = [
          {
            serial_number: 'EEU-AA-003',
            name: 'Bole Airport Transformer',
            type: 'distribution',
            capacity_kva: 1250.00,
            voltage_primary: 33.00,
            voltage_secondary: 0.40,
            manufacturer: 'Siemens',
            model: 'GEAFOL',
            year_manufactured: 2022,
            installation_date: '2022-01-20',
            location_name: 'Bole International Airport',
            latitude: 8.98060000,
            longitude: 38.79920000,
            region_id: availableRegions[0].id,
            status: 'operational',
            efficiency_rating: 99.50,
            load_factor: 70.00,
            temperature: 55.00,
            oil_level: 98.00,
            last_maintenance: '2024-10-01',
            next_maintenance: '2025-01-01'
          },
          {
            serial_number: 'EEU-OR-002',
            name: 'Adama Industrial Transformer',
            type: 'distribution',
            capacity_kva: 800.00,
            voltage_primary: 33.00,
            voltage_secondary: 0.40,
            manufacturer: 'ABB',
            model: 'UniGear ZS1',
            year_manufactured: 2018,
            installation_date: '2018-11-12',
            location_name: 'Adama Industrial Park',
            latitude: 8.54000000,
            longitude: 39.26750000,
            region_id: availableRegions.length > 1 ? availableRegions[1].id : availableRegions[0].id,
            status: 'warning',
            efficiency_rating: 96.50,
            load_factor: 87.00,
            temperature: 74.00,
            oil_level: 88.00,
            last_maintenance: '2024-08-15',
            next_maintenance: '2024-11-15'
          }
        ];
        
        for (const transformer of newTransformers) {
          try {
            await connection.execute(`
              INSERT INTO app_transformers (
                serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
                manufacturer, model, year_manufactured, installation_date, location_name,
                latitude, longitude, region_id, status, efficiency_rating, load_factor,
                temperature, oil_level, last_maintenance, next_maintenance
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              transformer.serial_number, transformer.name, transformer.type, transformer.capacity_kva,
              transformer.voltage_primary, transformer.voltage_secondary, transformer.manufacturer,
              transformer.model, transformer.year_manufactured, transformer.installation_date,
              transformer.location_name, transformer.latitude, transformer.longitude,
              transformer.region_id, transformer.status, transformer.efficiency_rating,
              transformer.load_factor, transformer.temperature, transformer.oil_level,
              transformer.last_maintenance, transformer.next_maintenance
            ]);
            console.log(`  ✅ Added: ${transformer.name}`);
          } catch (error) {
            console.log(`  ⚠️  Skipped: ${transformer.name} (${error.message})`);
          }
        }
      }
    } catch (error) {
      console.log('⚠️  Could not add more transformers:', error.message);
    }
    
    // Final verification
    const [finalTransformers] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    const [finalMaintenance] = await connection.execute('SELECT COUNT(*) as count FROM app_maintenance_schedules');
    const [finalAlerts] = await connection.execute('SELECT COUNT(*) as count FROM app_alerts');
    const [finalRegions] = await connection.execute('SELECT COUNT(*) as count FROM app_regions');
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎉 DATA POPULATION COMPLETED!');
    console.log('=' .repeat(50));
    
    console.log('\n📊 FINAL DATABASE SUMMARY:');
    console.log(`  ⚡ Transformers: ${finalTransformers[0].count}`);
    console.log(`  🔧 Maintenance Schedules: ${finalMaintenance[0].count}`);
    console.log(`  🚨 Alerts: ${finalAlerts[0].count}`);
    console.log(`  🗺️  Regions: ${finalRegions[0].count}`);
    
    // Show status distribution
    if (finalTransformers[0].count > 0) {
      const [statusStats] = await connection.execute(`
        SELECT status, COUNT(*) as count
        FROM app_transformers 
        GROUP BY status 
        ORDER BY count DESC
      `);
      
      console.log('\n📈 TRANSFORMER STATUS DISTRIBUTION:');
      statusStats.forEach(stat => {
        console.log(`  • ${stat.status}: ${stat.count} transformers`);
      });
    }
    
    // Show alert summary
    if (finalAlerts[0].count > 0) {
      const [alertStats] = await connection.execute(`
        SELECT 
          severity,
          COUNT(*) as total,
          SUM(CASE WHEN is_resolved = 0 OR is_resolved IS NULL THEN 1 ELSE 0 END) as active
        FROM app_alerts 
        GROUP BY severity 
        ORDER BY 
          CASE severity 
            WHEN 'critical' THEN 1 
            WHEN 'high' THEN 2 
            WHEN 'medium' THEN 3 
            WHEN 'low' THEN 4 
          END
      `);
      
      console.log('\n🚨 ALERT SUMMARY BY SEVERITY:');
      alertStats.forEach(stat => {
        console.log(`  • ${stat.severity}: ${stat.active} active / ${stat.total} total`);
      });
    }
    
    console.log('\n🎯 DASHBOARD IS NOW READY WITH:');
    console.log('  • Real transformer data with various statuses');
    console.log('  • Active maintenance schedules and tasks');
    console.log('  • Live alerts with different severity levels');
    console.log('  • Performance metrics and operational data');
    
    console.log('\n🌟 Your dashboard now has data to display!');
    console.log('🔗 Refresh your browser at: http://localhost:3002');
    console.log('👤 Login: <EMAIL> / password123');
    
  } catch (error) {
    console.error('❌ Error fixing and populating data:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { fixAndPopulateData };

// Run if called directly
if (require.main === module) {
  fixAndPopulateData();
}
