"use client"

import { useState } from 'react'
import { Database, Server, AlertTriangle, CheckCircle, Loader2 } from 'lucide-react'

export default function MigrationPanel() {
  const [isRunning, setIsRunning] = useState(false)
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null)
  const [apiKey, setApiKey] = useState('')
  
  const runMigrations = async () => {
    if (!apiKey) {
      setResult({
        success: false,
        message: 'API key is required'
      })
      return
    }
    
    try {
      setIsRunning(true)
      setResult(null)
      
      const response = await fetch('/api/migrations/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        }
      })
      
      const data = await response.json()
      
      if (response.ok) {
        setResult({
          success: true,
          message: data.message || 'Migrations completed successfully'
        })
      } else {
        setResult({
          success: false,
          message: data.error || 'Failed to run migrations'
        })
      }
    } catch (error) {
      setResult({
        success: false,
        message: error instanceof Error ? error.message : 'An unknown error occurred'
      })
    } finally {
      setIsRunning(false)
    }
  }
  
  return (
    <div className="bg-white rounded-lg border p-6">
      <div className="flex items-center mb-4">
        <Database className="text-blue-500 mr-2" size={24} />
        <h2 className="text-xl font-medium">Database Migrations</h2>
      </div>
      
      <p className="text-gray-600 mb-6">
        Run database migrations to set up advanced features. This will create necessary tables and migrate initial data.
      </p>
      
      <div className="mb-4">
        <label htmlFor="apiKey" className="block text-sm font-medium text-gray-700 mb-1">
          API Key
        </label>
        <input
          type="password"
          id="apiKey"
          value={apiKey}
          onChange={(e) => setApiKey(e.target.value)}
          className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          placeholder="Enter migration API key"
          disabled={isRunning}
        />
      </div>
      
      <div className="flex items-center justify-between mb-6">
        <div className="text-sm text-gray-500 flex items-center">
          <Server size={14} className="mr-1" />
          <span>Target: MySQL Database</span>
        </div>
        
        <button
          onClick={runMigrations}
          disabled={isRunning}
          className={`px-4 py-2 rounded-md text-white ${
            isRunning ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          {isRunning ? (
            <span className="flex items-center">
              <Loader2 size={16} className="mr-2 animate-spin" />
              Running Migrations...
            </span>
          ) : (
            'Run Migrations'
          )}
        </button>
      </div>
      
      {result && (
        <div className={`p-4 rounded-md ${
          result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
        }`}>
          <div className="flex items-start">
            {result.success ? (
              <CheckCircle size={20} className="text-green-500 mr-2 mt-0.5" />
            ) : (
              <AlertTriangle size={20} className="text-red-500 mr-2 mt-0.5" />
            )}
            <div>
              <h3 className={`font-medium ${
                result.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {result.success ? 'Success' : 'Error'}
              </h3>
              <p className={`text-sm ${
                result.success ? 'text-green-700' : 'text-red-700'
              }`}>
                {result.message}
              </p>
            </div>
          </div>
        </div>
      )}
      
      <div className="mt-6 border-t pt-4">
        <h3 className="text-sm font-medium mb-2">Migration Components</h3>
        <ul className="space-y-2 text-sm">
          <li className="flex items-center text-gray-600">
            <div className="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center mr-2">
              <span className="text-green-600 text-xs">1</span>
            </div>
            <span>Dashboard Widgets</span>
          </li>
          <li className="flex items-center text-gray-600">
            <div className="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center mr-2">
              <span className="text-green-600 text-xs">2</span>
            </div>
            <span>Advanced Transformer Features</span>
          </li>
          <li className="flex items-center text-gray-600">
            <div className="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center mr-2">
              <span className="text-green-600 text-xs">3</span>
            </div>
            <span>Maintenance Management</span>
          </li>
          <li className="flex items-center text-gray-600">
            <div className="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center mr-2">
              <span className="text-green-600 text-xs">4</span>
            </div>
            <span>Outage Management</span>
          </li>
          <li className="flex items-center text-gray-600">
            <div className="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center mr-2">
              <span className="text-green-600 text-xs">5</span>
            </div>
            <span>Weather Impact Analysis</span>
          </li>
        </ul>
      </div>
    </div>
  )
}
