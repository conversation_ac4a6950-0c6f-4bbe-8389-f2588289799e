import { NextRequest, NextResponse } from 'next/server'
import { getPool } from '@/src/lib/mysql-connection'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`⚡ API: Fetching transformer ${params.id} from MySQL...`)

    const pool = getPool()

    // Get transformer by ID
    const [transformers] = await pool.execute(`
      SELECT
        t.*,
        r.name as region_name,
        sc.name as service_center_name
      FROM app_transformers t
      LEFT JOIN app_regions r ON t.region_id = r.id
      LEFT JOIN app_service_centers sc ON t.service_center_id = sc.id
      WHERE t.id = ?
    `, [params.id])

    if (!Array.isArray(transformers) || transformers.length === 0) {
      console.log(`❌ API: Transformer ${params.id} not found`)
      return NextResponse.json(
        {
          success: false,
          error: 'Transformer not found',
          message: `Transformer with ID ${params.id} does not exist`
        },
        { status: 404 }
      )
    }

    const transformer = transformers[0] as any

    // Transform the data to match the expected format
    const transformedData = {
      id: transformer.id,
      createdAt: transformer.created_at,
      updatedAt: transformer.updated_at || transformer.created_at,
      serialNumber: transformer.serial_number,
      serial_number: transformer.serial_number,
      name: transformer.name,
      status: transformer.status,
      type: transformer.type,
      manufacturer: transformer.manufacturer,
      model: transformer.model,
      year_manufactured: transformer.year_manufactured,
      manufactureDate: transformer.year_manufactured,
      installation_date: transformer.installation_date,
      installationDate: transformer.installation_date,
      lastMaintenanceDate: transformer.last_maintenance,
      last_maintenance: transformer.last_maintenance,
      nextMaintenanceDate: transformer.next_maintenance,
      next_maintenance: transformer.next_maintenance,
      capacity: transformer.capacity_kva,
      capacity_kva: transformer.capacity_kva,
      voltage: {
        primary: transformer.voltage_primary,
        secondary: transformer.voltage_secondary
      },
      voltage_primary: transformer.voltage_primary,
      voltage_secondary: transformer.voltage_secondary,
      regionId: transformer.region_id,
      region_id: transformer.region_id,
      serviceCenterId: transformer.service_center_id,
      service_center_id: transformer.service_center_id,
      location: {
        address: transformer.location_name,
        coordinates: {
          lat: parseFloat(transformer.latitude) || 0,
          lng: parseFloat(transformer.longitude) || 0
        }
      },
      location_name: transformer.location_name,
      latitude: transformer.latitude,
      longitude: transformer.longitude,
      metrics: {
        temperature: parseFloat(transformer.temperature) || 0,
        loadPercentage: parseFloat(transformer.load_factor) || 0,
        oilLevel: parseFloat(transformer.oil_level) || 0,
        healthIndex: parseFloat(transformer.efficiency_rating) || 0
      },
      temperature: transformer.temperature,
      load_factor: transformer.load_factor,
      oil_level: transformer.oil_level,
      efficiency_rating: transformer.efficiency_rating,
      regionName: transformer.region_name,
      serviceCenterName: transformer.service_center_name
    }

    console.log(`✅ API: Transformer ${params.id} fetched successfully from MySQL`)

    return NextResponse.json({
      success: true,
      data: transformedData,
      source: 'MySQL Database',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ API: Error fetching transformer:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Database error',
        message: 'Failed to fetch transformer from database',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`⚡ API: Updating transformer ${params.id} in MySQL...`)

    const body = await request.json()
    const pool = getPool()

    // Build update query dynamically based on provided fields
    const updateFields = []
    const updateValues = []

    const fieldMappings = {
      name: 'name',
      serialNumber: 'serial_number',
      status: 'status',
      type: 'type',
      manufacturer: 'manufacturer',
      model: 'model',
      capacity: 'capacity_kva',
      voltagePrimary: 'voltage_primary',
      voltageSecondary: 'voltage_secondary',
      locationName: 'location_name',
      latitude: 'latitude',
      longitude: 'longitude',
      regionId: 'region_id',
      serviceCenterId: 'service_center_id',
      temperature: 'temperature',
      loadFactor: 'load_factor',
      oilLevel: 'oil_level',
      efficiencyRating: 'efficiency_rating'
    }

    Object.entries(fieldMappings).forEach(([bodyField, dbField]) => {
      if (body[bodyField] !== undefined) {
        updateFields.push(`${dbField} = ?`)
        updateValues.push(body[bodyField])
      }
    })

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid fields to update' },
        { status: 400 }
      )
    }

    // Add updated_at timestamp
    updateFields.push('updated_at = NOW()')
    updateValues.push(params.id)

    const updateQuery = `
      UPDATE app_transformers
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `

    const [result] = await pool.execute(updateQuery, updateValues)

    if ((result as any).affectedRows === 0) {
      return NextResponse.json(
        { success: false, error: 'Transformer not found' },
        { status: 404 }
      )
    }

    console.log(`✅ API: Transformer ${params.id} updated successfully`)

    return NextResponse.json({
      success: true,
      message: 'Transformer updated successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ API: Error updating transformer:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Database error',
        message: 'Failed to update transformer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`⚡ API: Deleting transformer ${params.id} from MySQL...`)

    const pool = getPool()

    // Check if transformer exists
    const [existing] = await pool.execute(
      'SELECT id FROM app_transformers WHERE id = ?',
      [params.id]
    )

    if (!Array.isArray(existing) || existing.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Transformer not found' },
        { status: 404 }
      )
    }

    // Delete transformer
    const [result] = await pool.execute(
      'DELETE FROM app_transformers WHERE id = ?',
      [params.id]
    )

    console.log(`✅ API: Transformer ${params.id} deleted successfully`)

    return NextResponse.json({
      success: true,
      message: 'Transformer deleted successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ API: Error deleting transformer:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Database error',
        message: 'Failed to delete transformer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
