"use client"

import { Transformer } from "@/src/types/transformer"
import { transformerCache } from "@/src/lib/cache"
import { getDbService } from "@/src/lib/db-loader"

// Create a singleton store for transformers
class TransformerStore {
  private transformers: Transformer[] = []
  private listeners: Array<() => void> = []
  private initialized: boolean = false

  constructor() {
    // We'll load transformers from the database when needed
    this.initialized = false
  }

  // Initialize the store with data from the database
  private async initialize() {
    if (this.initialized) return;

    try {
      // Get the database service
      const dbService = await getDbService();

      // Get transformers from the database
      const dbTransformers = dbService.transformers.getAll();

      // Convert database transformers to our application format
      const transformerPromises = dbTransformers.map(dbTransformer => this.convertDbTransformerToAppFormat(dbTransformer));
      this.transformers = await Promise.all(transformerPromises);

      this.initialized = true;
    } catch (error) {
      console.error("Error initializing transformer store:", error);
      // Fallback to empty array if database access fails
      this.transformers = [];
    }
  }

  // Convert database transformer to application format
  private async convertDbTransformerToAppFormat(dbTransformer: any): Promise<Transformer> {
    // Get region and service center names asynchronously
    const regionName = await this.getRegionNameById(dbTransformer.regionId);
    const serviceCenterName = await this.getServiceCenterNameById(dbTransformer.serviceCenterId);

    return {
      id: dbTransformer.id,
      serialNumber: dbTransformer.serialNumber,
      manufacturer: dbTransformer.manufacturer,
      model: dbTransformer.model,
      type: dbTransformer.type,
      capacity: dbTransformer.capacity.toString(),
      voltageRating: `${dbTransformer.voltage.primary}/${dbTransformer.voltage.secondary} kV`,
      installationDate: dbTransformer.installationDate,
      lastMaintenanceDate: dbTransformer.lastMaintenanceDate || undefined,
      lastInspectionDate: dbTransformer.lastMaintenanceDate || undefined,
      status: this.mapDbStatusToAppStatus(dbTransformer.status),
      location: {
        latitude: dbTransformer.location.coordinates.lat.toString(),
        longitude: dbTransformer.location.coordinates.lng.toString(),
        region: regionName,
        serviceCenter: serviceCenterName,
        address: dbTransformer.location.address,
        installationSite: "Distribution Network"
      },
      manufacturingYear: new Date(dbTransformer.manufactureDate).getFullYear().toString(),
      primaryVoltage: `${dbTransformer.voltage.primary} kV`,
      secondaryVoltage: `${dbTransformer.voltage.secondary} V`,
      connectionType: "Dyn11",
      phaseCount: "3",
      impedance: "4.5%",
      oilType: "Mineral Oil",
      coolingType: "ONAN",
      weight: `${Math.round(dbTransformer.capacity * 3 + 200)} kg`,
      dimensions: `${800 + dbTransformer.capacity/2}x${600 + dbTransformer.capacity/4}x${1000 + dbTransformer.capacity/3} mm`,
      temperatureRise: "55°C",
      noiseLevel: `${Math.round(45 + dbTransformer.capacity/20)} dB`,
      standardCompliance: ["IEC 60076", "ES IEC 60076-1:2011", "ES IEC 60076-2:2011"],
      feederName: `Feeder ${Math.floor(Math.random() * 5) + 1}`,
      substationName: `${this.getServiceCenterNameById(dbTransformer.serviceCenterId)} Substation`,
      // We'll add mock data for these fields since they're not in the database
      inspectionRecords: [],
      testResults: [],
      maintenanceHistory: [],
      abnormalityReports: [],
      meggerTests: []
    };
  }

  // Map database status to application status
  private mapDbStatusToAppStatus(dbStatus: string): "Operational" | "Maintenance" | "Critical" | "Burnt" | "Offline" {
    const statusMap: Record<string, "Operational" | "Maintenance" | "Critical" | "Burnt" | "Offline"> = {
      "operational": "Operational",
      "warning": "Maintenance",
      "critical": "Critical",
      "offline": "Offline",
      "maintenance": "Maintenance"
    };

    return statusMap[dbStatus] || "Operational";
  }

  // Get region name by ID
  private async getRegionNameById(regionId: string): Promise<string> {
    try {
      const dbService = await getDbService();
      const region = dbService.regions.getById(regionId);
      return region?.name || "Unknown Region";
    } catch (error) {
      console.error("Error getting region name:", error);
      return "Unknown Region";
    }
  }

  // Get service center name by ID
  private async getServiceCenterNameById(serviceCenterId: string): Promise<string> {
    try {
      const dbService = await getDbService();
      const serviceCenter = dbService.serviceCenters.getById(serviceCenterId);
      return serviceCenter?.name || "Unknown Service Center";
    } catch (error) {
      console.error("Error getting service center name:", error);
      return "Unknown Service Center";
    }
  }

  // Get all transformers
  public async getAllTransformers(): Promise<Transformer[]> {
    try {
      await this.initialize();

      // If transformers array is empty, use mock data
      if (this.transformers.length === 0) {
        console.warn('No transformers found in store, using mock data');
        // Import mock data from mock-transformers
        const { mockTransformers } = await import('./mock-transformers');
        return mockTransformers;
      }

      return [...this.transformers];
    } catch (error) {
      console.error('Error getting all transformers from store:', error);
      // Import mock data from mock-transformers as fallback
      const { mockTransformers } = await import('./mock-transformers');
      return mockTransformers;
    }
  }

  // Get transformer by ID
  public async getTransformerById(id: string): Promise<Transformer | null> {
    await this.initialize();

    // First try to find in the local cache
    const cachedTransformer = this.transformers.find(t => t.id === id);
    if (cachedTransformer) return cachedTransformer;

    // If not found in cache, try to get from the database
    try {
      const dbService = await getDbService();
      const dbTransformer = dbService.transformers.getById(id);
      if (dbTransformer) {
        const transformer = this.convertDbTransformerToAppFormat(dbTransformer);
        // Add to local cache
        this.transformers.push(transformer);
        return transformer;
      }
    } catch (error) {
      console.error(`Error getting transformer with ID ${id}:`, error);
    }

    return null;
  }

  // Update a transformer
  public async updateTransformer(id: string, updates: Partial<Transformer>): Promise<Transformer | null> {
    await this.initialize();

    const index = this.transformers.findIndex(t => t.id === id);
    if (index === -1) return null;

    // Create a new transformer object with the updates
    const updatedTransformer = {
      ...this.transformers[index],
      ...updates,
      // Handle nested updates for location
      location: updates.location
        ? { ...this.transformers[index].location, ...updates.location }
        : this.transformers[index].location
    };

    // Update the transformer in the array
    this.transformers[index] = updatedTransformer;

    // Update in the database
    try {
      // Convert app transformer to database format
      const dbUpdates = this.convertAppUpdatesToDbFormat(updates);

      // Get the database service and update in the database
      const dbService = await getDbService();
      dbService.transformers.update(id, dbUpdates);
    } catch (error) {
      console.error(`Error updating transformer with ID ${id}:`, error);
    }

    // Clear cache
    transformerCache.clear('all_transformers');
    transformerCache.clear(`transformer_${id}`);

    // Notify listeners
    this.notifyListeners();

    return updatedTransformer;
  }

  // Convert app updates to database format
  private convertAppUpdatesToDbFormat(updates: Partial<Transformer>): any {
    const dbUpdates: any = {};

    // Map status
    if (updates.status) {
      dbUpdates.status = this.mapAppStatusToDbStatus(updates.status);
    }

    // Map location
    if (updates.location) {
      dbUpdates.location = {
        coordinates: {}
      };

      if (updates.location.latitude) {
        dbUpdates.location.coordinates.lat = parseFloat(updates.location.latitude);
      }

      if (updates.location.longitude) {
        dbUpdates.location.coordinates.lng = parseFloat(updates.location.longitude);
      }

      if (updates.location.address) {
        dbUpdates.location.address = updates.location.address;
      }
    }

    return dbUpdates;
  }

  // Map application status to database status
  private mapAppStatusToDbStatus(appStatus: string): string {
    const statusMap: Record<string, string> = {
      "Operational": "operational",
      "Maintenance": "maintenance",
      "Critical": "critical",
      "Burnt": "critical",
      "Offline": "offline"
    };

    return statusMap[appStatus] || "operational";
  }

  // Update transformer status
  public async updateTransformerStatus(id: string, status: "Operational" | "Maintenance" | "Critical" | "Burnt" | "Offline"): Promise<Transformer | null> {
    return this.updateTransformer(id, { status });
  }

  // Add a listener for updates
  public addListener(listener: () => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // Notify all listeners
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }

  // Filter transformers by criteria
  public async filterTransformers(filters: any): Promise<Transformer[]> {
    await this.initialize();

    // Convert app filters to database filters
    const dbFilters: any = {};

    // Map status filter
    if (filters.status && filters.status.length > 0) {
      dbFilters.status = filters.status.map((s: string) => this.mapAppStatusToDbStatus(s));
    }

    // Map region filter
    if (filters.region && filters.region.length > 0) {
      // We need to convert region names to region IDs
      const regionIds = await this.getRegionIdsByNames(filters.region);
      if (regionIds.length > 0) {
        dbFilters.regionId = regionIds;
      }
    }

    // Map service center filter
    if (filters.serviceCenter && filters.serviceCenter.length > 0) {
      // We need to convert service center names to service center IDs
      const serviceCenterIds = await this.getServiceCenterIdsByNames(filters.serviceCenter);
      if (serviceCenterIds.length > 0) {
        dbFilters.serviceCenterId = serviceCenterIds;
      }
    }

    // Map capacity range filter
    if (filters.capacity) {
      dbFilters.capacity = {
        $gte: filters.capacity[0],
        $lte: filters.capacity[1]
      };
    }

    // Map search filter
    if (filters.search && filters.search.trim() !== "") {
      dbFilters.search = filters.search.trim();
    }

    try {
      // Get the database service and use it to filter transformers
      const dbService = await getDbService();
      const dbTransformers = dbService.transformers.find(dbFilters);

      // Convert database transformers to application format
      const transformerPromises = dbTransformers.map(dbTransformer => this.convertDbTransformerToAppFormat(dbTransformer));
      return await Promise.all(transformerPromises);
    } catch (error) {
      console.error("Error filtering transformers:", error);

      // Fallback to filtering the local cache
      return this.transformers.filter(transformer => {
        // Apply status filter
        if (filters.status && filters.status.length > 0 && !filters.status.includes(transformer.status)) {
          return false;
        }

        // Apply region filter
        if (filters.region && filters.region.length > 0 && !filters.region.includes(transformer.location.region)) {
          return false;
        }

        // Apply service center filter
        if (filters.serviceCenter && filters.serviceCenter.length > 0 &&
            !filters.serviceCenter.includes(transformer.location.serviceCenter)) {
          return false;
        }

        // Apply capacity range filter
        if (filters.capacity &&
            (parseInt(transformer.capacity) < filters.capacity[0] ||
             parseInt(transformer.capacity) > filters.capacity[1])) {
          return false;
        }

        // Apply search filter
        if (filters.search && filters.search.trim() !== "") {
          const searchLower = filters.search.toLowerCase();
          return (
            transformer.serialNumber.toLowerCase().includes(searchLower) ||
            transformer.manufacturer.toLowerCase().includes(searchLower) ||
            transformer.model.toLowerCase().includes(searchLower) ||
            transformer.location.region.toLowerCase().includes(searchLower) ||
            (transformer.location.serviceCenter &&
             transformer.location.serviceCenter.toLowerCase().includes(searchLower))
          );
        }

        return true;
      });
    }
  }

  // Get region IDs by names
  private async getRegionIdsByNames(regionNames: string[]): Promise<string[]> {
    try {
      const dbService = await getDbService();
      const regions = dbService.regions.getAll();
      const regionIds: string[] = [];

      for (const name of regionNames) {
        const region = regions.find(r =>
          r.name.toLowerCase() === name.toLowerCase() ||
          r.code.toLowerCase() === name.toLowerCase()
        );

        if (region) {
          regionIds.push(region.id);
        }
      }

      return regionIds;
    } catch (error) {
      console.error("Error getting region IDs by names:", error);
      return [];
    }
  }

  // Get service center IDs by names
  private async getServiceCenterIdsByNames(serviceCenterNames: string[]): Promise<string[]> {
    try {
      const dbService = await getDbService();
      const serviceCenters = dbService.serviceCenters.getAll();
      const serviceCenterIds: string[] = [];

      for (const name of serviceCenterNames) {
        const serviceCenter = serviceCenters.find(sc =>
          sc.name.toLowerCase() === name.toLowerCase() ||
          sc.code.toLowerCase() === name.toLowerCase()
        );

        if (serviceCenter) {
          serviceCenterIds.push(serviceCenter.id);
        }
      }

      return serviceCenterIds;
    } catch (error) {
      console.error("Error getting service center IDs by names:", error);
      return [];
    }
  }
}

// Export singleton instance
export const transformerStore = new TransformerStore()
