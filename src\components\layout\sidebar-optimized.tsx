"use client"

import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useState, useEffect, useMemo, useCallback } from "react"
import {
  ChevronDown,
  ChevronRight,
  LayoutDashboard,
  BarChart3,
  TrendingUp,
  Zap,
  Settings,
  MapPin,
  FileText,
  Wrench,
  Calendar,
  ClipboardList,
  Shield,
  Search,
  Package,
  Users,
  Wifi,
  Activity,
  PieChart,
  Building,
  Bell,
  AlertTriangle,
  Cloud
} from "lucide-react"
import { cn } from "@/src/lib/utils"
import { useSidebar } from "@/src/components/layout/sidebar-provider"
import { Button } from "@/src/components/ui/button"
import { useAuth } from "@/src/features/auth/context/auth-context"
import { useOptimizedMount, useDebounce } from "@/src/hooks/use-optimized-data"

interface NavSubItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  allowedRoles: string[]
  requiredPermissions?: Array<{
    resource: string
    action: string
  }>
}

interface NavItem {
  title: string
  href?: string
  icon: React.ComponentType<{ className?: string }>
  allowedRoles: string[]
  requiredPermissions?: Array<{
    resource: string
    action: string
  }>
  subItems?: NavSubItem[]
}

const navItems: NavItem[] = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    allowedRoles: [
      "super_admin",
      "national_asset_manager",
      "national_maintenance_manager",
      "regional_admin",
      "regional_asset_manager",
      "regional_maintenance_engineer",
      "service_center_manager",
      "field_technician",
      "customer_service_agent",
      "audit_compliance_officer",
    ],
    subItems: [
      {
        title: "Overview",
        href: "/dashboard",
        icon: BarChart3,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
          "customer_service_agent",
          "audit_compliance_officer",
        ],
      },
      {
        title: "Analytics",
        href: "/dashboard/analytics",
        icon: TrendingUp,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
    ],
  },
  {
    title: "Transformers",
    icon: Zap,
    allowedRoles: [
      "super_admin",
      "national_asset_manager",
      "national_maintenance_manager",
      "regional_admin",
      "regional_asset_manager",
      "regional_maintenance_engineer",
      "service_center_manager",
      "field_technician",
    ],
    requiredPermissions: [{ resource: "transformers", action: "read" }],
    subItems: [
      {
        title: "Overview",
        href: "/transformers",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Unified Management",
        href: "/transformers/unified-management",
        icon: Settings,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Map View",
        href: "/transformers/map",
        icon: MapPin,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
    ],
  },
  {
    title: "Maintenance",
    icon: Wrench,
    allowedRoles: [
      "super_admin",
      "national_maintenance_manager",
      "regional_admin",
      "regional_maintenance_engineer",
      "service_center_manager",
      "field_technician",
    ],
    requiredPermissions: [{ resource: "maintenance", action: "read" }],
    subItems: [
      {
        title: "Overview",
        href: "/maintenance",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Scheduled Tasks",
        href: "/maintenance/scheduled-tasks",
        icon: Calendar,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Work Orders",
        href: "/maintenance?tab=workorders",
        icon: ClipboardList,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
    ],
  },
  {
    title: "Smart Meters",
    icon: Wifi,
    allowedRoles: [
      "super_admin",
      "national_asset_manager",
      "regional_admin",
      "regional_asset_manager",
      "service_center_manager",
    ],
    subItems: [
      {
        title: "Overview",
        href: "/smart-meters",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "regional_admin",
          "regional_asset_manager",
          "service_center_manager",
        ],
      },
      {
        title: "Real-time Monitoring",
        href: "/smart-meters?tab=monitoring",
        icon: Activity,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "regional_admin",
          "regional_asset_manager",
          "service_center_manager",
        ],
      },
    ],
  },
  {
    title: "Reports",
    icon: BarChart3,
    allowedRoles: [
      "super_admin",
      "national_asset_manager",
      "national_maintenance_manager",
      "regional_admin",
      "regional_asset_manager",
      "regional_maintenance_engineer",
      "service_center_manager",
      "audit_compliance_officer",
    ],
    subItems: [
      {
        title: "Overview",
        href: "/reports",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "audit_compliance_officer",
        ],
      },
    ],
  },
  {
    title: "Alerts",
    icon: Bell,
    allowedRoles: [
      "super_admin",
      "national_maintenance_manager",
      "regional_admin",
      "regional_maintenance_engineer",
      "service_center_manager",
      "field_technician",
    ],
    subItems: [
      {
        title: "Overview",
        href: "/alerts",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Active Alerts",
        href: "/alerts?tab=active",
        icon: AlertTriangle,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
    ],
  },
  {
    title: "Users",
    icon: Users,
    allowedRoles: ["super_admin", "national_asset_manager", "regional_admin"],
    subItems: [
      {
        title: "User Management",
        href: "/users",
        icon: Users,
        allowedRoles: ["super_admin", "national_asset_manager", "regional_admin"],
      },
      {
        title: "Roles & Permissions",
        href: "/users?tab=roles",
        icon: Shield,
        allowedRoles: ["super_admin", "national_asset_manager"],
      },
    ],
  },
  {
    title: "Settings",
    icon: Settings,
    allowedRoles: ["super_admin", "national_asset_manager", "regional_admin"],
    subItems: [
      {
        title: "Overview",
        href: "/settings",
        icon: LayoutDashboard,
        allowedRoles: ["super_admin", "national_asset_manager", "regional_admin"],
      },
    ],
  },
]

export function SidebarOptimized() {
  const pathname = usePathname()
  const { isOpen } = useSidebar()
  const { user, hasRole, hasPermission } = useAuth()
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const [isInitialized, setIsInitialized] = useState(false)

  // Debounce pathname changes to reduce re-renders
  const debouncedPathname = useDebounce(pathname, 100)

  // Memoized filtered nav items to prevent unnecessary recalculations
  const filteredNavItems = useMemo(() => {
    if (!user) return []

    return navItems.filter((item) => {
      const hasRequiredRole = hasRole(item.allowedRoles)
      const hasRequiredPermissions = item.requiredPermissions
        ? item.requiredPermissions.every(({ resource, action }) => hasPermission(resource, action))
        : true

      return hasRequiredRole && hasRequiredPermissions
    })
  }, [user, hasRole, hasPermission])

  // Memoized function to filter sub-items
  const filterSubItems = useCallback((subItems: NavSubItem[] | undefined) => {
    if (!subItems || !user) return []

    return subItems.filter((subItem) => {
      const hasRequiredRole = hasRole(subItem.allowedRoles)
      const hasRequiredPermissions = subItem.requiredPermissions
        ? subItem.requiredPermissions.every(({ resource, action }) => hasPermission(resource, action))
        : true
      return hasRequiredRole && hasRequiredPermissions
    })
  }, [user, hasRole, hasPermission])

  // Memoized active state checkers
  const isMainItemActive = useCallback((item: NavItem) => {
    if (item.href) {
      return debouncedPathname === item.href || debouncedPathname.startsWith(item.href + '/')
    }
    return false
  }, [debouncedPathname])

  const isSubItemActive = useCallback((subItems: NavSubItem[] | undefined) => {
    if (!subItems) return false
    return subItems.some(subItem =>
      debouncedPathname === subItem.href || debouncedPathname.startsWith(subItem.href + '/')
    )
  }, [debouncedPathname])

  // Optimized toggle function with useCallback
  const toggleExpanded = useCallback((title: string) => {
    setExpandedItems(prev => {
      const isExpanded = prev.includes(title)
      if (isExpanded) {
        return prev.filter(item => item !== title)
      } else {
        return [...prev, title]
      }
    })
  }, [])

  // Auto-expand sections that contain the current active page (optimized)
  useOptimizedMount(() => {
    const itemsToExpand: string[] = []

    filteredNavItems.forEach((item) => {
      if (item.subItems && (isMainItemActive(item) || isSubItemActive(item.subItems))) {
        itemsToExpand.push(item.title)
      }
    })

    if (itemsToExpand.length > 0) {
      setExpandedItems(prev => {
        const newItems = itemsToExpand.filter(item => !prev.includes(item))
        return newItems.length > 0 ? [...prev, ...newItems] : prev
      })
    }

    setIsInitialized(true)
  }, 200) // Small delay to prevent flash

  // Early return if not open or user not loaded
  if (!isOpen || !user || !isInitialized) {
    return null
  }

  return (
    <div className="fixed inset-y-0 left-0 z-40 flex w-64 flex-col bg-gradient-to-b from-white to-gray-50 border-r border-gray-200 shadow-lg md:relative">
      {/* Header - Memoized to prevent re-renders */}
      <div className="flex h-16 items-center px-4 border-b border-gray-100 bg-white/80 backdrop-blur-sm">
        <Link href="/dashboard" className="flex items-center gap-3 group">
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-green-100 to-green-200 shadow-sm group-hover:shadow-md transition-all duration-200">
            <Image
              src="/eeu-logo.svg"
              alt="Ethiopian Electric Utility Logo"
              width={20}
              height={20}
              className="h-5 w-5"
              priority
            />
          </div>
          <div className="flex flex-col">
            <span className="font-bold text-gray-900 text-sm tracking-tight">EEU-DTMS</span>
            <span className="text-xs text-gray-500 font-medium">Ethiopian Electric Utility</span>
          </div>
        </Link>
      </div>

      {/* Navigation - Optimized rendering */}
      <div className="flex-1 overflow-auto py-3">
        <nav className="space-y-2 px-3">
          {filteredNavItems.map((item) => {
            const filteredSubItems = filterSubItems(item.subItems)
            const hasSubItems = filteredSubItems.length > 0
            const isExpanded = expandedItems.includes(item.title)
            const isActive = isMainItemActive(item) || isSubItemActive(item.subItems)

            return (
              <div key={item.title} className="space-y-1">
                {/* Main Item */}
                {item.href && !hasSubItems ? (
                  // Direct link item
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 hover:bg-white hover:shadow-sm",
                      isActive
                        ? "bg-white text-green-700 shadow-sm border border-green-100"
                        : "text-gray-700 hover:text-gray-900"
                    )}
                  >
                    <item.icon className={cn(
                      "h-4 w-4 transition-colors",
                      isActive ? "text-green-600" : "text-gray-500"
                    )} />
                    <span className="truncate">{item.title}</span>
                  </Link>
                ) : (
                  // Expandable item
                  <Button
                    variant="ghost"
                    onClick={() => toggleExpanded(item.title)}
                    className={cn(
                      "w-full justify-start gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 hover:bg-white hover:shadow-sm",
                      isActive
                        ? "bg-white text-green-700 shadow-sm border border-green-100"
                        : "text-gray-700 hover:text-gray-900"
                    )}
                  >
                    <item.icon className={cn(
                      "h-4 w-4 transition-colors",
                      isActive ? "text-green-600" : "text-gray-500"
                    )} />
                    <span className="flex-1 truncate text-left">{item.title}</span>
                    {hasSubItems && (
                      <div className="ml-auto">
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4 transition-transform duration-200" />
                        ) : (
                          <ChevronRight className="h-4 w-4 transition-transform duration-200" />
                        )}
                      </div>
                    )}
                  </Button>
                )}

                {/* Sub Items - Only render when expanded */}
                {hasSubItems && isExpanded && (
                  <div className="ml-4 space-y-1 border-l border-gray-200 pl-4">
                    {filteredSubItems.map((subItem) => {
                      const isSubActive = debouncedPathname === subItem.href ||
                                         debouncedPathname.startsWith(subItem.href + '/')

                      return (
                        <Link
                          key={subItem.href}
                          href={subItem.href}
                          className={cn(
                            "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all duration-200 hover:bg-white hover:shadow-sm",
                            isSubActive
                              ? "bg-white text-green-700 shadow-sm border border-green-100 font-medium"
                              : "text-gray-600 hover:text-gray-900"
                          )}
                        >
                          <subItem.icon className={cn(
                            "h-3.5 w-3.5 transition-colors",
                            isSubActive ? "text-green-600" : "text-gray-400"
                          )} />
                          <span className="truncate">{subItem.title}</span>
                        </Link>
                      )
                    })}
                  </div>
                )}
              </div>
            )
          })}
        </nav>
      </div>

      {/* Footer - User info */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 rounded-full bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
            <span className="text-xs font-semibold text-green-700">
              {user.firstName?.[0]}{user.lastName?.[0]}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user.firstName} {user.lastName}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {user.role?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
