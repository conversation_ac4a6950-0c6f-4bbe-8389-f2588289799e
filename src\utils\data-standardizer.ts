/**
 * Data Standardization Utilities
 * Helper functions to convert between different data formats and ensure consistency
 */

import {
  StandardTransformer,
  MaintenanceTask,
  Alert,
  TransformerStatus,
  MaintenanceStatus,
  Priority,
  AlertSeverity,
  APIResponse
} from '@/src/types/unified'

// ===== TRANSFORMER STANDARDIZATION =====

/**
 * Convert legacy transformer data to standardized format
 */
export function standardizeTransformer(legacyData: any): StandardTransformer {
  return {
    // Core Identity
    id: String(legacyData.id || legacyData.transformer_id),
    serialNumber: legacyData.serial_number || legacyData.serialNumber || '',
    name: legacyData.name || legacyData.transformer_name || '',
    
    // Technical Specifications
    type: legacyData.type || 'distribution',
    manufacturer: legacyData.manufacturer || '',
    model: legacyData.model || '',
    kvaRating: Number(legacyData.kva_rating || legacyData.capacity || legacyData.kvaRating || 0),
    voltage: {
      primary: Number(legacyData.voltage_primary || legacyData.voltage?.primary || 0),
      secondary: Number(legacyData.voltage_secondary || legacyData.voltage?.secondary || 0)
    },
    
    // Status & Health
    status: standardizeStatus(legacyData.status) as TransformerStatus,
    healthScore: Number(legacyData.health_score || legacyData.healthIndex || legacyData.healthScore || 0),
    
    // Location
    location: {
      name: legacyData.location || legacyData.location_name || '',
      region: legacyData.region_name || legacyData.region || '',
      serviceCenter: legacyData.service_center_name || legacyData.serviceCenter || '',
      address: legacyData.address || legacyData.location_name || '',
      coordinates: {
        lat: Number(legacyData.latitude || legacyData.coordinates?.lat || 0),
        lng: Number(legacyData.longitude || legacyData.coordinates?.lng || 0)
      }
    },
    
    // Performance Metrics
    metrics: {
      loadPercentage: Number(legacyData.load_percentage || legacyData.load || 0),
      temperature: Number(legacyData.temperature || legacyData.metrics?.temperature || 0),
      efficiency: Number(legacyData.efficiency || legacyData.efficiency_rating || 0),
      uptimePercentage: Number(legacyData.uptime_percentage || 0),
      healthScore: Number(legacyData.health_score || legacyData.healthIndex || 0),
      oilLevel: Number(legacyData.oil_level || legacyData.metrics?.oilLevel || 0),
      powerFactor: Number(legacyData.power_factor || legacyData.metrics?.powerFactor || 0)
    },
    
    // Maintenance
    maintenance: {
      lastDate: standardizeDate(legacyData.last_maintenance || legacyData.lastMaintenance),
      nextDate: standardizeDate(legacyData.next_maintenance || legacyData.nextMaintenance),
      activeAlerts: Number(legacyData.active_alerts || 0),
      totalMaintenanceHours: Number(legacyData.total_maintenance_hours || 0),
      averageDowntime: Number(legacyData.average_downtime || 0)
    },
    
    // Installation
    installationDate: standardizeDate(legacyData.installation_date || legacyData.installationDate),
    yearManufactured: legacyData.year_manufactured || legacyData.yearManufactured,
    
    // Timestamps
    createdAt: standardizeDate(legacyData.created_at || legacyData.createdAt || new Date().toISOString()),
    updatedAt: standardizeDate(legacyData.updated_at || legacyData.updatedAt || new Date().toISOString()),
    
    // Additional Properties
    tags: legacyData.tags || [],
    notes: legacyData.notes || ''
  }
}

/**
 * Convert legacy maintenance task data to standardized format
 */
export function standardizeMaintenanceTask(legacyData: any): MaintenanceTask {
  return {
    // Core Information
    id: String(legacyData.id),
    transformerId: String(legacyData.transformer_id || legacyData.transformerId),
    transformerName: legacyData.transformer_name || legacyData.transformerName || '',
    transformerLocation: legacyData.transformer_location || legacyData.transformerLocation || '',
    
    // Task Details
    type: legacyData.type || legacyData.maintenance_type || 'preventive',
    status: standardizeMaintenanceStatus(legacyData.status) as MaintenanceStatus,
    priority: standardizePriority(legacyData.priority) as Priority,
    title: legacyData.title || '',
    description: legacyData.description || '',
    
    // Scheduling
    scheduledDate: standardizeDate(legacyData.scheduled_date || legacyData.scheduledDate),
    estimatedDuration: Number(legacyData.estimated_duration || legacyData.estimatedDuration || 0),
    actualDuration: legacyData.actual_duration || legacyData.actualDuration,
    
    // Assignment
    assignedTechnicianId: legacyData.assigned_technician_id || legacyData.assignedTo,
    assignedTechnicianName: legacyData.technician_name || legacyData.assignedTechnicianName,
    assignedTechnicianPhone: legacyData.technician_phone || legacyData.assignedTechnicianPhone,
    
    // Progress
    completedDate: legacyData.completed_date || legacyData.completedDate,
    completionNotes: legacyData.completion_notes || legacyData.completionNotes,
    
    // Resources
    requiredParts: legacyData.required_parts || legacyData.parts || [],
    totalCost: legacyData.total_cost || legacyData.cost,
    
    // Urgency
    daysUntilDue: Number(legacyData.days_until_due || 0),
    urgencyStatus: legacyData.urgency_status || 'normal',
    
    // Timestamps
    createdAt: standardizeDate(legacyData.created_at || legacyData.createdAt || new Date().toISOString()),
    updatedAt: standardizeDate(legacyData.updated_at || legacyData.updatedAt || new Date().toISOString()),
    
    // Attachments
    attachments: legacyData.attachments || []
  }
}

/**
 * Convert legacy alert data to standardized format
 */
export function standardizeAlert(legacyData: any): Alert {
  return {
    // Core Information
    id: String(legacyData.id),
    title: legacyData.title || '',
    description: legacyData.description || legacyData.message || '',
    severity: standardizeAlertSeverity(legacyData.severity) as AlertSeverity,
    category: legacyData.category || 'system',
    
    // Status
    status: legacyData.status || 'active',
    
    // Assignment
    assignedToId: legacyData.assigned_to_id || legacyData.assignedTo,
    assignedToName: legacyData.assigned_name || legacyData.assignedToName,
    acknowledgedAt: legacyData.acknowledged_at || legacyData.acknowledgedAt,
    acknowledgedById: legacyData.acknowledged_by_id || legacyData.acknowledgedById,
    resolvedAt: legacyData.resolved_at || legacyData.resolvedAt,
    resolvedById: legacyData.resolved_by_id || legacyData.resolvedById,
    
    // Source
    transformerId: legacyData.transformer_id || legacyData.transformerId,
    transformerName: legacyData.transformer_name || legacyData.transformerName,
    location: legacyData.location || '',
    
    // Priority
    priority: standardizePriority(legacyData.priority) as Priority,
    
    // Metadata
    source: legacyData.source || 'system',
    ageInMinutes: Number(legacyData.age_in_minutes || legacyData.minutes_since_created || 0),
    hasWorkOrder: Boolean(legacyData.has_work_order || legacyData.work_order_id),
    
    // Resolution
    resolutionNotes: legacyData.resolution_notes || legacyData.resolutionNotes,
    escalationLevel: Number(legacyData.escalation_level || 0),
    
    // Timestamps
    createdAt: standardizeDate(legacyData.created_at || legacyData.createdAt || new Date().toISOString()),
    updatedAt: standardizeDate(legacyData.updated_at || legacyData.updatedAt || new Date().toISOString())
  }
}

// ===== HELPER FUNCTIONS =====

/**
 * Standardize status values
 */
export function standardizeStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'operational': 'operational',
    'warning': 'warning',
    'maintenance': 'maintenance',
    'critical': 'critical',
    'offline': 'offline',
    'burnt': 'burnt',
    'faulty': 'critical',  // Map faulty to critical
    'ok': 'operational',   // Map ok to operational
    'error': 'critical'    // Map error to critical
  }
  
  return statusMap[status?.toLowerCase()] || 'operational'
}

/**
 * Standardize maintenance status values
 */
export function standardizeMaintenanceStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'pending': 'pending',
    'in_progress': 'in_progress',
    'in-progress': 'in_progress',
    'completed': 'completed',
    'cancelled': 'cancelled',
    'canceled': 'cancelled',
    'overdue': 'overdue'
  }
  
  return statusMap[status?.toLowerCase()] || 'pending'
}

/**
 * Standardize priority values
 */
export function standardizePriority(priority: string): string {
  const priorityMap: Record<string, string> = {
    'low': 'low',
    'medium': 'medium',
    'high': 'high',
    'critical': 'critical',
    'urgent': 'high'  // Map urgent to high
  }
  
  return priorityMap[priority?.toLowerCase()] || 'medium'
}

/**
 * Standardize alert severity values
 */
export function standardizeAlertSeverity(severity: string): string {
  const severityMap: Record<string, string> = {
    'info': 'info',
    'warning': 'warning',
    'critical': 'critical',
    'error': 'critical',  // Map error to critical
    'success': 'info'     // Map success to info
  }
  
  return severityMap[severity?.toLowerCase()] || 'info'
}

/**
 * Standardize date formats to ISO strings
 */
export function standardizeDate(date: any): string {
  if (!date) return new Date().toISOString()
  
  if (typeof date === 'string') {
    // If already ISO string, return as is
    if (date.includes('T') && date.includes('Z')) return date
    
    // Try to parse and convert to ISO
    const parsed = new Date(date)
    return isNaN(parsed.getTime()) ? new Date().toISOString() : parsed.toISOString()
  }
  
  if (date instanceof Date) {
    return date.toISOString()
  }
  
  return new Date().toISOString()
}

/**
 * Create standardized API response
 */
export function createStandardResponse<T>(
  data: T,
  success: boolean = true,
  message?: string,
  pagination?: any
): APIResponse<T> {
  return {
    success,
    data,
    message,
    pagination,
    metadata: {
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      requestId: generateRequestId()
    }
  }
}

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Validate standardized transformer data
 */
export function validateTransformer(transformer: StandardTransformer): string[] {
  const errors: string[] = []
  
  if (!transformer.id) errors.push('ID is required')
  if (!transformer.serialNumber) errors.push('Serial number is required')
  if (!transformer.name) errors.push('Name is required')
  if (transformer.kvaRating <= 0) errors.push('KVA rating must be positive')
  if (!transformer.location.region) errors.push('Region is required')
  
  return errors
}

/**
 * Batch standardize transformers
 */
export function standardizeTransformers(legacyTransformers: any[]): StandardTransformer[] {
  return legacyTransformers.map(transformer => standardizeTransformer(transformer))
}

/**
 * Batch standardize maintenance tasks
 */
export function standardizeMaintenanceTasks(legacyTasks: any[]): MaintenanceTask[] {
  return legacyTasks.map(task => standardizeMaintenanceTask(task))
}

/**
 * Batch standardize alerts
 */
export function standardizeAlerts(legacyAlerts: any[]): Alert[] {
  return legacyAlerts.map(alert => standardizeAlert(alert))
}
