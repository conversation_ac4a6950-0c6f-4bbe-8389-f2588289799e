"use client"

import { useState, useEffect } from 'react'
import {
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  Wrench,
  Users,
  Filter,
  Search,
  ChevronDown,
  Plus,
  Download,
  RefreshCw,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  FileText,
  CheckSquare,
  XCircle
} from 'lucide-react'
import { MainLayout } from "@/src/components/layout/main-layout"
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/src/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/src/components/ui/dropdown-menu'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/src/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card'

interface MaintenanceTask {
  id: string
  transformerId: string
  transformerName: string
  type: 'preventive' | 'corrective' | 'predictive'
  priority: 'low' | 'medium' | 'high' | 'critical'
  status: 'scheduled' | 'in-progress' | 'completed' | 'overdue' | 'cancelled'
  scheduledDate: string
  completedDate?: string
  assignedTeam: string
  location: string
  description: string
  estimatedDuration: number
  actualDuration?: number
}

export default function TransformerMaintenanceSchedulePage() {
  const [maintenanceTasks, setMaintenanceTasks] = useState<MaintenanceTask[]>([])
  const [filteredTasks, setFilteredTasks] = useState<MaintenanceTask[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')
  const [priorityFilter, setPriorityFilter] = useState('all')
  const [isLoading, setIsLoading] = useState(true)

  // Initialize with mock data
  useEffect(() => {
    // In a real implementation, this would fetch from the API
    const mockTasks: MaintenanceTask[] = [
      {
        id: 'maint-001',
        transformerId: 'tr-001',
        transformerName: 'Bole Sub-station T1',
        type: 'preventive',
        priority: 'medium',
        status: 'scheduled',
        scheduledDate: '2023-06-15T09:00:00Z',
        assignedTeam: 'Team Alpha',
        location: 'Bole, Addis Ababa',
        description: 'Routine inspection and oil sampling',
        estimatedDuration: 4
      },
      {
        id: 'maint-002',
        transformerId: 'tr-015',
        transformerName: 'Kirkos Sub-station T3',
        type: 'corrective',
        priority: 'high',
        status: 'scheduled',
        scheduledDate: '2023-06-16T10:30:00Z',
        assignedTeam: 'Team Bravo',
        location: 'Kirkos, Addis Ababa',
        description: 'Repair oil leak and replace gasket',
        estimatedDuration: 6
      },
      {
        id: 'maint-003',
        transformerId: 'tr-042',
        transformerName: 'Arada Sub-station T2',
        type: 'predictive',
        priority: 'medium',
        status: 'scheduled',
        scheduledDate: '2023-06-18T08:00:00Z',
        assignedTeam: 'Team Charlie',
        location: 'Arada, Addis Ababa',
        description: 'Winding resistance measurement and analysis',
        estimatedDuration: 5
      },
      {
        id: 'maint-004',
        transformerId: 'tr-023',
        transformerName: 'Yeka Sub-station T1',
        type: 'preventive',
        priority: 'high',
        status: 'overdue',
        scheduledDate: '2023-06-10T14:00:00Z',
        assignedTeam: 'Team Delta',
        location: 'Yeka, Addis Ababa',
        description: 'Annual maintenance and bushing inspection',
        estimatedDuration: 8
      },
      {
        id: 'maint-005',
        transformerId: 'tr-037',
        transformerName: 'Lideta Sub-station T4',
        type: 'corrective',
        priority: 'critical',
        status: 'in-progress',
        scheduledDate: '2023-06-14T09:00:00Z',
        assignedTeam: 'Team Echo',
        location: 'Lideta, Addis Ababa',
        description: 'Replace faulty temperature sensor and cooling fan',
        estimatedDuration: 6
      },
      {
        id: 'maint-006',
        transformerId: 'tr-019',
        transformerName: 'Akaki Sub-station T2',
        type: 'preventive',
        priority: 'medium',
        status: 'completed',
        scheduledDate: '2023-06-12T11:00:00Z',
        completedDate: '2023-06-12T15:30:00Z',
        assignedTeam: 'Team Foxtrot',
        location: 'Akaki, Addis Ababa',
        description: 'Quarterly inspection and oil filtration',
        estimatedDuration: 4,
        actualDuration: 4.5
      },
      {
        id: 'maint-007',
        transformerId: 'tr-055',
        transformerName: 'Bahir Dar Sub-station T1',
        type: 'corrective',
        priority: 'critical',
        status: 'completed',
        scheduledDate: '2023-06-11T08:00:00Z',
        completedDate: '2023-06-11T16:45:00Z',
        assignedTeam: 'Team Golf',
        location: 'Bahir Dar, Amhara',
        description: 'Emergency repair of bushing failure',
        estimatedDuration: 8,
        actualDuration: 8.75
      }
    ]

    setMaintenanceTasks(mockTasks)
    setFilteredTasks(mockTasks)
    setIsLoading(false)
  }, [])

  // Apply filters and search
  useEffect(() => {
    let filtered = [...maintenanceTasks]

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(task =>
        task.transformerName.toLowerCase().includes(query) ||
        task.assignedTeam.toLowerCase().includes(query) ||
        task.location.toLowerCase().includes(query) ||
        task.description.toLowerCase().includes(query)
      )
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(task => task.status === statusFilter)
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(task => task.type === typeFilter)
    }

    // Apply priority filter
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(task => task.priority === priorityFilter)
    }

    setFilteredTasks(filtered)
  }, [maintenanceTasks, searchQuery, statusFilter, typeFilter, priorityFilter])

  // Refresh data
  const refreshData = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>Scheduled</span>
          </Badge>
        )
      case 'in-progress':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>In Progress</span>
          </Badge>
        )
      case 'completed':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            <span>Completed</span>
          </Badge>
        )
      case 'overdue':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            <span>Overdue</span>
          </Badge>
        )
      case 'cancelled':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            <span>Cancelled</span>
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700">
            Unknown
          </Badge>
        )
    }
  }

  // Get priority badge
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'low':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            Low
          </Badge>
        )
      case 'medium':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700">
            Medium
          </Badge>
        )
      case 'high':
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-700">
            High
          </Badge>
        )
      case 'critical':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            Critical
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700">
            Unknown
          </Badge>
        )
    }
  }

  // Get type badge
  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'preventive':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            Preventive
          </Badge>
        )
      case 'corrective':
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-700">
            Corrective
          </Badge>
        )
      case 'predictive':
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700">
            Predictive
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700">
            Unknown
          </Badge>
        )
    }
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "national_maintenance_manager",
        "regional_admin",
        "regional_asset_manager",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician"
      ]}
      requiredPermissions={[{ resource: "transformers", action: "read" }]}
    >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <Wrench className="text-green-600 mr-2" size={24} />
              <h1 className="text-2xl font-bold">Maintenance Schedule</h1>
            </div>

            <div className="flex items-center space-x-2">
              <Button className="bg-green-600 hover:bg-green-700">
                <Plus size={16} className="mr-2" />
                Schedule Maintenance
              </Button>

              <Button variant="outline" size="icon">
                <Download size={16} />
              </Button>

              <Button variant="outline" size="icon" onClick={refreshData}>
                <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
              </Button>
            </div>
          </div>

          {/* Stats cards */}
          <div className="grid grid-cols-4 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Total Tasks</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{maintenanceTasks.length}</div>
                <div className="text-xs text-gray-500 mt-1">All maintenance tasks</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Scheduled</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {maintenanceTasks.filter(task => task.status === 'scheduled').length}
                </div>
                <div className="text-xs text-gray-500 mt-1">Upcoming maintenance</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">In Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-600">
                  {maintenanceTasks.filter(task => task.status === 'in-progress').length}
                </div>
                <div className="text-xs text-gray-500 mt-1">Currently being worked on</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Overdue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {maintenanceTasks.filter(task => task.status === 'overdue').length}
                </div>
                <div className="text-xs text-gray-500 mt-1">Past scheduled date</div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-lg border p-4 mb-6">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search maintenance tasks..."
                    className="pl-9 pr-4 py-2 border rounded-md w-64 focus:outline-none focus:ring-1 focus:ring-green-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Search size={16} className="absolute left-3 top-3 text-gray-400" />
                </div>

                <div className="flex items-center space-x-2">
                  <Filter size={16} className="text-gray-500" />
                  <span className="text-sm text-gray-500">Filter:</span>

                  <select
                    className="border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option value="all">All Status</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="in-progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="overdue">Overdue</option>
                    <option value="cancelled">Cancelled</option>
                  </select>

                  <select
                    className="border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                  >
                    <option value="all">All Types</option>
                    <option value="preventive">Preventive</option>
                    <option value="corrective">Corrective</option>
                    <option value="predictive">Predictive</option>
                  </select>

                  <select
                    className="border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                    value={priorityFilter}
                    onChange={(e) => setPriorityFilter(e.target.value)}
                  >
                    <option value="all">All Priorities</option>
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Maintenance tasks table */}
          <div className="bg-white rounded-lg border overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Transformer</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Scheduled Date</TableHead>
                    <TableHead>Team</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTasks.map((task) => (
                    <TableRow key={task.id} className="hover:bg-gray-50">
                      <TableCell>
                        <div className="font-medium">{task.transformerName}</div>
                        <div className="text-xs text-gray-500">{task.location}</div>
                      </TableCell>
                      <TableCell>
                        {getTypeBadge(task.type)}
                      </TableCell>
                      <TableCell>
                        {getPriorityBadge(task.priority)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(task.status)}
                      </TableCell>
                      <TableCell>
                        <div>{formatDate(task.scheduledDate)}</div>
                        {task.completedDate && (
                          <div className="text-xs text-gray-500">
                            Completed: {formatDate(task.completedDate)}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Users size={14} className="text-gray-400 mr-1" />
                          <span>{task.assignedTeam}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>{task.estimatedDuration} hours</div>
                        {task.actualDuration && (
                          <div className="text-xs text-gray-500">
                            Actual: {task.actualDuration} hours
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {task.status === 'scheduled' && (
                              <DropdownMenuItem>
                                <CheckSquare className="h-4 w-4 mr-2" />
                                Start Task
                              </DropdownMenuItem>
                            )}
                            {task.status === 'in-progress' && (
                              <DropdownMenuItem>
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Complete Task
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem>
                              <FileText className="h-4 w-4 mr-2" />
                              Generate Report
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {filteredTasks.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No maintenance tasks found matching your filters
              </div>
            )}
          </div>
        </div>
      </MainLayout>
  )
}
