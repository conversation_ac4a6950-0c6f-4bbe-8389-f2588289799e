"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import {
  Search, FileText, Download, BookOpen, FileQuestion, Upload, BarChart3, Filter,
  Share2, Star, Trash2, Copy, X, Heart, MoreHorizontal, Check
} from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useToast } from "@/src/components/ui/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/src/components/ui/dialog"
import { DocumentViewer } from "@/components/document-viewer"
import { UploadDocumentDialog } from "@/components/upload-document-dialog"
import { RequestDocumentDialog } from "@/components/request-document-dialog"
import { documentationService } from "@/src/services/documentation-service"
import type { Document } from "@/src/types/document"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/src/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/src/components/ui/dropdown-menu"
import { Badge } from "@/src/components/ui/badge"

export function DocumentationContent() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [languageFilter, setLanguageFilter] = useState("all")
  const [documents, setDocuments] = useState<Document[]>([])
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null)
  const [isViewerOpen, setIsViewerOpen] = useState(false)
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)
  const [isRequestDialogOpen, setIsRequestDialogOpen] = useState(false)
  const [isStatsDialogOpen, setIsStatsDialogOpen] = useState(false)
  const [documentStats, setDocumentStats] = useState<any>(null)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [favorites, setFavorites] = useState<string[]>([])
  const [showSearchClear, setShowSearchClear] = useState(false)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [sortOrder, setSortOrder] = useState<"newest" | "oldest" | "a-z" | "z-a">("newest")
  const { toast } = useToast()

  // Load favorites from localStorage
  useEffect(() => {
    const storedFavorites = localStorage.getItem("eeu_doc_favorites")
    if (storedFavorites) {
      setFavorites(JSON.parse(storedFavorites))
    }
  }, [])

  // Fetch documents
  useEffect(() => {
    const fetchDocuments = async () => {
      setIsLoading(true)
      try {
        const data = await documentationService.getAllDocuments()
        setDocuments(data)
        setFilteredDocuments(data) // Initialize filtered documents with all documents
      } catch (error) {
        console.error("Error fetching documents:", error)
        toast({
          title: "Error",
          description: "Failed to load documents. Please try again.",
          variant: "destructive",
        })
        setDocuments([])
        setFilteredDocuments([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchDocuments()
  }, [refreshTrigger, toast])

  // Handle tab change
  useEffect(() => {
    // When tab changes, update the filtered documents based on the selected category
    if (documents.length > 0) {
      let filtered = [...documents]

      // Filter by category (tab)
      if (activeTab !== "all") {
        filtered = filtered.filter(doc => doc.category.toLowerCase() === activeTab.toLowerCase())
      }

      // Apply language filter if set
      if (languageFilter !== "all") {
        filtered = filtered.filter(doc => doc.language === languageFilter)
      }

      // Apply search filter if there's a query
      if (searchQuery) {
        filtered = filtered.filter(doc =>
          doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          doc.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
          doc.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
          doc.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
        )
      }

      setFilteredDocuments(filtered)
    }
  }, [searchQuery, activeTab, languageFilter, documents])

  // Handle document view
  const handleViewDocument = (document: Document) => {
    setSelectedDocument(document)
    setIsViewerOpen(true)
  }

  // Handle document download
  const handleDownloadDocument = async (document: Document) => {
    try {
      await documentationService.incrementDownloadCount(document.id)
      toast({
        title: "Download started",
        description: `${document.title} is being downloaded.`,
      })
    } catch (error) {
      toast({
        title: "Download failed",
        description: "There was an error downloading the document. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Handle document upload
  const handleDocumentUploaded = () => {
    setRefreshTrigger(prev => prev + 1)
    toast({
      title: "Document uploaded",
      description: "The document has been successfully uploaded.",
    })
  }

  // Handle document request
  const handleDocumentRequested = () => {
    toast({
      title: "Request submitted",
      description: "Your document request has been submitted successfully.",
    })
  }

  // Handle opening stats dialog
  const handleOpenStatsDialog = async () => {
    try {
      const stats = await documentationService.getDocumentStatistics()
      setDocumentStats(stats)
      setIsStatsDialogOpen(true)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load document statistics. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Handle toggling favorite status
  const toggleFavorite = useCallback((document: Document) => {
    const newFavorites = [...favorites]
    const index = newFavorites.indexOf(document.id)

    if (index >= 0) {
      // Remove from favorites
      newFavorites.splice(index, 1)
      toast({
        title: "Removed from favorites",
        description: `${document.title} has been removed from your favorites.`,
      })
    } else {
      // Add to favorites
      newFavorites.push(document.id)
      toast({
        title: "Added to favorites",
        description: `${document.title} has been added to your favorites.`,
      })
    }

    setFavorites(newFavorites)
    localStorage.setItem("eeu_doc_favorites", JSON.stringify(newFavorites))
  }, [favorites, toast])

  // Handle sharing document
  const handleShareDocument = useCallback((document: Document) => {
    if (navigator.share) {
      navigator.share({
        title: document.title,
        text: document.description,
        url: document.url,
      })
      .then(() => {
        toast({
          title: "Document shared",
          description: "The document has been shared successfully.",
        })
      })
      .catch((error) => {
        console.error("Error sharing document:", error)
        handleCopyDocumentLink(document)
      })
    } else {
      // Fallback for browsers that don't support Web Share API
      handleCopyDocumentLink(document)
    }
  }, [toast])

  // Handle copying document link
  const handleCopyDocumentLink = useCallback((document: Document) => {
    navigator.clipboard.writeText(document.url)
      .then(() => {
        toast({
          title: "Link copied",
          description: "Document link has been copied to clipboard.",
        })
      })
      .catch((error) => {
        console.error("Error copying link:", error)
        toast({
          title: "Failed to copy link",
          description: "Please try again or share manually.",
          variant: "destructive",
        })
      })
  }, [toast])

  // Handle clearing search
  const handleClearSearch = useCallback(() => {
    setSearchQuery("")
    setShowSearchClear(false)
  }, [])

  // Handle search input change
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
    setShowSearchClear(e.target.value.length > 0)
  }, [])

  // Handle sorting documents
  const handleSort = useCallback((order: "newest" | "oldest" | "a-z" | "z-a") => {
    setSortOrder(order)

    // Apply sorting to filtered documents
    const sorted = [...filteredDocuments]

    switch (order) {
      case "newest":
        sorted.sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime())
        break
      case "oldest":
        sorted.sort((a, b) => new Date(a.lastUpdated).getTime() - new Date(b.lastUpdated).getTime())
        break
      case "a-z":
        sorted.sort((a, b) => a.title.localeCompare(b.title))
        break
      case "z-a":
        sorted.sort((a, b) => b.title.localeCompare(a.title))
        break
    }

    setFilteredDocuments(sorted)
  }, [filteredDocuments])

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Documentation Center</h1>
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleOpenStatsDialog}>
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Statistics
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>View document usage statistics</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={() => setIsRequestDialogOpen(true)}>
                  <FileQuestion className="mr-2 h-4 w-4" />
                  Request Document
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Request a new document</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button size="sm" onClick={() => setIsUploadDialogOpen(true)}>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Document
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Upload a new document</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
        defaultValue="all"
      >
        <TabsList aria-label="Document categories" className="grid grid-cols-4 w-full max-w-md">
          <TabsTrigger
            value="all"
            aria-controls="all-tab-content"
            data-state={activeTab === "all" ? "active" : "inactive"}
          >
            All Documents
            <Badge variant="outline" className="ml-2 px-1 py-0 text-xs">
              {documents.length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="technical"
            aria-controls="technical-tab-content"
            data-state={activeTab === "technical" ? "active" : "inactive"}
          >
            Technical
            <Badge variant="outline" className="ml-2 px-1 py-0 text-xs">
              {documents.filter(doc => doc.category.toLowerCase() === "technical").length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="safety"
            aria-controls="safety-tab-content"
            data-state={activeTab === "safety" ? "active" : "inactive"}
          >
            Safety
            <Badge variant="outline" className="ml-2 px-1 py-0 text-xs">
              {documents.filter(doc => doc.category.toLowerCase() === "safety").length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="operations"
            aria-controls="operations-tab-content"
            data-state={activeTab === "operations" ? "active" : "inactive"}
          >
            Operations
            <Badge variant="outline" className="ml-2 px-1 py-0 text-xs">
              {documents.filter(doc => doc.category.toLowerCase() === "operations").length}
            </Badge>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Document Library</CardTitle>
              <CardDescription>Browse and search all available documentation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search documents..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={handleSearchChange}
                    aria-label="Search documents"
                  />
                  {showSearchClear && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full rounded-l-none"
                      onClick={handleClearSearch}
                      aria-label="Clear search"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
                <div className="flex gap-2">
                  <Select value={languageFilter} onValueChange={setLanguageFilter}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Languages</SelectItem>
                      <SelectItem value="English">English</SelectItem>
                      <SelectItem value="Amharic">Amharic</SelectItem>
                      <SelectItem value="Oromo">Oromo</SelectItem>
                      <SelectItem value="Tigrinya">Tigrinya</SelectItem>
                    </SelectContent>
                  </Select>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon" aria-label="Sort documents">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleSort("newest")}>
                        Newest first
                        {sortOrder === "newest" && <Check className="ml-2 h-4 w-4" />}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleSort("oldest")}>
                        Oldest first
                        {sortOrder === "oldest" && <Check className="ml-2 h-4 w-4" />}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleSort("a-z")}>
                        A to Z
                        {sortOrder === "a-z" && <Check className="ml-2 h-4 w-4" />}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleSort("z-a")}>
                        Z to A
                        {sortOrder === "z-a" && <Check className="ml-2 h-4 w-4" />}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}>
                        {showAdvancedFilters ? "Hide advanced filters" : "Show advanced filters"}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              {showAdvancedFilters && (
                <div className="mb-6 p-4 border rounded-md bg-muted/20">
                  <h3 className="text-sm font-medium mb-3">Advanced Filters</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="text-xs text-muted-foreground mb-1 block">File Type</label>
                      <Select defaultValue="all">
                        <SelectTrigger>
                          <SelectValue placeholder="File Type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Types</SelectItem>
                          <SelectItem value="PDF">PDF</SelectItem>
                          <SelectItem value="DOCX">DOCX</SelectItem>
                          <SelectItem value="PPTX">PPTX</SelectItem>
                          <SelectItem value="XLSX">XLSX</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-xs text-muted-foreground mb-1 block">Author</label>
                      <Select defaultValue="all">
                        <SelectTrigger>
                          <SelectValue placeholder="Author" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Authors</SelectItem>
                          <SelectItem value="Technical Department">Technical Department</SelectItem>
                          <SelectItem value="Safety Department">Safety Department</SelectItem>
                          <SelectItem value="Operations Department">Operations Department</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-xs text-muted-foreground mb-1 block">Date Range</label>
                      <Select defaultValue="all">
                        <SelectTrigger>
                          <SelectValue placeholder="Date Range" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Time</SelectItem>
                          <SelectItem value="last-week">Last Week</SelectItem>
                          <SelectItem value="last-month">Last Month</SelectItem>
                          <SelectItem value="last-year">Last Year</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}

              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                </div>
              ) : filteredDocuments.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 mx-auto text-muted-foreground opacity-50 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No documents found</h3>
                  <p className="text-muted-foreground">
                    Try adjusting your search or filters, or upload a new document.
                  </p>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {filteredDocuments.map((doc) => (
                    <Card key={doc.id} className="overflow-hidden">
                      <div className={`h-2 ${
                        doc.category === "Technical" ? "bg-blue-600" :
                        doc.category === "Safety" ? "bg-red-600" :
                        doc.category === "Operations" ? "bg-orange-600" :
                        doc.category === "Training" ? "bg-purple-600" :
                        doc.category === "Policy" ? "bg-emerald-600" : "bg-teal-600"
                      }`} />
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-muted-foreground">{doc.id}</span>
                          <span
                            className={`text-xs rounded-full px-2 py-0.5 ${
                              doc.language === "English"
                                ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                                : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                            }`}
                          >
                            {doc.language}
                          </span>
                        </div>
                        <CardTitle className="text-base">{doc.title}</CardTitle>
                        <CardDescription className="flex items-center justify-between">
                          <span>{doc.category}</span>
                          <span>{doc.pages} pages</span>
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Updated: {doc.lastUpdated}</span>
                          <div className="flex gap-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm" onClick={() => handleViewDocument(doc)}>
                                    <BookOpen className="h-4 w-4" />
                                    <span className="sr-only">View</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>View document</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm" onClick={() => handleDownloadDocument(doc)}>
                                    <Download className="h-4 w-4" />
                                    <span className="sr-only">Download</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Download document</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleFavorite(doc)}
                                    className={favorites.includes(doc.id) ? "text-yellow-500" : ""}
                                  >
                                    <Star className={`h-4 w-4 ${favorites.includes(doc.id) ? "fill-current" : ""}`} />
                                    <span className="sr-only">{favorites.includes(doc.id) ? "Remove from favorites" : "Add to favorites"}</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{favorites.includes(doc.id) ? "Remove from favorites" : "Add to favorites"}</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="sm">
                                        <MoreHorizontal className="h-4 w-4" />
                                        <span className="sr-only">More options</span>
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem onClick={() => handleShareDocument(doc)}>
                                        <Share2 className="mr-2 h-4 w-4" />
                                        Share
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleCopyDocumentLink(doc)}>
                                        <Copy className="mr-2 h-4 w-4" />
                                        Copy link
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem onClick={() => toggleFavorite(doc)}>
                                        <Star className="mr-2 h-4 w-4" />
                                        {favorites.includes(doc.id) ? "Remove from favorites" : "Add to favorites"}
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>More options</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="technical" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Technical Documentation</CardTitle>
              <CardDescription>Technical guides and specifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search technical documents..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div>
                  <Select value={languageFilter} onValueChange={setLanguageFilter}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Languages</SelectItem>
                      <SelectItem value="English">English</SelectItem>
                      <SelectItem value="Amharic">Amharic</SelectItem>
                      <SelectItem value="Oromo">Oromo</SelectItem>
                      <SelectItem value="Tigrinya">Tigrinya</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                </div>
              ) : activeTab === "technical" && filteredDocuments.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 mx-auto text-muted-foreground opacity-50 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No technical documents found</h3>
                  <p className="text-muted-foreground">
                    Try adjusting your search or filters, or upload a new document.
                  </p>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {filteredDocuments.map((doc) => (
                    <Card key={doc.id} className="overflow-hidden">
                      <div className="h-2 bg-blue-600" />
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-muted-foreground">{doc.id}</span>
                          <span
                            className={`text-xs rounded-full px-2 py-0.5 ${
                              doc.language === "English"
                                ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                                : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                            }`}
                          >
                            {doc.language}
                          </span>
                        </div>
                        <CardTitle className="text-base">{doc.title}</CardTitle>
                        <CardDescription className="flex items-center justify-between">
                          <span>{doc.category}</span>
                          <span>{doc.pages} pages</span>
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Updated: {doc.lastUpdated}</span>
                          <div className="flex gap-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm" onClick={() => handleViewDocument(doc)}>
                                    <BookOpen className="h-4 w-4" />
                                    <span className="sr-only">View</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>View document</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm" onClick={() => handleDownloadDocument(doc)}>
                                    <Download className="h-4 w-4" />
                                    <span className="sr-only">Download</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Download document</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleFavorite(doc)}
                                    className={favorites.includes(doc.id) ? "text-yellow-500" : ""}
                                  >
                                    <Star className={`h-4 w-4 ${favorites.includes(doc.id) ? "fill-current" : ""}`} />
                                    <span className="sr-only">{favorites.includes(doc.id) ? "Remove from favorites" : "Add to favorites"}</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{favorites.includes(doc.id) ? "Remove from favorites" : "Add to favorites"}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="safety" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Safety Documentation</CardTitle>
              <CardDescription>Safety protocols and guidelines</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search safety documents..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div>
                  <Select value={languageFilter} onValueChange={setLanguageFilter}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Languages</SelectItem>
                      <SelectItem value="English">English</SelectItem>
                      <SelectItem value="Amharic">Amharic</SelectItem>
                      <SelectItem value="Oromo">Oromo</SelectItem>
                      <SelectItem value="Tigrinya">Tigrinya</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                </div>
              ) : activeTab === "safety" && filteredDocuments.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 mx-auto text-muted-foreground opacity-50 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No safety documents found</h3>
                  <p className="text-muted-foreground">
                    Try adjusting your search or filters, or upload a new document.
                  </p>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {filteredDocuments.map((doc) => (
                    <Card key={doc.id} className="overflow-hidden">
                      <div className="h-2 bg-red-600" />
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-muted-foreground">{doc.id}</span>
                          <span
                            className={`text-xs rounded-full px-2 py-0.5 ${
                              doc.language === "English"
                                ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                                : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                            }`}
                          >
                            {doc.language}
                          </span>
                        </div>
                        <CardTitle className="text-base">{doc.title}</CardTitle>
                        <CardDescription className="flex items-center justify-between">
                          <span>{doc.category}</span>
                          <span>{doc.pages} pages</span>
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Updated: {doc.lastUpdated}</span>
                          <div className="flex gap-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm" onClick={() => handleViewDocument(doc)}>
                                    <BookOpen className="h-4 w-4" />
                                    <span className="sr-only">View</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>View document</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm" onClick={() => handleDownloadDocument(doc)}>
                                    <Download className="h-4 w-4" />
                                    <span className="sr-only">Download</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Download document</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleFavorite(doc)}
                                    className={favorites.includes(doc.id) ? "text-yellow-500" : ""}
                                  >
                                    <Star className={`h-4 w-4 ${favorites.includes(doc.id) ? "fill-current" : ""}`} />
                                    <span className="sr-only">{favorites.includes(doc.id) ? "Remove from favorites" : "Add to favorites"}</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{favorites.includes(doc.id) ? "Remove from favorites" : "Add to favorites"}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="operations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Operations Documentation</CardTitle>
              <CardDescription>Operational procedures and guidelines</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search operations documents..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div>
                  <Select value={languageFilter} onValueChange={setLanguageFilter}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Languages</SelectItem>
                      <SelectItem value="English">English</SelectItem>
                      <SelectItem value="Amharic">Amharic</SelectItem>
                      <SelectItem value="Oromo">Oromo</SelectItem>
                      <SelectItem value="Tigrinya">Tigrinya</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                </div>
              ) : activeTab === "operations" && filteredDocuments.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 mx-auto text-muted-foreground opacity-50 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No operations documents found</h3>
                  <p className="text-muted-foreground">
                    Try adjusting your search or filters, or upload a new document.
                  </p>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {filteredDocuments.map((doc) => (
                    <Card key={doc.id} className="overflow-hidden">
                      <div className="h-2 bg-orange-600" />
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-muted-foreground">{doc.id}</span>
                          <span
                            className={`text-xs rounded-full px-2 py-0.5 ${
                              doc.language === "English"
                                ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                                : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                            }`}
                          >
                            {doc.language}
                          </span>
                        </div>
                        <CardTitle className="text-base">{doc.title}</CardTitle>
                        <CardDescription className="flex items-center justify-between">
                          <span>{doc.category}</span>
                          <span>{doc.pages} pages</span>
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Updated: {doc.lastUpdated}</span>
                          <div className="flex gap-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm" onClick={() => handleViewDocument(doc)}>
                                    <BookOpen className="h-4 w-4" />
                                    <span className="sr-only">View</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>View document</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm" onClick={() => handleDownloadDocument(doc)}>
                                    <Download className="h-4 w-4" />
                                    <span className="sr-only">Download</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Download document</p>
                                </TooltipContent>
                              </Tooltip>

                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleFavorite(doc)}
                                    className={favorites.includes(doc.id) ? "text-yellow-500" : ""}
                                  >
                                    <Star className={`h-4 w-4 ${favorites.includes(doc.id) ? "fill-current" : ""}`} />
                                    <span className="sr-only">{favorites.includes(doc.id) ? "Remove from favorites" : "Add to favorites"}</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{favorites.includes(doc.id) ? "Remove from favorites" : "Add to favorites"}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Document Viewer Dialog */}
      <DocumentViewer
        document={selectedDocument}
        open={isViewerOpen}
        onOpenChange={setIsViewerOpen}
      />

      {/* Upload Document Dialog */}
      <UploadDocumentDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        onDocumentUploaded={handleDocumentUploaded}
      />

      {/* Request Document Dialog */}
      <RequestDocumentDialog
        open={isRequestDialogOpen}
        onOpenChange={setIsRequestDialogOpen}
      />

      {/* Statistics Dialog */}
      <Dialog open={isStatsDialogOpen} onOpenChange={setIsStatsDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Documentation Statistics</DialogTitle>
            <DialogDescription>
              View statistics about document usage and distribution
            </DialogDescription>
          </DialogHeader>
          {documentStats && documentStats.byCategory && documentStats.byLanguage && documentStats.mostDownloaded && documentStats.mostViewed ? (
            <div className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="p-4 pb-2">
                    <CardDescription>Total Documents</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-2xl font-bold">{documentStats.totalDocuments}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-4 pb-2">
                    <CardDescription>Total Downloads</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-2xl font-bold">{documentStats.totalDownloads}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-4 pb-2">
                    <CardDescription>Total Views</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-2xl font-bold">{documentStats.totalViews}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-4 pb-2">
                    <CardDescription>Languages</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-2xl font-bold">{Object.keys(documentStats.byLanguage).length}</p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Documents by Category</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {Object.entries(documentStats.byCategory).map(([category, count]) => (
                        <div key={category} className="flex items-center justify-between">
                          <span>{category}</span>
                          <span className="font-medium">{count as number}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Documents by Language</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {Object.entries(documentStats.byLanguage).map(([language, count]) => (
                        <div key={language} className="flex items-center justify-between">
                          <span>{language}</span>
                          <span className="font-medium">{count as number}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Most Downloaded Documents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {documentStats.mostDownloaded.map((doc: Document) => (
                        <div key={doc.id} className="flex items-center justify-between">
                          <span className="truncate max-w-[200px]">{doc.title}</span>
                          <span className="font-medium">{doc.downloadCount}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Most Viewed Documents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {documentStats.mostViewed.map((doc: Document) => (
                        <div key={doc.id} className="flex items-center justify-between">
                          <span className="truncate max-w-[200px]">{doc.title}</span>
                          <span className="font-medium">{doc.viewCount}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : (
            <div className="flex justify-center items-center py-12">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
