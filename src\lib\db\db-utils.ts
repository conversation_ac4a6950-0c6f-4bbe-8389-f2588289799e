/**
 * Database utility functions
 *
 * This file provides utility functions for interacting with our JSON database.
 * It uses a JSON file for storage on the server and localStorage on the client.
 */

import { DatabaseSchema, BaseEntity } from './schema';

// Simple UUID v4 generator
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

type DbCollection = keyof Omit<DatabaseSchema, '_metadata'>;

// Check if we're running on the server
const isServer = typeof window === 'undefined';

// In-memory database for both client and server
let inMemoryDb: DatabaseSchema | null = null;

// Constants
const DB_STORAGE_KEY = 'eeu_transformer_db';

// Create an empty database structure
function createEmptyDb(): DatabaseSchema {
  const now = new Date().toISOString();
  return {
    users: [],
    regions: [],
    serviceCenters: [],
    transformers: [],
    maintenanceRecords: [],
    alerts: [],
    outages: [],
    weatherAlerts: [],
    _metadata: {
      version: '1.0.0',
      lastUpdated: now,
      recordCounts: {
        users: 0,
        regions: 0,
        serviceCenters: 0,
        transformers: 0,
        maintenanceRecords: 0,
        alerts: 0,
        outages: 0,
        weatherAlerts: 0
      }
    }
  };
}

// Read the database
export function readDb(): DatabaseSchema {
  if (isServer) {
    // On server, use in-memory DB or initialize from file
    if (!inMemoryDb) {
      // In server-side, we'll just use in-memory DB for simplicity
      inMemoryDb = createEmptyDb();
    }
    return inMemoryDb;
  } else {
    // On client, try to read from localStorage or use empty DB
    try {
      const dbJson = localStorage.getItem(DB_STORAGE_KEY);
      return dbJson ? JSON.parse(dbJson) : createEmptyDb();
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return createEmptyDb();
    }
  }
}

// Write to the database
export function writeDb(db: DatabaseSchema): void {
  // Update metadata
  db._metadata = db._metadata || {} as any;
  db._metadata.lastUpdated = new Date().toISOString();
  db._metadata.recordCounts = {
    users: db.users?.length || 0,
    regions: db.regions?.length || 0,
    serviceCenters: db.serviceCenters?.length || 0,
    transformers: db.transformers?.length || 0,
    maintenanceRecords: db.maintenanceRecords?.length || 0,
    alerts: db.alerts?.length || 0,
    outages: db.outages?.length || 0,
    weatherAlerts: db.weatherAlerts?.length || 0,
  };

  if (isServer) {
    // On server, update in-memory DB
    inMemoryDb = db;
  } else {
    // On client, save to localStorage
    try {
      localStorage.setItem(DB_STORAGE_KEY, JSON.stringify(db));
    } catch (error) {
      console.error('Error writing to localStorage:', error);
    }
  }
}

// Ensure the database exists
export function ensureDbExists(): void {
  if (isServer) {
    // Server-side: Use in-memory DB
    if (!inMemoryDb) {
      inMemoryDb = createEmptyDb();
    }
  }
  // Client-side: No action needed as we'll use API routes
}

// Create a backup of the database
export function backupDb(): string {
  ensureDbExists();
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupKey = `${DB_STORAGE_KEY}_backup_${timestamp}`;

  if (!isServer) {
    // Client-side: Use localStorage
    const data = localStorage.getItem(DB_STORAGE_KEY);
    if (data) {
      localStorage.setItem(backupKey, data);
    }
  }

  return backupKey;
}

// Generic CRUD operations for any entity type

// Create a new entity
export function createEntity<T extends BaseEntity>(
  collection: keyof Omit<DatabaseSchema, '_metadata'>,
  data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>
): T {
  const db = readDb();
  const now = new Date().toISOString();

  const newEntity = {
    ...data,
    id: uuidv4(),
    createdAt: now,
    updatedAt: now,
  } as unknown as T;

  (db[collection] as unknown as T[]).push(newEntity);
  writeDb(db);

  return newEntity;
}

export function getEntityById<T extends BaseEntity>(
  collection: keyof Omit<DatabaseSchema, '_metadata'>,
  id: string
): T | null {
  const db = readDb();
  const entity = (db[collection] as unknown as T[]).find((item) => item.id === id);
  return entity || null;
}

export function updateEntity<T extends BaseEntity>(
  collection: keyof Omit<DatabaseSchema, '_metadata'>,
  id: string,
  data: Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>
): T | null {
  const db = readDb();
  const items = db[collection] as T[];
  const index = items.findIndex((item) => item.id === id);

  if (index === -1) return null;

  const now = new Date().toISOString();
  const updatedEntity = {
    ...items[index],
    ...data,
    updatedAt: now,
  } as T;

  items[index] = updatedEntity;
  writeDb(db);

  return updatedEntity;
}

export function deleteEntity<T extends BaseEntity>(
  collection: keyof Omit<DatabaseSchema, '_metadata'>,
  id: string
): boolean {
  const db = readDb();
  const items = db[collection] as T[];
  const index = items.findIndex((item) => item.id === id);

  if (index === -1) return false;

  items.splice(index, 1);
  writeDb(db);

  return true;
}

export function queryEntities<T extends BaseEntity>(
  collection: keyof Omit<DatabaseSchema, '_metadata'>,
  filters: Partial<Record<keyof T, any>> = {},
  options: {
    limit?: number;
    offset?: number;
    sortBy?: keyof T;
    sortOrder?: 'asc' | 'desc';
  } = {}
): T[] {
  const db = readDb();
  let results = [...(db[collection] as unknown as T[])];

  // Apply filters
  if (Object.keys(filters).length > 0) {
    results = results.filter((item) => {
      return Object.entries(filters).every(([key, value]) => {
        // Handle nested properties with dot notation (e.g., 'address.city')
        const keys = key.split('.');
        const itemValue = keys.reduce((obj, k) => obj?.[k], item as any);

        if (Array.isArray(value)) {
          return value.includes(itemValue);
        }
        return itemValue === value;
      });
    });
  }

  // Apply sorting
  if (options.sortBy) {
    const { sortBy, sortOrder = 'asc' } = options;
    results.sort((a, b) => {
      const aValue = a[sortBy as keyof T];
      const bValue = b[sortBy as keyof T];

      if (aValue === bValue) return 0;

      // Handle different types of values
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      return sortOrder === 'asc' ? 1 : -1;
    });
  }

  // Apply pagination
  const offset = options.offset || 0;
  const limit = options.limit || results.length;

  return results.slice(offset, offset + limit) as T[];
}

export function countEntities<T extends BaseEntity>(
  collection: keyof Omit<DatabaseSchema, '_metadata'>,
  filters: Partial<Record<keyof T, any>> = {}
): number {
  return queryEntities<T>(collection, filters).length;
}

// Get database metadata
export function getDbMetadata() {
  const db = readDb();
  return db._metadata;
}

// Initialize the database with seed data
export function initializeDb(seedData: Partial<DatabaseSchema>): void {
  ensureDbExists();
  const db = readDb();

  // Merge seed data with existing data
  Object.keys(seedData).forEach(key => {
    if (key === '_metadata') return; // Skip metadata

    const collection = key as keyof Omit<DatabaseSchema, '_metadata'>;
    if (Array.isArray(seedData[collection]) && seedData[collection]!.length > 0) {
      // Only add entities that don't already exist (by ID)
      const existingIds = new Set((db[collection] as BaseEntity[]).map(item => item.id));
      const newEntities = (seedData[collection] as BaseEntity[]).filter(
        item => !existingIds.has(item.id)
      );

      (db[collection] as BaseEntity[]).push(...newEntities);
    }
  });

  writeDb(db);
}
