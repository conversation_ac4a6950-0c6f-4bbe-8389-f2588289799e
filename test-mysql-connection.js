/**
 * Test MySQL Connection
 * 
 * Simple test to verify MySQL connection before running migration
 */

const mysql = require('mysql2/promise');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db'
};

async function testConnection() {
  console.log('🔍 Testing MySQL connection...');
  console.log('Configuration:', {
    host: config.host,
    port: config.port,
    user: config.user,
    database: config.database
  });
  
  try {
    // First try to connect without database
    const tempConfig = { ...config };
    delete tempConfig.database;
    
    const connection = await mysql.createConnection(tempConfig);
    console.log('✅ MySQL server connection successful');
    
    // Create database if it doesn't exist
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${config.database}\``);
    console.log(`✅ Database ${config.database} created or already exists`);
    
    await connection.end();
    
    // Now connect to the specific database
    const dbConnection = await mysql.createConnection(config);
    console.log('✅ Database connection successful');
    
    // Test a simple query
    const [result] = await dbConnection.execute('SELECT 1 as test');
    console.log('✅ Test query successful:', result);
    
    await dbConnection.end();
    
    console.log('🎉 MySQL connection test completed successfully!');
    console.log('Ready to run migration.');
    
  } catch (error) {
    console.error('❌ MySQL connection test failed:', error.message);
    console.error('');
    console.error('Troubleshooting:');
    console.error('1. Make sure MySQL server is running');
    console.error('2. Check your database credentials');
    console.error('3. Verify the user has necessary permissions');
    console.error('4. Ensure the host and port are correct');
    process.exit(1);
  }
}

testConnection();
