"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Switch } from "@/src/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Textarea } from "@/src/components/ui/textarea"
import {
  AlertCircle,
  AlertTriangle,
  Check,
  Cpu,
  Download,
  Globe,
  Info,
  RefreshCw,
  Save,
  Server,
  Settings2,
  Shield
} from "lucide-react"
import { useSettings } from "@/src/contexts/settings-context"
import { Alert, AlertDescription, AlertTitle } from "@/src/components/ui/alert"
import { Badge } from "@/src/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/src/components/ui/dialog"
import { useToast } from "@/src/components/ui/use-toast"

export function SettingsContent() {
  const {
    settings,
    updateSystemSettings,
    updateNotificationSettings,
    updateSecuritySettings,
    updateLanguageSettings,
    resetToDefaults,
    saveSettings,
    hasUnsavedChanges
  } = useSettings()

  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("general")
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false)
  const [isApiKeyDialogOpen, setIsApiKeyDialogOpen] = useState(false)
  const [newApiKey, setNewApiKey] = useState("")

  // Handle tab change
  const handleTabChange = (value: string) => {
    if (hasUnsavedChanges) {
      toast({
        title: "Unsaved Changes",
        description: "You have unsaved changes. Please save or discard them before switching tabs.",
        variant: "destructive"
      })
      return
    }
    setActiveTab(value)
  }

  // Handle reset confirmation
  const handleResetConfirm = () => {
    resetToDefaults()
    setIsResetDialogOpen(false)
  }

  // Generate new API key
  const generateApiKey = () => {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
    let key = 'sk_live_'
    for (let i = 0; i < 24; i++) {
      key += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    setNewApiKey(key)
  }

  // Apply new API key
  const applyNewApiKey = () => {
    updateSystemSettings({ apiKey: newApiKey })
    setIsApiKeyDialogOpen(false)
    toast({
      title: "API Key Updated",
      description: "Your API key has been regenerated. Remember to update your applications.",
    })
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
          {hasUnsavedChanges && (
            <Badge variant="outline" className="text-yellow-600 border-yellow-600">
              Unsaved Changes
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          {hasUnsavedChanges && (
            <Button onClick={saveSettings} size="sm">
              <Save className="mr-2 h-4 w-4" />
              Save All Changes
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsResetDialogOpen(true)}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
        </div>
      </div>

      {hasUnsavedChanges && (
        <Alert className="bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Unsaved Changes</AlertTitle>
          <AlertDescription>
            You have made changes to your settings. Click "Save All Changes" to apply them.
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList className="grid grid-cols-6 w-full md:w-auto">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="language">Language</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings2 className="mr-2 h-5 w-5" />
                General Settings
              </CardTitle>
              <CardDescription>Manage your general application settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="company-name">Company Name</Label>
                <Input
                  id="company-name"
                  value={settings.system.companyName}
                  onChange={(e) => updateSystemSettings({ companyName: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="timezone">Timezone</Label>
                <Select
                  value={settings.system.timezone}
                  onValueChange={(value) => updateSystemSettings({ timezone: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="eat">EAT (East Africa Time)</SelectItem>
                    <SelectItem value="utc">UTC (Coordinated Universal Time)</SelectItem>
                    <SelectItem value="est">EST (Eastern Standard Time)</SelectItem>
                    <SelectItem value="cst">CST (Central Standard Time)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="date-format">Date Format</Label>
                <Select
                  value={settings.system.dateFormat}
                  onValueChange={(value) => updateSystemSettings({ dateFormat: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select date format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mdy">MM/DD/YYYY</SelectItem>
                    <SelectItem value="dmy">DD/MM/YYYY</SelectItem>
                    <SelectItem value="ymd">YYYY/MM/DD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="dark-mode">Dark Mode</Label>
                <Switch
                  id="dark-mode"
                  checked={settings.system.darkMode}
                  onCheckedChange={(checked) => updateSystemSettings({ darkMode: checked })}
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSettings}>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertCircle className="mr-2 h-5 w-5" />
                Notification Settings
              </CardTitle>
              <CardDescription>Configure how you receive notifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="email-notifications" className="block mb-1">Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                </div>
                <Switch
                  id="email-notifications"
                  checked={settings.notifications.emailNotifications}
                  onCheckedChange={(checked) => updateNotificationSettings({ emailNotifications: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="sms-notifications" className="block mb-1">SMS Notifications</Label>
                  <p className="text-sm text-muted-foreground">Receive notifications via SMS</p>
                </div>
                <Switch
                  id="sms-notifications"
                  checked={settings.notifications.smsNotifications}
                  onCheckedChange={(checked) => updateNotificationSettings({ smsNotifications: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="push-notifications" className="block mb-1">Push Notifications</Label>
                  <p className="text-sm text-muted-foreground">Receive push notifications in browser</p>
                </div>
                <Switch
                  id="push-notifications"
                  checked={settings.notifications.pushNotifications}
                  onCheckedChange={(checked) => updateNotificationSettings({ pushNotifications: checked })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="notification-frequency">Notification Frequency</Label>
                <Select
                  value={settings.notifications.notificationFrequency}
                  onValueChange={(value) => updateNotificationSettings({ notificationFrequency: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="realtime">Real-time</SelectItem>
                    <SelectItem value="hourly">Hourly Digest</SelectItem>
                    <SelectItem value="daily">Daily Digest</SelectItem>
                    <SelectItem value="weekly">Weekly Digest</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="pt-4 border-t">
                <h3 className="text-sm font-medium mb-3">Notification Types</h3>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="critical-alerts" className="block mb-1">Critical Alerts Only</Label>
                      <p className="text-sm text-muted-foreground">Only receive critical notifications</p>
                    </div>
                    <Switch
                      id="critical-alerts"
                      checked={settings.notifications.criticalAlertsOnly}
                      onCheckedChange={(checked) => updateNotificationSettings({ criticalAlertsOnly: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="maintenance-reminders" className="block mb-1">Maintenance Reminders</Label>
                      <p className="text-sm text-muted-foreground">Receive maintenance schedule notifications</p>
                    </div>
                    <Switch
                      id="maintenance-reminders"
                      checked={settings.notifications.maintenanceReminders}
                      onCheckedChange={(checked) => updateNotificationSettings({ maintenanceReminders: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="outage-alerts" className="block mb-1">Outage Alerts</Label>
                      <p className="text-sm text-muted-foreground">Receive notifications about power outages</p>
                    </div>
                    <Switch
                      id="outage-alerts"
                      checked={settings.notifications.outageAlerts}
                      onCheckedChange={(checked) => updateNotificationSettings({ outageAlerts: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="weather-alerts" className="block mb-1">Weather Alerts</Label>
                      <p className="text-sm text-muted-foreground">Receive notifications about severe weather</p>
                    </div>
                    <Switch
                      id="weather-alerts"
                      checked={settings.notifications.weatherAlerts}
                      onCheckedChange={(checked) => updateNotificationSettings({ weatherAlerts: checked })}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => {
                toast({
                  title: "Test Notification Sent",
                  description: "A test notification has been sent to your configured channels."
                })
              }}>
                Send Test Notification
              </Button>
              <Button onClick={saveSettings}>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Security Settings
              </CardTitle>
              <CardDescription>Manage your security preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Password Management</h3>
                <div className="space-y-2">
                  <Label htmlFor="current-password">Current Password</Label>
                  <Input id="current-password" type="password" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-password">New Password</Label>
                  <Input id="new-password" type="password" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirm-password">Confirm New Password</Label>
                  <Input id="confirm-password" type="password" />
                </div>
                <Button
                  variant="secondary"
                  className="w-full sm:w-auto"
                  onClick={() => {
                    toast({
                      title: "Password Updated",
                      description: "Your password has been updated successfully."
                    })
                  }}
                >
                  Update Password
                </Button>
              </div>

              <div className="pt-4 border-t space-y-4">
                <h3 className="text-sm font-medium">Authentication Settings</h3>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="two-factor" className="block mb-1">Two-Factor Authentication</Label>
                    <p className="text-sm text-muted-foreground">Require a verification code when logging in</p>
                  </div>
                  <Switch
                    id="two-factor"
                    checked={settings.security.twoFactorAuth}
                    onCheckedChange={(checked) => updateSecuritySettings({ twoFactorAuth: checked })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password-expiry">Password Expiry (days)</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="password-expiry"
                      type="number"
                      min="0"
                      max="365"
                      value={settings.security.passwordExpiry}
                      onChange={(e) => updateSecuritySettings({
                        passwordExpiry: parseInt(e.target.value) || 0
                      })}
                    />
                    <div className="text-sm text-muted-foreground whitespace-nowrap">
                      {settings.security.passwordExpiry === 0 ?
                        "Never expires" :
                        `Expires after ${settings.security.passwordExpiry} days`}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="session-timeout"
                      type="number"
                      min="5"
                      max="1440"
                      value={settings.security.sessionTimeout}
                      onChange={(e) => updateSecuritySettings({
                        sessionTimeout: parseInt(e.target.value) || 30
                      })}
                    />
                    <div className="text-sm text-muted-foreground whitespace-nowrap">
                      {`Session expires after ${settings.security.sessionTimeout} minutes of inactivity`}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="failed-login-attempts">Failed Login Attempts Before Lockout</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="failed-login-attempts"
                      type="number"
                      min="1"
                      max="10"
                      value={settings.security.failedLoginAttempts}
                      onChange={(e) => updateSecuritySettings({
                        failedLoginAttempts: parseInt(e.target.value) || 5
                      })}
                    />
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t space-y-4">
                <h3 className="text-sm font-medium">Access Restrictions</h3>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="ip-restriction" className="block mb-1">IP Address Restriction</Label>
                    <p className="text-sm text-muted-foreground">Limit access to specific IP addresses</p>
                  </div>
                  <Switch
                    id="ip-restriction"
                    checked={settings.security.ipRestriction}
                    onCheckedChange={(checked) => updateSecuritySettings({ ipRestriction: checked })}
                  />
                </div>

                {settings.security.ipRestriction && (
                  <div className="space-y-2">
                    <Label htmlFor="allowed-ips">Allowed IP Addresses (comma separated)</Label>
                    <Textarea
                      id="allowed-ips"
                      placeholder="e.g. ***********, ********"
                      value={settings.security.allowedIPs.join(", ")}
                      onChange={(e) => updateSecuritySettings({
                        allowedIPs: e.target.value.split(",").map(ip => ip.trim()).filter(Boolean)
                      })}
                    />
                    <p className="text-xs text-muted-foreground">
                      Enter IP addresses separated by commas. Leave empty to allow all IPs.
                    </p>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="audit-logging" className="block mb-1">Audit Logging</Label>
                    <p className="text-sm text-muted-foreground">Log all security-related activities</p>
                  </div>
                  <Switch
                    id="audit-logging"
                    checked={settings.security.auditLogging}
                    onCheckedChange={(checked) => updateSecuritySettings({ auditLogging: checked })}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSettings}>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Server className="mr-2 h-5 w-5" />
                System Settings
              </CardTitle>
              <CardDescription>Configure system-wide settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Maintenance Configuration</h3>
                <div className="space-y-2">
                  <Label htmlFor="maintenance-window">Maintenance Window</Label>
                  <Select
                    value={settings.system.maintenanceWindow}
                    onValueChange={(value) => updateSystemSettings({ maintenanceWindow: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select maintenance window" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="weekend">Weekends</SelectItem>
                      <SelectItem value="night">Overnight (12AM - 5AM)</SelectItem>
                      <SelectItem value="custom">Custom Schedule</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="data-retention">Data Retention Period (days)</Label>
                  <Input
                    id="data-retention"
                    type="number"
                    min="30"
                    max="3650"
                    value={settings.system.dataRetention}
                    onChange={(e) => updateSystemSettings({
                      dataRetention: parseInt(e.target.value) || 90
                    })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Data older than this will be automatically archived or deleted
                  </p>
                </div>
              </div>

              <div className="pt-4 border-t space-y-4">
                <h3 className="text-sm font-medium">API Configuration</h3>
                <div className="space-y-2">
                  <Label htmlFor="api-key">API Key</Label>
                  <div className="flex gap-2">
                    <Input
                      id="api-key"
                      value={settings.system.apiKey}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        generateApiKey()
                        setIsApiKeyDialogOpen(true)
                      }}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Regenerate
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    This key is used for API access. Keep it secure and don't share it.
                  </p>
                </div>
              </div>

              <div className="pt-4 border-t space-y-4">
                <h3 className="text-sm font-medium">System Information</h3>
                <div className="space-y-2">
                  <Label htmlFor="system-notes">System Notes</Label>
                  <Textarea
                    id="system-notes"
                    placeholder="Enter any system-wide notes here..."
                    value={settings.system.systemNotes}
                    onChange={(e) => updateSystemSettings({ systemNotes: e.target.value })}
                    className="min-h-[100px]"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-2">
                  <div className="p-4 bg-muted rounded-md">
                    <h4 className="text-sm font-medium mb-2">System Version</h4>
                    <p className="text-sm">EEU Transformer Management System v1.2.4</p>
                  </div>
                  <div className="p-4 bg-muted rounded-md">
                    <h4 className="text-sm font-medium mb-2">Last Backup</h4>
                    <p className="text-sm">2025-04-30 03:15 AM (Automatic)</p>
                  </div>
                  <div className="p-4 bg-muted rounded-md">
                    <h4 className="text-sm font-medium mb-2">Database Status</h4>
                    <div className="flex items-center">
                      <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                      <p className="text-sm">Healthy (3.2 GB used)</p>
                    </div>
                  </div>
                  <div className="p-4 bg-muted rounded-md">
                    <h4 className="text-sm font-medium mb-2">Server Load</h4>
                    <div className="flex items-center">
                      <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                      <p className="text-sm">Normal (23% CPU, 42% Memory)</p>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      toast({
                        title: "System Backup Started",
                        description: "A backup of the system has been initiated. This may take a few minutes."
                      })
                    }}
                  >
                    Backup System
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      toast({
                        title: "System Check Completed",
                        description: "All system components are functioning normally."
                      })
                    }}
                  >
                    Run System Check
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSettings}>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="language" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="mr-2 h-5 w-5" />
                Language Settings
              </CardTitle>
              <CardDescription>Configure language and localization preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Language Preferences</h3>
                <div className="space-y-2">
                  <Label htmlFor="primary-language">Primary Language</Label>
                  <Select
                    value={settings.language.primaryLanguage}
                    onValueChange={(value) => updateLanguageSettings({ primaryLanguage: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select primary language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="am">አማርኛ (Amharic)</SelectItem>
                      <SelectItem value="ti">ትግርኛ (Tigrinya)</SelectItem>
                      <SelectItem value="or">Afaan Oromoo</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    This will be the main language used throughout the application
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="secondary-language">Secondary Language</Label>
                  <Select
                    value={settings.language.secondaryLanguage}
                    onValueChange={(value) => updateLanguageSettings({ secondaryLanguage: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select secondary language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="am">አማርኛ (Amharic)</SelectItem>
                      <SelectItem value="ti">ትግርኛ (Tigrinya)</SelectItem>
                      <SelectItem value="or">Afaan Oromoo</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Optional secondary language for bilingual display
                  </p>
                </div>
              </div>

              <div className="pt-4 border-t space-y-4">
                <h3 className="text-sm font-medium">Translation Options</h3>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="show-translations" className="block mb-1">Show Translations</Label>
                    <p className="text-sm text-muted-foreground">Show both languages side by side when available</p>
                  </div>
                  <Switch
                    id="show-translations"
                    checked={settings.language.showTranslations}
                    onCheckedChange={(checked) => updateLanguageSettings({ showTranslations: checked })}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto-translate" className="block mb-1">Auto-Translate Documents</Label>
                    <p className="text-sm text-muted-foreground">Automatically translate documents when possible</p>
                  </div>
                  <Switch
                    id="auto-translate"
                    checked={settings.language.autoTranslate}
                    onCheckedChange={(checked) => updateLanguageSettings({ autoTranslate: checked })}
                  />
                </div>
              </div>

              <div className="pt-4 border-t space-y-4">
                <h3 className="text-sm font-medium">Regional Settings</h3>
                <div className="space-y-2">
                  <Label htmlFor="region-format">Regional Format</Label>
                  <Select
                    value={settings.language.regionalFormat}
                    onValueChange={(value) => updateLanguageSettings({ regionalFormat: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select regional format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="et">Ethiopia</SelectItem>
                      <SelectItem value="us">United States</SelectItem>
                      <SelectItem value="uk">United Kingdom</SelectItem>
                      <SelectItem value="eu">European Union</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Affects number formatting, currency, and other regional preferences
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-2">
                  <div className="p-4 bg-muted rounded-md">
                    <h4 className="text-sm font-medium mb-2">Number Format</h4>
                    <p className="text-sm">
                      {settings.language.regionalFormat === 'et' ? '1.234,56' :
                       settings.language.regionalFormat === 'us' ? '1,234.56' :
                       settings.language.regionalFormat === 'uk' ? '1,234.56' : '1.234,56'}
                    </p>
                  </div>
                  <div className="p-4 bg-muted rounded-md">
                    <h4 className="text-sm font-medium mb-2">Currency Format</h4>
                    <p className="text-sm">
                      {settings.language.regionalFormat === 'et' ? 'ETB 1,234.56' :
                       settings.language.regionalFormat === 'us' ? '$1,234.56' :
                       settings.language.regionalFormat === 'uk' ? '£1,234.56' : '€1.234,56'}
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <Alert className="bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30">
                  <Info className="h-4 w-4" />
                  <AlertTitle>Translation Information</AlertTitle>
                  <AlertDescription>
                    Some parts of the application may not be fully translated in all languages.
                    English is always available as a fallback.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => {
                  toast({
                    title: "Language Cache Cleared",
                    description: "The language cache has been cleared. Refresh the page to see changes."
                  })
                }}
              >
                Clear Language Cache
              </Button>
              <Button onClick={saveSettings}>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Cpu className="mr-2 h-5 w-5" />
                Advanced Settings
              </CardTitle>
              <CardDescription>Configure advanced system settings (for administrators only)</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert className="bg-amber-50 text-amber-800 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/30">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Warning</AlertTitle>
                <AlertDescription>
                  These settings are for advanced users only. Incorrect configuration may affect system performance.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Development Options</h3>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="debug-mode" className="block mb-1">Debug Mode</Label>
                    <p className="text-sm text-muted-foreground">Enable detailed logging and debugging tools</p>
                  </div>
                  <Switch
                    id="debug-mode"
                    checked={settings.advanced.debugMode}
                    onCheckedChange={(checked) => updateAdvancedSettings({ debugMode: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="beta-features" className="block mb-1">Enable Beta Features</Label>
                    <p className="text-sm text-muted-foreground">Access experimental features that are still in testing</p>
                  </div>
                  <Switch
                    id="beta-features"
                    checked={settings.advanced.enableBetaFeatures}
                    onCheckedChange={(checked) => updateAdvancedSettings({ enableBetaFeatures: checked })}
                  />
                </div>
              </div>

              <div className="pt-4 border-t space-y-4">
                <h3 className="text-sm font-medium">Performance Settings</h3>
                <div className="space-y-2">
                  <Label htmlFor="performance-mode">Performance Mode</Label>
                  <Select
                    value={settings.advanced.performanceMode}
                    onValueChange={(value: "balanced" | "performance" | "power-saving") =>
                      updateAdvancedSettings({ performanceMode: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select performance mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="balanced">Balanced</SelectItem>
                      <SelectItem value="performance">Performance (Higher resource usage)</SelectItem>
                      <SelectItem value="power-saving">Power Saving (Lower resource usage)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="api-rate-limit">API Rate Limit (requests per minute)</Label>
                  <Input
                    id="api-rate-limit"
                    type="number"
                    min="10"
                    max="1000"
                    value={settings.advanced.apiRateLimit}
                    onChange={(e) => updateAdvancedSettings({
                      apiRateLimit: parseInt(e.target.value) || 100
                    })}
                  />
                </div>
              </div>

              <div className="pt-4 border-t space-y-4">
                <h3 className="text-sm font-medium">Backup Configuration</h3>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto-backup" className="block mb-1">Automatic Backup</Label>
                    <p className="text-sm text-muted-foreground">Automatically backup system data</p>
                  </div>
                  <Switch
                    id="auto-backup"
                    checked={settings.advanced.autoBackup}
                    onCheckedChange={(checked) => updateAdvancedSettings({ autoBackup: checked })}
                  />
                </div>

                {settings.advanced.autoBackup && (
                  <div className="space-y-2">
                    <Label htmlFor="backup-frequency">Backup Frequency</Label>
                    <Select
                      value={settings.advanced.backupFrequency}
                      onValueChange={(value) => updateAdvancedSettings({ backupFrequency: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select backup frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hourly">Hourly</SelectItem>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              <div className="pt-4 border-t space-y-4">
                <h3 className="text-sm font-medium">Custom CSS</h3>
                <div className="space-y-2">
                  <Label htmlFor="custom-css">Custom CSS Overrides</Label>
                  <Textarea
                    id="custom-css"
                    placeholder=":root { --primary-color: #00796b; }"
                    className="font-mono text-sm h-[100px]"
                    value={settings.advanced.customCss}
                    onChange={(e) => updateAdvancedSettings({ customCss: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Add custom CSS to override the default styles. Changes will apply after saving and refreshing.
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={() => {
                  toast({
                    title: "Cache Cleared",
                    description: "Application cache has been cleared. Some features may be slower initially."
                  })
                }}
              >
                Clear Application Cache
              </Button>
              <Button onClick={saveSettings}>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Reset Settings Dialog */}
      <Dialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Settings</DialogTitle>
            <DialogDescription>
              Are you sure you want to reset all settings to their default values? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="pt-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                This will reset all your customizations and preferences.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsResetDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleResetConfirm}>
              Reset All Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* API Key Dialog */}
      <Dialog open={isApiKeyDialogOpen} onOpenChange={setIsApiKeyDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Regenerate API Key</DialogTitle>
            <DialogDescription>
              Are you sure you want to regenerate your API key? All applications using the current key will need to be updated.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>New API Key</Label>
              <div className="p-2 bg-muted rounded-md font-mono text-sm break-all">
                {newApiKey}
              </div>
            </div>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                Save this key somewhere safe. For security reasons, you won't be able to view it again after closing this dialog.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApiKeyDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={applyNewApiKey}>
              Apply New API Key
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
