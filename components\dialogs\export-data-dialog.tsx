"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/src/components/ui/form"
import { Input } from "@/src/components/ui/input"
import { Button } from "@/src/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Checkbox } from "@/src/components/ui/checkbox"
import { useToast } from "@/src/components/ui/use-toast"
import { Loader2, Download, Database, FileSpreadsheet } from "lucide-react"

const exportSchema = z.object({
  dataType: z.string().min(1, "Data type is required"),
  format: z.string().min(1, "Format is required"),
  dateRange: z.string().min(1, "Date range is required"),
  regions: z.array(z.string()).min(1, "At least one region must be selected"),
  includeHeaders: z.boolean().default(true),
  includeMetadata: z.boolean().default(false),
  compressOutput: z.boolean().default(false),
  emailResults: z.boolean().default(false),
  emailAddress: z.string().optional(),
  customQuery: z.string().optional(),
})

type ExportFormData = z.infer<typeof exportSchema>

interface ExportDataDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function ExportDataDialog({ open, onOpenChange, onSuccess }: ExportDataDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const form = useForm<ExportFormData>({
    resolver: zodResolver(exportSchema),
    defaultValues: {
      dataType: "",
      format: "csv",
      dateRange: "30d",
      regions: [],
      includeHeaders: true,
      includeMetadata: false,
      compressOutput: false,
      emailResults: false,
      emailAddress: "",
      customQuery: "",
    },
  })

  const watchEmailResults = form.watch("emailResults")

  const onSubmit = async (data: ExportFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/mysql/dashboard/actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'exportData',
          payload: data,
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Export Started",
          description: "Your data export is being processed. You'll be notified when it's ready.",
        })
        form.reset()
        onOpenChange(false)
        onSuccess()
      } else {
        throw new Error(result.message || 'Failed to export data')
      }
    } catch (error) {
      console.error('Error exporting data:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to export data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const regions = [
    { id: "addis", label: "Addis Ababa" },
    { id: "oromia", label: "Oromia" },
    { id: "amhara", label: "Amhara" },
    { id: "tigray", label: "Tigray" },
    { id: "snnpr", label: "SNNPR" },
    { id: "afar", label: "Afar" },
    { id: "somali", label: "Somali" },
    { id: "benishangul", label: "Benishangul-Gumuz" },
    { id: "gambela", label: "Gambela" },
    { id: "harari", label: "Harari" },
    { id: "dire_dawa", label: "Dire Dawa" },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Download className="h-5 w-5 mr-2" />
            Export Data
          </DialogTitle>
          <DialogDescription>
            Export transformer data in various formats for analysis or backup purposes.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="dataType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      <Database className="h-4 w-4 mr-1" />
                      Data Type
                    </FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select data type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="transformers">Transformer Data</SelectItem>
                          <SelectItem value="maintenance">Maintenance Records</SelectItem>
                          <SelectItem value="alerts">Alert History</SelectItem>
                          <SelectItem value="performance">Performance Metrics</SelectItem>
                          <SelectItem value="outages">Outage Records</SelectItem>
                          <SelectItem value="users">User Data</SelectItem>
                          <SelectItem value="regions">Regional Data</SelectItem>
                          <SelectItem value="all">All Data</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="format"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      <FileSpreadsheet className="h-4 w-4 mr-1" />
                      Export Format
                    </FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="csv">CSV (Comma Separated)</SelectItem>
                          <SelectItem value="excel">Excel Spreadsheet</SelectItem>
                          <SelectItem value="json">JSON Format</SelectItem>
                          <SelectItem value="xml">XML Format</SelectItem>
                          <SelectItem value="sql">SQL Dump</SelectItem>
                          <SelectItem value="pdf">PDF Report</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dateRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date Range</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select date range" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="7d">Last 7 days</SelectItem>
                          <SelectItem value="30d">Last 30 days</SelectItem>
                          <SelectItem value="90d">Last 90 days</SelectItem>
                          <SelectItem value="6m">Last 6 months</SelectItem>
                          <SelectItem value="1y">Last year</SelectItem>
                          <SelectItem value="all">All time</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="regions"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel className="text-base">Regions to Include</FormLabel>
                    <FormDescription>
                      Select the regions to include in the export
                    </FormDescription>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {regions.map((region) => (
                      <FormField
                        key={region.id}
                        control={form.control}
                        name="regions"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={region.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(region.id)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...field.value, region.id])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== region.id
                                          )
                                        )
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="text-sm font-normal">
                                {region.label}
                              </FormLabel>
                            </FormItem>
                          )
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-3">
              <FormLabel className="text-base">Export Options</FormLabel>
              
              <FormField
                control={form.control}
                name="includeHeaders"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Include Column Headers</FormLabel>
                      <FormDescription>
                        Add descriptive headers to the exported data
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="includeMetadata"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Include Metadata</FormLabel>
                      <FormDescription>
                        Add export timestamp, user info, and data source details
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="compressOutput"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Compress Output</FormLabel>
                      <FormDescription>
                        Create a ZIP archive for large exports
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="emailResults"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Email Results</FormLabel>
                      <FormDescription>
                        Send the export file via email when ready
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {watchEmailResults && (
              <FormField
                control={form.control}
                name="emailAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter email address"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      The export file will be sent to this email address
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="customQuery"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Custom Query (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., status='operational' AND capacity>500"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Add custom filters to refine the exported data
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Start Export
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
