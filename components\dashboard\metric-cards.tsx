"use client"

import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  TrendingUp, 
  TrendingDown, 
  Activity,
  Zap,
  CheckCircle,
  AlertTriangle,
  Clock,
  Gauge,
  ThermometerSun,
  DollarSign,
  Globe,
  Shield,
  Battery,
  Wifi,
  Users,
  Building
} from 'lucide-react'

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  changeLabel?: string
  icon: React.ReactNode
  gradient: string
  trend?: 'up' | 'down' | 'stable'
  subtitle?: string
  status?: 'success' | 'warning' | 'danger' | 'info'
}

export function MetricCard({ 
  title, 
  value, 
  change, 
  changeLabel, 
  icon, 
  gradient, 
  trend, 
  subtitle,
  status = 'info'
}: MetricCardProps) {
  const getTrendIcon = () => {
    if (trend === 'up') return <TrendingUp className="h-4 w-4 text-emerald-500" />
    if (trend === 'down') return <TrendingDown className="h-4 w-4 text-red-500" />
    return <Activity className="h-4 w-4 text-gray-500" />
  }

  const getStatusColor = () => {
    switch (status) {
      case 'success': return 'text-emerald-600'
      case 'warning': return 'text-amber-600'
      case 'danger': return 'text-red-600'
      default: return 'text-blue-600'
    }
  }

  return (
    <Card className="relative overflow-hidden group hover:shadow-xl transition-all duration-500 border-0 bg-white/80 backdrop-blur-sm">
      {/* Gradient top border */}
      <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${gradient}`}></div>
      
      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className={`w-full h-full bg-gradient-to-br ${gradient}`}></div>
      </div>
      
      <CardContent className="p-6 relative">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <div className="flex items-baseline gap-2">
              <span className="text-3xl font-bold text-gray-900">{value}</span>
              {change !== undefined && (
                <div className="flex items-center">
                  {getTrendIcon()}
                  <span className={`ml-1 text-sm font-medium ${
                    trend === 'up' ? 'text-emerald-600' : 
                    trend === 'down' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {change > 0 ? '+' : ''}{change}%
                  </span>
                </div>
              )}
            </div>
          </div>
          
          <div className={`p-3 rounded-xl bg-gradient-to-br ${gradient} text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}>
            {icon}
          </div>
        </div>
        
        {subtitle && (
          <p className="text-sm text-gray-500 mb-2">{subtitle}</p>
        )}
        
        {changeLabel && (
          <p className="text-xs text-gray-400">{changeLabel}</p>
        )}
      </CardContent>
    </Card>
  )
}

interface AdvancedMetricCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon: React.ReactNode
  gradient: string
  sparklineData?: number[]
  badge?: {
    text: string
    variant: 'success' | 'warning' | 'danger' | 'info'
  }
}

export function AdvancedMetricCard({ 
  title, 
  value, 
  subtitle, 
  icon, 
  gradient, 
  sparklineData,
  badge
}: AdvancedMetricCardProps) {
  return (
    <Card className="relative overflow-hidden group hover:shadow-2xl transition-all duration-500 border-0 bg-white/90 backdrop-blur-md">
      {/* Animated gradient background */}
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-500`}></div>
      
      {/* Glowing border effect */}
      <div className={`absolute inset-0 bg-gradient-to-r ${gradient} opacity-20 blur-sm group-hover:opacity-30 transition-opacity duration-500`}></div>
      
      <CardContent className="p-6 relative z-10">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <p className="text-sm font-semibold text-gray-700">{title}</p>
              {badge && (
                <Badge 
                  variant={badge.variant === 'success' ? 'default' : 'secondary'}
                  className={`text-xs ${
                    badge.variant === 'success' ? 'bg-emerald-100 text-emerald-700' :
                    badge.variant === 'warning' ? 'bg-amber-100 text-amber-700' :
                    badge.variant === 'danger' ? 'bg-red-100 text-red-700' :
                    'bg-blue-100 text-blue-700'
                  }`}
                >
                  {badge.text}
                </Badge>
              )}
            </div>
            <div className="text-4xl font-bold text-gray-900 mb-1">{value}</div>
            {subtitle && (
              <p className="text-sm text-gray-500">{subtitle}</p>
            )}
          </div>
          
          <div className={`p-4 rounded-2xl bg-gradient-to-br ${gradient} text-white shadow-xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-300`}>
            {icon}
          </div>
        </div>
        
        {/* Sparkline visualization */}
        {sparklineData && (
          <div className="mt-4">
            <div className="flex items-end gap-1 h-8">
              {sparklineData.map((value, index) => (
                <div
                  key={index}
                  className={`bg-gradient-to-t ${gradient} rounded-sm flex-1 transition-all duration-300 hover:opacity-80`}
                  style={{ height: `${(value / Math.max(...sparklineData)) * 100}%` }}
                ></div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface StatusMetricCardProps {
  title: string
  value: string | number
  status: 'excellent' | 'good' | 'warning' | 'critical'
  icon: React.ReactNode
  details?: string[]
}

export function StatusMetricCard({ title, value, status, icon, details }: StatusMetricCardProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'excellent':
        return {
          gradient: 'from-emerald-500 to-teal-500',
          bgColor: 'bg-emerald-50',
          textColor: 'text-emerald-700',
          borderColor: 'border-emerald-200'
        }
      case 'good':
        return {
          gradient: 'from-blue-500 to-cyan-500',
          bgColor: 'bg-blue-50',
          textColor: 'text-blue-700',
          borderColor: 'border-blue-200'
        }
      case 'warning':
        return {
          gradient: 'from-amber-500 to-orange-500',
          bgColor: 'bg-amber-50',
          textColor: 'text-amber-700',
          borderColor: 'border-amber-200'
        }
      case 'critical':
        return {
          gradient: 'from-red-500 to-pink-500',
          bgColor: 'bg-red-50',
          textColor: 'text-red-700',
          borderColor: 'border-red-200'
        }
    }
  }

  const config = getStatusConfig()

  return (
    <Card className={`relative overflow-hidden group hover:shadow-xl transition-all duration-500 border-2 ${config.borderColor} ${config.bgColor}`}>
      {/* Status indicator */}
      <div className={`absolute top-0 left-0 w-full h-2 bg-gradient-to-r ${config.gradient}`}></div>
      
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <div className="text-3xl font-bold text-gray-900 mb-2">{value}</div>
            <Badge className={`${config.textColor} ${config.bgColor} border-0`}>
              {status.toUpperCase()}
            </Badge>
          </div>
          
          <div className={`p-3 rounded-xl bg-gradient-to-br ${config.gradient} text-white shadow-lg`}>
            {icon}
          </div>
        </div>
        
        {details && (
          <div className="space-y-1">
            {details.map((detail, index) => (
              <p key={index} className="text-xs text-gray-500">• {detail}</p>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Pre-configured metric cards for common use cases
export function SystemHealthCard({ health }: { health: number }) {
  const getStatus = () => {
    if (health >= 95) return 'excellent'
    if (health >= 85) return 'good'
    if (health >= 70) return 'warning'
    return 'critical'
  }

  return (
    <StatusMetricCard
      title="System Health"
      value={`${health}%`}
      status={getStatus()}
      icon={<Shield className="h-6 w-6" />}
      details={[
        'All systems operational',
        'Performance within limits',
        'No critical alerts'
      ]}
    />
  )
}

export function EfficiencyCard({ efficiency }: { efficiency: number }) {
  return (
    <AdvancedMetricCard
      title="System Efficiency"
      value={`${efficiency.toFixed(1)}%`}
      subtitle="Average across all transformers"
      icon={<Gauge className="h-6 w-6" />}
      gradient="from-purple-500 to-indigo-500"
      sparklineData={[92, 94, 96, 97, 98, 97, 98]}
      badge={{
        text: efficiency > 97 ? 'Excellent' : efficiency > 95 ? 'Good' : 'Needs Attention',
        variant: efficiency > 97 ? 'success' : efficiency > 95 ? 'info' : 'warning'
      }}
    />
  )
}

export function UptimeCard({ uptime }: { uptime: number }) {
  return (
    <MetricCard
      title="System Uptime"
      value={`${uptime.toFixed(1)}%`}
      change={0.2}
      changeLabel="vs last month"
      icon={<CheckCircle className="h-6 w-6" />}
      gradient="from-emerald-500 to-teal-500"
      trend="up"
      subtitle="99.9% target"
      status="success"
    />
  )
}

export function AlertsCard({ alerts }: { alerts: number }) {
  return (
    <MetricCard
      title="Active Alerts"
      value={alerts}
      icon={<AlertTriangle className="h-6 w-6" />}
      gradient="from-red-500 to-pink-500"
      subtitle={alerts > 0 ? "Requires attention" : "All systems normal"}
      status={alerts > 5 ? "danger" : alerts > 0 ? "warning" : "success"}
    />
  )
}

export function TemperatureCard({ temperature }: { temperature: number }) {
  const getStatus = () => {
    if (temperature > 80) return 'critical'
    if (temperature > 70) return 'warning'
    if (temperature > 60) return 'good'
    return 'excellent'
  }

  return (
    <StatusMetricCard
      title="Average Temperature"
      value={`${temperature.toFixed(1)}°C`}
      status={getStatus()}
      icon={<ThermometerSun className="h-6 w-6" />}
      details={[
        `Operating range: 45-75°C`,
        `Current: ${temperature.toFixed(1)}°C`,
        temperature > 70 ? 'Monitor closely' : 'Within normal range'
      ]}
    />
  )
}
