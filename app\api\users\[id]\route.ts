import { NextRequest, NextResponse } from 'next/server'

// This would typically come from a database
const mockUsers = [
  {
    id: 1,
    employeeId: 'EEU001',
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+251-911-123456',
    role: 'regional_admin',
    department: 'Operations',
    region: 'Addis Ababa',
    serviceCenter: 'Central Service Center',
    status: 'active',
    lastLogin: '2024-01-20T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    hireDate: '2023-06-15',
    supervisor: '<PERSON>',
    permissions: ['read_transformers', 'write_transformers', 'read_maintenance', 'write_maintenance']
  },
  {
    id: 2,
    employeeId: 'EEU002',
    firstName: 'Jane',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+251-911-234567',
    role: 'field_technician',
    department: 'Maintenance',
    region: 'Oromia',
    serviceCenter: 'Oromia Service Center',
    status: 'active',
    lastLogin: '2024-01-19T14:15:00Z',
    createdAt: '2024-01-02T00:00:00Z',
    hireDate: '2023-08-20',
    supervisor: '<PERSON> <PERSON>',
    permissions: ['read_transformers', 'read_maintenance', 'write_maintenance']
  }
]

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = parseInt(params.id)

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      )
    }

    const user = mockUsers.find(u => u.id === userId)

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Remove sensitive information
    const { ...safeUser } = user

    return NextResponse.json({
      success: true,
      data: { user: safeUser }
    })
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = parseInt(params.id)
    const body = await request.json()

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      )
    }

    const userIndex = mockUsers.findIndex(u => u.id === userId)

    if (userIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Update user data
    const updatedUser = {
      ...mockUsers[userIndex],
      ...body,
      id: userId, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    }

    mockUsers[userIndex] = updatedUser

    // Remove sensitive information
    const { ...safeUser } = updatedUser

    return NextResponse.json({
      success: true,
      data: { user: safeUser },
      message: 'User updated successfully'
    })
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update user' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = parseInt(params.id)

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' },
        { status: 400 }
      )
    }

    const userIndex = mockUsers.findIndex(u => u.id === userId)

    if (userIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Remove user from array
    mockUsers.splice(userIndex, 1)

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete user' },
      { status: 500 }
    )
  }
}
