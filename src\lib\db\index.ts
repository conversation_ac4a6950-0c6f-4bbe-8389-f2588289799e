/**
 * Database module exports
 * 
 * This file exports the main database components for use in the application.
 */

// Export schema types
export * from './schema';

// Export database service
export * from './db-service';

// Export repositories
export * from './repositories';

// Export utility functions
export {
  ensureDbExists,
  readDb,
  writeDb,
  backupDb,
  createEntity,
  getEntityById,
  updateEntity,
  deleteEntity,
  queryEntities,
  countEntities,
  getDbMetadata
} from './db-utils';

// Export initialization function
export { initializeDatabase } from './init-db';
