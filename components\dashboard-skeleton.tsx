"use client"

import { Skeleton } from "@/src/components/ui/skeleton"

/**
 * Skeleton loading state for the dashboard
 */
export function DashboardSkeleton() {
  return (
    <div className="flex flex-col gap-4">
      <div className="mb-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex flex-wrap gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-28" />
        ))}
      </div>

      <Skeleton className="h-10 w-full mt-4 mb-2" />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Skeleton className="lg:col-span-4 h-80" />
        <Skeleton className="lg:col-span-3 h-80" />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Skeleton className="h-64" />
        <Skeleton className="h-64" />
        <Skeleton className="h-64" />
      </div>
    </div>
  )
}
