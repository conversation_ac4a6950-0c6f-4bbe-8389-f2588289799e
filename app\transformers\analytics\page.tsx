"use client"

import React, { use<PERSON>tate, use<PERSON>ffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  Zap,
  Thermometer,
  Gauge,
  PieChart,
  LineChart,
  BarChart,
  Calendar,
  Clock,
  MapPin,
  RefreshCw,
  Download,
  Filter,
  Search,
  Settings,
  Eye,
  AlertTriangle,
  CheckCircle,
  Target,
  Award,
  Brain,
  Cpu,
  Database,
  FileText,
  TrendingRight
} from 'lucide-react'
import {
  Line<PERSON>hart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart as RechartsBarChart,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>rts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  ScatterChart,
  Scatter,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts'
import { MainLayout } from "@/src/components/layout/main-layout"

// Mock analytics data
const performanceData = Array.from({ length: 30 }, (_, i) => ({
  date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  efficiency: 96 + Math.random() * 3,
  loadFactor: 60 + Math.random() * 30,
  temperature: 55 + Math.random() * 20,
  faults: Math.floor(Math.random() * 3),
  availability: 95 + Math.random() * 5,
  powerQuality: 98 + Math.random() * 2
}))

const transformerComparison = [
  {
    id: 'T-AA-001',
    name: 'Lideta Primary',
    region: 'Addis Ababa',
    efficiency: 98.5,
    reliability: 99.2,
    loadUtilization: 78,
    maintenanceCost: 45000,
    faultRate: 0.2,
    age: 4,
    condition: 'excellent'
  },
  {
    id: 'T-OR-045',
    name: 'Sebeta Distribution',
    region: 'Oromia',
    efficiency: 97.8,
    reliability: 96.5,
    loadUtilization: 87,
    maintenanceCost: 62000,
    faultRate: 1.1,
    age: 6,
    condition: 'good'
  },
  {
    id: 'T-AM-023',
    name: 'Bahir Dar Distribution',
    region: 'Amhara',
    efficiency: 96.2,
    reliability: 94.8,
    loadUtilization: 65,
    maintenanceCost: 78000,
    faultRate: 2.3,
    age: 8,
    condition: 'fair'
  }
]

const regionalAnalytics = [
  {
    region: 'Addis Ababa',
    transformers: 456,
    avgEfficiency: 97.8,
    avgLoad: 73.2,
    totalCapacity: 2850,
    utilizationRate: 68.5,
    faultRate: 0.8,
    maintenanceCost: 2.4
  },
  {
    region: 'Oromia',
    transformers: 387,
    avgEfficiency: 97.2,
    avgLoad: 78.9,
    totalCapacity: 2340,
    utilizationRate: 72.1,
    faultRate: 1.2,
    maintenanceCost: 2.8
  },
  {
    region: 'Amhara',
    transformers: 298,
    avgEfficiency: 96.5,
    avgLoad: 69.4,
    totalCapacity: 1890,
    utilizationRate: 65.3,
    faultRate: 1.5,
    maintenanceCost: 3.1
  },
  {
    region: 'Tigray',
    transformers: 106,
    avgEfficiency: 95.8,
    avgLoad: 62.7,
    totalCapacity: 680,
    utilizationRate: 58.9,
    faultRate: 2.1,
    maintenanceCost: 3.8
  }
]

const kpiMetrics = {
  overallEfficiency: 97.2,
  systemReliability: 98.1,
  avgLoadFactor: 72.8,
  maintenanceCompliance: 94.5,
  costPerMWh: 2.85,
  carbonFootprint: 0.42,
  predictiveAccuracy: 89.3,
  assetUtilization: 68.7
}

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']

export default function TransformerAnalyticsPage() {
  const [loading, setLoading] = useState(true)
  const [selectedTimeRange, setSelectedTimeRange] = useState('30d')
  const [selectedRegion, setSelectedRegion] = useState('all')
  const [selectedMetric, setSelectedMetric] = useState('efficiency')

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading analytics dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "national_maintenance_manager",
        "regional_admin",
        "regional_asset_manager",
        "data_analyst"
      ]}
      requiredPermissions={[{ resource: "transformers", action: "read" }]}
    >
          <div className="space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Transformer Analytics</h1>
                <p className="text-muted-foreground">
                  Advanced analytics and performance insights for Ethiopian Electric Utility transformers
                </p>
              </div>
              <div className="flex items-center gap-2">
                <select
                  value={selectedTimeRange}
                  onChange={(e) => setSelectedTimeRange(e.target.value)}
                  className="border rounded-md px-3 py-2"
                >
                  <option value="7d">Last 7 days</option>
                  <option value="30d">Last 30 days</option>
                  <option value="90d">Last 90 days</option>
                  <option value="1y">Last year</option>
                </select>
                <Button variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </Button>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Report
                </Button>
              </div>
            </div>

            {/* KPI Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Overall Efficiency</CardTitle>
                  <TrendingUp className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{kpiMetrics.overallEfficiency}%</div>
                  <p className="text-xs text-muted-foreground">
                    +0.3% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">System Reliability</CardTitle>
                  <CheckCircle className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">{kpiMetrics.systemReliability}%</div>
                  <p className="text-xs text-muted-foreground">
                    +0.1% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Load Factor</CardTitle>
                  <Gauge className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">{kpiMetrics.avgLoadFactor}%</div>
                  <p className="text-xs text-muted-foreground">
                    -1.2% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Cost per MWh</CardTitle>
                  <TrendingDown className="h-4 w-4 text-orange-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">{kpiMetrics.costPerMWh} ETB</div>
                  <p className="text-xs text-muted-foreground">
                    -0.15 ETB from last month
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Main Analytics Interface */}
            <Tabs defaultValue="performance" className="space-y-4">
              <TabsList>
                <TabsTrigger value="performance">Performance Analysis</TabsTrigger>
                <TabsTrigger value="comparison">Comparative Analysis</TabsTrigger>
                <TabsTrigger value="regional">Regional Analytics</TabsTrigger>
                <TabsTrigger value="predictive">Predictive Insights</TabsTrigger>
              </TabsList>

              <TabsContent value="performance" className="space-y-4">
                {/* Performance Trends */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Efficiency Trends</CardTitle>
                      <CardDescription>Transformer efficiency over time</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsLineChart data={performanceData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis domain={[95, 100]} />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="efficiency" stroke="#3b82f6" name="Efficiency %" />
                          <ReferenceLine y={97} stroke="#f59e0b" strokeDasharray="5 5" label="Target" />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Load Factor Analysis</CardTitle>
                      <CardDescription>Load utilization patterns</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={performanceData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Area type="monotone" dataKey="loadFactor" stroke="#10b981" fill="#10b981" fillOpacity={0.3} name="Load Factor %" />
                          <ReferenceLine y={75} stroke="#f59e0b" strokeDasharray="5 5" label="Optimal" />
                        </AreaChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Temperature and Fault Analysis */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Temperature Analysis</CardTitle>
                      <CardDescription>Operating temperature trends</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsLineChart data={performanceData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="temperature" stroke="#ef4444" name="Temperature °C" />
                          <ReferenceLine y={85} stroke="#f59e0b" strokeDasharray="5 5" label="Warning" />
                          <ReferenceLine y={95} stroke="#ef4444" strokeDasharray="5 5" label="Critical" />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Fault Frequency</CardTitle>
                      <CardDescription>Daily fault occurrences</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsBarChart data={performanceData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="faults" fill="#f59e0b" name="Daily Faults" />
                        </RechartsBarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Availability and Power Quality */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>System Availability</CardTitle>
                      <CardDescription>Uptime percentage over time</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={performanceData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis domain={[90, 100]} />
                          <Tooltip />
                          <Legend />
                          <Area type="monotone" dataKey="availability" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.3} name="Availability %" />
                          <ReferenceLine y={99} stroke="#10b981" strokeDasharray="5 5" label="Target" />
                        </AreaChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Power Quality Index</CardTitle>
                      <CardDescription>Power quality metrics</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsLineChart data={performanceData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis domain={[95, 100]} />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="powerQuality" stroke="#06b6d4" name="Power Quality %" />
                          <ReferenceLine y={98} stroke="#10b981" strokeDasharray="5 5" label="Standard" />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="comparison" className="space-y-4">
                {/* Transformer Comparison */}
                <Card>
                  <CardHeader>
                    <CardTitle>Transformer Performance Comparison</CardTitle>
                    <CardDescription>Comparative analysis of key transformers</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-3">Transformer</th>
                            <th className="text-left p-3">Region</th>
                            <th className="text-left p-3">Efficiency (%)</th>
                            <th className="text-left p-3">Reliability (%)</th>
                            <th className="text-left p-3">Load Util. (%)</th>
                            <th className="text-left p-3">Maint. Cost (ETB)</th>
                            <th className="text-left p-3">Fault Rate</th>
                            <th className="text-left p-3">Condition</th>
                          </tr>
                        </thead>
                        <tbody>
                          {transformerComparison.map((transformer) => (
                            <tr key={transformer.id} className="border-b hover:bg-gray-50">
                              <td className="p-3 font-medium">{transformer.name}</td>
                              <td className="p-3">{transformer.region}</td>
                              <td className="p-3">
                                <div className="flex items-center gap-2">
                                  <span>{transformer.efficiency}%</span>
                                  <div className={`w-2 h-2 rounded-full ${
                                    transformer.efficiency > 98 ? 'bg-green-500' :
                                    transformer.efficiency > 97 ? 'bg-yellow-500' : 'bg-red-500'
                                  }`}></div>
                                </div>
                              </td>
                              <td className="p-3">{transformer.reliability}%</td>
                              <td className="p-3">{transformer.loadUtilization}%</td>
                              <td className="p-3">{transformer.maintenanceCost.toLocaleString()}</td>
                              <td className="p-3">{transformer.faultRate}</td>
                              <td className="p-3">
                                <Badge className={
                                  transformer.condition === 'excellent' ? 'bg-green-100 text-green-800' :
                                  transformer.condition === 'good' ? 'bg-blue-100 text-blue-800' :
                                  'bg-yellow-100 text-yellow-800'
                                }>
                                  {transformer.condition}
                                </Badge>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>

                {/* Performance Radar Chart */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Performance Radar</CardTitle>
                      <CardDescription>Multi-dimensional performance comparison</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RadarChart data={[
                          { metric: 'Efficiency', 'T-AA-001': 98.5, 'T-OR-045': 97.8, 'T-AM-023': 96.2 },
                          { metric: 'Reliability', 'T-AA-001': 99.2, 'T-OR-045': 96.5, 'T-AM-023': 94.8 },
                          { metric: 'Load Util.', 'T-AA-001': 78, 'T-OR-045': 87, 'T-AM-023': 65 },
                          { metric: 'Cost Eff.', 'T-AA-001': 85, 'T-OR-045': 75, 'T-AM-023': 65 },
                          { metric: 'Condition', 'T-AA-001': 95, 'T-OR-045': 85, 'T-AM-023': 75 }
                        ]}>
                          <PolarGrid />
                          <PolarAngleAxis dataKey="metric" />
                          <PolarRadiusAxis domain={[0, 100]} />
                          <Radar name="T-AA-001" dataKey="T-AA-001" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.1} />
                          <Radar name="T-OR-045" dataKey="T-OR-045" stroke="#10b981" fill="#10b981" fillOpacity={0.1} />
                          <Radar name="T-AM-023" dataKey="T-AM-023" stroke="#f59e0b" fill="#f59e0b" fillOpacity={0.1} />
                          <Legend />
                        </RadarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Efficiency vs Load Correlation</CardTitle>
                      <CardDescription>Relationship between efficiency and load</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <ScatterChart data={transformerComparison.map(t => ({
                          efficiency: t.efficiency,
                          load: t.loadUtilization,
                          name: t.id
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="load" name="Load Utilization %" />
                          <YAxis dataKey="efficiency" name="Efficiency %" domain={[95, 100]} />
                          <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                          <Scatter name="Transformers" dataKey="efficiency" fill="#8b5cf6" />
                        </ScatterChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="regional" className="space-y-4">
                {/* Regional Performance Overview */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Regional Efficiency Comparison</CardTitle>
                      <CardDescription>Average efficiency by region</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsBarChart data={regionalAnalytics}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="region" />
                          <YAxis domain={[95, 100]} />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="avgEfficiency" fill="#3b82f6" name="Avg Efficiency %" />
                          <ReferenceLine y={97} stroke="#f59e0b" strokeDasharray="5 5" label="Target" />
                        </RechartsBarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Regional Load Distribution</CardTitle>
                      <CardDescription>Load utilization across regions</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsPieChart>
                          <Pie
                            data={regionalAnalytics.map((region, index) => ({
                              name: region.region,
                              value: region.totalCapacity,
                              fill: COLORS[index % COLORS.length]
                            }))}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            dataKey="value"
                          />
                          <Tooltip />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Regional Analytics Table */}
                <Card>
                  <CardHeader>
                    <CardTitle>Regional Performance Metrics</CardTitle>
                    <CardDescription>Comprehensive regional analysis</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-3">Region</th>
                            <th className="text-left p-3">Transformers</th>
                            <th className="text-left p-3">Avg Efficiency (%)</th>
                            <th className="text-left p-3">Avg Load (%)</th>
                            <th className="text-left p-3">Total Capacity (MVA)</th>
                            <th className="text-left p-3">Utilization (%)</th>
                            <th className="text-left p-3">Fault Rate</th>
                            <th className="text-left p-3">Maint. Cost (M ETB)</th>
                          </tr>
                        </thead>
                        <tbody>
                          {regionalAnalytics.map((region) => (
                            <tr key={region.region} className="border-b hover:bg-gray-50">
                              <td className="p-3 font-medium">{region.region}</td>
                              <td className="p-3">{region.transformers}</td>
                              <td className="p-3">{region.avgEfficiency}%</td>
                              <td className="p-3">{region.avgLoad}%</td>
                              <td className="p-3">{region.totalCapacity}</td>
                              <td className="p-3">{region.utilizationRate}%</td>
                              <td className="p-3">{region.faultRate}</td>
                              <td className="p-3">{region.maintenanceCost}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="predictive" className="space-y-4">
                {/* Predictive Models Overview */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-blue-600">Model Accuracy</p>
                          <p className="text-2xl font-bold text-blue-600">{kpiMetrics.predictiveAccuracy}%</p>
                        </div>
                        <Brain className="h-8 w-8 text-blue-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-green-600">Predictions Made</p>
                          <p className="text-2xl font-bold text-green-600">1,247</p>
                        </div>
                        <Target className="h-8 w-8 text-green-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-purple-600">Risk Alerts</p>
                          <p className="text-2xl font-bold text-purple-600">23</p>
                        </div>
                        <AlertTriangle className="h-8 w-8 text-purple-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-orange-600">Cost Savings</p>
                          <p className="text-2xl font-bold text-orange-600">2.4M ETB</p>
                        </div>
                        <TrendingDown className="h-8 w-8 text-orange-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Predictive Analytics Charts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Failure Probability Forecast</CardTitle>
                      <CardDescription>AI-predicted failure probability over next 12 months</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsLineChart data={Array.from({ length: 12 }, (_, i) => ({
                          month: `Month ${i + 1}`,
                          probability: Math.max(0, 5 + Math.sin(i * 0.5) * 3 + Math.random() * 2),
                          confidence: 85 + Math.random() * 10
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="probability" stroke="#ef4444" name="Failure Probability %" />
                          <Line type="monotone" dataKey="confidence" stroke="#3b82f6" name="Confidence %" />
                          <ReferenceLine y={10} stroke="#f59e0b" strokeDasharray="5 5" label="Warning Threshold" />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Maintenance Prediction Timeline</CardTitle>
                      <CardDescription>Predicted maintenance requirements</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsBarChart data={Array.from({ length: 12 }, (_, i) => ({
                          month: `M${i + 1}`,
                          preventive: Math.floor(Math.random() * 15) + 5,
                          corrective: Math.floor(Math.random() * 8) + 2,
                          emergency: Math.floor(Math.random() * 3)
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="preventive" stackId="a" fill="#10b981" name="Preventive" />
                          <Bar dataKey="corrective" stackId="a" fill="#f59e0b" name="Corrective" />
                          <Bar dataKey="emergency" stackId="a" fill="#ef4444" name="Emergency" />
                        </RechartsBarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Load Demand Forecast</CardTitle>
                      <CardDescription>Predicted load patterns for next 30 days</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={Array.from({ length: 30 }, (_, i) => ({
                          day: `Day ${i + 1}`,
                          predicted: 70 + Math.sin(i * 0.2) * 15 + Math.random() * 5,
                          actual: i < 15 ? 70 + Math.sin(i * 0.2) * 15 + Math.random() * 5 : null,
                          upper: 70 + Math.sin(i * 0.2) * 15 + 10,
                          lower: 70 + Math.sin(i * 0.2) * 15 - 10
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="day" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Area type="monotone" dataKey="upper" stroke="none" fill="#3b82f6" fillOpacity={0.1} />
                          <Area type="monotone" dataKey="lower" stroke="none" fill="#ffffff" />
                          <Line type="monotone" dataKey="predicted" stroke="#3b82f6" name="Predicted Load %" />
                          <Line type="monotone" dataKey="actual" stroke="#10b981" name="Actual Load %" />
                        </AreaChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Cost Optimization Forecast</CardTitle>
                      <CardDescription>Predicted cost savings from optimization</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsLineChart data={Array.from({ length: 12 }, (_, i) => ({
                          month: `M${i + 1}`,
                          baseline: 100 + i * 2,
                          optimized: 100 + i * 2 - (5 + Math.sin(i * 0.3) * 3),
                          savings: 5 + Math.sin(i * 0.3) * 3
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="baseline" stroke="#ef4444" name="Baseline Cost" strokeDasharray="5 5" />
                          <Line type="monotone" dataKey="optimized" stroke="#10b981" name="Optimized Cost" />
                          <Area type="monotone" dataKey="savings" stroke="#f59e0b" fill="#f59e0b" fillOpacity={0.3} name="Savings" />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* AI Insights and Recommendations */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Brain className="h-5 w-5" />
                        AI Insights
                      </CardTitle>
                      <CardDescription>Machine learning generated insights</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="p-3 bg-blue-50 rounded-lg">
                          <h4 className="font-medium text-blue-900">Load Pattern Analysis</h4>
                          <p className="text-sm text-blue-700 mt-1">
                            Peak load occurs consistently between 6-9 PM. Consider load balancing strategies.
                          </p>
                          <Badge className="mt-2 bg-blue-100 text-blue-800">High Confidence</Badge>
                        </div>
                        <div className="p-3 bg-green-50 rounded-lg">
                          <h4 className="font-medium text-green-900">Efficiency Optimization</h4>
                          <p className="text-sm text-green-700 mt-1">
                            T-AA-001 shows optimal performance. Apply similar parameters to other units.
                          </p>
                          <Badge className="mt-2 bg-green-100 text-green-800">Medium Confidence</Badge>
                        </div>
                        <div className="p-3 bg-yellow-50 rounded-lg">
                          <h4 className="font-medium text-yellow-900">Temperature Correlation</h4>
                          <p className="text-sm text-yellow-700 mt-1">
                            Strong correlation between ambient temperature and transformer load.
                          </p>
                          <Badge className="mt-2 bg-yellow-100 text-yellow-800">High Confidence</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Target className="h-5 w-5" />
                        Risk Assessment
                      </CardTitle>
                      <CardDescription>Predictive risk analysis</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                          <div>
                            <h4 className="font-medium text-red-900">T-OR-045</h4>
                            <p className="text-sm text-red-700">High failure risk detected</p>
                          </div>
                          <Badge className="bg-red-100 text-red-800">Critical</Badge>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                          <div>
                            <h4 className="font-medium text-yellow-900">T-AM-023</h4>
                            <p className="text-sm text-yellow-700">Maintenance required soon</p>
                          </div>
                          <Badge className="bg-yellow-100 text-yellow-800">Warning</Badge>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                          <div>
                            <h4 className="font-medium text-blue-900">Regional Load</h4>
                            <p className="text-sm text-blue-700">Oromia region approaching capacity</p>
                          </div>
                          <Badge className="bg-blue-100 text-blue-800">Monitor</Badge>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <div>
                            <h4 className="font-medium text-green-900">T-AA-001</h4>
                            <p className="text-sm text-green-700">Optimal performance maintained</p>
                          </div>
                          <Badge className="bg-green-100 text-green-800">Good</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Award className="h-5 w-5" />
                        Optimization Recommendations
                      </CardTitle>
                      <CardDescription>AI-powered optimization suggestions</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="p-3 border rounded-lg">
                          <h4 className="font-medium">Load Redistribution</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            Redistribute 15% load from T-OR-045 to T-AA-001 during peak hours
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-green-600">Potential savings: 180K ETB/year</span>
                            <Button size="sm" variant="outline">Apply</Button>
                          </div>
                        </div>
                        <div className="p-3 border rounded-lg">
                          <h4 className="font-medium">Maintenance Scheduling</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            Schedule T-AM-023 maintenance for next week to prevent failure
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-blue-600">Risk reduction: 85%</span>
                            <Button size="sm" variant="outline">Schedule</Button>
                          </div>
                        </div>
                        <div className="p-3 border rounded-lg">
                          <h4 className="font-medium">Efficiency Tuning</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            Adjust tap positions on 3 transformers for optimal efficiency
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-purple-600">Efficiency gain: +0.8%</span>
                            <Button size="sm" variant="outline">Optimize</Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Model Performance Metrics */}
                <Card>
                  <CardHeader>
                    <CardTitle>AI Model Performance</CardTitle>
                    <CardDescription>Machine learning model accuracy and performance metrics</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">89.3%</div>
                        <p className="text-sm text-muted-foreground">Overall Accuracy</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">92.1%</div>
                        <p className="text-sm text-muted-foreground">Failure Prediction</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">87.5%</div>
                        <p className="text-sm text-muted-foreground">Load Forecasting</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">94.2%</div>
                        <p className="text-sm text-muted-foreground">Maintenance Timing</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">0.05%</div>
                        <p className="text-sm text-muted-foreground">False Positive Rate</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-cyan-600">2.4M</div>
                        <p className="text-sm text-muted-foreground">Cost Savings (ETB)</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
      </div>
    </MainLayout>
  )
}
