"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/src/components/ui/dialog"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Shield, RefreshCw, CheckCircle2 } from "lucide-react"
import { Badge } from "@/src/components/ui/badge"
import { useUnifiedUser } from "@/contexts/unified-user-context"
import type { User } from "@/src/types/auth"

interface RoleAssignmentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: User | null
  onAssign: (roleId: string) => void
}

export function RoleAssignmentDialog({
  open,
  onOpenChange,
  user,
  onAssign
}: RoleAssignmentDialogProps) {
  const { roles, isLoading } = useUnifiedUser()
  const [selectedRoleId, setSelectedRoleId] = useState<string>("")

  // Reset form when dialog opens or user changes
  useEffect(() => {
    if (open && user) {
      // Find the current role ID based on the role name
      const currentRole = roles.find(r => r.name.toLowerCase() === user.role.toLowerCase())
      setSelectedRoleId(currentRole?.id || "")
    }
  }, [open, user, roles])

  // Handle save
  const handleSave = () => {
    if (!selectedRoleId) return
    onAssign(selectedRoleId)
  }

  if (!user) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Shield className="mr-2 h-5 w-5" />
            Assign Role
          </DialogTitle>
          <DialogDescription>
            Assign a new role to {user.name}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Current Role:</span>
              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                {user.role.replace(/_/g, ' ')}
              </Badge>
            </div>
            
            <div className="space-y-1">
              <label className="text-sm font-medium">Select New Role:</label>
              <Select value={selectedRoleId} onValueChange={setSelectedRoleId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map(role => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {selectedRoleId && (
              <div className="mt-4 rounded-md border p-3">
                <div className="text-sm font-medium">Role Details:</div>
                <div className="mt-2 space-y-2 text-sm">
                  {roles.find(r => r.id === selectedRoleId)?.description}
                  <div className="flex items-center gap-2">
                    <span>Permissions:</span>
                    <Badge variant="outline">
                      {roles.find(r => r.id === selectedRoleId)?.permissions.length} permissions
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>Organizational Level:</span>
                    <Badge variant="outline">
                      {roles.find(r => r.id === selectedRoleId)?.organizationalLevel.replace(/_/g, ' ')}
                    </Badge>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!selectedRoleId || isLoading}
          >
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Assigning...
              </>
            ) : (
              <>
                <CheckCircle2 className="mr-2 h-4 w-4" />
                Assign Role
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
