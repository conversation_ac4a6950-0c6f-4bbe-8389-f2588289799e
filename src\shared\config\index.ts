/**
 * Configuration Index
 * Central export for all configuration modules
 */

// Export all configurations
export { default as appConfig, APP_CONFIG, getConfig } from './app.config'
export { default as databaseConfig, getDatabaseConfig, validateDatabaseConfig } from './database.config'
export { default as authConfig, getAuthConfig, validateAuthConfig, ROLE_PERMISSIONS } from './auth.config'
export { default as apiConfig, getApiConfig, validateApiConfig, API_ENDPOINTS, HTTP_STATUS } from './api.config'

// Export types
export type { AppConfig, FeatureFlags, UIConfig, SecurityConfig } from './app.config'
export type { DatabaseConfig } from './database.config'
export type { AuthConfig } from './auth.config'
export type { ApiConfig, ApiResponse, ApiError } from './api.config'

// Combined configuration interface
export interface EEUConfig {
  app: ReturnType<typeof import('./app.config').getConfig>
  database: ReturnType<typeof import('./database.config').getDatabaseConfig>
  auth: ReturnType<typeof import('./auth.config').getAuthConfig>
  api: ReturnType<typeof import('./api.config').getApiConfig>
}

// Get all configurations
export const getEEUConfig = (): EEUConfig => ({
  app: require('./app.config').getConfig(),
  database: require('./database.config').getDatabaseConfig(),
  auth: require('./auth.config').getAuthConfig(),
  api: require('./api.config').getApiConfig()
})

// Validate all configurations
export const validateAllConfigs = (): boolean => {
  const config = getEEUConfig()
  
  try {
    require('./database.config').validateDatabaseConfig(config.database)
    require('./auth.config').validateAuthConfig(config.auth)
    require('./api.config').validateApiConfig(config.api)
    
    console.log('✅ All configurations validated successfully')
    return true
  } catch (error) {
    console.error('❌ Configuration validation failed:', error)
    throw error
  }
}

// Environment-specific configuration loader
export const loadEnvironmentConfig = () => {
  const env = process.env.NODE_ENV || 'development'
  
  console.log(`🔧 Loading configuration for environment: ${env}`)
  
  const config = getEEUConfig()
  
  // Log configuration summary (without sensitive data)
  console.log('📋 Configuration Summary:')
  console.log(`  App: ${config.app.name} v${config.app.version}`)
  console.log(`  Database: ${config.database.provider}`)
  console.log(`  Auth Strategy: ${config.auth.session.strategy}`)
  console.log(`  API Base URL: ${config.api.baseUrl}`)
  
  return config
}

// Configuration constants
export const CONFIG_CONSTANTS = {
  ENVIRONMENTS: ['development', 'test', 'staging', 'production'] as const,
  DATABASE_PROVIDERS: ['mysql', 'sqlite', 'supabase'] as const,
  AUTH_STRATEGIES: ['jwt', 'database'] as const,
  CACHE_STRATEGIES: ['memory', 'redis', 'database'] as const
} as const

// Default export
export default getEEUConfig()
