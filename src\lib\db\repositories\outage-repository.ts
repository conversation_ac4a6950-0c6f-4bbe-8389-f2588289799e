/**
 * Outage repository
 * 
 * This class provides specialized methods for working with outage entities.
 */

import { BaseRepository } from './base-repository';
import { Outage, OutageStatus } from '../schema';

export class OutageRepository extends BaseRepository<Outage> {
  constructor() {
    super('outages');
  }
  
  /**
   * Find outages by transformer
   */
  findByTransformer(transformerId: string): Outage[] {
    return this.find({ transformerId });
  }
  
  /**
   * Find outages by status
   */
  findByStatus(status: OutageStatus | OutageStatus[]): Outage[] {
    if (Array.isArray(status)) {
      return this.find({}).filter(outage => status.includes(outage.status));
    }
    return this.find({ status });
  }
  
  /**
   * Find outages by cause
   */
  findByCause(cause: string): Outage[] {
    return this.find({}).filter(outage => 
      outage.cause && outage.cause.toLowerCase().includes(cause.toLowerCase())
    );
  }
  
  /**
   * Find outages by reported user
   */
  findByReportedUser(userId: string): Outage[] {
    return this.find({ reportedBy: userId });
  }
  
  /**
   * Find outages by resolved user
   */
  findByResolvedUser(userId: string): Outage[] {
    return this.find({ resolvedBy: userId });
  }
  
  /**
   * Find outages by date range
   */
  findByDateRange(startDate: Date, endDate: Date): Outage[] {
    return this.find({}).filter(outage => {
      const outageStartDate = new Date(outage.startTime);
      return outageStartDate >= startDate && outageStartDate <= endDate;
    });
  }
  
  /**
   * Find active outages
   */
  findActive(): Outage[] {
    return this.find({ status: 'active' });
  }
  
  /**
   * Find scheduled outages
   */
  findScheduled(): Outage[] {
    return this.find({ status: 'scheduled' });
  }
  
  /**
   * Find resolved outages
   */
  findResolved(): Outage[] {
    return this.find({ status: 'resolved' });
  }
  
  /**
   * Find outages with high impact (affecting many customers)
   */
  findHighImpact(minAffectedCustomers: number = 100): Outage[] {
    return this.find({}).filter(outage => 
      outage.affectedCustomers >= minAffectedCustomers
    );
  }
  
  /**
   * Resolve an outage
   */
  resolveOutage(id: string, resolvedBy: string): Outage | null {
    return this.update(id, {
      status: 'resolved',
      endTime: new Date().toISOString(),
      resolvedBy
    });
  }
  
  /**
   * Get outage statistics
   */
  getStatistics() {
    const outages = this.getAll();
    
    // Count by status
    const statusCounts = outages.reduce((counts, outage) => {
      counts[outage.status] = (counts[outage.status] || 0) + 1;
      return counts;
    }, {} as Record<OutageStatus, number>);
    
    // Count by cause
    const causeCounts = outages.reduce((counts, outage) => {
      if (outage.cause) {
        counts[outage.cause] = (counts[outage.cause] || 0) + 1;
      }
      return counts;
    }, {} as Record<string, number>);
    
    // Calculate total affected customers
    const totalAffectedCustomers = outages.reduce(
      (sum, outage) => sum + outage.affectedCustomers, 
      0
    );
    
    // Calculate average duration for resolved outages
    const resolvedOutages = outages.filter(
      outage => outage.status === 'resolved' && outage.endTime
    );
    
    const averageDuration = resolvedOutages.length > 0
      ? resolvedOutages.reduce((sum, outage) => {
          const startTime = new Date(outage.startTime).getTime();
          const endTime = new Date(outage.endTime!).getTime();
          const durationHours = (endTime - startTime) / (1000 * 60 * 60);
          return sum + durationHours;
        }, 0) / resolvedOutages.length
      : 0;
    
    return {
      total: outages.length,
      active: statusCounts.active || 0,
      scheduled: statusCounts.scheduled || 0,
      resolved: statusCounts.resolved || 0,
      byCause: causeCounts,
      totalAffectedCustomers,
      averageDuration
    };
  }
}

// Export a singleton instance
export const outageRepository = new OutageRepository();
