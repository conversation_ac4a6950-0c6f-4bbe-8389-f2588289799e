"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { Button } from "@/src/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/src/components/ui/dialog"
import { Search, Filter, AlertCircle, Bell, CheckCircle, Clock, AlertTriangle } from "lucide-react"
import { SmartMeterAlert } from "@/src/types/smart-meter"
import { smartMeterService } from "@/src/services/smart-meter-service"

interface SmartMeterAlertsProps {
  initialAlerts?: SmartMeterAlert[]
}

export function SmartMeterAlerts({ initialAlerts = [] }: SmartMeterAlertsProps) {
  const [alerts, setAlerts] = useState<SmartMeterAlert[]>(initialAlerts)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [severityFilter, setSeverityFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")

  // Fetch alerts directly from the service instead of the API
  useEffect(() => {
    const fetchAlerts = async () => {
      try {
        setLoading(true)

        // Get alerts directly from the service
        let alertsData: SmartMeterAlert[] = []

        if (typeFilter !== "all" || severityFilter !== "all" || statusFilter !== "all") {
          // Filter alerts
          alertsData = await smartMeterService.filterAlerts({
            type: typeFilter !== "all" ? typeFilter : undefined,
            severity: severityFilter !== "all" ? severityFilter : undefined,
            status: statusFilter !== "all" ? statusFilter : undefined
          })
        } else {
          // Get all alerts
          alertsData = await smartMeterService.getAllAlerts()
        }

        setAlerts(alertsData)
      } catch (error) {
        console.error("Error fetching alerts:", error)
      } finally {
        setLoading(false)
      }
    }

    // Use initial alerts if provided, otherwise fetch from service
    if (initialAlerts.length > 0 && typeFilter === "all" && severityFilter === "all" && statusFilter === "all") {
      setAlerts(initialAlerts)
      setLoading(false)
    } else {
      fetchAlerts()
    }
  }, [initialAlerts, typeFilter, severityFilter, statusFilter])

  // Filter alerts by search query
  const filteredAlerts = alerts.filter(alert => {
    if (!searchQuery) return true

    const query = searchQuery.toLowerCase()
    return (
      alert.id.toLowerCase().includes(query) ||
      alert.meterId.toLowerCase().includes(query) ||
      alert.message.toLowerCase().includes(query) ||
      alert.type.toLowerCase().includes(query)
    )
  })

  // Get severity badge color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "Low":
        return "bg-blue-500 hover:bg-blue-600"
      case "Medium":
        return "bg-yellow-500 hover:bg-yellow-600"
      case "High":
        return "bg-orange-500 hover:bg-orange-600"
      case "Critical":
        return "bg-red-500 hover:bg-red-600"
      default:
        return "bg-gray-500 hover:bg-gray-600"
    }
  }

  // State for alert details dialog
  const [selectedAlert, setSelectedAlert] = useState<SmartMeterAlert | null>(null)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)

  // Handle updating alert status
  const handleUpdateAlertStatus = async (alert: SmartMeterAlert, newStatus: "Acknowledged" | "Resolved" | "Ignored") => {
    try {
      setIsUpdating(true)

      // Create a copy of the alert with the updated status
      const updatedAlert = {
        ...alert,
        status: newStatus,
        resolvedAt: newStatus === "Resolved" ? new Date().toISOString() : alert.resolvedAt,
        resolvedBy: newStatus === "Resolved" ? "User" : alert.resolvedBy
      }

      // Update the alert in the local state
      const updatedAlerts = alerts.map(a => a.id === alert.id ? updatedAlert : a)
      setAlerts(updatedAlerts)

      // Show success message (in a real app, you would also update the backend)
      console.log(`Alert ${alert.id} status updated to ${newStatus}`)
    } catch (error) {
      console.error("Error updating alert status:", error)
    } finally {
      setIsUpdating(false)
    }
  }

  // Handle viewing alert details
  const handleViewAlertDetails = (alert: SmartMeterAlert) => {
    setSelectedAlert(alert)
    setShowDetailsDialog(true)
  }

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "New":
        return "bg-blue-500 hover:bg-blue-600"
      case "Acknowledged":
        return "bg-yellow-500 hover:bg-yellow-600"
      case "Resolved":
        return "bg-green-500 hover:bg-green-600"
      case "Ignored":
        return "bg-gray-500 hover:bg-gray-600"
      default:
        return "bg-gray-500 hover:bg-gray-600"
    }
  }

  // Get alert type icon
  const getAlertTypeIcon = (type: string) => {
    switch (type) {
      case "Disconnection":
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case "Tampering":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />
      case "LowBattery":
        return <Battery className="h-4 w-4 text-yellow-500" />
      case "CommunicationFailure":
        return <WifiOff className="h-4 w-4 text-red-500" />
      case "HighConsumption":
        return <TrendingUp className="h-4 w-4 text-blue-500" />
      case "LowConsumption":
        return <TrendingDown className="h-4 w-4 text-blue-500" />
      default:
        return <Bell className="h-4 w-4 text-gray-500" />
    }
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleString()
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <CardTitle>Smart Meter Alerts</CardTitle>
            <CardDescription>Alerts and notifications from smart meters</CardDescription>
          </div>
          <div className="flex items-center gap-2 mt-2 sm:mt-0">
            <Badge className="bg-blue-500">{alerts.filter(a => a.status === "New").length} New</Badge>
            <Badge className="bg-yellow-500">{alerts.filter(a => a.status === "Acknowledged").length} Acknowledged</Badge>
            <Badge className="bg-green-500">{alerts.filter(a => a.status === "Resolved").length} Resolved</Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4 sm:flex-row mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search alerts..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full sm:w-[160px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Alert Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Disconnection">Disconnection</SelectItem>
                <SelectItem value="Tampering">Tampering</SelectItem>
                <SelectItem value="LowBattery">Low Battery</SelectItem>
                <SelectItem value="CommunicationFailure">Communication Failure</SelectItem>
                <SelectItem value="HighConsumption">High Consumption</SelectItem>
                <SelectItem value="LowConsumption">Low Consumption</SelectItem>
              </SelectContent>
            </Select>

            <Select value={severityFilter} onValueChange={setSeverityFilter}>
              <SelectTrigger className="w-full sm:w-[160px]">
                <AlertCircle className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Severity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severities</SelectItem>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="High">High</SelectItem>
                <SelectItem value="Critical">Critical</SelectItem>
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[160px]">
                <Clock className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="New">New</SelectItem>
                <SelectItem value="Acknowledged">Acknowledged</SelectItem>
                <SelectItem value="Resolved">Resolved</SelectItem>
                <SelectItem value="Ignored">Ignored</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : filteredAlerts.length === 0 ? (
          <div className="text-center py-8">
            <Bell className="mx-auto h-12 w-12 text-muted-foreground opacity-50" />
            <h3 className="mt-2 text-lg font-medium">No alerts found</h3>
            <p className="text-sm text-muted-foreground">
              {searchQuery ? "Try adjusting your search or filters" : "All smart meters are operating normally"}
            </p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Meter ID</TableHead>
                  <TableHead>Message</TableHead>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>Severity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAlerts.map((alert) => (
                  <TableRow key={alert.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getAlertTypeIcon(alert.type)}
                        <span>{alert.type}</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">{alert.meterId}</TableCell>
                    <TableCell>{alert.message}</TableCell>
                    <TableCell>{new Date(alert.timestamp).toLocaleString()}</TableCell>
                    <TableCell>
                      <Badge className={getSeverityColor(alert.severity)}>
                        {alert.severity}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(alert.status)}>
                        {alert.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      {alert.status === "New" && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUpdateAlertStatus(alert, "Acknowledged")}
                        >
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Acknowledge
                        </Button>
                      )}
                      {alert.status === "Acknowledged" && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUpdateAlertStatus(alert, "Resolved")}
                        >
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Resolve
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-2"
                        onClick={() => handleViewAlertDetails(alert)}
                      >
                        Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      {/* Alert Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="sm:max-w-[600px]">
          {selectedAlert && (
            <>
              <DialogHeader>
                <DialogTitle>Alert Details</DialogTitle>
                <DialogDescription>
                  Detailed information about this alert
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Alert ID</Label>
                  <div className="col-span-3">
                    <p className="font-medium">{selectedAlert.id}</p>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Meter ID</Label>
                  <div className="col-span-3">
                    <p className="font-medium">{selectedAlert.meterId}</p>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Type</Label>
                  <div className="col-span-3 flex items-center gap-2">
                    {getAlertTypeIcon(selectedAlert.type)}
                    <p className="font-medium">{selectedAlert.type}</p>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Severity</Label>
                  <div className="col-span-3">
                    <Badge className={getSeverityColor(selectedAlert.severity)}>
                      {selectedAlert.severity}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Status</Label>
                  <div className="col-span-3">
                    <Badge className={getStatusColor(selectedAlert.status)}>
                      {selectedAlert.status}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Timestamp</Label>
                  <div className="col-span-3">
                    <p className="font-medium">{formatDate(selectedAlert.timestamp)}</p>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Message</Label>
                  <div className="col-span-3">
                    <p className="font-medium">{selectedAlert.message}</p>
                  </div>
                </div>

                {selectedAlert.resolvedAt && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label className="text-right">Resolved At</Label>
                    <div className="col-span-3">
                      <p className="font-medium">{formatDate(selectedAlert.resolvedAt)}</p>
                    </div>
                  </div>
                )}

                {selectedAlert.resolvedBy && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label className="text-right">Resolved By</Label>
                    <div className="col-span-3">
                      <p className="font-medium">{selectedAlert.resolvedBy}</p>
                    </div>
                  </div>
                )}

                {selectedAlert.notes && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label className="text-right">Notes</Label>
                    <div className="col-span-3">
                      <p className="font-medium">{selectedAlert.notes}</p>
                    </div>
                  </div>
                )}
              </div>

              <DialogFooter>
                {selectedAlert.status === "New" && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleUpdateAlertStatus(selectedAlert, "Acknowledged")
                      setShowDetailsDialog(false)
                    }}
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Acknowledge
                  </Button>
                )}

                {selectedAlert.status === "Acknowledged" && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleUpdateAlertStatus(selectedAlert, "Resolved")
                      setShowDetailsDialog(false)
                    }}
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Resolve
                  </Button>
                )}

                <Button
                  variant="secondary"
                  onClick={() => setShowDetailsDialog(false)}
                >
                  Close
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  )
}

// Import these icons
function Battery(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect x="2" y="7" width="16" height="10" rx="2" ry="2" />
      <line x1="22" x2="22" y1="11" y2="13" />
      <line x1="6" x2="6" y1="10" y2="14" />
    </svg>
  )
}

function WifiOff(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <line x1="2" x2="22" y1="2" y2="22" />
      <path d="M8.5 16.5a5 5 0 0 1 7 0" />
      <path d="M2 8.82a15 15 0 0 1 4.17-2.65" />
      <path d="M10.66 5c4.01-.36 8.14.9 11.34 3.76" />
      <path d="M16.85 11.25a10 10 0 0 1 2.22 1.68" />
      <path d="M5 13a10 10 0 0 1 5.24-2.76" />
      <line x1="12" x2="12.01" y1="20" y2="20" />
    </svg>
  )
}

function TrendingUp(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <polyline points="22 7 13.5 15.5 8.5 10.5 2 17" />
      <polyline points="16 7 22 7 22 13" />
    </svg>
  )
}

function TrendingDown(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <polyline points="22 17 13.5 8.5 8.5 13.5 2 7" />
      <polyline points="16 17 22 17 22 11" />
    </svg>
  )
}
