"use client"

import { useEffect, useRef, useState } from "react"
import { MapLocation } from "@/src/services/map-service"
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Badge } from "@/src/components/ui/badge"
import { Button } from "@/src/components/ui/button"
import { MapPin, Eye, Map, Satellite, Layers, Locate, Maximize2, Minimize2 } from "lucide-react"
import { TransformerMapFilters, TransformerFilters } from './transformers/transformer-map-filters'
import { TransformerMapStats } from './transformers/transformer-map-stats'
import { TransformerMapToolbar } from './transformers/transformer-map-toolbar'

// Leaflet types
declare global {
  interface Window {
    L: any;
  }
}

interface MapboxMapProps {
  locations?: MapLocation[]
  center?: [number, number]
  zoom?: number
  interactive?: boolean
  showControls?: boolean
  height?: string
  onMarkerClick?: (location: MapLocation) => void
  onMapClick?: (lngLat: { lng: number; lat: number }) => void
  onMapLoad?: (map: any) => void
  mapStyle?: 'light' | 'dark' | 'satellite' | 'streets'
  clustered?: boolean
}

// Real Mapbox satellite map component
const MapboxSatelliteMap = ({
  locations = [],
  height = "500px",
  onMarkerClick,
  mapStyle = 'satellite'
}: {
  locations: MapLocation[]
  height?: string
  onMarkerClick?: (location: MapLocation) => void
  mapStyle?: 'light' | 'dark' | 'satellite' | 'streets'
}) => {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<any>(null)
  const markers = useRef<any[]>([])
  const [mapLoaded, setMapLoaded] = useState(false)
  const [mapboxLoaded, setMapboxLoaded] = useState(false)
  const [currentStyle, setCurrentStyle] = useState(mapStyle)
  const [fullscreen, setFullscreen] = useState(false)
  const [satelliteProvider, setSatelliteProvider] = useState('google_hybrid') // esri, google, google_hybrid, bing, mapbox
  const [showRoadsOverlay, setShowRoadsOverlay] = useState(true)
  const [showLabelsOverlay, setShowLabelsOverlay] = useState(true)
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null)
  const [showDetailOverlay, setShowDetailOverlay] = useState(false)

  // Filter and stats state
  const [showFilters, setShowFilters] = useState(false)
  const [showStats, setShowStats] = useState(false)
  const [filters, setFilters] = useState<TransformerFilters>({
    search: '',
    status: [],
    region: [],
    serviceCenter: [],
    manufacturer: [],
    type: [],
    voltageLevel: [],
    capacityRange: [0, 5000],
    healthIndexRange: [0, 100],
    installationYear: [],
    lastMaintenanceRange: [0, 365],
    showCriticalOnly: false,
    showMaintenanceDue: false
  })
  const [filteredLocations, setFilteredLocations] = useState<MapLocation[]>(locations)
  const [selectedMarkers, setSelectedMarkers] = useState<string[]>([])
  const [showGrid, setShowGrid] = useState(false)
  const [showMeasurement, setShowMeasurement] = useState(false)
  const [clusteredView, setClustered] = useState(true)

  // Get status color for markers
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'operational': return '#22c55e' // green-500
      case 'warning': return '#f97316' // orange-500
      case 'maintenance': return '#f59e0b' // amber-500
      case 'critical': return '#ef4444' // red-500
      case 'burnt': return '#7c3aed' // purple-600
      case 'offline': return '#6b7280' // gray-500
      default: return '#6b7280' // gray-500
    }
  }

  // Apply filters to locations
  const applyFilters = (locations: MapLocation[], filters: TransformerFilters) => {
    let filtered = [...locations]

    // Search filter
    if (filters.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(location =>
        location.title.toLowerCase().includes(search) ||
        location.description.toLowerCase().includes(search) ||
        (location.data?.id && location.data.id.toLowerCase().includes(search)) ||
        (location.data?.manufacturer && location.data.manufacturer.toLowerCase().includes(search)) ||
        (location.data?.model && location.data.model.toLowerCase().includes(search))
      )
    }

    // Status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(location =>
        filters.status.some(status => status.toLowerCase() === location.status.toLowerCase())
      )
    }

    // Region filter
    if (filters.region.length > 0) {
      filtered = filtered.filter(location =>
        location.data?.location?.region &&
        filters.region.some(region => region.toLowerCase() === location.data.location.region.toLowerCase())
      )
    }

    // Service Center filter
    if (filters.serviceCenter.length > 0) {
      filtered = filtered.filter(location =>
        location.data?.location?.serviceCenter &&
        filters.serviceCenter.some(sc => sc.toLowerCase() === location.data.location.serviceCenter.toLowerCase())
      )
    }

    // Manufacturer filter
    if (filters.manufacturer.length > 0) {
      filtered = filtered.filter(location =>
        location.data?.manufacturer &&
        filters.manufacturer.some(mfg => mfg.toLowerCase() === location.data.manufacturer.toLowerCase())
      )
    }

    // Type filter
    if (filters.type.length > 0) {
      filtered = filtered.filter(location =>
        location.data?.type &&
        filters.type.some(type => type.toLowerCase() === location.data.type.toLowerCase())
      )
    }

    // Voltage Level filter
    if (filters.voltageLevel.length > 0) {
      filtered = filtered.filter(location =>
        location.data?.voltagePrimary &&
        filters.voltageLevel.some(voltage => {
          const locationVoltage = `${location.data.voltagePrimary}V`
          return voltage === locationVoltage || voltage.includes(String(location.data.voltagePrimary))
        })
      )
    }

    // Capacity Range filter
    if (filters.capacityRange[0] > 0 || filters.capacityRange[1] < 5000) {
      filtered = filtered.filter(location => {
        if (!location.data?.capacity) return false
        const capacity = parseFloat(location.data.capacity)
        return capacity >= filters.capacityRange[0] && capacity <= filters.capacityRange[1]
      })
    }

    // Health Index Range filter
    if (filters.healthIndexRange[0] > 0 || filters.healthIndexRange[1] < 100) {
      filtered = filtered.filter(location => {
        if (!location.data?.healthIndex) return false
        const health = location.data.healthIndex
        return health >= filters.healthIndexRange[0] && health <= filters.healthIndexRange[1]
      })
    }

    // Critical Only filter
    if (filters.showCriticalOnly) {
      filtered = filtered.filter(location =>
        ['critical', 'burnt'].includes(location.status.toLowerCase())
      )
    }

    // Maintenance Due filter
    if (filters.showMaintenanceDue) {
      filtered = filtered.filter(location => {
        if (!location.data?.nextMaintenanceDate) return false
        const nextMaintenance = new Date(location.data.nextMaintenanceDate)
        const now = new Date()
        const daysDiff = (nextMaintenance.getTime() - now.getTime()) / (1000 * 3600 * 24)
        return daysDiff <= 30 // Due within 30 days
      })
    }

    return filtered
  }

  // Calculate statistics
  const calculateStats = (locations: MapLocation[]) => {
    const statusCounts = {
      operational: 0,
      warning: 0,
      maintenance: 0,
      critical: 0,
      burnt: 0,
      offline: 0
    }

    const regionCounts: Record<string, number> = {}
    let totalHealth = 0
    let healthCount = 0
    let maintenanceDue = 0
    let criticalAlerts = 0

    locations.forEach(location => {
      // Count by status
      const status = location.status.toLowerCase()
      if (status in statusCounts) {
        statusCounts[status as keyof typeof statusCounts]++
      }

      // Count by region
      if (location.data?.location?.region) {
        const region = location.data.location.region
        regionCounts[region] = (regionCounts[region] || 0) + 1
      }

      // Calculate health
      if (location.data?.healthIndex) {
        totalHealth += location.data.healthIndex
        healthCount++
      }

      // Count maintenance due
      if (location.data?.nextMaintenanceDate) {
        const nextMaintenance = new Date(location.data.nextMaintenanceDate)
        const now = new Date()
        const daysDiff = (nextMaintenance.getTime() - now.getTime()) / (1000 * 3600 * 24)
        if (daysDiff <= 30) maintenanceDue++
      }

      // Count critical alerts
      if (['critical', 'burnt'].includes(status)) {
        criticalAlerts++
      }
    })

    return {
      statusCounts,
      regionCounts,
      averageHealth: healthCount > 0 ? totalHealth / healthCount : 0,
      maintenanceDue,
      criticalAlerts
    }
  }

  // Update filtered locations when filters or locations change
  useEffect(() => {
    const filtered = applyFilters(locations, filters)
    setFilteredLocations(filtered)
  }, [locations, filters])

  // Get Mapbox style URL
  const getMapboxStyle = (style: string) => {
    switch (style) {
      case 'satellite': return 'mapbox://styles/mapbox/satellite-streets-v12' // Satellite with roads and labels
      case 'dark': return 'mapbox://styles/mapbox/dark-v11'
      case 'streets': return 'mapbox://styles/mapbox/streets-v12'
      default: return 'mapbox://styles/mapbox/light-v11' // light
    }
  }

  // Load Leaflet for satellite map (better compatibility)
  useEffect(() => {
    const loadLeaflet = async () => {
      try {
        // Load Leaflet CSS
        if (!document.querySelector('link[href*="leaflet.css"]')) {
          const link = document.createElement('link')
          link.rel = 'stylesheet'
          link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'
          document.head.appendChild(link)
        }

        // Load Leaflet JS
        if (!window.L) {
          const script = document.createElement('script')
          script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js'
          script.onload = () => {
            setMapboxLoaded(true)
          }
          document.head.appendChild(script)
        } else {
          setMapboxLoaded(true)
        }
      } catch (error) {
        console.error('Error loading Leaflet:', error)
      }
    }

    loadLeaflet()
  }, [])

  // Initialize map
  useEffect(() => {
    if (!mapboxLoaded || !mapContainer.current || map.current) return

    try {
      // Ethiopia center coordinates
      const ethiopiaCenter = [9.0222, 38.7468] // [lat, lng] for Addis Ababa

      // Initialize Leaflet map
      map.current = window.L.map(mapContainer.current, {
        center: ethiopiaCenter,
        zoom: 6,
        zoomControl: true
      })

      // Get satellite tile layer based on provider
      const getSatelliteTileLayer = (provider: string) => {
        switch (provider) {
          case 'esri':
            return window.L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
              attribution: '&copy; <a href="https://www.esri.com/">Esri</a> &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
              maxZoom: 20
            })
          case 'google':
            return window.L.tileLayer('https://mt{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', {
              attribution: '&copy; <a href="https://www.google.com/maps">Google</a>',
              maxZoom: 21,
              subdomains: ['0', '1', '2', '3']
            })
          case 'google_hybrid':
            return window.L.tileLayer('https://mt{s}.google.com/vt/lyrs=y&x={x}&y={y}&z={z}', {
              attribution: '&copy; <a href="https://www.google.com/maps">Google</a>',
              maxZoom: 21,
              subdomains: ['0', '1', '2', '3']
            })
          case 'bing':
            return window.L.tileLayer('https://ecn.t{s}.tiles.virtualearth.net/tiles/a{q}.jpeg?g=587&mkt=en-gb&n=z', {
              attribution: '&copy; <a href="https://www.bing.com/maps">Microsoft Bing Maps</a>',
              maxZoom: 20,
              subdomains: ['0', '1', '2', '3']
            })
          case 'mapbox':
            return window.L.tileLayer('https://api.mapbox.com/styles/v1/mapbox/satellite-streets-v12/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4M3VycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw', {
              attribution: '&copy; <a href="https://www.mapbox.com/">Mapbox</a>',
              maxZoom: 22
            })
          default:
            return window.L.tileLayer('https://mt{s}.google.com/vt/lyrs=y&x={x}&y={y}&z={z}', {
              attribution: '&copy; <a href="https://www.google.com/maps">Google</a>',
              maxZoom: 21,
              subdomains: ['0', '1', '2', '3']
            })
        }
      }

      // Get tile layer based on style
      const getTileLayer = (style: string) => {
        switch (style) {
          case 'satellite':
            return getSatelliteTileLayer(satelliteProvider)
          case 'streets':
            return window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
              attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
              maxZoom: 18
            })
          case 'dark':
            return window.L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
              attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
              maxZoom: 18
            })
          default: // light
            return window.L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
              attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
              maxZoom: 18
            })
        }
      }

      // Add initial tile layer
      const initialLayer = getTileLayer(mapStyle)
      initialLayer.addTo(map.current)
      map.current.currentTileLayer = initialLayer

      // Add overlays for satellite view (only for providers that don't include them)
      if (mapStyle === 'satellite' && !['google_hybrid', 'mapbox'].includes(satelliteProvider)) {
        // Add roads overlay if enabled
        if (showRoadsOverlay) {
          const roadsLayer = window.L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/toner-lines/{z}/{x}/{y}{r}.png', {
            attribution: 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18,
            opacity: 0.8
          })
          roadsLayer.addTo(map.current)
          map.current.roadsLayer = roadsLayer
        }

        // Add labels overlay if enabled
        if (showLabelsOverlay) {
          const labelsLayer = window.L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/toner-labels/{z}/{x}/{y}{r}.png', {
            attribution: 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18,
            opacity: 0.9
          })
          labelsLayer.addTo(map.current)
          map.current.labelsLayer = labelsLayer
        }
      }

      setMapLoaded(true)

      return () => {
        if (map.current) {
          map.current.remove()
          map.current = null
        }
      }
    } catch (error) {
      console.error('Error initializing Leaflet map:', error)
    }
  }, [mapboxLoaded, mapStyle])

  // Update markers when filtered locations change
  useEffect(() => {
    if (!map.current || !mapLoaded || !window.L) return

    // Clear existing markers
    markers.current.forEach(marker => map.current.removeLayer(marker))
    markers.current = []

    // Add new markers for filtered locations
    filteredLocations.forEach(location => {
      try {
        // Create custom icon
        const customIcon = window.L.divIcon({
          className: 'custom-transformer-marker',
          html: `<div style="
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: ${getStatusColor(location.status)};
            border: 3px solid white;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
          ">
            <div style="
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: white;
              opacity: 0.8;
            "></div>
          </div>`,
          iconSize: [20, 20],
          iconAnchor: [10, 10],
          popupAnchor: [0, -10]
        })

        // Create marker
        const marker = window.L.marker([location.latitude, location.longitude], {
          icon: customIcon
        }).addTo(map.current)

        // Create popup content
        const popupContent = `
          <div style="min-width: 200px; max-width: 300px;">
            <h3 style="font-weight: bold; margin-bottom: 8px; font-size: 16px; color: #1f2937;">
              ${location.title}
            </h3>
            <p style="margin: 0 0 8px 0; font-size: 14px; color: #6b7280;">
              ${location.description}
            </p>
            <div style="display: flex; align-items: center; gap: 8px; margin-top: 8px; padding: 6px 8px; background-color: #f9fafb; border-radius: 4px;">
              <div style="width: 12px; height: 12px; border-radius: 50%; background-color: ${getStatusColor(location.status)};"></div>
              <span style="font-size: 14px; color: #374151;">Status:</span>
              <span style="color: ${getStatusColor(location.status)}; font-weight: bold; font-size: 14px; text-transform: capitalize;">
                ${location.status}
              </span>
            </div>
            ${location.data ? `
              <div style="margin-top: 12px; padding: 8px; background-color: #f3f4f6; border-radius: 4px; font-size: 12px;">
                <div style="margin-bottom: 4px;"><strong>ID:</strong> ${location.data.id}</div>
                ${location.data.capacity ? `<div style="margin-bottom: 4px;"><strong>Capacity:</strong> ${location.data.capacity} kVA</div>` : ''}
                ${location.data.manufacturer ? `<div style="margin-bottom: 4px;"><strong>Manufacturer:</strong> ${location.data.manufacturer}</div>` : ''}
                ${location.data.model ? `<div style="margin-bottom: 4px;"><strong>Model:</strong> ${location.data.model}</div>` : ''}
                ${location.data.location ? `<div><strong>Location:</strong> ${location.data.location.name}</div>` : ''}
              </div>
            ` : ''}
          </div>
        `

        // Bind popup
        marker.bindPopup(popupContent)

        // Add click event
        marker.on('click', () => {
          setSelectedLocation(location)
          setShowDetailOverlay(true)
          if (onMarkerClick) {
            onMarkerClick(location)
          }
        })

        markers.current.push(marker)
      } catch (error) {
        console.error('Error creating marker:', error)
      }
    })

    // Fit bounds if there are filtered locations
    if (filteredLocations.length > 0) {
      try {
        const group = new window.L.featureGroup(markers.current)
        map.current.fitBounds(group.getBounds(), {
          padding: [20, 20],
          maxZoom: 15
        })
      } catch (error) {
        console.error('Error fitting bounds:', error)
      }
    }
  }, [filteredLocations, mapLoaded, onMarkerClick])

  // Handle style change
  const changeMapStyle = (style: 'light' | 'dark' | 'satellite' | 'streets') => {
    if (map.current && mapLoaded && window.L) {
      try {
        // Remove current tile layer
        if (map.current.currentTileLayer) {
          map.current.removeLayer(map.current.currentTileLayer)
        }

        // Remove overlay layers if they exist
        if (map.current.roadsLayer) {
          map.current.removeLayer(map.current.roadsLayer)
          map.current.roadsLayer = null
        }
        if (map.current.labelsLayer) {
          map.current.removeLayer(map.current.labelsLayer)
          map.current.labelsLayer = null
        }

        // Get satellite tile layer based on provider
        const getSatelliteTileLayer = (provider: string) => {
          switch (provider) {
            case 'esri':
              return window.L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: '&copy; <a href="https://www.esri.com/">Esri</a> &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 20
              })
            case 'google':
              return window.L.tileLayer('https://mt{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', {
                attribution: '&copy; <a href="https://www.google.com/maps">Google</a>',
                maxZoom: 21,
                subdomains: ['0', '1', '2', '3']
              })
            case 'google_hybrid':
              return window.L.tileLayer('https://mt{s}.google.com/vt/lyrs=y&x={x}&y={y}&z={z}', {
                attribution: '&copy; <a href="https://www.google.com/maps">Google</a>',
                maxZoom: 21,
                subdomains: ['0', '1', '2', '3']
              })
            case 'bing':
              return window.L.tileLayer('https://ecn.t{s}.tiles.virtualearth.net/tiles/a{q}.jpeg?g=587&mkt=en-gb&n=z', {
                attribution: '&copy; <a href="https://www.bing.com/maps">Microsoft Bing Maps</a>',
                maxZoom: 20,
                subdomains: ['0', '1', '2', '3']
              })
            case 'mapbox':
              return window.L.tileLayer('https://api.mapbox.com/styles/v1/mapbox/satellite-streets-v12/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4M3VycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw', {
                attribution: '&copy; <a href="https://www.mapbox.com/">Mapbox</a>',
                maxZoom: 22
              })
            default:
              return window.L.tileLayer('https://mt{s}.google.com/vt/lyrs=y&x={x}&y={y}&z={z}', {
                attribution: '&copy; <a href="https://www.google.com/maps">Google</a>',
                maxZoom: 21,
                subdomains: ['0', '1', '2', '3']
              })
          }
        }

        // Get new tile layer
        const getNewTileLayer = (newStyle: string) => {
          switch (newStyle) {
            case 'satellite':
              return getSatelliteTileLayer(satelliteProvider)
            case 'streets':
              return window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 18
              })
            case 'dark':
              return window.L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                maxZoom: 18
              })
            default: // light
              return window.L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                maxZoom: 18
              })
          }
        }

        // Add new tile layer
        const newLayer = getNewTileLayer(style)
        newLayer.addTo(map.current)
        map.current.currentTileLayer = newLayer

        // Add overlays for satellite view (only for providers that don't include them)
        if (style === 'satellite' && !['google_hybrid', 'mapbox'].includes(satelliteProvider)) {
          // Add roads overlay if enabled
          if (showRoadsOverlay) {
            const roadsLayer = window.L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/toner-lines/{z}/{x}/{y}{r}.png', {
              attribution: 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
              maxZoom: 18,
              opacity: 0.8
            })
            roadsLayer.addTo(map.current)
            map.current.roadsLayer = roadsLayer
          }

          // Add labels overlay if enabled
          if (showLabelsOverlay) {
            const labelsLayer = window.L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/toner-labels/{z}/{x}/{y}{r}.png', {
              attribution: 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
              maxZoom: 18,
              opacity: 0.9
            })
            labelsLayer.addTo(map.current)
            map.current.labelsLayer = labelsLayer
          }
        }

        setCurrentStyle(style)
      } catch (error) {
        console.error('Error changing map style:', error)
      }
    }
  }

  // Handle fullscreen toggle
  const toggleFullscreen = () => {
    setFullscreen(!fullscreen)
  }

  // Handle satellite provider change
  const changeSatelliteProvider = (provider: 'esri' | 'google' | 'google_hybrid' | 'bing' | 'mapbox') => {
    setSatelliteProvider(provider)
    if (currentStyle === 'satellite') {
      // Refresh the satellite layer with new provider
      changeMapStyle('satellite')
    }
  }

  // Toggle roads overlay
  const toggleRoadsOverlay = () => {
    setShowRoadsOverlay(!showRoadsOverlay)
    if (currentStyle === 'satellite') {
      // Refresh the satellite layer with new overlay settings
      changeMapStyle('satellite')
    }
  }

  // Toggle labels overlay
  const toggleLabelsOverlay = () => {
    setShowLabelsOverlay(!showLabelsOverlay)
    if (currentStyle === 'satellite') {
      // Refresh the satellite layer with new overlay settings
      changeMapStyle('satellite')
    }
  }

  // Toolbar functions
  const handleZoomIn = () => {
    if (map.current) {
      map.current.zoomIn()
    }
  }

  const handleZoomOut = () => {
    if (map.current) {
      map.current.zoomOut()
    }
  }

  const handleResetView = () => {
    if (map.current) {
      const ethiopiaCenter = [9.0222, 38.7468]
      map.current.setView(ethiopiaCenter, 6)
    }
  }

  const handleCenterOnUser = () => {
    if (map.current && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords
          map.current.setView([latitude, longitude], 15)
        },
        (error) => {
          console.error('Error getting location:', error)
        }
      )
    }
  }

  const handleRefresh = () => {
    // Refresh the map and data
    if (map.current) {
      map.current.invalidateSize()
    }
  }

  const handleExport = () => {
    // Export map functionality
    console.log('Export map functionality')
  }

  const handleShare = () => {
    // Share map functionality
    console.log('Share map functionality')
  }

  // Handle find my location
  const findMyLocation = () => {
    if (map.current && navigator.geolocation) {
      try {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords
            map.current.setView([latitude, longitude], 15)

            // Add a temporary marker for user location
            const userIcon = window.L.divIcon({
              className: 'user-location-marker',
              html: `<div style="
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background-color: #3b82f6;
                border: 3px solid white;
                box-shadow: 0 2px 6px rgba(0,0,0,0.3);
              "></div>`,
              iconSize: [16, 16],
              iconAnchor: [8, 8]
            })

            const userMarker = window.L.marker([latitude, longitude], {
              icon: userIcon
            }).addTo(map.current)

            userMarker.bindPopup('Your Location').openPopup()

            // Remove the marker after 5 seconds
            setTimeout(() => {
              map.current.removeLayer(userMarker)
            }, 5000)
          },
          (error) => {
            console.error('Error getting location:', error)
            alert('Unable to get your location. Please check your browser permissions.')
          }
        )
      } catch (error) {
        console.error('Error finding location:', error)
      }
    }
  }

  const statusCounts = locations.reduce((acc, location) => {
    acc[location.status] = (acc[location.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return (
    <div
      className={`relative ${fullscreen ? 'fixed inset-0 z-50 bg-background' : ''}`}
      style={{ height: fullscreen ? '100vh' : height }}
    >
      <div ref={mapContainer} className="w-full h-full" />

      {(!mapboxLoaded || !mapLoaded) && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80">
          <div className="flex flex-col items-center gap-2">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
            <p className="text-sm text-muted-foreground">Loading Mapbox satellite map...</p>
          </div>
        </div>
      )}

      {mapLoaded && (
        <div className="absolute top-4 left-4 flex flex-col gap-2">
          {/* Map Style Controls */}
          <div className="bg-background/90 p-2 rounded-md shadow-md flex flex-col gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => changeMapStyle('light')}
              className={currentStyle === 'light' ? 'border-primary' : ''}
              title="Light Mode"
            >
              <div className="h-5 w-5 rounded-full bg-slate-200 border border-slate-300" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => changeMapStyle('dark')}
              className={currentStyle === 'dark' ? 'border-primary' : ''}
              title="Dark Mode"
            >
              <div className="h-5 w-5 rounded-full bg-slate-800 border border-slate-700" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => changeMapStyle('satellite')}
              className={currentStyle === 'satellite' ? 'border-primary' : ''}
              title="Satellite Mode"
            >
              <Satellite className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => changeMapStyle('streets')}
              className={currentStyle === 'streets' ? 'border-primary' : ''}
              title="Streets Mode"
            >
              <div className="h-5 w-5 rounded-full bg-amber-100 border border-amber-200" />
            </Button>
          </div>

          {/* Satellite Provider Controls - Only show when satellite mode is active */}
          {currentStyle === 'satellite' && (
            <div className="bg-background/90 p-2 rounded-md shadow-md">
              <div className="text-xs font-medium mb-2 text-center">Satellite Provider</div>
              <div className="flex flex-col gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changeSatelliteProvider('google_hybrid')}
                  className={satelliteProvider === 'google_hybrid' ? 'border-primary bg-primary/10' : ''}
                  title="Google Hybrid - Satellite with Roads & Labels"
                >
                  <span className="text-xs">Google Hybrid</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changeSatelliteProvider('google')}
                  className={satelliteProvider === 'google' ? 'border-primary bg-primary/10' : ''}
                  title="Google Satellite Only"
                >
                  <span className="text-xs">Google Sat</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changeSatelliteProvider('esri')}
                  className={satelliteProvider === 'esri' ? 'border-primary bg-primary/10' : ''}
                  title="Esri World Imagery"
                >
                  <span className="text-xs">Esri</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changeSatelliteProvider('bing')}
                  className={satelliteProvider === 'bing' ? 'border-primary bg-primary/10' : ''}
                  title="Bing Satellite"
                >
                  <span className="text-xs">Bing</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changeSatelliteProvider('mapbox')}
                  className={satelliteProvider === 'mapbox' ? 'border-primary bg-primary/10' : ''}
                  title="Mapbox Satellite with Streets"
                >
                  <span className="text-xs">Mapbox</span>
                </Button>
              </div>
            </div>
          )}

          {/* Overlay Controls - Only show for providers that support overlays */}
          {currentStyle === 'satellite' && !['google_hybrid', 'mapbox'].includes(satelliteProvider) && (
            <div className="bg-background/90 p-2 rounded-md shadow-md">
              <div className="text-xs font-medium mb-2 text-center">Overlays</div>
              <div className="flex flex-col gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleRoadsOverlay}
                  className={showRoadsOverlay ? 'border-primary bg-primary/10' : ''}
                  title="Toggle Roads Overlay"
                >
                  <span className="text-xs">Roads</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleLabelsOverlay}
                  className={showLabelsOverlay ? 'border-primary bg-primary/10' : ''}
                  title="Toggle Labels Overlay"
                >
                  <span className="text-xs">Labels</span>
                </Button>
              </div>
            </div>
          )}

          {/* Navigation Controls */}
          <div className="bg-background/90 p-2 rounded-md shadow-md flex flex-col gap-2">
            <Button variant="outline" size="icon" onClick={findMyLocation} title="Find My Location">
              <Locate className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={toggleFullscreen} title="Toggle Fullscreen">
              {fullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      )}

      {mapLoaded && (
        <div className="absolute bottom-4 left-4 bg-background/90 p-2 rounded-md shadow-md">
          <div className="text-xs font-medium mb-2">
            Transformers: {locations.length} | Style: {currentStyle.charAt(0).toUpperCase() + currentStyle.slice(1)}
            {currentStyle === 'satellite' && (
              <span> | Provider: {satelliteProvider.charAt(0).toUpperCase() + satelliteProvider.slice(1)}</span>
            )}
          </div>
          {currentStyle === 'satellite' && (
            <div className="text-xs mb-2 flex gap-2">
              {['google_hybrid', 'mapbox'].includes(satelliteProvider) ? (
                <span className="text-primary">Roads & Labels: BUILT-IN</span>
              ) : (
                <>
                  <span className={showRoadsOverlay ? 'text-primary' : 'text-muted-foreground'}>
                    Roads: {showRoadsOverlay ? 'ON' : 'OFF'}
                  </span>
                  <span className={showLabelsOverlay ? 'text-primary' : 'text-muted-foreground'}>
                    Labels: {showLabelsOverlay ? 'ON' : 'OFF'}
                  </span>
                </>
              )}
            </div>
          )}
          <div className="grid grid-cols-2 gap-x-4 gap-y-1">
            {Object.entries(statusCounts).map(([status, count]) => (
              <div key={status} className="flex items-center gap-1">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: getStatusColor(status) }}
                />
                <span className="text-xs">{status}: {count}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Filter Panel */}
      <TransformerMapFilters
        filters={filters}
        onFiltersChange={setFilters}
        isVisible={showFilters}
        onToggleVisibility={() => setShowFilters(!showFilters)}
        totalCount={locations.length}
        filteredCount={filteredLocations.length}
      />

      {/* Statistics Panel */}
      <TransformerMapStats
        totalCount={locations.length}
        filteredCount={filteredLocations.length}
        statusCounts={calculateStats(filteredLocations).statusCounts}
        regionCounts={calculateStats(filteredLocations).regionCounts}
        averageHealth={calculateStats(filteredLocations).averageHealth}
        maintenanceDue={calculateStats(filteredLocations).maintenanceDue}
        criticalAlerts={calculateStats(filteredLocations).criticalAlerts}
        isVisible={showStats}
        onToggleVisibility={() => setShowStats(!showStats)}
      />

      {/* Map Toolbar */}
      <TransformerMapToolbar
        mapStyle={currentStyle}
        onMapStyleChange={changeMapStyle}
        isFullscreen={fullscreen}
        onToggleFullscreen={toggleFullscreen}
        onRefresh={handleRefresh}
        onExport={handleExport}
        onShare={handleShare}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetView={handleResetView}
        onCenterOnUser={handleCenterOnUser}
        showGrid={showGrid}
        onToggleGrid={() => setShowGrid(!showGrid)}
        showMeasurement={showMeasurement}
        onToggleMeasurement={() => setShowMeasurement(!showMeasurement)}
        clusteredView={clusteredView}
        onToggleClustered={() => setClustered(!clusteredView)}
        selectedCount={selectedMarkers.length}
      />

      {/* Transformer Detail Panel Above Map */}
      {showDetailOverlay && selectedLocation && (
        <div className="absolute top-4 left-4 right-4 bg-white rounded-lg shadow-2xl z-[9999] max-h-[45vh] overflow-y-auto border-2 border-gray-200">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b bg-gray-50 rounded-t-lg">
              <div className="flex items-center gap-3">
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: getStatusColor(selectedLocation.status) }}
                />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{selectedLocation.title}</h3>
                  <p className="text-sm text-gray-600">{selectedLocation.description}</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowDetailOverlay(false)}
                className="h-8 w-8 hover:bg-gray-200"
              >
                ✕
              </Button>
            </div>

            {/* Content */}
            <div className="p-4 space-y-3 bg-white">
              {/* Status Badge */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">Status:</span>
                <Badge
                  className="text-white text-xs"
                  style={{ backgroundColor: getStatusColor(selectedLocation.status) }}
                >
                  {selectedLocation.status.toUpperCase()}
                </Badge>
              </div>

              {/* Transformer Details */}
              {selectedLocation.data && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {/* Technical Specifications */}
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <MapPin className="h-3 w-3" />
                        Technical Specs
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-1">
                      <div className="grid grid-cols-1 gap-1 text-xs">
                        <div>
                          <span className="text-muted-foreground">ID:</span>
                          <p className="font-medium">{selectedLocation.data.id}</p>
                        </div>
                        {selectedLocation.data.capacity && (
                          <div>
                            <span className="text-muted-foreground">Capacity:</span>
                            <p className="font-medium">{selectedLocation.data.capacity} kVA</p>
                          </div>
                        )}
                        {selectedLocation.data.manufacturer && (
                          <div>
                            <span className="text-muted-foreground">Manufacturer:</span>
                            <p className="font-medium">{selectedLocation.data.manufacturer}</p>
                          </div>
                        )}
                        {selectedLocation.data.model && (
                          <div>
                            <span className="text-muted-foreground">Model:</span>
                            <p className="font-medium">{selectedLocation.data.model}</p>
                          </div>
                        )}
                        {selectedLocation.data.voltagePrimary && (
                          <div>
                            <span className="text-muted-foreground">Primary V:</span>
                            <p className="font-medium">{selectedLocation.data.voltagePrimary}V</p>
                          </div>
                        )}
                        {selectedLocation.data.voltageSecondary && (
                          <div>
                            <span className="text-muted-foreground">Secondary V:</span>
                            <p className="font-medium">{selectedLocation.data.voltageSecondary}V</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Location Information */}
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <MapPin className="h-3 w-3" />
                        Location Info
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-1">
                      <div className="text-xs space-y-1">
                        {selectedLocation.data.location && (
                          <>
                            <div>
                              <span className="text-muted-foreground">Location:</span>
                              <p className="font-medium">{selectedLocation.data.location.name}</p>
                            </div>
                            {selectedLocation.data.location.region && (
                              <div>
                                <span className="text-muted-foreground">Region:</span>
                                <p className="font-medium">{selectedLocation.data.location.region}</p>
                              </div>
                            )}
                            {selectedLocation.data.location.district && (
                              <div>
                                <span className="text-muted-foreground">District:</span>
                                <p className="font-medium">{selectedLocation.data.location.district}</p>
                              </div>
                            )}
                          </>
                        )}
                        <div>
                          <span className="text-muted-foreground">Coordinates:</span>
                          <p className="font-medium text-xs">
                            {selectedLocation.latitude.toFixed(6)}, {selectedLocation.longitude.toFixed(6)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Performance Metrics */}
                  {selectedLocation.data && (selectedLocation.data.temperature || selectedLocation.data.loadFactor || selectedLocation.data.healthIndex) && (
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Eye className="h-3 w-3" />
                          Performance
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-1">
                          {selectedLocation.data.temperature && (
                            <div className="flex justify-between items-center p-1 bg-blue-50 rounded text-xs">
                              <span className="text-muted-foreground">Temp:</span>
                              <span className="font-bold text-blue-600">{selectedLocation.data.temperature}°C</span>
                            </div>
                          )}
                          {selectedLocation.data.loadFactor && (
                            <div className="flex justify-between items-center p-1 bg-green-50 rounded text-xs">
                              <span className="text-muted-foreground">Load:</span>
                              <span className="font-bold text-green-600">{selectedLocation.data.loadFactor}%</span>
                            </div>
                          )}
                          {selectedLocation.data.healthIndex && (
                            <div className="flex justify-between items-center p-1 bg-purple-50 rounded text-xs">
                              <span className="text-muted-foreground">Health:</span>
                              <span className="font-bold text-purple-600">{selectedLocation.data.healthIndex}%</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2 pt-1">
                <Button
                  variant="default"
                  size="xs"
                  className="text-xs h-7"
                  onClick={() => {
                    setShowDetailOverlay(false)
                    if (onMarkerClick) {
                      onMarkerClick(selectedLocation)
                    }
                  }}
                >
                  <Eye className="h-3 w-3 mr-1" />
                  View Details
                </Button>
                <Button
                  variant="outline"
                  size="xs"
                  className="text-xs h-7"
                  onClick={() => {
                    // Center map on transformer location
                    if (map.current) {
                      map.current.setView([selectedLocation.latitude, selectedLocation.longitude], 15)
                    }
                  }}
                >
                  <MapPin className="h-3 w-3 mr-1" />
                  Center
                </Button>
                <Button
                  variant="outline"
                  size="xs"
                  className="text-xs h-7"
                  onClick={() => setShowDetailOverlay(false)}
                >
                  Close
                </Button>
              </div>
            </div>
        </div>
      )}
    </div>
  )
}

export function MapboxMap({
  locations = [],
  center,
  zoom = 6,
  interactive = true,
  showControls = true,
  height = "100%",
  onMarkerClick,
  onMapClick,
  onMapLoad,
  mapStyle = 'satellite',
  clustered = true
}: MapboxMapProps) {
  // Use the real Mapbox satellite map component
  return (
    <MapboxSatelliteMap
      locations={locations}
      height={height}
      onMarkerClick={onMarkerClick}
      mapStyle={mapStyle}
    />
  )
}
