"use client"

import { useState, useEffect } from "react"
import { Bell, CheckCircle2, AlertTriangle, Info, X, Clock, Settings, RefreshCw } from "lucide-react"
import { Button } from "@/src/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuGroup,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuPortal
} from "@/src/components/ui/dropdown-menu"
import { Badge } from "@/src/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { ScrollArea } from "@/src/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar"
import { cn } from "@/src/lib/utils"
import { useToast } from "@/src/components/ui/use-toast"
import { notificationService, type Notification } from "@/src/services/notification-service"

export function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [activeTab, setActiveTab] = useState("all")
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  // Fetch notifications on mount
  useEffect(() => {
    fetchNotifications()
  }, [])

  // Fetch notifications from service
  const fetchNotifications = async () => {
    setIsLoading(true)
    try {
      const data = await notificationService.getAllNotifications()
      setNotifications(data)

      // Get unread count
      const counts = await notificationService.getNotificationCount()
      setUnreadCount(counts.unread)
    } catch (error) {
      console.error("Error fetching notifications:", error)
      toast({
        title: "Error",
        description: "Failed to load notifications",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Mark notification as read
  const markAsRead = async (id: string) => {
    try {
      await notificationService.markAsRead(id)

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id
            ? { ...notification, read: true }
            : notification
        )
      )

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (error) {
      console.error("Error marking notification as read:", error)
      toast({
        title: "Error",
        description: "Failed to mark notification as read",
        variant: "destructive"
      })
    }
  }

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead()

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      )

      // Update unread count
      setUnreadCount(0)

      toast({
        title: "All notifications marked as read",
        description: "You have no unread notifications",
      })
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
      toast({
        title: "Error",
        description: "Failed to mark all notifications as read",
        variant: "destructive"
      })
    }
  }

  // Clear all notifications
  const clearAllNotifications = async () => {
    try {
      await notificationService.clearAllNotifications()

      // Update local state
      setNotifications([])
      setUnreadCount(0)

      toast({
        title: "All notifications cleared",
        description: "Your notification center is now empty",
      })
    } catch (error) {
      console.error("Error clearing notifications:", error)
      toast({
        title: "Error",
        description: "Failed to clear notifications",
        variant: "destructive"
      })
    }
  }

  // Filter notifications based on active tab
  const getFilteredNotifications = () => {
    if (activeTab === "all") return notifications
    if (activeTab === "unread") return notifications.filter(n => !n.read)
    return notifications.filter(n => n.source === activeTab)
  }

  // Get icon based on notification type
  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "success": return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case "warning": return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case "error": return <AlertTriangle className="h-4 w-4 text-red-500" />
      default: return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  // Format timestamp to relative time
  const formatRelativeTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`

    const diffHours = Math.floor(diffMins / 60)
    if (diffHours < 24) return `${diffHours}h ago`

    const diffDays = Math.floor(diffHours / 24)
    if (diffDays < 7) return `${diffDays}d ago`

    return date.toLocaleDateString()
  }

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id)
    // In a real app, you would navigate to the actionUrl
    toast({
      title: "Notification clicked",
      description: `Navigating to: ${notification.actionUrl || 'dashboard'}`,
    })
  }

  // Refresh notifications
  const refreshNotifications = async () => {
    setIsLoading(true)
    toast({
      title: "Refreshing notifications",
      description: "Checking for new notifications...",
    })

    try {
      // Fetch fresh notifications
      await fetchNotifications()

      // Simulate adding a new notification
      const newNotification = await notificationService.addNotification({
        title: "New Alert Detected",
        description: "A new alert has been detected in the system",
        type: "warning",
        read: false,
        source: "system"
      })

      // Update local state with the new notification
      setNotifications(prev => [newNotification, ...prev])
      setUnreadCount(prev => prev + 1)

      toast({
        title: "New notification",
        description: "You have a new system alert",
      })
    } catch (error) {
      console.error("Error refreshing notifications:", error)
      toast({
        title: "Error",
        description: "Failed to refresh notifications",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-red-500"
              variant="destructive"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
          <span className="sr-only">Notifications</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[380px]">
        <div className="flex items-center justify-between p-4">
          <DropdownMenuLabel className="text-base font-semibold">Notifications</DropdownMenuLabel>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={refreshNotifications}
              disabled={isLoading}
            >
              <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
              <span className="sr-only">Refresh</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => {
                toast({
                  title: "Notification Settings",
                  description: "Opening notification settings",
                })
              }}
            >
              <Settings className="h-4 w-4" />
              <span className="sr-only">Settings</span>
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <div className="px-4 pb-2">
            <TabsList className="w-full">
              <TabsTrigger value="all" className="flex-1">All</TabsTrigger>
              <TabsTrigger value="unread" className="flex-1">Unread</TabsTrigger>
              <TabsTrigger value="transformer" className="flex-1">Alerts</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value={activeTab} className="mt-0">
            {isLoading ? (
              <div className="p-4">
                <div className="flex flex-col items-center justify-center p-4 text-center">
                  <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">Loading notifications...</p>
                </div>
                <div className="space-y-4 mt-2">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="flex items-start gap-3 p-3 rounded-md bg-muted/30 animate-pulse">
                      <div className="h-5 w-5 rounded-full bg-muted"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 w-3/4 bg-muted rounded"></div>
                        <div className="h-3 w-full bg-muted rounded"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : getFilteredNotifications().length > 0 ? (
              <ScrollArea className="h-[300px]">
                <div className="flex flex-col gap-1 p-1">
                  {getFilteredNotifications().map((notification) => (
                    <button
                      key={notification.id}
                      className={cn(
                        "flex items-start gap-3 rounded-md p-3 text-left transition-colors hover:bg-muted",
                        !notification.read && "bg-muted/50"
                      )}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="mt-1">{getNotificationIcon(notification.type)}</div>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <p className={cn("text-sm font-medium", !notification.read && "font-semibold")}>
                            {notification.title}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatRelativeTime(notification.timestamp)}
                          </p>
                        </div>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {notification.description}
                        </p>
                      </div>
                    </button>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="flex flex-col items-center justify-center p-8 text-center">
                <div className="rounded-full bg-muted p-3 mb-3">
                  <Bell className="h-6 w-6 text-muted-foreground" />
                </div>
                <h3 className="text-sm font-medium">No notifications</h3>
                <p className="text-xs text-muted-foreground mt-1">
                  {activeTab === "all"
                    ? "You don't have any notifications yet"
                    : activeTab === "unread"
                      ? "You've read all your notifications"
                      : `No ${activeTab} notifications`}
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DropdownMenuSeparator />

        <div className="flex items-center justify-between p-2">
          <DropdownMenuItem
            className="cursor-pointer text-xs"
            onClick={markAllAsRead}
            disabled={isLoading || !notifications.some(n => !n.read)}
          >
            {isLoading ? (
              <>
                <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                Processing...
              </>
            ) : (
              "Mark all as read"
            )}
          </DropdownMenuItem>
          <DropdownMenuItem
            className="cursor-pointer text-xs"
            onClick={clearAllNotifications}
            disabled={isLoading || notifications.length === 0}
          >
            {isLoading ? (
              <>
                <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                Processing...
              </>
            ) : (
              "Clear all"
            )}
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
