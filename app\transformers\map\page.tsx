"use client"

import { MainLayout } from "@/src/components/layout/main-layout"
import { ProtectedRoute } from "@/src/components/layout/protected-route"
import ProvidersWrapper from "@/src/shared/contexts/providers-wrapper"
import TransformerMapWithDetails from "@/components/transformers/TransformerMapWithDetails"

function TransformerMapContentInner() {
  return (
    <TransformerMapWithDetails
      height="calc(100vh - 13rem)"
      showControls={true}
      allowFullscreen={true}
    />
  )
}

export default function TransformerMapPage() {
  return (
    <ProvidersWrapper>
      <ProtectedRoute
        allowedRoles={[
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician"
        ]}
        requiredPermissions={[{ resource: "transformers", action: "read" }]}
      >
        <MainLayout>
        <div className="p-6">
          <TransformerMapContentInner />
        </div>
        </MainLayout>
      </ProtectedRoute>
    </ProvidersWrapper>
  )
}
