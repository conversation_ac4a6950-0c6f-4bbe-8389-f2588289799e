/**
 * Application Configuration
 * Central configuration for the EEU Transformer Management System
 */

export const APP_CONFIG = {
  // Application Info
  name: 'EEU Transformer Management System',
  shortName: 'EEU-DTMS',
  version: '2.0.0',
  description: 'Ethiopian Electric Utility - Digital Transformer Management System',
  
  // Company Info
  company: {
    name: 'Ethiopian Electric Utility',
    shortName: 'EEU',
    website: 'https://eeu.gov.et',
    email: '<EMAIL>',
    phone: '+251-11-661-6000'
  },
  
  // Developer Info
  developer: {
    name: '<PERSON>u Mesafint',
    email: '<EMAIL>',
    github: 'https://github.com/workubest'
  },
  
  // Application URLs
  urls: {
    base: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002',
    api: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002/api',
    docs: '/documentation',
    support: '/help'
  },
  
  // Feature Flags
  features: {
    authentication: true,
    roleBasedAccess: true,
    realTimeUpdates: true,
    notifications: true,
    darkMode: true,
    multiLanguage: false,
    offlineMode: false,
    analytics: true,
    exportData: true,
    importData: true,
    bulkOperations: true,
    advancedFilters: true,
    customReports: true,
    mobileApp: true,
    weatherIntegration: true,
    predictiveMaintenance: true,
    aiInsights: false
  },
  
  // UI Configuration
  ui: {
    theme: {
      default: 'light',
      options: ['light', 'dark', 'system']
    },
    sidebar: {
      defaultCollapsed: false,
      collapsible: true
    },
    pagination: {
      defaultPageSize: 20,
      pageSizeOptions: [10, 20, 50, 100]
    },
    dateFormat: 'MMM dd, yyyy',
    timeFormat: 'HH:mm',
    currency: 'ETB',
    language: 'en'
  },
  
  // Data Refresh Intervals (in milliseconds)
  refreshIntervals: {
    dashboard: 30000,      // 30 seconds
    realTimeData: 5000,    // 5 seconds
    notifications: 60000,  // 1 minute
    statistics: 300000,    // 5 minutes
    weatherData: 600000    // 10 minutes
  },
  
  // Limits and Constraints
  limits: {
    maxFileUploadSize: 10 * 1024 * 1024, // 10MB
    maxBulkOperations: 1000,
    maxSearchResults: 500,
    sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours
    maxRetries: 3,
    requestTimeout: 30000 // 30 seconds
  },
  
  // Map Configuration
  map: {
    defaultCenter: {
      lat: 9.0320,  // Addis Ababa
      lng: 38.7469
    },
    defaultZoom: 7,
    maxZoom: 18,
    minZoom: 5,
    provider: 'leaflet', // 'leaflet' | 'mapbox'
    style: 'streets'
  },
  
  // Notification Settings
  notifications: {
    position: 'top-right',
    duration: 5000,
    maxVisible: 5,
    types: {
      success: { icon: 'check-circle', color: 'green' },
      error: { icon: 'alert-circle', color: 'red' },
      warning: { icon: 'alert-triangle', color: 'yellow' },
      info: { icon: 'info', color: 'blue' }
    }
  },
  
  // Chart Configuration
  charts: {
    defaultColors: [
      '#10B981', // green
      '#3B82F6', // blue
      '#F59E0B', // yellow
      '#EF4444', // red
      '#8B5CF6', // purple
      '#06B6D4', // cyan
      '#F97316', // orange
      '#84CC16'  // lime
    ],
    animations: true,
    responsive: true
  },
  
  // Export Configuration
  export: {
    formats: ['pdf', 'excel', 'csv', 'json'],
    maxRecords: 10000,
    defaultFormat: 'pdf'
  },
  
  // Security Configuration
  security: {
    passwordMinLength: 8,
    passwordRequireSpecialChar: true,
    passwordRequireNumber: true,
    passwordRequireUppercase: true,
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    sessionCookieName: 'eeu_session',
    csrfTokenName: 'eeu_csrf_token'
  },
  
  // Cache Configuration
  cache: {
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    maxSize: 100, // Maximum number of cached items
    strategies: {
      transformers: 10 * 60 * 1000,    // 10 minutes
      users: 15 * 60 * 1000,           // 15 minutes
      statistics: 5 * 60 * 1000,       // 5 minutes
      reports: 30 * 60 * 1000          // 30 minutes
    }
  },
  
  // Error Handling
  errors: {
    showStackTrace: process.env.NODE_ENV === 'development',
    logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'error',
    retryAttempts: 3,
    retryDelay: 1000 // 1 second
  },
  
  // Performance Monitoring
  performance: {
    enableMetrics: true,
    sampleRate: 0.1, // 10% sampling
    trackUserInteractions: true,
    trackNetworkRequests: true
  }
} as const

// Type definitions for configuration
export type AppConfig = typeof APP_CONFIG
export type FeatureFlags = typeof APP_CONFIG.features
export type UIConfig = typeof APP_CONFIG.ui
export type SecurityConfig = typeof APP_CONFIG.security

// Environment-specific overrides
export const getConfig = (): AppConfig => {
  const env = process.env.NODE_ENV
  
  if (env === 'production') {
    return {
      ...APP_CONFIG,
      errors: {
        ...APP_CONFIG.errors,
        showStackTrace: false,
        logLevel: 'error'
      },
      performance: {
        ...APP_CONFIG.performance,
        sampleRate: 0.01 // 1% sampling in production
      }
    }
  }
  
  if (env === 'test') {
    return {
      ...APP_CONFIG,
      refreshIntervals: {
        dashboard: 1000,
        realTimeData: 500,
        notifications: 1000,
        statistics: 1000,
        weatherData: 1000
      },
      limits: {
        ...APP_CONFIG.limits,
        sessionTimeout: 60000 // 1 minute for tests
      }
    }
  }
  
  return APP_CONFIG
}

export default getConfig()
