'use client'

import { useState, useEffect } from 'react'

export default function DebugTransformers() {
  const [data, setData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('🔄 Debug: Fetching transformers...')
        const response = await fetch('/api/mysql/transformers')
        
        console.log('📊 Debug: Response status:', response.status)
        console.log('📊 Debug: Response ok:', response.ok)
        
        if (response.ok) {
          const result = await response.json()
          console.log('📊 Debug: Full response:', result)
          setData(result)
        } else {
          setError(`API request failed: ${response.status}`)
        }
      } catch (err) {
        console.error('❌ Debug: Error:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4">🔄 Loading Debug Info...</h2>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4 text-red-800">❌ Debug Error</h2>
        <p className="text-red-600">{error}</p>
      </div>
    )
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h2 className="text-xl font-bold mb-4">🔍 Debug: Transformer API Response</h2>
      
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold">Response Structure:</h3>
          <ul className="list-disc list-inside text-sm">
            <li>Success: {data?.success ? '✅' : '❌'}</li>
            <li>Count: {data?.count || 'N/A'}</li>
            <li>Source: {data?.source || 'N/A'}</li>
            <li>Has Data: {data?.data ? '✅' : '❌'}</li>
            <li>Has Transformers: {data?.data?.transformers ? '✅' : '❌'}</li>
            <li>Transformers Count: {data?.data?.transformers?.length || 0}</li>
          </ul>
        </div>

        {data?.data?.transformers && data.data.transformers.length > 0 && (
          <div>
            <h3 className="font-semibold">First Transformer Sample:</h3>
            <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-64">
              {JSON.stringify(data.data.transformers[0], null, 2)}
            </pre>
          </div>
        )}

        <div>
          <h3 className="font-semibold">Full Response (truncated):</h3>
          <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-32">
            {JSON.stringify(data, null, 2).substring(0, 500)}...
          </pre>
        </div>
      </div>
    </div>
  )
}
