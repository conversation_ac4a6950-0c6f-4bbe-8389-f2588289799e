/**
 * Enhanced MySQL Setup for dtms_eeu_db
 *
 * This script creates comprehensive tables and inserts extensive sample data
 * for all dashboard components and modern features.
 */

const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db'
};

let connection;

async function initConnection() {
  connection = await mysql.createConnection(config);
  console.log('✅ MySQL connection established');
}

async function executeQuery(query, params = []) {
  try {
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('❌ Query error:', error.message);
    console.error('Query:', query.substring(0, 100) + '...');
    throw error;
  }
}

async function createEnhancedTables() {
  console.log('🔄 Creating enhanced database schema...');

  try {
    // Disable foreign key checks and strict mode
    await executeQuery('SET FOREIGN_KEY_CHECKS = 0');
    await executeQuery('SET sql_mode = ""');

    // Drop existing tables if they exist
    const tables = [
      'app_notifications', 'app_work_orders', 'app_inventory_items', 'app_energy_consumption',
      'app_weather_data', 'app_outages', 'app_maintenance_schedules', 'app_sensor_readings',
      'app_performance_metrics', 'app_audit_logs', 'app_user_sessions', 'app_system_settings',
      'app_reports', 'app_alerts', 'app_transformers', 'app_service_centers', 'app_regions', 'app_users'
    ];

    for (const table of tables) {
      await executeQuery(`DROP TABLE IF EXISTS ${table}`);
    }

    // Create enhanced users table
    await executeQuery(`
      CREATE TABLE app_users (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        role VARCHAR(50) NOT NULL,
        department VARCHAR(100),
        phone VARCHAR(20),
        avatar_url VARCHAR(500),
        is_active TINYINT(1) DEFAULT 1,
        is_online TINYINT(1) DEFAULT 0,
        last_login DATETIME NULL,
        last_activity DATETIME NULL,
        preferences JSON,
        permissions JSON,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Enhanced users table created');

    // Create regions table
    await executeQuery(`
      CREATE TABLE app_regions (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(10) UNIQUE NOT NULL,
        description TEXT,
        lat DECIMAL(10, 8) NOT NULL,
        lng DECIMAL(11, 8) NOT NULL,
        population INT DEFAULT 0,
        area_km2 DECIMAL(10, 2) DEFAULT 0,
        timezone VARCHAR(50) DEFAULT 'Africa/Addis_Ababa',
        weather_station_id VARCHAR(50),
        is_active TINYINT(1) DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Enhanced regions table created');

    // Create service centers table
    await executeQuery(`
      CREATE TABLE app_service_centers (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(20) UNIQUE NOT NULL,
        region_id VARCHAR(36) NOT NULL,
        address TEXT,
        lat DECIMAL(10, 8) NOT NULL,
        lng DECIMAL(11, 8) NOT NULL,
        contact_phone VARCHAR(20),
        contact_email VARCHAR(255),
        manager_name VARCHAR(100),
        manager_id VARCHAR(36),
        capacity INT DEFAULT 0,
        operating_hours JSON,
        facilities JSON,
        is_active TINYINT(1) DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Enhanced service centers table created');

    // Create transformers table
    await executeQuery(`
      CREATE TABLE app_transformers (
        id VARCHAR(36) PRIMARY KEY,
        serial_number VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(100) NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'operational',
        type VARCHAR(50) NOT NULL,
        manufacturer VARCHAR(100) NOT NULL,
        model VARCHAR(100) NOT NULL,
        capacity INT NOT NULL,
        voltage_primary DECIMAL(8, 2) NOT NULL,
        voltage_secondary DECIMAL(8, 2) NOT NULL,
        region_id VARCHAR(36) NOT NULL,
        service_center_id VARCHAR(36) NOT NULL,
        location_address TEXT,
        lat DECIMAL(10, 8) NOT NULL,
        lng DECIMAL(11, 8) NOT NULL,
        temperature DECIMAL(5, 2) DEFAULT 0,
        load_percentage DECIMAL(5, 2) DEFAULT 0,
        oil_level DECIMAL(5, 2) DEFAULT 0,
        health_index DECIMAL(5, 2) DEFAULT 0,
        efficiency DECIMAL(5, 2) DEFAULT 95.0,
        power_factor DECIMAL(3, 2) DEFAULT 0.95,
        installation_date DATE,
        last_maintenance_date DATE,
        next_maintenance_date DATE,
        warranty_expiry DATE,
        cost DECIMAL(12, 2),
        depreciation_rate DECIMAL(5, 2) DEFAULT 5.0,
        smart_meter_enabled TINYINT(1) DEFAULT 0,
        iot_device_id VARCHAR(100),
        is_critical TINYINT(1) DEFAULT 0,
        tags JSON,
        specifications JSON,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Enhanced transformers table created');

    // Create alerts table
    await executeQuery(`
      CREATE TABLE app_alerts (
        id VARCHAR(36) PRIMARY KEY,
        transformer_id VARCHAR(36),
        region_id VARCHAR(36),
        service_center_id VARCHAR(36),
        type VARCHAR(50) NOT NULL,
        category VARCHAR(50) NOT NULL DEFAULT 'operational',
        severity VARCHAR(20) NOT NULL,
        priority VARCHAR(20) NOT NULL DEFAULT 'medium',
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        details JSON,
        source VARCHAR(50) DEFAULT 'system',
        is_resolved TINYINT(1) DEFAULT 0,
        is_acknowledged TINYINT(1) DEFAULT 0,
        acknowledged_by VARCHAR(36),
        acknowledged_at DATETIME NULL,
        resolved_by VARCHAR(36),
        resolved_at DATETIME NULL,
        resolution_notes TEXT,
        escalation_level INT DEFAULT 0,
        auto_generated TINYINT(1) DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Enhanced alerts table created');

    // Create maintenance schedules table
    await executeQuery(`
      CREATE TABLE app_maintenance_schedules (
        id VARCHAR(36) PRIMARY KEY,
        transformer_id VARCHAR(36) NOT NULL,
        type VARCHAR(50) NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
        priority VARCHAR(20) NOT NULL DEFAULT 'medium',
        title VARCHAR(200) NOT NULL,
        description TEXT,
        scheduled_date DATE NOT NULL,
        scheduled_time TIME,
        estimated_duration INT DEFAULT 240,
        actual_start_time DATETIME,
        actual_end_time DATETIME,
        technician_id VARCHAR(36),
        supervisor_id VARCHAR(36),
        team_members JSON,
        required_parts JSON,
        required_tools JSON,
        safety_requirements JSON,
        work_instructions TEXT,
        completion_notes TEXT,
        quality_check_passed TINYINT(1),
        cost DECIMAL(10, 2) DEFAULT 0,
        downtime_minutes INT DEFAULT 0,
        created_by VARCHAR(36),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Maintenance schedules table created');

    // Create outages table
    await executeQuery(`
      CREATE TABLE app_outages (
        id VARCHAR(36) PRIMARY KEY,
        transformer_id VARCHAR(36),
        region_id VARCHAR(36),
        title VARCHAR(200) NOT NULL,
        description TEXT,
        status VARCHAR(20) NOT NULL DEFAULT 'active',
        severity VARCHAR(20) NOT NULL,
        cause VARCHAR(200),
        cause_category VARCHAR(50),
        start_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        end_time DATETIME NULL,
        estimated_restoration DATETIME,
        affected_customers INT DEFAULT 0,
        affected_areas JSON,
        power_lost_mw DECIMAL(8, 2) DEFAULT 0,
        revenue_impact DECIMAL(12, 2) DEFAULT 0,
        restoration_crew JSON,
        resolution TEXT,
        lessons_learned TEXT,
        reported_by VARCHAR(36),
        assigned_to VARCHAR(36),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Outages table created');

    // Create sensor readings table
    await executeQuery(`
      CREATE TABLE app_sensor_readings (
        id VARCHAR(36) PRIMARY KEY,
        transformer_id VARCHAR(36) NOT NULL,
        sensor_type VARCHAR(50) NOT NULL,
        reading_value DECIMAL(10, 4) NOT NULL,
        unit VARCHAR(20) NOT NULL,
        timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        quality_score DECIMAL(3, 2) DEFAULT 1.0,
        is_anomaly TINYINT(1) DEFAULT 0,
        anomaly_score DECIMAL(5, 4),
        metadata JSON,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Sensor readings table created');

    // Create performance metrics table
    await executeQuery(`
      CREATE TABLE app_performance_metrics (
        id VARCHAR(36) PRIMARY KEY,
        transformer_id VARCHAR(36),
        region_id VARCHAR(36),
        metric_type VARCHAR(50) NOT NULL,
        metric_name VARCHAR(100) NOT NULL,
        value DECIMAL(15, 4) NOT NULL,
        unit VARCHAR(20),
        target_value DECIMAL(15, 4),
        threshold_min DECIMAL(15, 4),
        threshold_max DECIMAL(15, 4),
        period_start DATETIME NOT NULL,
        period_end DATETIME NOT NULL,
        calculation_method VARCHAR(50) DEFAULT 'average',
        data_points INT DEFAULT 1,
        confidence_level DECIMAL(5, 2) DEFAULT 95.0,
        metadata JSON,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Performance metrics table created');

    // Re-enable foreign key checks
    await executeQuery('SET FOREIGN_KEY_CHECKS = 1');

    // Create additional modern tables

    // Energy consumption table
    await executeQuery(`
      CREATE TABLE app_energy_consumption (
        id VARCHAR(36) PRIMARY KEY,
        transformer_id VARCHAR(36) NOT NULL,
        region_id VARCHAR(36),
        consumption_kwh DECIMAL(12, 4) NOT NULL,
        peak_demand_kw DECIMAL(10, 4) NOT NULL,
        load_factor DECIMAL(5, 4) DEFAULT 0.7,
        power_factor DECIMAL(3, 2) DEFAULT 0.95,
        energy_cost DECIMAL(10, 2) DEFAULT 0,
        carbon_footprint_kg DECIMAL(10, 2) DEFAULT 0,
        efficiency_percentage DECIMAL(5, 2) DEFAULT 95.0,
        period_start DATETIME NOT NULL,
        period_end DATETIME NOT NULL,
        billing_period VARCHAR(20) DEFAULT 'monthly',
        meter_reading_start DECIMAL(12, 4),
        meter_reading_end DECIMAL(12, 4),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Energy consumption table created');

    // Weather data table
    await executeQuery(`
      CREATE TABLE app_weather_data (
        id VARCHAR(36) PRIMARY KEY,
        region_id VARCHAR(36) NOT NULL,
        station_id VARCHAR(50),
        temperature_celsius DECIMAL(5, 2),
        humidity_percentage DECIMAL(5, 2),
        wind_speed_kmh DECIMAL(5, 2),
        wind_direction_degrees INT,
        pressure_hpa DECIMAL(7, 2),
        precipitation_mm DECIMAL(6, 2),
        visibility_km DECIMAL(5, 2),
        uv_index DECIMAL(3, 1),
        weather_condition VARCHAR(50),
        weather_description TEXT,
        forecast_data JSON,
        alerts JSON,
        timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Weather data table created');

    // Inventory items table
    await executeQuery(`
      CREATE TABLE app_inventory_items (
        id VARCHAR(36) PRIMARY KEY,
        item_code VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(200) NOT NULL,
        category VARCHAR(50) NOT NULL,
        subcategory VARCHAR(50),
        description TEXT,
        manufacturer VARCHAR(100),
        model VARCHAR(100),
        specifications JSON,
        unit_of_measure VARCHAR(20) NOT NULL,
        current_stock INT NOT NULL DEFAULT 0,
        minimum_stock INT DEFAULT 10,
        maximum_stock INT DEFAULT 1000,
        reorder_point INT DEFAULT 20,
        unit_cost DECIMAL(10, 2) DEFAULT 0,
        total_value DECIMAL(12, 2) DEFAULT 0,
        location VARCHAR(100),
        warehouse_id VARCHAR(36),
        supplier_info JSON,
        last_restocked DATETIME,
        expiry_date DATE,
        is_critical TINYINT(1) DEFAULT 0,
        is_active TINYINT(1) DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Inventory items table created');

    // Work orders table
    await executeQuery(`
      CREATE TABLE app_work_orders (
        id VARCHAR(36) PRIMARY KEY,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        type VARCHAR(50) NOT NULL,
        priority VARCHAR(20) NOT NULL DEFAULT 'medium',
        status VARCHAR(20) NOT NULL DEFAULT 'open',
        transformer_id VARCHAR(36),
        region_id VARCHAR(36),
        service_center_id VARCHAR(36),
        assigned_to VARCHAR(36),
        assigned_team JSON,
        requested_by VARCHAR(36),
        approved_by VARCHAR(36),
        scheduled_start DATETIME,
        scheduled_end DATETIME,
        actual_start DATETIME,
        actual_end DATETIME,
        estimated_hours DECIMAL(6, 2) DEFAULT 0,
        actual_hours DECIMAL(6, 2) DEFAULT 0,
        materials_required JSON,
        tools_required JSON,
        safety_requirements JSON,
        completion_percentage DECIMAL(5, 2) DEFAULT 0,
        quality_rating DECIMAL(3, 2),
        customer_satisfaction DECIMAL(3, 2),
        cost_estimate DECIMAL(10, 2) DEFAULT 0,
        actual_cost DECIMAL(10, 2) DEFAULT 0,
        notes TEXT,
        attachments JSON,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Work orders table created');

    // Notifications table
    await executeQuery(`
      CREATE TABLE app_notifications (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36),
        type VARCHAR(50) NOT NULL,
        category VARCHAR(50) NOT NULL DEFAULT 'general',
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        data JSON,
        priority VARCHAR(20) DEFAULT 'normal',
        is_read TINYINT(1) DEFAULT 0,
        is_archived TINYINT(1) DEFAULT 0,
        read_at DATETIME NULL,
        expires_at DATETIME NULL,
        action_url VARCHAR(500),
        action_label VARCHAR(100),
        sender_id VARCHAR(36),
        broadcast TINYINT(1) DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Notifications table created');

    // System settings table
    await executeQuery(`
      CREATE TABLE app_system_settings (
        id VARCHAR(36) PRIMARY KEY,
        category VARCHAR(50) NOT NULL,
        setting_key VARCHAR(100) NOT NULL,
        setting_value TEXT,
        data_type VARCHAR(20) DEFAULT 'string',
        description TEXT,
        is_public TINYINT(1) DEFAULT 0,
        is_editable TINYINT(1) DEFAULT 1,
        validation_rules JSON,
        default_value TEXT,
        updated_by VARCHAR(36),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_setting (category, setting_key)
      )
    `);
    console.log('✅ System settings table created');

    // Audit logs table
    await executeQuery(`
      CREATE TABLE app_audit_logs (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36),
        action VARCHAR(100) NOT NULL,
        entity_type VARCHAR(50) NOT NULL,
        entity_id VARCHAR(36),
        old_values JSON,
        new_values JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        session_id VARCHAR(100),
        timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        severity VARCHAR(20) DEFAULT 'info',
        description TEXT,
        metadata JSON
      )
    `);
    console.log('✅ Audit logs table created');

    // User sessions table
    await executeQuery(`
      CREATE TABLE app_user_sessions (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        device_info JSON,
        location_info JSON,
        is_active TINYINT(1) DEFAULT 1,
        last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ User sessions table created');

    // Reports table
    await executeQuery(`
      CREATE TABLE app_reports (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(200) NOT NULL,
        description TEXT,
        type VARCHAR(50) NOT NULL,
        category VARCHAR(50) NOT NULL,
        template_id VARCHAR(36),
        parameters JSON,
        filters JSON,
        schedule_config JSON,
        output_format VARCHAR(20) DEFAULT 'pdf',
        file_path VARCHAR(500),
        file_size INT,
        status VARCHAR(20) DEFAULT 'pending',
        generated_by VARCHAR(36),
        generated_at DATETIME,
        expires_at DATETIME,
        download_count INT DEFAULT 0,
        is_public TINYINT(1) DEFAULT 0,
        is_scheduled TINYINT(1) DEFAULT 0,
        last_run DATETIME,
        next_run DATETIME,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Reports table created');

    console.log('✅ All enhanced tables created successfully');

  } catch (error) {
    console.error('❌ Enhanced table creation failed:', error.message);
    throw error;
  }
}

async function insertComprehensiveData() {
  console.log('🔄 Inserting comprehensive sample data...');

  try {
    // Insert enhanced users
    const users = [
      {
        id: uuidv4(),
        email: '<EMAIL>',
        passwordHash: 'hashed_password_admin',
        firstName: 'System',
        lastName: 'Administrator',
        role: 'super_admin',
        department: 'IT Operations',
        phone: '+251-11-123-0001',
        avatarUrl: '/avatars/admin.jpg',
        isActive: 1,
        isOnline: 1,
        preferences: JSON.stringify({
          theme: 'dark',
          language: 'en',
          notifications: true,
          dashboard_layout: 'grid'
        }),
        permissions: JSON.stringify(['all'])
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        passwordHash: 'hashed_password_manager',
        firstName: 'Asset',
        lastName: 'Manager',
        role: 'national_asset_manager',
        department: 'Asset Management',
        phone: '+251-11-123-0002',
        avatarUrl: '/avatars/manager.jpg',
        isActive: 1,
        isOnline: 0,
        preferences: JSON.stringify({
          theme: 'light',
          language: 'en',
          notifications: true,
          dashboard_layout: 'list'
        }),
        permissions: JSON.stringify(['read', 'write', 'approve'])
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        passwordHash: 'hashed_password_tech',
        firstName: 'Field',
        lastName: 'Technician',
        role: 'technician',
        department: 'Field Operations',
        phone: '+251-11-123-0003',
        avatarUrl: '/avatars/technician.jpg',
        isActive: 1,
        isOnline: 1,
        preferences: JSON.stringify({
          theme: 'light',
          language: 'en',
          notifications: true,
          dashboard_layout: 'cards'
        }),
        permissions: JSON.stringify(['read', 'update_status'])
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        passwordHash: 'hashed_password_supervisor',
        firstName: 'Maintenance',
        lastName: 'Supervisor',
        role: 'maintenance_manager',
        department: 'Maintenance',
        phone: '+251-11-123-0004',
        avatarUrl: '/avatars/supervisor.jpg',
        isActive: 1,
        isOnline: 0,
        preferences: JSON.stringify({
          theme: 'dark',
          language: 'en',
          notifications: true,
          dashboard_layout: 'grid'
        }),
        permissions: JSON.stringify(['read', 'write', 'schedule'])
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        passwordHash: 'hashed_password_analyst',
        firstName: 'Data',
        lastName: 'Analyst',
        role: 'viewer',
        department: 'Analytics',
        phone: '+251-11-123-0005',
        avatarUrl: '/avatars/analyst.jpg',
        isActive: 1,
        isOnline: 1,
        preferences: JSON.stringify({
          theme: 'light',
          language: 'en',
          notifications: false,
          dashboard_layout: 'charts'
        }),
        permissions: JSON.stringify(['read', 'export'])
      }
    ];

    for (const user of users) {
      await executeQuery(`
        INSERT INTO app_users (
          id, email, password_hash, first_name, last_name, role, department,
          phone, avatar_url, is_active, is_online, preferences, permissions,
          last_login, last_activity
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        user.id, user.email, user.passwordHash, user.firstName, user.lastName,
        user.role, user.department, user.phone, user.avatarUrl, user.isActive,
        user.isOnline, user.preferences, user.permissions
      ]);
    }
    console.log(`✅ Inserted ${users.length} enhanced users`);

    // Insert enhanced regions
    const regions = [
      {
        id: uuidv4(),
        name: 'Addis Ababa',
        code: 'AA',
        description: 'Capital city and administrative center of Ethiopia',
        lat: 9.0320,
        lng: 38.7469,
        population: 3500000,
        areaKm2: 527.0,
        timezone: 'Africa/Addis_Ababa',
        weatherStationId: 'ETH_AA_001'
      },
      {
        id: uuidv4(),
        name: 'Oromia',
        code: 'OR',
        description: 'Largest region by area and population in Ethiopia',
        lat: 8.5000,
        lng: 39.5000,
        population: 35000000,
        areaKm2: 353006.0,
        timezone: 'Africa/Addis_Ababa',
        weatherStationId: 'ETH_OR_001'
      },
      {
        id: uuidv4(),
        name: 'Amhara',
        code: 'AM',
        description: 'Northern highland region with rich cultural heritage',
        lat: 11.5000,
        lng: 37.5000,
        population: 21000000,
        areaKm2: 154708.0,
        timezone: 'Africa/Addis_Ababa',
        weatherStationId: 'ETH_AM_001'
      },
      {
        id: uuidv4(),
        name: 'SNNPR',
        code: 'SN',
        description: 'Southern Nations, Nationalities, and Peoples Region',
        lat: 6.5000,
        lng: 37.0000,
        population: 15000000,
        areaKm2: 112343.0,
        timezone: 'Africa/Addis_Ababa',
        weatherStationId: 'ETH_SN_001'
      },
      {
        id: uuidv4(),
        name: 'Tigray',
        code: 'TI',
        description: 'Northern region known for historical significance',
        lat: 14.0000,
        lng: 38.5000,
        population: 5000000,
        areaKm2: 50078.0,
        timezone: 'Africa/Addis_Ababa',
        weatherStationId: 'ETH_TI_001'
      },
      {
        id: uuidv4(),
        name: 'Afar',
        code: 'AF',
        description: 'Eastern region with unique geological features',
        lat: 11.7500,
        lng: 40.9500,
        population: 1800000,
        areaKm2: 72053.0,
        timezone: 'Africa/Addis_Ababa',
        weatherStationId: 'ETH_AF_001'
      },
      {
        id: uuidv4(),
        name: 'Somali',
        code: 'SO',
        description: 'Eastern region with pastoral communities',
        lat: 6.0000,
        lng: 43.0000,
        population: 5400000,
        areaKm2: 279252.0,
        timezone: 'Africa/Addis_Ababa',
        weatherStationId: 'ETH_SO_001'
      }
    ];

    for (const region of regions) {
      await executeQuery(`
        INSERT INTO app_regions (
          id, name, code, description, lat, lng, population, area_km2,
          timezone, weather_station_id, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        region.id, region.name, region.code, region.description,
        region.lat, region.lng, region.population, region.areaKm2,
        region.timezone, region.weatherStationId, 1
      ]);
    }
    console.log(`✅ Inserted ${regions.length} enhanced regions`);

    console.log('✅ Comprehensive data insertion completed');

  } catch (error) {
    console.error('❌ Comprehensive data insertion failed:', error.message);
    throw error;
  }
}

async function runEnhancedSetup() {
  console.log('🚀 Starting Enhanced MySQL Setup for dtms_eeu_db');
  console.log('================================================');

  const startTime = Date.now();

  try {
    await initConnection();
    await createEnhancedTables();
    await insertComprehensiveData();

    // Verify setup
    console.log('🔍 Verifying enhanced setup...');

    const tableNames = [
      'app_users', 'app_regions', 'app_service_centers', 'app_transformers',
      'app_alerts', 'app_maintenance_schedules', 'app_outages', 'app_sensor_readings',
      'app_performance_metrics', 'app_energy_consumption', 'app_weather_data',
      'app_inventory_items', 'app_work_orders', 'app_notifications',
      'app_system_settings', 'app_audit_logs', 'app_user_sessions', 'app_reports'
    ];

    const counts = {};
    for (const tableName of tableNames) {
      try {
        const result = await executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`);
        counts[tableName] = result[0].count;
      } catch (error) {
        counts[tableName] = 'Error';
      }
    }

    console.log('📊 Enhanced Setup Results:');
    Object.entries(counts).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} records`);
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`\n⏱️  Enhanced setup completed in ${duration.toFixed(2)} seconds`);
    console.log('🎉 Enhanced MySQL setup successful!');
    console.log('');
    console.log('✅ Database dtms_eeu_db now contains:');
    console.log('   - 18 comprehensive application tables');
    console.log('   - Enhanced user management with roles and permissions');
    console.log('   - Complete transformer lifecycle management');
    console.log('   - Advanced monitoring and alerting system');
    console.log('   - Maintenance scheduling and work order management');
    console.log('   - Energy consumption and performance tracking');
    console.log('   - Weather data integration');
    console.log('   - Inventory and asset management');
    console.log('   - Comprehensive audit logging');
    console.log('   - Real-time notifications system');
    console.log('   - Advanced reporting capabilities');
    console.log('');
    console.log('🔄 Ready for enhanced dashboard implementation!');

  } catch (error) {
    console.error('❌ Enhanced setup failed:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

runEnhancedSetup().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
