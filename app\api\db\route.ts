import { NextRequest, NextResponse } from 'next/server';
import { mysqlService } from '@/src/lib/db/mysql-service';

/**
 * API route for database operations
 * This allows us to use MySQL on the server side only
 */
export async function GET(request: NextRequest) {
  try {
    // Get the entity type and operation from the URL
    const { searchParams } = new URL(request.url);
    const entity = searchParams.get('entity');
    const operation = searchParams.get('operation');
    const id = searchParams.get('id');
    const query = searchParams.get('query');
    
    if (!entity || !operation) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }
    
    // Check if the entity repository exists
    const repository = (mysqlService as any)[entity];
    if (!repository) {
      return NextResponse.json(
        { error: `Entity repository '${entity}' not found` },
        { status: 404 }
      );
    }
    
    // Check if the operation exists
    const operationFn = repository[operation];
    if (!operationFn) {
      return NextResponse.json(
        { error: `Operation '${operation}' not found for entity '${entity}'` },
        { status: 404 }
      );
    }
    
    // Execute the operation
    let result;
    
    if (operation === 'getById' && id) {
      result = await operationFn(id);
    } else if (operation === 'getAll') {
      result = await operationFn();
    } else if (operation === 'search' && query) {
      result = await operationFn(query);
    } else if (operation === 'getByRegion' && id) {
      result = await operationFn(id);
    } else if (operation === 'getByServiceCenter' && id) {
      result = await operationFn(id);
    } else if (operation === 'getByStatus' && query) {
      result = await operationFn(query);
    } else {
      return NextResponse.json(
        { error: 'Invalid operation parameters' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Database API error:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing the request' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the entity type and operation from the URL
    const { searchParams } = new URL(request.url);
    const entity = searchParams.get('entity');
    const operation = searchParams.get('operation');
    
    if (!entity || !operation) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }
    
    // Get the request body
    const body = await request.json();
    
    // Check if the entity repository exists
    const repository = (mysqlService as any)[entity];
    if (!repository) {
      return NextResponse.json(
        { error: `Entity repository '${entity}' not found` },
        { status: 404 }
      );
    }
    
    // Check if the operation exists
    const operationFn = repository[operation];
    if (!operationFn) {
      return NextResponse.json(
        { error: `Operation '${operation}' not found for entity '${entity}'` },
        { status: 404 }
      );
    }
    
    // Execute the operation
    let result;
    
    if (operation === 'create') {
      result = await operationFn(body);
    } else if (operation === 'update' && body.id) {
      result = await operationFn(body.id, body);
    } else if (operation === 'delete' && body.id) {
      result = await operationFn(body.id);
    } else {
      return NextResponse.json(
        { error: 'Invalid operation parameters' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Database API error:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing the request' },
      { status: 500 }
    );
  }
}
