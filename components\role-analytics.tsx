"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/src/components/ui/card"
import { Badge } from "@/src/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { 
  ChartContainer, 
  ChartTooltip, 
  ChartTooltipContent, 
  ChartLegend, 
  ChartLegendContent 
} from "@/src/components/ui/chart"
import { 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  ResponsiveContainer,
  Tooltip,
  Legend
} from "recharts"
import { 
  Shield, 
  Users, 
  FileText, 
  BarChart3, 
  <PERSON><PERSON><PERSON> as <PERSON>ChartI<PERSON>,
  Layers
} from "lucide-react"
import type { RoleDefinition } from "@/src/types/auth"

interface RoleAnalyticsProps {
  roles: RoleDefinition[]
}

export function RoleAnalytics({ roles }: RoleAnalyticsProps) {
  // Calculate role distribution by organizational level
  const levelDistribution = [
    { 
      name: "Head Office", 
      value: roles.filter(r => r.organizationalLevel === "head_office").length,
      color: "#3b82f6" // blue-500
    },
    { 
      name: "Regional Office", 
      value: roles.filter(r => r.organizationalLevel === "regional_office").length,
      color: "#10b981" // emerald-500
    },
    { 
      name: "Service Center", 
      value: roles.filter(r => r.organizationalLevel === "service_center").length,
      color: "#f59e0b" // amber-500
    }
  ]

  // Calculate role distribution by type
  const typeDistribution = [
    { 
      name: "System Roles", 
      value: roles.filter(r => r.isSystemRole).length,
      color: "#6366f1" // indigo-500
    },
    { 
      name: "Custom Roles", 
      value: roles.filter(r => !r.isSystemRole).length,
      color: "#ec4899" // pink-500
    }
  ]

  // Calculate permission distribution
  const getPermissionCounts = () => {
    const resourceCounts: Record<string, number> = {}
    
    roles.forEach(role => {
      role.permissions.forEach(permission => {
        if (!resourceCounts[permission.resource]) {
          resourceCounts[permission.resource] = 0
        }
        resourceCounts[permission.resource]++
      })
    })
    
    return Object.entries(resourceCounts)
      .map(([name, value]) => ({ 
        name: name.charAt(0).toUpperCase() + name.slice(1), 
        value,
        color: getColorForResource(name)
      }))
      .sort((a, b) => b.value - a.value)
  }
  
  // Get a consistent color for each resource
  const getColorForResource = (resource: string) => {
    const colors = {
      users: "#3b82f6", // blue-500
      roles: "#8b5cf6", // violet-500
      transformers: "#10b981", // emerald-500
      maintenance: "#f59e0b", // amber-500
      outages: "#ef4444", // red-500
      customer_requests: "#ec4899", // pink-500
      reports: "#6366f1", // indigo-500
    }
    
    return colors[resource as keyof typeof colors] || "#94a3b8" // slate-400 as fallback
  }
  
  const permissionDistribution = getPermissionCounts()

  // Custom render for pie chart labels
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    const RADIAN = Math.PI / 180
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        className="text-xs font-medium"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5 text-muted-foreground" />
              Role Analytics
            </CardTitle>
            <CardDescription>Visualize role distribution and permissions</CardDescription>
          </div>
          <Badge variant="outline" className="ml-2">
            {roles.length} roles
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="distribution" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="distribution" className="flex items-center gap-2">
              <PieChartIcon className="h-4 w-4" />
              Distribution
            </TabsTrigger>
            <TabsTrigger value="permissions" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Permissions
            </TabsTrigger>
            <TabsTrigger value="types" className="flex items-center gap-2">
              <Layers className="h-4 w-4" />
              Types
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="distribution" className="space-y-4">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={levelDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={renderCustomizedLabel}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {levelDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="rounded-lg border bg-background p-2 shadow-md">
                            <div className="flex items-center gap-2">
                              <div 
                                className="h-3 w-3 rounded-full" 
                                style={{ backgroundColor: payload[0].payload.color }}
                              />
                              <span className="font-medium">{payload[0].name}</span>
                            </div>
                            <div className="mt-1 font-mono text-sm">
                              {payload[0].value} roles ({((payload[0].value / roles.length) * 100).toFixed(0)}%)
                            </div>
                          </div>
                        )
                      }
                      return null
                    }}
                  />
                  <Legend 
                    layout="horizontal" 
                    verticalAlign="bottom" 
                    align="center"
                    formatter={(value, entry, index) => (
                      <span className="text-sm text-muted-foreground">{value}</span>
                    )}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          
          <TabsContent value="permissions" className="space-y-4">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={permissionDistribution}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
                >
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="rounded-lg border bg-background p-2 shadow-md">
                            <div className="flex items-center gap-2">
                              <div 
                                className="h-3 w-3 rounded-full" 
                                style={{ backgroundColor: payload[0].payload.color }}
                              />
                              <span className="font-medium">{payload[0].name}</span>
                            </div>
                            <div className="mt-1 font-mono text-sm">
                              {payload[0].value} permissions
                            </div>
                          </div>
                        )
                      }
                      return null
                    }}
                  />
                  <Bar 
                    dataKey="value" 
                    nameKey="name"
                    radius={[0, 4, 4, 0]}
                  >
                    {permissionDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          
          <TabsContent value="types" className="space-y-4">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={typeDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={renderCustomizedLabel}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {typeDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="rounded-lg border bg-background p-2 shadow-md">
                            <div className="flex items-center gap-2">
                              <div 
                                className="h-3 w-3 rounded-full" 
                                style={{ backgroundColor: payload[0].payload.color }}
                              />
                              <span className="font-medium">{payload[0].name}</span>
                            </div>
                            <div className="mt-1 font-mono text-sm">
                              {payload[0].value} roles ({((payload[0].value / roles.length) * 100).toFixed(0)}%)
                            </div>
                          </div>
                        )
                      }
                      return null
                    }}
                  />
                  <Legend 
                    layout="horizontal" 
                    verticalAlign="bottom" 
                    align="center"
                    formatter={(value, entry, index) => (
                      <span className="text-sm text-muted-foreground">{value}</span>
                    )}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
