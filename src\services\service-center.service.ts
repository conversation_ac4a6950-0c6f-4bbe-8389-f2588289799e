/**
 * Service Center Service
 * Data access layer for service center operations
 */

import { executeQuery } from '@/src/lib/database'
import { ServiceCenter } from '@/src/types'

/**
 * Get all service centers with transformer counts
 */
export async function getServiceCenters(): Promise<ServiceCenter[]> {
  try {
    const query = `
      SELECT 
        sc.*,
        COUNT(t.id) as transformer_count
      FROM dtms_service_centers sc
      LEFT JOIN dtms_transformers t ON sc.id = t.service_center_id
      GROUP BY sc.id
      ORDER BY sc.name
    `
    
    const serviceCenters = await executeQuery<ServiceCenter>(query)
    return serviceCenters
  } catch (error) {
    console.error('Error fetching service centers:', error)
    throw new Error('Failed to fetch service centers')
  }
}

/**
 * Get service centers by region
 */
export async function getServiceCentersByRegion(regionId: string): Promise<ServiceCenter[]> {
  try {
    const query = `
      SELECT 
        sc.*,
        COUNT(t.id) as transformer_count
      FROM dtms_service_centers sc
      LEFT JOIN dtms_transformers t ON sc.id = t.service_center_id
      WHERE sc.region_id = ?
      GROUP BY sc.id
      ORDER BY sc.name
    `
    
    const serviceCenters = await executeQuery<ServiceCenter>(query, [regionId])
    return serviceCenters
  } catch (error) {
    console.error('Error fetching service centers by region:', error)
    throw new Error('Failed to fetch service centers by region')
  }
}
