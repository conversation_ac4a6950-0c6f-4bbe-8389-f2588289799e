/**
 * Weather Alert repository
 * 
 * This class provides specialized methods for working with weather alert entities.
 */

import { BaseRepository } from './base-repository';
import { WeatherAlert, AlertSeverity } from '../schema';

export class WeatherAlertRepository extends BaseRepository<WeatherAlert> {
  constructor() {
    super('weatherAlerts');
  }
  
  /**
   * Find weather alerts by region
   */
  findByRegion(regionId: string): WeatherAlert[] {
    return this.find({ regionId });
  }
  
  /**
   * Find weather alerts by severity
   */
  findBySeverity(severity: AlertSeverity | AlertSeverity[]): WeatherAlert[] {
    if (Array.isArray(severity)) {
      return this.find({}).filter(alert => severity.includes(alert.severity));
    }
    return this.find({ severity });
  }
  
  /**
   * Find weather alerts by type
   */
  findByType(type: string): WeatherAlert[] {
    return this.find({ type });
  }
  
  /**
   * Find active weather alerts
   */
  findActive(): WeatherAlert[] {
    return this.find({ isActive: true });
  }
  
  /**
   * Find weather alerts by date range
   */
  findByDateRange(startDate: Date, endDate: Date): WeatherAlert[] {
    return this.find({}).filter(alert => {
      const alertStartDate = new Date(alert.startTime);
      const alertEndDate = new Date(alert.endTime);
      
      // Check if the alert overlaps with the given date range
      return (
        (alertStartDate >= startDate && alertStartDate <= endDate) ||
        (alertEndDate >= startDate && alertEndDate <= endDate) ||
        (alertStartDate <= startDate && alertEndDate >= endDate)
      );
    });
  }
  
  /**
   * Find weather alerts affecting a transformer
   */
  findByTransformer(transformerId: string): WeatherAlert[] {
    return this.find({}).filter(alert => 
      alert.affectedTransformers.includes(transformerId)
    );
  }
  
  /**
   * Find upcoming weather alerts
   */
  findUpcoming(days: number = 7): WeatherAlert[] {
    const now = new Date();
    const futureDate = new Date(now);
    futureDate.setDate(futureDate.getDate() + days);
    
    return this.find({}).filter(alert => {
      const startTime = new Date(alert.startTime);
      return startTime >= now && startTime <= futureDate;
    });
  }
  
  /**
   * Update weather alert status
   */
  updateStatus(id: string, isActive: boolean): WeatherAlert | null {
    return this.update(id, { isActive });
  }
  
  /**
   * Get weather alert statistics
   */
  getStatistics() {
    const alerts = this.getAll();
    
    // Count by severity
    const severityCounts = alerts.reduce((counts, alert) => {
      counts[alert.severity] = (counts[alert.severity] || 0) + 1;
      return counts;
    }, {} as Record<AlertSeverity, number>);
    
    // Count by type
    const typeCounts = alerts.reduce((counts, alert) => {
      counts[alert.type] = (counts[alert.type] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);
    
    // Count by region
    const regionCounts = alerts.reduce((counts, alert) => {
      counts[alert.regionId] = (counts[alert.regionId] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);
    
    // Count active vs inactive
    const activeCount = alerts.filter(alert => alert.isActive).length;
    const inactiveCount = alerts.length - activeCount;
    
    // Calculate total affected transformers
    const totalAffectedTransformers = new Set(
      alerts.flatMap(alert => alert.affectedTransformers)
    ).size;
    
    return {
      total: alerts.length,
      active: activeCount,
      inactive: inactiveCount,
      bySeverity: severityCounts,
      byType: typeCounts,
      byRegion: regionCounts,
      totalAffectedTransformers
    };
  }
}

// Export a singleton instance
export const weatherAlertRepository = new WeatherAlertRepository();
