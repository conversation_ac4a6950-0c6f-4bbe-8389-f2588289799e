"use client"

import React, { useState, use<PERSON><PERSON>back, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { 
  Bell,
  BellOff,
  AlertTriangle,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  User,
  Users,
  Mail,
  Phone,
  MessageSquare,
  Smartphone,
  Monitor,
  Volume2,
  VolumeX,
  Eye,
  EyeOff,
  Filter,
  Search,
  MoreVertical,
  Settings,
  Download,
  Upload,
  RefreshCw,
  Archive,
  Trash2,
  Star,
  StarOff,
  Flag,
  Send,
  Reply,
  Forward,
  Share2,
  Copy,
  Edit,
  Save,
  X,
  Plus,
  Minus,
  ChevronDown,
  ChevronUp,
  History,
  TrendingUp,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  ThermometerSun,
  Gauge,
  Battery,
  Wifi,
  WifiOff,
  Shield,
  ShieldAlert,
  Power,
  PowerOff
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { toast } from 'sonner'

export interface Alert {
  id: string
  type: 'temperature' | 'voltage' | 'load' | 'maintenance' | 'communication' | 'weather' | 'security' | 'performance'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  source: {
    transformerId: string
    transformerName: string
    location: string
  }
  timestamp: Date
  acknowledged: boolean
  acknowledgedBy?: string
  acknowledgedAt?: Date
  resolved: boolean
  resolvedBy?: string
  resolvedAt?: Date
  escalated: boolean
  escalatedTo?: string
  escalatedAt?: Date
  priority: number
  tags: string[]
  attachments: Array<{
    id: string
    name: string
    url: string
    type: string
  }>
  relatedAlerts: string[]
  actions: Array<{
    id: string
    name: string
    description: string
    automated: boolean
  }>
}

export interface NotificationRule {
  id: string
  name: string
  description: string
  enabled: boolean
  conditions: {
    alertTypes: string[]
    severities: string[]
    sources: string[]
    timeRange?: { start: string; end: string }
    frequency: 'immediate' | 'hourly' | 'daily' | 'weekly'
  }
  channels: {
    email: { enabled: boolean; addresses: string[] }
    sms: { enabled: boolean; numbers: string[] }
    push: { enabled: boolean; devices: string[] }
    webhook: { enabled: boolean; url: string; headers: Record<string, string> }
    slack: { enabled: boolean; channel: string; webhook: string }
  }
  escalation: {
    enabled: boolean
    levels: Array<{
      delay: number // minutes
      recipients: string[]
      channels: string[]
    }>
  }
  createdAt: Date
  updatedAt: Date
}

interface AlertNotificationSystemProps {
  alerts: Alert[]
  notificationRules: NotificationRule[]
  onAlertUpdate: (id: string, updates: Partial<Alert>) => void
  onRuleUpdate: (id: string, updates: Partial<NotificationRule>) => void
  onBulkAlertAction: (ids: string[], action: string) => void
  className?: string
}

export function AlertNotificationSystem({
  alerts,
  notificationRules,
  onAlertUpdate,
  onRuleUpdate,
  onBulkAlertAction,
  className = ''
}: AlertNotificationSystemProps) {
  const [selectedAlerts, setSelectedAlerts] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [severityFilter, setSeverityFilter] = useState<string[]>([])
  const [typeFilter, setTypeFilter] = useState<string[]>([])
  const [statusFilter, setStatusFilter] = useState<string[]>([])
  const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null)
  const [showAlertDialog, setShowAlertDialog] = useState(false)
  const [showRuleDialog, setShowRuleDialog] = useState(false)
  const [showBulkDialog, setShowBulkDialog] = useState(false)
  const [activeTab, setActiveTab] = useState('alerts')
  const [sortBy, setSortBy] = useState<'timestamp' | 'severity' | 'type'>('timestamp')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [isLoading, setIsLoading] = useState(false)

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />
      case 'high': return <AlertCircle className="h-4 w-4" />
      case 'medium': return <Info className="h-4 w-4" />
      case 'low': return <Info className="h-4 w-4" />
      default: return <Bell className="h-4 w-4" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'temperature': return <ThermometerSun className="h-4 w-4" />
      case 'voltage': return <Zap className="h-4 w-4" />
      case 'load': return <Gauge className="h-4 w-4" />
      case 'maintenance': return <Settings className="h-4 w-4" />
      case 'communication': return <Wifi className="h-4 w-4" />
      case 'weather': return <Activity className="h-4 w-4" />
      case 'security': return <Shield className="h-4 w-4" />
      case 'performance': return <TrendingUp className="h-4 w-4" />
      default: return <Bell className="h-4 w-4" />
    }
  }

  const filteredAlerts = alerts
    .filter(alert => {
      const matchesSearch = alert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           alert.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           alert.source.transformerName.toLowerCase().includes(searchQuery.toLowerCase())
      
      const matchesSeverity = severityFilter.length === 0 || severityFilter.includes(alert.severity)
      const matchesType = typeFilter.length === 0 || typeFilter.includes(alert.type)
      
      const matchesStatus = statusFilter.length === 0 || 
        (statusFilter.includes('acknowledged') && alert.acknowledged) ||
        (statusFilter.includes('unacknowledged') && !alert.acknowledged) ||
        (statusFilter.includes('resolved') && alert.resolved) ||
        (statusFilter.includes('unresolved') && !alert.resolved) ||
        (statusFilter.includes('escalated') && alert.escalated)
      
      return matchesSearch && matchesSeverity && matchesType && matchesStatus
    })
    .sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'timestamp':
          comparison = a.timestamp.getTime() - b.timestamp.getTime()
          break
        case 'severity':
          const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
          comparison = severityOrder[a.severity] - severityOrder[b.severity]
          break
        case 'type':
          comparison = a.type.localeCompare(b.type)
          break
      }
      
      return sortOrder === 'desc' ? -comparison : comparison
    })

  const handleAlertAction = useCallback(async (alert: Alert, action: string) => {
    setIsLoading(true)
    try {
      switch (action) {
        case 'acknowledge':
          onAlertUpdate(alert.id, {
            acknowledged: true,
            acknowledgedBy: 'current-user',
            acknowledgedAt: new Date()
          })
          toast.success('Alert acknowledged')
          break
        case 'resolve':
          onAlertUpdate(alert.id, {
            resolved: true,
            resolvedBy: 'current-user',
            resolvedAt: new Date()
          })
          toast.success('Alert resolved')
          break
        case 'escalate':
          onAlertUpdate(alert.id, {
            escalated: true,
            escalatedTo: 'supervisor',
            escalatedAt: new Date()
          })
          toast.success('Alert escalated')
          break
        case 'archive':
          // In real implementation, this would archive the alert
          toast.success('Alert archived')
          break
      }
    } catch (error) {
      toast.error(`Action failed: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }, [onAlertUpdate])

  const handleBulkAction = useCallback(async (action: string) => {
    if (selectedAlerts.length === 0) {
      toast.error('No alerts selected')
      return
    }

    setIsLoading(true)
    try {
      await onBulkAlertAction(selectedAlerts, action)
      toast.success(`Bulk action completed for ${selectedAlerts.length} alerts`)
      setSelectedAlerts([])
      setShowBulkDialog(false)
    } catch (error) {
      toast.error(`Bulk action failed: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }, [selectedAlerts, onBulkAlertAction])

  const AlertCard = ({ alert }: { alert: Alert }) => (
    <Card className={`relative overflow-hidden group hover:shadow-lg transition-all duration-300 border-l-4 ${
      alert.severity === 'critical' ? 'border-l-red-500' :
      alert.severity === 'high' ? 'border-l-orange-500' :
      alert.severity === 'medium' ? 'border-l-yellow-500' : 'border-l-blue-500'
    } ${alert.acknowledged ? 'opacity-75' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <input
              type="checkbox"
              checked={selectedAlerts.includes(alert.id)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedAlerts([...selectedAlerts, alert.id])
                } else {
                  setSelectedAlerts(selectedAlerts.filter(id => id !== alert.id))
                }
              }}
              className="mt-1"
            />
            
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                {getTypeIcon(alert.type)}
                <CardTitle className="text-lg font-semibold">{alert.title}</CardTitle>
                <Badge className={getSeverityColor(alert.severity)}>
                  {getSeverityIcon(alert.severity)}
                  <span className="ml-1">{alert.severity.toUpperCase()}</span>
                </Badge>
              </div>
              
              <p className="text-gray-600 mb-2">{alert.message}</p>
              
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span className="flex items-center gap-1">
                  <Zap className="h-3 w-3" />
                  {alert.source.transformerName}
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {alert.timestamp.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {alert.acknowledged && (
              <Badge variant="outline" className="bg-green-50 text-green-700">
                <CheckCircle className="mr-1 h-3 w-3" />
                Acknowledged
              </Badge>
            )}
            
            {alert.resolved && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                <CheckCircle className="mr-1 h-3 w-3" />
                Resolved
              </Badge>
            )}
            
            {alert.escalated && (
              <Badge variant="outline" className="bg-orange-50 text-orange-700">
                <Flag className="mr-1 h-3 w-3" />
                Escalated
              </Badge>
            )}
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => {
                  setSelectedAlert(alert)
                  setShowAlertDialog(true)
                }}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                {!alert.acknowledged && (
                  <DropdownMenuItem onClick={() => handleAlertAction(alert, 'acknowledge')}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Acknowledge
                  </DropdownMenuItem>
                )}
                
                {!alert.resolved && (
                  <DropdownMenuItem onClick={() => handleAlertAction(alert, 'resolve')}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Resolve
                  </DropdownMenuItem>
                )}
                
                {!alert.escalated && (
                  <DropdownMenuItem onClick={() => handleAlertAction(alert, 'escalate')}>
                    <Flag className="mr-2 h-4 w-4" />
                    Escalate
                  </DropdownMenuItem>
                )}
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={() => handleAlertAction(alert, 'archive')}>
                  <Archive className="mr-2 h-4 w-4" />
                  Archive
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
    </Card>
  )

  const criticalAlerts = alerts.filter(a => a.severity === 'critical' && !a.resolved).length
  const unacknowledgedAlerts = alerts.filter(a => !a.acknowledged && !a.resolved).length
  const escalatedAlerts = alerts.filter(a => a.escalated && !a.resolved).length

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Alert Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-red-50 to-red-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600">Critical Alerts</p>
                <p className="text-2xl font-bold text-red-900">{criticalAlerts}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">Unacknowledged</p>
                <p className="text-2xl font-bold text-orange-900">{unacknowledgedAlerts}</p>
              </div>
              <Bell className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-yellow-50 to-yellow-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600">Escalated</p>
                <p className="text-2xl font-bold text-yellow-900">{escalatedAlerts}</p>
              </div>
              <Flag className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Alerts</p>
                <p className="text-2xl font-bold text-blue-900">{alerts.length}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="alerts">
            <Bell className="h-4 w-4 mr-2" />
            Alerts ({filteredAlerts.length})
          </TabsTrigger>
          <TabsTrigger value="rules">
            <Settings className="h-4 w-4 mr-2" />
            Notification Rules ({notificationRules.length})
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="alerts" className="space-y-4">
          {/* Alert Controls */}
          <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Alert Management</CardTitle>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>

              {/* Search and Filters */}
              <div className="flex items-center gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search alerts by title, message, or transformer..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="timestamp">Timestamp</SelectItem>
                    <SelectItem value="severity">Severity</SelectItem>
                    <SelectItem value="type">Type</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                >
                  {sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </div>

              {/* Bulk Actions */}
              {selectedAlerts.length > 0 && (
                <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <span className="text-sm font-medium">
                    {selectedAlerts.length} alert{selectedAlerts.length > 1 ? 's' : ''} selected
                  </span>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleBulkAction('acknowledge')}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Acknowledge All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleBulkAction('resolve')}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Resolve All
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedAlerts([])}
                    >
                      Clear Selection
                    </Button>
                  </div>
                </div>
              )}
            </CardHeader>
          </Card>

          {/* Alerts List */}
          <div className="space-y-4">
            {filteredAlerts.map((alert) => (
              <AlertCard key={alert.id} alert={alert} />
            ))}
            
            {filteredAlerts.length === 0 && (
              <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
                <CardContent className="p-12 text-center">
                  <Bell className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Alerts Found</h3>
                  <p className="text-gray-600">
                    {searchQuery || severityFilter.length > 0 || typeFilter.length > 0 || statusFilter.length > 0
                      ? 'No alerts match your current filters. Try adjusting your search criteria.'
                      : 'No alerts are currently active in the system.'
                    }
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="rules" className="space-y-4">
          <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Notification Rules</CardTitle>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Rule
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {notificationRules.map((rule) => (
                  <Card key={rule.id} className="border">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold">{rule.name}</h3>
                          <p className="text-sm text-gray-600">{rule.description}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={rule.enabled}
                            onCheckedChange={(checked) => onRuleUpdate(rule.id, { enabled: checked })}
                          />
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Alert Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4" />
                  <p>Alert analytics and trends will be displayed here</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Alert Detail Dialog */}
      <Dialog open={showAlertDialog} onOpenChange={setShowAlertDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedAlert && getTypeIcon(selectedAlert.type)}
              Alert Details
            </DialogTitle>
          </DialogHeader>
          
          {selectedAlert && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Title</Label>
                  <p className="font-semibold">{selectedAlert.title}</p>
                </div>
                <div>
                  <Label>Severity</Label>
                  <Badge className={getSeverityColor(selectedAlert.severity)}>
                    {selectedAlert.severity.toUpperCase()}
                  </Badge>
                </div>
                <div>
                  <Label>Type</Label>
                  <p>{selectedAlert.type}</p>
                </div>
                <div>
                  <Label>Timestamp</Label>
                  <p>{selectedAlert.timestamp.toLocaleString()}</p>
                </div>
              </div>
              
              <div>
                <Label>Message</Label>
                <p className="mt-1 p-3 bg-gray-50 rounded-lg">{selectedAlert.message}</p>
              </div>
              
              <div>
                <Label>Source</Label>
                <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                  <p><strong>Transformer:</strong> {selectedAlert.source.transformerName}</p>
                  <p><strong>Location:</strong> {selectedAlert.source.location}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
