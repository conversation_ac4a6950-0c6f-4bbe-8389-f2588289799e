/**
 * Clean Dashboard API Route
 * Optimized dashboard data fetching with caching
 */

import { NextRequest, NextResponse } from 'next/server'
import { getDashboardSummary, getTransformers } from '@/src/services/transformer.service'
import { getRegions } from '@/src/services/region.service'
import { getServiceCenters } from '@/src/services/service-center.service'
import { getActiveAlerts } from '@/src/services/alert.service'
import { getPendingMaintenance } from '@/src/services/maintenance.service'
import { FilterOptions, DashboardData } from '@/src/types'

// Cache dashboard data for 30 seconds to improve performance
const CACHE_DURATION = 30 * 1000 // 30 seconds
let cachedData: { data: DashboardData; timestamp: number } | null = null

/**
 * GET /api/dashboard
 * Fetch complete dashboard data with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Check if we should use cached data
    const useCache = searchParams.get('cache') !== 'false'
    const now = Date.now()
    
    if (useCache && cachedData && (now - cachedData.timestamp) < CACHE_DURATION) {
      return NextResponse.json({
        success: true,
        data: cachedData.data,
        cached: true,
        cacheAge: Math.round((now - cachedData.timestamp) / 1000)
      })
    }
    
    // Parse filter parameters (same as transformers API)
    const filters: FilterOptions = parseFilters(searchParams)
    
    // Fetch all dashboard data in parallel for better performance
    const [
      transformersResult,
      summary,
      regions,
      serviceCenters,
      alerts,
      maintenance
    ] = await Promise.all([
      getTransformers(filters, 1, 1000), // Get first 1000 transformers
      getDashboardSummary(filters),
      getRegions(),
      getServiceCenters(),
      getActiveAlerts(filters),
      getPendingMaintenance(filters)
    ])
    
    const dashboardData: DashboardData = {
      transformers: transformersResult.data || [],
      alerts: alerts || [],
      maintenance: maintenance || [],
      regions: regions || [],
      serviceCenters: serviceCenters || [],
      summary
    }
    
    // Cache the data
    if (useCache) {
      cachedData = {
        data: dashboardData,
        timestamp: now
      }
    }
    
    return NextResponse.json({
      success: true,
      data: dashboardData,
      cached: false,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('Error in dashboard API:', error)
    
    // Return fallback data if database fails
    const fallbackData = getFallbackDashboardData()
    
    return NextResponse.json({
      success: false,
      data: fallbackData,
      error: 'Database error, showing fallback data',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 200 }) // Return 200 with fallback data instead of 500
  }
}

/**
 * Parse filter parameters from search params
 */
function parseFilters(searchParams: URLSearchParams): FilterOptions {
  const filters: FilterOptions = {}
  
  // Array filters
  const regions = searchParams.get('regions')?.split(',').filter(Boolean)
  if (regions?.length) filters.regions = regions
  
  const serviceCenters = searchParams.get('serviceCenters')?.split(',').filter(Boolean)
  if (serviceCenters?.length) filters.serviceCenters = serviceCenters
  
  const types = searchParams.get('types')?.split(',').filter(Boolean)
  if (types?.length) filters.types = types as any
  
  const statuses = searchParams.get('statuses')?.split(',').filter(Boolean)
  if (statuses?.length) filters.statuses = statuses as any
  
  const manufacturers = searchParams.get('manufacturers')?.split(',').filter(Boolean)
  if (manufacturers?.length) filters.manufacturers = manufacturers
  
  // Range filters with validation
  const capacityMin = parseFloat(searchParams.get('capacityMin') || '0')
  const capacityMax = parseFloat(searchParams.get('capacityMax') || '2000')
  if (capacityMin >= 0 && capacityMax > capacityMin) {
    filters.capacityRange = [capacityMin, capacityMax]
  }
  
  const efficiencyMin = parseFloat(searchParams.get('efficiencyMin') || '0')
  const efficiencyMax = parseFloat(searchParams.get('efficiencyMax') || '100')
  if (efficiencyMin >= 0 && efficiencyMax > efficiencyMin) {
    filters.efficiencyRange = [efficiencyMin, efficiencyMax]
  }
  
  // Search filter
  const search = searchParams.get('search')
  if (search?.trim()) filters.search = search.trim()
  
  return filters
}

/**
 * Fallback data when database is unavailable
 */
function getFallbackDashboardData(): DashboardData {
  return {
    transformers: [],
    alerts: [],
    maintenance: [],
    regions: [
      { id: '1', name: 'Addis Ababa', code: 'AA', createdAt: new Date(), updatedAt: new Date() },
      { id: '2', name: 'Oromia', code: 'OR', createdAt: new Date(), updatedAt: new Date() },
      { id: '3', name: 'Amhara', code: 'AM', createdAt: new Date(), updatedAt: new Date() },
      { id: '4', name: 'Tigray', code: 'TI', createdAt: new Date(), updatedAt: new Date() },
      { id: '5', name: 'SNNPR', code: 'SN', createdAt: new Date(), updatedAt: new Date() }
    ],
    serviceCenters: [],
    summary: {
      totalTransformers: 0,
      operationalCount: 0,
      warningCount: 0,
      criticalCount: 0,
      maintenanceCount: 0,
      avgEfficiency: 0,
      avgLoadFactor: 0,
      avgTemperature: 0,
      totalAssetValue: 0,
      activeAlerts: 0,
      pendingMaintenance: 0
    }
  }
}

/**
 * POST /api/dashboard/refresh
 * Force refresh dashboard cache
 */
export async function POST(request: NextRequest) {
  try {
    // Clear cache
    cachedData = null
    
    return NextResponse.json({
      success: true,
      message: 'Dashboard cache cleared'
    })
    
  } catch (error) {
    console.error('Error refreshing dashboard:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to refresh dashboard',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
