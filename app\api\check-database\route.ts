import { NextRequest, NextResponse } from 'next/server'
import { checkDatabaseStatus, getTableStructure } from '../../../src/lib/db/check-database'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 API: Checking database status...')
    
    const status = await checkDatabaseStatus()
    
    return NextResponse.json({
      success: true,
      status,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('❌ API: Database status check failed:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to check database status',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, tableName } = body
    
    if (action === 'getTableStructure' && tableName) {
      console.log(`🔍 API: Getting structure for table ${tableName}...`)
      
      const structure = await getTableStructure(tableName)
      
      return NextResponse.json({
        success: true,
        structure,
        timestamp: new Date().toISOString()
      })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Invalid action or missing parameters',
      timestamp: new Date().toISOString()
    }, { status: 400 })
    
  } catch (error) {
    console.error('❌ API: Database check failed:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Database check failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
