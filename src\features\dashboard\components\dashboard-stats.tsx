import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/src/components/ui/card"
import { <PERSON>ge } from "@/src/components/ui/badge"
import { Progress } from "@/src/components/ui/progress"
import { Skeleton } from "@/src/components/ui/skeleton"
import { 
  Zap, Shield, AlertTriangle, Wrench, TrendingUp, Activity,
  CheckCircle2, Clock, XCircle, AlertCircle
} from "lucide-react"
import { cn } from "@/src/lib/utils"
import type { DashboardStatsProps } from "@/src/types/dashboard"

export function DashboardStats({ stats, isLoading, className }: DashboardStatsProps) {
  if (isLoading) {
    return (
      <div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-6", className)}>
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-20" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const statCards = [
    {
      title: "Total Transformers",
      value: stats.totalTransformers,
      icon: Zap,
      color: "text-blue-600",
      bgColor: "bg-gradient-to-r from-blue-500/5 to-transparent",
      description: "Active units",
      trend: "+2.5%"
    },
    {
      title: "System Health",
      value: `${stats.healthyPercentage}%`,
      icon: Shield,
      color: "text-green-600",
      bgColor: "bg-gradient-to-r from-green-500/5 to-transparent",
      description: "Overall health",
      progress: stats.healthyPercentage
    },
    {
      title: "Active Alerts",
      value: stats.activeAlerts,
      icon: AlertTriangle,
      color: "text-orange-600",
      bgColor: "bg-gradient-to-r from-orange-500/5 to-transparent",
      description: "Require attention",
      badge: stats.criticalIssues > 0 ? "Critical" : "Normal"
    },
    {
      title: "Maintenance",
      value: stats.scheduledMaintenance,
      icon: Wrench,
      color: "text-purple-600",
      bgColor: "bg-gradient-to-r from-purple-500/5 to-transparent",
      description: "Scheduled tasks",
      trend: "+12%"
    },
    {
      title: "System Uptime",
      value: `${stats.systemUptime}%`,
      icon: TrendingUp,
      color: "text-emerald-600",
      bgColor: "bg-gradient-to-r from-emerald-500/5 to-transparent",
      description: "Last 30 days",
      progress: stats.systemUptime
    },
    {
      title: "Critical Issues",
      value: stats.criticalIssues,
      icon: Activity,
      color: stats.criticalIssues > 0 ? "text-red-600" : "text-gray-600",
      bgColor: stats.criticalIssues > 0 
        ? "bg-gradient-to-r from-red-500/5 to-transparent"
        : "bg-gradient-to-r from-gray-500/5 to-transparent",
      description: "Need immediate action",
      badge: stats.criticalIssues > 0 ? "Urgent" : "Clear"
    }
  ]

  return (
    <div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-6", className)}>
      {statCards.map((stat, index) => (
        <Card key={index} className="relative overflow-hidden group hover:shadow-md transition-shadow">
          <div className={cn("absolute inset-0 rounded-lg", stat.bgColor)}></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className={cn("h-4 w-4", stat.color)} />
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stat.value}</div>
              {stat.badge && (
                <Badge 
                  variant={stat.badge === "Critical" || stat.badge === "Urgent" ? "destructive" : "secondary"}
                  className="text-xs"
                >
                  {stat.badge}
                </Badge>
              )}
            </div>
            <div className="flex items-center justify-between mt-2">
              <p className="text-xs text-muted-foreground">{stat.description}</p>
              {stat.trend && (
                <span className="text-xs text-green-600 font-medium">{stat.trend}</span>
              )}
            </div>
            {stat.progress !== undefined && (
              <Progress 
                value={stat.progress} 
                className="mt-2 h-1"
                indicatorClassName={
                  stat.progress >= 90 ? "bg-green-500" :
                  stat.progress >= 70 ? "bg-yellow-500" : "bg-red-500"
                }
              />
            )}
          </CardContent>
        </Card>
      ))}

      {/* Status Breakdown Card */}
      <Card className="md:col-span-2 lg:col-span-6">
        <CardHeader>
          <CardTitle className="text-lg">Transformer Status Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="flex items-center space-x-2">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">Operational</p>
                <p className="text-2xl font-bold text-green-600">{stats.operationalCount}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm font-medium">Warning</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.warningCount}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Maintenance</p>
                <p className="text-2xl font-bold text-blue-600">{stats.maintenanceCount}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm font-medium">Critical</p>
                <p className="text-2xl font-bold text-red-600">{stats.criticalCount}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <XCircle className="h-4 w-4 text-gray-600" />
              <div>
                <p className="text-sm font-medium">Offline</p>
                <p className="text-2xl font-bold text-gray-600">{stats.offlineCount}</p>
              </div>
            </div>
          </div>
          
          {/* Status Progress Bars */}
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Operational Status</span>
              <span>{Math.round((stats.operationalCount / stats.totalTransformers) * 100)}%</span>
            </div>
            <Progress 
              value={(stats.operationalCount / stats.totalTransformers) * 100} 
              className="h-2"
              indicatorClassName="bg-green-500"
            />
            
            <div className="flex items-center justify-between text-sm">
              <span>Issues Requiring Attention</span>
              <span>{Math.round(((stats.warningCount + stats.criticalCount) / stats.totalTransformers) * 100)}%</span>
            </div>
            <Progress 
              value={((stats.warningCount + stats.criticalCount) / stats.totalTransformers) * 100} 
              className="h-2"
              indicatorClassName="bg-orange-500"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
