/**
 * Insert Dashboard Data for EEU DTMS
 * Simple data insertion without table modifications
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function insertDashboardData() {
  let connection;
  
  try {
    console.log('📊 INSERTING DASHBOARD DATA FOR EEU DTMS');
    console.log('=' .repeat(50));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // Check current data
    const [currentTransformers] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    console.log(`📊 Current transformers: ${currentTransformers[0].count}`);
    
    // Add transformers if none exist
    if (currentTransformers[0].count === 0) {
      console.log('\n⚡ Adding Transformers...');
      
      const transformers = [
        {
          serial_number: 'EEU-AA-001',
          name: 'Bole Main Distribution Transformer',
          type: 'distribution',
          capacity_kva: 1000.00,
          voltage_primary: 33.00,
          voltage_secondary: 0.40,
          manufacturer: 'Siemens',
          model: 'GEAFOL Cast Resin',
          year_manufactured: 2020,
          installation_date: '2020-06-15',
          location_name: 'Bole Road, Near EEU Headquarters',
          latitude: 9.02220000,
          longitude: 38.74680000,
          region_id: 1,
          status: 'operational',
          efficiency_rating: 98.50,
          load_factor: 75.00,
          temperature: 65.00,
          oil_level: 95.00,
          last_maintenance: '2024-09-15',
          next_maintenance: '2024-12-15'
        },
        {
          serial_number: 'EEU-AA-002',
          name: 'Megenagna Distribution Transformer',
          type: 'distribution',
          capacity_kva: 500.00,
          voltage_primary: 33.00,
          voltage_secondary: 0.40,
          manufacturer: 'ABB',
          model: 'UniGear ZS1',
          year_manufactured: 2019,
          installation_date: '2019-08-20',
          location_name: 'Megenagna, Addis Ababa',
          latitude: 9.02990000,
          longitude: 38.80790000,
          region_id: 1,
          status: 'warning',
          efficiency_rating: 97.80,
          load_factor: 88.00,
          temperature: 72.00,
          oil_level: 85.00,
          last_maintenance: '2024-08-10',
          next_maintenance: '2024-11-10'
        },
        {
          serial_number: 'EEU-OR-001',
          name: 'Jimma Central Distribution Transformer',
          type: 'distribution',
          capacity_kva: 500.00,
          voltage_primary: 33.00,
          voltage_secondary: 0.40,
          manufacturer: 'ABB',
          model: 'UniGear ZS1',
          year_manufactured: 2019,
          installation_date: '2019-08-20',
          location_name: 'Jimma City Center',
          latitude: 7.67810000,
          longitude: 36.83440000,
          region_id: 2,
          status: 'operational',
          efficiency_rating: 97.80,
          load_factor: 68.00,
          temperature: 62.00,
          oil_level: 92.00,
          last_maintenance: '2024-10-05',
          next_maintenance: '2025-01-05'
        },
        {
          serial_number: 'EEU-AM-001',
          name: 'Bahir Dar Power Distribution Transformer',
          type: 'power',
          capacity_kva: 2000.00,
          voltage_primary: 132.00,
          voltage_secondary: 33.00,
          manufacturer: 'Schneider Electric',
          model: 'Trihal',
          year_manufactured: 2021,
          installation_date: '2021-03-10',
          location_name: 'Bahir Dar Industrial Zone',
          latitude: 11.59590000,
          longitude: 37.39060000,
          region_id: 3,
          status: 'maintenance',
          efficiency_rating: 99.20,
          load_factor: 45.00,
          temperature: 58.00,
          oil_level: 98.00,
          last_maintenance: '2024-11-20',
          next_maintenance: '2024-12-20'
        },
        {
          serial_number: 'EEU-TI-001',
          name: 'Mekelle Central Transformer',
          type: 'distribution',
          capacity_kva: 630.00,
          voltage_primary: 33.00,
          voltage_secondary: 0.40,
          manufacturer: 'ABB',
          model: 'UniGear ZS1',
          year_manufactured: 2017,
          installation_date: '2017-05-08',
          location_name: 'Mekelle City Center',
          latitude: 13.49670000,
          longitude: 39.47530000,
          region_id: 4,
          status: 'critical',
          efficiency_rating: 94.20,
          load_factor: 95.00,
          temperature: 85.00,
          oil_level: 65.00,
          last_maintenance: '2024-07-15',
          next_maintenance: '2024-12-10'
        },
        {
          serial_number: 'EEU-SN-001',
          name: 'Hawassa Distribution Transformer',
          type: 'distribution',
          capacity_kva: 400.00,
          voltage_primary: 33.00,
          voltage_secondary: 0.40,
          manufacturer: 'Schneider Electric',
          model: 'Trihal',
          year_manufactured: 2020,
          installation_date: '2020-09-15',
          location_name: 'Hawassa City Center',
          latitude: 7.06210000,
          longitude: 38.47760000,
          region_id: 5,
          status: 'operational',
          efficiency_rating: 98.00,
          load_factor: 65.00,
          temperature: 60.00,
          oil_level: 90.00,
          last_maintenance: '2024-09-01',
          next_maintenance: '2024-12-01'
        }
      ];

      for (const transformer of transformers) {
        try {
          await connection.execute(`
            INSERT INTO app_transformers (
              serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
              manufacturer, model, year_manufactured, installation_date, location_name,
              latitude, longitude, region_id, status, efficiency_rating, load_factor,
              temperature, oil_level, last_maintenance, next_maintenance
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            transformer.serial_number, transformer.name, transformer.type, transformer.capacity_kva,
            transformer.voltage_primary, transformer.voltage_secondary, transformer.manufacturer,
            transformer.model, transformer.year_manufactured, transformer.installation_date,
            transformer.location_name, transformer.latitude, transformer.longitude,
            transformer.region_id, transformer.status, transformer.efficiency_rating,
            transformer.load_factor, transformer.temperature, transformer.oil_level,
            transformer.last_maintenance, transformer.next_maintenance
          ]);
          console.log(`  ✅ Added: ${transformer.name}`);
        } catch (error) {
          console.log(`  ⚠️  Skipped: ${transformer.name} (${error.message})`);
        }
      }
    }

    // Add maintenance schedules
    console.log('\n🔧 Adding Maintenance Schedules...');
    const maintenanceSchedules = [
      { transformer_id: 1, type: 'routine', title: 'Monthly Visual Inspection - Bole Main', description: 'Regular monthly visual inspection and basic checks', scheduled_date: '2024-12-30', estimated_duration: 2, priority: 'medium', status: 'scheduled' },
      { transformer_id: 2, type: 'preventive', title: 'Quarterly Maintenance - Megenagna', description: 'Comprehensive quarterly electrical testing and oil analysis', scheduled_date: '2024-12-25', estimated_duration: 8, priority: 'high', status: 'in_progress' },
      { transformer_id: 3, type: 'routine', title: 'Monthly Inspection - Jimma Central', description: 'Monthly routine inspection and cleaning', scheduled_date: '2024-12-20', estimated_duration: 2, priority: 'low', status: 'completed' },
      { transformer_id: 4, type: 'corrective', title: 'Oil Level Restoration - Bahir Dar', description: 'Restore oil level and investigate potential leaks', scheduled_date: '2024-12-15', estimated_duration: 4, priority: 'high', status: 'scheduled' },
      { transformer_id: 5, type: 'emergency', title: 'Critical Temperature Issue - Mekelle', description: 'Emergency response to critical temperature alert', scheduled_date: '2024-12-10', estimated_duration: 6, priority: 'critical', status: 'completed' },
      { transformer_id: 6, type: 'preventive', title: 'Annual Comprehensive Maintenance - Hawassa', description: 'Complete annual maintenance including oil change', scheduled_date: '2025-01-15', estimated_duration: 24, priority: 'high', status: 'scheduled' }
    ];

    for (const schedule of maintenanceSchedules) {
      try {
        await connection.execute(`
          INSERT INTO app_maintenance_schedules (
            transformer_id, type, title, description, scheduled_date, estimated_duration, priority, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          schedule.transformer_id, schedule.type, schedule.title, schedule.description,
          schedule.scheduled_date, schedule.estimated_duration, schedule.priority, schedule.status
        ]);
        console.log(`  ✅ Added: ${schedule.title}`);
      } catch (error) {
        console.log(`  ⚠️  Skipped: ${schedule.title} (${error.message})`);
      }
    }

    // Add alerts
    console.log('\n🚨 Adding Alerts...');
    const alerts = [
      { transformer_id: 2, title: 'High Temperature Alert', description: 'Transformer temperature has exceeded 70°C threshold', severity: 'high', type: 'temperature', priority: 'high', status: 'active', is_resolved: false },
      { transformer_id: 2, title: 'Overload Warning', description: 'Transformer operating at 88% capacity, exceeding 85% threshold', severity: 'medium', type: 'load', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 5, title: 'Critical Temperature Alert', description: 'Transformer temperature reached critical level - resolved', severity: 'critical', type: 'temperature', priority: 'critical', status: 'resolved', is_resolved: true, resolved_at: '2024-12-10 14:30:00' },
      { transformer_id: 5, title: 'Low Oil Level Warning', description: 'Transformer oil level dropped to 65%, below 80% minimum', severity: 'medium', type: 'maintenance', priority: 'medium', status: 'active', is_resolved: false },
      { transformer_id: 1, title: 'Routine Maintenance Due', description: 'Scheduled monthly maintenance approaching', severity: 'low', type: 'maintenance', priority: 'low', status: 'active', is_resolved: false },
      { transformer_id: 6, title: 'Communication Test Alert', description: 'Testing alert system - resolved successfully', severity: 'low', type: 'communication', priority: 'low', status: 'resolved', is_resolved: true, resolved_at: '2024-12-01 10:00:00' }
    ];

    for (const alert of alerts) {
      try {
        await connection.execute(`
          INSERT INTO app_alerts (
            transformer_id, title, description, severity, type, priority, status, is_resolved, resolved_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          alert.transformer_id, alert.title, alert.description, alert.severity,
          alert.type, alert.priority, alert.status, alert.is_resolved,
          alert.resolved_at || null
        ]);
        console.log(`  ✅ Added: ${alert.title}`);
      } catch (error) {
        console.log(`  ⚠️  Skipped: ${alert.title} (${error.message})`);
      }
    }

    // Final verification
    const [finalTransformers] = await connection.execute('SELECT COUNT(*) as count FROM app_transformers');
    const [finalMaintenance] = await connection.execute('SELECT COUNT(*) as count FROM app_maintenance_schedules');
    const [finalAlerts] = await connection.execute('SELECT COUNT(*) as count FROM app_alerts');

    console.log('\n' + '=' .repeat(50));
    console.log('🎉 DASHBOARD DATA INSERTION COMPLETED!');
    console.log('=' .repeat(50));
    
    console.log('\n📊 FINAL COUNTS:');
    console.log(`  ⚡ Transformers: ${finalTransformers[0].count}`);
    console.log(`  🔧 Maintenance Schedules: ${finalMaintenance[0].count}`);
    console.log(`  🚨 Alerts: ${finalAlerts[0].count}`);
    
    console.log('\n🎯 DASHBOARD NOW READY WITH:');
    console.log('  • Real transformer data from Ethiopian locations');
    console.log('  • Active maintenance schedules and tasks');
    console.log('  • Live alerts with different severity levels');
    console.log('  • Performance metrics and status indicators');
    
    console.log('\n🌟 Refresh your dashboard to see the data!');
    console.log('🔗 URL: http://localhost:3002');
    
  } catch (error) {
    console.error('❌ Error inserting dashboard data:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { insertDashboardData };

// Run if called directly
if (require.main === module) {
  insertDashboardData();
}
