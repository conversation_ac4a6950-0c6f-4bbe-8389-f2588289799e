const mysql = require('mysql2/promise');

(async () => {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'dtms_eeu_db'
  });

  try {
    console.log('Connected to the database.');

    console.log('Initializing MySQL tables...');

    // Create transformers table
    console.log('Executing table creation for transformers...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS transformers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        location VARCHAR(255) NOT NULL,
        status ENUM('operational', 'maintenance', 'burned') DEFAULT 'operational',
        voltage VARCHAR(50),
        capacity VARCHAR(50),
        load_percentage DECIMAL(5,2) DEFAULT 0,
        condition_score DECIMAL(5,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create maintenance_schedules table
    console.log('Executing table creation for maintenance_schedules...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS maintenance_schedules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        transformer_id INT,
        type ENUM('preventive', 'corrective', 'emergency') NOT NULL,
        status ENUM('scheduled', 'in_progress', 'completed') DEFAULT 'scheduled',
        scheduled_date DATE,
        completed_date DATE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES transformers(id)
      )
    `);

    // Create alerts table
    console.log('Executing table creation for alerts...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS alerts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        transformer_id INT,
        severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES transformers(id)
      )
    `);

    console.log('MySQL tables initialized successfully.');
  } catch (error) {
    console.error('Error initializing MySQL tables:', error);
  } finally {
    await connection.end();
  }
})();
