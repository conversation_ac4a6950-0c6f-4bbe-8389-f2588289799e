'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import { UnifiedMap } from '@/components/unified-map'
import { MapLocation } from '@/src/services/map-service'
import { TransformerDataStandardizer, StandardizedTransformer } from '@/src/services/transformer-data-standardizer'
import {
  MapPin,
  Zap,
  Calendar,
  Thermometer,
  Gauge,
  RefreshCw,
  Filter,
  Download,
  Eye,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wrench
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/src/components/ui/dialog'
import { ErrorBoundary } from '@/components/error-boundary'

interface TransformerMapWithDetailsProps {
  height?: string
  showControls?: boolean
  allowFullscreen?: boolean
}

export default function TransformerMapWithDetails({
  height = "600px",
  showControls = true,
  allowFullscreen = true
}: TransformerMapWithDetailsProps) {
  const router = useRouter()
  const [transformers, setTransformers] = useState<StandardizedTransformer[]>([])
  const [mapLocations, setMapLocations] = useState<MapLocation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTransformer, setSelectedTransformer] = useState<StandardizedTransformer | null>(null)
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [stats, setStats] = useState({
    total: 0,
    operational: 0,
    warning: 0,
    maintenance: 0,
    critical: 0,
    offline: 0
  })

  const loadTransformers = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🗺️ Loading all transformers for map...')
      const response = await fetch('/api/mysql/transformers')

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`)
      }

      const data = await response.json()
      console.log('🗺️ API Response:', data)

      if (!data.success || !data.data?.transformers) {
        throw new Error('Invalid response structure')
      }

      // Transform all data using standardizer
      const standardizedTransformers = data.data.transformers.map((t: any) =>
        TransformerDataStandardizer.standardize(t)
      )

      // Convert to map locations with detailed information
      const locations: MapLocation[] = standardizedTransformers.map((transformer: StandardizedTransformer) => ({
        id: transformer.id,
        latitude: transformer.location.coordinates.lat,
        longitude: transformer.location.coordinates.lng,
        title: `${transformer.serialNumber}`,
        description: `${transformer.manufacturer} ${transformer.model} - ${transformer.capacity} kVA`,
        status: transformer.status,
        data: transformer
      }))

      // Calculate statistics
      const statusCounts = standardizedTransformers.reduce((acc, t) => {
        acc[t.status] = (acc[t.status] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      setTransformers(standardizedTransformers)
      setMapLocations(locations)
      setStats({
        total: standardizedTransformers.length,
        operational: statusCounts.operational || 0,
        warning: statusCounts.warning || 0,
        maintenance: statusCounts.maintenance || 0,
        critical: statusCounts.critical || 0,
        offline: statusCounts.offline || 0
      })

      console.log(`✅ Loaded ${standardizedTransformers.length} transformers on map`)

    } catch (err) {
      console.error('❌ Error loading transformers:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadTransformers()
  }, [])

  const handleMarkerClick = (location: MapLocation) => {
    const transformer = location.data as StandardizedTransformer
    setSelectedTransformer(transformer)
    setShowDetailDialog(true)
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'operational': return 'bg-green-100 text-green-800'
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      case 'maintenance': return 'bg-blue-100 text-blue-800'
      case 'critical': return 'bg-red-100 text-red-800'
      case 'offline': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'operational': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'maintenance': return <Wrench className="h-4 w-4 text-blue-600" />
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'offline': return <Clock className="h-4 w-4 text-gray-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Transformer Map</h1>
          <p className="text-gray-600 mt-1">Interactive map showing all transformers with detailed information</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={loadTransformers} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Zap className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Operational</p>
                <p className="text-2xl font-bold text-green-600">{stats.operational}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Warning</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.warning}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Maintenance</p>
                <p className="text-2xl font-bold text-blue-600">{stats.maintenance}</p>
              </div>
              <Wrench className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical</p>
                <p className="text-2xl font-bold text-red-600">{stats.critical}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Offline</p>
                <p className="text-2xl font-bold text-gray-600">{stats.offline}</p>
              </div>
              <Clock className="h-8 w-8 text-gray-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Info */}
      {loading && (
        <div className="p-4 bg-blue-50 rounded-lg text-sm">
          <div className="flex items-center">
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            Loading transformers...
          </div>
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-50 rounded-lg text-sm">
          <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 mr-2 text-red-600" />
            <strong>Error:</strong> {error}
          </div>
        </div>
      )}

      {!loading && !error && (
        <div className="p-4 bg-green-50 rounded-lg text-sm">
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
            <strong>Success:</strong> Loaded {stats.total} transformers on map. Click any marker for detailed information.
          </div>
        </div>
      )}

      {/* Map */}
      <Card>
        <CardContent className="p-0">
          <div style={{ height }}>
            <ErrorBoundary>
              <UnifiedMap
                locations={mapLocations}
                height={height}
                onMarkerClick={handleMarkerClick}
                clustered={true}
                showControls={showControls}
                showLegend={true}
                showFilters={true}
                interactive={true}
                allowFullscreen={allowFullscreen}
                showHeatmap={false}
                showSearch={true}
                showExport={true}
                showMeasurement={true}
                initialZoom={6}
              />
            </ErrorBoundary>
          </div>
        </CardContent>
      </Card>

      {/* Transformer Detail Dialog */}
      <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-blue-600" />
              <span>Transformer Details - {selectedTransformer?.serialNumber}</span>
            </DialogTitle>
            <DialogDescription>
              Complete information for {selectedTransformer?.manufacturer} {selectedTransformer?.model}
            </DialogDescription>
          </DialogHeader>

          {selectedTransformer && (
            <div className="space-y-6">
              {/* Status and Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(selectedTransformer.status)}
                  <Badge className={getStatusColor(selectedTransformer.status)}>
                    {selectedTransformer.status.toUpperCase()}
                  </Badge>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push(`/transformers/${selectedTransformer.id}`)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage
                  </Button>
                </div>
              </div>

              {/* Main Information Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Technical Specifications */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Technical Specifications</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Serial Number</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.serialNumber}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Capacity</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.capacity} kVA</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Manufacturer</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.manufacturer}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Model</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.model}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Primary Voltage</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.voltagePrimary}V</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Secondary Voltage</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.voltageSecondary}V</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Type</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.type}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Installation Date</label>
                        <p className="text-gray-900 font-medium">
                          {new Date(selectedTransformer.installationDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Location Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <MapPin className="h-5 w-5 mr-2" />
                      Location Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Location Name</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.location.name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Region</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.location.region}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">District</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.location.district}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Sub City</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.location.subCity}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Kebele</label>
                        <p className="text-gray-900 font-medium">{selectedTransformer.location.kebele}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">GPS Coordinates</label>
                        <p className="text-gray-900 font-medium">
                          {selectedTransformer.location.coordinates.lat.toFixed(6)}, {selectedTransformer.location.coordinates.lng.toFixed(6)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Performance Metrics */}
              {(selectedTransformer.temperature || selectedTransformer.loadFactor || selectedTransformer.healthIndex) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Gauge className="h-5 w-5 mr-2" />
                      Performance Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {selectedTransformer.temperature && (
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                          <Thermometer className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                          <div className="text-2xl font-bold text-blue-600">{selectedTransformer.temperature}°C</div>
                          <div className="text-sm text-gray-600">Temperature</div>
                        </div>
                      )}
                      {selectedTransformer.loadFactor && (
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                          <Gauge className="h-8 w-8 text-green-600 mx-auto mb-2" />
                          <div className="text-2xl font-bold text-green-600">{selectedTransformer.loadFactor}%</div>
                          <div className="text-sm text-gray-600">Load Factor</div>
                        </div>
                      )}
                      {selectedTransformer.healthIndex && (
                        <div className="text-center p-4 bg-purple-50 rounded-lg">
                          <CheckCircle className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                          <div className="text-2xl font-bold text-purple-600">{selectedTransformer.healthIndex}%</div>
                          <div className="text-sm text-gray-600">Health Index</div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Maintenance Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Wrench className="h-5 w-5 mr-2" />
                    Maintenance Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Last Maintenance</label>
                      <p className="text-gray-900 font-medium">
                        {selectedTransformer.lastMaintenance
                          ? new Date(selectedTransformer.lastMaintenance).toLocaleDateString()
                          : 'No record'
                        }
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Next Maintenance</label>
                      <p className="text-gray-900 font-medium">
                        {selectedTransformer.nextMaintenance
                          ? new Date(selectedTransformer.nextMaintenance).toLocaleDateString()
                          : 'Not scheduled'
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
