"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Textarea } from "@/src/components/ui/textarea"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Badge } from "@/src/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar"
import { Alert, useAlerts } from "@/src/contexts/alert-context"
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  MessageSquare,
  User,
  FileText,
  History,
  Thermometer,
  Battery,
  Zap,
  AlertCircle,
  UserCheck,
  MapPin,
  Link,
  Send,
  Bell,
  BarChart,
  Plus,
  Eye,
  ExternalLink
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/src/lib/utils"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Input } from "@/src/components/ui/input"

interface AlertDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  alertId: string
}

export function AlertDetailDialog({ open, onOpenChange, alertId }: AlertDetailDialogProps) {
  const router = useRouter()
  const { alerts, acknowledgeAlert, resolveAlert, assignAlert, updateAlert } = useAlerts()
  const [activeTab, setActiveTab] = useState("details")
  const [resolutionNote, setResolutionNote] = useState("")
  const [assignee, setAssignee] = useState("")
  const [newNote, setNewNote] = useState("")
  const [relatedAlerts, setRelatedAlerts] = useState<Alert[]>([])
  const [isAddingNote, setIsAddingNote] = useState(false)

  const alert = alerts.find(a => a.id === alertId)
  if (!alert) return null

  // Mock user ID for demo purposes
  const currentUserId = "Current User"

  // Navigate to transformer details page
  const navigateToTransformer = () => {
    // Get the transformer ID directly from the alert
    // The transformer field contains the full transformer ID (e.g., "tr-001")
    const transformerId = alert.transformer
    onOpenChange(false) // Close the dialog
    router.push(`/transformers/${transformerId}`)
  }

  // Find related alerts (same transformer)
  useEffect(() => {
    if (alert) {
      const related = alerts.filter(a =>
        a.transformer === alert.transformer && a.id !== alert.id
      ).slice(0, 5); // Get up to 5 related alerts
      setRelatedAlerts(related);
    }
  }, [alert, alerts]);

  // Get icon based on alert type
  const getAlertIcon = (type: string) => {
    switch (type) {
      case "Temperature": return Thermometer
      case "Oil Level": return Battery
      case "Load": return Zap
      case "Connection": return AlertCircle
      default: return AlertTriangle
    }
  }

  const AlertIcon = getAlertIcon(alert.type)

  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "Critical": return "bg-red-500 hover:bg-red-600"
      case "High": return "bg-orange-500 hover:bg-orange-600"
      case "Medium": return "bg-yellow-500 hover:bg-yellow-600"
      case "Low": return "bg-blue-500 hover:bg-blue-600"
      default: return "bg-slate-500 hover:bg-slate-600"
    }
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "border-red-500 text-red-500"
      case "Acknowledged": return "border-blue-500 text-blue-500"
      case "In Progress": return "border-yellow-500 text-yellow-500"
      case "Resolved": return "border-green-500 text-green-500"
      default: return "border-slate-500 text-slate-500"
    }
  }

  const handleAcknowledge = () => {
    acknowledgeAlert(alertId, currentUserId)
  }

  const handleResolve = () => {
    if (resolutionNote.trim()) {
      resolveAlert(alertId, currentUserId, resolutionNote)
      setResolutionNote("")
      onOpenChange(false)
    }
  }

  const handleAssign = () => {
    if (assignee) {
      assignAlert(alertId, assignee)
      setAssignee("")
    }
  }

  // Handle adding a new note
  const handleAddNote = () => {
    if (newNote.trim()) {
      const updatedNotes = [...(alert.notes || []), `${currentUserId}: ${newNote}`];
      updateAlert(alertId, { notes: updatedNotes });
      setNewNote("");
      setIsAddingNote(false);
    }
  }

  // Mock users for assignment
  const users = [
    { id: "user1", name: "John Doe", role: "Technician" },
    { id: "user2", name: "Jane Smith", role: "Engineer" },
    { id: "user3", name: "Mike Johnson", role: "Supervisor" },
    { id: "team1", name: "Tech Team Alpha", role: "Maintenance Team" },
    { id: "team2", name: "Field Crew Beta", role: "Field Team" }
  ]

  // Mock transformer location data
  const transformerLocation = {
    latitude: 9.0222,
    longitude: 38.7468,
    address: "Addis Ababa, Ethiopia"
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertIcon className={cn(
                "h-5 w-5",
                alert.severity === "Critical" ? "text-red-500" :
                alert.severity === "High" ? "text-orange-500" :
                alert.severity === "Medium" ? "text-yellow-500" : "text-blue-500"
              )} />
              <DialogTitle className="text-xl">
                Alert {alert.id}
              </DialogTitle>
              <Badge className={getSeverityColor(alert.severity)}>
                {alert.severity}
              </Badge>
              <Badge variant="outline" className={getStatusColor(alert.status)}>
                {alert.status}
              </Badge>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={navigateToTransformer}
              className="gap-1"
            >
              <ExternalLink className="h-4 w-4" />
              View Transformer
            </Button>
          </div>
          <DialogDescription className="text-base font-medium pt-1">
            {alert.message}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-2">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="details">
              <FileText className="mr-2 h-4 w-4" />
              Details
            </TabsTrigger>
            <TabsTrigger value="notes">
              <MessageSquare className="mr-2 h-4 w-4" />
              Notes & Activity
            </TabsTrigger>
            <TabsTrigger value="location">
              <MapPin className="mr-2 h-4 w-4" />
              Location
            </TabsTrigger>
            <TabsTrigger value="related">
              <Link className="mr-2 h-4 w-4" />
              Related
            </TabsTrigger>
            <TabsTrigger value="actions">
              <CheckCircle className="mr-2 h-4 w-4" />
              Actions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Transformer</div>
                <div className="font-medium flex items-center gap-2">
                  {alert.transformer}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={navigateToTransformer}
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    View
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Location</div>
                <div className="font-medium">{alert.location}</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Alert Type</div>
                <div className="font-medium">{alert.type}</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Timestamp</div>
                <div className="font-medium">{alert.timestamp}</div>
              </div>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Severity</div>
                <div className="font-medium">
                  <Badge className={getSeverityColor(alert.severity)}>
                    {alert.severity}
                  </Badge>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Status</div>
                <div className="font-medium">
                  <Badge variant="outline" className={getStatusColor(alert.status)}>
                    {alert.status}
                  </Badge>
                </div>
              </div>
              {alert.assignedTo && (
                <div className="space-y-2 col-span-2">
                  <div className="text-sm text-muted-foreground">Assigned To</div>
                  <div className="font-medium flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback>{alert.assignedTo[0]}</AvatarFallback>
                    </Avatar>
                    {alert.assignedTo}
                  </div>
                </div>
              )}

              <div className="col-span-2 mt-4">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={navigateToTransformer}
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Go to Transformer Details
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="notes" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium">Activity Timeline</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsAddingNote(!isAddingNote)}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Add Note
                </Button>
              </div>

              {isAddingNote && (
                <div className="flex gap-2 items-center">
                  <Input
                    placeholder="Add a note..."
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    className="flex-1"
                  />
                  <Button size="sm" onClick={handleAddNote} disabled={!newNote.trim()}>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              )}

              <div className="space-y-4">
                {/* Created */}
                <div className="flex gap-3">
                  <div className="flex flex-col items-center">
                    <div className="rounded-full bg-blue-100 p-1 dark:bg-blue-900/20">
                      <AlertTriangle className="h-4 w-4 text-blue-500" />
                    </div>
                    <div className="flex-grow w-px bg-border mt-1"></div>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Alert Created</p>
                    <p className="text-xs text-muted-foreground">{alert.timestamp}</p>
                    <p className="text-sm mt-1">{alert.message}</p>
                  </div>
                </div>

                {/* Acknowledged */}
                {alert.acknowledgedBy && (
                  <div className="flex gap-3">
                    <div className="flex flex-col items-center">
                      <div className="rounded-full bg-blue-100 p-1 dark:bg-blue-900/20">
                        <UserCheck className="h-4 w-4 text-blue-500" />
                      </div>
                      <div className="flex-grow w-px bg-border mt-1"></div>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Alert Acknowledged</p>
                      <p className="text-xs text-muted-foreground">{alert.acknowledgedAt}</p>
                      <p className="text-sm mt-1">Acknowledged by {alert.acknowledgedBy}</p>
                    </div>
                  </div>
                )}

                {/* Assigned */}
                {alert.assignedTo && (
                  <div className="flex gap-3">
                    <div className="flex flex-col items-center">
                      <div className="rounded-full bg-yellow-100 p-1 dark:bg-yellow-900/20">
                        <User className="h-4 w-4 text-yellow-500" />
                      </div>
                      <div className="flex-grow w-px bg-border mt-1"></div>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Alert Assigned</p>
                      <p className="text-sm mt-1">Assigned to {alert.assignedTo}</p>
                    </div>
                  </div>
                )}

                {/* Resolved */}
                {alert.resolvedBy && (
                  <div className="flex gap-3">
                    <div className="flex flex-col items-center">
                      <div className="rounded-full bg-green-100 p-1 dark:bg-green-900/20">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Alert Resolved</p>
                      <p className="text-xs text-muted-foreground">{alert.resolvedAt}</p>
                      <p className="text-sm mt-1">Resolved by {alert.resolvedBy}</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Notes */}
              <h4 className="text-sm font-medium mt-6">Notes</h4>
              {alert.notes && alert.notes.length > 0 ? (
                <div className="space-y-3">
                  {alert.notes.map((note, index) => (
                    <div key={index} className="bg-muted p-3 rounded-md">
                      <p className="text-sm">{note}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No notes available</p>
              )}
            </div>
          </TabsContent>

          <TabsContent value="location" className="space-y-4 mt-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Transformer Location</h4>
              <div className="bg-muted rounded-md p-4 flex flex-col items-center">
                <div className="w-full h-[250px] bg-slate-200 rounded-md mb-4 flex items-center justify-center relative">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <MapPin className="h-8 w-8 text-red-500" />
                  </div>
                  <p className="text-sm text-muted-foreground">Map View</p>
                </div>
                <div className="w-full space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Transformer:</span>
                    <span className="text-sm">{alert.transformer}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Location:</span>
                    <span className="text-sm">{alert.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Coordinates:</span>
                    <span className="text-sm">{transformerLocation.latitude}, {transformerLocation.longitude}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Address:</span>
                    <span className="text-sm">{transformerLocation.address}</span>
                  </div>
                </div>
                <div className="flex gap-2 mt-4 w-full">
                  <Button className="flex-1" variant="outline">
                    <MapPin className="mr-2 h-4 w-4" />
                    View Full Map
                  </Button>
                  <Button
                    className="flex-1"
                    variant="outline"
                    onClick={navigateToTransformer}
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    View Transformer
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="related" className="space-y-4 mt-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Related Alerts</h4>
              {relatedAlerts.length > 0 ? (
                <div className="space-y-3">
                  {relatedAlerts.map((relatedAlert) => {
                    const RelatedAlertIcon = getAlertIcon(relatedAlert.type);
                    return (
                      <div key={relatedAlert.id} className="border rounded-md p-3 hover:bg-muted transition-colors">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-2">
                            <RelatedAlertIcon className={cn(
                              "h-5 w-5 mt-0.5",
                              relatedAlert.severity === "Critical" ? "text-red-500" :
                              relatedAlert.severity === "High" ? "text-orange-500" :
                              relatedAlert.severity === "Medium" ? "text-yellow-500" : "text-blue-500"
                            )} />
                            <div>
                              <div className="flex items-center gap-2">
                                <p className="text-sm font-medium">{relatedAlert.id}</p>
                                <Badge className={getSeverityColor(relatedAlert.severity)}>
                                  {relatedAlert.severity}
                                </Badge>
                                <Badge variant="outline" className={getStatusColor(relatedAlert.status)}>
                                  {relatedAlert.status}
                                </Badge>
                              </div>
                              <p className="text-sm">{relatedAlert.message}</p>
                              <p className="text-xs text-muted-foreground mt-1">{relatedAlert.timestamp}</p>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => {
                              onOpenChange(false);
                              // Small delay to avoid dialog animation issues
                              setTimeout(() => {
                                onOpenChange(true);
                              }, 100);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No related alerts found</p>
              )}

              <div className="mt-4">
                <h4 className="text-sm font-medium">Transformer History</h4>
                <div className="bg-muted rounded-md p-4 mt-2">
                  <div className="flex items-center justify-between mb-4">
                    <p className="text-sm font-medium">Alert Frequency</p>
                    <Select defaultValue="month">
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="Select period" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="week">Last Week</SelectItem>
                        <SelectItem value="month">Last Month</SelectItem>
                        <SelectItem value="quarter">Last Quarter</SelectItem>
                        <SelectItem value="year">Last Year</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="h-[150px] flex items-end justify-between gap-2">
                    <div className="flex-1 bg-blue-100 dark:bg-blue-900/20 h-[30%] rounded-t-sm"></div>
                    <div className="flex-1 bg-blue-100 dark:bg-blue-900/20 h-[50%] rounded-t-sm"></div>
                    <div className="flex-1 bg-blue-100 dark:bg-blue-900/20 h-[70%] rounded-t-sm"></div>
                    <div className="flex-1 bg-blue-100 dark:bg-blue-900/20 h-[40%] rounded-t-sm"></div>
                    <div className="flex-1 bg-blue-100 dark:bg-blue-900/20 h-[90%] rounded-t-sm"></div>
                    <div className="flex-1 bg-blue-100 dark:bg-blue-900/20 h-[60%] rounded-t-sm"></div>
                    <div className="flex-1 bg-blue-100 dark:bg-blue-900/20 h-[20%] rounded-t-sm"></div>
                  </div>
                  <div className="flex justify-between mt-2">
                    <span className="text-xs text-muted-foreground">Temperature</span>
                    <span className="text-xs text-muted-foreground">Oil Level</span>
                    <span className="text-xs text-muted-foreground">Load</span>
                    <span className="text-xs text-muted-foreground">Connection</span>
                    <span className="text-xs text-muted-foreground">Voltage</span>
                    <span className="text-xs text-muted-foreground">Physical</span>
                    <span className="text-xs text-muted-foreground">Other</span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="actions" className="space-y-4 mt-4">
            {alert.status !== "Resolved" && (
              <>
                {alert.status === "Active" && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Acknowledge Alert</h4>
                    <p className="text-sm text-muted-foreground">
                      Acknowledge this alert to indicate you are aware of the issue.
                    </p>
                    <Button onClick={handleAcknowledge} className="mt-2">
                      <UserCheck className="mr-2 h-4 w-4" />
                      Acknowledge Alert
                    </Button>
                  </div>
                )}

                <div className="space-y-2 pt-4 border-t">
                  <h4 className="text-sm font-medium">Assign Alert</h4>
                  <p className="text-sm text-muted-foreground">
                    Assign this alert to a user or team for resolution.
                  </p>
                  <div className="flex gap-2 mt-2">
                    <Select value={assignee} onValueChange={setAssignee}>
                      <SelectTrigger className="w-[250px]">
                        <SelectValue placeholder="Select assignee" />
                      </SelectTrigger>
                      <SelectContent>
                        {users.map(user => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.name} ({user.role})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button onClick={handleAssign} disabled={!assignee}>
                      Assign
                    </Button>
                  </div>
                </div>

                <div className="space-y-2 pt-4 border-t">
                  <h4 className="text-sm font-medium">Schedule Maintenance</h4>
                  <p className="text-sm text-muted-foreground">
                    Schedule maintenance for this transformer.
                  </p>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <div className="space-y-1">
                      <p className="text-xs text-muted-foreground">Priority</p>
                      <Select defaultValue="medium">
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="critical">Critical</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-1">
                      <p className="text-xs text-muted-foreground">Type</p>
                      <Select defaultValue="corrective">
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="preventive">Preventive</SelectItem>
                          <SelectItem value="corrective">Corrective</SelectItem>
                          <SelectItem value="emergency">Emergency</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <Button className="mt-2 w-full">
                    <Clock className="mr-2 h-4 w-4" />
                    Schedule Maintenance
                  </Button>
                </div>

                <div className="space-y-2 pt-4 border-t">
                  <h4 className="text-sm font-medium">Escalate Alert</h4>
                  <p className="text-sm text-muted-foreground">
                    Escalate this alert to higher management or specialized team.
                  </p>
                  <div className="flex gap-2 mt-2">
                    <Select defaultValue="supervisor">
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select escalation level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="supervisor">Supervisor</SelectItem>
                        <SelectItem value="manager">Regional Manager</SelectItem>
                        <SelectItem value="specialist">Technical Specialist</SelectItem>
                        <SelectItem value="emergency">Emergency Response Team</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button variant="outline" className="mt-2 w-full">
                    <Bell className="mr-2 h-4 w-4" />
                    Escalate Alert
                  </Button>
                </div>

                <div className="space-y-2 pt-4 border-t">
                  <h4 className="text-sm font-medium">Resolve Alert</h4>
                  <p className="text-sm text-muted-foreground">
                    Mark this alert as resolved and provide resolution details.
                  </p>
                  <Textarea
                    placeholder="Enter resolution details..."
                    value={resolutionNote}
                    onChange={(e) => setResolutionNote(e.target.value)}
                    className="mt-2"
                  />
                  <Button
                    onClick={handleResolve}
                    disabled={!resolutionNote.trim()}
                    className="mt-2 w-full"
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Resolve Alert
                  </Button>
                </div>
              </>
            )}

            {alert.status === "Resolved" && (
              <div className="space-y-6">
                <div className="flex items-center justify-center h-32">
                  <div className="text-center">
                    <CheckCircle className="h-10 w-10 text-green-500 mx-auto mb-2" />
                    <h3 className="text-lg font-medium">Alert Resolved</h3>
                    <p className="text-sm text-muted-foreground">
                      This alert has been resolved on {alert.resolvedAt} by {alert.resolvedBy}.
                    </p>
                  </div>
                </div>

                <div className="space-y-2 border-t pt-4">
                  <h4 className="text-sm font-medium">Reopen Alert</h4>
                  <p className="text-sm text-muted-foreground">
                    If the issue has reoccurred, you can reopen this alert.
                  </p>
                  <Button variant="outline" className="mt-2 w-full">
                    <AlertTriangle className="mr-2 h-4 w-4" />
                    Reopen Alert
                  </Button>
                </div>

                <div className="space-y-2 border-t pt-4">
                  <h4 className="text-sm font-medium">Create Similar Alert</h4>
                  <p className="text-sm text-muted-foreground">
                    Create a new alert based on this one.
                  </p>
                  <Button variant="outline" className="mt-2 w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Similar Alert
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
