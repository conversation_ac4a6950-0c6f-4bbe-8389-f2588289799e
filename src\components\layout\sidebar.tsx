"use client"

import type React from "react"

import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import {
  LayoutDashboard,
  Zap,
  ClipboardList,
  BarChart3,
  Settings,
  Users,
  AlertTriangle,
  Wifi,
  Shield,
  Search,
  Package,
  ChevronDown,
  FileText,
  TrendingUp,
  Bell,
  Wrench,
  MapPin,
  Calendar,
  Activity,
  PieChart,
  Building
} from "lucide-react"

import { cn } from "@/src/lib/utils"
import { useSidebar } from "@/src/components/layout/sidebar-provider"
import { Button } from "@/src/components/ui/button"
import { useAuth } from "@/src/features/auth/context/auth-context"
import { useState, useEffect } from "react"
// import type { UserRole } from "@/src/types/auth" // Commented out - type not found

interface NavSubItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  allowedRoles: string[]
  requiredPermissions?: Array<{
    resource: string
    action: string
  }>
}

interface NavItem {
  title: string
  href?: string
  icon: React.ComponentType<{ className?: string }>
  allowedRoles: string[]
  requiredPermissions?: Array<{
    resource: string
    action: string
  }>
  subItems?: NavSubItem[]
}

const navItems: NavItem[] = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    allowedRoles: [
      "super_admin",
      "national_asset_manager",
      "national_maintenance_manager",
      "regional_admin",
      "regional_asset_manager",
      "regional_maintenance_engineer",
      "service_center_manager",
      "field_technician",
      "customer_service_agent",
      "audit_compliance_officer",
    ],
    subItems: [
      {
        title: "Overview",
        href: "/dashboard",
        icon: BarChart3,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
          "customer_service_agent",
          "audit_compliance_officer",
        ],
      },
      {
        title: "Analytics",
        href: "/dashboard?tab=analytics",
        icon: TrendingUp,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
    ],
  },
  {
    title: "Transformers",
    icon: Zap,
    allowedRoles: [
      "super_admin",
      "national_asset_manager",
      "national_maintenance_manager",
      "regional_admin",
      "regional_asset_manager",
      "regional_maintenance_engineer",
      "service_center_manager",
      "field_technician",
    ],
    requiredPermissions: [{ resource: "transformers", action: "read" }],
    subItems: [
      {
        title: "Overview",
        href: "/transformers",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Unified Management",
        href: "/transformers/unified-management",
        icon: Settings,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Map View",
        href: "/transformers/map",
        icon: MapPin,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Analytics",
        href: "/transformers?tab=analytics",
        icon: BarChart3,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
      {
        title: "History & Records",
        href: "/transformers?tab=history",
        icon: FileText,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
    ],
  },
  {
    title: "Maintenance",
    icon: Wrench,
    allowedRoles: [
      "super_admin",
      "national_maintenance_manager",
      "regional_admin",
      "regional_maintenance_engineer",
      "service_center_manager",
      "field_technician",
    ],
    requiredPermissions: [{ resource: "maintenance", action: "read" }],
    subItems: [
      {
        title: "Overview",
        href: "/maintenance",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Scheduled Tasks",
        href: "/maintenance?tab=scheduled",
        icon: Calendar,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Work Orders",
        href: "/maintenance?tab=workorders",
        icon: ClipboardList,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Preventive Maintenance",
        href: "/maintenance?tab=preventive",
        icon: Shield,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
      {
        title: "Inspections",
        href: "/maintenance?tab=inspections",
        icon: Search,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Parts & Inventory",
        href: "/maintenance?tab=inventory",
        icon: Package,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
      {
        title: "Technicians",
        href: "/maintenance?tab=technicians",
        icon: Users,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
    ],
  },
  {
    title: "Smart Meters",
    icon: Wifi,
    allowedRoles: [
      "super_admin",
      "national_asset_manager",
      "regional_admin",
      "regional_asset_manager",
      "service_center_manager",
    ],
    subItems: [
      {
        title: "Overview",
        href: "/smart-meters",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "regional_admin",
          "regional_asset_manager",
          "service_center_manager",
        ],
      },
      {
        title: "Real-time Monitoring",
        href: "/smart-meters?tab=monitoring",
        icon: Activity,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "regional_admin",
          "regional_asset_manager",
          "service_center_manager",
        ],
      },
      {
        title: "Data Analytics",
        href: "/smart-meters?tab=analytics",
        icon: TrendingUp,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "regional_admin",
          "regional_asset_manager",
        ],
      },
      {
        title: "Usage Reports",
        href: "/smart-meters?tab=reports",
        icon: FileText,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "regional_admin",
          "regional_asset_manager",
          "service_center_manager",
        ],
      },
    ],
  },
  {
    title: "Reports",
    icon: BarChart3,
    allowedRoles: [
      "super_admin",
      "national_asset_manager",
      "national_maintenance_manager",
      "regional_admin",
      "regional_asset_manager",
      "regional_maintenance_engineer",
      "service_center_manager",
      "audit_compliance_officer",
    ],
    subItems: [
      {
        title: "Overview",
        href: "/reports",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "audit_compliance_officer",
        ],
      },
      {
        title: "Distribution Analysis",
        href: "/reports?tab=distribution",
        icon: PieChart,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
      {
        title: "Regional Analysis",
        href: "/reports?tab=regional",
        icon: MapPin,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
      {
        title: "Infrastructure Reports",
        href: "/reports?tab=infrastructure",
        icon: Building,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
      {
        title: "Comprehensive Reports",
        href: "/reports?tab=comprehensive",
        icon: FileText,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "audit_compliance_officer",
        ],
      },
      {
        title: "Performance Reports",
        href: "/reports?tab=performance",
        icon: TrendingUp,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
      {
        title: "Maintenance Reports",
        href: "/reports?tab=maintenance",
        icon: Wrench,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "audit_compliance_officer",
        ],
      },
    ],
  },
  {
    title: "Alerts",
    icon: Bell,
    allowedRoles: [
      "super_admin",
      "national_maintenance_manager",
      "regional_admin",
      "regional_maintenance_engineer",
      "service_center_manager",
      "field_technician",
    ],
    subItems: [
      {
        title: "Overview",
        href: "/alerts",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Active Alerts",
        href: "/alerts?tab=active",
        icon: AlertTriangle,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Critical Alerts",
        href: "/alerts?tab=critical",
        icon: Shield,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Alert History",
        href: "/alerts?tab=history",
        icon: FileText,
        allowedRoles: [
          "super_admin",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
    ],
  },
  {
    title: "Settings",
    icon: Settings,
    allowedRoles: ["super_admin", "national_asset_manager", "regional_admin"],
    subItems: [
      {
        title: "Overview",
        href: "/settings",
        icon: LayoutDashboard,
        allowedRoles: ["super_admin", "national_asset_manager", "regional_admin"],
      },
      {
        title: "User Management",
        href: "/settings?tab=users",
        icon: Users,
        allowedRoles: ["super_admin", "regional_admin"],
      },
      {
        title: "System Configuration",
        href: "/settings?tab=system",
        icon: Shield,
        allowedRoles: ["super_admin", "national_asset_manager", "regional_admin"],
      },
      {
        title: "Roles & Permissions",
        href: "/settings?tab=roles",
        icon: Users,
        allowedRoles: ["super_admin", "regional_admin"],
      },
      {
        title: "Regions & Centers",
        href: "/settings?tab=regions",
        icon: MapPin,
        allowedRoles: ["super_admin", "national_asset_manager", "regional_admin"],
      },
    ],
  },
  {
    title: "Performance",
    icon: TrendingUp,
    allowedRoles: [
      "super_admin",
      "national_asset_manager",
      "national_maintenance_manager",
      "regional_admin",
      "regional_asset_manager",
      "regional_maintenance_engineer",
      "service_center_manager",
    ],
    subItems: [
      {
        title: "Overview",
        href: "/performance",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
      {
        title: "Real-time Monitoring",
        href: "/performance?tab=monitoring",
        icon: Activity,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
        ],
      },
    ],
  },
  {
    title: "Weather",
    icon: Activity,
    allowedRoles: [
      "super_admin",
      "national_asset_manager",
      "national_maintenance_manager",
      "regional_admin",
      "regional_asset_manager",
      "regional_maintenance_engineer",
      "service_center_manager",
      "field_technician",
    ],
    subItems: [
      {
        title: "Overview",
        href: "/weather",
        icon: LayoutDashboard,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
      {
        title: "Weather Impact",
        href: "/weather?tab=impact",
        icon: AlertTriangle,
        allowedRoles: [
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_asset_manager",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician",
        ],
      },
    ],
  },
]

export function Sidebar() {
  const pathname = usePathname()
  const { isOpen } = useSidebar()
  const { user, hasRole, hasPermission } = useAuth()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  if (!isOpen || !user) {
    return null
  }

  // Filter nav items based on user role and permissions
  const filteredNavItems = navItems.filter((item) => {
    // Check if user has the required role
    const hasRequiredRole = hasRole(item.allowedRoles)

    // Check if user has all required permissions
    const hasRequiredPermissions = item.requiredPermissions
      ? item.requiredPermissions.every(({ resource, action }) => hasPermission(resource, action))
      : true

    return hasRequiredRole && hasRequiredPermissions
  })

  // Filter sub-items based on user role and permissions
  const filterSubItems = (subItems: NavSubItem[] | undefined) => {
    if (!subItems) return []
    return subItems.filter((subItem) => {
      const hasRequiredRole = hasRole(subItem.allowedRoles)
      const hasRequiredPermissions = subItem.requiredPermissions
        ? subItem.requiredPermissions.every(({ resource, action }) => hasPermission(resource, action))
        : true
      return hasRequiredRole && hasRequiredPermissions
    })
  }

  // Toggle expanded state for main items
  const toggleExpanded = (title: string) => {
    setExpandedItems(prev =>
      prev.includes(title)
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  // Check if current path matches any sub-item (considering query parameters)
  const isSubItemActive = (subItems: NavSubItem[] | undefined) => {
    if (!subItems) return false
    return subItems.some(subItem => {
      const [subPath, subQuery] = subItem.href.split('?')
      return pathname === subPath && (subQuery ? window.location.search.includes(subQuery) : true)
    })
  }

  // Check if main item is active (either direct match or sub-item match)
  const isMainItemActive = (item: NavItem) => {
    if (item.href) {
      const [itemPath] = item.href.split('?')
      return pathname === itemPath
    }
    return false
  }

  // Auto-expand sections that contain the current active page
  useEffect(() => {
    filteredNavItems.forEach((item) => {
      if (item.subItems && (isMainItemActive(item) || isSubItemActive(item.subItems))) {
        setExpandedItems(prev =>
          prev.includes(item.title) ? prev : [...prev, item.title]
        )
      }
    })
  }, [pathname, filteredNavItems])

  return (
    <div className="fixed inset-y-0 left-0 z-40 flex w-64 flex-col bg-gradient-to-b from-white to-gray-50 border-r border-gray-200 shadow-lg md:relative">
      <div className="flex h-16 items-center px-4 border-b border-gray-100 bg-white/80 backdrop-blur-sm">
        <Link href="/dashboard" className="flex items-center gap-3 group">
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-green-100 to-green-200 shadow-sm group-hover:shadow-md transition-all duration-200">
            <Image
              src="/eeu-logo.svg"
              alt="Ethiopian Electric Utility Logo"
              width={20}
              height={20}
              className="h-5 w-5"
            />
          </div>
          <div className="flex flex-col">
            <span className="font-bold text-gray-900 text-sm tracking-tight">EEU-DTMS</span>
            <span className="text-xs text-gray-500 font-medium">Ethiopian Electric Utility</span>
          </div>
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-3">
        <nav className="space-y-2 px-3">
          {filteredNavItems.map((item) => {
            const filteredSubItems = filterSubItems(item.subItems)
            const hasSubItems = filteredSubItems.length > 0
            const isExpanded = expandedItems.includes(item.title)
            const isActive = isMainItemActive(item) || isSubItemActive(item.subItems)

            return (
              <div key={item.title} className="group">
                {/* Main Navigation Item */}
                {item.href ? (
                  // Direct link item
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full flex h-11 justify-start gap-3 text-gray-600 hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100 hover:text-gray-900 rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md",
                      isActive ? "bg-gradient-to-r from-green-50 to-green-100 text-green-700 shadow-md border border-green-200" : "",
                    )}
                    asChild
                  >
                    <Link href={item.href}>
                      <div className={cn(
                        "flex h-6 w-6 items-center justify-center rounded-lg transition-colors duration-200",
                        isActive ? "bg-green-200 text-green-700" : "bg-gray-100 text-gray-500 group-hover:bg-green-200 group-hover:text-green-600"
                      )}>
                        <item.icon className="h-4 w-4" />
                      </div>
                      {item.title}
                    </Link>
                  </Button>
                ) : (
                  // Expandable item
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full flex h-11 justify-start gap-3 text-gray-600 hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100 hover:text-gray-900 rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md",
                      isActive ? "bg-gradient-to-r from-green-50 to-green-100 text-green-700 shadow-md border border-green-200" : "",
                    )}
                    onClick={() => hasSubItems && toggleExpanded(item.title)}
                  >
                    <div className={cn(
                      "flex h-6 w-6 items-center justify-center rounded-lg transition-colors duration-200",
                      isActive ? "bg-green-200 text-green-700" : "bg-gray-100 text-gray-500 group-hover:bg-green-200 group-hover:text-green-600"
                    )}>
                      <item.icon className="h-4 w-4" />
                    </div>
                    <span className="flex-1 text-left">{item.title}</span>
                    {hasSubItems && (
                      <div className={cn(
                        "flex h-5 w-5 items-center justify-center rounded transition-transform duration-200",
                        isExpanded ? "rotate-180" : "rotate-0"
                      )}>
                        <ChevronDown className="h-3 w-3 text-gray-400" />
                      </div>
                    )}
                  </Button>
                )}

                {/* Sub Items */}
                {hasSubItems && isExpanded && (
                  <div className="ml-8 mt-2 space-y-1 border-l-2 border-green-100 pl-3">
                    {filteredSubItems.map((subItem) => {
                      const [subPath, subQuery] = subItem.href.split('?')
                      const isSubActive = pathname === subPath && (subQuery ? window.location.search.includes(subQuery) : false)

                      return (
                        <Button
                          key={subItem.href}
                          variant="ghost"
                          className={cn(
                            "w-full flex h-9 justify-start gap-3 text-gray-500 hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100 hover:text-gray-700 text-sm rounded-lg font-medium transition-all duration-200 group",
                            isSubActive ? "bg-gradient-to-r from-green-100 to-green-200 text-green-700 shadow-sm border border-green-300" : "",
                          )}
                          asChild
                        >
                          <Link href={subItem.href}>
                            <div className={cn(
                              "flex h-5 w-5 items-center justify-center rounded transition-colors duration-200",
                              isSubActive ? "bg-green-300 text-green-800" : "bg-gray-200 text-gray-500 group-hover:bg-green-300 group-hover:text-green-700"
                            )}>
                              <subItem.icon className="h-3 w-3" />
                            </div>
                            {subItem.title}
                          </Link>
                        </Button>
                      )
                    })}
                  </div>
                )}
              </div>
            )
          })}
        </nav>
      </div>
      <div className="border-t border-gray-100 p-4 bg-white/50 backdrop-blur-sm">
        <div className="flex items-center gap-3 rounded-xl bg-gradient-to-r from-green-50 to-green-100 px-4 py-3 shadow-sm border border-green-200">
          <div className="flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-green-200 to-green-300 shadow-sm">
            <Image
              src="/eeu-logo.svg"
              alt="Ethiopian Electric Utility Logo"
              width={16}
              height={16}
              className="h-4 w-4"
            />
          </div>
          <div>
            <p className="text-sm font-bold text-green-800 tracking-tight">EEU-DTMS v2.0</p>
            <p className="text-xs text-green-600 font-medium">Ethiopian Electric Utility</p>
          </div>
        </div>
        <div className="mt-3 text-center">
          <p className="text-xs text-gray-500 font-medium">System developed by</p>
          <p className="text-xs text-gray-600 font-semibold">Worku Mesafint</p>
        </div>
      </div>
    </div>
  )
}
