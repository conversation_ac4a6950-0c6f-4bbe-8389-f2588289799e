/**
 * Customization Configuration
 * Centralized configuration for UI customization, themes, and branding
 */

export interface CustomizationConfig {
  branding: {
    companyName: string
    companyShortName: string
    logo: {
      light: string
      dark: string
      favicon: string
      sizes: Record<string, string>
    }
    colors: {
      primary: string
      secondary: string
      accent: string
      success: string
      warning: string
      error: string
      info: string
    }
    fonts: {
      primary: string
      secondary: string
      mono: string
    }
  }
  
  themes: {
    default: string
    available: ThemeDefinition[]
  }
  
  layout: {
    sidebar: {
      width: {
        expanded: number
        collapsed: number
      }
      defaultCollapsed: boolean
      position: 'left' | 'right'
      variant: 'default' | 'floating' | 'minimal'
    }
    header: {
      height: number
      sticky: boolean
      variant: 'default' | 'minimal' | 'compact'
    }
    footer: {
      height: number
      show: boolean
      variant: 'default' | 'minimal'
    }
    container: {
      maxWidth: string
      padding: number
    }
  }
  
  components: {
    buttons: {
      defaultSize: 'sm' | 'md' | 'lg'
      defaultVariant: 'default' | 'primary' | 'secondary' | 'outline' | 'ghost'
      borderRadius: number
    }
    cards: {
      defaultShadow: 'none' | 'sm' | 'md' | 'lg' | 'xl'
      borderRadius: number
      padding: number
    }
    forms: {
      defaultSize: 'sm' | 'md' | 'lg'
      borderRadius: number
      focusRing: boolean
    }
    tables: {
      striped: boolean
      hover: boolean
      compact: boolean
      borderRadius: number
    }
  }
  
  dashboard: {
    widgets: {
      defaultSpacing: number
      borderRadius: number
      shadow: string
      animation: boolean
    }
    charts: {
      defaultColors: string[]
      animations: boolean
      responsive: boolean
      theme: 'light' | 'dark' | 'auto'
    }
    layout: {
      columns: number
      gap: number
      minWidgetHeight: number
    }
  }
  
  animations: {
    enabled: boolean
    duration: {
      fast: number
      normal: number
      slow: number
    }
    easing: {
      default: string
      bounce: string
      elastic: string
    }
  }
  
  accessibility: {
    highContrast: boolean
    reducedMotion: boolean
    fontSize: 'small' | 'medium' | 'large' | 'xl'
    focusVisible: boolean
  }
}

export interface ThemeDefinition {
  id: string
  name: string
  description?: string
  colors: {
    background: string
    foreground: string
    card: string
    cardForeground: string
    popover: string
    popoverForeground: string
    primary: string
    primaryForeground: string
    secondary: string
    secondaryForeground: string
    muted: string
    mutedForeground: string
    accent: string
    accentForeground: string
    destructive: string
    destructiveForeground: string
    border: string
    input: string
    ring: string
  }
  shadows: {
    sm: string
    md: string
    lg: string
    xl: string
  }
  borderRadius: {
    sm: string
    md: string
    lg: string
    xl: string
  }
}

// Default EEU customization configuration
export const DEFAULT_CUSTOMIZATION: CustomizationConfig = {
  branding: {
    companyName: 'Ethiopian Electric Utility',
    companyShortName: 'EEU',
    logo: {
      light: '/images/eeu-logo-light.svg',
      dark: '/images/eeu-logo-dark.svg',
      favicon: '/favicon.ico',
      sizes: {
        sm: '/images/eeu-logo-sm.svg',
        md: '/images/eeu-logo-md.svg',
        lg: '/images/eeu-logo-lg.svg',
        xl: '/images/eeu-logo-xl.svg'
      }
    },
    colors: {
      primary: '#10B981',      // Green
      secondary: '#3B82F6',    // Blue
      accent: '#8B5CF6',       // Purple
      success: '#10B981',      // Green
      warning: '#F59E0B',      // Amber
      error: '#EF4444',        // Red
      info: '#3B82F6'          // Blue
    },
    fonts: {
      primary: 'Inter, system-ui, sans-serif',
      secondary: 'Inter, system-ui, sans-serif',
      mono: 'JetBrains Mono, Consolas, monospace'
    }
  },
  
  themes: {
    default: 'light',
    available: [
      {
        id: 'light',
        name: 'Light',
        description: 'Clean and bright theme',
        colors: {
          background: '#ffffff',
          foreground: '#0f172a',
          card: '#ffffff',
          cardForeground: '#0f172a',
          popover: '#ffffff',
          popoverForeground: '#0f172a',
          primary: '#10B981',
          primaryForeground: '#ffffff',
          secondary: '#f1f5f9',
          secondaryForeground: '#0f172a',
          muted: '#f1f5f9',
          mutedForeground: '#64748b',
          accent: '#f1f5f9',
          accentForeground: '#0f172a',
          destructive: '#ef4444',
          destructiveForeground: '#ffffff',
          border: '#e2e8f0',
          input: '#e2e8f0',
          ring: '#10B981'
        },
        shadows: {
          sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
          md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
          lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
          xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)'
        },
        borderRadius: {
          sm: '0.375rem',
          md: '0.5rem',
          lg: '0.75rem',
          xl: '1rem'
        }
      },
      {
        id: 'dark',
        name: 'Dark',
        description: 'Modern dark theme',
        colors: {
          background: '#0f172a',
          foreground: '#f8fafc',
          card: '#1e293b',
          cardForeground: '#f8fafc',
          popover: '#1e293b',
          popoverForeground: '#f8fafc',
          primary: '#10B981',
          primaryForeground: '#ffffff',
          secondary: '#1e293b',
          secondaryForeground: '#f8fafc',
          muted: '#1e293b',
          mutedForeground: '#94a3b8',
          accent: '#1e293b',
          accentForeground: '#f8fafc',
          destructive: '#ef4444',
          destructiveForeground: '#ffffff',
          border: '#334155',
          input: '#334155',
          ring: '#10B981'
        },
        shadows: {
          sm: '0 1px 2px 0 rgb(0 0 0 / 0.3)',
          md: '0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)',
          lg: '0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3)',
          xl: '0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3)'
        },
        borderRadius: {
          sm: '0.375rem',
          md: '0.5rem',
          lg: '0.75rem',
          xl: '1rem'
        }
      }
    ]
  },
  
  layout: {
    sidebar: {
      width: {
        expanded: 280,
        collapsed: 80
      },
      defaultCollapsed: false,
      position: 'left',
      variant: 'default'
    },
    header: {
      height: 64,
      sticky: true,
      variant: 'default'
    },
    footer: {
      height: 48,
      show: true,
      variant: 'minimal'
    },
    container: {
      maxWidth: '1400px',
      padding: 24
    }
  },
  
  components: {
    buttons: {
      defaultSize: 'md',
      defaultVariant: 'default',
      borderRadius: 8
    },
    cards: {
      defaultShadow: 'md',
      borderRadius: 12,
      padding: 24
    },
    forms: {
      defaultSize: 'md',
      borderRadius: 8,
      focusRing: true
    },
    tables: {
      striped: true,
      hover: true,
      compact: false,
      borderRadius: 8
    }
  },
  
  dashboard: {
    widgets: {
      defaultSpacing: 24,
      borderRadius: 16,
      shadow: 'lg',
      animation: true
    },
    charts: {
      defaultColors: [
        '#10B981', '#3B82F6', '#F59E0B', '#EF4444',
        '#8B5CF6', '#06B6D4', '#F97316', '#84CC16'
      ],
      animations: true,
      responsive: true,
      theme: 'auto'
    },
    layout: {
      columns: 12,
      gap: 24,
      minWidgetHeight: 200
    }
  },
  
  animations: {
    enabled: true,
    duration: {
      fast: 150,
      normal: 300,
      slow: 500
    },
    easing: {
      default: 'cubic-bezier(0.4, 0, 0.2, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
    }
  },
  
  accessibility: {
    highContrast: false,
    reducedMotion: false,
    fontSize: 'medium',
    focusVisible: true
  }
}

// Customization utilities
export const getCustomization = (): CustomizationConfig => {
  // In a real app, this would load from user preferences, database, etc.
  const stored = typeof window !== 'undefined' 
    ? localStorage.getItem('eeu_customization')
    : null
  
  if (stored) {
    try {
      const parsed = JSON.parse(stored)
      return { ...DEFAULT_CUSTOMIZATION, ...parsed }
    } catch {
      return DEFAULT_CUSTOMIZATION
    }
  }
  
  return DEFAULT_CUSTOMIZATION
}

export const saveCustomization = (config: Partial<CustomizationConfig>): void => {
  if (typeof window !== 'undefined') {
    const current = getCustomization()
    const updated = { ...current, ...config }
    localStorage.setItem('eeu_customization', JSON.stringify(updated))
  }
}

export const resetCustomization = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('eeu_customization')
  }
}

export const applyTheme = (themeId: string): void => {
  const config = getCustomization()
  const theme = config.themes.available.find(t => t.id === themeId)
  
  if (!theme) return
  
  const root = document.documentElement
  
  // Apply CSS custom properties
  Object.entries(theme.colors).forEach(([key, value]) => {
    root.style.setProperty(`--color-${key}`, value)
  })
  
  Object.entries(theme.shadows).forEach(([key, value]) => {
    root.style.setProperty(`--shadow-${key}`, value)
  })
  
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    root.style.setProperty(`--radius-${key}`, value)
  })
  
  // Save theme preference
  saveCustomization({
    themes: {
      ...config.themes,
      default: themeId
    }
  })
}

export const generateCSSVariables = (config: CustomizationConfig): string => {
  const theme = config.themes.available.find(t => t.id === config.themes.default)
  if (!theme) return ''
  
  const variables: string[] = []
  
  // Colors
  Object.entries(theme.colors).forEach(([key, value]) => {
    variables.push(`  --color-${key}: ${value};`)
  })
  
  // Shadows
  Object.entries(theme.shadows).forEach(([key, value]) => {
    variables.push(`  --shadow-${key}: ${value};`)
  })
  
  // Border radius
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    variables.push(`  --radius-${key}: ${value};`)
  })
  
  // Layout
  variables.push(`  --sidebar-width-expanded: ${config.layout.sidebar.width.expanded}px;`)
  variables.push(`  --sidebar-width-collapsed: ${config.layout.sidebar.width.collapsed}px;`)
  variables.push(`  --header-height: ${config.layout.header.height}px;`)
  variables.push(`  --footer-height: ${config.layout.footer.height}px;`)
  
  // Animations
  variables.push(`  --duration-fast: ${config.animations.duration.fast}ms;`)
  variables.push(`  --duration-normal: ${config.animations.duration.normal}ms;`)
  variables.push(`  --duration-slow: ${config.animations.duration.slow}ms;`)
  variables.push(`  --easing-default: ${config.animations.easing.default};`)
  
  return `:root {\n${variables.join('\n')}\n}`
}

export default getCustomization()
