-- My<PERSON><PERSON> Schema for dtms_eeu_db
-- Ethiopian Electric Utility - Digital Transformer Management System

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS `dtms_eeu_db`
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `dtms_eeu_db`;

-- Users table
CREATE TABLE IF NOT EXISTS `users` (
  `id` VARCHAR(36) PRIMARY KEY,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `email` VARCHAR(255) UNIQUE NOT NULL,
  `password_hash` VARCHAR(255) NOT NULL,
  `salt` VARCHAR(255) NOT NULL,
  `first_name` VARCHAR(100) NOT NULL,
  `last_name` VARCHAR(100) NOT NULL,
  `role` ENUM('super_admin', 'national_asset_manager', 'regional_asset_manager', 'maintenance_manager', 'technician', 'viewer') NOT NULL,
  `is_active` BOOLEAN DEFAULT TRUE,
  `last_login` TIMESTAMP NULL,
  `region_id` VARCHAR(36) NULL,
  `service_center_id` VARCHAR(36) NULL,
  INDEX `idx_users_email` (`email`),
  INDEX `idx_users_role` (`role`),
  INDEX `idx_users_region` (`region_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Regions table
CREATE TABLE IF NOT EXISTS `regions` (
  `id` VARCHAR(36) PRIMARY KEY,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `name` VARCHAR(100) NOT NULL,
  `code` VARCHAR(10) UNIQUE NOT NULL,
  `description` TEXT,
  `coordinates_lat` DECIMAL(10, 8) NOT NULL,
  `coordinates_lng` DECIMAL(11, 8) NOT NULL,
  `population` INT DEFAULT 0,
  `area_km2` DECIMAL(10, 2) DEFAULT 0,
  INDEX `idx_regions_code` (`code`),
  INDEX `idx_regions_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Service Centers table
CREATE TABLE IF NOT EXISTS `service_centers` (
  `id` VARCHAR(36) PRIMARY KEY,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `name` VARCHAR(100) NOT NULL,
  `code` VARCHAR(20) UNIQUE NOT NULL,
  `region_id` VARCHAR(36) NOT NULL,
  `address` TEXT,
  `coordinates_lat` DECIMAL(10, 8) NOT NULL,
  `coordinates_lng` DECIMAL(11, 8) NOT NULL,
  `contact_phone` VARCHAR(20),
  `contact_email` VARCHAR(255),
  `manager_name` VARCHAR(100),
  `capacity` INT DEFAULT 0,
  INDEX `idx_service_centers_region` (`region_id`),
  INDEX `idx_service_centers_code` (`code`),
  FOREIGN KEY (`region_id`) REFERENCES `regions`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Transformers table
CREATE TABLE IF NOT EXISTS `transformers` (
  `id` VARCHAR(36) PRIMARY KEY,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `serial_number` VARCHAR(50) UNIQUE NOT NULL,
  `name` VARCHAR(100) NOT NULL,
  `status` ENUM('operational', 'warning', 'critical', 'offline', 'maintenance') NOT NULL DEFAULT 'operational',
  `type` VARCHAR(50) NOT NULL,
  `manufacturer` VARCHAR(100) NOT NULL,
  `model` VARCHAR(100) NOT NULL,
  `manufacture_date` DATE,
  `installation_date` DATE,
  `last_maintenance_date` DATE,
  `next_maintenance_date` DATE,
  `capacity` INT NOT NULL,
  `voltage_primary` DECIMAL(8, 2) NOT NULL,
  `voltage_secondary` DECIMAL(8, 2) NOT NULL,
  `region_id` VARCHAR(36) NOT NULL,
  `service_center_id` VARCHAR(36) NOT NULL,
  `location_address` TEXT,
  `location_lat` DECIMAL(10, 8) NOT NULL,
  `location_lng` DECIMAL(11, 8) NOT NULL,
  `temperature` DECIMAL(5, 2) DEFAULT 0,
  `load_percentage` DECIMAL(5, 2) DEFAULT 0,
  `oil_level` DECIMAL(5, 2) DEFAULT 0,
  `health_index` DECIMAL(5, 2) DEFAULT 0,
  `tags` JSON,
  INDEX `idx_transformers_serial` (`serial_number`),
  INDEX `idx_transformers_status` (`status`),
  INDEX `idx_transformers_region` (`region_id`),
  INDEX `idx_transformers_service_center` (`service_center_id`),
  INDEX `idx_transformers_type` (`type`),
  INDEX `idx_transformers_manufacturer` (`manufacturer`),
  FOREIGN KEY (`region_id`) REFERENCES `regions`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`service_center_id`) REFERENCES `service_centers`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Maintenance Records table
CREATE TABLE IF NOT EXISTS `maintenance_records` (
  `id` VARCHAR(36) PRIMARY KEY,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `transformer_id` VARCHAR(36) NOT NULL,
  `type` ENUM('preventive', 'corrective', 'emergency', 'inspection') NOT NULL,
  `status` ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'overdue') NOT NULL DEFAULT 'scheduled',
  `priority` ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
  `scheduled_date` DATE NOT NULL,
  `completed_date` DATE NULL,
  `technician_id` VARCHAR(36) NULL,
  `description` TEXT,
  `work_performed` TEXT,
  `parts_used` JSON,
  `cost` DECIMAL(10, 2) DEFAULT 0,
  `notes` TEXT,
  INDEX `idx_maintenance_transformer` (`transformer_id`),
  INDEX `idx_maintenance_status` (`status`),
  INDEX `idx_maintenance_type` (`type`),
  INDEX `idx_maintenance_scheduled_date` (`scheduled_date`),
  FOREIGN KEY (`transformer_id`) REFERENCES `transformers`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`technician_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Alerts table
CREATE TABLE IF NOT EXISTS `alerts` (
  `id` VARCHAR(36) PRIMARY KEY,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `transformer_id` VARCHAR(36) NULL,
  `type` VARCHAR(50) NOT NULL,
  `severity` ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  `title` VARCHAR(200) NOT NULL,
  `message` TEXT NOT NULL,
  `is_resolved` BOOLEAN DEFAULT FALSE,
  `resolved_at` TIMESTAMP NULL,
  `resolved_by` VARCHAR(36) NULL,
  `resolution_notes` TEXT,
  INDEX `idx_alerts_transformer` (`transformer_id`),
  INDEX `idx_alerts_severity` (`severity`),
  INDEX `idx_alerts_type` (`type`),
  INDEX `idx_alerts_resolved` (`is_resolved`),
  INDEX `idx_alerts_created` (`created_at`),
  FOREIGN KEY (`transformer_id`) REFERENCES `transformers`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`resolved_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Outages table
CREATE TABLE IF NOT EXISTS `outages` (
  `id` VARCHAR(36) PRIMARY KEY,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `transformer_id` VARCHAR(36) NULL,
  `region_id` VARCHAR(36) NULL,
  `title` VARCHAR(200) NOT NULL,
  `description` TEXT,
  `status` ENUM('active', 'resolved', 'investigating') NOT NULL DEFAULT 'active',
  `severity` ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  `start_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_time` TIMESTAMP NULL,
  `affected_customers` INT DEFAULT 0,
  `cause` VARCHAR(200),
  `resolution` TEXT,
  INDEX `idx_outages_transformer` (`transformer_id`),
  INDEX `idx_outages_region` (`region_id`),
  INDEX `idx_outages_status` (`status`),
  INDEX `idx_outages_severity` (`severity`),
  INDEX `idx_outages_start_time` (`start_time`),
  FOREIGN KEY (`transformer_id`) REFERENCES `transformers`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`region_id`) REFERENCES `regions`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Weather Alerts table
CREATE TABLE IF NOT EXISTS `weather_alerts` (
  `id` VARCHAR(36) PRIMARY KEY,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `region_id` VARCHAR(36) NULL,
  `type` VARCHAR(50) NOT NULL,
  `severity` ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  `title` VARCHAR(200) NOT NULL,
  `description` TEXT,
  `is_active` BOOLEAN DEFAULT TRUE,
  `start_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_time` TIMESTAMP NULL,
  `affected_areas` JSON,
  INDEX `idx_weather_alerts_region` (`region_id`),
  INDEX `idx_weather_alerts_type` (`type`),
  INDEX `idx_weather_alerts_severity` (`severity`),
  INDEX `idx_weather_alerts_active` (`is_active`),
  INDEX `idx_weather_alerts_start_time` (`start_time`),
  FOREIGN KEY (`region_id`) REFERENCES `regions`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_transformers_health` ON `transformers`(`health_index`);
CREATE INDEX IF NOT EXISTS `idx_transformers_capacity` ON `transformers`(`capacity`);
CREATE INDEX IF NOT EXISTS `idx_maintenance_priority` ON `maintenance_records`(`priority`);
CREATE INDEX IF NOT EXISTS `idx_alerts_severity_created` ON `alerts`(`severity`, `created_at`);
CREATE INDEX IF NOT EXISTS `idx_outages_duration` ON `outages`(`start_time`, `end_time`);
