/**
 * MySQL Connection Service for dtms_eeu_db
 * 
 * This service provides MySQL database connectivity for the Ethiopian Electric Utility
 * Digital Transformer Management System database.
 */

import mysql from 'mysql2/promise';

// Database configuration
const DB_CONFIG = {
  host: process.env.DB_HOST || process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || process.env.MYSQL_PORT || '3306'),
  user: process.env.DB_USER || process.env.MYSQL_USER || 'root',
  password: process.env.DB_PASSWORD || process.env.MYSQL_PASSWORD || '',
  database: process.env.DB_NAME || process.env.MYSQL_DATABASE || 'dtms_eeu_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  charset: 'utf8mb4'
};

// Connection pool
let pool: mysql.Pool | null = null;

/**
 * Initialize MySQL connection pool
 */
export function initializePool(): mysql.Pool {
  if (!pool) {
    console.log('🔄 Initializing MySQL connection pool for dtms_eeu_db...');
    pool = mysql.createPool(DB_CONFIG);
    console.log('✅ MySQL connection pool initialized successfully');
  }
  return pool;
}

/**
 * Get MySQL connection pool
 */
export function getPool(): mysql.Pool {
  if (!pool) {
    return initializePool();
  }
  return pool;
}

/**
 * Execute a MySQL query
 */
export async function executeQuery<T = any>(
  query: string, 
  params: any[] = []
): Promise<T> {
  const connection = getPool();
  
  try {
    console.log('🔍 Executing query:', query.substring(0, 100) + (query.length > 100 ? '...' : ''));
    const [results] = await connection.execute(query, params);
    return results as T;
  } catch (error) {
    console.error('❌ MySQL query error:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
}

/**
 * Test database connection
 */
export async function testConnection(): Promise<boolean> {
  try {
    console.log('🔄 Testing MySQL connection to dtms_eeu_db...');
    await executeQuery('SELECT 1 as test');
    console.log('✅ MySQL connection test successful');
    return true;
  } catch (error) {
    console.error('❌ MySQL connection test failed:', error);
    return false;
  }
}

/**
 * Create database if it doesn't exist
 */
export async function createDatabase(): Promise<void> {
  try {
    console.log('🔄 Creating database dtms_eeu_db if it doesn\'t exist...');
    
    // Connect without specifying database
    const tempConfig: Partial<typeof DB_CONFIG> = { ...DB_CONFIG };
    delete tempConfig.database;
    
    const tempConnection = await mysql.createConnection(tempConfig);
    
    await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS \`${DB_CONFIG.database}\` 
      CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    
    await tempConnection.end();
    
    console.log('✅ Database dtms_eeu_db created or already exists');
  } catch (error) {
    console.error('❌ Error creating database:', error);
    throw error;
  }
}

/**
 * Close all connections
 */
export async function closeConnections(): Promise<void> {
  if (pool) {
    console.log('🔄 Closing MySQL connection pool...');
    await pool.end();
    pool = null;
    console.log('✅ MySQL connection pool closed');
  }
}

/**
 * Get database metadata
 */
export async function getDatabaseInfo() {
  try {
    const tables = await executeQuery<any[]>(`
      SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ?
    `, [DB_CONFIG.database]);

    const version = await executeQuery<any[]>('SELECT VERSION() as version');
    
    return {
      database: DB_CONFIG.database,
      host: DB_CONFIG.host,
      port: DB_CONFIG.port,
      version: version[0]?.version,
      tables: tables.map(table => ({
        name: table.TABLE_NAME,
        rows: table.TABLE_ROWS,
        dataSize: table.DATA_LENGTH,
        indexSize: table.INDEX_LENGTH
      }))
    };
  } catch (error) {
    console.error('❌ Error getting database info:', error);
    throw error;
  }
}

// Export configuration for debugging
export { DB_CONFIG };
