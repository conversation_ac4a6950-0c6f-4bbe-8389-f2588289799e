"use client"

import { useState } from "react"
import Image from "next/image"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/src/components/ui/card"
import { But<PERSON> } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Textarea } from "@/src/components/ui/textarea"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Checkbox } from "@/src/components/ui/checkbox"
import { Calendar } from "@/src/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON>atter<PERSON>hart,
  Radar,
  Grid3X3,
  Calendar as CalendarIcon,
  Save,
  Play,
  Clock,
  Users,
  Plus,
  X
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/src/lib/utils"
import { useReports, ChartType, CustomReportConfig } from "@/src/contexts/reports-context"

export function CustomReportBuilder() {
  const {
    getMetrics,
    getRegions,
    getServiceCenters,
    getTransformerTypes,
    getManufacturers,
    saveCustomReport,
    generateReport
  } = useReports()

  const [activeTab, setActiveTab] = useState("metrics")
  const [reportName, setReportName] = useState("")
  const [reportDescription, setReportDescription] = useState("")
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([])
  const [selectedChartType, setSelectedChartType] = useState<ChartType>("bar")
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({
    from: new Date(new Date().getFullYear(), 0, 1),
    to: new Date()
  })
  const [filters, setFilters] = useState({
    region: "all",
    serviceCenter: "all",
    transformerType: "all",
    manufacturer: "all"
  })
  const [scheduleEnabled, setScheduleEnabled] = useState(false)
  const [scheduleConfig, setScheduleConfig] = useState({
    frequency: "monthly",
    day: 1,
    time: "08:00",
    recipients: [""]
  })

  const metrics = getMetrics()
  const regions = getRegions()
  const serviceCenters = getServiceCenters()
  const transformerTypes = getTransformerTypes()
  const manufacturers = getManufacturers()

  const handleMetricToggle = (metricId: string) => {
    setSelectedMetrics(prev =>
      prev.includes(metricId)
        ? prev.filter(id => id !== metricId)
        : [...prev, metricId]
    )
  }

  const handleSaveReport = () => {
    if (!reportName) {
      alert("Please enter a report name")
      return
    }

    if (selectedMetrics.length === 0) {
      alert("Please select at least one metric")
      return
    }

    const reportConfig: Omit<CustomReportConfig, 'id' | 'createdAt'> = {
      name: reportName,
      description: reportDescription,
      metrics: selectedMetrics,
      chartType: selectedChartType,
      dateRange,
      filters: {
        region: filters.region !== "all" ? filters.region : undefined,
        serviceCenter: filters.serviceCenter !== "all" ? filters.serviceCenter : undefined,
        transformerType: filters.transformerType !== "all" ? filters.transformerType : undefined,
        manufacturer: filters.manufacturer !== "all" ? filters.manufacturer : undefined
      },
      schedule: scheduleEnabled ? {
        frequency: scheduleConfig.frequency as "daily" | "weekly" | "monthly",
        day: scheduleConfig.day,
        time: scheduleConfig.time,
        recipients: scheduleConfig.recipients.filter(r => r.trim() !== "")
      } : undefined
    }

    saveCustomReport(reportConfig)

    // Reset form
    setReportName("")
    setReportDescription("")
    setSelectedMetrics([])
    setSelectedChartType("bar")
    setDateRange({
      from: new Date(new Date().getFullYear(), 0, 1),
      to: new Date()
    })
    setFilters({
      region: "all",
      serviceCenter: "all",
      transformerType: "all",
      manufacturer: "all"
    })
    setScheduleEnabled(false)
    setScheduleConfig({
      frequency: "monthly",
      day: 1,
      time: "08:00",
      recipients: [""]
    })
  }

  const handleGenerateReport = () => {
    if (selectedMetrics.length === 0) {
      alert("Please select at least one metric")
      return
    }

    generateReport("custom", {
      metrics: selectedMetrics,
      chartType: selectedChartType,
      dateRange,
      filters: {
        region: filters.region !== "all" ? filters.region : undefined,
        serviceCenter: filters.serviceCenter !== "all" ? filters.serviceCenter : undefined,
        transformerType: filters.transformerType !== "all" ? filters.transformerType : undefined,
        manufacturer: filters.manufacturer !== "all" ? filters.manufacturer : undefined
      }
    })
  }

  const handleAddRecipient = () => {
    setScheduleConfig(prev => ({
      ...prev,
      recipients: [...prev.recipients, ""]
    }))
  }

  const handleRemoveRecipient = (index: number) => {
    setScheduleConfig(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }))
  }

  const handleUpdateRecipient = (index: number, value: string) => {
    setScheduleConfig(prev => ({
      ...prev,
      recipients: prev.recipients.map((r, i) => i === index ? value : r)
    }))
  }

  const getChartIcon = (type: ChartType) => {
    switch (type) {
      case "bar": return <BarChart3 className="h-4 w-4" />
      case "line": return <LineChart className="h-4 w-4" />
      case "pie": return <PieChart className="h-4 w-4" />
      case "area": return <AreaChart className="h-4 w-4" />
      case "scatter": return <ScatterChart className="h-4 w-4" />
      case "radar": return <Radar className="h-4 w-4" />
      case "heatmap": return <Grid3X3 className="h-4 w-4" />
      default: return <BarChart3 className="h-4 w-4" />
    }
  }

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-col sm:flex-row sm:items-center gap-4">
        <div className="hidden sm:block">
          <Image
            src="/eeu-logo.svg"
            alt="Ethiopian Electric Utility Logo"
            width={48}
            height={48}
            className="h-12 w-12"
          />
        </div>
        <div>
          <CardTitle>Custom Report Builder</CardTitle>
          <CardDescription>Create and configure custom reports based on your specific needs</CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
            <TabsTrigger value="visualization">Visualization</TabsTrigger>
            <TabsTrigger value="filters">Filters</TabsTrigger>
            <TabsTrigger value="schedule">Schedule</TabsTrigger>
          </TabsList>

          <TabsContent value="metrics" className="space-y-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="report-name">Report Name</Label>
                <Input
                  id="report-name"
                  placeholder="Enter report name"
                  value={reportName}
                  onChange={(e) => setReportName(e.target.value)}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="report-description">Description</Label>
                <Textarea
                  id="report-description"
                  placeholder="Enter report description"
                  value={reportDescription}
                  onChange={(e) => setReportDescription(e.target.value)}
                />
              </div>

              <div className="grid gap-2">
                <Label>Select Metrics</Label>
                <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                  {metrics.map((metric) => (
                    <div key={metric.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`metric-${metric.id}`}
                        checked={selectedMetrics.includes(metric.id)}
                        onCheckedChange={() => handleMetricToggle(metric.id)}
                      />
                      <Label
                        htmlFor={`metric-${metric.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {metric.name}
                        <span className="ml-2 text-xs text-muted-foreground">({metric.category})</span>
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="visualization" className="space-y-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label>Chart Type</Label>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                  {(["bar", "line", "pie", "area", "scatter", "radar", "heatmap"] as ChartType[]).map((type) => (
                    <div
                      key={type}
                      className={cn(
                        "flex flex-col items-center justify-center p-4 border rounded-md cursor-pointer hover:border-primary transition-colors",
                        selectedChartType === type ? "border-primary bg-primary/5" : "border-border"
                      )}
                      onClick={() => setSelectedChartType(type)}
                    >
                      {getChartIcon(type)}
                      <span className="mt-2 text-sm capitalize">{type}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="grid gap-2">
                <Label>Date Range</Label>
                <div className="grid gap-2">
                  <div className="flex flex-col space-y-2 sm:flex-row sm:space-x-2 sm:space-y-0">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          id="date-from"
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal sm:w-[240px]",
                            !dateRange.from && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateRange.from ? format(dateRange.from, "PPP") : "From date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={dateRange.from}
                          onSelect={(date) => setDateRange(prev => ({ ...prev, from: date }))}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>

                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          id="date-to"
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal sm:w-[240px]",
                            !dateRange.to && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateRange.to ? format(dateRange.to, "PPP") : "To date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={dateRange.to}
                          onSelect={(date) => setDateRange(prev => ({ ...prev, to: date }))}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="filters" className="space-y-4">
            <div className="grid gap-4 sm:grid-cols-2">
              <div className="grid gap-2">
                <Label htmlFor="region-filter">Region</Label>
                <Select
                  value={filters.region}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, region: value }))}
                >
                  <SelectTrigger id="region-filter">
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Regions</SelectItem>
                    {regions.map((region) => (
                      <SelectItem key={region} value={region}>{region}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="service-center-filter">Service Center</Label>
                <Select
                  value={filters.serviceCenter}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, serviceCenter: value }))}
                >
                  <SelectTrigger id="service-center-filter">
                    <SelectValue placeholder="Select service center" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Service Centers</SelectItem>
                    {serviceCenters.map((center) => (
                      <SelectItem key={center} value={center}>{center}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="transformer-type-filter">Transformer Type</Label>
                <Select
                  value={filters.transformerType}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, transformerType: value }))}
                >
                  <SelectTrigger id="transformer-type-filter">
                    <SelectValue placeholder="Select transformer type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {transformerTypes.map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="manufacturer-filter">Manufacturer</Label>
                <Select
                  value={filters.manufacturer}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, manufacturer: value }))}
                >
                  <SelectTrigger id="manufacturer-filter">
                    <SelectValue placeholder="Select manufacturer" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Manufacturers</SelectItem>
                    {manufacturers.map((manufacturer) => (
                      <SelectItem key={manufacturer} value={manufacturer}>{manufacturer}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="schedule-enabled"
                checked={scheduleEnabled}
                onCheckedChange={(checked) => setScheduleEnabled(checked === true)}
              />
              <Label htmlFor="schedule-enabled">Schedule this report</Label>
            </div>

            {scheduleEnabled && (
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="grid gap-2">
                  <Label htmlFor="schedule-frequency">Frequency</Label>
                  <Select
                    value={scheduleConfig.frequency}
                    onValueChange={(value) => setScheduleConfig(prev => ({ ...prev, frequency: value }))}
                  >
                    <SelectTrigger id="schedule-frequency">
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {scheduleConfig.frequency !== "daily" && (
                  <div className="grid gap-2">
                    <Label htmlFor="schedule-day">
                      {scheduleConfig.frequency === "weekly" ? "Day of Week" : "Day of Month"}
                    </Label>
                    <Select
                      value={scheduleConfig.day.toString()}
                      onValueChange={(value) => setScheduleConfig(prev => ({ ...prev, day: parseInt(value) }))}
                    >
                      <SelectTrigger id="schedule-day">
                        <SelectValue placeholder="Select day" />
                      </SelectTrigger>
                      <SelectContent>
                        {scheduleConfig.frequency === "weekly" ? (
                          <>
                            <SelectItem value="1">Monday</SelectItem>
                            <SelectItem value="2">Tuesday</SelectItem>
                            <SelectItem value="3">Wednesday</SelectItem>
                            <SelectItem value="4">Thursday</SelectItem>
                            <SelectItem value="5">Friday</SelectItem>
                            <SelectItem value="6">Saturday</SelectItem>
                            <SelectItem value="7">Sunday</SelectItem>
                          </>
                        ) : (
                          Array.from({ length: 31 }, (_, i) => (
                            <SelectItem key={i + 1} value={(i + 1).toString()}>{i + 1}</SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="grid gap-2">
                  <Label htmlFor="schedule-time">Time</Label>
                  <Input
                    id="schedule-time"
                    type="time"
                    value={scheduleConfig.time}
                    onChange={(e) => setScheduleConfig(prev => ({ ...prev, time: e.target.value }))}
                  />
                </div>

                <div className="grid gap-2 sm:col-span-2">
                  <div className="flex items-center justify-between">
                    <Label>Recipients</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleAddRecipient}
                    >
                      <Plus className="h-4 w-4 mr-1" /> Add
                    </Button>
                  </div>

                  <div className="space-y-2">
                    {scheduleConfig.recipients.map((recipient, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input
                          placeholder="Email address"
                          value={recipient}
                          onChange={(e) => handleUpdateRecipient(index, e.target.value)}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => handleRemoveRecipient(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => setActiveTab(activeTab === "metrics" ? "metrics" : getPreviousTab(activeTab))}>
          Back
        </Button>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleGenerateReport}
          >
            <Play className="mr-2 h-4 w-4" />
            Generate
          </Button>

          <Button onClick={handleSaveReport}>
            <Save className="mr-2 h-4 w-4" />
            Save Report
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}

function getPreviousTab(currentTab: string): string {
  switch (currentTab) {
    case "visualization": return "metrics"
    case "filters": return "visualization"
    case "schedule": return "filters"
    default: return "metrics"
  }
}
