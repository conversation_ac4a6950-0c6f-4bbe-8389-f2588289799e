"use client"

import { useEffect, useRef, useState } from "react"
import mapboxgl from "mapbox-gl"
import "mapbox-gl/dist/mapbox-gl.css"

// Mapbox access token - in a real app, this would be in environment variables
const MAPBOX_ACCESS_TOKEN = "pk.eyJ1IjoiZXRoaW9waWFlZXUiLCJhIjoiY2xzMnRqMnRpMGF1eTJrcGFtdXJ5Z2VsZSJ9.Qr9MQNqvAYZLjLz0_kDrqw"

interface SimpleMapProps {
  height?: string
}

export function SimpleMap({ height = "500px" }: SimpleMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Set Mapbox access token
  mapboxgl.accessToken = MAPBOX_ACCESS_TOKEN

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current) return

    try {
      console.log("Initializing map...")
      
      // Create map instance
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/streets-v12",
        center: [38.7468, 9.0222], // Addis Ababa
        zoom: 6
      })

      // Add navigation control
      map.current.addControl(new mapboxgl.NavigationControl(), "top-right")

      // Map load event
      map.current.on("load", () => {
        console.log("Map loaded successfully")
        setMapLoaded(true)
      })

      // Map error event
      map.current.on("error", (e) => {
        console.error("Map error:", e)
        setError(`Map error: ${e.error?.message || "Unknown error"}`)
      })
    } catch (err) {
      console.error("Error initializing map:", err)
      setError(`Error initializing map: ${err instanceof Error ? err.message : "Unknown error"}`)
    }

    return () => {
      if (map.current) {
        console.log("Removing map...")
        map.current.remove()
        map.current = null
      }
    }
  }, [])

  return (
    <div className="relative w-full" style={{ height }}>
      <div ref={mapContainer} className="w-full h-full rounded-md" />
      
      {!mapLoaded && !error && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80">
          <div className="flex flex-col items-center gap-2">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
            <p className="text-sm text-muted-foreground">Loading map...</p>
          </div>
        </div>
      )}
      
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/90">
          <div className="flex flex-col items-center gap-2 max-w-md p-4 bg-destructive/10 rounded-md border border-destructive/20">
            <p className="text-sm font-medium text-destructive">{error}</p>
            <p className="text-xs text-muted-foreground">
              Please check your internet connection and try again. If the problem persists, contact support.
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
