import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    console.log('Test filter API called')
    
    const { searchParams } = new URL(request.url)
    const regions = searchParams.get('regions')
    
    return NextResponse.json({
      message: 'Test filter API working',
      regions: regions || 'none',
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('Test filter API error:', error)
    return NextResponse.json(
      { error: 'Test filter API failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
