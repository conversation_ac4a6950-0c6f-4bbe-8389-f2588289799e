"use client"

import { useEffect } from "react"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { AlertTriangle, RefreshCw, Home } from "lucide-react"
import Link from "next/link"

export default function DashboardError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Dashboard error:", error)
  }, [error])

  // Check if this is a chunk loading error
  const isChunkError = error.message?.includes('ChunkLoadError') ||
                       error.message?.includes('Loading chunk') ||
                       error.digest?.includes('chunk');

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 text-center">
      <div className="bg-red-50 dark:bg-red-900/20 p-8 rounded-lg border border-red-200 dark:border-red-800 max-w-md">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Dashboard Error</h2>

        {isChunkError ? (
          <>
            <p className="text-muted-foreground mb-4">
              Failed to load dashboard resources. This could be due to a network issue or a temporary problem.
            </p>
            <p className="text-sm text-muted-foreground mb-6">
              Try refreshing the page or clearing your browser cache.
            </p>
          </>
        ) : (
          <>
            <p className="text-muted-foreground mb-4">
              Something went wrong while loading the dashboard.
            </p>
            <p className="text-sm text-muted-foreground mb-6">
              Error: {error.message || "Unknown error"}
            </p>
          </>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button onClick={() => reset()} className="flex-1">
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
          <Button variant="outline" className="flex-1" asChild>
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
