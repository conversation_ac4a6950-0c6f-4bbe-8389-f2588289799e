/**
 * Realistic Data Seeding Script for Ethiopian Electric Utility
 *
 * This script seeds comprehensive realistic data for all modules and features
 * including transformers, maintenance, smart meters, field operations, and more.
 */

const mysql = require('mysql2/promise')

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: null, // Explicitly set to null for local setup without a password
  database: process.env.DB_NAME || 'dtms_eeu_db'
}

// Ethiopian regions and major cities
const ethiopianRegions = [
  { name: 'Addis Ababa', code: 'AA', population: 3500000, coordinates: { lat: 9.0320, lng: 38.7469 } },
  { name: 'Oromia', code: 'OR', population: 37000000, coordinates: { lat: 8.5000, lng: 39.5000 } },
  { name: '<PERSON><PERSON>', code: 'AM', population: 21000000, coordinates: { lat: 11.5000, lng: 37.5000 } },
  { name: 'Tig<PERSON>', code: 'TI', population: 5500000, coordinates: { lat: 14.0000, lng: 38.5000 } },
  { name: 'SNNP', code: 'SN', population: 20000000, coordinates: { lat: 6.5000, lng: 37.0000 } },
  { name: 'Somali', code: 'SO', population: 5500000, coordinates: { lat: 6.0000, lng: 43.0000 } },
  { name: 'Afar', code: 'AF', population: 1800000, coordinates: { lat: 11.5000, lng: 41.0000 } },
  { name: 'Benishangul-Gumuz', code: 'BG', population: 1100000, coordinates: { lat: 10.5000, lng: 35.0000 } },
  { name: 'Gambela', code: 'GA', population: 450000, coordinates: { lat: 8.0000, lng: 34.0000 } },
  { name: 'Harari', code: 'HA', population: 250000, coordinates: { lat: 9.3000, lng: 42.1000 } },
  { name: 'Dire Dawa', code: 'DD', population: 500000, coordinates: { lat: 9.6000, lng: 41.8500 } }
]

// Ethiopian cities and substations
const ethiopianCities = [
  'Addis Ababa', 'Dire Dawa', 'Mekelle', 'Gondar', 'Dessie', 'Jimma', 'Jijiga', 'Shashamane',
  'Bahir Dar', 'Hawassa', 'Adama', 'Debre Markos', 'Harar', 'Dilla', 'Nekemte', 'Debre Birhan',
  'Asella', 'Adigrat', 'Wukro', 'Aksum', 'Shire', 'Alamata', 'Kombolcha', 'Debre Tabor',
  'Finote Selam', 'Injibara', 'Bonga', 'Mizan Teferi', 'Gambela', 'Assosa', 'Semera', 'Goba',
  'Robe', 'Sodo', 'Arba Minch', 'Jinka', 'Turmi', 'Moyale', 'Yabelo', 'Mega'
]

// Transformer manufacturers and models
const transformerData = {
  manufacturers: ['ABB', 'Siemens', 'Schneider Electric', 'General Electric', 'Hyundai Heavy Industries', 'TBEA', 'Crompton Greaves'],
  types: ['Distribution', 'Power', 'Instrument', 'Auto', 'Isolation', 'Rectifier'],
  voltageRatings: [
    { primary: 15000, secondary: 400 },
    { primary: 33000, secondary: 11000 },
    { primary: 66000, secondary: 15000 },
    { primary: 132000, secondary: 33000 },
    { primary: 230000, secondary: 66000 },
    { primary: 400000, secondary: 132000 }
  ],
  capacities: [50, 100, 160, 250, 315, 400, 500, 630, 800, 1000, 1250, 1600, 2000, 2500, 3150, 4000, 5000]
}

// Ethiopian names for technicians and users
const ethiopianNames = {
  first: ['Abebe', 'Almaz', 'Bekele', 'Chaltu', 'Dawit', 'Emebet', 'Fikru', 'Genet', 'Haile', 'Iyasu', 'Kassa', 'Lemlem', 'Meron', 'Negash', 'Rahel', 'Selamawit', 'Tadesse', 'Tigist', 'Worku', 'Yohannes'],
  last: ['Abera', 'Bekele', 'Chala', 'Desta', 'Eshetu', 'Fanta', 'Girma', 'Hailu', 'Kebede', 'Lemma', 'Mekonnen', 'Negash', 'Regassa', 'Sisay', 'Tadesse', 'Wolde', 'Yimer', 'Zenebe']
}

// Equipment and tools
const fieldEquipment = [
  'Digital Multimeter', 'Insulation Tester', 'Power Quality Analyzer', 'Thermal Camera', 'Oscilloscope',
  'Clamp Meter', 'High Voltage Detector', 'Safety Equipment', 'Hand Tools', 'Laptop Computer',
  'GPS Device', 'Radio Communication', 'First Aid Kit', 'Protective Gear', 'Cable Fault Locator'
]

// Skills for technicians
const technicalSkills = [
  'High Voltage Systems', 'Protection Systems', 'SCADA Operations', 'Transformer Maintenance',
  'Power System Analysis', 'Electrical Safety', 'Instrumentation', 'Control Systems',
  'Power Electronics', 'Renewable Energy', 'Grid Operations', 'Emergency Response'
]

// Maintenance types and procedures
const maintenanceTypes = [
  'Preventive Maintenance', 'Corrective Maintenance', 'Predictive Maintenance', 'Emergency Repair',
  'Routine Inspection', 'Oil Analysis', 'Bushing Replacement', 'Tap Changer Service',
  'Cooling System Maintenance', 'Protection System Testing', 'Grounding System Check', 'Insulation Testing'
]

// Alert types and descriptions
const alertTypes = [
  { type: 'Temperature', severity: 'high', description: 'Transformer temperature exceeding normal limits' },
  { type: 'Oil Level', severity: 'medium', description: 'Oil level below recommended threshold' },
  { type: 'Load', severity: 'high', description: 'Transformer overload condition detected' },
  { type: 'Vibration', severity: 'medium', description: 'Abnormal vibration patterns detected' },
  { type: 'Insulation', severity: 'critical', description: 'Insulation resistance below safety limits' },
  { type: 'Protection', severity: 'critical', description: 'Protection system malfunction' },
  { type: 'Communication', severity: 'low', description: 'Communication link interruption' },
  { type: 'Power Quality', severity: 'medium', description: 'Power quality parameters out of range' }
]

// Generate random Ethiopian phone number
function generateEthiopianPhone() {
  const prefixes = ['091', '092', '093', '094', '097', '098']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const number = Math.floor(Math.random() * 10000000).toString().padStart(7, '0')
  return `+251-${prefix}-${number}`
}

// Generate random Ethiopian name
function generateEthiopianName() {
  const firstName = ethiopianNames.first[Math.floor(Math.random() * ethiopianNames.first.length)]
  const lastName = ethiopianNames.last[Math.floor(Math.random() * ethiopianNames.last.length)]
  return `${firstName} ${lastName}`
}

// Generate random coordinates within Ethiopia
function generateEthiopianCoordinates() {
  const lat = 3.0 + Math.random() * 12.0 // Ethiopia latitude range: 3°N to 15°N
  const lng = 33.0 + Math.random() * 15.0 // Ethiopia longitude range: 33°E to 48°E
  return { lat: parseFloat(lat.toFixed(6)), lng: parseFloat(lng.toFixed(6)) }
}

// Generate realistic transformer serial number
function generateTransformerSerial(manufacturer, year) {
  const prefix = manufacturer.substring(0, 3).toUpperCase()
  const yearCode = year.toString().substring(2)
  const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0')
  return `${prefix}-${yearCode}-${sequence}`
}

// Generate realistic maintenance cost
function generateMaintenanceCost(type) {
  const baseCosts = {
    'Preventive Maintenance': 5000,
    'Corrective Maintenance': 15000,
    'Predictive Maintenance': 8000,
    'Emergency Repair': 25000,
    'Routine Inspection': 2000,
    'Oil Analysis': 3000,
    'Bushing Replacement': 20000,
    'Tap Changer Service': 12000,
    'Cooling System Maintenance': 8000,
    'Protection System Testing': 6000,
    'Grounding System Check': 4000,
    'Insulation Testing': 5000
  }

  const baseCost = baseCosts[type] || 5000
  const variation = 0.3 // ±30% variation
  const multiplier = 1 + (Math.random() - 0.5) * 2 * variation
  return Math.round(baseCost * multiplier)
}

// Generate realistic sensor readings
function generateSensorReadings() {
  return {
    temperature: 35 + Math.random() * 30, // 35-65°C
    oilLevel: 80 + Math.random() * 20, // 80-100%
    vibration: Math.random() * 5, // 0-5 mm/s
    humidity: 40 + Math.random() * 40, // 40-80%
    pressure: 1 + Math.random() * 2, // 1-3 bar
    gasConcentration: Math.random() * 100, // 0-100 ppm
    insulationResistance: 1000 + Math.random() * 9000, // 1000-10000 MΩ
    powerFactor: 0.85 + Math.random() * 0.1 // 0.85-0.95
  }
}

// Main seeding function
async function seedRealisticData() {
  let connection

  try {
    console.log('🌱 Starting realistic data seeding for Ethiopian Electric Utility...')

    // Connect to database
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to MySQL database')

    // Clear existing data
    console.log('🧹 Clearing existing data...')
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')

    const tables = [
      'app_sensor_readings', 'app_notifications', 'app_work_orders', 'app_outages',
      'app_maintenance_schedules', 'app_alerts', 'app_transformers', 'app_inventory_items',
      'app_service_centers', 'app_regions', 'app_users', 'app_performance_metrics',
      'app_energy_consumption', 'app_weather_data', 'app_system_settings',
      'app_audit_logs', 'app_user_sessions', 'app_reports'
    ]

    for (const table of tables) {
      await connection.execute(`DELETE FROM ${table}`)
      console.log(`  Cleared ${table}`)
    }

    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')

    // Seed regions
    console.log('🌍 Seeding Ethiopian regions...')
    for (const region of ethiopianRegions) {
      await connection.execute(
        `INSERT INTO app_regions (id, name, code, country, population, area_km2, coordinates, timezone, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          `region-${region.code.toLowerCase()}`,
          region.name,
          region.code,
          'Ethiopia',
          region.population,
          Math.floor(Math.random() * 100000) + 10000,
          JSON.stringify(region.coordinates),
          'Africa/Addis_Ababa'
        ]
      )
    }
    console.log(`✅ Seeded ${ethiopianRegions.length} Ethiopian regions`)

    // Seed service centers
    console.log('🏢 Seeding service centers...')
    const serviceCenters = []
    for (let i = 0; i < 25; i++) {
      const region = ethiopianRegions[Math.floor(Math.random() * ethiopianRegions.length)]
      const city = ethiopianCities[Math.floor(Math.random() * ethiopianCities.length)]
      const coordinates = generateEthiopianCoordinates()

      const serviceCenter = {
        id: `sc-${String(i + 1).padStart(3, '0')}`,
        name: `${city} Service Center`,
        regionId: `region-${region.code.toLowerCase()}`,
        address: `${city}, ${region.name}, Ethiopia`,
        coordinates: coordinates,
        phone: generateEthiopianPhone(),
        email: `${city.toLowerCase().replace(/\s+/g, '')}@eeu.gov.et`,
        managerName: generateEthiopianName(),
        operatingHours: '08:00-17:00',
        facilities: JSON.stringify(['Workshop', 'Storage', 'Testing Lab', 'Vehicle Fleet'])
      }

      serviceCenters.push(serviceCenter)

      await connection.execute(
        `INSERT INTO app_service_centers (id, name, region_id, address, coordinates, phone, email, manager_name, operating_hours, facilities, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          serviceCenter.id,
          serviceCenter.name,
          serviceCenter.regionId,
          serviceCenter.address,
          JSON.stringify(serviceCenter.coordinates),
          serviceCenter.phone,
          serviceCenter.email,
          serviceCenter.managerName,
          serviceCenter.operatingHours,
          serviceCenter.facilities
        ]
      )
    }
    console.log(`✅ Seeded ${serviceCenters.length} service centers`)

    // Seed users
    console.log('👥 Seeding realistic users...')
    const users = [
      {
        id: 'user-001',
        name: 'Abebe Bekele',
        email: '<EMAIL>',
        password: '$2b$10$rQZ9vZ9Z9Z9Z9Z9Z9Z9Z9O', // hashed '123'
        role: 'super_admin',
        organizationalLevel: 'head_office',
        permissions: JSON.stringify([
          { resource: 'users', action: 'create' },
          { resource: 'users', action: 'read' },
          { resource: 'users', action: 'update' },
          { resource: 'users', action: 'delete' }
        ]),
        isActive: true,
        avatar: '/placeholder.svg?height=40&width=40&text=AB',
        phone: '+251-911-123-456',
        department: 'IT Administration',
        location: 'Addis Ababa',
        bio: 'System administrator responsible for managing the EEU Transformer Management System.'
      },
      {
        id: 'user-002',
        name: 'Almaz Tadesse',
        email: '<EMAIL>',
        password: '$2b$10$rQZ9vZ9Z9Z9Z9Z9Z9Z9Z9O',
        role: 'asset_manager',
        organizationalLevel: 'regional',
        permissions: JSON.stringify([
          { resource: 'transformers', action: 'create' },
          { resource: 'transformers', action: 'read' },
          { resource: 'transformers', action: 'update' },
          { resource: 'maintenance', action: 'read' }
        ]),
        isActive: true,
        avatar: '/placeholder.svg?height=40&width=40&text=AT',
        phone: '+251-911-234-567',
        department: 'Asset Management',
        location: 'Addis Ababa',
        bio: 'Asset manager overseeing transformer inventory and lifecycle management.'
      },
      {
        id: 'user-003',
        name: 'Dawit Mekonnen',
        email: '<EMAIL>',
        password: '$2b$10$rQZ9vZ9Z9Z9Z9Z9Z9Z9Z9O',
        role: 'regional_admin',
        organizationalLevel: 'regional',
        permissions: JSON.stringify([
          { resource: 'transformers', action: 'read' },
          { resource: 'maintenance', action: 'create' },
          { resource: 'maintenance', action: 'read' },
          { resource: 'maintenance', action: 'update' }
        ]),
        isActive: true,
        avatar: '/placeholder.svg?height=40&width=40&text=DM',
        phone: '+251-911-345-678',
        department: 'Regional Operations',
        location: 'Bahir Dar',
        bio: 'Regional administrator managing operations in Amhara region.'
      },
      {
        id: 'user-004',
        name: 'Genet Hailu',
        email: '<EMAIL>',
        password: '$2b$10$rQZ9vZ9Z9Z9Z9Z9Z9Z9Z9O',
        role: 'service_manager',
        organizationalLevel: 'service_center',
        permissions: JSON.stringify([
          { resource: 'maintenance', action: 'create' },
          { resource: 'maintenance', action: 'read' },
          { resource: 'maintenance', action: 'update' },
          { resource: 'work_orders', action: 'create' }
        ]),
        isActive: true,
        avatar: '/placeholder.svg?height=40&width=40&text=GH',
        phone: '+251-911-456-789',
        department: 'Service Management',
        location: 'Mekelle',
        bio: 'Service manager coordinating maintenance activities and work orders.'
      },
      {
        id: 'user-005',
        name: 'Fikru Wolde',
        email: '<EMAIL>',
        password: '$2b$10$rQZ9vZ9Z9Z9Z9Z9Z9Z9Z9O',
        role: 'technician',
        organizationalLevel: 'field',
        permissions: JSON.stringify([
          { resource: 'transformers', action: 'read' },
          { resource: 'maintenance', action: 'read' },
          { resource: 'work_orders', action: 'read' },
          { resource: 'work_orders', action: 'update' }
        ]),
        isActive: true,
        avatar: '/placeholder.svg?height=40&width=40&text=FW',
        phone: '+251-911-567-890',
        department: 'Field Operations',
        location: 'Jimma',
        bio: 'Field technician responsible for transformer maintenance and repairs.'
      },
      {
        id: 'user-006',
        name: 'Selamawit Girma',
        email: '<EMAIL>',
        password: '$2b$10$rQZ9vZ9Z9Z9Z9Z9Z9Z9Z9O',
        role: 'customer_service',
        organizationalLevel: 'service_center',
        permissions: JSON.stringify([
          { resource: 'transformers', action: 'read' },
          { resource: 'outages', action: 'read' },
          { resource: 'reports', action: 'read' }
        ]),
        isActive: true,
        avatar: '/placeholder.svg?height=40&width=40&text=SG',
        phone: '+251-911-678-901',
        department: 'Customer Service',
        location: 'Hawassa',
        bio: 'Customer service representative handling customer inquiries and outage reports.'
      }
    ]

    for (const user of users) {
      await connection.execute(
        `INSERT INTO app_users (id, name, email, password, role, organizational_level, permissions, is_active, last_login, avatar, phone, department, location, bio, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?, NOW(), NOW())`,
        [
          user.id,
          user.name,
          user.email,
          user.password,
          user.role,
          user.organizationalLevel,
          user.permissions,
          user.isActive,
          user.avatar,
          user.phone,
          user.department,
          user.location,
          user.bio
        ]
      )
    }
    console.log(`✅ Seeded ${users.length} realistic users`)

    // Seed transformers
    console.log('⚡ Seeding realistic transformers...')
    const transformers = []
    for (let i = 0; i < 150; i++) {
      const manufacturer = transformerData.manufacturers[Math.floor(Math.random() * transformerData.manufacturers.length)]
      const type = transformerData.types[Math.floor(Math.random() * transformerData.types.length)]
      const voltage = transformerData.voltageRatings[Math.floor(Math.random() * transformerData.voltageRatings.length)]
      const capacity = transformerData.capacities[Math.floor(Math.random() * transformerData.capacities.length)]
      const installationYear = 2010 + Math.floor(Math.random() * 14)
      const serviceCenter = serviceCenters[Math.floor(Math.random() * serviceCenters.length)]
      const coordinates = generateEthiopianCoordinates()

      const status = ['operational', 'warning', 'maintenance', 'critical', 'offline'][Math.floor(Math.random() * 5)]
      const healthIndex = status === 'operational' ? 80 + Math.random() * 20 :
                         status === 'warning' ? 60 + Math.random() * 20 :
                         status === 'maintenance' ? 70 + Math.random() * 15 :
                         status === 'critical' ? 20 + Math.random() * 40 :
                         10 + Math.random() * 30

      const transformer = {
        id: `transformer-${String(i + 1).padStart(3, '0')}`,
        serialNumber: generateTransformerSerial(manufacturer, installationYear),
        name: `${serviceCenter.name.split(' ')[0]} Transformer ${i + 1}`,
        manufacturer: manufacturer,
        model: `${manufacturer.substring(0, 3).toUpperCase()}-${capacity}${type.substring(0, 1)}`,
        type: type,
        capacity: capacity,
        primaryVoltage: voltage.primary,
        secondaryVoltage: voltage.secondary,
        status: status,
        healthIndex: parseFloat(healthIndex.toFixed(1)),
        installationDate: `${installationYear}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
        lastMaintenanceDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        nextMaintenanceDate: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        serviceCenterId: serviceCenter.id,
        regionId: serviceCenter.regionId,
        location: {
          address: `${serviceCenter.address} Substation`,
          coordinates: coordinates,
          substationName: `${serviceCenter.name.split(' ')[0]} Substation`
        },
        specifications: {
          coolingType: ['ONAN', 'ONAF', 'OFAF', 'ODAF'][Math.floor(Math.random() * 4)],
          tapChanger: Math.random() > 0.3,
          protectionClass: ['IP23', 'IP44', 'IP54'][Math.floor(Math.random() * 3)],
          frequency: 50,
          phases: 3,
          connectionType: ['Dyn11', 'Yyn0', 'Dyn1'][Math.floor(Math.random() * 3)]
        },
        currentMetrics: {
          temperature: 35 + Math.random() * 30,
          loadPercentage: Math.random() * 100,
          oilLevel: 80 + Math.random() * 20,
          vibration: Math.random() * 5,
          powerFactor: 0.85 + Math.random() * 0.1,
          efficiency: 95 + Math.random() * 4
        }
      }

      transformers.push(transformer)

      await connection.execute(
        `INSERT INTO app_transformers (id, serial_number, name, manufacturer, model, type, capacity, primary_voltage, secondary_voltage, status, health_index, installation_date, last_maintenance_date, next_maintenance_date, service_center_id, region_id, location, specifications, current_metrics, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          transformer.id,
          transformer.serialNumber,
          transformer.name,
          transformer.manufacturer,
          transformer.model,
          transformer.type,
          transformer.capacity,
          transformer.primaryVoltage,
          transformer.secondaryVoltage,
          transformer.status,
          transformer.healthIndex,
          transformer.installationDate,
          transformer.lastMaintenanceDate,
          transformer.nextMaintenanceDate,
          transformer.serviceCenterId,
          transformer.regionId,
          JSON.stringify(transformer.location),
          JSON.stringify(transformer.specifications),
          JSON.stringify(transformer.currentMetrics)
        ]
      )
    }
    console.log(`✅ Seeded ${transformers.length} realistic transformers`)

    // Seed maintenance schedules
    console.log('🔧 Seeding maintenance schedules...')
    const maintenanceSchedules = []
    for (let i = 0; i < 200; i++) {
      const transformer = transformers[Math.floor(Math.random() * transformers.length)]
      const type = maintenanceTypes[Math.floor(Math.random() * maintenanceTypes.length)]
      const priority = ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)]
      const status = ['scheduled', 'in_progress', 'completed', 'cancelled', 'overdue'][Math.floor(Math.random() * 5)]
      const technicianName = generateEthiopianName()
      const supervisorName = generateEthiopianName()

      const scheduledDate = new Date(Date.now() + (Math.random() - 0.5) * 180 * 24 * 60 * 60 * 1000)
      const estimatedDuration = Math.floor(Math.random() * 8) + 1
      const cost = generateMaintenanceCost(type)

      const maintenance = {
        id: `maint-${String(i + 1).padStart(3, '0')}`,
        title: `${type} - ${transformer.name}`,
        description: `${type} for transformer ${transformer.serialNumber} at ${transformer.location.address}`,
        type: type,
        status: status,
        priority: priority,
        scheduledDate: scheduledDate.toISOString().split('T')[0],
        scheduledTime: `${String(Math.floor(Math.random() * 16) + 6).padStart(2, '0')}:00`,
        estimatedDuration: estimatedDuration,
        transformerId: transformer.id,
        transformerSerial: transformer.serialNumber,
        transformerName: transformer.name,
        technicianName: technicianName,
        supervisorName: supervisorName,
        cost: cost,
        completionPercentage: status === 'completed' ? 100 :
                             status === 'in_progress' ? Math.floor(Math.random() * 80) + 10 : 0,
        workInstructions: `Detailed work instructions for ${type.toLowerCase()} on ${transformer.type} transformer`,
        safetyRequirements: 'Standard electrical safety procedures, PPE required, lockout/tagout procedures',
        requiredParts: JSON.stringify(['Oil', 'Gaskets', 'Filters', 'Testing Equipment'].slice(0, Math.floor(Math.random() * 4) + 1)),
        notes: `Maintenance notes for ${transformer.serialNumber}`,
        completionDays: status === 'completed' ? Math.floor(Math.random() * 10) + 1 : null
      }

      maintenanceSchedules.push(maintenance)

      await connection.execute(
        `INSERT INTO app_maintenance_schedules (id, title, description, type, status, priority, scheduled_date, scheduled_time, estimated_duration, transformer_id, transformer_serial, transformer_name, technician_name, supervisor_name, cost, completion_percentage, work_instructions, safety_requirements, required_parts, notes, completion_days, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          maintenance.id,
          maintenance.title,
          maintenance.description,
          maintenance.type,
          maintenance.status,
          maintenance.priority,
          maintenance.scheduledDate,
          maintenance.scheduledTime,
          maintenance.estimatedDuration,
          maintenance.transformerId,
          maintenance.transformerSerial,
          maintenance.transformerName,
          maintenance.technicianName,
          maintenance.supervisorName,
          maintenance.cost,
          maintenance.completionPercentage,
          maintenance.workInstructions,
          maintenance.safetyRequirements,
          maintenance.requiredParts,
          maintenance.notes,
          maintenance.completionDays
        ]
      )
    }
    console.log(`✅ Seeded ${maintenanceSchedules.length} maintenance schedules`)

    // Seed alerts
    console.log('🚨 Seeding realistic alerts...')
    const alerts = []
    for (let i = 0; i < 100; i++) {
      const transformer = transformers[Math.floor(Math.random() * transformers.length)]
      const alertType = alertTypes[Math.floor(Math.random() * alertTypes.length)]
      const status = ['active', 'acknowledged', 'resolved', 'escalated'][Math.floor(Math.random() * 4)]
      const createdDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

      const alert = {
        id: `alert-${String(i + 1).padStart(3, '0')}`,
        title: `${alertType.type} Alert - ${transformer.name}`,
        message: alertType.description,
        type: alertType.type.toLowerCase().replace(' ', '_'),
        severity: alertType.severity,
        status: status,
        transformerId: transformer.id,
        transformerSerial: transformer.serialNumber,
        transformerName: transformer.name,
        regionName: transformer.regionId.replace('region-', '').toUpperCase(),
        location: transformer.location.address,
        acknowledgedBy: status !== 'active' ? generateEthiopianName() : null,
        acknowledgedAt: status !== 'active' ? new Date(createdDate.getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString() : null,
        resolvedAt: status === 'resolved' ? new Date(createdDate.getTime() + Math.random() * 48 * 60 * 60 * 1000).toISOString() : null,
        escalationLevel: alertType.severity === 'critical' ? Math.floor(Math.random() * 3) + 1 : 0,
        affectedCustomers: Math.floor(Math.random() * 1000),
        estimatedRestoreTime: status === 'active' ? new Date(Date.now() + Math.random() * 12 * 60 * 60 * 1000).toISOString() : null,
        rootCause: status === 'resolved' ? `Root cause analysis for ${alertType.type.toLowerCase()} issue` : null,
        correctiveActions: status === 'resolved' ? `Corrective actions taken for ${alertType.type.toLowerCase()}` : null,
        createdAt: createdDate.toISOString()
      }

      alerts.push(alert)

      await connection.execute(
        `INSERT INTO app_alerts (id, title, message, type, severity, status, transformer_id, transformer_serial, transformer_name, region_name, location, acknowledged_by, acknowledged_at, resolved_at, escalation_level, affected_customers, estimated_restore_time, root_cause, corrective_actions, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
        [
          alert.id,
          alert.title,
          alert.message,
          alert.type,
          alert.severity,
          alert.status,
          alert.transformerId,
          alert.transformerSerial,
          alert.transformerName,
          alert.regionName,
          alert.location,
          alert.acknowledgedBy,
          alert.acknowledgedAt,
          alert.resolvedAt,
          alert.escalationLevel,
          alert.affectedCustomers,
          alert.estimatedRestoreTime,
          alert.rootCause,
          alert.correctiveActions,
          alert.createdAt
        ]
      )
    }
    console.log(`✅ Seeded ${alerts.length} realistic alerts`)

    // Seed sensor readings
    console.log('📊 Seeding sensor readings...')
    const sensorReadings = []
    for (let i = 0; i < 500; i++) {
      const transformer = transformers[Math.floor(Math.random() * transformers.length)]
      const readingDate = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
      const readings = generateSensorReadings()

      const sensorReading = {
        id: `sensor-${String(i + 1).padStart(4, '0')}`,
        transformerId: transformer.id,
        transformerSerial: transformer.serialNumber,
        sensorType: 'multi_sensor',
        readings: readings,
        timestamp: readingDate.toISOString(),
        dataQuality: ['excellent', 'good', 'fair', 'poor'][Math.floor(Math.random() * 4)],
        calibrationStatus: Math.random() > 0.1 ? 'calibrated' : 'needs_calibration',
        anomalyDetected: readings.temperature > 60 || readings.vibration > 4 || readings.oilLevel < 85,
        alertGenerated: readings.temperature > 65 || readings.vibration > 4.5 || readings.oilLevel < 80
      }

      sensorReadings.push(sensorReading)

      await connection.execute(
        `INSERT INTO app_sensor_readings (id, transformer_id, transformer_serial, sensor_type, readings, timestamp, data_quality, calibration_status, anomaly_detected, alert_generated, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          sensorReading.id,
          sensorReading.transformerId,
          sensorReading.transformerSerial,
          sensorReading.sensorType,
          JSON.stringify(sensorReading.readings),
          sensorReading.timestamp,
          sensorReading.dataQuality,
          sensorReading.calibrationStatus,
          sensorReading.anomalyDetected,
          sensorReading.alertGenerated
        ]
      )
    }
    console.log(`✅ Seeded ${sensorReadings.length} sensor readings`)

    // Seed work orders
    console.log('📋 Seeding work orders...')
    const workOrders = []
    for (let i = 0; i < 80; i++) {
      const maintenance = maintenanceSchedules[Math.floor(Math.random() * maintenanceSchedules.length)]
      const status = ['pending', 'assigned', 'in_progress', 'completed', 'cancelled'][Math.floor(Math.random() * 5)]
      const priority = ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)]

      const workOrder = {
        id: `wo-${String(i + 1).padStart(3, '0')}`,
        title: `Work Order - ${maintenance.title}`,
        description: `Work order for ${maintenance.description}`,
        type: maintenance.type,
        status: status,
        priority: priority,
        maintenanceScheduleId: maintenance.id,
        transformerId: maintenance.transformerId,
        transformerSerial: maintenance.transformerSerial,
        assignedTechnician: generateEthiopianName(),
        supervisorName: generateEthiopianName(),
        estimatedHours: Math.floor(Math.random() * 16) + 4,
        actualHours: status === 'completed' ? Math.floor(Math.random() * 20) + 2 : null,
        laborCost: Math.floor(Math.random() * 5000) + 1000,
        materialCost: Math.floor(Math.random() * 10000) + 2000,
        totalCost: 0, // Will be calculated
        workInstructions: `Detailed work instructions for ${maintenance.type}`,
        safetyRequirements: 'Standard safety protocols and PPE requirements',
        requiredTools: JSON.stringify(fieldEquipment.slice(0, Math.floor(Math.random() * 5) + 3)),
        completionNotes: status === 'completed' ? `Work completed successfully for ${maintenance.transformerSerial}` : null,
        qualityCheckPassed: status === 'completed' ? Math.random() > 0.1 : null,
        customerSatisfaction: status === 'completed' ? Math.floor(Math.random() * 3) + 3 : null
      }

      workOrder.totalCost = workOrder.laborCost + workOrder.materialCost
      workOrders.push(workOrder)

      await connection.execute(
        `INSERT INTO app_work_orders (id, title, description, type, status, priority, maintenance_schedule_id, transformer_id, transformer_serial, assigned_technician, supervisor_name, estimated_hours, actual_hours, labor_cost, material_cost, total_cost, work_instructions, safety_requirements, required_tools, completion_notes, quality_check_passed, customer_satisfaction, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          workOrder.id,
          workOrder.title,
          workOrder.description,
          workOrder.type,
          workOrder.status,
          workOrder.priority,
          workOrder.maintenanceScheduleId,
          workOrder.transformerId,
          workOrder.transformerSerial,
          workOrder.assignedTechnician,
          workOrder.supervisorName,
          workOrder.estimatedHours,
          workOrder.actualHours,
          workOrder.laborCost,
          workOrder.materialCost,
          workOrder.totalCost,
          workOrder.workInstructions,
          workOrder.safetyRequirements,
          JSON.stringify(workOrder.requiredTools),
          workOrder.completionNotes,
          workOrder.qualityCheckPassed,
          workOrder.customerSatisfaction
        ]
      )
    }
    console.log(`✅ Seeded ${workOrders.length} work orders`)

    // Seed notifications
    console.log('🔔 Seeding notifications...')
    const notifications = []
    for (let i = 0; i < 50; i++) {
      const user = users[Math.floor(Math.random() * users.length)]
      const type = ['system', 'maintenance', 'alert', 'reminder', 'update'][Math.floor(Math.random() * 5)]
      const priority = ['low', 'medium', 'high'][Math.floor(Math.random() * 3)]
      const isRead = Math.random() > 0.3

      const notification = {
        id: `notif-${String(i + 1).padStart(3, '0')}`,
        userId: user.id,
        title: `${type.charAt(0).toUpperCase() + type.slice(1)} Notification`,
        message: `Important ${type} notification for ${user.name}`,
        type: type,
        priority: priority,
        isRead: isRead,
        readAt: isRead ? new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString() : null,
        actionUrl: type === 'alert' ? '/alerts' : type === 'maintenance' ? '/maintenance' : '/dashboard',
        metadata: JSON.stringify({
          source: 'system',
          category: type,
          relatedEntity: type === 'maintenance' ? 'transformer' : 'system'
        })
      }

      notifications.push(notification)

      await connection.execute(
        `INSERT INTO app_notifications (id, user_id, title, message, type, priority, is_read, read_at, action_url, metadata, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          notification.id,
          notification.userId,
          notification.title,
          notification.message,
          notification.type,
          notification.priority,
          notification.isRead,
          notification.readAt,
          notification.actionUrl,
          notification.metadata
        ]
      )
    }
    console.log(`✅ Seeded ${notifications.length} notifications`)

    // Seed inventory items
    console.log('📦 Seeding inventory items...')
    const inventoryItems = []
    const inventoryCategories = [
      { name: 'Transformer Oil', unit: 'liters', minStock: 1000, maxStock: 5000 },
      { name: 'Bushings', unit: 'pieces', minStock: 10, maxStock: 50 },
      { name: 'Gaskets', unit: 'pieces', minStock: 50, maxStock: 200 },
      { name: 'Cooling Fans', unit: 'pieces', minStock: 5, maxStock: 25 },
      { name: 'Protection Relays', unit: 'pieces', minStock: 10, maxStock: 40 },
      { name: 'Tap Changer Parts', unit: 'sets', minStock: 5, maxStock: 20 },
      { name: 'Insulation Materials', unit: 'rolls', minStock: 20, maxStock: 100 },
      { name: 'Testing Equipment', unit: 'pieces', minStock: 3, maxStock: 15 }
    ]

    for (let i = 0; i < inventoryCategories.length; i++) {
      const category = inventoryCategories[i]
      const currentStock = Math.floor(Math.random() * (category.maxStock - category.minStock)) + category.minStock
      const unitCost = Math.floor(Math.random() * 1000) + 100

      const inventoryItem = {
        id: `inv-${String(i + 1).padStart(3, '0')}`,
        name: category.name,
        description: `High-quality ${category.name.toLowerCase()} for transformer maintenance`,
        category: 'maintenance_supplies',
        unit: category.unit,
        currentStock: currentStock,
        minStockLevel: category.minStock,
        maxStockLevel: category.maxStock,
        unitCost: unitCost,
        totalValue: currentStock * unitCost,
        supplier: ['ABB Supply', 'Siemens Parts', 'Local Supplier', 'International Parts'][Math.floor(Math.random() * 4)],
        lastRestocked: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        expiryDate: category.name.includes('Oil') ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
        location: `Warehouse ${String.fromCharCode(65 + Math.floor(Math.random() * 5))}-${Math.floor(Math.random() * 20) + 1}`,
        status: currentStock < category.minStock ? 'low_stock' : currentStock > category.maxStock * 0.8 ? 'overstocked' : 'normal'
      }

      inventoryItems.push(inventoryItem)

      await connection.execute(
        `INSERT INTO app_inventory_items (id, name, description, category, unit, current_stock, min_stock_level, max_stock_level, unit_cost, total_value, supplier, last_restocked, expiry_date, location, status, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          inventoryItem.id,
          inventoryItem.name,
          inventoryItem.description,
          inventoryItem.category,
          inventoryItem.unit,
          inventoryItem.currentStock,
          inventoryItem.minStockLevel,
          inventoryItem.maxStockLevel,
          inventoryItem.unitCost,
          inventoryItem.totalValue,
          inventoryItem.supplier,
          inventoryItem.lastRestocked,
          inventoryItem.expiryDate,
          inventoryItem.location,
          inventoryItem.status
        ]
      )
    }
    console.log(`✅ Seeded ${inventoryItems.length} inventory items`)

    // Seed performance metrics
    console.log('📈 Seeding performance metrics...')
    const performanceMetrics = []
    for (let i = 0; i < 100; i++) {
      const transformer = transformers[Math.floor(Math.random() * transformers.length)]
      const metricDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

      const performanceMetric = {
        id: `perf-${String(i + 1).padStart(3, '0')}`,
        transformerId: transformer.id,
        transformerSerial: transformer.serialNumber,
        metricType: 'daily_performance',
        efficiency: 95 + Math.random() * 4,
        availability: 98 + Math.random() * 2,
        reliability: 96 + Math.random() * 3,
        loadFactor: 0.6 + Math.random() * 0.3,
        powerFactor: 0.85 + Math.random() * 0.1,
        energyLosses: Math.random() * 5,
        maintenanceCost: Math.floor(Math.random() * 10000),
        downtimeHours: Math.random() * 24,
        faultCount: Math.floor(Math.random() * 5),
        customerComplaints: Math.floor(Math.random() * 3),
        environmentalImpact: Math.random() * 100,
        recordDate: metricDate.toISOString().split('T')[0],
        calculatedBy: 'automated_system',
        verifiedBy: Math.random() > 0.7 ? generateEthiopianName() : null,
        notes: `Performance metrics for ${transformer.serialNumber} on ${metricDate.toDateString()}`
      }

      performanceMetrics.push(performanceMetric)

      await connection.execute(
        `INSERT INTO app_performance_metrics (id, transformer_id, transformer_serial, metric_type, efficiency, availability, reliability, load_factor, power_factor, energy_losses, maintenance_cost, downtime_hours, fault_count, customer_complaints, environmental_impact, record_date, calculated_by, verified_by, notes, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          performanceMetric.id,
          performanceMetric.transformerId,
          performanceMetric.transformerSerial,
          performanceMetric.metricType,
          performanceMetric.efficiency,
          performanceMetric.availability,
          performanceMetric.reliability,
          performanceMetric.loadFactor,
          performanceMetric.powerFactor,
          performanceMetric.energyLosses,
          performanceMetric.maintenanceCost,
          performanceMetric.downtimeHours,
          performanceMetric.faultCount,
          performanceMetric.customerComplaints,
          performanceMetric.environmentalImpact,
          performanceMetric.recordDate,
          performanceMetric.calculatedBy,
          performanceMetric.verifiedBy,
          performanceMetric.notes
        ]
      )
    }
    console.log(`✅ Seeded ${performanceMetrics.length} performance metrics`)

    console.log('✅ Realistic data seeding completed successfully!')

  } catch (error) {
    console.error('❌ Error seeding realistic data:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}

// Run the seeding script
if (require.main === module) {
  seedRealisticData()
    .then(() => {
      console.log('🎉 Realistic data seeding completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Realistic data seeding failed:', error)
      process.exit(1)
    })
}

module.exports = { seedRealisticData }
