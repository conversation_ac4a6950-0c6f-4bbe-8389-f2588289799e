/**
 * MySQL Maintenance API Route
 * 
 * This API route handles maintenance schedule data requests from MySQL database.
 */

import { NextRequest, NextResponse } from 'next/server';
import { MySQLServerService } from '@/src/lib/mysql-server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 API: Fetching maintenance schedules from MySQL...');
    
    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();
    
    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        { 
          error: 'MySQL connection failed',
          fallback: true 
        },
        { status: 503 }
      );
    }
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const type = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    // Get maintenance schedules data
    const maintenanceSchedules = await MySQLServerService.getMaintenanceSchedules({
      status,
      priority,
      type,
      limit,
      offset
    });
    
    console.log(`✅ API: ${maintenanceSchedules.length} maintenance schedules fetched successfully from MySQL`);
    
    return NextResponse.json({
      success: true,
      data: maintenanceSchedules,
      count: maintenanceSchedules.length,
      source: 'mysql'
    });
    
  } catch (error) {
    console.error('❌ API: Error fetching maintenance schedules:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch maintenance schedules',
        message: error instanceof Error ? error.message : 'Unknown error',
        fallback: true
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 API: Creating new maintenance schedule...');
    
    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();
    
    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        { 
          error: 'MySQL connection failed',
          fallback: true 
        },
        { status: 503 }
      );
    }
    
    const scheduleData = await request.json();
    
    // Create new maintenance schedule
    const newSchedule = await MySQLServerService.createMaintenanceSchedule(scheduleData);
    
    console.log('✅ API: Maintenance schedule created successfully');
    
    return NextResponse.json({
      success: true,
      data: newSchedule,
      source: 'mysql'
    });
    
  } catch (error) {
    console.error('❌ API: Error creating maintenance schedule:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to create maintenance schedule',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    console.log('🔧 API: Updating maintenance schedule...');
    
    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();
    
    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        { 
          error: 'MySQL connection failed',
          fallback: true 
        },
        { status: 503 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    const scheduleId = searchParams.get('id');
    const updateData = await request.json();
    
    if (!scheduleId) {
      return NextResponse.json(
        { error: 'Schedule ID is required' },
        { status: 400 }
      );
    }
    
    // Update maintenance schedule
    const updatedSchedule = await MySQLServerService.updateMaintenanceSchedule(scheduleId, updateData);
    
    console.log('✅ API: Maintenance schedule updated successfully');
    
    return NextResponse.json({
      success: true,
      data: updatedSchedule,
      source: 'mysql'
    });
    
  } catch (error) {
    console.error('❌ API: Error updating maintenance schedule:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to update maintenance schedule',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
