"use client"

import { useState } from 'react'
import {
  Zap,
  Save,
  X,
  ChevronDown,
  MapPin,
  Calendar,
  Info,
  Layers,
  Percent,
  Thermometer,
  Droplet,
  Gauge,
  ArrowLeft
} from 'lucide-react'
import { MainLayout } from '@/src/components/layout/main-layout'
import { But<PERSON> } from '@/src/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Input } from '@/src/components/ui/input'
import { Label } from '@/src/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/src/components/ui/tabs'
import { Textarea } from '@/src/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/src/components/ui/select'
import { useRouter } from 'next/navigation'

export default function AddTransformerPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('basic')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    // Basic information
    serialNumber: '',
    manufacturer: '',
    model: '',
    type: '',
    ratingKVA: '',
    primaryVoltage: '',
    secondaryVoltage: '',
    installationDate: '',

    // Location information
    region: '',
    serviceCenter: '',
    latitude: '',
    longitude: '',
    address: '',

    // Technical specifications
    coolingType: '',
    oilType: '',
    impedance: '',
    weight: '',
    dimensions: '',
    connectionType: '',
    phaseCount: '',
    frequency: '',
    temperatureRise: '',
    insulationClass: '',
    efficiencyRating: '',
    noiseLevel: '',

    // Additional information
    manufacturingYear: '',
    warrantyExpiration: '',
    notes: ''
  })

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      console.log('Submitting transformer data:', formData)

      // Prepare data for API
      const transformerData = {
        serialNumber: formData.serialNumber,
        name: `${formData.manufacturer} ${formData.model} Transformer`,
        manufacturer: formData.manufacturer,
        model: formData.model,
        type: formData.type,
        capacity: parseInt(formData.ratingKVA) || 500,
        voltagePrimary: parseFloat(formData.primaryVoltage) || 11000,
        voltageSecondary: parseFloat(formData.secondaryVoltage) || 400,
        installationDate: formData.installationDate,
        locationName: formData.address,
        latitude: parseFloat(formData.latitude) || 0,
        longitude: parseFloat(formData.longitude) || 0,
        regionId: 1, // Default region ID, could be mapped from region name
        serviceCenterId: 1, // Default service center ID
        status: 'operational'
      }

      // Call API to create transformer
      const response = await fetch('/api/mysql/transformers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(transformerData)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        // Show success message
        alert(`Transformer added successfully! Serial Number: ${result.data.serialNumber}`)

        // Redirect to transformers list
        router.push('/transformers')
      } else {
        throw new Error(result.message || 'Failed to add transformer')
      }
    } catch (error) {
      console.error('Error adding transformer:', error)
      alert(`Failed to add transformer: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Cancel form submission
  const handleCancel = () => {
    if (confirm('Are you sure you want to cancel? All entered data will be lost.')) {
      router.push('/transformers')
    }
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "national_maintenance_manager",
        "regional_admin",
        "regional_asset_manager",
        "regional_maintenance_engineer",
        "service_center_manager"
      ]}
      requiredPermissions={[{ resource: "transformers", action: "create" }]}
    >
      <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <Zap className="text-green-600 mr-2" size={24} />
              <h1 className="text-2xl font-bold">Add New Transformer</h1>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={handleCancel}>
                <X size={16} className="mr-2" />
                Cancel
              </Button>

              <Button
                className="bg-green-600 hover:bg-green-700"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                <Save size={16} className="mr-2" />
                {isSubmitting ? 'Saving...' : 'Save Transformer'}
              </Button>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
              <TabsList className="grid grid-cols-4 w-full">
                <TabsTrigger value="basic" className="flex items-center">
                  <Info size={16} className="mr-2" />
                  Basic Information
                </TabsTrigger>
                <TabsTrigger value="location" className="flex items-center">
                  <MapPin size={16} className="mr-2" />
                  Location
                </TabsTrigger>
                <TabsTrigger value="technical" className="flex items-center">
                  <Gauge size={16} className="mr-2" />
                  Technical Specifications
                </TabsTrigger>
                <TabsTrigger value="additional" className="flex items-center">
                  <Layers size={16} className="mr-2" />
                  Additional Information
                </TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="serialNumber">Serial Number <span className="text-red-500">*</span></Label>
                          <Input
                            id="serialNumber"
                            name="serialNumber"
                            placeholder="e.g., EEU-TR-001"
                            value={formData.serialNumber}
                            onChange={handleInputChange}
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="manufacturer">Manufacturer <span className="text-red-500">*</span></Label>
                          <Select
                            value={formData.manufacturer}
                            onValueChange={(value) => handleSelectChange('manufacturer', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select manufacturer" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="ABB">ABB</SelectItem>
                              <SelectItem value="Siemens">Siemens</SelectItem>
                              <SelectItem value="Schneider Electric">Schneider Electric</SelectItem>
                              <SelectItem value="General Electric">General Electric</SelectItem>
                              <SelectItem value="Other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="model">Model <span className="text-red-500">*</span></Label>
                          <Input
                            id="model"
                            name="model"
                            placeholder="e.g., PowerTrans X3"
                            value={formData.model}
                            onChange={handleInputChange}
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="type">Type <span className="text-red-500">*</span></Label>
                          <Select
                            value={formData.type}
                            onValueChange={(value) => handleSelectChange('type', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="distribution">Distribution</SelectItem>
                              <SelectItem value="power">Power</SelectItem>
                              <SelectItem value="auto">Auto</SelectItem>
                              <SelectItem value="pad-mounted">Pad-mounted</SelectItem>
                              <SelectItem value="pole-mounted">Pole-mounted</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="ratingKVA">Rating (kVA) <span className="text-red-500">*</span></Label>
                          <Input
                            id="ratingKVA"
                            name="ratingKVA"
                            type="number"
                            placeholder="e.g., 500"
                            value={formData.ratingKVA}
                            onChange={handleInputChange}
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="primaryVoltage">Primary Voltage (V) <span className="text-red-500">*</span></Label>
                          <Input
                            id="primaryVoltage"
                            name="primaryVoltage"
                            type="number"
                            placeholder="e.g., 11000"
                            value={formData.primaryVoltage}
                            onChange={handleInputChange}
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="secondaryVoltage">Secondary Voltage (V) <span className="text-red-500">*</span></Label>
                          <Input
                            id="secondaryVoltage"
                            name="secondaryVoltage"
                            type="number"
                            placeholder="e.g., 400"
                            value={formData.secondaryVoltage}
                            onChange={handleInputChange}
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="installationDate">Installation Date <span className="text-red-500">*</span></Label>
                          <div className="relative">
                            <Input
                              id="installationDate"
                              name="installationDate"
                              type="date"
                              value={formData.installationDate}
                              onChange={handleInputChange}
                              required
                            />
                            <Calendar className="absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none" />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mt-6 flex justify-end">
                      <Button
                        type="button"
                        onClick={() => setActiveTab('location')}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Next: Location
                        <ChevronDown className="ml-2 h-4 w-4 rotate-270" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="location" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Location Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="region">Region <span className="text-red-500">*</span></Label>
                          <Select
                            value={formData.region}
                            onValueChange={(value) => handleSelectChange('region', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select region" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Addis Ababa">Addis Ababa</SelectItem>
                              <SelectItem value="Oromia">Oromia</SelectItem>
                              <SelectItem value="Amhara">Amhara</SelectItem>
                              <SelectItem value="SNNPR">SNNPR</SelectItem>
                              <SelectItem value="Tigray">Tigray</SelectItem>
                              <SelectItem value="Afar">Afar</SelectItem>
                              <SelectItem value="Somali">Somali</SelectItem>
                              <SelectItem value="Benishangul-Gumuz">Benishangul-Gumuz</SelectItem>
                              <SelectItem value="Gambela">Gambela</SelectItem>
                              <SelectItem value="Harari">Harari</SelectItem>
                              <SelectItem value="Dire Dawa">Dire Dawa</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="serviceCenter">Service Center <span className="text-red-500">*</span></Label>
                          <Input
                            id="serviceCenter"
                            name="serviceCenter"
                            placeholder="e.g., Bole"
                            value={formData.serviceCenter}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="latitude">Latitude <span className="text-red-500">*</span></Label>
                            <Input
                              id="latitude"
                              name="latitude"
                              placeholder="e.g., 9.0222"
                              value={formData.latitude}
                              onChange={handleInputChange}
                              required
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="longitude">Longitude <span className="text-red-500">*</span></Label>
                            <Input
                              id="longitude"
                              name="longitude"
                              placeholder="e.g., 38.7468"
                              value={formData.longitude}
                              onChange={handleInputChange}
                              required
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="address">Address <span className="text-red-500">*</span></Label>
                          <Textarea
                            id="address"
                            name="address"
                            placeholder="e.g., Bole Sub-district, Addis Ababa"
                            value={formData.address}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div className="mt-6 flex justify-between">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setActiveTab('basic')}
                      >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back: Basic Information
                      </Button>

                      <Button
                        type="button"
                        onClick={() => setActiveTab('technical')}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Next: Technical Specifications
                        <ChevronDown className="ml-2 h-4 w-4 rotate-270" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="technical" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Technical Specifications</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="coolingType">Cooling Type</Label>
                          <Select
                            value={formData.coolingType}
                            onValueChange={(value) => handleSelectChange('coolingType', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select cooling type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="ONAN">ONAN (Oil Natural Air Natural)</SelectItem>
                              <SelectItem value="ONAF">ONAF (Oil Natural Air Forced)</SelectItem>
                              <SelectItem value="OFAF">OFAF (Oil Forced Air Forced)</SelectItem>
                              <SelectItem value="OFWF">OFWF (Oil Forced Water Forced)</SelectItem>
                              <SelectItem value="ODAF">ODAF (Oil Directed Air Forced)</SelectItem>
                              <SelectItem value="ODWF">ODWF (Oil Directed Water Forced)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="oilType">Oil Type</Label>
                          <Select
                            value={formData.oilType}
                            onValueChange={(value) => handleSelectChange('oilType', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select oil type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="mineral">Mineral Oil</SelectItem>
                              <SelectItem value="silicone">Silicone Oil</SelectItem>
                              <SelectItem value="synthetic">Synthetic Ester</SelectItem>
                              <SelectItem value="natural">Natural Ester</SelectItem>
                              <SelectItem value="dry">Dry Type (No Oil)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="impedance">Impedance (%)</Label>
                          <Input
                            id="impedance"
                            name="impedance"
                            placeholder="e.g., 4.5"
                            value={formData.impedance}
                            onChange={handleInputChange}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="weight">Weight (kg)</Label>
                          <Input
                            id="weight"
                            name="weight"
                            placeholder="e.g., 1500"
                            value={formData.weight}
                            onChange={handleInputChange}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="dimensions">Dimensions (mm)</Label>
                          <Input
                            id="dimensions"
                            name="dimensions"
                            placeholder="e.g., 1200x800x1500"
                            value={formData.dimensions}
                            onChange={handleInputChange}
                          />
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="connectionType">Connection Type</Label>
                          <Select
                            value={formData.connectionType}
                            onValueChange={(value) => handleSelectChange('connectionType', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select connection type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="delta-wye">Delta-Wye (Δ-Y)</SelectItem>
                              <SelectItem value="delta-delta">Delta-Delta (Δ-Δ)</SelectItem>
                              <SelectItem value="wye-wye">Wye-Wye (Y-Y)</SelectItem>
                              <SelectItem value="wye-delta">Wye-Delta (Y-Δ)</SelectItem>
                              <SelectItem value="zigzag">Zigzag</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="phaseCount">Phase Count</Label>
                          <Select
                            value={formData.phaseCount}
                            onValueChange={(value) => handleSelectChange('phaseCount', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select phase count" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">Single Phase</SelectItem>
                              <SelectItem value="3">Three Phase</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="frequency">Frequency (Hz)</Label>
                          <Select
                            value={formData.frequency}
                            onValueChange={(value) => handleSelectChange('frequency', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select frequency" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="50">50 Hz</SelectItem>
                              <SelectItem value="60">60 Hz</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="temperatureRise">Temperature Rise (°C)</Label>
                          <Input
                            id="temperatureRise"
                            name="temperatureRise"
                            placeholder="e.g., 65"
                            value={formData.temperatureRise}
                            onChange={handleInputChange}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="insulationClass">Insulation Class</Label>
                          <Select
                            value={formData.insulationClass}
                            onValueChange={(value) => handleSelectChange('insulationClass', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select insulation class" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="A">Class A (105°C)</SelectItem>
                              <SelectItem value="B">Class B (130°C)</SelectItem>
                              <SelectItem value="F">Class F (155°C)</SelectItem>
                              <SelectItem value="H">Class H (180°C)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    <div className="mt-6 flex justify-between">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setActiveTab('location')}
                      >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back: Location
                      </Button>

                      <Button
                        type="button"
                        onClick={() => setActiveTab('additional')}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Next: Additional Information
                        <ChevronDown className="ml-2 h-4 w-4 rotate-270" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="additional" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Additional Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="manufacturingYear">Manufacturing Year</Label>
                          <Input
                            id="manufacturingYear"
                            name="manufacturingYear"
                            placeholder="e.g., 2020"
                            value={formData.manufacturingYear}
                            onChange={handleInputChange}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="warrantyExpiration">Warranty Expiration Date</Label>
                          <div className="relative">
                            <Input
                              id="warrantyExpiration"
                              name="warrantyExpiration"
                              type="date"
                              value={formData.warrantyExpiration}
                              onChange={handleInputChange}
                            />
                            <Calendar className="absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none" />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="notes">Notes</Label>
                          <Textarea
                            id="notes"
                            name="notes"
                            placeholder="Enter any additional notes or comments"
                            value={formData.notes}
                            onChange={handleInputChange}
                            rows={5}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="mt-6 flex justify-between">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setActiveTab('technical')}
                      >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back: Technical Specifications
                      </Button>

                      <Button
                        type="submit"
                        className="bg-green-600 hover:bg-green-700"
                        disabled={isSubmitting}
                      >
                        <Save className="mr-2 h-4 w-4" />
                        {isSubmitting ? 'Saving...' : 'Save Transformer'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </form>
      </div>
    </MainLayout>
  )
}
