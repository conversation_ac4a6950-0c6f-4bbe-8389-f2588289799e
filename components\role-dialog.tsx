"use client"

import type React from "react"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Checkbox } from "@/src/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { ScrollArea } from "@/src/components/ui/scroll-area"
import type { RoleDefinition, OrganizationalLevel } from "@/src/types/auth"

interface RoleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  role: RoleDefinition | null
  onSave: (role: RoleDefinition) => void
}

export function RoleDialog({ open, onOpenChange, role, onSave }: RoleDialogProps) {
  const [formData, setFormData] = useState<Partial<RoleDefinition>>({
    name: "",
    description: "",
    organizationalLevel: "service_center",
    permissions: [],
    isSystemRole: false,
  })

  // Reset form when dialog opens or role changes
  useEffect(() => {
    if (role) {
      setFormData({
        ...role,
      })
    } else {
      setFormData({
        name: "",
        description: "",
        organizationalLevel: "service_center",
        permissions: [],
        isSystemRole: false,
      })
    }
  }, [role, open])

  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave({
      id: role?.id || `role-${Date.now()}`,
      name: formData.name || "",
      description: formData.description || "",
      organizationalLevel: formData.organizationalLevel as OrganizationalLevel,
      permissions: formData.permissions || [],
      isSystemRole: role?.isSystemRole || false,
    })
  }

  const togglePermission = (resource: string, action: string) => {
    const permission = { resource, action }
    const currentPermissions = formData.permissions || []

    const exists = currentPermissions.some((p) => p.resource === permission.resource && p.action === permission.action)

    if (exists) {
      const updatedPermissions = currentPermissions.filter(
        (p) => !(p.resource === permission.resource && p.action === permission.action),
      )
      handleChange("permissions", updatedPermissions)
    } else {
      handleChange("permissions", [...currentPermissions, permission])
    }
  }

  const hasPermission = (resource: string, action: string) => {
    return (formData.permissions || []).some((p) => p.resource === resource && p.action === action)
  }

  // Available resources and actions
  const resources = [
    {
      name: "users",
      label: "Users",
      actions: ["create", "read", "update", "delete"],
    },
    {
      name: "roles",
      label: "Roles",
      actions: ["create", "read", "update", "delete"],
    },
    {
      name: "transformers",
      label: "Transformers",
      actions: ["create", "read", "update", "delete", "approve"],
    },
    {
      name: "maintenance",
      label: "Maintenance",
      actions: ["create", "read", "update", "delete", "approve", "assign"],
    },
    {
      name: "outages",
      label: "Outages",
      actions: ["create", "read", "update", "delete"],
    },
    {
      name: "customer_requests",
      label: "Customer Requests",
      actions: ["create", "read", "update", "delete"],
    },
    {
      name: "reports",
      label: "Reports",
      actions: ["create", "read", "update", "delete", "export"],
    },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>{role ? "Edit Role" : "Add New Role"}</DialogTitle>
            <DialogDescription>
              {role ? "Update role details and permissions." : "Create a new role with specific permissions."}
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="details" className="mt-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Role Details</TabsTrigger>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
            </TabsList>
            <TabsContent value="details" className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Role Name</Label>
                <Input
                  id="name"
                  value={formData.name || ""}
                  onChange={(e) => handleChange("name", e.target.value)}
                  required
                  disabled={role?.isSystemRole}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={formData.description || ""}
                  onChange={(e) => handleChange("description", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="organizationalLevel">Organizational Level</Label>
                <Select
                  value={formData.organizationalLevel}
                  onValueChange={(value) => handleChange("organizationalLevel", value)}
                  disabled={role?.isSystemRole}
                >
                  <SelectTrigger id="organizationalLevel">
                    <SelectValue placeholder="Select level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="head_office">Head Office</SelectItem>
                    <SelectItem value="regional_office">Regional Office</SelectItem>
                    <SelectItem value="service_center">Service Center</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
            <TabsContent value="permissions" className="py-4">
              <ScrollArea className="h-[300px] pr-4">
                <div className="space-y-6">
                  {resources.map((resource) => (
                    <div key={resource.name} className="space-y-2">
                      <h4 className="font-medium">{resource.label}</h4>
                      <div className="grid grid-cols-2 gap-2">
                        {resource.actions.map((action) => (
                          <div key={`${resource.name}-${action}`} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${resource.name}-${action}`}
                              checked={hasPermission(resource.name, action)}
                              onCheckedChange={() => togglePermission(resource.name, action)}
                              disabled={role?.isSystemRole}
                            />
                            <Label htmlFor={`${resource.name}-${action}`} className="capitalize">
                              {action}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
          <DialogFooter className="mt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={role?.isSystemRole}>
              {role ? "Update Role" : "Create Role"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
