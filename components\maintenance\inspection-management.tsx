"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Textarea } from "@/src/components/ui/textarea"
import { Progress } from "@/src/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/src/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/src/components/ui/dialog"
import {
  Search,
  Eye,
  Plus,
  Filter,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  MapPin,
  FileText,
  Camera,
  Thermometer,
  Zap,
  Download,
  RefreshCw,
  Calendar,
  Settings,
  Activity,
  BarChart3,
  TrendingUp,
  Shield
} from 'lucide-react'
import { format } from 'date-fns'
import { maintenanceDatabaseService } from '@/src/services/maintenance-database-service'

interface InspectionRecord {
  id: string
  transformerId: string
  transformerLocation: string
  inspectionType: 'routine' | 'thermal' | 'electrical' | 'oil_analysis' | 'visual' | 'comprehensive'
  status: 'scheduled' | 'in_progress' | 'completed' | 'failed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'critical'
  scheduledDate: string
  completedDate?: string
  inspector: string
  duration: number
  findings: {
    id: string
    category: string
    description: string
    severity: 'low' | 'medium' | 'high' | 'critical'
    status: 'open' | 'resolved'
    images?: string[]
  }[]
  measurements: {
    temperature?: number
    voltage?: number
    current?: number
    resistance?: number
    oilLevel?: number
    moistureContent?: number
  }
  recommendations: string[]
  nextInspectionDue?: string
  reportGenerated: boolean
  attachments: string[]
}

interface InspectionManagementProps {
  className?: string
}

export function InspectionManagement({ className }: InspectionManagementProps) {
  const [inspections, setInspections] = useState<InspectionRecord[]>([])
  const [filteredInspections, setFilteredInspections] = useState<InspectionRecord[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [selectedInspection, setSelectedInspection] = useState<InspectionRecord | null>(null)
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    type: 'all',
    priority: 'all'
  })

  // Mock data for development
  const mockInspections: InspectionRecord[] = [
    {
      id: 'INS-001',
      transformerId: 'T-AA-001',
      transformerLocation: 'Addis Ababa - Sector 1',
      inspectionType: 'thermal',
      status: 'completed',
      priority: 'medium',
      scheduledDate: '2024-02-10T09:00:00Z',
      completedDate: '2024-02-10T11:30:00Z',
      inspector: 'John Doe',
      duration: 2.5,
      findings: [
        {
          id: '1',
          category: 'Thermal',
          description: 'Elevated temperature detected on bushing A',
          severity: 'medium',
          status: 'open',
          images: ['thermal_001.jpg']
        }
      ],
      measurements: {
        temperature: 85,
        voltage: 11000,
        current: 150,
        resistance: 0.5
      },
      recommendations: [
        'Monitor temperature closely',
        'Schedule detailed bushing inspection',
        'Check cooling system efficiency'
      ],
      nextInspectionDue: '2024-05-10T09:00:00Z',
      reportGenerated: true,
      attachments: ['inspection_report_001.pdf', 'thermal_images.zip']
    },
    {
      id: 'INS-002',
      transformerId: 'T-OR-045',
      transformerLocation: 'Oromia - Industrial Zone',
      inspectionType: 'oil_analysis',
      status: 'in_progress',
      priority: 'high',
      scheduledDate: '2024-02-15T08:00:00Z',
      inspector: 'Jane Smith',
      duration: 4,
      findings: [],
      measurements: {
        oilLevel: 95,
        moistureContent: 15
      },
      recommendations: [],
      reportGenerated: false,
      attachments: []
    },
    {
      id: 'INS-003',
      transformerId: 'T-AM-023',
      transformerLocation: 'Amhara - Distribution Center',
      inspectionType: 'routine',
      status: 'scheduled',
      priority: 'low',
      scheduledDate: '2024-02-20T10:00:00Z',
      inspector: 'Bob Johnson',
      duration: 3,
      findings: [],
      measurements: {},
      recommendations: [],
      reportGenerated: false,
      attachments: []
    }
  ]

  // Load inspection records
  const loadInspections = async () => {
    try {
      setIsLoading(true)
      // Fetch inspection records from database
      const maintenanceRecords = await maintenanceDatabaseService.getMaintenanceRecords({
        type: ['inspection']
      })

      // Convert to inspection records format
      const inspectionRecords = maintenanceRecords.map(record => ({
        id: record.id,
        transformerId: record.transformerId,
        transformerLocation: `Location for ${record.transformerId}`,
        inspectionType: 'routine' as const,
        status: record.status as 'scheduled' | 'in_progress' | 'completed' | 'failed' | 'cancelled',
        priority: record.priority as 'low' | 'medium' | 'high' | 'critical',
        scheduledDate: record.scheduledDate,
        completedDate: record.completedDate,
        inspector: record.assignedTo,
        duration: record.estimatedDuration || 3,
        findings: [],
        measurements: {},
        recommendations: [],
        reportGenerated: false,
        attachments: []
      }))

      // Fallback to mock data if no records found
      const finalInspections = inspectionRecords.length > 0 ? inspectionRecords : mockInspections
      setInspections(finalInspections)
      setFilteredInspections(finalInspections)
    } catch (error) {
      console.error('Error loading inspection records:', error)
      // Use mock data as fallback
      setInspections(mockInspections)
      setFilteredInspections(mockInspections)
    } finally {
      setIsLoading(false)
    }
  }

  // Apply filters
  const applyFilters = () => {
    let filtered = [...inspections]

    // Search filter
    if (filters.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(inspection =>
        inspection.transformerId.toLowerCase().includes(search) ||
        inspection.inspector.toLowerCase().includes(search) ||
        inspection.transformerLocation.toLowerCase().includes(search)
      )
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(inspection => inspection.status === filters.status)
    }

    // Type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(inspection => inspection.inspectionType === filters.type)
    }

    // Priority filter
    if (filters.priority !== 'all') {
      filtered = filtered.filter(inspection => inspection.priority === filters.priority)
    }

    setFilteredInspections(filtered)
  }

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'failed': return 'bg-red-100 text-red-800'
      case 'cancelled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'critical': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get inspection type icon
  const getInspectionTypeIcon = (type: string) => {
    switch (type) {
      case 'thermal': return <Thermometer className="h-4 w-4" />
      case 'electrical': return <Zap className="h-4 w-4" />
      case 'oil_analysis': return <FileText className="h-4 w-4" />
      case 'visual': return <Eye className="h-4 w-4" />
      case 'comprehensive': return <Shield className="h-4 w-4" />
      default: return <Search className="h-4 w-4" />
    }
  }

  // Handle inspection actions
  const handleStartInspection = async (inspectionId: string) => {
    try {
      setInspections(prev => prev.map(inspection =>
        inspection.id === inspectionId
          ? { ...inspection, status: 'in_progress' as const }
          : inspection
      ))
    } catch (error) {
      console.error('Error starting inspection:', error)
    }
  }

  const handleCompleteInspection = async (inspectionId: string) => {
    try {
      setInspections(prev => prev.map(inspection =>
        inspection.id === inspectionId
          ? {
              ...inspection,
              status: 'completed' as const,
              completedDate: new Date().toISOString()
            }
          : inspection
      ))
    } catch (error) {
      console.error('Error completing inspection:', error)
    }
  }

  const handleCreateInspection = async () => {
    // This would open a create inspection dialog
    setShowCreateDialog(true)
  }

  const handleViewDetails = (inspection: InspectionRecord) => {
    setSelectedInspection(inspection)
  }

  useEffect(() => {
    loadInspections()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [filters, inspections])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Inspection Management</h2>
          <p className="text-muted-foreground">
            Comprehensive transformer inspection tracking and management
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={loadInspections}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={handleCreateInspection}>
            <Plus className="h-4 w-4 mr-2" />
            Schedule Inspection
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">{inspections.length}</p>
              </div>
              <Search className="h-8 w-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Scheduled</p>
                <p className="text-2xl font-bold text-blue-600">
                  {inspections.filter(i => i.status === 'scheduled').length}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">In Progress</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {inspections.filter(i => i.status === 'in_progress').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold text-green-600">
                  {inspections.filter(i => i.status === 'completed').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Issues Found</p>
                <p className="text-2xl font-bold text-red-600">
                  {inspections.reduce((acc, i) => acc + i.findings.length, 0)}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="inspections">Inspections</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Search</label>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search inspections..."
                      value={filters.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>

                {/* Status Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Status</label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => handleFilterChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Type Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Type</label>
                  <Select
                    value={filters.type}
                    onValueChange={(value) => handleFilterChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="routine">Routine</SelectItem>
                      <SelectItem value="thermal">Thermal</SelectItem>
                      <SelectItem value="electrical">Electrical</SelectItem>
                      <SelectItem value="oil_analysis">Oil Analysis</SelectItem>
                      <SelectItem value="visual">Visual</SelectItem>
                      <SelectItem value="comprehensive">Comprehensive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Priority Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Priority</label>
                  <Select
                    value={filters.priority}
                    onValueChange={(value) => handleFilterChange('priority', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All priorities" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priorities</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Results Summary */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>
              Showing {filteredInspections.length} of {inspections.length} inspections
            </span>
            {filters.search && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilterChange('search', '')}
              >
                Clear search
              </Button>
            )}
          </div>

          {/* Inspections Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredInspections.map((inspection) => (
              <Card key={inspection.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg flex items-center gap-2">
                        {getInspectionTypeIcon(inspection.inspectionType)}
                        {inspection.inspectionType.replace('_', ' ').charAt(0).toUpperCase() +
                         inspection.inspectionType.replace('_', ' ').slice(1)} Inspection
                      </CardTitle>
                      <CardDescription className="flex items-center gap-2 mt-1">
                        <MapPin className="h-4 w-4" />
                        {inspection.transformerId} - {inspection.transformerLocation}
                      </CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Badge className={getStatusColor(inspection.status)}>
                        {inspection.status.replace('_', ' ').charAt(0).toUpperCase() +
                         inspection.status.replace('_', ' ').slice(1)}
                      </Badge>
                      <Badge className={getPriorityColor(inspection.priority)}>
                        {inspection.priority.charAt(0).toUpperCase() + inspection.priority.slice(1)}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Inspection Info */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Scheduled Date</p>
                      <p className="font-medium">
                        {format(new Date(inspection.scheduledDate), 'MMM d, yyyy HH:mm')}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Inspector</p>
                      <p className="font-medium flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {inspection.inspector}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Duration</p>
                      <p className="font-medium flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {inspection.duration}h
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Findings</p>
                      <p className="font-medium">
                        {inspection.findings.length} issue{inspection.findings.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>

                  {/* Findings Summary */}
                  {inspection.findings.length > 0 && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">Recent Findings</p>
                      <div className="space-y-1">
                        {inspection.findings.slice(0, 2).map((finding) => (
                          <div key={finding.id} className="flex items-center gap-2 text-sm">
                            <AlertTriangle className={`h-4 w-4 ${
                              finding.severity === 'critical' ? 'text-red-500' :
                              finding.severity === 'high' ? 'text-orange-500' :
                              finding.severity === 'medium' ? 'text-yellow-500' : 'text-green-500'
                            }`} />
                            <span className="truncate">{finding.description}</span>
                          </div>
                        ))}
                        {inspection.findings.length > 2 && (
                          <p className="text-xs text-muted-foreground">
                            +{inspection.findings.length - 2} more findings
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex items-center gap-2 pt-2">
                    <Button size="sm" variant="outline" onClick={() => handleViewDetails(inspection)}>
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                    {inspection.status === 'scheduled' && (
                      <Button size="sm" onClick={() => handleStartInspection(inspection.id)}>
                        <Activity className="h-4 w-4 mr-2" />
                        Start Inspection
                      </Button>
                    )}
                    {inspection.status === 'in_progress' && (
                      <Button size="sm" onClick={() => handleCompleteInspection(inspection.id)}>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Complete
                      </Button>
                    )}
                    {inspection.reportGenerated && (
                      <Button size="sm" variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Report
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="inspections" className="space-y-4">
          {/* Inspections Table */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Transformer</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Scheduled Date</TableHead>
                    <TableHead>Inspector</TableHead>
                    <TableHead>Findings</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                          <span className="ml-2">Loading inspections...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredInspections.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                        No inspections found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredInspections.map((inspection) => (
                      <TableRow key={inspection.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{inspection.transformerId}</div>
                            <div className="text-sm text-muted-foreground">{inspection.transformerLocation}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getInspectionTypeIcon(inspection.inspectionType)}
                            <span className="capitalize">
                              {inspection.inspectionType.replace('_', ' ')}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(inspection.status)}>
                            {inspection.status.replace('_', ' ').charAt(0).toUpperCase() +
                             inspection.status.replace('_', ' ').slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getPriorityColor(inspection.priority)}>
                            {inspection.priority.charAt(0).toUpperCase() + inspection.priority.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div>{format(new Date(inspection.scheduledDate), 'MMM d, yyyy')}</div>
                            <div className="text-sm text-muted-foreground">
                              {format(new Date(inspection.scheduledDate), 'HH:mm')}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{inspection.inspector}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {inspection.findings.length > 0 ? (
                              <>
                                <AlertTriangle className="h-4 w-4 text-orange-500" />
                                <span className="text-sm">{inspection.findings.length}</span>
                              </>
                            ) : (
                              <span className="text-sm text-muted-foreground">None</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewDetails(inspection)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {inspection.status === 'scheduled' && (
                                <DropdownMenuItem onClick={() => handleStartInspection(inspection.id)}>
                                  <Activity className="mr-2 h-4 w-4" />
                                  Start Inspection
                                </DropdownMenuItem>
                              )}
                              {inspection.status === 'in_progress' && (
                                <DropdownMenuItem onClick={() => handleCompleteInspection(inspection.id)}>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Complete
                                </DropdownMenuItem>
                              )}
                              {inspection.reportGenerated && (
                                <DropdownMenuItem>
                                  <Download className="mr-2 h-4 w-4" />
                                  Download Report
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Inspection Reports
              </CardTitle>
              <CardDescription>
                Generated inspection reports and documentation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  Inspection Reports
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Access and manage inspection reports, findings documentation,
                  and compliance certificates.
                </p>
                <div className="text-sm text-gray-500">
                  Report management features coming soon
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Inspection Analytics
              </CardTitle>
              <CardDescription>
                Performance metrics and inspection trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <TrendingUp className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  Analytics Dashboard
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Comprehensive analytics for inspection performance,
                  findings trends, and compliance tracking.
                </p>
                <div className="text-sm text-gray-500">
                  Advanced analytics features coming soon
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Inspection Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Schedule New Inspection</DialogTitle>
            <DialogDescription>
              Create a new inspection schedule for transformer monitoring.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Transformer ID</label>
                <Input placeholder="T-XXX-XXX" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Inspection Type</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="routine">Routine</SelectItem>
                    <SelectItem value="thermal">Thermal</SelectItem>
                    <SelectItem value="electrical">Electrical</SelectItem>
                    <SelectItem value="oil_analysis">Oil Analysis</SelectItem>
                    <SelectItem value="visual">Visual</SelectItem>
                    <SelectItem value="comprehensive">Comprehensive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Priority</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Inspector</label>
                <Input placeholder="Inspector name" />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Notes</label>
              <Textarea placeholder="Additional notes or special instructions..." />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowCreateDialog(false)}>
              Schedule Inspection
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
