const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Create directory if it doesn't exist
const imagesDir = path.join(__dirname, '../public/static/images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Generate image placeholder
function generatePlaceholder(width, height, filename, bgColor = '#f3f4f6', textColor = '#9ca3af') {
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // Background
  ctx.fillStyle = bgColor;
  ctx.fillRect(0, 0, width, height);

  // Text
  ctx.fillStyle = textColor;
  ctx.font = `${Math.floor(width / 10)}px Arial`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(`${width}×${height}`, width / 2, height / 2);

  // Save to file
  const buffer = canvas.toBuffer('image/jpeg');
  fs.writeFileSync(path.join(imagesDir, filename), buffer);
  console.log(`Generated ${filename}`);
}

// Generate avatar placeholder
function generateAvatarPlaceholder(size, filename) {
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  // Background
  ctx.fillStyle = '#e5e7eb';
  ctx.beginPath();
  ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
  ctx.fill();

  // Person silhouette
  ctx.fillStyle = '#9ca3af';
  
  // Head
  const headRadius = size * 0.2;
  ctx.beginPath();
  ctx.arc(size / 2, size * 0.4, headRadius, 0, Math.PI * 2);
  ctx.fill();
  
  // Body
  ctx.beginPath();
  ctx.moveTo(size / 2 - size * 0.25, size * 0.8);
  ctx.quadraticCurveTo(size / 2, size * 0.5, size / 2 + size * 0.25, size * 0.8);
  ctx.closePath();
  ctx.fill();

  // Save to file
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(path.join(imagesDir, filename), buffer);
  console.log(`Generated ${filename}`);
}

// Generate placeholders
generatePlaceholder(1200, 800, 'image-placeholder.jpg');
generatePlaceholder(800, 600, 'image-placeholder-landscape.jpg');
generatePlaceholder(600, 800, 'image-placeholder-portrait.jpg');
generateAvatarPlaceholder(200, 'avatar-placeholder.png');

console.log('All placeholder images generated successfully!');
