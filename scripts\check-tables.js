/**
 * Check Database Tables and Structure
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function checkTables() {
  let connection;
  
  try {
    console.log('🔌 Connecting to MySQL database...');
    connection = await mysql.createConnection(config);
    console.log('✅ Connected successfully');

    // Show all tables
    console.log('\n📋 Available tables:');
    const [tables] = await connection.execute('SHOW TABLES');
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });

    // Check structure of key tables
    const tablesToCheck = ['app_transformers', 'app_maintenance_schedules', 'app_alerts'];
    
    for (const tableName of tablesToCheck) {
      try {
        console.log(`\n🔍 Structure of ${tableName}:`);
        const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
        columns.forEach(col => {
          console.log(`  - ${col.Field} (${col.Type}) ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
        });
      } catch (error) {
        console.log(`  ❌ Table ${tableName} does not exist`);
      }
    }

    // Check if there's any data
    console.log('\n📊 Data count in tables:');
    for (const tableName of tablesToCheck) {
      try {
        const [result] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`  - ${tableName}: ${result[0].count} records`);
      } catch (error) {
        console.log(`  - ${tableName}: Table does not exist`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

checkTables();
