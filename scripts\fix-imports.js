#!/usr/bin/env node

/**
 * Fix Import Paths Script
 * Updates all import paths to use the new reorganized structure
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 Fixing import paths...\n')

// Import mappings for the reorganized structure
const importMappings = [
  // UI Components
  { from: '@/src/components/ui/', to: '@/src/components/ui/' },
  
  // Layout Components
  { from: '@/src/components/layout/main-layout', to: '@/src/components/layout/main-layout' },
  { from: '@/src/components/layout/sidebar', to: '@/src/components/layout/sidebar' },
  { from: '@/src/components/layout/header', to: '@/src/components/layout/header' },
  { from: '@/src/components/layout/footer', to: '@/src/components/layout/footer' },
  { from: '@/src/components/layout/protected-route', to: '@/src/components/layout/protected-route' },
  
  // Feature Components
  { from: '@/src/features/dashboard/components/unified-dashboard', to: '@/src/features/dashboard/components/unified-dashboard' },
  
  // Hooks
  { from: '@/src/components/ui/use-toast', to: '@/src/components/ui/use-toast' },
  
  // Contexts
  { from: '@/src/features/auth/context/auth-context', to: '@/src/features/auth/context/auth-context' },
  
  // Services
  { from: '@/src/services/', to: '@/src/services/' },
  
  // Types
  { from: '@/src/types/', to: '@/src/types/' },
  
  // Lib
  { from: '@/src/lib/', to: '@/src/lib/' }
]

// Function to recursively find all TypeScript/JavaScript files
function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  let files = []
  
  try {
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        // Skip node_modules and .next directories
        if (!['node_modules', '.next', 'dist', 'build'].includes(item)) {
          files = files.concat(findFiles(fullPath, extensions))
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath)
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message)
  }
  
  return files
}

// Function to update imports in a file
function updateImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    let updated = false
    
    // Apply each import mapping
    for (const mapping of importMappings) {
      const regex = new RegExp(mapping.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
      if (content.includes(mapping.from)) {
        content = content.replace(regex, mapping.to)
        updated = true
      }
    }
    
    // Write back if updated
    if (updated) {
      fs.writeFileSync(filePath, content, 'utf8')
      console.log(`✅ Updated: ${filePath}`)
      return true
    }
    
    return false
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message)
    return false
  }
}

// Main function
function fixImports() {
  console.log('🔍 Finding files to update...')
  
  // Find all relevant files
  const files = findFiles('.', ['.ts', '.tsx', '.js', '.jsx'])
  console.log(`📁 Found ${files.length} files to check`)
  
  let updatedCount = 0
  
  // Update each file
  for (const file of files) {
    if (updateImportsInFile(file)) {
      updatedCount++
    }
  }
  
  console.log(`\n📊 Summary:`)
  console.log(`   • Files checked: ${files.length}`)
  console.log(`   • Files updated: ${updatedCount}`)
  console.log(`   • Import mappings applied: ${importMappings.length}`)
  
  if (updatedCount > 0) {
    console.log('\n✅ Import paths updated successfully!')
    console.log('🔄 Please restart your development server to see changes')
  } else {
    console.log('\n✨ No import paths needed updating')
  }
}

// Run the script
if (require.main === module) {
  fixImports()
}

module.exports = { fixImports }
