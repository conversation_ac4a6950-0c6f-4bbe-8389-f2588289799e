/**
 * Comprehensive Database Setup for EEU DTMS
 * Creates all necessary tables and seeds them with realistic data
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function createComprehensiveDatabase() {
  let connection;
  
  try {
    console.log('🏗️  CREATING COMPREHENSIVE DATABASE FOR EEU DTMS');
    console.log('=' .repeat(60));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // 1. Create all necessary tables
    console.log('\n📋 CREATING DATABASE TABLES');
    console.log('-' .repeat(40));
    
    // Users table (enhanced)
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        email VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        role ENUM('super_admin', 'national_asset_manager', 'national_maintenance_manager', 
                  'regional_admin', 'regional_asset_manager', 'regional_maintenance_engineer',
                  'service_center_manager', 'field_technician', 'customer_service_agent',
                  'audit_compliance_officer') NOT NULL,
        department VARCHAR(100),
        region_id INT,
        service_center_id INT,
        phone VARCHAR(20),
        avatar VARCHAR(255),
        is_active BOOLEAN DEFAULT true,
        last_login TIMESTAMP NULL,
        preferences JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_role (role),
        INDEX idx_region (region_id),
        INDEX idx_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_users table');

    // Enhanced regions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_regions (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(10) NOT NULL UNIQUE,
        population INT,
        area_km2 DECIMAL(10,2),
        capital_city VARCHAR(100),
        coordinates JSON,
        timezone VARCHAR(50) DEFAULT 'Africa/Addis_Ababa',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_code (code)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_regions table');

    // Service centers table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_service_centers (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(200) NOT NULL,
        code VARCHAR(20) NOT NULL UNIQUE,
        region_id INT NOT NULL,
        address TEXT,
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        phone VARCHAR(20),
        email VARCHAR(255),
        manager_id INT,
        capacity INT DEFAULT 50,
        status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES app_regions(id),
        INDEX idx_region (region_id),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_service_centers table');

    // Enhanced transformers table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_transformers (
        id INT PRIMARY KEY AUTO_INCREMENT,
        serial_number VARCHAR(50) NOT NULL UNIQUE,
        name VARCHAR(200) NOT NULL,
        type ENUM('distribution', 'power', 'instrument', 'auto') NOT NULL,
        capacity_kva DECIMAL(10,2) NOT NULL,
        voltage_primary DECIMAL(10,2) NOT NULL,
        voltage_secondary DECIMAL(10,2) NOT NULL,
        manufacturer VARCHAR(100),
        model VARCHAR(100),
        year_manufactured YEAR,
        installation_date DATE,
        location_name VARCHAR(200),
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        region_id INT NOT NULL,
        service_center_id INT,
        status ENUM('operational', 'warning', 'maintenance', 'critical', 'burnt', 'offline') DEFAULT 'operational',
        efficiency_rating DECIMAL(5,2),
        load_factor DECIMAL(5,2),
        temperature DECIMAL(5,2),
        oil_level DECIMAL(5,2),
        health_index DECIMAL(5,2),
        last_maintenance DATE,
        next_maintenance DATE,
        criticality ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        customer_type ENUM('residential', 'commercial', 'industrial', 'agricultural') DEFAULT 'residential',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES app_regions(id),
        FOREIGN KEY (service_center_id) REFERENCES app_service_centers(id),
        INDEX idx_status (status),
        INDEX idx_region (region_id),
        INDEX idx_serial (serial_number),
        INDEX idx_location (latitude, longitude)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_transformers table');

    // Enhanced maintenance schedules table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_maintenance_schedules (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT NOT NULL,
        type ENUM('routine', 'preventive', 'corrective', 'emergency', 'seasonal') NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        scheduled_date DATE NOT NULL,
        estimated_duration INT,
        actual_duration INT,
        priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        status ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'postponed') DEFAULT 'scheduled',
        technician_id INT,
        supervisor_id INT,
        cost DECIMAL(10,2),
        parts_used JSON,
        notes TEXT,
        completion_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES app_transformers(id),
        FOREIGN KEY (technician_id) REFERENCES app_users(id),
        FOREIGN KEY (supervisor_id) REFERENCES app_users(id),
        INDEX idx_transformer (transformer_id),
        INDEX idx_status (status),
        INDEX idx_scheduled_date (scheduled_date),
        INDEX idx_type (type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_maintenance_schedules table');

    // Enhanced alerts table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_alerts (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        type ENUM('temperature', 'voltage', 'load', 'maintenance', 'communication', 'weather', 'security') NOT NULL,
        status ENUM('active', 'investigating', 'resolved', 'monitoring') DEFAULT 'active',
        priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        source VARCHAR(100),
        threshold_value DECIMAL(10,2),
        actual_value DECIMAL(10,2),
        created_by INT,
        assigned_to INT,
        resolved_at TIMESTAMP NULL,
        resolved_by INT,
        resolution_notes TEXT,
        is_resolved BOOLEAN DEFAULT false,
        escalation_level INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES app_transformers(id),
        FOREIGN KEY (created_by) REFERENCES app_users(id),
        FOREIGN KEY (assigned_to) REFERENCES app_users(id),
        FOREIGN KEY (resolved_by) REFERENCES app_users(id),
        INDEX idx_severity (severity),
        INDEX idx_status (status),
        INDEX idx_type (type),
        INDEX idx_transformer (transformer_id),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_alerts table');

    // Performance metrics table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_performance_metrics (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT NOT NULL,
        metric_type ENUM('efficiency', 'load_factor', 'temperature', 'voltage', 'current', 'power_factor') NOT NULL,
        value DECIMAL(10,4) NOT NULL,
        unit VARCHAR(20),
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        source VARCHAR(100),
        quality_flag ENUM('good', 'questionable', 'bad') DEFAULT 'good',
        FOREIGN KEY (transformer_id) REFERENCES app_transformers(id),
        INDEX idx_transformer_type (transformer_id, metric_type),
        INDEX idx_recorded_at (recorded_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_performance_metrics table');

    // Weather data table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_weather_data (
        id INT PRIMARY KEY AUTO_INCREMENT,
        region_id INT NOT NULL,
        temperature DECIMAL(5,2),
        humidity DECIMAL(5,2),
        wind_speed DECIMAL(5,2),
        precipitation DECIMAL(5,2),
        weather_condition VARCHAR(50),
        risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
        uv_index DECIMAL(3,1),
        visibility DECIMAL(5,2),
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES app_regions(id),
        INDEX idx_region_time (region_id, recorded_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_weather_data table');

    // Notifications table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_notifications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
        category ENUM('system', 'maintenance', 'alert', 'user', 'report') DEFAULT 'system',
        is_read BOOLEAN DEFAULT false,
        action_url VARCHAR(500),
        metadata JSON,
        expires_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES app_users(id),
        INDEX idx_user_read (user_id, is_read),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_notifications table');

    // Outages table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_outages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        cause ENUM('equipment_failure', 'weather', 'maintenance', 'overload', 'external', 'unknown') NOT NULL,
        severity ENUM('minor', 'major', 'critical') NOT NULL,
        customers_affected INT DEFAULT 0,
        estimated_duration INT,
        actual_duration INT,
        status ENUM('active', 'investigating', 'repairing', 'resolved') DEFAULT 'active',
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        resolved_at TIMESTAMP NULL,
        reported_by INT,
        assigned_to INT,
        resolution_notes TEXT,
        FOREIGN KEY (transformer_id) REFERENCES app_transformers(id),
        FOREIGN KEY (reported_by) REFERENCES app_users(id),
        FOREIGN KEY (assigned_to) REFERENCES app_users(id),
        INDEX idx_transformer (transformer_id),
        INDEX idx_status (status),
        INDEX idx_started_at (started_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_outages table');

    // System settings table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_system_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        data_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        category VARCHAR(50),
        description TEXT,
        is_public BOOLEAN DEFAULT false,
        updated_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (updated_by) REFERENCES app_users(id),
        INDEX idx_category (category),
        INDEX idx_key (setting_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created app_system_settings table');

    console.log('\n✅ All database tables created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating database:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Export for use in other scripts
module.exports = { createComprehensiveDatabase };

// Run if called directly
if (require.main === module) {
  createComprehensiveDatabase();
}
