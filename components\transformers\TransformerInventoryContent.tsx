"use client"

import { useEffect, useState } from "react"
import {
  Search,
  Filter,
  Download,
  Plus,
  RefreshCw,
  ChevronDown,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Eye,
  Edit,
  Trash2,
  Map,
  BarChart2,
  FileText,
  MoreHorizontal,
  Package,
  TrendingUp,
  Activity,
  Upload,
  History,
  ClipboardList
} from 'lucide-react'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/src/components/ui/dropdown-menu'
import { TransformerHistoryCard } from './TransformerHistoryCard'

interface TransformerInventoryContentProps {
  showHeader?: boolean
  showTabs?: boolean
}

export function TransformerInventoryContent({
  showHeader = true,
  showTabs = true
}: TransformerInventoryContentProps) {
  const [transformers, setTransformers] = useState<any[]>([])
  const [filteredTransformers, setFilteredTransformers] = useState<any[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [regionFilter, setRegionFilter] = useState('all')
  const [sortBy, setSortBy] = useState('serial')
  const [sortOrder, setSortOrder] = useState('asc')
  const [isLoading, setIsLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showHistoryCard, setShowHistoryCard] = useState(false)
  const [selectedTransformerId, setSelectedTransformerId] = useState<string | null>(null)

  // Fetch transformers data
  const fetchTransformers = async () => {
    setIsLoading(true)

    try {
      console.log('🔄 Fetching transformers from MySQL API...')

      // Fetch from the MySQL API endpoint
      const response = await fetch('/api/mysql/transformers', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('✅ Transformers fetched successfully:', data.transformers?.length || 0)

      // Transform the data to match the expected format
      const transformedData = data.transformers?.map((transformer: any) => ({
        id: transformer.id,
        serialNumber: transformer.serial_number || transformer.serialNumber,
        manufacturer: transformer.manufacturer,
        model: transformer.model,
        type: transformer.type,
        ratingKVA: transformer.capacity || transformer.ratingKVA,
        primaryVoltage: transformer.voltage_primary || transformer.primaryVoltage,
        secondaryVoltage: transformer.voltage_secondary || transformer.secondaryVoltage,
        installationDate: transformer.installation_date || transformer.installationDate,
        status: transformer.status,
        healthIndex: transformer.health_index || transformer.healthIndex || 85,
        condition: transformer.condition || 'good',
        warrantyExpiry: transformer.warranty_expiry || '2025-12-31',
        cost: transformer.cost || 500000,
        supplier: `${transformer.manufacturer} Ethiopia`,
        oilLevel: transformer.oil_level || transformer.oilLevel || 95,
        temperature: transformer.temperature || 65,
        loadFactor: transformer.load_percentage || transformer.loadFactor || 75,
        efficiency: transformer.efficiency || 98.5,
        faultHistory: 0,
        maintenanceScore: transformer.maintenance_score || 90,
        location: {
          region: transformer.region_name || transformer.location?.region || 'Addis Ababa',
          serviceCenter: transformer.service_center_name || transformer.location?.serviceCenter || 'Central',
          coordinates: {
            lat: transformer.lat || transformer.location?.coordinates?.lat || 9.0320,
            lng: transformer.lng || transformer.location?.coordinates?.lng || 38.7469
          },
          address: transformer.location_address || transformer.location?.address || 'Addis Ababa, Ethiopia'
        },
        lastMaintenance: transformer.last_maintenance_date || transformer.lastMaintenance || '2024-01-01',
        nextMaintenance: transformer.next_maintenance_date || transformer.nextMaintenance || '2024-07-01'
      })) || []

      console.log('🔄 Transformed data:', transformedData.length, 'transformers')

      setTransformers(transformedData)
      setFilteredTransformers(transformedData)
    } catch (error) {
      console.error('❌ Error fetching transformers:', error)

      // Fallback to empty array if API fails
      setTransformers([])
      setFilteredTransformers([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchTransformers()
  }, [])

  // Handler functions
  const handleAddTransformer = () => {
    setShowAddDialog(true)
  }

  const handleImportData = () => {
    setShowImportDialog(true)
  }

  const handleExportInventory = () => {
    setShowExportDialog(true)
  }

  const handleOpenHistoryCard = (transformerId: string) => {
    setSelectedTransformerId(transformerId)
    setShowHistoryCard(true)
  }

  const handleCloseHistoryCard = () => {
    setShowHistoryCard(false)
    setSelectedTransformerId(null)
  }

  const handleExportData = (format: string) => {
    console.log(`Exporting inventory in ${format} format`)

    // Prepare data for export
    const exportData = filteredTransformers.map(transformer => ({
      'Serial Number': transformer.serialNumber,
      'Manufacturer': transformer.manufacturer,
      'Model': transformer.model,
      'Type': transformer.type,
      'Rating (kVA)': transformer.ratingKVA,
      'Primary Voltage (V)': transformer.primaryVoltage,
      'Secondary Voltage (V)': transformer.secondaryVoltage,
      'Status': transformer.status,
      'Health Index (%)': transformer.healthIndex,
      'Region': transformer.location.region,
      'Service Center': transformer.location.serviceCenter,
      'Installation Date': transformer.installationDate,
      'Last Maintenance': transformer.lastMaintenance,
      'Next Maintenance': transformer.nextMaintenance,
      'Cost (ETB)': transformer.cost
    }))

    const filename = `transformer_inventory_${new Date().toISOString().split('T')[0]}`

    if (format === 'csv') {
      const csv = convertToCSV(exportData)
      downloadFile(csv, `${filename}.csv`, 'text/csv')
    } else if (format === 'excel') {
      console.log('Excel export would be implemented here')
    } else if (format === 'pdf') {
      console.log('PDF export would be implemented here')
    }

    setShowExportDialog(false)
  }

  // Action handlers for dropdown menu items
  const handleViewDetails = (transformerId: string) => {
    console.log(`Viewing details for transformer: ${transformerId}`)
    // Navigate to transformer details page or open modal
    window.open(`/transformers/${transformerId}`, '_blank')
  }

  const handleEditTransformer = (transformerId: string) => {
    console.log(`Editing transformer: ${transformerId}`)
    // Open edit modal or navigate to edit page
    alert(`Edit functionality for transformer ${transformerId} would be implemented here`)
  }

  const handleViewOnMap = (transformerId: string) => {
    console.log(`Viewing transformer ${transformerId} on map`)
    // Navigate to map view with transformer highlighted
    window.open(`/transformers/map?highlight=${transformerId}`, '_blank')
  }

  const handleMaintenanceHistory = (transformerId: string) => {
    console.log(`Viewing maintenance history for transformer: ${transformerId}`)
    // Open maintenance history modal or navigate to maintenance page
    alert(`Maintenance history for transformer ${transformerId} would be displayed here`)
  }

  const handleDeleteTransformer = (transformerId: string) => {
    console.log(`Deleting transformer: ${transformerId}`)
    const confirmed = window.confirm('Are you sure you want to delete this transformer? This action cannot be undone.')
    if (confirmed) {
      // Implement delete functionality
      alert(`Delete functionality for transformer ${transformerId} would be implemented here`)
    }
  }

  const convertToCSV = (data: any[]) => {
    if (!data.length) return ''

    const headers = Object.keys(data[0]).join(',')
    const rows = data.map(row => Object.values(row).map(value =>
      typeof value === 'string' && value.includes(',') ? `"${value}"` : value
    ).join(','))
    return [headers, ...rows].join('\n')
  }

  const downloadFile = (content: string, filename: string, contentType: string) => {
    const blob = new Blob([content], { type: contentType })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.click()
    window.URL.revokeObjectURL(url)
  }

  // Apply filters and search
  useEffect(() => {
    let filtered = [...transformers]

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(transformer =>
        transformer.serialNumber.toLowerCase().includes(query) ||
        transformer.manufacturer.toLowerCase().includes(query) ||
        transformer.model.toLowerCase().includes(query) ||
        transformer.location.address.toLowerCase().includes(query)
      )
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(transformer => transformer.status === statusFilter)
    }

    // Apply region filter
    if (regionFilter !== 'all') {
      filtered = filtered.filter(transformer => transformer.location.region === regionFilter)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let valueA, valueB

      switch (sortBy) {
        case 'serial':
          valueA = a.serialNumber
          valueB = b.serialNumber
          break
        case 'manufacturer':
          valueA = a.manufacturer
          valueB = b.manufacturer
          break
        case 'rating':
          valueA = a.ratingKVA
          valueB = b.ratingKVA
          break
        case 'health':
          valueA = a.healthIndex
          valueB = b.healthIndex
          break
        case 'installation':
          valueA = new Date(a.installationDate).getTime()
          valueB = new Date(b.installationDate).getTime()
          break
        default:
          valueA = a.serialNumber
          valueB = b.serialNumber
      }

      if (sortOrder === 'asc') {
        return valueA > valueB ? 1 : -1
      } else {
        return valueA < valueB ? 1 : -1
      }
    })

    setFilteredTransformers(filtered)
  }, [transformers, searchQuery, statusFilter, regionFilter, sortBy, sortOrder])

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'operational':
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle size={12} className="mr-1" />
            Operational
          </span>
        )
      case 'warning':
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <AlertCircle size={12} className="mr-1" />
            Warning
          </span>
        )
      case 'maintenance':
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <AlertTriangle size={12} className="mr-1" />
            Maintenance
          </span>
        )
      case 'critical':
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <AlertTriangle size={12} className="mr-1" />
            Critical
          </span>
        )
      default:
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Unknown
          </span>
        )
    }
  }

  // Get health indicator
  const getHealthIndicator = (healthIndex: number) => {
    let color

    if (healthIndex >= 80) {
      color = 'bg-green-500'
    } else if (healthIndex >= 60) {
      color = 'bg-yellow-500'
    } else if (healthIndex >= 40) {
      color = 'bg-orange-500'
    } else {
      color = 'bg-red-500'
    }

    return (
      <div className="flex items-center">
        <div className="w-16 h-2 bg-gray-200 rounded-full mr-2">
          <div className={`h-full rounded-full ${color}`} style={{ width: `${healthIndex}%` }}></div>
        </div>
        <span>{healthIndex}%</span>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    )
  }

  const inventoryContent = (
    <div className="space-y-6">
      {/* Header */}
      {showHeader && (
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Transformer Inventory</h1>
            <p className="text-muted-foreground">
              Comprehensive inventory management for Ethiopian Electric Utility transformers
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleExportInventory}>
              <Download className="h-4 w-4 mr-2" />
              Export Inventory
            </Button>
            <Button variant="outline" onClick={handleImportData}>
              <Upload className="h-4 w-4 mr-2" />
              Import Data
            </Button>
            <Button className="bg-green-600 hover:bg-green-700" onClick={handleAddTransformer}>
              <Plus size={16} className="mr-2" />
              Add Transformer
            </Button>
          </div>
        </div>
      )}

      {/* Inventory Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transformers</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredTransformers.length}</div>
            <p className="text-xs text-muted-foreground">
              {filteredTransformers.filter(t => t.status === 'operational').length} operational
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {(filteredTransformers.reduce((sum, t) => sum + (t.cost || 0), 0) / 1000000).toFixed(1)}M ETB
            </div>
            <p className="text-xs text-muted-foreground">
              Asset valuation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Health Index</CardTitle>
            <Activity className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {filteredTransformers.length > 0 ? (filteredTransformers.reduce((sum, t) => sum + t.healthIndex, 0) / filteredTransformers.length).toFixed(1) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Fleet health
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance Due</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {filteredTransformers.filter(t => new Date(t.nextMaintenance) < new Date()).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Require attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg border p-4">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search transformers..."
                className="pl-9 pr-4 py-2 border rounded-md w-64 focus:outline-none focus:ring-1 focus:ring-green-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Search size={16} className="absolute left-3 top-3 text-gray-400" />
            </div>

            <div className="flex items-center space-x-2">
              <Filter size={16} className="text-gray-500" />
              <span className="text-sm text-gray-500">Filter:</span>

              <select
                className="border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="operational">Operational</option>
                <option value="warning">Warning</option>
                <option value="maintenance">Maintenance</option>
                <option value="critical">Critical</option>
                <option value="burnt">Burnt</option>
              </select>

              <select
                className="border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                value={regionFilter}
                onChange={(e) => setRegionFilter(e.target.value)}
              >
                <option value="all">All Regions</option>
                <option value="Addis Ababa">Addis Ababa</option>
                <option value="Oromia">Oromia</option>
                <option value="Amhara">Amhara</option>
                <option value="Tigray">Tigray</option>
                <option value="SNNP">SNNP</option>
                <option value="Somali">Somali</option>
                <option value="Afar">Afar</option>
              </select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Sort by:</span>
            <select
              className="border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
            >
              <option value="serial">Serial Number</option>
              <option value="manufacturer">Manufacturer</option>
              <option value="rating">Rating (kVA)</option>
              <option value="health">Health Index</option>
              <option value="installation">Installation Date</option>
            </select>

            <Button
              variant="outline"
              size="icon"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            >
              <ChevronDown className={`h-4 w-4 transform ${sortOrder === 'desc' ? 'rotate-180' : ''}`} />
            </Button>
          </div>
        </div>
      </div>

      {/* Transformers table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Serial Number
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Manufacturer / Model
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rating
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Location
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Health
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTransformers.map((transformer) => (
                <tr key={transformer.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{transformer.serialNumber}</div>
                    <div className="text-xs text-gray-500">ID: {transformer.id}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{transformer.manufacturer}</div>
                    <div className="text-xs text-gray-500">{transformer.model}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{transformer.ratingKVA} kVA</div>
                    <div className="text-xs text-gray-500">{transformer.primaryVoltage/1000}/{transformer.secondaryVoltage} kV</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{transformer.location.serviceCenter}</div>
                    <div className="text-xs text-gray-500">{transformer.location.region}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(transformer.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getHealthIndicator(transformer.healthIndex)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleViewDetails(transformer.id)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditTransformer(transformer.id)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleViewOnMap(transformer.id)}>
                          <Map className="h-4 w-4 mr-2" />
                          View on Map
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleMaintenanceHistory(transformer.id)}>
                          <FileText className="h-4 w-4 mr-2" />
                          Maintenance History
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleOpenHistoryCard(transformer.id)}>
                          <ClipboardList className="h-4 w-4 mr-2" />
                          History Card
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteTransformer(transformer.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredTransformers.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No transformers found matching your filters
          </div>
        )}
      </div>

      {/* Action buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          variant="outline"
          className="flex items-center"
          onClick={() => window.open('/transformers/map', '_blank')}
        >
          <Map size={16} className="mr-2" />
          View on Map
        </Button>

        <Button
          variant="outline"
          className="flex items-center"
          onClick={() => window.open('/transformers?tab=analytics', '_blank')}
        >
          <BarChart2 size={16} className="mr-2" />
          Analytics
        </Button>

        <Button
          variant="outline"
          className="flex items-center"
          onClick={handleExportInventory}
        >
          <FileText size={16} className="mr-2" />
          Export Report
        </Button>
      </div>
    </div>
  )

  const dialogsComponent = (
    <>
      {/* Add Transformer Dialog */}
      {showAddDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">Add New Transformer</h3>
            <form className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Serial Number</label>
                  <input
                    type="text"
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="Enter serial number"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Manufacturer</label>
                  <select className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500">
                    <option value="">Select manufacturer</option>
                    <option value="ABB">ABB</option>
                    <option value="Siemens">Siemens</option>
                    <option value="Schneider Electric">Schneider Electric</option>
                    <option value="General Electric">General Electric</option>
                    <option value="Hyundai">Hyundai</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Model</label>
                  <input
                    type="text"
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="Enter model"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Type</label>
                  <select className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500">
                    <option value="">Select type</option>
                    <option value="Distribution">Distribution</option>
                    <option value="Power">Power</option>
                    <option value="Instrument">Instrument</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Rating (kVA)</label>
                  <input
                    type="number"
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="Enter rating"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Primary Voltage (V)</label>
                  <input
                    type="number"
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="Enter primary voltage"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Secondary Voltage (V)</label>
                  <input
                    type="number"
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="Enter secondary voltage"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Region</label>
                  <select className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500">
                    <option value="">Select region</option>
                    <option value="Addis Ababa">Addis Ababa</option>
                    <option value="Oromia">Oromia</option>
                    <option value="Amhara">Amhara</option>
                    <option value="Tigray">Tigray</option>
                    <option value="SNNP">SNNP</option>
                    <option value="Somali">Somali</option>
                    <option value="Afar">Afar</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Installation Date</label>
                  <input
                    type="date"
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Cost (ETB)</label>
                  <input
                    type="number"
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                    placeholder="Enter cost"
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-2 mt-6">
                <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                  Cancel
                </Button>
                <Button className="bg-green-600 hover:bg-green-700">
                  Add Transformer
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Export Dialog */}
      {showExportDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Export Inventory Data</h3>
            <div className="space-y-3">
              <button
                onClick={() => handleExportData('csv')}
                className="w-full text-left p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className="font-medium">CSV Format</div>
                <div className="text-sm text-gray-600">Comma-separated values for spreadsheets</div>
              </button>
              <button
                onClick={() => handleExportData('excel')}
                className="w-full text-left p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className="font-medium">Excel Format</div>
                <div className="text-sm text-gray-600">Microsoft Excel workbook</div>
              </button>
              <button
                onClick={() => handleExportData('pdf')}
                className="w-full text-left p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className="font-medium">PDF Report</div>
                <div className="text-sm text-gray-600">Formatted PDF document</div>
              </button>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={() => setShowExportDialog(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Import Dialog */}
      {showImportDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Import Transformer Data</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Select File</label>
                <input
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-green-500"
                />
              </div>
              <div className="text-sm text-gray-600">
                Supported formats: CSV, Excel (.xlsx, .xls)
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={() => setShowImportDialog(false)}>
                Cancel
              </Button>
              <Button className="bg-green-600 hover:bg-green-700">
                Import Data
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* History Card Dialog */}
      {showHistoryCard && selectedTransformerId && (
        <TransformerHistoryCard
          transformerId={selectedTransformerId}
          onClose={handleCloseHistoryCard}
        />
      )}
    </>
  )

  if (showTabs) {
    return (
      <>
        {dialogsComponent}
        <Tabs defaultValue="inventory" className="space-y-4">
          <TabsList>
            <TabsTrigger value="inventory">Inventory List</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            <TabsTrigger value="assets">Asset Management</TabsTrigger>
          </TabsList>

          <TabsContent value="inventory" className="space-y-4">
            {inventoryContent}
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Inventory Analytics</CardTitle>
                <CardDescription>Comprehensive analytics for transformer inventory</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  Analytics dashboard will be implemented here
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="maintenance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Overview</CardTitle>
                <CardDescription>Maintenance status and scheduling</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  Maintenance overview will be implemented here
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="assets" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Asset Management</CardTitle>
                <CardDescription>Advanced asset management features</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  Asset management features will be implemented here
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </>
    )
  }

  return (
    <>
      {dialogsComponent}
      {inventoryContent}
    </>
  )
}
