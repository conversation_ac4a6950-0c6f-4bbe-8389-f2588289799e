"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import {
  Download, Search, Filter, Edit, Trash, Plus, Shield,
  X, RefreshCw, FileText, Users, CheckCircle, AlertTriangle
} from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table"
import { Badge } from "@/src/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useAuth } from "@/src/features/auth/context/auth-context"
import { useToast } from "@/src/components/ui/use-toast"
import { RoleDialog } from "@/components/role-dialog"
import type { RoleDefinition } from "@/src/types/auth"
import {
  <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON>ltip<PERSON>rovider, Tooltip<PERSON>rigger
} from "@/src/components/ui/tooltip"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/src/components/ui/alert-dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuGroup,
} from "@/src/components/ui/dropdown-menu"

export function RolesManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [levelFilter, setLevelFilter] = useState<string>("all")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [permissionFilter, setPermissionFilter] = useState<string>("all")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedRole, setSelectedRole] = useState<RoleDefinition | null>(null)
  const [roleToDelete, setRoleToDelete] = useState<RoleDefinition | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { user: currentUser } = useAuth()
  const { toast } = useToast()

  // Mock roles data - in a real app, this would come from an API
  const roles: RoleDefinition[] = [
    {
      id: "role-001",
      name: "Super Admin",
      description: "Full system control (users, roles, settings)",
      organizationalLevel: "head_office",
      permissions: [
        { resource: "users", action: "create" },
        { resource: "users", action: "read" },
        { resource: "users", action: "update" },
        { resource: "users", action: "delete" },
        { resource: "roles", action: "create" },
        { resource: "roles", action: "read" },
        { resource: "roles", action: "update" },
        { resource: "roles", action: "delete" },
        { resource: "transformers", action: "create" },
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "transformers", action: "delete" },
        { resource: "transformers", action: "approve" },
        { resource: "maintenance", action: "create" },
        { resource: "maintenance", action: "read" },
        { resource: "maintenance", action: "update" },
        { resource: "maintenance", action: "delete" },
        { resource: "maintenance", action: "approve" },
        { resource: "maintenance", action: "assign" },
        { resource: "reports", action: "export" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-002",
      name: "National Asset Manager",
      description: "Access nationwide transformer inventory",
      organizationalLevel: "head_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "transformers", action: "approve" },
        { resource: "reports", action: "export" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-003",
      name: "National Maintenance Manager",
      description: "National maintenance planning",
      organizationalLevel: "head_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "maintenance", action: "create" },
        { resource: "maintenance", action: "read" },
        { resource: "maintenance", action: "update" },
        { resource: "maintenance", action: "approve" },
        { resource: "reports", action: "export" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-004",
      name: "Regional Admin",
      description: "Manage regional users & service centers",
      organizationalLevel: "regional_office",
      permissions: [
        { resource: "users", action: "create" },
        { resource: "users", action: "read" },
        { resource: "users", action: "update" },
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "maintenance", action: "assign" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-005",
      name: "Regional Asset Manager",
      description: "Manage regional assets",
      organizationalLevel: "regional_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "transformers", action: "approve" },
        { resource: "reports", action: "export" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-006",
      name: "Regional Maintenance Engineer",
      description: "Monitor regional transformer health",
      organizationalLevel: "regional_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "maintenance", action: "create" },
        { resource: "maintenance", action: "read" },
        { resource: "maintenance", action: "update" },
        { resource: "maintenance", action: "approve" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-007",
      name: "Service Center Manager",
      description: "Oversee daily operations",
      organizationalLevel: "service_center",
      permissions: [
        { resource: "users", action: "read" },
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "maintenance", action: "create" },
        { resource: "maintenance", action: "read" },
        { resource: "maintenance", action: "update" },
        { resource: "maintenance", action: "assign" },
        { resource: "maintenance", action: "approve" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-008",
      name: "Field Technician",
      description: "Conduct inspections & repairs",
      organizationalLevel: "service_center",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "transformers", action: "update" },
        { resource: "maintenance", action: "read" },
        { resource: "maintenance", action: "update" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-009",
      name: "Customer Service Agent",
      description: "Log customer complaints/requests",
      organizationalLevel: "service_center",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "outages", action: "read" },
        { resource: "customer_requests", action: "create" },
        { resource: "customer_requests", action: "read" },
        { resource: "customer_requests", action: "update" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-010",
      name: "Audit & Compliance Officer",
      description: "Review logs and monitor compliance",
      organizationalLevel: "head_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "maintenance", action: "read" },
        { resource: "reports", action: "export" },
      ],
      isSystemRole: true,
    },
    {
      id: "role-011",
      name: "Custom Role",
      description: "Custom role for specific needs",
      organizationalLevel: "regional_office",
      permissions: [
        { resource: "transformers", action: "read" },
        { resource: "maintenance", action: "read" },
      ],
      isSystemRole: false,
    },
  ]

  // Helper function to check if a role has a specific permission
  const hasPermission = (role: RoleDefinition, resource: string, action?: string) => {
    return role.permissions.some(
      (p) => p.resource === resource && (action ? p.action === action : true)
    )
  }

  // Filter roles based on search query and filters
  const filteredRoles = roles.filter((role) => {
    const matchesSearch =
      role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      role.description.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesLevel = levelFilter === "all" || role.organizationalLevel === levelFilter

    const matchesType = typeFilter === "all" ||
      (typeFilter === "system" && role.isSystemRole) ||
      (typeFilter === "custom" && !role.isSystemRole)

    let matchesPermission = true
    if (permissionFilter !== "all") {
      const [resource, action] = permissionFilter.split(":")
      matchesPermission = hasPermission(role, resource, action)
    }

    return matchesSearch && matchesLevel && matchesType && matchesPermission
  })

  // Get unique resources from all roles for filtering
  const getUniqueResources = () => {
    const resources = new Set<string>()
    roles.forEach(role => {
      role.permissions.forEach(p => {
        resources.add(p.resource)
      })
    })
    return Array.from(resources)
  }

  const uniqueResources = getUniqueResources()

  const handleAddRole = () => {
    setSelectedRole(null)
    setIsDialogOpen(true)
  }

  const handleEditRole = (role: RoleDefinition) => {
    setSelectedRole(role)
    setIsDialogOpen(true)
  }

  const handleDeleteClick = (role: RoleDefinition) => {
    if (role.isSystemRole) {
      toast({
        title: "Cannot delete system role",
        description: "System roles cannot be deleted.",
        variant: "destructive",
      })
      return
    }

    setRoleToDelete(role)
    setIsDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = () => {
    if (!roleToDelete) return

    setIsLoading(true)

    // In a real app, this would call an API to delete the role
    setTimeout(() => {
      toast({
        title: "Role deleted",
        description: `${roleToDelete.name} has been deleted successfully.`,
      })
      setIsLoading(false)
      setIsDeleteDialogOpen(false)
      setRoleToDelete(null)
    }, 500)
  }

  const handleRoleSave = (role: RoleDefinition) => {
    // In a real app, this would call an API to save the role
    toast({
      title: selectedRole ? "Role updated" : "Role created",
      description: `${role.name} has been ${selectedRole ? "updated" : "created"}.`,
    })
    setIsDialogOpen(false)
  }

  const handleExport = () => {
    setIsLoading(true)

    // In a real app, this would generate and download a file
    setTimeout(() => {
      toast({
        title: "Roles exported",
        description: "Roles have been exported to CSV successfully.",
      })
      setIsLoading(false)
    }, 500)
  }

  const handleRefresh = () => {
    setIsLoading(true)

    // In a real app, this would fetch fresh data from the API
    setTimeout(() => {
      toast({
        title: "Roles refreshed",
        description: "Role data has been refreshed successfully.",
      })
      setIsLoading(false)
    }, 500)
  }

  const handleClearFilters = () => {
    setSearchQuery("")
    setLevelFilter("all")
    setTypeFilter("all")
    setPermissionFilter("all")
  }

  // Only super admin can manage roles
  const canManageRoles = currentUser?.role === "super_admin"

  return (
    <TooltipProvider>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <h1 className="text-2xl font-bold tracking-tight">Role Management</h1>
          <div className="flex items-center gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleExport} disabled={isLoading}>
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export roles to CSV</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoading}>
                  <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
                  Refresh
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Refresh role data</p>
              </TooltipContent>
            </Tooltip>

            {canManageRoles && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button size="sm" onClick={handleAddRole} disabled={isLoading}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Role
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Create a new role</p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Roles</CardTitle>
                <CardDescription>Manage system roles and permissions</CardDescription>
              </div>
              <Badge variant="outline" className="ml-2">
                {filteredRoles.length} of {roles.length} roles
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4 sm:flex-row mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search roles..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full rounded-l-none"
                    onClick={() => setSearchQuery("")}
                    aria-label="Clear search"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                <Select value={levelFilter} onValueChange={setLevelFilter}>
                  <SelectTrigger className="w-[180px]">
                    <Users className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Organizational Level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="head_office">Head Office</SelectItem>
                    <SelectItem value="regional_office">Regional Office</SelectItem>
                    <SelectItem value="service_center">Service Center</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[150px]">
                    <Shield className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Role Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="system">System Roles</SelectItem>
                    <SelectItem value="custom">Custom Roles</SelectItem>
                  </SelectContent>
                </Select>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-[180px]">
                      <FileText className="mr-2 h-4 w-4" />
                      <span className="truncate">
                        {permissionFilter === "all"
                          ? "All Permissions"
                          : permissionFilter.split(":").map(p =>
                              p.charAt(0).toUpperCase() + p.slice(1)
                            ).join(" - ")}
                      </span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56">
                    <DropdownMenuLabel>Filter by Permission</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setPermissionFilter("all")}>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      <span>All Permissions</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {uniqueResources.map(resource => (
                      <DropdownMenuGroup key={resource}>
                        <DropdownMenuItem onClick={() => setPermissionFilter(`${resource}`)}>
                          <span className="capitalize">{resource}</span>
                        </DropdownMenuItem>
                      </DropdownMenuGroup>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {(searchQuery || levelFilter !== "all" || typeFilter !== "all" || permissionFilter !== "all") && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" onClick={handleClearFilters}>
                        <X className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Clear all filters</p>
                    </TooltipContent>
                  </Tooltip>
                )}
              </div>
            </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Role</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Organizational Level</TableHead>
                  <TableHead>Permissions</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRoles.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No roles found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRoles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Shield className="h-4 w-4 text-teal-600" />
                          <span className="font-medium">{role.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{role.description}</TableCell>
                      <TableCell>
                        {role.organizationalLevel === "head_office"
                          ? "Head Office"
                          : role.organizationalLevel === "regional_office"
                            ? "Regional Office"
                            : "Service Center"}
                      </TableCell>
                      <TableCell>{role.permissions.length}</TableCell>
                      <TableCell>
                        <Badge
                          variant={role.isSystemRole ? "default" : "outline"}
                          className={role.isSystemRole ? "bg-blue-500 hover:bg-blue-600" : ""}
                        >
                          {role.isSystemRole ? "System" : "Custom"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleEditRole(role)}
                                disabled={!canManageRoles || isLoading}
                              >
                                <Edit className="h-4 w-4" />
                                <span className="sr-only">Edit</span>
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{role.isSystemRole ? "View" : "Edit"} role</p>
                            </TooltipContent>
                          </Tooltip>

                          {canManageRoles && !role.isSystemRole && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDeleteClick(role)}
                                  disabled={isLoading}
                                >
                                  <Trash className="h-4 w-4 text-red-500" />
                                  <span className="sr-only">Delete</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Delete role</p>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <RoleDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} role={selectedRole} onSave={handleRoleSave} />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-red-500">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Delete Role
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the role "{roleToDelete?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isLoading}
              className="bg-red-500 hover:bg-red-600"
            >
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash className="mr-2 h-4 w-4" />
                  Delete
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
    </TooltipProvider>
  )
}
