"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadialBarChart,
  RadialBar,
  Legend
} from 'recharts'
import { 
  Zap, 
  TrendingUp, 
  TrendingDown,
  Activity, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  MapPin,
  Gauge,
  ThermometerSun,
  Battery,
  Shield,
  Settings,
  BarChart3,
  PieChart as PieChartIcon,
  Globe,
  Building,
  Users,
  DollarSign,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Eye,
  EyeOff,
  Maximize2,
  MoreVertical
} from 'lucide-react'
import { useDashboardFilters } from "@/hooks/use-dashboard-filters"
import { ComprehensiveFilterPanel } from "@/components/filters/comprehensive-filter-panel"

const COLORS = {
  primary: '#3b82f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#06b6d4',
  purple: '#8b5cf6',
  pink: '#ec4899',
  indigo: '#6366f1'
}

const STATUS_COLORS = {
  operational: '#10b981',
  warning: '#f59e0b',
  critical: '#ef4444',
  maintenance: '#3b82f6',
  burnt: '#6b7280',
  offline: '#9ca3af'
}

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  changeLabel?: string
  icon: React.ReactNode
  color: string
  trend?: 'up' | 'down' | 'stable'
  subtitle?: string
}

function MetricCard({ title, value, change, changeLabel, icon, color, trend, subtitle }: MetricCardProps) {
  const getTrendIcon = () => {
    if (trend === 'up') return <TrendingUp className="h-4 w-4 text-green-500" />
    if (trend === 'down') return <TrendingDown className="h-4 w-4 text-red-500" />
    return <Activity className="h-4 w-4 text-gray-500" />
  }

  return (
    <Card className="relative overflow-hidden group hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50/50">
      <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${color}`}></div>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
        <div className={`p-2 rounded-lg bg-gradient-to-br ${color} text-white shadow-lg`}>
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-3xl font-bold text-gray-900 mb-1">{value}</div>
        {subtitle && <p className="text-sm text-gray-500 mb-2">{subtitle}</p>}
        {change !== undefined && (
          <div className="flex items-center text-sm">
            {getTrendIcon()}
            <span className={`ml-1 ${trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600'}`}>
              {change > 0 ? '+' : ''}{change}% {changeLabel}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface ChartCardProps {
  title: string
  children: React.ReactNode
  actions?: React.ReactNode
  fullWidth?: boolean
}

function ChartCard({ title, children, actions, fullWidth = false }: ChartCardProps) {
  return (
    <Card className={`${fullWidth ? 'col-span-full' : ''} border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/30`}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-semibold text-gray-900">{title}</CardTitle>
        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </CardHeader>
      <CardContent className="pt-0">
        {children}
      </CardContent>
    </Card>
  )
}

export function ModernDashboard() {
  const {
    filters,
    filteredData,
    isLoading,
    error,
    updateFilters,
    resetFilters,
    getActiveFilterCount,
    saveFiltersToStorage,
    loadFiltersFromStorage
  } = useDashboardFilters()

  const [showFilters, setShowFilters] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // Mock real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate real-time data updates
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const handleRefresh = async () => {
    setRefreshing(true)
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000)
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
        <Card className="max-w-md mx-auto mt-20">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-500" />
            <h3 className="text-lg font-semibold mb-2">Dashboard Error</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Prepare chart data
  const statusData = filteredData ? [
    { name: 'Operational', value: filteredData.summary.operationalCount, color: STATUS_COLORS.operational },
    { name: 'Warning', value: filteredData.summary.warningCount, color: STATUS_COLORS.warning },
    { name: 'Critical', value: filteredData.summary.criticalCount, color: STATUS_COLORS.critical },
    { name: 'Maintenance', value: filteredData.summary.maintenanceCount, color: STATUS_COLORS.maintenance }
  ].filter(item => item.value > 0) : []

  const regionalData = filteredData?.regions.map(region => ({
    name: region.name,
    transformers: region.transformer_count || 0,
    efficiency: 95 + Math.random() * 5, // Mock efficiency data
    code: region.code
  })) || []

  const performanceTrend = [
    { time: '00:00', efficiency: 97.2, load: 72, temperature: 62 },
    { time: '04:00', efficiency: 97.5, load: 68, temperature: 59 },
    { time: '08:00', efficiency: 97.8, load: 85, temperature: 71 },
    { time: '12:00', efficiency: 97.6, load: 92, temperature: 75 },
    { time: '16:00', efficiency: 97.4, load: 88, temperature: 73 },
    { time: '20:00', efficiency: 97.7, load: 79, temperature: 67 },
  ]

  const systemHealth = filteredData ? [
    { name: 'System Health', value: 95, fill: COLORS.success },
    { name: 'Performance', value: Math.round(filteredData.summary.avgEfficiency), fill: COLORS.primary },
    { name: 'Reliability', value: 98, fill: COLORS.info },
  ] : []

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-40">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                EEU Digital Transformer Management
              </h1>
              <p className="text-gray-600 mt-1">
                Real-time monitoring and analytics for Ethiopian Electric Utility
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="relative"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {getActiveFilterCount() > 0 && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                    {getActiveFilterCount()}
                  </Badge>
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-6 space-y-6">
        {/* Filter Panel */}
        {showFilters && (
          <div className="animate-in slide-in-from-top-2 duration-300">
            <ComprehensiveFilterPanel
              onFiltersChange={updateFilters}
              onReset={resetFilters}
            />
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <Card className="border-0 shadow-lg">
            <CardContent className="p-8">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                <span className="text-lg text-gray-600">Loading dashboard data...</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Dashboard Content */}
        {filteredData && (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <MetricCard
                title="Total Transformers"
                value={filteredData.summary.totalTransformers}
                change={5.2}
                changeLabel="vs last month"
                icon={<Zap className="h-5 w-5" />}
                color="from-blue-500 to-blue-600"
                trend="up"
                subtitle={`${filteredData.summary.operationalCount} operational`}
              />
              <MetricCard
                title="System Availability"
                value={`${filteredData.summary.totalTransformers > 0 
                  ? ((filteredData.summary.operationalCount / filteredData.summary.totalTransformers) * 100).toFixed(1)
                  : 0}%`}
                change={2.1}
                changeLabel="uptime"
                icon={<CheckCircle className="h-5 w-5" />}
                color="from-green-500 to-green-600"
                trend="up"
                subtitle="99.8% target"
              />
              <MetricCard
                title="Average Efficiency"
                value={`${filteredData.summary.avgEfficiency.toFixed(1)}%`}
                change={0.8}
                changeLabel="improvement"
                icon={<Gauge className="h-5 w-5" />}
                color="from-purple-500 to-purple-600"
                trend="up"
                subtitle={`${filteredData.summary.avgLoadFactor.toFixed(1)}% load factor`}
              />
              <MetricCard
                title="Asset Value"
                value={`$${(filteredData.summary.totalAssetValue / 1000000).toFixed(1)}M`}
                change={-1.2}
                changeLabel="depreciation"
                icon={<DollarSign className="h-5 w-5" />}
                color="from-orange-500 to-orange-600"
                trend="down"
                subtitle="Total portfolio"
              />
            </div>

            {/* Secondary Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <MetricCard
                title="Active Alerts"
                value={filteredData.summary.activeAlerts}
                icon={<AlertTriangle className="h-5 w-5" />}
                color="from-red-500 to-red-600"
                subtitle={`${filteredData.summary.criticalCount} critical issues`}
              />
              <MetricCard
                title="Pending Maintenance"
                value={filteredData.summary.pendingMaintenance}
                icon={<Clock className="h-5 w-5" />}
                color="from-yellow-500 to-yellow-600"
                subtitle="Scheduled tasks"
              />
              <MetricCard
                title="Average Temperature"
                value={`${filteredData.summary.avgTemperature.toFixed(1)}°C`}
                icon={<ThermometerSun className="h-5 w-5" />}
                color="from-cyan-500 to-cyan-600"
                subtitle="Operating range"
              />
              <MetricCard
                title="Regions Covered"
                value={filteredData.regions.filter(r => r.transformer_count > 0).length}
                icon={<Globe className="h-5 w-5" />}
                color="from-indigo-500 to-indigo-600"
                subtitle="Ethiopian regions"
              />
            </div>

            {/* Charts Section */}
            <Tabs defaultValue="overview" className="space-y-6">
              <TabsList className="grid w-full grid-cols-5 lg:w-auto lg:grid-cols-5">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="performance" className="flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Performance
                </TabsTrigger>
                <TabsTrigger value="regional" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Regional
                </TabsTrigger>
                <TabsTrigger value="health" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Health
                </TabsTrigger>
                <TabsTrigger value="analytics" className="flex items-center gap-2">
                  <PieChartIcon className="h-4 w-4" />
                  Analytics
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <ChartCard 
                    title="Transformer Status Distribution"
                    actions={
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    }
                  >
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={statusData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={100}
                          paddingAngle={5}
                          dataKey="value"
                        >
                          {statusData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartCard>

                  <ChartCard title="Regional Distribution">
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={regionalData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="code" stroke="#666" />
                        <YAxis stroke="#666" />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: 'white', 
                            border: 'none', 
                            borderRadius: '8px', 
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                          }} 
                        />
                        <Bar dataKey="transformers" fill={COLORS.primary} radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </ChartCard>
                </div>
              </TabsContent>

              <TabsContent value="performance" className="space-y-6">
                <ChartCard title="Real-time Performance Trends" fullWidth>
                  <ResponsiveContainer width="100%" height={400}>
                    <AreaChart data={performanceTrend}>
                      <defs>
                        <linearGradient id="efficiencyGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor={COLORS.success} stopOpacity={0.8}/>
                          <stop offset="95%" stopColor={COLORS.success} stopOpacity={0.1}/>
                        </linearGradient>
                        <linearGradient id="loadGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor={COLORS.primary} stopOpacity={0.8}/>
                          <stop offset="95%" stopColor={COLORS.primary} stopOpacity={0.1}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="time" stroke="#666" />
                      <YAxis stroke="#666" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: 'white', 
                          border: 'none', 
                          borderRadius: '8px', 
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                        }} 
                      />
                      <Area 
                        type="monotone" 
                        dataKey="efficiency" 
                        stroke={COLORS.success} 
                        fillOpacity={1} 
                        fill="url(#efficiencyGradient)" 
                        strokeWidth={2}
                      />
                      <Area 
                        type="monotone" 
                        dataKey="load" 
                        stroke={COLORS.primary} 
                        fillOpacity={1} 
                        fill="url(#loadGradient)" 
                        strokeWidth={2}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </ChartCard>
              </TabsContent>

              <TabsContent value="regional" className="space-y-6">
                <ChartCard title="Regional Performance Comparison" fullWidth>
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={regionalData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="name" stroke="#666" />
                      <YAxis stroke="#666" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: 'white', 
                          border: 'none', 
                          borderRadius: '8px', 
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                        }} 
                      />
                      <Bar dataKey="transformers" fill={COLORS.primary} radius={[4, 4, 0, 0]} />
                      <Bar dataKey="efficiency" fill={COLORS.success} radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartCard>
              </TabsContent>

              <TabsContent value="health" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <ChartCard title="System Health Score">
                    <ResponsiveContainer width="100%" height={300}>
                      <RadialBarChart cx="50%" cy="50%" innerRadius="20%" outerRadius="90%" data={systemHealth}>
                        <RadialBar dataKey="value" cornerRadius={10} fill="#8884d8" />
                        <Tooltip />
                      </RadialBarChart>
                    </ResponsiveContainer>
                  </ChartCard>

                  <ChartCard title="Temperature Monitoring">
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={performanceTrend}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="time" stroke="#666" />
                        <YAxis stroke="#666" />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: 'white', 
                            border: 'none', 
                            borderRadius: '8px', 
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' 
                          }} 
                        />
                        <Line 
                          type="monotone" 
                          dataKey="temperature" 
                          stroke={COLORS.danger} 
                          strokeWidth={3}
                          dot={{ fill: COLORS.danger, strokeWidth: 2, r: 4 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </ChartCard>
                </div>
              </TabsContent>

              <TabsContent value="analytics" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-blue-100">Efficiency Score</p>
                          <p className="text-3xl font-bold">{filteredData.summary.avgEfficiency.toFixed(1)}%</p>
                        </div>
                        <Gauge className="h-8 w-8 text-blue-200" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-lg bg-gradient-to-br from-green-500 to-green-600 text-white">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-green-100">Uptime</p>
                          <p className="text-3xl font-bold">99.8%</p>
                        </div>
                        <Activity className="h-8 w-8 text-green-200" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-500 to-purple-600 text-white">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-purple-100">Load Factor</p>
                          <p className="text-3xl font-bold">{filteredData.summary.avgLoadFactor.toFixed(1)}%</p>
                        </div>
                        <Battery className="h-8 w-8 text-purple-200" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </div>
  )
}
