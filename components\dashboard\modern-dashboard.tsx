"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadialBarChart,
  RadialBar,
  Legend
} from 'recharts'
import { 
  Zap, 
  TrendingUp, 
  TrendingDown,
  Activity, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  MapPin,
  Gauge,
  ThermometerSun,
  Battery,
  Shield,
  Settings,
  BarChart3,
  PieChart as PieChartIcon,
  Globe,
  Building,
  Users,
  DollarSign,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Eye,
  EyeOff,
  Maximize2,
  MoreVertical
} from 'lucide-react'
import { useDashboardFilters } from "@/hooks/use-dashboard-filters"
import { ComprehensiveFilterPanel } from "@/components/filters/comprehensive-filter-panel"
import {
  AdvancedMetricCard,
  StatusMetricCard,
  SystemHealthCard,
  EfficiencyCard,
  UptimeCard,
  AlertsCard,
  TemperatureCard
} from "./metric-cards"
import {
  ModernChartCard,
  StatusPieChart,
  PerformanceTrendChart,
  RegionalBarChart,
  SystemHealthRadial
} from "./modern-charts"

const COLORS = {
  primary: '#3b82f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#06b6d4',
  purple: '#8b5cf6',
  pink: '#ec4899',
  indigo: '#6366f1'
}

const STATUS_COLORS = {
  operational: '#10b981',
  warning: '#f59e0b',
  critical: '#ef4444',
  maintenance: '#3b82f6',
  burnt: '#6b7280',
  offline: '#9ca3af'
}



export function ModernDashboard() {
  const {
    filters,
    filteredData,
    isLoading,
    error,
    updateFilters,
    resetFilters,
    getActiveFilterCount,
    saveFiltersToStorage,
    loadFiltersFromStorage
  } = useDashboardFilters()

  const [showFilters, setShowFilters] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // Mock real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate real-time data updates
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const handleRefresh = async () => {
    setRefreshing(true)
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000)
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
        <Card className="max-w-md mx-auto mt-20">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-500" />
            <h3 className="text-lg font-semibold mb-2">Dashboard Error</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Prepare chart data
  const statusData = filteredData ? [
    { name: 'Operational', value: filteredData.summary.operationalCount, color: STATUS_COLORS.operational },
    { name: 'Warning', value: filteredData.summary.warningCount, color: STATUS_COLORS.warning },
    { name: 'Critical', value: filteredData.summary.criticalCount, color: STATUS_COLORS.critical },
    { name: 'Maintenance', value: filteredData.summary.maintenanceCount, color: STATUS_COLORS.maintenance }
  ].filter(item => item.value > 0) : []

  const regionalData = filteredData?.regions.map(region => ({
    name: region.name,
    transformers: region.transformer_count || 0,
    efficiency: 95 + Math.random() * 5, // Mock efficiency data
    code: region.code
  })) || []

  const performanceTrend = [
    { time: '00:00', efficiency: 97.2, load: 72, temperature: 62 },
    { time: '04:00', efficiency: 97.5, load: 68, temperature: 59 },
    { time: '08:00', efficiency: 97.8, load: 85, temperature: 71 },
    { time: '12:00', efficiency: 97.6, load: 92, temperature: 75 },
    { time: '16:00', efficiency: 97.4, load: 88, temperature: 73 },
    { time: '20:00', efficiency: 97.7, load: 79, temperature: 67 },
  ]

  const systemHealth = filteredData ? [
    { name: 'System Health', value: 95, fill: COLORS.success },
    { name: 'Performance', value: Math.round(filteredData.summary.avgEfficiency), fill: COLORS.primary },
    { name: 'Reliability', value: 98, fill: COLORS.info },
  ] : []

  return (
    <div className="min-h-screen modern-dashboard-bg">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-40">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                EEU Digital Transformer Management
              </h1>
              <p className="text-gray-600 mt-1">
                Real-time monitoring and analytics for Ethiopian Electric Utility
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="relative"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {getActiveFilterCount() > 0 && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                    {getActiveFilterCount()}
                  </Badge>
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-6 space-y-6">
        {/* Filter Panel */}
        {showFilters && (
          <div className="animate-in slide-in-from-top-2 duration-300">
            <ComprehensiveFilterPanel
              onFiltersChange={updateFilters}
              onReset={resetFilters}
            />
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <Card className="border-0 shadow-lg">
            <CardContent className="p-8">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                <span className="text-lg text-gray-600">Loading dashboard data...</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Dashboard Content */}
        {filteredData && (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <AdvancedMetricCard
                title="Total Transformers"
                value={filteredData.summary.totalTransformers}
                subtitle={`${filteredData.summary.operationalCount} operational`}
                icon={<Zap className="h-6 w-6" />}
                gradient="from-blue-500 to-indigo-600"
                sparklineData={[12, 13, 14, 15, 15, 15, 15]}
                badge={{
                  text: "+5.2% vs last month",
                  variant: "success"
                }}
              />
              <UptimeCard
                uptime={filteredData.summary.totalTransformers > 0
                  ? ((filteredData.summary.operationalCount / filteredData.summary.totalTransformers) * 100)
                  : 0}
              />
              <EfficiencyCard efficiency={filteredData.summary.avgEfficiency} />
              <AdvancedMetricCard
                title="Asset Portfolio"
                value={`$${(filteredData.summary.totalAssetValue / 1000000).toFixed(1)}M`}
                subtitle="Total asset value"
                icon={<DollarSign className="h-6 w-6" />}
                gradient="from-orange-500 to-red-500"
                sparklineData={[1.8, 1.7, 1.75, 1.76, 1.77, 1.76, 1.765]}
                badge={{
                  text: "Depreciation -1.2%",
                  variant: "warning"
                }}
              />
            </div>

            {/* Secondary Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <AlertsCard alerts={filteredData.summary.activeAlerts} />
              <StatusMetricCard
                title="Pending Maintenance"
                value={filteredData.summary.pendingMaintenance}
                status={filteredData.summary.pendingMaintenance > 5 ? 'warning' : 'good'}
                icon={<Clock className="h-6 w-6" />}
                details={[
                  'Scheduled maintenance tasks',
                  `${filteredData.summary.criticalCount} critical priority`,
                  'Next: Weekly inspection'
                ]}
              />
              <TemperatureCard temperature={filteredData.summary.avgTemperature} />
              <SystemHealthCard health={95} />
            </div>

            {/* Charts Section */}
            <Tabs defaultValue="overview" className="space-y-6">
              <TabsList className="grid w-full grid-cols-5 lg:w-auto lg:grid-cols-5">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="performance" className="flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Performance
                </TabsTrigger>
                <TabsTrigger value="regional" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Regional
                </TabsTrigger>
                <TabsTrigger value="health" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Health
                </TabsTrigger>
                <TabsTrigger value="analytics" className="flex items-center gap-2">
                  <PieChartIcon className="h-4 w-4" />
                  Analytics
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <StatusPieChart
                    data={statusData}
                    title="Transformer Status Distribution"
                  />
                  <RegionalBarChart
                    data={regionalData}
                    title="Regional Distribution"
                  />
                </div>
              </TabsContent>

              <TabsContent value="performance" className="space-y-6">
                <PerformanceTrendChart
                  data={performanceTrend}
                  title="Real-time Performance Trends"
                />
              </TabsContent>

              <TabsContent value="regional" className="space-y-6">
                <RegionalBarChart
                  data={regionalData}
                  title="Regional Performance Comparison"
                />
              </TabsContent>

              <TabsContent value="health" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <SystemHealthRadial
                    data={systemHealth}
                    title="System Health Score"
                  />
                  <ModernChartCard title="Temperature Monitoring">
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={performanceTrend}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" strokeWidth={1} />
                        <XAxis dataKey="time" stroke="#64748b" fontSize={12} tickLine={false} axisLine={false} />
                        <YAxis stroke="#64748b" fontSize={12} tickLine={false} axisLine={false} />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            border: 'none',
                            borderRadius: '12px',
                            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                            backdropFilter: 'blur(10px)'
                          }}
                        />
                        <Line
                          type="monotone"
                          dataKey="temperature"
                          stroke={COLORS.danger}
                          strokeWidth={3}
                          dot={{ fill: COLORS.danger, strokeWidth: 2, r: 4 }}
                          activeDot={{ r: 6, stroke: COLORS.danger, strokeWidth: 2 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </ModernChartCard>
                </div>
              </TabsContent>

              <TabsContent value="analytics" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-blue-100">Efficiency Score</p>
                          <p className="text-3xl font-bold">{filteredData.summary.avgEfficiency.toFixed(1)}%</p>
                        </div>
                        <Gauge className="h-8 w-8 text-blue-200" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-lg bg-gradient-to-br from-green-500 to-green-600 text-white">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-green-100">Uptime</p>
                          <p className="text-3xl font-bold">99.8%</p>
                        </div>
                        <Activity className="h-8 w-8 text-green-200" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-500 to-purple-600 text-white">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-purple-100">Load Factor</p>
                          <p className="text-3xl font-bold">{filteredData.summary.avgLoadFactor.toFixed(1)}%</p>
                        </div>
                        <Battery className="h-8 w-8 text-purple-200" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </div>
  )
}
