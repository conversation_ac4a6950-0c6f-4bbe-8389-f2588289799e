"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import {
  Activity,
  Zap,
  Thermometer,
  Gauge,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  MapPin,
  RefreshCw,
  Search,
  Filter,
  Download,
  Settings,
  Eye,
  BarChart3,
  LineChart,
  PieChart,
  Monitor,
  Wifi,
  WifiOff,
  Battery,
  Signal,
  Radio,
  Cpu,
  HardDrive,
  Power,
  Fan
} from 'lucide-react'
import {
  LineChart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON>ontainer,
  ReferenceLine
} from 'recharts'
import { TransformerLayout } from '@/components/transformer/TransformerLayout'
import { ProtectedRoute } from "@/src/components/layout/protected-route"
import ProvidersWrapper from "@/components/providers-wrapper"

// Mock real-time monitoring data
const mockMonitoringData = [
  {
    id: 'T-AA-001',
    name: 'Lideta Primary Transformer',
    location: 'Lideta Substation, Addis Ababa',
    status: 'operational',
    isOnline: true,
    lastUpdate: '2024-02-12T14:30:00Z',
    realTimeData: {
      temperature: {
        oil: 65,
        winding: 72,
        ambient: 28,
        threshold: 85
      },
      voltage: {
        primary: 132.5,
        secondary: 33.2,
        nominal: { primary: 132, secondary: 33 }
      },
      current: {
        primary: 245.8,
        secondary: 987.2,
        rated: { primary: 378, secondary: 1512 }
      },
      power: {
        active: 42.5,
        reactive: 12.3,
        apparent: 44.2,
        factor: 0.96
      },
      load: {
        percentage: 78,
        kva: 39000,
        rated: 50000
      },
      frequency: 50.1,
      efficiency: 98.5,
      oilLevel: 95,
      tapPosition: 5,
      alarms: []
    },
    trends: Array.from({ length: 24 }, (_, i) => ({
      time: `${i}:00`,
      temperature: 60 + Math.random() * 15,
      load: 70 + Math.random() * 20,
      voltage: 131 + Math.random() * 3,
      efficiency: 97 + Math.random() * 2
    }))
  },
  {
    id: 'T-OR-045',
    name: 'Sebeta Distribution Transformer',
    location: 'Sebeta Substation, Oromia',
    status: 'warning',
    isOnline: true,
    lastUpdate: '2024-02-12T14:28:00Z',
    realTimeData: {
      temperature: {
        oil: 82,
        winding: 89,
        ambient: 32,
        threshold: 85
      },
      voltage: {
        primary: 66.1,
        secondary: 11.05,
        nominal: { primary: 66, secondary: 11 }
      },
      current: {
        primary: 189.3,
        secondary: 1134.6,
        rated: { primary: 218, secondary: 1309 }
      },
      power: {
        active: 21.2,
        reactive: 8.7,
        apparent: 22.9,
        factor: 0.93
      },
      load: {
        percentage: 87,
        kva: 21750,
        rated: 25000
      },
      frequency: 49.9,
      efficiency: 97.8,
      oilLevel: 88,
      tapPosition: 3,
      alarms: ['High Temperature', 'High Load']
    },
    trends: Array.from({ length: 24 }, (_, i) => ({
      time: `${i}:00`,
      temperature: 75 + Math.random() * 15,
      load: 80 + Math.random() * 15,
      voltage: 65 + Math.random() * 2,
      efficiency: 96 + Math.random() * 3
    }))
  },
  {
    id: 'T-AM-023',
    name: 'Bahir Dar Distribution Transformer',
    location: 'Bahir Dar Substation, Amhara',
    status: 'offline',
    isOnline: false,
    lastUpdate: '2024-02-11T09:15:00Z',
    realTimeData: {
      temperature: {
        oil: 0,
        winding: 0,
        ambient: 25,
        threshold: 85
      },
      voltage: {
        primary: 0,
        secondary: 0,
        nominal: { primary: 33, secondary: 11 }
      },
      current: {
        primary: 0,
        secondary: 0,
        rated: { primary: 262, secondary: 787 }
      },
      power: {
        active: 0,
        reactive: 0,
        apparent: 0,
        factor: 0
      },
      load: {
        percentage: 0,
        kva: 0,
        rated: 15000
      },
      frequency: 0,
      efficiency: 0,
      oilLevel: 82,
      tapPosition: 0,
      alarms: ['Communication Lost', 'Offline']
    },
    trends: Array.from({ length: 24 }, (_, i) => ({
      time: `${i}:00`,
      temperature: 0,
      load: 0,
      voltage: 0,
      efficiency: 0
    }))
  }
]

const monitoringStats = {
  totalTransformers: 1247,
  onlineTransformers: 1156,
  offlineTransformers: 67,
  warningTransformers: 24,
  criticalAlarms: 5,
  avgLoad: 73.2,
  avgEfficiency: 97.8,
  systemHealth: 94.2
}

const statusColors = {
  operational: 'bg-green-100 text-green-800 border-green-200',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  critical: 'bg-red-100 text-red-800 border-red-200',
  offline: 'bg-gray-100 text-gray-800 border-gray-200'
}

const getStatusIcon = (status: string, isOnline: boolean) => {
  if (!isOnline) return <WifiOff className="h-4 w-4 text-gray-500" />

  switch (status) {
    case 'operational':
      return <CheckCircle className="h-4 w-4 text-green-500" />
    case 'warning':
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    case 'critical':
      return <AlertTriangle className="h-4 w-4 text-red-500" />
    default:
      return <Activity className="h-4 w-4 text-blue-500" />
  }
}

export default function TransformerMonitoringPage() {
  const [monitoringData, setMonitoringData] = useState(mockMonitoringData)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [autoRefresh, setAutoRefresh] = useState(true)

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Auto-refresh data every 30 seconds
    if (autoRefresh) {
      const interval = setInterval(() => {
        // Simulate real-time data updates
        setMonitoringData(prev => prev.map(transformer => ({
          ...transformer,
          lastUpdate: new Date().toISOString(),
          realTimeData: {
            ...transformer.realTimeData,
            temperature: {
              ...transformer.realTimeData.temperature,
              oil: transformer.realTimeData.temperature.oil + (Math.random() - 0.5) * 2,
              winding: transformer.realTimeData.temperature.winding + (Math.random() - 0.5) * 2
            },
            load: {
              ...transformer.realTimeData.load,
              percentage: Math.max(0, Math.min(100, transformer.realTimeData.load.percentage + (Math.random() - 0.5) * 5))
            }
          }
        })))
      }, 30000)

      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  const filteredData = monitoringData.filter(transformer => {
    const matchesSearch = transformer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transformer.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transformer.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || transformer.status === selectedStatus

    return matchesSearch && matchesStatus
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading monitoring dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <ProvidersWrapper>
      <ProtectedRoute
        allowedRoles={[
          "super_admin",
          "national_asset_manager",
          "national_maintenance_manager",
          "regional_admin",
          "regional_maintenance_engineer",
          "service_center_manager",
          "field_technician"
        ]}
      >
        <TransformerLayout>
          <div className="space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Transformer Monitoring</h1>
                <p className="text-muted-foreground">
                  Real-time monitoring and performance tracking for Ethiopian Electric Utility transformers
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant={autoRefresh ? "default" : "outline"}
                  onClick={() => setAutoRefresh(!autoRefresh)}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
                  Auto Refresh
                </Button>
                <Button variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </Button>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </Button>
              </div>
            </div>

            {/* System Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Online Transformers</CardTitle>
                  <Wifi className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{monitoringStats.onlineTransformers}</div>
                  <p className="text-xs text-muted-foreground">
                    {((monitoringStats.onlineTransformers / monitoringStats.totalTransformers) * 100).toFixed(1)}% online
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">System Health</CardTitle>
                  <Activity className="h-4 w-4 text-blue-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">{monitoringStats.systemHealth}%</div>
                  <p className="text-xs text-muted-foreground">
                    Overall system health
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Load</CardTitle>
                  <Gauge className="h-4 w-4 text-purple-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">{monitoringStats.avgLoad}%</div>
                  <p className="text-xs text-muted-foreground">
                    Fleet average load
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Critical Alarms</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">{monitoringStats.criticalAlarms}</div>
                  <p className="text-xs text-muted-foreground">
                    Require immediate attention
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Main Monitoring Interface */}
            <Tabs defaultValue="overview" className="space-y-4">
              <TabsList>
                <TabsTrigger value="overview">System Overview</TabsTrigger>
                <TabsTrigger value="realtime">Real-time Data</TabsTrigger>
                <TabsTrigger value="alarms">Alarms & Alerts</TabsTrigger>
                <TabsTrigger value="trends">Trends & Analytics</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                {/* Filters */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-4">
                      <div className="flex-1">
                        <Input
                          placeholder="Search transformers..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="max-w-sm"
                        />
                      </div>
                      <select
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                        className="border rounded-md px-3 py-2"
                      >
                        <option value="all">All Status</option>
                        <option value="operational">Operational</option>
                        <option value="warning">Warning</option>
                        <option value="critical">Critical</option>
                        <option value="offline">Offline</option>
                      </select>
                    </div>
                  </CardContent>
                </Card>

                {/* Transformer Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                  {filteredData.map((transformer) => (
                    <Card key={transformer.id} className="hover:shadow-lg transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <CardTitle className="text-lg">{transformer.name}</CardTitle>
                            <CardDescription className="flex items-center gap-1 mt-1">
                              <MapPin className="h-3 w-3" />
                              {transformer.location}
                            </CardDescription>
                          </div>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(transformer.status, transformer.isOnline)}
                            <Badge className={statusColors[transformer.status as keyof typeof statusColors]}>
                              {transformer.status}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {/* Key Metrics */}
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-1">
                            <div className="flex items-center gap-1">
                              <Thermometer className="h-3 w-3 text-red-500" />
                              <span className="text-xs font-medium">Temperature</span>
                            </div>
                            <div className="text-lg font-bold">
                              {transformer.realTimeData.temperature.oil}°C
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Oil temperature
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center gap-1">
                              <Gauge className="h-3 w-3 text-blue-500" />
                              <span className="text-xs font-medium">Load</span>
                            </div>
                            <div className="text-lg font-bold">
                              {transformer.realTimeData.load.percentage}%
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {transformer.realTimeData.load.kva.toLocaleString()} kVA
                            </div>
                          </div>
                        </div>

                        {/* Load Bar */}
                        <div className="space-y-2">
                          <div className="flex justify-between text-xs">
                            <span>Load Factor</span>
                            <span>{transformer.realTimeData.load.percentage}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                transformer.realTimeData.load.percentage > 90 ? 'bg-red-500' :
                                transformer.realTimeData.load.percentage > 75 ? 'bg-yellow-500' :
                                'bg-green-500'
                              }`}
                              style={{ width: `${transformer.realTimeData.load.percentage}%` }}
                            ></div>
                          </div>
                        </div>

                        {/* Voltage & Current */}
                        <div className="grid grid-cols-2 gap-4 text-xs">
                          <div>
                            <div className="font-medium">Primary</div>
                            <div>{transformer.realTimeData.voltage.primary} kV</div>
                            <div>{transformer.realTimeData.current.primary} A</div>
                          </div>
                          <div>
                            <div className="font-medium">Secondary</div>
                            <div>{transformer.realTimeData.voltage.secondary} kV</div>
                            <div>{transformer.realTimeData.current.secondary} A</div>
                          </div>
                        </div>

                        {/* Alarms */}
                        {transformer.realTimeData.alarms.length > 0 && (
                          <div className="space-y-1">
                            <div className="text-xs font-medium text-red-600">Active Alarms:</div>
                            {transformer.realTimeData.alarms.map((alarm, index) => (
                              <Badge key={index} variant="destructive" className="text-xs mr-1">
                                {alarm}
                              </Badge>
                            ))}
                          </div>
                        )}

                        {/* Last Update */}
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Last update: {new Date(transformer.lastUpdate).toLocaleTimeString()}
                          </div>
                          <Button size="sm" variant="outline">
                            <Eye className="h-3 w-3 mr-1" />
                            Details
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="realtime" className="space-y-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Real-time Charts */}
                  <Card>
                    <CardHeader>
                      <CardTitle>System Load Distribution</CardTitle>
                      <CardDescription>Current load across all transformers</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={filteredData.map(t => ({
                          name: t.id,
                          load: t.realTimeData.load.percentage,
                          capacity: 100
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="load" fill="#3b82f6" />
                          <ReferenceLine y={75} stroke="#f59e0b" strokeDasharray="5 5" label="Warning" />
                          <ReferenceLine y={90} stroke="#ef4444" strokeDasharray="5 5" label="Critical" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Temperature Monitoring</CardTitle>
                      <CardDescription>Oil temperature across transformers</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={filteredData.map(t => ({
                          name: t.id,
                          temperature: t.realTimeData.temperature.oil,
                          threshold: t.realTimeData.temperature.threshold
                        }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="temperature" fill="#ef4444" />
                          <ReferenceLine y={85} stroke="#f59e0b" strokeDasharray="5 5" label="Threshold" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Real-time Data Table */}
                <Card>
                  <CardHeader>
                    <CardTitle>Live Data Feed</CardTitle>
                    <CardDescription>Real-time transformer parameters</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">Transformer</th>
                            <th className="text-left p-2">Status</th>
                            <th className="text-left p-2">Load (%)</th>
                            <th className="text-left p-2">Temperature (°C)</th>
                            <th className="text-left p-2">Voltage (kV)</th>
                            <th className="text-left p-2">Efficiency (%)</th>
                            <th className="text-left p-2">Last Update</th>
                          </tr>
                        </thead>
                        <tbody>
                          {filteredData.map((transformer) => (
                            <tr key={transformer.id} className="border-b hover:bg-gray-50">
                              <td className="p-2 font-medium">{transformer.id}</td>
                              <td className="p-2">
                                <Badge className={statusColors[transformer.status as keyof typeof statusColors]}>
                                  {transformer.status}
                                </Badge>
                              </td>
                              <td className="p-2">{transformer.realTimeData.load.percentage}%</td>
                              <td className="p-2">{transformer.realTimeData.temperature.oil}°C</td>
                              <td className="p-2">{transformer.realTimeData.voltage.primary}</td>
                              <td className="p-2">{transformer.realTimeData.efficiency}%</td>
                              <td className="p-2">{new Date(transformer.lastUpdate).toLocaleTimeString()}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="alarms" className="space-y-4">
                {/* Active Alarms Summary */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-red-600">Critical</p>
                          <p className="text-2xl font-bold text-red-600">3</p>
                        </div>
                        <AlertTriangle className="h-8 w-8 text-red-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-yellow-600">Warning</p>
                          <p className="text-2xl font-bold text-yellow-600">8</p>
                        </div>
                        <AlertTriangle className="h-8 w-8 text-yellow-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-blue-600">Info</p>
                          <p className="text-2xl font-bold text-blue-600">12</p>
                        </div>
                        <Activity className="h-8 w-8 text-blue-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">Offline</p>
                          <p className="text-2xl font-bold text-gray-600">5</p>
                        </div>
                        <WifiOff className="h-8 w-8 text-gray-500" />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Active Alarms List */}
                <Card>
                  <CardHeader>
                    <CardTitle>Active Alarms</CardTitle>
                    <CardDescription>Current system alarms requiring attention</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        {
                          id: 'ALM-001',
                          transformer: 'T-OR-045',
                          severity: 'critical',
                          type: 'High Temperature',
                          message: 'Oil temperature exceeded 85°C threshold',
                          timestamp: '2024-02-12T14:25:00Z',
                          acknowledged: false
                        },
                        {
                          id: 'ALM-002',
                          transformer: 'T-OR-045',
                          severity: 'warning',
                          type: 'High Load',
                          message: 'Load factor exceeded 85% for 15 minutes',
                          timestamp: '2024-02-12T14:20:00Z',
                          acknowledged: false
                        },
                        {
                          id: 'ALM-003',
                          transformer: 'T-AM-023',
                          severity: 'critical',
                          type: 'Communication Lost',
                          message: 'No data received for 2 hours',
                          timestamp: '2024-02-12T12:15:00Z',
                          acknowledged: true
                        },
                        {
                          id: 'ALM-004',
                          transformer: 'T-AA-001',
                          severity: 'info',
                          type: 'Maintenance Due',
                          message: 'Scheduled maintenance due in 7 days',
                          timestamp: '2024-02-12T08:00:00Z',
                          acknowledged: false
                        }
                      ].map((alarm) => (
                        <div key={alarm.id} className="flex items-start justify-between p-4 border rounded-lg">
                          <div className="flex items-start gap-3">
                            <div className={`w-3 h-3 rounded-full mt-1 ${
                              alarm.severity === 'critical' ? 'bg-red-500' :
                              alarm.severity === 'warning' ? 'bg-yellow-500' :
                              'bg-blue-500'
                            }`}></div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium">{alarm.transformer}</span>
                                <Badge className={
                                  alarm.severity === 'critical' ? 'bg-red-100 text-red-800' :
                                  alarm.severity === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-blue-100 text-blue-800'
                                }>
                                  {alarm.type}
                                </Badge>
                                {alarm.acknowledged && (
                                  <Badge variant="outline">Acknowledged</Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground mb-2">{alarm.message}</p>
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <Clock className="h-3 w-3" />
                                {new Date(alarm.timestamp).toLocaleString()}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {!alarm.acknowledged && (
                              <Button size="sm" variant="outline">
                                Acknowledge
                              </Button>
                            )}
                            <Button size="sm" variant="outline">
                              <Eye className="h-3 w-3 mr-1" />
                              Details
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Alarm History */}
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Alarm History</CardTitle>
                    <CardDescription>Last 24 hours alarm activity</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <AreaChart data={Array.from({ length: 24 }, (_, i) => ({
                        hour: `${i}:00`,
                        critical: Math.floor(Math.random() * 3),
                        warning: Math.floor(Math.random() * 5) + 2,
                        info: Math.floor(Math.random() * 8) + 3
                      }))}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="hour" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Area type="monotone" dataKey="critical" stackId="1" stroke="#ef4444" fill="#ef4444" />
                        <Area type="monotone" dataKey="warning" stackId="1" stroke="#f59e0b" fill="#f59e0b" />
                        <Area type="monotone" dataKey="info" stackId="1" stroke="#3b82f6" fill="#3b82f6" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="trends" className="space-y-4">
                {/* Performance Trends */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Load Trend Analysis</CardTitle>
                      <CardDescription>24-hour load patterns across transformers</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsLineChart data={mockMonitoringData[0].trends}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="time" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="load" stroke="#3b82f6" name="Load %" />
                          <ReferenceLine y={75} stroke="#f59e0b" strokeDasharray="5 5" label="Warning" />
                          <ReferenceLine y={90} stroke="#ef4444" strokeDasharray="5 5" label="Critical" />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Temperature Trends</CardTitle>
                      <CardDescription>Oil temperature monitoring over 24 hours</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsLineChart data={mockMonitoringData[0].trends}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="time" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="temperature" stroke="#ef4444" name="Temperature °C" />
                          <ReferenceLine y={85} stroke="#f59e0b" strokeDasharray="5 5" label="Threshold" />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Voltage Stability</CardTitle>
                      <CardDescription>Primary voltage variations</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsLineChart data={mockMonitoringData[0].trends}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="time" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="voltage" stroke="#10b981" name="Voltage kV" />
                          <ReferenceLine y={132} stroke="#6b7280" strokeDasharray="5 5" label="Nominal" />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Efficiency Tracking</CardTitle>
                      <CardDescription>Transformer efficiency over time</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <RechartsLineChart data={mockMonitoringData[0].trends}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="time" />
                          <YAxis domain={[95, 100]} />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="efficiency" stroke="#8b5cf6" name="Efficiency %" />
                          <ReferenceLine y={97} stroke="#f59e0b" strokeDasharray="5 5" label="Target" />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Predictive Analytics */}
                <Card>
                  <CardHeader>
                    <CardTitle>Predictive Analytics</CardTitle>
                    <CardDescription>AI-powered insights and maintenance predictions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <h4 className="font-medium">Maintenance Predictions</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center p-2 bg-yellow-50 rounded">
                            <span className="text-sm">T-OR-045</span>
                            <Badge variant="outline">7 days</Badge>
                          </div>
                          <div className="flex justify-between items-center p-2 bg-blue-50 rounded">
                            <span className="text-sm">T-AA-001</span>
                            <Badge variant="outline">14 days</Badge>
                          </div>
                          <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                            <span className="text-sm">T-AM-023</span>
                            <Badge variant="outline">21 days</Badge>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium">Risk Assessment</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                            <span className="text-sm">High Temperature Risk</span>
                            <Badge className="bg-red-100 text-red-800">High</Badge>
                          </div>
                          <div className="flex justify-between items-center p-2 bg-yellow-50 rounded">
                            <span className="text-sm">Overload Risk</span>
                            <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>
                          </div>
                          <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                            <span className="text-sm">Failure Risk</span>
                            <Badge className="bg-green-100 text-green-800">Low</Badge>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium">Optimization Suggestions</h4>
                        <div className="space-y-2">
                          <div className="p-2 bg-blue-50 rounded">
                            <p className="text-sm font-medium">Load Balancing</p>
                            <p className="text-xs text-muted-foreground">Redistribute load from T-OR-045</p>
                          </div>
                          <div className="p-2 bg-green-50 rounded">
                            <p className="text-sm font-medium">Cooling Optimization</p>
                            <p className="text-xs text-muted-foreground">Improve ventilation at Sebeta</p>
                          </div>
                          <div className="p-2 bg-purple-50 rounded">
                            <p className="text-sm font-medium">Efficiency Boost</p>
                            <p className="text-xs text-muted-foreground">Tap position adjustment recommended</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </TransformerLayout>
      </ProtectedRoute>
    </ProvidersWrapper>
  )
}
