"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuth } from "@/src/features/auth/context/auth-context"
import type { UserRole } from "@/src/types/auth"

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles?: UserRole[]
  requiredPermissions?: Array<{
    resource: string
    action: string
  }>
}

export function ProtectedRoute({ children, allowedRoles, requiredPermissions }: ProtectedRouteProps) {
  const { user, isLoading, hasRole, hasPermission } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [isAuthorized, setIsAuthorized] = useState(false)

  useEffect(() => {
    // Only run this effect once the loading state is resolved
    if (isLoading) return

    if (!user) {
      // Redirect to login if not authenticated
      router.push(`/login`)
      return
    }

    // Check role-based access
    if (allowedRoles && !hasRole(allowedRoles)) {
      router.push("/unauthorized")
      return
    }

    // Check permission-based access
    if (requiredPermissions) {
      const hasAllPermissions = requiredPermissions.every(({ resource, action }) => hasPermission(resource, action))

      if (!hasAllPermissions) {
        router.push("/unauthorized")
        return
      }
    }

    // If we get here, the user is authorized
    setIsAuthorized(true)
  }, [user, isLoading, router, pathname, allowedRoles, requiredPermissions, hasRole, hasPermission])

  // Show loading state while checking authentication or authorization
  if (isLoading || (!isAuthorized && user)) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    )
  }

  // If not authenticated or not authorized, don't render children
  if (!user || !isAuthorized) {
    return null
  }

  // Otherwise, render the protected content
  return <>{children}</>
}
