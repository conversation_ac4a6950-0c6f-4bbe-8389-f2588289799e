"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltipContent } from "@/src/components/ui/chart"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Button } from "@/src/components/ui/button"
import { Download, Filter } from "lucide-react"

const data = [
  {
    id: "TRF-1024",
    name: "Addis Ababa Main",
    failureProbability: 12,
    recommendedDate: "May 15, 2025",
    healthScore: 88,
  },
  {
    id: "TRF-0872",
    name: "<PERSON>re Dawa <PERSON>",
    failureProbability: 28,
    recommendedDate: "May 5, 2025",
    healthScore: 72,
  },
  {
    id: "TRF-1105",
    name: "<PERSON><PERSON>",
    failureProbability: 8,
    recommendedDate: "Jun 20, 2025",
    healthScore: 92,
  },
  {
    id: "TRF-0945",
    name: "<PERSON>was<PERSON>",
    failureProbability: 45,
    recommendedDate: "Apr 30, 2025",
    healthScore: 55,
  },
  {
    id: "TRF-1187",
    name: "Mekelle Main",
    failureProbability: 18,
    recommendedDate: "May 25, 2025",
    healthScore: 82,
  },
  {
    id: "TRF-1322",
    name: "Jimma District",
    failureProbability: 32,
    recommendedDate: "May 10, 2025",
    healthScore: 68,
  },
  {
    id: "TRF-0991",
    name: "Gondar Area",
    failureProbability: 15,
    recommendedDate: "Jun 5, 2025",
    healthScore: 85,
  },
]

const chartData = data.map((item) => ({
  name: item.id,
  failureProbability: item.failureProbability,
  healthScore: item.healthScore,
}))

export function PredictiveMaintenanceChart() {
  const [timeframe, setTimeframe] = useState("30days")

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <Select value={timeframe} onValueChange={setTimeframe}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select timeframe" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">Next 7 Days</SelectItem>
            <SelectItem value="30days">Next 30 Days</SelectItem>
            <SelectItem value="90days">Next 90 Days</SelectItem>
          </SelectContent>
        </Select>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Failure Probability Chart</CardTitle>
            <CardDescription>
              AI-predicted failure probability within{" "}
              {timeframe === "7days" ? "7" : timeframe === "30days" ? "30" : "90"} days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ChartContainer
                config={{
                  failureProbability: {
                    label: "Failure Probability (%)",
                    color: "hsl(0, 85%, 60%)",
                  },
                  healthScore: {
                    label: "Health Score",
                    color: "hsl(143, 85%, 40%)",
                  },
                }}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={chartData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip content={<ChartTooltipContent />} />
                    <Legend />
                    <Bar dataKey="failureProbability" fill="var(--color-failureProbability)" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="healthScore" fill="var(--color-healthScore)" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Maintenance Recommendations</CardTitle>
            <CardDescription>AI-generated maintenance schedule based on predictive analysis</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data
                .sort((a, b) => b.failureProbability - a.failureProbability)
                .slice(0, 5)
                .map((item) => (
                  <div key={item.id} className="flex items-center justify-between border-b pb-2 last:border-0">
                    <div>
                      <p className="font-medium">
                        {item.id} - {item.name}
                      </p>
                      <p className="text-xs text-muted-foreground">Recommended before: {item.recommendedDate}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className={`h-2 w-16 rounded-full ${
                          item.failureProbability > 30
                            ? "bg-red-500"
                            : item.failureProbability > 15
                              ? "bg-yellow-500"
                              : "bg-green-500"
                        }`}
                      >
                        <div
                          className="h-full rounded-full bg-green-500"
                          style={{ width: `${100 - item.failureProbability}%` }}
                        />
                      </div>
                      <span
                        className={
                          item.failureProbability > 30
                            ? "text-red-500"
                            : item.failureProbability > 15
                              ? "text-yellow-500"
                              : "text-green-500"
                        }
                      >
                        {item.failureProbability}%
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
