import { User } from '@/src/types/auth';

// Mock user data for demonstration
const mockUsers: Record<string, User> = {
  "<EMAIL>": {
    id: "user-001",
    name: "Admin User",
    email: "<EMAIL>",
    role: "super_admin",
    organizationalLevel: "head_office",
    permissions: [
      { resource: "users", action: "create" },
      { resource: "users", action: "read" },
      { resource: "users", action: "update" },
      { resource: "users", action: "delete" },
      // Super admin has all permissions
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=AU",
    phone: "+251-911-123-456",
    department: "IT Administration",
    location: "Addis Ababa",
    bio: "System administrator responsible for managing the EEU Transformer Management System.",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "user-002",
    name: "Asset Manager",
    email: "<EMAIL>",
    role: "national_asset_manager",
    organizationalLevel: "head_office",
    permissions: [
      { resource: "transformers", action: "read" },
      { resource: "transformers", action: "update" },
      { resource: "transformers", action: "approve" },
      { resource: "reports", action: "export" },
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=AM",
    phone: "+251-911-234-567",
    department: "Asset Management",
    location: "Addis Ababa",
    bio: "National asset manager responsible for overseeing transformer assets across the country.",
    createdAt: "2023-01-15T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "user-003",
    name: "Regional Admin",
    email: "<EMAIL>",
    role: "regional_admin",
    organizationalLevel: "regional_office",
    regionId: "region-001", // Addis Ababa
    permissions: [
      { resource: "users", action: "create" },
      { resource: "users", action: "read" },
      { resource: "users", action: "update" },
      { resource: "transformers", action: "read" },
      { resource: "transformers", action: "update" },
      { resource: "maintenance", action: "assign" },
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=RA",
    phone: "+251-911-345-678",
    department: "Regional Administration",
    location: "Addis Ababa",
    bio: "Regional administrator for the Addis Ababa region, managing local operations and staff.",
    createdAt: "2023-02-01T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "user-004",
    name: "Service Center Manager",
    email: "<EMAIL>",
    role: "service_center_manager",
    organizationalLevel: "service_center",
    regionId: "region-001", // Addis Ababa
    serviceCenterId: "sc-001", // Bole Service Center
    permissions: [
      { resource: "transformers", action: "read" },
      { resource: "transformers", action: "update" },
      { resource: "maintenance", action: "assign" },
      { resource: "maintenance", action: "approve" },
      { resource: "reports", action: "export" },
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=SM",
    phone: "+251-911-456-789",
    department: "Service Operations",
    location: "Bole, Addis Ababa",
    bio: "Service center manager for the Bole district, overseeing local maintenance and service operations.",
    createdAt: "2023-02-15T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "user-005",
    name: "Field Technician",
    email: "<EMAIL>",
    role: "field_technician",
    organizationalLevel: "service_center",
    regionId: "region-001", // Addis Ababa
    serviceCenterId: "sc-001", // Bole Service Center
    permissions: [
      { resource: "transformers", action: "read" },
      { resource: "transformers", action: "update" },
      { resource: "maintenance", action: "update" },
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=FT",
    phone: "+251-911-567-890",
    department: "Field Operations",
    location: "Bole, Addis Ababa",
    bio: "Field technician responsible for on-site maintenance and repair of transformers.",
    createdAt: "2023-03-01T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "user-006",
    name: "Customer Service",
    email: "<EMAIL>",
    role: "customer_service_agent",
    organizationalLevel: "service_center",
    regionId: "region-001", // Addis Ababa
    serviceCenterId: "sc-001", // Bole Service Center
    permissions: [
      { resource: "outages", action: "read" },
      { resource: "customer_requests", action: "create" },
      { resource: "customer_requests", action: "read" },
      { resource: "customer_requests", action: "update" },
    ],
    isActive: true,
    lastLogin: new Date().toISOString(),
    avatar: "/placeholder.svg?height=40&width=40&text=CS",
    phone: "+251-911-678-901",
    department: "Customer Support",
    location: "Bole, Addis Ababa",
    bio: "Customer service agent handling customer inquiries and service requests.",
    createdAt: "2023-03-15T00:00:00Z",
    updatedAt: new Date().toISOString(),
  },
};

/**
 * Service for handling user data
 */
class UserService {
  /**
   * Get all users
   */
  async getAllUsers(): Promise<User[]> {
    return Object.values(mockUsers);
  }

  /**
   * Get a user by ID
   */
  async getUserById(id: string): Promise<User | null> {
    const user = Object.values(mockUsers).find(user => user.id === id);
    return user || null;
  }

  /**
   * Get a user by email
   */
  async getUserByEmail(email: string): Promise<User | null> {
    console.log("UserService.getUserByEmail called with email:", email);
    console.log("Available emails:", Object.keys(mockUsers));

    const user = mockUsers[email] || null;
    console.log("User found:", user ? "Yes" : "No");

    return user;
  }

  /**
   * Add a new user
   */
  async addUser(user: User): Promise<User | null> {
    if (!user.email) {
      return null;
    }

    mockUsers[user.email] = user;
    return user;
  }

  /**
   * Update a user
   */
  async updateUser(user: User): Promise<User | null> {
    if (!user.email || !mockUsers[user.email]) {
      return null;
    }

    mockUsers[user.email] = { ...mockUsers[user.email], ...user };
    return mockUsers[user.email];
  }

  /**
   * Delete a user
   */
  async deleteUser(id: string): Promise<boolean> {
    const userEmail = Object.keys(mockUsers).find(email => mockUsers[email].id === id);

    if (!userEmail) {
      return false;
    }

    delete mockUsers[userEmail];
    return true;
  }

  /**
   * Authenticate a user
   */
  async authenticateUser(email: string, password: string): Promise<User | null> {
    console.log("UserService.authenticateUser called with email:", email);

    // In a real app, this would verify the password
    // For demo purposes, we'll just return the user if the email exists
    const user = await this.getUserByEmail(email);

    console.log("User found:", user);

    // For demo purposes, any password is valid
    return user;
  }

  /**
   * Update last login time
   */
  async updateLastLogin(id: string): Promise<boolean> {
    const user = await this.getUserById(id);

    if (!user || !user.email) {
      return false;
    }

    mockUsers[user.email] = {
      ...user,
      lastLogin: new Date().toISOString()
    };

    return true;
  }
}

// Export singleton instance
export const userService = new UserService();
