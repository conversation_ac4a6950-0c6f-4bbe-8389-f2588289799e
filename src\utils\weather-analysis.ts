import type { WeatherData, WeatherForecast, WeatherImpact } from '@/src/hooks/use-weather-data'

export interface WeatherRiskAssessment {
  overall: 'low' | 'medium' | 'high' | 'critical'
  factors: {
    temperature: number
    humidity: number
    windSpeed: number
    lightning: number
    precipitation: number
  }
  recommendations: string[]
  affectedAreas: string[]
}

export interface WeatherForecastAnalysis {
  nextDayRisk: 'low' | 'medium' | 'high' | 'critical'
  weeklyTrend: 'improving' | 'stable' | 'deteriorating'
  criticalPeriods: Array<{
    date: string
    risk: 'low' | 'medium' | 'high' | 'critical'
    factors: string[]
  }>
  recommendations: string[]
}

export interface HistoricalWeatherImpact {
  totalIncidents: number
  weatherRelatedOutages: number
  averageDowntime: number
  mostVulnerableMonths: string[]
  commonCauses: Array<{
    cause: string
    frequency: number
    impact: string
  }>
}

/**
 * Analyze current weather conditions and their impact on transformer operations
 */
export function analyzeWeatherImpact(weatherData: WeatherData): WeatherRiskAssessment {
  const factors = {
    temperature: calculateTemperatureRisk(weatherData.temperature),
    humidity: calculateHumidityRisk(weatherData.humidity),
    windSpeed: calculateWindRisk(weatherData.windSpeed),
    lightning: calculateLightningRisk(weatherData.condition),
    precipitation: calculatePrecipitationRisk(weatherData.condition)
  }

  // Calculate overall risk
  const riskScores = Object.values(factors)
  const averageRisk = riskScores.reduce((sum, score) => sum + score, 0) / riskScores.length
  
  let overall: WeatherRiskAssessment['overall']
  if (averageRisk >= 0.8) overall = 'critical'
  else if (averageRisk >= 0.6) overall = 'high'
  else if (averageRisk >= 0.4) overall = 'medium'
  else overall = 'low'

  const recommendations = generateRecommendations(factors, weatherData)
  const affectedAreas = identifyAffectedAreas(factors, weatherData)

  return {
    overall,
    factors,
    recommendations,
    affectedAreas
  }
}

/**
 * Analyze weather forecast for upcoming risks
 */
export function analyzeWeatherForecast(forecast: WeatherForecast[]): WeatherForecastAnalysis {
  if (!forecast || forecast.length === 0) {
    return {
      nextDayRisk: 'low',
      weeklyTrend: 'stable',
      criticalPeriods: [],
      recommendations: ['No forecast data available']
    }
  }

  // Analyze next day risk
  const nextDay = forecast[0]
  const nextDayRisk = calculateForecastRisk(nextDay)

  // Analyze weekly trend
  const weeklyTrend = calculateWeeklyTrend(forecast)

  // Identify critical periods
  const criticalPeriods = forecast.map(day => ({
    date: day.date,
    risk: calculateForecastRisk(day),
    factors: identifyRiskFactors(day)
  })).filter(period => period.risk === 'high' || period.risk === 'critical')

  // Generate recommendations
  const recommendations = generateForecastRecommendations(forecast, criticalPeriods)

  return {
    nextDayRisk,
    weeklyTrend,
    criticalPeriods,
    recommendations
  }
}

/**
 * Calculate historical weather impact on transformer operations
 */
export function calculateHistoricalImpact(): HistoricalWeatherImpact {
  // Mock historical data - in a real app, this would come from a database
  return {
    totalIncidents: 156,
    weatherRelatedOutages: 42,
    averageDowntime: 3.2, // hours
    mostVulnerableMonths: ['June', 'July', 'August', 'September'],
    commonCauses: [
      { cause: 'Lightning strikes', frequency: 35, impact: 'High' },
      { cause: 'High winds', frequency: 28, impact: 'Medium' },
      { cause: 'Heavy rainfall', frequency: 22, impact: 'Medium' },
      { cause: 'Extreme temperatures', frequency: 15, impact: 'Low' }
    ]
  }
}

// Helper functions

function calculateTemperatureRisk(temperature: number): number {
  if (temperature > 40 || temperature < 0) return 1.0 // Critical
  if (temperature > 35 || temperature < 5) return 0.8 // High
  if (temperature > 30 || temperature < 10) return 0.4 // Medium
  return 0.1 // Low
}

function calculateHumidityRisk(humidity: number): number {
  if (humidity > 90) return 0.8 // High
  if (humidity > 80) return 0.6 // Medium-High
  if (humidity > 70) return 0.3 // Medium
  return 0.1 // Low
}

function calculateWindRisk(windSpeed: number): number {
  if (windSpeed > 30) return 1.0 // Critical
  if (windSpeed > 25) return 0.8 // High
  if (windSpeed > 15) return 0.4 // Medium
  return 0.1 // Low
}

function calculateLightningRisk(condition: string): number {
  const lightningConditions = ['thunderstorm', 'storm', 'lightning']
  if (lightningConditions.some(cond => condition.toLowerCase().includes(cond))) {
    return 1.0 // Critical
  }
  if (condition.toLowerCase().includes('cloudy')) return 0.2
  return 0.1 // Low
}

function calculatePrecipitationRisk(condition: string): number {
  if (condition.toLowerCase().includes('heavy rain')) return 0.8
  if (condition.toLowerCase().includes('rain')) return 0.4
  if (condition.toLowerCase().includes('drizzle')) return 0.2
  return 0.1
}

function generateRecommendations(factors: any, weatherData: WeatherData): string[] {
  const recommendations: string[] = []

  if (factors.temperature > 0.6) {
    recommendations.push('Monitor transformer cooling systems')
    recommendations.push('Check oil temperature levels')
  }

  if (factors.humidity > 0.6) {
    recommendations.push('Inspect insulation systems')
    recommendations.push('Monitor for condensation issues')
  }

  if (factors.windSpeed > 0.6) {
    recommendations.push('Secure loose equipment and structures')
    recommendations.push('Monitor overhead power lines')
  }

  if (factors.lightning > 0.8) {
    recommendations.push('Activate lightning protection systems')
    recommendations.push('Consider temporary load reduction')
    recommendations.push('Prepare emergency response teams')
  }

  if (factors.precipitation > 0.6) {
    recommendations.push('Check drainage systems')
    recommendations.push('Monitor for flooding risks')
  }

  if (recommendations.length === 0) {
    recommendations.push('Continue normal operations with standard monitoring')
  }

  return recommendations
}

function identifyAffectedAreas(factors: any, weatherData: WeatherData): string[] {
  const areas: string[] = []
  
  // Mock affected areas based on risk factors
  if (factors.windSpeed > 0.6) {
    areas.push('Northern Region', 'Highland Areas')
  }
  
  if (factors.lightning > 0.8) {
    areas.push('Central Region', 'Urban Areas')
  }
  
  if (factors.precipitation > 0.6) {
    areas.push('Southern Region', 'Low-lying Areas')
  }

  return areas.length > 0 ? areas : ['All Regions']
}

function calculateForecastRisk(forecast: WeatherForecast): 'low' | 'medium' | 'high' | 'critical' {
  let riskScore = 0

  // Temperature risk
  if (forecast.temperature.max > 35 || forecast.temperature.min < 5) riskScore += 0.3
  
  // Humidity risk
  if (forecast.humidity > 80) riskScore += 0.2
  
  // Wind risk
  if (forecast.windSpeed > 25) riskScore += 0.3
  else if (forecast.windSpeed > 15) riskScore += 0.1
  
  // Precipitation risk
  if (forecast.precipitation > 70) riskScore += 0.2
  else if (forecast.precipitation > 40) riskScore += 0.1

  if (riskScore >= 0.8) return 'critical'
  if (riskScore >= 0.6) return 'high'
  if (riskScore >= 0.3) return 'medium'
  return 'low'
}

function calculateWeeklyTrend(forecast: WeatherForecast[]): 'improving' | 'stable' | 'deteriorating' {
  if (forecast.length < 3) return 'stable'

  const risks = forecast.slice(0, 7).map(calculateForecastRisk)
  const riskValues = risks.map(risk => {
    switch (risk) {
      case 'critical': return 4
      case 'high': return 3
      case 'medium': return 2
      case 'low': return 1
      default: return 1
    }
  })

  const firstHalf = riskValues.slice(0, Math.floor(riskValues.length / 2))
  const secondHalf = riskValues.slice(Math.floor(riskValues.length / 2))

  const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length
  const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length

  if (secondAvg > firstAvg + 0.5) return 'deteriorating'
  if (firstAvg > secondAvg + 0.5) return 'improving'
  return 'stable'
}

function identifyRiskFactors(forecast: WeatherForecast): string[] {
  const factors: string[] = []

  if (forecast.temperature.max > 35) factors.push('High temperature')
  if (forecast.temperature.min < 5) factors.push('Low temperature')
  if (forecast.humidity > 80) factors.push('High humidity')
  if (forecast.windSpeed > 25) factors.push('Strong winds')
  if (forecast.precipitation > 70) factors.push('Heavy precipitation')

  return factors
}

function generateForecastRecommendations(
  forecast: WeatherForecast[], 
  criticalPeriods: any[]
): string[] {
  const recommendations: string[] = []

  if (criticalPeriods.length > 0) {
    recommendations.push('Prepare for adverse weather conditions')
    recommendations.push('Review emergency response procedures')
    recommendations.push('Ensure backup systems are operational')
  }

  const hasHighWind = forecast.some(day => day.windSpeed > 25)
  if (hasHighWind) {
    recommendations.push('Secure outdoor equipment before high wind periods')
  }

  const hasHeavyRain = forecast.some(day => day.precipitation > 70)
  if (hasHeavyRain) {
    recommendations.push('Check drainage systems and flood defenses')
  }

  const hasExtremeTemp = forecast.some(day => 
    day.temperature.max > 35 || day.temperature.min < 5
  )
  if (hasExtremeTemp) {
    recommendations.push('Monitor transformer cooling and heating systems')
  }

  if (recommendations.length === 0) {
    recommendations.push('Weather conditions appear favorable for normal operations')
  }

  return recommendations
}
