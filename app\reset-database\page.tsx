'use client'

import { useState, useEffect } from 'react'

export default function ResetDatabasePage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [bypassStatus, setBypassStatus] = useState<any>(null)

  // Check bypass status on page load
  useEffect(() => {
    checkBypassStatus()
  }, [])

  const checkBypassStatus = async () => {
    try {
      const response = await fetch('/api/check-bypass-tables')
      const data = await response.json()
      setBypassStatus(data)
    } catch (err) {
      console.error('Failed to check bypass status:', err)
    }
  }

  const resetDatabase = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/reset-database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        setResult(data)
      } else {
        setError(data.message || 'Database reset failed')
      }
    } catch (err) {
      setError('Failed to connect to API')
      console.error('Database reset error:', err)
    } finally {
      setLoading(false)
    }
  }

  const recreateTables = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/recreate-tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        setResult(data)
      } else {
        setError(data.message || 'Table recreation failed')
      }
    } catch (err) {
      setError('Failed to connect to API')
      console.error('Table recreation error:', err)
    } finally {
      setLoading(false)
    }
  }

  const forceCleanup = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/force-cleanup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        setResult(data)
      } else {
        setError(data.message || 'Force cleanup failed')
      }
    } catch (err) {
      setError('Failed to connect to API')
      console.error('Force cleanup error:', err)
    } finally {
      setLoading(false)
    }
  }

  const bypassTablespace = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/bypass-tablespace', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        setResult(data)
      } else {
        setError(data.message || 'Tablespace bypass failed')
      }
    } catch (err) {
      setError('Failed to connect to API')
      console.error('Tablespace bypass error:', err)
    } finally {
      setLoading(false)
    }
  }

  const seedBypassData = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/seed-bypass', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        setResult(data)
      } else {
        setError(data.message || 'Seeding failed')
      }
    } catch (err) {
      setError('Failed to connect to API')
      console.error('Seeding error:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">🔄 Database Reset</h1>
          <p className="text-gray-600">
            Reset the database to fix storage engine issues and ensure clean setup.
          </p>
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800">
              ⚠️ <strong>Warning:</strong> This will completely reset the database and all existing data will be lost.
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Database Reset Options</h2>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
            {/* Full Reset Option */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-red-600">🔄 Full Database Reset</h3>
              <p className="text-gray-600 text-sm">
                Complete reset (may fail on Windows):
              </p>
              <ul className="list-disc list-inside space-y-1 text-gray-600 text-sm ml-4">
                <li>Drop the existing dtms_eeu_db database</li>
                <li>Create a new database with proper charset</li>
                <li>Create all tables with InnoDB engine</li>
                <li>Seed with Ethiopian transformer data</li>
              </ul>

              <button
                onClick={resetDatabase}
                disabled={loading}
                className={`w-full px-4 py-3 rounded-lg font-medium ${
                  loading
                    ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                    : 'bg-red-600 text-white hover:bg-red-700'
                }`}
              >
                {loading ? '🔄 Resetting...' : '🔄 Full Reset'}
              </button>
            </div>

            {/* Table Recreation Option */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">🔧 Recreate Tables Only</h3>
              <p className="text-gray-600 text-sm">
                Safer option (recommended):
              </p>
              <ul className="list-disc list-inside space-y-1 text-gray-600 text-sm ml-4">
                <li>Drop all existing tables</li>
                <li>Recreate tables with InnoDB engine</li>
                <li>Keep the existing database</li>
                <li>Seed with Ethiopian transformer data</li>
              </ul>

              <button
                onClick={recreateTables}
                disabled={loading}
                className={`w-full px-4 py-3 rounded-lg font-medium ${
                  loading
                    ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {loading ? '🔄 Recreating...' : '🔧 Recreate Tables'}
              </button>
            </div>

            {/* Force Cleanup Option */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-green-600">🧹 Force Cleanup</h3>
              <p className="text-gray-600 text-sm">
                Nuclear option (fixes tablespace issues):
              </p>
              <ul className="list-disc list-inside space-y-1 text-gray-600 text-sm ml-4">
                <li>Force discard all tablespaces</li>
                <li>Drop all tables completely</li>
                <li>Create fresh tables with new tablespaces</li>
                <li>Seed with Ethiopian transformer data</li>
              </ul>

              <button
                onClick={forceCleanup}
                disabled={loading}
                className={`w-full px-4 py-3 rounded-lg font-medium ${
                  loading
                    ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {loading ? '🔄 Cleaning...' : '🧹 Force Cleanup'}
              </button>
            </div>

            {/* Tablespace Bypass Option */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-purple-600">🚀 Tablespace Bypass</h3>
              <p className="text-gray-600 text-sm">
                Ultimate solution (bypasses corruption):
              </p>
              <ul className="list-disc list-inside space-y-1 text-gray-600 text-sm ml-4">
                <li>Create tables with completely new names</li>
                <li>Bypass corrupted tablespace files entirely</li>
                <li>Create views with standard names</li>
                <li>Ready for seeding with Ethiopian data</li>
              </ul>

              <button
                onClick={bypassTablespace}
                disabled={loading}
                className={`w-full px-4 py-3 rounded-lg font-medium ${
                  loading
                    ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                    : 'bg-purple-600 text-white hover:bg-purple-700'
                }`}
              >
                {loading ? '🔄 Bypassing...' : '🚀 Bypass Tablespace'}
              </button>
            </div>
          </div>

          {loading && (
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 text-blue-600">
                🔄 Resetting database... This may take a few moments.
              </div>
            </div>
          )}

          {error && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-600">
                ❌ {error}
              </div>
            </div>
          )}

          {result && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-green-600">
                  ✅ {result.message}
                </div>

                <div className="text-sm text-gray-600">
                  {result.schemaSuccess !== undefined && (
                    <div>Schema Success: {result.schemaSuccess ? '✅' : '❌'}</div>
                  )}
                  {result.tablesSuccess !== undefined && (
                    <div>Tables Success: {result.tablesSuccess ? '✅' : '❌'}</div>
                  )}
                  {result.cleanupSuccess !== undefined && (
                    <div>Cleanup Success: {result.cleanupSuccess ? '✅' : '❌'}</div>
                  )}
                  {result.bypassSuccess !== undefined && (
                    <div>Bypass Success: {result.bypassSuccess ? '✅' : '❌'}</div>
                  )}
                  {result.seedSuccess !== undefined && (
                    <div>Seed Success: {result.seedSuccess ? '✅' : '❌'}</div>
                  )}
                  <div>Timestamp: {result.timestamp}</div>
                </div>

                <div className="mt-4 pt-4 border-t border-green-200">
                  <p className="text-green-700 font-medium">Next Steps:</p>
                  <ul className="list-disc list-inside space-y-1 text-green-600 text-sm mt-2">
                    <li>Click "Seed Data" below to add Ethiopian transformer data</li>
                    <li>Go to <a href="/check-database" className="underline">Database Status</a> to verify tables</li>
                    <li>Go to <a href="/" className="underline">Dashboard</a> to see real data</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Seed Data Section */}
          {((result && result.bypassSuccess) || (bypassStatus && bypassStatus.bypassSuccess)) && (
            <div className="mt-6 bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">🌱 Seed Database with Ethiopian Data</h2>
              <p className="text-gray-600 mb-4">
                {bypassStatus && bypassStatus.dataExists
                  ? "Data already exists in the database. You can re-seed if needed."
                  : "Now that the tables are created, add Ethiopian transformer data to test the dashboard."
                }
              </p>

              {bypassStatus && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm">
                  <div>📊 Bypass Tables: {bypassStatus.bypassTables}</div>
                  <div>📋 Standard Views: {bypassStatus.standardViews}</div>
                  <div>💾 Data Exists: {bypassStatus.dataExists ? '✅ Yes' : '❌ No'}</div>
                </div>
              )}

              <button
                onClick={seedBypassData}
                disabled={loading}
                className={`px-6 py-3 rounded-lg font-medium ${
                  loading
                    ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {loading ? '🔄 Seeding Data...' : '🌱 Seed Data'}
              </button>
            </div>
          )}
        </div>

        <div className="mt-6 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">🔗 Quick Links</h2>
          <div className="grid gap-3 md:grid-cols-3">
            <a
              href="/check-database"
              className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 text-center"
            >
              🔍 Check Database Status
            </a>
            <a
              href="/setup-database"
              className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 text-center"
            >
              ⚙️ Setup Database
            </a>
            <a
              href="/"
              className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 text-center"
            >
              🏠 Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
