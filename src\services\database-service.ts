"use client"

/**
 * Database Service
 *
 * This service provides a unified interface to access database data
 * and replaces the mock data usage throughout the application.
 */

import { transformerRepository } from '@/src/lib/db/repositories/transformer-repository';
import { alertRepository } from '@/src/lib/db/repositories/alert-repository';
import { maintenanceRepository } from '@/src/lib/db/repositories/maintenance-repository';
import { userRepository } from '@/src/lib/db/repositories/user-repository';
import { regionRepository } from '@/src/lib/db/repositories/region-repository';
import { serviceCenterRepository } from '@/src/lib/db/repositories/service-center-repository';
import { outageRepository } from '@/src/lib/db/repositories/outage-repository';
import { weatherAlertRepository } from '@/src/lib/db/repositories/weather-alert-repository';
import { ensureDbInitialized } from '@/src/lib/db/init-app-db';

export class DatabaseService {

  /**
   * Get all transformers
   */
  static async getTransformers() {
    try {
      await ensureDbInitialized();
      return transformerRepository.getAll();
    } catch (error) {
      console.error('Error fetching transformers:', error);
      return [];
    }
  }

  /**
   * Get transformer statistics
   */
  static async getTransformerStatistics() {
    try {
      await ensureDbInitialized();
      const transformers = transformerRepository.getAll();

      // Calculate statistics
      const total = transformers.length;

      // Count by status
      const byStatus = transformers.reduce((acc, transformer) => {
        acc[transformer.status] = (acc[transformer.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Count by region
      const byRegion = transformers.reduce((acc, transformer) => {
        const region = regionRepository.getById(transformer.regionId);
        if (region) {
          acc[region.name] = (acc[region.name] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);

      // Calculate maintenance statistics
      const maintenanceRecords = maintenanceRepository.getAll();
      const maintenance = {
        scheduled: maintenanceRecords.filter(m => m.status === 'scheduled').length,
        overdue: maintenanceRecords.filter(m => m.status === 'overdue').length,
        completed: maintenanceRecords.filter(m => m.status === 'completed').length,
        total: maintenanceRecords.length
      };

      // Calculate alert statistics
      const alerts = alertRepository.getAll();
      const alertStats = {
        critical: alerts.filter(a => a.severity === 'critical' && !a.isResolved).length,
        warning: alerts.filter(a => a.severity === 'high' && !a.isResolved).length,
        info: alerts.filter(a => ['medium', 'low'].includes(a.severity) && !a.isResolved).length,
        total: alerts.filter(a => !a.isResolved).length
      };

      return {
        total,
        byStatus,
        byRegion,
        maintenance,
        alerts: alertStats
      };
    } catch (error) {
      console.error('Error calculating transformer statistics:', error);
      return {
        total: 0,
        byStatus: {},
        byRegion: {},
        maintenance: { scheduled: 0, overdue: 0, completed: 0, total: 0 },
        alerts: { critical: 0, warning: 0, info: 0, total: 0 }
      };
    }
  }

  /**
   * Get transformers by region
   */
  static getTransformersByRegion(regionId: string) {
    try {
      return transformerRepository.findByRegion(regionId);
    } catch (error) {
      console.error('Error fetching transformers by region:', error);
      return [];
    }
  }

  /**
   * Get transformers by status
   */
  static getTransformersByStatus(status: string | string[]) {
    try {
      return transformerRepository.findByStatus(status as any);
    } catch (error) {
      console.error('Error fetching transformers by status:', error);
      return [];
    }
  }

  /**
   * Get all alerts
   */
  static getAlerts() {
    try {
      return alertRepository.getAll();
    } catch (error) {
      console.error('Error fetching alerts:', error);
      return [];
    }
  }

  /**
   * Get recent alerts
   */
  static getRecentAlerts(limit: number = 10) {
    try {
      const alerts = alertRepository.getAll();
      return alerts
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Error fetching recent alerts:', error);
      return [];
    }
  }

  /**
   * Get critical alerts
   */
  static getCriticalAlerts() {
    try {
      const alerts = alertRepository.getAll();
      return alerts.filter(alert =>
        alert.severity === 'critical' && !alert.isResolved
      );
    } catch (error) {
      console.error('Error fetching critical alerts:', error);
      return [];
    }
  }

  /**
   * Get all maintenance records
   */
  static getMaintenanceRecords() {
    try {
      return maintenanceRepository.getAll();
    } catch (error) {
      console.error('Error fetching maintenance records:', error);
      return [];
    }
  }

  /**
   * Get maintenance records by transformer
   */
  static getMaintenanceByTransformer(transformerId: string) {
    try {
      const records = maintenanceRepository.getAll();
      return records.filter(record => record.transformerId === transformerId);
    } catch (error) {
      console.error('Error fetching maintenance by transformer:', error);
      return [];
    }
  }

  /**
   * Get all regions
   */
  static getRegions() {
    try {
      return regionRepository.getAll();
    } catch (error) {
      console.error('Error fetching regions:', error);
      return [];
    }
  }

  /**
   * Get all service centers
   */
  static getServiceCenters() {
    try {
      return serviceCenterRepository.getAll();
    } catch (error) {
      console.error('Error fetching service centers:', error);
      return [];
    }
  }

  /**
   * Get service centers by region
   */
  static getServiceCentersByRegion(regionId: string) {
    try {
      const serviceCenters = serviceCenterRepository.getAll();
      return serviceCenters.filter(sc => sc.regionId === regionId);
    } catch (error) {
      console.error('Error fetching service centers by region:', error);
      return [];
    }
  }

  /**
   * Get all outages
   */
  static getOutages() {
    try {
      return outageRepository.getAll();
    } catch (error) {
      console.error('Error fetching outages:', error);
      return [];
    }
  }

  /**
   * Get active outages
   */
  static getActiveOutages() {
    try {
      const outages = outageRepository.getAll();
      return outages.filter(outage => outage.status === 'active');
    } catch (error) {
      console.error('Error fetching active outages:', error);
      return [];
    }
  }

  /**
   * Get weather alerts
   */
  static getWeatherAlerts() {
    try {
      return weatherAlertRepository.getAll();
    } catch (error) {
      console.error('Error fetching weather alerts:', error);
      return [];
    }
  }

  /**
   * Get active weather alerts
   */
  static getActiveWeatherAlerts() {
    try {
      const alerts = weatherAlertRepository.getAll();
      return alerts.filter(alert => alert.isActive);
    } catch (error) {
      console.error('Error fetching active weather alerts:', error);
      return [];
    }
  }

  /**
   * Search transformers
   */
  static searchTransformers(query: string) {
    try {
      return transformerRepository.search(query);
    } catch (error) {
      console.error('Error searching transformers:', error);
      return [];
    }
  }

  /**
   * Get dashboard data
   */
  static async getDashboardData() {
    try {
      await ensureDbInitialized();
      const transformerStats = await this.getTransformerStatistics();
      const recentAlerts = this.getRecentAlerts(5);
      const activeOutages = this.getActiveOutages();
      const weatherAlerts = this.getActiveWeatherAlerts();

      return {
        transformerStatistics: transformerStats,
        recentAlerts,
        activeOutages,
        weatherAlerts,
        isLoading: false
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      return {
        transformerStatistics: {
          total: 0,
          byStatus: {},
          byRegion: {},
          maintenance: { scheduled: 0, overdue: 0, completed: 0, total: 0 },
          alerts: { critical: 0, warning: 0, info: 0, total: 0 }
        },
        recentAlerts: [],
        activeOutages: [],
        weatherAlerts: [],
        isLoading: false
      };
    }
  }
}

export default DatabaseService;
