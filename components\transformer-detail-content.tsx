"use client"

import { useEffect, useState } from "react"
import { TransformerDetails } from "@/components/transformer-details"
import type { Transformer } from "@/src/types/transformer"
import { Skeleton } from "@/src/components/ui/skeleton"
import { AlertCircle } from "lucide-react"
import { But<PERSON> } from "@/src/components/ui/button"
import { useRouter } from "next/navigation"
import { useTransformers } from "@/src/contexts/transformer-context"

interface TransformerDetailContentProps {
  transformerId: string
}

export function TransformerDetailContent({ transformerId }: TransformerDetailContentProps) {
  const [transformer, setTransformer] = useState<Transformer | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const { getTransformerById } = useTransformers()

  useEffect(() => {
    const fetchTransformer = async () => {
      try {
        setLoading(true)
        const data = await getTransformerById(transformerId)
        if (data) {
          setTransformer(data)
          setError(null)
        } else {
          setError("Transformer not found")
        }
      } catch (err) {
        setError("Failed to load transformer details")
        console.error(err)
      } finally {
        setLoading(false)
      }
    }

    fetchTransformer()
  }, [transformerId, getTransformerById])

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-80" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-32" />
            <Skeleton className="h-9 w-32" />
            <Skeleton className="h-9 w-32" />
          </div>
        </div>
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-center">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h2 className="text-2xl font-bold mb-2">Error Loading Transformer</h2>
        <p className="text-muted-foreground mb-6">{error}</p>
        <Button onClick={() => router.push("/transformers")}>Return to Transformer List</Button>
      </div>
    )
  }

  if (!transformer) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-center">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h2 className="text-2xl font-bold mb-2">Transformer Not Found</h2>
        <p className="text-muted-foreground mb-6">The requested transformer could not be found.</p>
        <Button onClick={() => router.push("/transformers")}>Return to Transformer List</Button>
      </div>
    )
  }

  return <TransformerDetails transformer={transformer} />
}
