/**
 * Create All Dashboard Component Tables for EEU DTMS
 * This script creates comprehensive database tables for every dashboard component
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function createAllDashboardTables() {
  let connection;
  
  try {
    console.log('🏗️  CREATING ALL DASHBOARD COMPONENT TABLES');
    console.log('=' .repeat(70));
    console.log('🏢 Ethiopian Electric Utility');
    console.log('🔌 Digital Transformer Management System');
    console.log('📅 Table Creation Date:', new Date().toLocaleString());
    console.log('=' .repeat(70));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // 1. Enhanced Users Table
    console.log('\n👥 CREATING ENHANCED USERS TABLE');
    console.log('-' .repeat(40));
    
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        role ENUM('super_admin', 'national_asset_manager', 'national_maintenance_manager', 
                  'regional_admin', 'regional_asset_manager', 'regional_maintenance_engineer',
                  'service_center_manager', 'field_technician', 'customer_service_agent',
                  'audit_compliance_officer') NOT NULL,
        department VARCHAR(100),
        region_id INT,
        service_center_id INT,
        phone VARCHAR(20),
        avatar VARCHAR(255),
        is_active BOOLEAN DEFAULT true,
        last_login TIMESTAMP NULL,
        login_attempts INT DEFAULT 0,
        preferences JSON,
        permissions JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_role (role),
        INDEX idx_region (region_id),
        INDEX idx_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_users table');

    // 2. Enhanced Regions Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_regions (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(10) NOT NULL UNIQUE,
        population INT,
        area_km2 DECIMAL(10,2),
        capital_city VARCHAR(100),
        coordinates JSON,
        timezone VARCHAR(50) DEFAULT 'Africa/Addis_Ababa',
        weather_station_id VARCHAR(50),
        grid_connection_status ENUM('connected', 'partial', 'isolated') DEFAULT 'connected',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_code (code),
        INDEX idx_grid_status (grid_connection_status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_regions table');

    // 3. Service Centers Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_service_centers (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(200) NOT NULL,
        code VARCHAR(20) NOT NULL UNIQUE,
        region_id INT NOT NULL,
        address TEXT,
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        phone VARCHAR(20),
        email VARCHAR(255),
        manager_id INT,
        capacity INT DEFAULT 50,
        status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
        operating_hours JSON,
        emergency_contact VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES dtms_regions(id),
        FOREIGN KEY (manager_id) REFERENCES dtms_users(id),
        INDEX idx_region (region_id),
        INDEX idx_status (status),
        INDEX idx_code (code)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_service_centers table');

    // 4. Enhanced Transformers Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_transformers (
        id INT PRIMARY KEY AUTO_INCREMENT,
        serial_number VARCHAR(50) NOT NULL UNIQUE,
        name VARCHAR(200) NOT NULL,
        type ENUM('distribution', 'power', 'instrument', 'auto') NOT NULL,
        capacity_kva DECIMAL(10,2) NOT NULL,
        voltage_primary DECIMAL(10,2) NOT NULL,
        voltage_secondary DECIMAL(10,2) NOT NULL,
        manufacturer VARCHAR(100),
        model VARCHAR(100),
        year_manufactured YEAR,
        installation_date DATE,
        commissioning_date DATE,
        location_name VARCHAR(200),
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        region_id INT NOT NULL,
        service_center_id INT,
        status ENUM('operational', 'warning', 'maintenance', 'critical', 'burnt', 'offline') DEFAULT 'operational',
        efficiency_rating DECIMAL(5,2),
        load_factor DECIMAL(5,2),
        temperature DECIMAL(5,2),
        oil_level DECIMAL(5,2),
        health_index DECIMAL(5,2),
        last_maintenance DATE,
        next_maintenance DATE,
        maintenance_interval_days INT DEFAULT 90,
        criticality ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        customer_type ENUM('residential', 'commercial', 'industrial', 'agricultural') DEFAULT 'residential',
        asset_value DECIMAL(12,2),
        warranty_expiry DATE,
        insurance_policy VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES dtms_regions(id),
        FOREIGN KEY (service_center_id) REFERENCES dtms_service_centers(id),
        INDEX idx_status (status),
        INDEX idx_region (region_id),
        INDEX idx_serial (serial_number),
        INDEX idx_location (latitude, longitude),
        INDEX idx_criticality (criticality),
        INDEX idx_type (type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_transformers table');

    // 5. Enhanced Maintenance Schedules Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_maintenance_schedules (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT NOT NULL,
        type ENUM('routine', 'preventive', 'corrective', 'emergency', 'seasonal', 'predictive') NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        scheduled_date DATE NOT NULL,
        estimated_duration INT,
        actual_duration INT,
        priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        status ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'postponed', 'overdue') DEFAULT 'scheduled',
        technician_id INT,
        supervisor_id INT,
        team_size INT DEFAULT 1,
        cost DECIMAL(10,2),
        parts_used JSON,
        tools_required JSON,
        safety_requirements JSON,
        notes TEXT,
        completion_date DATE,
        completion_notes TEXT,
        quality_rating ENUM('excellent', 'good', 'satisfactory', 'poor') NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES dtms_transformers(id),
        FOREIGN KEY (technician_id) REFERENCES dtms_users(id),
        FOREIGN KEY (supervisor_id) REFERENCES dtms_users(id),
        INDEX idx_transformer (transformer_id),
        INDEX idx_status (status),
        INDEX idx_scheduled_date (scheduled_date),
        INDEX idx_type (type),
        INDEX idx_priority (priority)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_maintenance_schedules table');

    // 6. Enhanced Alerts Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_alerts (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        type ENUM('temperature', 'voltage', 'load', 'maintenance', 'communication', 'weather', 'security', 'performance') NOT NULL,
        status ENUM('active', 'investigating', 'resolved', 'monitoring', 'escalated') DEFAULT 'active',
        priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        source VARCHAR(100),
        threshold_value DECIMAL(10,2),
        actual_value DECIMAL(10,2),
        unit VARCHAR(20),
        created_by INT,
        assigned_to INT,
        resolved_at TIMESTAMP NULL,
        resolved_by INT,
        resolution_notes TEXT,
        is_resolved BOOLEAN DEFAULT false,
        escalation_level INT DEFAULT 0,
        escalated_at TIMESTAMP NULL,
        auto_generated BOOLEAN DEFAULT false,
        notification_sent BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES dtms_transformers(id),
        FOREIGN KEY (created_by) REFERENCES dtms_users(id),
        FOREIGN KEY (assigned_to) REFERENCES dtms_users(id),
        FOREIGN KEY (resolved_by) REFERENCES dtms_users(id),
        INDEX idx_severity (severity),
        INDEX idx_status (status),
        INDEX idx_type (type),
        INDEX idx_transformer (transformer_id),
        INDEX idx_created_at (created_at),
        INDEX idx_priority (priority)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_alerts table');

    // 7. Performance Metrics Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_performance_metrics (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT NOT NULL,
        metric_type ENUM('efficiency', 'load_factor', 'temperature', 'voltage', 'current', 'power_factor', 'oil_level', 'vibration') NOT NULL,
        value DECIMAL(10,4) NOT NULL,
        unit VARCHAR(20),
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        source VARCHAR(100),
        quality_flag ENUM('good', 'questionable', 'bad') DEFAULT 'good',
        measurement_method ENUM('manual', 'automatic', 'calculated') DEFAULT 'automatic',
        calibration_date DATE,
        FOREIGN KEY (transformer_id) REFERENCES dtms_transformers(id),
        INDEX idx_transformer_type (transformer_id, metric_type),
        INDEX idx_recorded_at (recorded_at),
        INDEX idx_metric_type (metric_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_performance_metrics table');

    // 8. Weather Data Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_weather_data (
        id INT PRIMARY KEY AUTO_INCREMENT,
        region_id INT NOT NULL,
        temperature DECIMAL(5,2),
        humidity DECIMAL(5,2),
        wind_speed DECIMAL(5,2),
        wind_direction DECIMAL(5,2),
        precipitation DECIMAL(5,2),
        weather_condition VARCHAR(50),
        risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
        uv_index DECIMAL(3,1),
        visibility DECIMAL(5,2),
        atmospheric_pressure DECIMAL(7,2),
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        source VARCHAR(100),
        FOREIGN KEY (region_id) REFERENCES dtms_regions(id),
        INDEX idx_region_time (region_id, recorded_at),
        INDEX idx_risk_level (risk_level)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_weather_data table');

    // 9. Notifications Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_notifications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
        category ENUM('system', 'maintenance', 'alert', 'user', 'report', 'weather') DEFAULT 'system',
        is_read BOOLEAN DEFAULT false,
        action_url VARCHAR(500),
        metadata JSON,
        expires_at TIMESTAMP NULL,
        sent_via ENUM('web', 'email', 'sms', 'push') DEFAULT 'web',
        delivery_status ENUM('pending', 'sent', 'delivered', 'failed') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES dtms_users(id),
        INDEX idx_user_read (user_id, is_read),
        INDEX idx_created_at (created_at),
        INDEX idx_category (category),
        INDEX idx_type (type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_notifications table');

    // 10. Outages Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_outages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        cause ENUM('equipment_failure', 'weather', 'maintenance', 'overload', 'external', 'unknown', 'cyber_attack') NOT NULL,
        severity ENUM('minor', 'major', 'critical') NOT NULL,
        customers_affected INT DEFAULT 0,
        estimated_duration INT,
        actual_duration INT,
        status ENUM('active', 'investigating', 'repairing', 'resolved', 'monitoring') DEFAULT 'active',
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        resolved_at TIMESTAMP NULL,
        reported_by INT,
        assigned_to INT,
        resolution_notes TEXT,
        financial_impact DECIMAL(12,2),
        regulatory_reported BOOLEAN DEFAULT false,
        FOREIGN KEY (transformer_id) REFERENCES dtms_transformers(id),
        FOREIGN KEY (reported_by) REFERENCES dtms_users(id),
        FOREIGN KEY (assigned_to) REFERENCES dtms_users(id),
        INDEX idx_transformer (transformer_id),
        INDEX idx_status (status),
        INDEX idx_started_at (started_at),
        INDEX idx_severity (severity)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_outages table');

    // 11. System Settings Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_system_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        data_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        category VARCHAR(50),
        description TEXT,
        is_public BOOLEAN DEFAULT false,
        is_editable BOOLEAN DEFAULT true,
        validation_rules JSON,
        updated_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (updated_by) REFERENCES dtms_users(id),
        INDEX idx_category (category),
        INDEX idx_key (setting_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_system_settings table');

    // 12. Reports Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_reports (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(200) NOT NULL,
        type ENUM('performance', 'maintenance', 'financial', 'regulatory', 'custom', 'dashboard') NOT NULL,
        description TEXT,
        parameters JSON,
        generated_by INT NOT NULL,
        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        file_path VARCHAR(500),
        file_size INT,
        format ENUM('pdf', 'excel', 'csv', 'json') DEFAULT 'pdf',
        status ENUM('generating', 'completed', 'failed', 'expired') DEFAULT 'generating',
        expires_at TIMESTAMP NULL,
        download_count INT DEFAULT 0,
        is_scheduled BOOLEAN DEFAULT false,
        schedule_frequency ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly') NULL,
        next_generation TIMESTAMP NULL,
        FOREIGN KEY (generated_by) REFERENCES dtms_users(id),
        INDEX idx_type (type),
        INDEX idx_generated_by (generated_by),
        INDEX idx_generated_at (generated_at),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_reports table');

    // 13. Dashboard Widgets Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_dashboard_widgets (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        widget_type ENUM('chart', 'metric', 'table', 'map', 'alert', 'status', 'trend') NOT NULL,
        title VARCHAR(200) NOT NULL,
        configuration JSON NOT NULL,
        position_x INT DEFAULT 0,
        position_y INT DEFAULT 0,
        width INT DEFAULT 4,
        height INT DEFAULT 3,
        is_visible BOOLEAN DEFAULT true,
        refresh_interval INT DEFAULT 300,
        data_source VARCHAR(100),
        filters JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES dtms_users(id),
        INDEX idx_user (user_id),
        INDEX idx_type (widget_type),
        INDEX idx_visible (is_visible)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_dashboard_widgets table');

    // 14. Audit Logs Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_audit_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        table_name VARCHAR(100),
        record_id INT,
        old_values JSON,
        new_values JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        session_id VARCHAR(255),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES dtms_users(id),
        INDEX idx_user (user_id),
        INDEX idx_action (action),
        INDEX idx_table (table_name),
        INDEX idx_timestamp (timestamp)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_audit_logs table');

    // 15. Asset Management Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_assets (
        id INT PRIMARY KEY AUTO_INCREMENT,
        asset_tag VARCHAR(50) NOT NULL UNIQUE,
        transformer_id INT,
        name VARCHAR(200) NOT NULL,
        category ENUM('transformer', 'switch', 'cable', 'meter', 'protection', 'monitoring') NOT NULL,
        subcategory VARCHAR(100),
        manufacturer VARCHAR(100),
        model VARCHAR(100),
        serial_number VARCHAR(100),
        purchase_date DATE,
        purchase_cost DECIMAL(12,2),
        current_value DECIMAL(12,2),
        depreciation_rate DECIMAL(5,2),
        warranty_expiry DATE,
        location VARCHAR(200),
        status ENUM('active', 'inactive', 'maintenance', 'retired', 'disposed') DEFAULT 'active',
        condition_rating ENUM('excellent', 'good', 'fair', 'poor', 'critical') DEFAULT 'good',
        last_inspection DATE,
        next_inspection DATE,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES dtms_transformers(id),
        INDEX idx_asset_tag (asset_tag),
        INDEX idx_transformer (transformer_id),
        INDEX idx_category (category),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_assets table');

    // 16. Energy Consumption Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_energy_consumption (
        id INT PRIMARY KEY AUTO_INCREMENT,
        transformer_id INT NOT NULL,
        reading_date DATE NOT NULL,
        energy_delivered DECIMAL(12,4),
        energy_consumed DECIMAL(12,4),
        peak_demand DECIMAL(10,4),
        power_factor DECIMAL(5,4),
        voltage_avg DECIMAL(8,4),
        current_avg DECIMAL(8,4),
        frequency DECIMAL(6,4),
        total_harmonic_distortion DECIMAL(6,4),
        meter_reading VARCHAR(50),
        reading_type ENUM('manual', 'automatic', 'estimated') DEFAULT 'automatic',
        quality_flag ENUM('good', 'estimated', 'questionable', 'bad') DEFAULT 'good',
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES dtms_transformers(id),
        INDEX idx_transformer_date (transformer_id, reading_date),
        INDEX idx_reading_date (reading_date),
        UNIQUE KEY unique_transformer_date (transformer_id, reading_date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_energy_consumption table');

    // 17. Spare Parts Inventory Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_spare_parts (
        id INT PRIMARY KEY AUTO_INCREMENT,
        part_number VARCHAR(100) NOT NULL UNIQUE,
        name VARCHAR(200) NOT NULL,
        description TEXT,
        category ENUM('electrical', 'mechanical', 'protection', 'monitoring', 'consumable') NOT NULL,
        manufacturer VARCHAR(100),
        model VARCHAR(100),
        unit_of_measure VARCHAR(20),
        unit_cost DECIMAL(10,2),
        quantity_in_stock INT DEFAULT 0,
        minimum_stock_level INT DEFAULT 0,
        maximum_stock_level INT DEFAULT 100,
        reorder_point INT DEFAULT 0,
        lead_time_days INT DEFAULT 30,
        storage_location VARCHAR(100),
        expiry_date DATE,
        is_critical BOOLEAN DEFAULT false,
        compatible_transformers JSON,
        supplier_info JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_part_number (part_number),
        INDEX idx_category (category),
        INDEX idx_stock_level (quantity_in_stock),
        INDEX idx_critical (is_critical)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_spare_parts table');

    // 18. Work Orders Table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dtms_work_orders (
        id INT PRIMARY KEY AUTO_INCREMENT,
        work_order_number VARCHAR(50) NOT NULL UNIQUE,
        transformer_id INT NOT NULL,
        maintenance_schedule_id INT,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        type ENUM('maintenance', 'repair', 'installation', 'inspection', 'emergency') NOT NULL,
        priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        status ENUM('created', 'assigned', 'in_progress', 'on_hold', 'completed', 'cancelled') DEFAULT 'created',
        created_by INT NOT NULL,
        assigned_to INT,
        estimated_hours DECIMAL(6,2),
        actual_hours DECIMAL(6,2),
        estimated_cost DECIMAL(10,2),
        actual_cost DECIMAL(10,2),
        scheduled_start TIMESTAMP NULL,
        actual_start TIMESTAMP NULL,
        scheduled_end TIMESTAMP NULL,
        actual_end TIMESTAMP NULL,
        parts_used JSON,
        tools_required JSON,
        safety_notes TEXT,
        completion_notes TEXT,
        customer_notification BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (transformer_id) REFERENCES dtms_transformers(id),
        FOREIGN KEY (maintenance_schedule_id) REFERENCES dtms_maintenance_schedules(id),
        FOREIGN KEY (created_by) REFERENCES dtms_users(id),
        FOREIGN KEY (assigned_to) REFERENCES dtms_users(id),
        INDEX idx_work_order_number (work_order_number),
        INDEX idx_transformer (transformer_id),
        INDEX idx_status (status),
        INDEX idx_priority (priority),
        INDEX idx_assigned_to (assigned_to)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    `);
    console.log('✅ Created dtms_work_orders table');

    // Final summary
    console.log('\n' + '=' .repeat(70));
    console.log('🎉 ALL DASHBOARD COMPONENT TABLES CREATED SUCCESSFULLY!');
    console.log('=' .repeat(70));

    console.log('\n📊 COMPREHENSIVE DATABASE SCHEMA SUMMARY:');
    console.log('  ✅ 18 Complete Tables for All Dashboard Components');
    console.log('  ✅ Full Foreign Key Relationships and Constraints');
    console.log('  ✅ Optimized Indexes for Performance');
    console.log('  ✅ JSON Fields for Flexible Data Storage');
    console.log('  ✅ Audit Trail and Security Features');

    console.log('\n🏗️  CREATED TABLES:');
    console.log('  1. ✅ dtms_users - Enhanced user management with roles and permissions');
    console.log('  2. ✅ dtms_regions - Ethiopian regions with geographic data');
    console.log('  3. ✅ dtms_service_centers - Service center management');
    console.log('  4. ✅ dtms_transformers - Comprehensive transformer data');
    console.log('  5. ✅ dtms_maintenance_schedules - Maintenance planning and tracking');
    console.log('  6. ✅ dtms_alerts - Multi-level alert system');
    console.log('  7. ✅ dtms_performance_metrics - Real-time performance data');
    console.log('  8. ✅ dtms_weather_data - Weather monitoring and risk assessment');
    console.log('  9. ✅ dtms_notifications - User notification system');
    console.log('  10. ✅ dtms_outages - Outage tracking and management');
    console.log('  11. ✅ dtms_system_settings - System configuration');
    console.log('  12. ✅ dtms_reports - Report generation and management');
    console.log('  13. ✅ dtms_dashboard_widgets - Customizable dashboard widgets');
    console.log('  14. ✅ dtms_audit_logs - Complete audit trail');
    console.log('  15. ✅ dtms_assets - Asset management and tracking');
    console.log('  16. ✅ dtms_energy_consumption - Energy monitoring and analytics');
    console.log('  17. ✅ dtms_spare_parts - Inventory management');
    console.log('  18. ✅ dtms_work_orders - Work order management system');

    console.log('\n🎯 DASHBOARD COMPONENTS SUPPORTED:');
    console.log('  📊 Status Distribution Charts (Pie, Bar, Donut)');
    console.log('  📈 Performance Analytics (Line, Area, Trend)');
    console.log('  🗺️  Geographic Maps (Regional, Location-based)');
    console.log('  🚨 Alert Management (Multi-level, Real-time)');
    console.log('  🔧 Maintenance Tracking (Schedules, Work Orders)');
    console.log('  ⚡ Energy Monitoring (Consumption, Efficiency)');
    console.log('  📱 User Dashboards (Customizable Widgets)');
    console.log('  📋 Report Generation (Automated, Scheduled)');
    console.log('  🏭 Asset Management (Inventory, Lifecycle)');
    console.log('  🌤️  Weather Integration (Risk Assessment)');
    console.log('  👥 User Management (Roles, Permissions)');
    console.log('  🔍 Audit & Compliance (Complete Trail)');

    console.log('\n🔧 DATABASE FEATURES:');
    console.log('  ✅ InnoDB Engine for ACID Compliance');
    console.log('  ✅ UTF8MB4 Character Set for International Support');
    console.log('  ✅ Comprehensive Foreign Key Constraints');
    console.log('  ✅ Optimized Indexes for Query Performance');
    console.log('  ✅ JSON Fields for Flexible Configuration');
    console.log('  ✅ Timestamp Tracking for All Records');
    console.log('  ✅ Enum Fields for Data Validation');
    console.log('  ✅ Decimal Precision for Financial Data');

    console.log('\n🌟 READY FOR:');
    console.log('  • Complete Ethiopian Electric Utility Operations');
    console.log('  • Real-time Transformer Monitoring and Management');
    console.log('  • Comprehensive Maintenance Planning and Execution');
    console.log('  • Multi-level Alert and Notification Systems');
    console.log('  • Advanced Analytics and Reporting');
    console.log('  • Asset Lifecycle Management');
    console.log('  • Energy Consumption Monitoring');
    console.log('  • Weather-based Risk Assessment');
    console.log('  • User Role-based Access Control');
    console.log('  • Complete Audit and Compliance Tracking');

    console.log('\n📱 NEXT STEPS:');
    console.log('  1. Run data seeding scripts to populate tables');
    console.log('  2. Configure dashboard widgets and layouts');
    console.log('  3. Set up automated report generation');
    console.log('  4. Configure alert thresholds and notifications');
    console.log('  5. Import existing transformer and asset data');

    console.log('\n🏆 DATABASE SCHEMA CREATION COMPLETE!');
    console.log('  Your EEU DTMS now has a comprehensive database foundation');
    console.log('  ready to support all dashboard components and operations!');

    console.log('\n✅ All dashboard component tables created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating dashboard tables:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Export for use in other scripts
module.exports = { createAllDashboardTables };

// Run if called directly
if (require.main === module) {
  createAllDashboardTables();
}
