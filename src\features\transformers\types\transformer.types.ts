export interface TransformerLocation {
  latitude: string;
  longitude: string;
  region: string;
  serviceCenter?: string;
  address?: string;
  installationSite?: string;
}

export interface MaintenanceRecord {
  date: string;
  type: string;
  description: string;
  performedBy: string;
  status: string;
  cost?: number;
}

export interface InspectionRecord {
  date: string;
  inspector: string;
  findings: string;
  recommendations: string;
  status: string;
}

export interface TestResult {
  date: string;
  type: string;
  result: string;
  performedBy: string;
  parameters: Record<string, any>;
}

export interface AbnormalityReport {
  id: string;
  reportDate: string;
  reportedBy: string;
  serviceCenterId: string;
  description: string;
  symptoms: string[];
  priority: "Low" | "Medium" | "High" | "Critical";
  status: "Reported" | "Under Investigation" | "Verified" | "Resolved";
  assignedTo?: string;
  verificationDate?: string;
  verifiedBy?: string;
  notes?: string[];
}

export interface MeggerTest {
  id: string;
  testDate: string;
  performedBy: string;
  switchgearTeamId: string;
  insulationResistanceHV: string;
  insulationResistanceLV: string;
  insulationResistanceHVLV: string;
  polarizationIndex: string;
  dielectricAbsorptionRatio: string;
  testVoltage: string;
  ambientTemperature: string;
  humidity: string;
  testEquipment: string;
  testDuration: string;
  result: "Pass" | "Fail" | "Borderline";
  recommendation: "Return to Service" | "Maintenance Required" | "Burnt - Replace" | "Further Testing Required";
  notes?: string;
}

export interface Transformer {
  id: string;
  serialNumber: string;
  manufacturer: string;
  model: string;
  type: string;
  capacity: string;
  ratingKVA: number;
  voltageRating?: string;
  installationDate: string;
  lastMaintenanceDate?: string;
  lastInspectionDate?: string;
  lastMaintenance?: string;
  nextMaintenance?: string;
  status: string;
  healthIndex: number;
  location: {
    region: string;
    serviceCenter: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    address: string;
  };
  maintenanceHistory?: MaintenanceRecord[];
  inspectionRecords?: InspectionRecord[];
  testResults?: TestResult[];
  abnormalityReports?: AbnormalityReport[];
  meggerTests?: MeggerTest[];
  failureDate?: string;
  failureCause?: string;
  failureVerifiedBy?: string;
  failureVerificationMethod?: string;
  maintenanceRecommendation?: string;
  workshopReferral?: boolean;
  workshopReferralDate?: string;
  workshopReferralNumber?: string;
  replacementDate?: string;
  replacementTransformerId?: string;
  manufacturingYear?: string;
  warrantyExpiration?: string;
  coolingType?: string;
  oilType?: string;
  impedance?: string;
  weight?: string;
  dimensions?: string;
  primaryVoltage?: number;
  secondaryVoltage?: number;
  connectionType?: string;
  phaseCount?: string;
  frequency?: string;
  temperatureRise?: string;
  insulationClass?: string;
  efficiencyRating?: string;
  noiseLevel?: string;
  specialFeatures?: string[];
  notes?: string;

  // Advanced monitoring data
  monitoringData?: {
    temperature?: number;
    loadPercentage?: number;
    oilLevel?: number;
    voltage?: {
      primary?: number;
      secondary?: number;
    };
    current?: {
      primary?: number;
      secondary?: number;
    };
    powerFactor?: number;
    lastUpdated?: string;
  };

  // Predictive maintenance data
  predictiveMaintenance?: {
    failureProbability?: number;
    estimatedLifespan?: number;
    recommendedActions?: {
      action: string;
      priority: 'low' | 'medium' | 'high' | 'critical';
      dueDate?: string;
    }[];
    healthTrend?: ('improving' | 'stable' | 'declining');
  };
}

export interface TransformerFilter {
  status?: string;
  region?: string;
  manufacturer?: string;
  type?: string;
  capacityRange?: {
    min: number;
    max: number;
  };
  installationDateRange?: {
    from: string;
    to: string;
  };
  serviceCenter?: string;
  voltageLevel?: string;
  manufacturingYear?: string;
}

export interface TransformerStatistics {
  total: number;
  byStatus: Record<string, number>;
  byRegion: Record<string, number>;
  byManufacturer: Record<string, number>;
  byType: Record<string, number>;
  byVoltageLevel: Record<string, number>;
  byYear: Record<string, number>;
  byServiceCenter: Record<string, number>;

  // Enhanced dashboard metrics
  operational: number;
  critical: number;
  maintenance: number;
  offline: number;
  burnt: number;

  // Performance metrics
  averageAge: number;
  totalCapacity: number;
  utilizationRate: number;
  reliabilityScore: number;

  // Maintenance metrics
  maintenanceStats: {
    scheduled: number;
    overdue: number;
    completed: number;
    pending: number;
  };

  // Alert metrics
  alerts: {
    critical: number;
    warning: number;
    info: number;
    total: number;
  };

  // Regional performance
  regionalPerformance: Record<string, {
    total: number;
    operational: number;
    critical: number;
    maintenance: number;
    reliability: number;
  }>;

  // Trend data
  trends: {
    monthly: Array<{
      month: string;
      operational: number;
      maintenance: number;
      critical: number;
    }>;
    failures: Array<{
      month: string;
      failures: number;
      repairs: number;
    }>;
    maintenance: Array<{
      month: string;
      scheduled: number;
      completed: number;
      overdue: number;
    }>;
  };

  abnormalityReports: {
    total: number;
    byStatus: Record<string, number>;
    byPriority: Record<string, number>;
    byServiceCenter: Record<string, number>;
    byRegion: Record<string, number>;
  };
  meggerTests: {
    total: number;
    byResult: Record<string, number>;
    byRecommendation: Record<string, number>;
    bySwitchgearTeam: Record<string, number>;
    byRegion: Record<string, number>;
  };
  burntTransformers: {
    total: number;
    byRegion: Record<string, number>;
    byManufacturer: Record<string, number>;
    byType: Record<string, number>;
    byVoltageLevel: Record<string, number>;
    byYear: Record<string, number>;
    byServiceCenter: Record<string, number>;
    byFailureCause: Record<string, number>;
    byVerificationMethod: Record<string, number>;
    byWorkshopReferral: {
      referred: number;
      notReferred: number;
    };
    byMeggerTestResult: Record<string, number>;
    byReplacementStatus: {
      replaced: number;
      notReplaced: number;
    };
    byAbnormalityReportPriority: Record<string, number>;
  };
}
