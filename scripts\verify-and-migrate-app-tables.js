// verify-and-migrate-app-tables.js
// Consolidated script to verify, migrate, and check schema/data for app_* tables and their _2 counterparts
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

async function getTables(connection, pattern) {
  // Directly interpolate the pattern (safe for LIKE)
  const [rows] = await connection.execute(
    `SHOW TABLES LIKE '${pattern}'`
  );
  return rows.map(row => Object.values(row)[0]);
}

async function getViews(connection, pattern) {
  const [rows] = await connection.execute(
    `SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_dtms_eeu_db LIKE '${pattern}'`
  );
  return rows.map(row => Object.values(row)[0]);
}

async function getRowCount(connection, table) {
  // Use proper backtick escaping for table names
  const [rows] = await connection.execute(`SELECT COUNT(*) as cnt FROM \`${table}\``);
  return rows[0].cnt;
}

async function verifyAndMigrate() {
  const connection = await mysql.createConnection(config);
  try {
    const appTables = await getTables(connection, 'app\_%');
    const app2Tables = await getTables(connection, 'app\_%2');
    const appViews = await getViews(connection, 'app\_%');

    console.log('Found app_* tables:', appTables);
    console.log('Found app_*2 tables:', app2Tables);
    console.log('Found app_* views:', appViews);

    // Check for missing _2 tables
    for (const table of appTables) {
      const t2 = table + '2';
      if (!app2Tables.includes(t2)) {
        console.warn(`❌ Missing _2 table for ${table}`);
      }
    }

    // Check for missing views
    for (const table of appTables) {
      if (!appViews.includes(table)) {
        console.warn(`❌ Missing compatibility view for ${table}`);
      }
    }

    // Compare row counts
    for (const table of appTables) {
      const t2 = table + '2';
      if (app2Tables.includes(t2)) {
        const [cnt1, cnt2] = await Promise.all([
          getRowCount(connection, table),
          getRowCount(connection, t2)
        ]);
        if (cnt1 !== cnt2) {
          console.warn(`⚠️ Row count mismatch: ${table} (${cnt1}) vs ${t2} (${cnt2})`);
        } else {
          console.log(`✅ Row count match: ${table} (${cnt1})`);
        }
      }
    }

    // Optionally: Add sample data comparison here

    console.log('Verification complete.');
  } catch (err) {
    console.error('❌ Error during verification:', err);
  } finally {
    await connection.end();
  }
}

verifyAndMigrate();
