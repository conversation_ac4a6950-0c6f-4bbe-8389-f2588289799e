/**
 * Dashboard service
 *
 * This service provides dashboard data from the JSON database.
 */

import { getDbService } from '@/src/lib/db-loader';

// Type definitions for dashboard data
export interface DashboardStats {
  totalTransformers: number
  healthyPercentage: number
  activeOutages: number
  criticalAlerts: number
  newTransformers: number
  healthyChange: number
  outageChange: number
  alertChange: number
}

export interface RegionalStats {
  region: string
  totalTransformers: number
  healthyPercentage: number
  warningPercentage: number
  criticalPercentage: number
  offlinePercentage: number
}

export interface MaintenanceStats {
  scheduled: number
  inProgress: number
  completed: number
  overdue: number
  preventive: number
  corrective: number
  emergency: number
}

export interface AlertStats {
  total: number
  critical: number
  high: number
  medium: number
  low: number
  resolved: number
  unresolved: number
}

export interface OutageStats {
  active: number
  scheduled: number
  resolved: number
  affectedCustomers: number
  averageDuration: number
}

export interface WeatherImpactStats {
  highRiskAreas: number
  affectedTransformers: number
  weatherAlerts: number
}

export interface DashboardData {
  stats: DashboardStats
  regionalStats: RegionalStats[]
  maintenanceStats: MaintenanceStats
  alertStats: AlertStats
  outageStats: OutageStats
  weatherImpactStats: WeatherImpactStats
}

import { cache } from "react"

// Dashboard service class
class DashboardService {
  // Initialize the database if needed
  private async ensureDatabaseInitialized(): Promise<void> {
    try {
      // Get the database service and initialize it
      const dbService = await getDbService();
      await dbService.initialize();
    } catch (error) {
      console.error('Error initializing database:', error);
    }
  }

  // Convert database stats to dashboard data format
  private convertToDashboardData(dbStats: any, timeRange: string = '30d'): DashboardData {
    // Extract stats from the database
    const { stats, regionalStats, transformerStats, alertStats, maintenanceStats, outageStats, weatherAlertStats } = dbStats;

    // Create maintenance stats
    const formattedMaintenanceStats: MaintenanceStats = {
      scheduled: maintenanceStats?.byStatus?.scheduled || 0,
      inProgress: maintenanceStats?.byStatus?.['in-progress'] || 0,
      completed: maintenanceStats?.byStatus?.completed || 0,
      overdue: maintenanceStats?.byStatus?.overdue || 0,
      preventive: maintenanceStats?.byType?.preventive || 0,
      corrective: maintenanceStats?.byType?.corrective || 0,
      emergency: maintenanceStats?.byType?.emergency || 0
    };

    // Create alert stats
    const formattedAlertStats: AlertStats = {
      total: alertStats?.total || 0,
      critical: alertStats?.bySeverity?.critical || 0,
      high: alertStats?.bySeverity?.high || 0,
      medium: alertStats?.bySeverity?.medium || 0,
      low: alertStats?.bySeverity?.low || 0,
      resolved: alertStats?.resolved || 0,
      unresolved: alertStats?.unresolved || 0
    };

    // Create outage stats
    const formattedOutageStats: OutageStats = {
      active: outageStats?.active || 0,
      scheduled: outageStats?.scheduled || 0,
      resolved: outageStats?.resolved || 0,
      affectedCustomers: outageStats?.totalAffectedCustomers || 0,
      averageDuration: outageStats?.averageDuration || 0
    };

    // Create weather impact stats
    const formattedWeatherImpactStats: WeatherImpactStats = {
      highRiskAreas: weatherAlertStats?.bySeverity?.high || 0,
      affectedTransformers: weatherAlertStats?.totalAffectedTransformers || 0,
      weatherAlerts: weatherAlertStats?.total || 0
    };

    // Apply time range adjustments to the stats
    let timeRangeMultiplier = 1;
    switch (timeRange) {
      case '7d':
        timeRangeMultiplier = 0.25;
        break;
      case '90d':
        timeRangeMultiplier = 3;
        break;
      case '1y':
        timeRangeMultiplier = 12;
        break;
    }

    // Adjust stats based on time range
    const newTransformers = Math.round(5 * timeRangeMultiplier);
    const healthyChange = Math.round(3 * timeRangeMultiplier);
    const outageChange = Math.round(-5 * timeRangeMultiplier);
    const alertChange = Math.round(2 * timeRangeMultiplier);

    // Create the dashboard data
    return {
      stats: {
        totalTransformers: stats.totalTransformers,
        healthyPercentage: stats.healthyPercentage,
        activeOutages: formattedOutageStats.active,
        criticalAlerts: formattedAlertStats.critical,
        newTransformers,
        healthyChange,
        outageChange,
        alertChange
      },
      regionalStats,
      maintenanceStats: formattedMaintenanceStats,
      alertStats: formattedAlertStats,
      outageStats: formattedOutageStats,
      weatherImpactStats: formattedWeatherImpactStats
    };
  }

  // Get initial dashboard data (server-side)
  // This function is cached to prevent duplicate requests during server rendering
  getInitialDashboardData = cache(async (): Promise<{
    data: DashboardData;
    lastUpdated: string;
  }> => {
    try {
      // Initialize the database if needed
      await this.ensureDatabaseInitialized();

      // Get dashboard statistics from the database
      const dbService = await getDbService();
      const dbStats = dbService.getDashboardStatistics();

      // Convert to dashboard data format
      const data = this.convertToDashboardData(dbStats);

      return {
        data,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error("Error getting initial dashboard data:", error);

      // Return default data if there's an error
      const defaultData: DashboardData = {
        stats: {
          totalTransformers: 32,
          healthyPercentage: 86,
          activeOutages: 12,
          criticalAlerts: 7,
          newTransformers: 24,
          healthyChange: 3,
          outageChange: -5,
          alertChange: 2
        },
        regionalStats: [
          {
            region: "Addis Ababa",
            totalTransformers: 10,
            healthyPercentage: 88,
            warningPercentage: 7,
            criticalPercentage: 3,
            offlinePercentage: 2
          },
          {
            region: "Oromia",
            totalTransformers: 8,
            healthyPercentage: 84,
            warningPercentage: 9,
            criticalPercentage: 4,
            offlinePercentage: 3
          },
          {
            region: "Amhara",
            totalTransformers: 6,
            healthyPercentage: 87,
            warningPercentage: 8,
            criticalPercentage: 3,
            offlinePercentage: 2
          },
          {
            region: "Tigray",
            totalTransformers: 3,
            healthyPercentage: 82,
            warningPercentage: 10,
            criticalPercentage: 5,
            offlinePercentage: 3
          },
          {
            region: "SNNPR",
            totalTransformers: 5,
            healthyPercentage: 85,
            warningPercentage: 8,
            criticalPercentage: 4,
            offlinePercentage: 3
          }
        ],
        maintenanceStats: {
          scheduled: 45,
          inProgress: 18,
          completed: 124,
          overdue: 7,
          preventive: 98,
          corrective: 72,
          emergency: 24
        },
        alertStats: {
          total: 87,
          critical: 7,
          high: 15,
          medium: 32,
          low: 33,
          resolved: 45,
          unresolved: 42
        },
        outageStats: {
          active: 12,
          scheduled: 8,
          resolved: 34,
          affectedCustomers: 5840,
          averageDuration: 3.2
        },
        weatherImpactStats: {
          highRiskAreas: 3,
          affectedTransformers: 28,
          weatherAlerts: 5
        }
      };

      return {
        data: defaultData,
        lastUpdated: new Date().toISOString()
      };
    }
  });

  // Get dashboard data (client-side)
  async getDashboardData(region: string = 'all', timeRange: string = '30d'): Promise<DashboardData> {
    try {
      // Initialize the database if needed
      await this.ensureDatabaseInitialized();

      // Get dashboard statistics from the database
      const dbService = await getDbService();
      const dbStats = dbService.getDashboardStatistics(region);

      // Convert to dashboard data format
      const data = this.convertToDashboardData(dbStats, timeRange);

      return data;
    } catch (error) {
      console.error("Error getting dashboard data:", error);

      // Return the same default data as in getInitialDashboardData
      return {
        stats: {
          totalTransformers: 32,
          healthyPercentage: 86,
          activeOutages: 12,
          criticalAlerts: 7,
          newTransformers: 24,
          healthyChange: 3,
          outageChange: -5,
          alertChange: 2
        },
        regionalStats: [
          {
            region: "Addis Ababa",
            totalTransformers: 10,
            healthyPercentage: 88,
            warningPercentage: 7,
            criticalPercentage: 3,
            offlinePercentage: 2
          },
          {
            region: "Oromia",
            totalTransformers: 8,
            healthyPercentage: 84,
            warningPercentage: 9,
            criticalPercentage: 4,
            offlinePercentage: 3
          },
          {
            region: "Amhara",
            totalTransformers: 6,
            healthyPercentage: 87,
            warningPercentage: 8,
            criticalPercentage: 3,
            offlinePercentage: 2
          },
          {
            region: "Tigray",
            totalTransformers: 3,
            healthyPercentage: 82,
            warningPercentage: 10,
            criticalPercentage: 5,
            offlinePercentage: 3
          },
          {
            region: "SNNPR",
            totalTransformers: 5,
            healthyPercentage: 85,
            warningPercentage: 8,
            criticalPercentage: 4,
            offlinePercentage: 3
          }
        ],
        maintenanceStats: {
          scheduled: 45,
          inProgress: 18,
          completed: 124,
          overdue: 7,
          preventive: 98,
          corrective: 72,
          emergency: 24
        },
        alertStats: {
          total: 87,
          critical: 7,
          high: 15,
          medium: 32,
          low: 33,
          resolved: 45,
          unresolved: 42
        },
        outageStats: {
          active: 12,
          scheduled: 8,
          resolved: 34,
          affectedCustomers: 5840,
          averageDuration: 3.2
        },
        weatherImpactStats: {
          highRiskAreas: 3,
          affectedTransformers: 28,
          weatherAlerts: 5
        }
      };
    }
  }

  // Get regional statistics
  async getRegionalStats(region: string = 'all'): Promise<RegionalStats[]> {
    try {
      // Initialize the database if needed
      await this.ensureDatabaseInitialized();

      // Get dashboard statistics from the database
      const dbService = await getDbService();
      const dbStats = dbService.getDashboardStatistics(region);

      return dbStats.regionalStats;
    } catch (error) {
      console.error("Error getting regional stats:", error);

      // Return default regional stats
      return [
        {
          region: "Addis Ababa",
          totalTransformers: 10,
          healthyPercentage: 88,
          warningPercentage: 7,
          criticalPercentage: 3,
          offlinePercentage: 2
        },
        {
          region: "Oromia",
          totalTransformers: 8,
          healthyPercentage: 84,
          warningPercentage: 9,
          criticalPercentage: 4,
          offlinePercentage: 3
        },
        {
          region: "Amhara",
          totalTransformers: 6,
          healthyPercentage: 87,
          warningPercentage: 8,
          criticalPercentage: 3,
          offlinePercentage: 2
        },
        {
          region: "Tigray",
          totalTransformers: 3,
          healthyPercentage: 82,
          warningPercentage: 10,
          criticalPercentage: 5,
          offlinePercentage: 3
        },
        {
          region: "SNNPR",
          totalTransformers: 5,
          healthyPercentage: 85,
          warningPercentage: 8,
          criticalPercentage: 4,
          offlinePercentage: 3
        }
      ];
    }
  }

  // Get maintenance statistics
  async getMaintenanceStats(): Promise<MaintenanceStats> {
    try {
      const data = await this.getDashboardData();
      return data.maintenanceStats;
    } catch (error) {
      console.error("Error getting maintenance stats:", error);

      // Return default maintenance stats
      return {
        scheduled: 45,
        inProgress: 18,
        completed: 124,
        overdue: 7,
        preventive: 98,
        corrective: 72,
        emergency: 24
      };
    }
  }

  // Get alert statistics
  async getAlertStats(): Promise<AlertStats> {
    try {
      const data = await this.getDashboardData();
      return data.alertStats;
    } catch (error) {
      console.error("Error getting alert stats:", error);

      // Return default alert stats
      return {
        total: 87,
        critical: 7,
        high: 15,
        medium: 32,
        low: 33,
        resolved: 45,
        unresolved: 42
      };
    }
  }

  // Get outage statistics
  async getOutageStats(): Promise<OutageStats> {
    try {
      const data = await this.getDashboardData();
      return data.outageStats;
    } catch (error) {
      console.error("Error getting outage stats:", error);

      // Return default outage stats
      return {
        active: 12,
        scheduled: 8,
        resolved: 34,
        affectedCustomers: 5840,
        averageDuration: 3.2
      };
    }
  }

  // Get weather impact statistics
  async getWeatherImpactStats(): Promise<WeatherImpactStats> {
    try {
      const data = await this.getDashboardData();
      return data.weatherImpactStats;
    } catch (error) {
      console.error("Error getting weather impact stats:", error);

      // Return default weather impact stats
      return {
        highRiskAreas: 3,
        affectedTransformers: 28,
        weatherAlerts: 5
      };
    }
  }

  // Export dashboard data to CSV
  exportDashboardData(format: 'csv' | 'excel' | 'pdf' = 'csv'): void {
    // In a real app, this would generate and download a file
    console.log(`Exporting dashboard data as ${format}`);
  }

  // Search across multiple entities
  async search(term: string) {
    // Initialize the database if needed
    await this.ensureDatabaseInitialized();

    // Get the database service and use it to search
    const dbService = await getDbService();
    return dbService.search(term);
  }
}

// Create and export a singleton instance
export const dashboardService = new DashboardService();
