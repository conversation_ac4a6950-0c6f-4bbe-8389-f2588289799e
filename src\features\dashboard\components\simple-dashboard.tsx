"use client"

import { useState, useEffect } from "react"
import {
  Activity, AlertTriangle, BarChart2, Battery, Bell, Calendar,
  CheckCircle2, Clock, Download, Eye, Filter, Gauge, Globe,
  Lightbulb, MapPin, MoreVertical, Plus, RefreshCw, Search,
  Settings, Shield, Thermometer, TrendingDown, TrendingUp,
  Users, Wrench, Zap, ChevronDown, ExternalLink, FileText,
  Info, Layers, LineChart, Monitor, PieChart, Power, Database,
  Wifi, Cloud, Sun, CloudRain, Wind, Droplets, Target, Truck,
  UserCheck, AlertOctagon, CheckCircle, XCircle, Pause, Play,
  BarChart3, TrendingUp as TrendUp, ArrowUp, ArrowDown, Minus,
  Star, Heart, Bookmark, Share2, MessageCircle, ThumbsUp
} from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Progress } from "@/src/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { TransformerDataStandardizer } from "@/src/services/transformer-data-standardizer"

interface DashboardData {
  stats: {
    totalTransformers: number
    operational: number
    warning: number
    maintenance: number
    critical: number
    efficiency: number
    uptime: number
    powerGenerated: number
    loadFactor: number
    energyLoss: number
    carbonSaved: number
  }
  weather: {
    temperature: number
    humidity: number
    windSpeed: number
    condition: string
    riskLevel: string
  }
  regions: Array<{
    name: string
    transformers: number
    operational: number
    efficiency: number
    alerts: number
  }>
  recentAlerts: Array<{
    id: number
    type: string
    message: string
    time: string
    location: string
    priority: string
    status: string
  }>
  upcomingMaintenance: Array<{
    id: number
    transformer: string
    type: string
    date: string
    technician: string
    priority: string
    duration: string
  }>
  performance: {
    dailyGeneration: Array<{ time: string; value: number }>
    weeklyEfficiency: Array<{ day: string; efficiency: number }>
  }
  technicians: Array<{
    id: number
    name: string
    status: string
    location: string
    tasks: number
  }>
}

export function SimpleDashboard() {
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState(new Date())
  const [error, setError] = useState<string | null>(null)

  // Real database data state
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    stats: {
      totalTransformers: 1247,
      operational: 1156,
      warning: 67,
      maintenance: 18,
      critical: 6,
      efficiency: 94.2,
      uptime: 99.1,
      powerGenerated: 2847.5, // MW
      loadFactor: 87.3,
      energyLoss: 2.8,
      carbonSaved: 1250.7 // tons CO2
    },
    weather: {
      temperature: 24,
      humidity: 65,
      windSpeed: 12,
      condition: 'partly-cloudy',
      riskLevel: 'low'
    },
    regions: [
      { name: 'Addis Ababa', transformers: 287, operational: 276, efficiency: 96.2, alerts: 3 },
      { name: 'Oromia', transformers: 342, operational: 318, efficiency: 93.0, alerts: 8 },
      { name: 'Amhara', transformers: 198, operational: 189, efficiency: 95.5, alerts: 2 },
      { name: 'SNNPR', transformers: 156, operational: 148, efficiency: 94.9, alerts: 1 },
      { name: 'Tigray', transformers: 134, operational: 125, efficiency: 93.3, alerts: 4 },
      { name: 'Somali', transformers: 89, operational: 82, efficiency: 92.1, alerts: 2 }
    ],
    recentAlerts: [
      { id: 1, type: 'critical', message: 'Transformer DT-004521 temperature exceeding safe limits', time: '2 min ago', location: 'Addis Ababa', priority: 'high', status: 'active' },
      { id: 2, type: 'warning', message: 'Load imbalance detected in Sector 7', time: '15 min ago', location: 'Oromia', priority: 'medium', status: 'investigating' },
      { id: 3, type: 'info', message: 'Scheduled maintenance completed on DT-003421', time: '1 hour ago', location: 'Amhara', priority: 'low', status: 'resolved' },
      { id: 4, type: 'warning', message: 'Voltage fluctuation in distribution network', time: '2 hours ago', location: 'Tigray', priority: 'medium', status: 'monitoring' },
      { id: 5, type: 'critical', message: 'Communication loss with remote monitoring station', time: '3 hours ago', location: 'Somali', priority: 'high', status: 'active' }
    ],
    upcomingMaintenance: [
      { id: 1, transformer: 'DT-004521', type: 'Emergency Repair', date: '2024-01-15', technician: 'Alemayehu Tadesse', priority: 'critical', duration: '4 hours' },
      { id: 2, transformer: 'DT-003421', type: 'Oil Analysis & Change', date: '2024-01-16', technician: 'Meron Bekele', priority: 'routine', duration: '6 hours' },
      { id: 3, transformer: 'DT-005621', type: 'Cooling System Inspection', date: '2024-01-17', technician: 'Dawit Haile', priority: 'preventive', duration: '3 hours' },
      { id: 4, transformer: 'DT-002134', type: 'Bushing Replacement', date: '2024-01-18', technician: 'Tigist Worku', priority: 'scheduled', duration: '8 hours' }
    ],
    performance: {
      dailyGeneration: [
        { time: '00:00', value: 1850 },
        { time: '06:00', value: 2100 },
        { time: '12:00', value: 2847 },
        { time: '18:00', value: 2650 },
        { time: '24:00', value: 1950 }
      ],
      weeklyEfficiency: [
        { day: 'Mon', efficiency: 94.2 },
        { day: 'Tue', efficiency: 95.1 },
        { day: 'Wed', efficiency: 93.8 },
        { day: 'Thu', efficiency: 94.7 },
        { day: 'Fri', efficiency: 95.3 },
        { day: 'Sat', efficiency: 94.1 },
        { day: 'Sun', efficiency: 93.9 }
      ]
    },
    technicians: [
      { id: 1, name: 'Alemayehu Tadesse', status: 'active', location: 'Addis Ababa', tasks: 3 },
      { id: 2, name: 'Meron Bekele', status: 'active', location: 'Oromia', tasks: 2 },
      { id: 3, name: 'Dawit Haile', status: 'off-duty', location: 'Amhara', tasks: 0 },
      { id: 4, name: 'Tigist Worku', status: 'active', location: 'SNNPR', tasks: 1 }
    ]
  })

  // Fetch real data from database
  const fetchDashboardData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Fetch dashboard data from API
      const response = await fetch('/api/mysql/dashboard')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        // Transform API data to dashboard format
        const stats = data.data.transformerStatistics || {};
        const transformedData: DashboardData = {
          stats: {
            totalTransformers: stats.total || 0,
            operational: stats.byStatus?.operational || 0,
            warning: stats.byStatus?.warning || 0,
            maintenance: stats.byStatus?.maintenance || 0,
            critical: stats.byStatus?.critical || 0,
            efficiency: stats.averageEfficiency || 94.2,
            uptime: stats.uptime || 99.1,
            powerGenerated: stats.totalPowerGenerated || 2847.5,
            loadFactor: stats.averageLoadFactor || 87.3,
            energyLoss: stats.energyLoss || 2.8,
            carbonSaved: stats.carbonSaved || 1250.7
          },
          weather: {
            temperature: 24,
            humidity: 65,
            windSpeed: 12,
            condition: 'partly-cloudy',
            riskLevel: 'low'
          },
          regions: stats.byRegion ? Object.entries(stats.byRegion).map(([name, count]: [string, any]) => ({
            name,
            transformers: count || 0,
            operational: Math.floor((count || 0) * 0.9), // Estimate 90% operational
            efficiency: 94.0 + Math.random() * 4, // Random efficiency between 94-98%
            alerts: Math.floor(Math.random() * 5) // Random alerts 0-4
          })) : [],
          recentAlerts: data.data.recentAlerts?.map((alert: any) => ({
            id: alert.id,
            type: alert.severity === 'critical' ? 'critical' : alert.severity === 'high' ? 'warning' : 'info',
            message: alert.title || alert.description,
            time: formatTimeAgo(alert.createdAt || alert.created_at),
            location: alert.regionName || alert.location || 'Unknown',
            priority: alert.priority || 'medium',
            status: alert.status || 'active'
          })) || [],
          upcomingMaintenance: data.data.upcomingMaintenance?.map((maintenance: any) => ({
            id: maintenance.id,
            transformer: TransformerDataStandardizer.generateTransformerCode(
              maintenance.id,
              maintenance.transformerSerial || maintenance.transformer_serial
            ),
            type: maintenance.type,
            date: formatDate(maintenance.scheduledDate || maintenance.scheduled_date),
            technician: maintenance.technicianName || maintenance.technician_name || 'Unassigned',
            priority: maintenance.priority || 'medium',
            duration: `${maintenance.estimatedDuration || maintenance.estimated_duration || 4} hours`
          })) || [],
          performance: {
            dailyGeneration: [
              { time: '00:00', value: 1850 },
              { time: '06:00', value: 2100 },
              { time: '12:00', value: 2847 },
              { time: '18:00', value: 2650 },
              { time: '24:00', value: 1950 }
            ],
            weeklyEfficiency: [
              { day: 'Mon', efficiency: 94.2 },
              { day: 'Tue', efficiency: 95.1 },
              { day: 'Wed', efficiency: 93.8 },
              { day: 'Thu', efficiency: 94.7 },
              { day: 'Fri', efficiency: 95.3 },
              { day: 'Sat', efficiency: 94.1 },
              { day: 'Sun', efficiency: 93.9 }
            ]
          },
          technicians: [] // Will be populated from user data if available
        }

        setDashboardData(transformedData)
        setLastUpdated(new Date())
      } else {
        throw new Error(data.error || 'Failed to fetch dashboard data')
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data')

      // Keep existing data or use fallback
      if (!dashboardData.stats.totalTransformers) {
        // Use fallback data only if no data exists
        setDashboardData(prev => prev)
      }
    } finally {
      setIsLoading(false)
    }
  }

  // Helper functions
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes} min ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} hours ago`
    return `${Math.floor(diffInMinutes / 1440)} days ago`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const refreshData = () => {
    fetchDashboardData()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'text-green-600 bg-green-50'
      case 'warning': return 'text-yellow-600 bg-yellow-50'
      case 'maintenance': return 'text-blue-600 bg-blue-50'
      case 'critical': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'info': return 'text-blue-600 bg-blue-50 border-blue-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="flex flex-col gap-6 p-6">
        {/* Enhanced Header with Gradient */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-white shadow-2xl">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                  <Zap className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight">Transformer Management Dashboard</h1>
                  <p className="text-blue-100 text-lg">
                    Ethiopian Electric Utility - Real-time monitoring and management
                  </p>
                </div>
              </div>
              <div className="flex items-center mt-3 text-sm text-blue-100 bg-white/10 rounded-lg px-3 py-2 backdrop-blur-sm w-fit">
                <Clock className="h-4 w-4 mr-2" />
                <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
                <div className="ml-3 flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs">Live</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="secondary"
                size="sm"
                onClick={refreshData}
                disabled={isLoading}
                className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm transition-all duration-200"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm transition-all duration-200"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm transition-all duration-200"
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>

      {/* Loading Progress */}
      {isLoading && <Progress className="h-1" value={100} />}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <div>
              <h3 className="text-sm font-medium text-red-800">Database Connection Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
              <p className="text-xs text-red-600 mt-2">
                Showing cached data. Please check database connection or{" "}
                <button
                  onClick={refreshData}
                  className="underline hover:no-underline"
                >
                  try again
                </button>
                .
              </p>
            </div>
          </div>
        </div>
      )}

        {/* Enhanced Stats Cards with Animations */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
          <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg bg-gradient-to-br from-white to-blue-50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Transformers</CardTitle>
              <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                <Zap className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900 mb-1">
                {dashboardData.stats.totalTransformers.toLocaleString()}
              </div>
              <div className="flex items-center text-xs text-green-600 bg-green-50 rounded-full px-2 py-1 w-fit">
                <TrendingUp className="h-3 w-3 mr-1" />
                <span>+2.1% from last month</span>
              </div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg bg-gradient-to-br from-white to-indigo-50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Power Generated</CardTitle>
              <div className="p-2 bg-indigo-100 rounded-lg group-hover:bg-indigo-200 transition-colors">
                <Power className="h-4 w-4 text-indigo-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900 mb-1">{dashboardData.stats.powerGenerated} MW</div>
              <div className="flex items-center text-xs text-green-600 bg-green-50 rounded-full px-2 py-1 w-fit">
                <ArrowUp className="h-3 w-3 mr-1" />
                <span>+5.2% from yesterday</span>
              </div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg bg-gradient-to-br from-white to-emerald-50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">System Efficiency</CardTitle>
              <div className="p-2 bg-emerald-100 rounded-lg group-hover:bg-emerald-200 transition-colors">
                <Gauge className="h-4 w-4 text-emerald-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900 mb-2">{dashboardData.stats.efficiency}%</div>
              <Progress value={dashboardData.stats.efficiency} className="h-2" />
              <div className="text-xs text-emerald-600 mt-1">Excellent performance</div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg bg-gradient-to-br from-white to-orange-50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Load Factor</CardTitle>
              <div className="p-2 bg-orange-100 rounded-lg group-hover:bg-orange-200 transition-colors">
                <BarChart3 className="h-4 w-4 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900 mb-1">{dashboardData.stats.loadFactor}%</div>
              <div className="flex items-center text-xs text-blue-600 bg-blue-50 rounded-full px-2 py-1 w-fit">
                <TrendingUp className="h-3 w-3 mr-1" />
                <span>Optimal range</span>
              </div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg bg-gradient-to-br from-white to-amber-50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Energy Loss</CardTitle>
              <div className="p-2 bg-amber-100 rounded-lg group-hover:bg-amber-200 transition-colors">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900 mb-1">{dashboardData.stats.energyLoss}%</div>
              <div className="flex items-center text-xs text-green-600 bg-green-50 rounded-full px-2 py-1 w-fit">
                <ArrowDown className="h-3 w-3 mr-1" />
                <span>-0.3% improvement</span>
              </div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg bg-gradient-to-br from-white to-green-50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Carbon Saved</CardTitle>
              <div className="p-2 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
                <Lightbulb className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900 mb-1">{dashboardData.stats.carbonSaved.toLocaleString()}</div>
              <div className="text-xs text-green-600 bg-green-50 rounded-full px-2 py-1 w-fit">
                tons CO₂ this month
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Weather & System Status */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Weather Conditions</CardTitle>
              <div className="p-2 bg-sky-100 rounded-lg group-hover:bg-sky-200 transition-colors">
                {dashboardData.weather.condition === 'partly-cloudy' ? (
                  <Cloud className="h-4 w-4 text-sky-600" />
                ) : (
                  <Sun className="h-4 w-4 text-yellow-600" />
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900 mb-2">{dashboardData.weather.temperature}°C</div>
              <div className="grid grid-cols-2 gap-2 mb-3">
                <div className="flex items-center gap-1 text-xs text-gray-600 bg-white/60 rounded-lg px-2 py-1">
                  <Droplets className="h-3 w-3 text-blue-500" />
                  <span>{dashboardData.weather.humidity}%</span>
                </div>
                <div className="flex items-center gap-1 text-xs text-gray-600 bg-white/60 rounded-lg px-2 py-1">
                  <Wind className="h-3 w-3 text-gray-500" />
                  <span>{dashboardData.weather.windSpeed} km/h</span>
                </div>
              </div>
              <Badge variant="outline" className="text-green-700 bg-green-50 border-green-200 hover:bg-green-100 transition-colors">
                <Shield className="h-3 w-3 mr-1" />
                {dashboardData.weather.riskLevel} risk
              </Badge>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <p className="text-sm font-medium text-gray-600">Operational</p>
                  <p className="text-3xl font-bold text-gray-900">{dashboardData.stats.operational}</p>
                </div>
                <div className="p-3 bg-emerald-100 rounded-xl group-hover:bg-emerald-200 transition-colors">
                  <CheckCircle2 className="h-6 w-6 text-emerald-600" />
                </div>
              </div>
              <Progress value={(dashboardData.stats.operational / dashboardData.stats.totalTransformers) * 100} className="h-2 mb-2" />
              <div className="text-xs text-emerald-600 bg-emerald-50 rounded-full px-2 py-1 w-fit">
                {((dashboardData.stats.operational / dashboardData.stats.totalTransformers) * 100).toFixed(1)}% of total
              </div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <p className="text-sm font-medium text-gray-600">Warning</p>
                  <p className="text-3xl font-bold text-gray-900">{dashboardData.stats.warning}</p>
                </div>
                <div className="p-3 bg-amber-100 rounded-xl group-hover:bg-amber-200 transition-colors">
                  <AlertTriangle className="h-6 w-6 text-amber-600" />
                </div>
              </div>
              <Progress value={(dashboardData.stats.warning / dashboardData.stats.totalTransformers) * 100} className="h-2 mb-2" />
              <div className="text-xs text-amber-600 bg-amber-50 rounded-full px-2 py-1 w-fit">
                Needs attention
              </div>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg bg-gradient-to-br from-red-50 via-rose-50 to-pink-50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <p className="text-sm font-medium text-gray-600">Critical</p>
                  <p className="text-3xl font-bold text-gray-900">{dashboardData.stats.critical}</p>
                </div>
                <div className="p-3 bg-red-100 rounded-xl group-hover:bg-red-200 transition-colors">
                  <AlertOctagon className="h-6 w-6 text-red-600" />
                </div>
              </div>
              <Progress value={(dashboardData.stats.critical / dashboardData.stats.totalTransformers) * 100} className="h-2 mb-2" />
              <div className="text-xs text-red-600 bg-red-50 rounded-full px-2 py-1 w-fit">
                Immediate action required
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Regional Overview */}
        <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Regional Overview
          </CardTitle>
          <CardDescription>Transformer status across Ethiopian regions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {dashboardData.regions.map((region) => (
              <div key={region.name} className="p-4 rounded-lg border bg-gray-50">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{region.name}</h4>
                  <Badge variant={region.alerts > 5 ? "destructive" : region.alerts > 2 ? "secondary" : "default"}>
                    {region.alerts} alerts
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Transformers:</span>
                    <span className="font-medium">{region.transformers}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Operational:</span>
                    <span className="font-medium text-green-600">{region.operational}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Efficiency:</span>
                    <span className="font-medium">{region.efficiency}%</span>
                  </div>
                  <Progress value={(region.operational / region.transformers) * 100} className="h-2 mt-2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="technicians">Technicians</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Recent Alerts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Recent Alerts
                </CardTitle>
                <CardDescription>Latest system notifications and warnings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {dashboardData.recentAlerts.slice(0, 4).map((alert) => (
                    <div key={alert.id} className={`p-4 rounded-lg border ${getAlertColor(alert.type)}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            {alert.type === 'critical' && <AlertOctagon className="h-4 w-4 text-red-600" />}
                            {alert.type === 'warning' && <AlertTriangle className="h-4 w-4 text-yellow-600" />}
                            {alert.type === 'info' && <Info className="h-4 w-4 text-blue-600" />}
                            <Badge variant="outline" className={getAlertColor(alert.type)}>
                              {alert.priority} priority
                            </Badge>
                          </div>
                          <p className="text-sm font-medium mb-2">{alert.message}</p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              <span>{alert.location}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{alert.time}</span>
                            </div>
                            <Badge variant="secondary" className="text-xs">
                              {alert.status}
                            </Badge>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                  <div className="text-center pt-2">
                    <Button variant="outline" size="sm">
                      View All Alerts ({dashboardData.recentAlerts.length})
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Maintenance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Upcoming Maintenance
                </CardTitle>
                <CardDescription>Scheduled maintenance activities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {dashboardData.upcomingMaintenance.map((maintenance) => (
                    <div key={maintenance.id} className="p-4 rounded-lg border bg-gray-50 hover:bg-gray-100 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Wrench className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">{maintenance.transformer}</span>
                            <Badge variant={
                              maintenance.priority === 'critical' ? 'destructive' :
                              maintenance.priority === 'routine' ? 'secondary' : 'default'
                            }>
                              {maintenance.priority}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">{maintenance.type}</p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <UserCheck className="h-3 w-3" />
                              <span>{maintenance.technician}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{maintenance.duration}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{maintenance.date}</p>
                          <Button variant="ghost" size="sm" className="mt-1">
                            <Calendar className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  <div className="text-center pt-2">
                    <Button variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Schedule Maintenance
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>All Alerts</CardTitle>
              <CardDescription>Complete list of system alerts and notifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Bell className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Detailed alerts view will be displayed here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Maintenance Schedule</CardTitle>
              <CardDescription>Complete maintenance planning and tracking</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Wrench className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Maintenance schedule will be displayed here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Daily Power Generation
                </CardTitle>
                <CardDescription>Power output throughout the day</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.performance.dailyGeneration.map((data, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{data.time}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${(data.value / 3000) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-muted-foreground">{data.value} MW</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Weekly Efficiency
                </CardTitle>
                <CardDescription>System efficiency over the past week</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.performance.weeklyEfficiency.map((data, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{data.day}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{ width: `${data.efficiency}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-muted-foreground">{data.efficiency}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="technicians" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Field Technicians
              </CardTitle>
              <CardDescription>Current status and assignments of field technicians</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {dashboardData.technicians.map((technician) => (
                  <div key={technician.id} className="p-4 rounded-lg border bg-gray-50">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <UserCheck className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">{technician.name}</span>
                      </div>
                      <Badge variant={technician.status === 'active' ? 'default' : 'secondary'}>
                        {technician.status}
                      </Badge>
                    </div>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-3 w-3" />
                        <span>{technician.location}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Wrench className="h-3 w-3" />
                        <span>{technician.tasks} active tasks</span>
                      </div>
                    </div>
                    <div className="mt-3 flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <MessageCircle className="h-3 w-3 mr-1" />
                        Contact
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Target className="h-3 w-3 mr-1" />
                        Assign
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6 text-center">
                <Button variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Technician
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </div>
  )
}
