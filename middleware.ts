import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { validateToken } from '@/src/lib/auth/mysql-auth'

// Define public paths that don't require authentication
const publicPaths = [
  '/login',
  '/simple-login',
  '/register',
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/logout',
  '/api/auth/mysql-login',
  '/api/dashboard/filtered-data',
  '/api/test-filter',
  '/api/health',
  '/api/check-database'
]

// Define paths that should be redirected to dashboard
const redirectToDashboard = ['/']

// Define API paths
const apiPaths = ['/api/']

export async function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname

  // Check if this is a public path
  const isPublicPath = publicPaths.some(p => path.startsWith(p))

  // Check if this is an API path
  const isApiPath = apiPaths.some(p => path.startsWith(p))

  // Check if this path should redirect to dashboard
  const shouldRedirectToDashboard = redirectToDashboard.includes(path)

  // If it's the root path, redirect to dashboard
  if (shouldRedirectToDashboard) {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  // If it's a public path, allow access
  if (isPublicPath) {
    return NextResponse.next()
  }

  // Check for auth token or eeu_user cookie
  const token = request.cookies.get('auth_token')?.value
  const eeuUser = request.cookies.get('eeu_user')?.value

  // If no token and no eeu_user, redirect to login
  if (!token && !eeuUser) {
    // For API requests, return 401
    if (isApiPath) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    // For other requests, redirect to login
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // For all other paths, let the client-side auth handle it
  return NextResponse.next()
}

// Configure the paths that middleware should run on
export const config = {
  matcher: [
    // Match all paths except static files and images
    '/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.svg$).*)',
  ],
}
