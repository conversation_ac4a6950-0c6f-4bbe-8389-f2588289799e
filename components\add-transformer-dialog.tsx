"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/src/components/ui/dialog"
import { But<PERSON> } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useToast } from "@/src/components/ui/use-toast"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { CalendarIcon, MapPin } from "lucide-react"
import { Calendar } from "@/src/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/src/lib/utils"
import { transformerService } from "@/src/services/transformer-service"
import type { Transformer } from "@/src/types/transformer"

interface AddTransformerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onTransformerAdded?: () => void
}

export function AddTransformerDialog({ open, onOpenChange, onTransformerAdded }: AddTransformerDialogProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [serialNumber, setSerialNumber] = useState("")
  const [manufacturer, setManufacturer] = useState("")
  const [model, setModel] = useState("")
  const [type, setType] = useState("")
  const [capacity, setCapacity] = useState("")
  const [installationDate, setInstallationDate] = useState<Date | undefined>(new Date())
  const [latitude, setLatitude] = useState("")
  const [longitude, setLongitude] = useState("")
  const [region, setRegion] = useState("")
  const [serviceCenter, setServiceCenter] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Create transformer object
      const newTransformer: Omit<Transformer, "id"> = {
        serialNumber,
        manufacturer,
        model,
        type: type === "distribution" ? "Distribution" :
              type === "power" ? "Power" :
              type === "pad_mounted" ? "Pad Mounted" :
              type === "pole_mounted" ? "Pole Mounted" :
              type === "dry_type" ? "Dry Type" : "Oil Filled",
        capacity,
        installationDate: installationDate ? format(installationDate, "yyyy-MM-dd") : format(new Date(), "yyyy-MM-dd"),
        lastMaintenanceDate: "",
        lastInspectionDate: "",
        status: "Operational",
        location: {
          latitude,
          longitude,
          region: region === "addis_ababa" ? "Addis Ababa" :
                 region === "oromia" ? "Oromia" :
                 region === "amhara" ? "Amhara" :
                 region === "tigray" ? "Tigray" :
                 region === "sidama" ? "Sidama" :
                 region === "snnpr" ? "SNNPR" :
                 region === "somali" ? "Somali" :
                 region === "afar" ? "Afar" :
                 region === "benishangul_gumuz" ? "Benishangul-Gumuz" :
                 region === "gambela" ? "Gambela" :
                 region === "harari" ? "Harari" : "Dire Dawa",
          serviceCenter: serviceCenter === "bole" ? "Bole" :
                        serviceCenter === "mekanisa" ? "Mekanisa" :
                        serviceCenter === "gulele" ? "Gulele" :
                        serviceCenter === "arada" ? "Arada" :
                        serviceCenter === "kirkos" ? "Kirkos" :
                        serviceCenter === "yeka" ? "Yeka" :
                        serviceCenter === "akaki" ? "Akaki" : "Nefas Silk",
        }
      }

      // Add transformer using service
      const addedTransformer = await transformerService.addTransformer(newTransformer)

      toast({
        title: "Transformer added successfully",
        description: `Transformer ${serialNumber} has been added to the inventory.`,
      })

      // Reset form
      setSerialNumber("")
      setManufacturer("")
      setModel("")
      setType("")
      setCapacity("")
      setInstallationDate(new Date())
      setLatitude("")
      setLongitude("")
      setRegion("")
      setServiceCenter("")
      setActiveTab("basic")

      // Close dialog and notify parent
      onOpenChange(false)
      if (onTransformerAdded) {
        onTransformerAdded()
      }
    } catch (error) {
      toast({
        title: "Error adding transformer",
        description: "There was an error adding the transformer. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add New Transformer</DialogTitle>
          <DialogDescription>
            Add a new transformer to the inventory. Fill out the form below with the transformer details.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Information</TabsTrigger>
              <TabsTrigger value="technical">Technical Specs</TabsTrigger>
              <TabsTrigger value="location">Location</TabsTrigger>
            </TabsList>
            <TabsContent value="basic" className="space-y-4 pt-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="serialNumber" className="text-right">
                  Serial Number
                </Label>
                <Input
                  id="serialNumber"
                  value={serialNumber}
                  onChange={(e) => setSerialNumber(e.target.value)}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="manufacturer" className="text-right">
                  Manufacturer
                </Label>
                <Input
                  id="manufacturer"
                  value={manufacturer}
                  onChange={(e) => setManufacturer(e.target.value)}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="model" className="text-right">
                  Model
                </Label>
                <Input
                  id="model"
                  value={model}
                  onChange={(e) => setModel(e.target.value)}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="installationDate" className="text-right">
                  Installation Date
                </Label>
                <div className="col-span-3">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !installationDate && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {installationDate ? format(installationDate, "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar mode="single" selected={installationDate} onSelect={setInstallationDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div className="flex justify-end">
                <Button type="button" onClick={() => setActiveTab("technical")}>
                  Next
                </Button>
              </div>
            </TabsContent>
            <TabsContent value="technical" className="space-y-4 pt-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="type" className="text-right">
                  Type
                </Label>
                <Select value={type} onValueChange={setType} required>
                  <SelectTrigger id="type" className="col-span-3">
                    <SelectValue placeholder="Select transformer type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="distribution">Distribution</SelectItem>
                    <SelectItem value="power">Power</SelectItem>
                    <SelectItem value="pad_mounted">Pad Mounted</SelectItem>
                    <SelectItem value="pole_mounted">Pole Mounted</SelectItem>
                    <SelectItem value="dry_type">Dry Type</SelectItem>
                    <SelectItem value="oil_filled">Oil Filled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="capacity" className="text-right">
                  Capacity (kVA)
                </Label>
                <Input
                  id="capacity"
                  value={capacity}
                  onChange={(e) => setCapacity(e.target.value)}
                  className="col-span-3"
                  type="number"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="primaryVoltage" className="text-right">
                  Primary Voltage
                </Label>
                <Select defaultValue="11kV">
                  <SelectTrigger id="primaryVoltage" className="col-span-3">
                    <SelectValue placeholder="Select primary voltage" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="11kV">11 kV</SelectItem>
                    <SelectItem value="33kV">33 kV</SelectItem>
                    <SelectItem value="66kV">66 kV</SelectItem>
                    <SelectItem value="132kV">132 kV</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="secondaryVoltage" className="text-right">
                  Secondary Voltage
                </Label>
                <Select defaultValue="400V">
                  <SelectTrigger id="secondaryVoltage" className="col-span-3">
                    <SelectValue placeholder="Select secondary voltage" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="400V">400 V</SelectItem>
                    <SelectItem value="230V">230 V</SelectItem>
                    <SelectItem value="110V">110 V</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-between">
                <Button type="button" variant="outline" onClick={() => setActiveTab("basic")}>
                  Previous
                </Button>
                <Button type="button" onClick={() => setActiveTab("location")}>
                  Next
                </Button>
              </div>
            </TabsContent>
            <TabsContent value="location" className="space-y-4 pt-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="region" className="text-right">
                  Region
                </Label>
                <Select value={region} onValueChange={setRegion} required>
                  <SelectTrigger id="region" className="col-span-3">
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="addis_ababa">Addis Ababa</SelectItem>
                    <SelectItem value="oromia">Oromia</SelectItem>
                    <SelectItem value="amhara">Amhara</SelectItem>
                    <SelectItem value="tigray">Tigray</SelectItem>
                    <SelectItem value="sidama">Sidama</SelectItem>
                    <SelectItem value="snnpr">SNNPR</SelectItem>
                    <SelectItem value="somali">Somali</SelectItem>
                    <SelectItem value="afar">Afar</SelectItem>
                    <SelectItem value="benishangul_gumuz">Benishangul-Gumuz</SelectItem>
                    <SelectItem value="gambela">Gambela</SelectItem>
                    <SelectItem value="harari">Harari</SelectItem>
                    <SelectItem value="dire_dawa">Dire Dawa</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="serviceCenter" className="text-right">
                  Service Center
                </Label>
                <Select value={serviceCenter} onValueChange={setServiceCenter} required>
                  <SelectTrigger id="serviceCenter" className="col-span-3">
                    <SelectValue placeholder="Select service center" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bole">Bole</SelectItem>
                    <SelectItem value="mekanisa">Mekanisa</SelectItem>
                    <SelectItem value="gulele">Gulele</SelectItem>
                    <SelectItem value="arada">Arada</SelectItem>
                    <SelectItem value="kirkos">Kirkos</SelectItem>
                    <SelectItem value="yeka">Yeka</SelectItem>
                    <SelectItem value="akaki">Akaki</SelectItem>
                    <SelectItem value="nefas_silk">Nefas Silk</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="coordinates" className="text-right">
                  Coordinates
                </Label>
                <div className="col-span-3 flex items-center gap-2">
                  <div className="flex items-center gap-2 flex-1">
                    <Label htmlFor="latitude" className="whitespace-nowrap">
                      Latitude:
                    </Label>
                    <Input
                      id="latitude"
                      value={latitude}
                      onChange={(e) => setLatitude(e.target.value)}
                      placeholder="e.g., 9.0222"
                      required
                    />
                  </div>
                  <div className="flex items-center gap-2 flex-1">
                    <Label htmlFor="longitude" className="whitespace-nowrap">
                      Longitude:
                    </Label>
                    <Input
                      id="longitude"
                      value={longitude}
                      onChange={(e) => setLongitude(e.target.value)}
                      placeholder="e.g., 38.7468"
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Map</Label>
                <div className="col-span-3 border rounded-md p-2 flex items-center justify-center h-[150px] bg-muted/20">
                  <div className="flex flex-col items-center text-muted-foreground">
                    <MapPin className="h-6 w-6 mb-2" />
                    <span>Map preview will be shown here</span>
                    <span className="text-xs">Enter coordinates to see location</span>
                  </div>
                </div>
              </div>
              <div className="flex justify-between">
                <Button type="button" variant="outline" onClick={() => setActiveTab("technical")}>
                  Previous
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Adding..." : "Add Transformer"}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </form>
      </DialogContent>
    </Dialog>
  )
}
