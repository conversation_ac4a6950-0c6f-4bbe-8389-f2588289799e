"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/src/components/ui/dialog"
import { Button } from "@/src/components/ui/button"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Input } from "@/src/components/ui/input"
import { Checkbox } from "@/src/components/ui/checkbox"
import { useToast } from "@/src/components/ui/use-toast"
import { Filter, Save, RefreshCw, Search, X } from "lucide-react"
import { Badge } from "@/src/components/ui/badge"

interface FilterSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentRegion?: string;
  currentTimeRange?: string;
  onApplyFilters?: (filters: {
    region: string;
    serviceCenter: string;
    status: string[];
    capacityRange: [number, number];
    dateRange: [string, string];
    searchTerm: string;
    tags: string[];
    timeRange: string;
  }) => void;
}

export function FilterSettingsDialog({
  open,
  onOpenChange,
  currentRegion = "",
  currentTimeRange = "30d",
  onApplyFilters
}: FilterSettingsDialogProps) {
  const { toast } = useToast()

  // Filter settings
  const [region, setRegion] = useState<string>(currentRegion)
  const [serviceCenter, setServiceCenter] = useState<string>("")
  const [status, setStatus] = useState<string[]>(["active"])
  const [capacityRange, setCapacityRange] = useState<[number, number]>([0, 1000])
  const [dateRange, setDateRange] = useState<[string, string]>(["", ""])
  const [searchTerm, setSearchTerm] = useState<string>("")
  const [tags, setTags] = useState<string[]>(["critical", "maintenance-due"])
  const [timeRange, setTimeRange] = useState<string>(currentTimeRange)
  const [newTag, setNewTag] = useState<string>("")

  const handleAddTag = () => {
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag])
      setNewTag("")
    }
  }

  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag))
  }

  const handleStatusChange = (status: string) => {
    if (status === "all") {
      setStatus([])
    } else {
      setStatus(prev =>
        prev.includes(status)
          ? prev.filter(s => s !== status)
          : [...prev, status]
      )
    }
  }

  const handleSave = () => {
    // Apply the filters
    if (onApplyFilters) {
      onApplyFilters({
        region,
        serviceCenter,
        status,
        capacityRange,
        dateRange,
        searchTerm,
        tags,
        timeRange
      })
    }

    toast({
      title: "Filter Settings Applied",
      description: "Your advanced filter settings have been applied to the dashboard."
    })

    onOpenChange(false)
  }

  const handleReset = () => {
    // Reset to defaults
    setRegion("")
    setServiceCenter("")
    setStatus(["active"])
    setCapacityRange([0, 1000])
    setDateRange(["", ""])
    setSearchTerm("")
    setTags(["critical", "maintenance-due"])

    toast({
      title: "Filters Reset",
      description: "Filter settings have been reset to defaults."
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Advanced Filters</DialogTitle>
          <DialogDescription>
            Configure advanced filtering options for the dashboard
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="search-term">Search</Label>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="search-term"
                placeholder="Search by ID, name, or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="region-filter">Region</Label>
              <Select value={region} onValueChange={setRegion}>
                <SelectTrigger id="region-filter">
                  <SelectValue placeholder="All Regions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Regions</SelectItem>
                  <SelectItem value="addis">Addis Ababa</SelectItem>
                  <SelectItem value="oromia">Oromia</SelectItem>
                  <SelectItem value="amhara">Amhara</SelectItem>
                  <SelectItem value="tigray">Tigray</SelectItem>
                  <SelectItem value="snnpr">SNNPR</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="service-center-filter">Service Center</Label>
              <Select value={serviceCenter} onValueChange={setServiceCenter}>
                <SelectTrigger id="service-center-filter">
                  <SelectValue placeholder="All Service Centers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Service Centers</SelectItem>
                  <SelectItem value="bole">Bole Service Center</SelectItem>
                  <SelectItem value="kirkos">Kirkos Service Center</SelectItem>
                  <SelectItem value="adama">Adama Service Center</SelectItem>
                  <SelectItem value="bahir-dar">Bahir Dar Service Center</SelectItem>
                  <SelectItem value="mekelle">Mekelle Service Center</SelectItem>
                  <SelectItem value="hawassa">Hawassa Service Center</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="time-range-filter">Time Range</Label>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger id="time-range-filter">
                <SelectValue placeholder="Select Time Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
                <SelectItem value="custom">Custom range</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Status</Label>
            <div className="flex flex-wrap gap-2 mt-1">
              <Badge
                variant={status.length === 0 ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setStatus([])}
              >
                All
              </Badge>
              <Badge
                variant={status.includes("active") ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => handleStatusChange("active")}
              >
                Active
              </Badge>
              <Badge
                variant={status.includes("warning") ? "default" : "outline"}
                className="cursor-pointer bg-yellow-500 hover:bg-yellow-600"
                onClick={() => handleStatusChange("warning")}
              >
                Warning
              </Badge>
              <Badge
                variant={status.includes("critical") ? "default" : "outline"}
                className="cursor-pointer bg-red-500 hover:bg-red-600"
                onClick={() => handleStatusChange("critical")}
              >
                Critical
              </Badge>
              <Badge
                variant={status.includes("offline") ? "default" : "outline"}
                className="cursor-pointer bg-gray-500 hover:bg-gray-600"
                onClick={() => handleStatusChange("offline")}
              >
                Offline
              </Badge>
              <Badge
                variant={status.includes("maintenance") ? "default" : "outline"}
                className="cursor-pointer bg-blue-500 hover:bg-blue-600"
                onClick={() => handleStatusChange("maintenance")}
              >
                Maintenance
              </Badge>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Capacity Range (kVA)</Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="capacity-min" className="text-xs">Min</Label>
                <Input
                  id="capacity-min"
                  type="number"
                  min="0"
                  max="1000"
                  value={capacityRange[0]}
                  onChange={(e) => setCapacityRange([parseInt(e.target.value), capacityRange[1]])}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="capacity-max" className="text-xs">Max</Label>
                <Input
                  id="capacity-max"
                  type="number"
                  min="0"
                  max="1000"
                  value={capacityRange[1]}
                  onChange={(e) => setCapacityRange([capacityRange[0], parseInt(e.target.value)])}
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Date Range</Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="date-from" className="text-xs">From</Label>
                <Input
                  id="date-from"
                  type="date"
                  value={dateRange[0]}
                  onChange={(e) => setDateRange([e.target.value, dateRange[1]])}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="date-to" className="text-xs">To</Label>
                <Input
                  id="date-to"
                  type="date"
                  value={dateRange[1]}
                  onChange={(e) => setDateRange([dateRange[0], e.target.value])}
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex flex-wrap gap-2 mt-1">
              {tags.map(tag => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveTag(tag)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2 mt-2">
              <Input
                placeholder="Add a tag..."
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleAddTag()
                  }
                }}
              />
              <Button type="button" size="sm" onClick={handleAddTag}>
                Add
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between items-center mt-4">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset Filters
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              <Filter className="mr-2 h-4 w-4" />
              Apply Filters
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
