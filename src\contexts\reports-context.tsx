"use client"

import React, { createContext, useContext, useState, ReactNode } from "react"
import { useToast } from "@/src/components/ui/use-toast"

// Define report types
export type ReportTimeframe = "week" | "month" | "quarter" | "year" | "custom"
export type ReportType = "overview" | "performance" | "maintenance" | "custom"
export type ReportFormat = "pdf" | "excel" | "csv" | "json"
export type ChartType = "bar" | "line" | "pie" | "area" | "scatter" | "radar" | "heatmap"

export interface DateRange {
  from?: Date
  to?: Date
}

export interface ReportFilter {
  region?: string
  serviceCenter?: string
  transformerType?: string
  status?: string
  manufacturer?: string
  installationYear?: number
  maintenanceStatus?: string
}

export interface CustomReportConfig {
  id: string
  name: string
  description: string
  metrics: string[]
  filters: ReportFilter
  chartType: ChartType
  dateRange: DateRange
  createdAt: Date
  lastRun?: Date
  schedule?: {
    frequency: "daily" | "weekly" | "monthly"
    day?: number
    time?: string
    recipients?: string[]
  }
}

// Define the context type
interface ReportsContextType {
  // State
  activeTab: ReportType
  timeframe: ReportTimeframe
  dateRange: DateRange
  filters: ReportFilter
  customReports: CustomReportConfig[]
  selectedCustomReport: string | null
  isGeneratingReport: boolean
  
  // Actions
  setActiveTab: (tab: ReportType) => void
  setTimeframe: (timeframe: ReportTimeframe) => void
  setDateRange: (range: DateRange) => void
  updateFilter: (key: keyof ReportFilter, value: any) => void
  resetFilters: () => void
  exportReport: (format: ReportFormat) => void
  generateReport: (type: ReportType, config?: any) => Promise<void>
  saveCustomReport: (config: Omit<CustomReportConfig, 'id' | 'createdAt'>) => void
  deleteCustomReport: (id: string) => void
  scheduleReport: (id: string, schedule: CustomReportConfig['schedule']) => void
  selectCustomReport: (id: string | null) => void
  
  // Data
  getRegions: () => string[]
  getServiceCenters: () => string[]
  getTransformerTypes: () => string[]
  getManufacturers: () => string[]
  getMetrics: () => { id: string, name: string, category: string }[]
}

// Sample data for reports
const sampleRegions = ["Addis Ababa", "Dire Dawa", "Bahir Dar", "Hawassa", "Mekelle", "Gondar", "Jimma"]
const sampleServiceCenters = ["Central", "North", "South", "East", "West", "Northwest", "Southeast"]
const sampleTransformerTypes = ["Distribution", "Power", "Auto", "Instrument", "Rectifier"]
const sampleManufacturers = ["ABB", "Siemens", "General Electric", "Schneider Electric", "Mitsubishi Electric"]
const sampleMetrics = [
  { id: "total_transformers", name: "Total Transformers", category: "inventory" },
  { id: "avg_efficiency", name: "Average Efficiency", category: "performance" },
  { id: "maintenance_cost", name: "Maintenance Cost", category: "financial" },
  { id: "downtime_hours", name: "Downtime Hours", category: "performance" },
  { id: "failure_rate", name: "Failure Rate", category: "reliability" },
  { id: "avg_load", name: "Average Load", category: "performance" },
  { id: "peak_load", name: "Peak Load", category: "performance" },
  { id: "oil_temperature", name: "Oil Temperature", category: "condition" },
  { id: "maintenance_frequency", name: "Maintenance Frequency", category: "maintenance" },
  { id: "age_distribution", name: "Age Distribution", category: "inventory" },
  { id: "power_quality", name: "Power Quality", category: "performance" },
  { id: "energy_loss", name: "Energy Loss", category: "efficiency" },
  { id: "cost_per_kva", name: "Cost per kVA", category: "financial" },
  { id: "mtbf", name: "Mean Time Between Failures", category: "reliability" },
  { id: "mttr", name: "Mean Time To Repair", category: "maintenance" }
]

// Sample custom reports
const sampleCustomReports: CustomReportConfig[] = [
  {
    id: "cr-001",
    name: "Regional Efficiency Analysis",
    description: "Compares transformer efficiency across different regions",
    metrics: ["avg_efficiency", "energy_loss", "peak_load"],
    filters: { region: "all" },
    chartType: "bar",
    dateRange: { from: new Date(2023, 0, 1), to: new Date() },
    createdAt: new Date(2023, 6, 15),
    lastRun: new Date(2023, 11, 10)
  },
  {
    id: "cr-002",
    name: "Maintenance Cost Breakdown",
    description: "Analyzes maintenance costs by transformer type and region",
    metrics: ["maintenance_cost", "maintenance_frequency", "mttr"],
    filters: { transformerType: "Distribution" },
    chartType: "pie",
    dateRange: { from: new Date(2023, 0, 1), to: new Date() },
    createdAt: new Date(2023, 8, 22),
    lastRun: new Date(2023, 11, 5),
    schedule: {
      frequency: "monthly",
      day: 1,
      time: "08:00",
      recipients: ["<EMAIL>", "<EMAIL>"]
    }
  },
  {
    id: "cr-003",
    name: "Reliability Metrics",
    description: "Tracks reliability indicators over time",
    metrics: ["failure_rate", "mtbf", "downtime_hours"],
    filters: { manufacturer: "all" },
    chartType: "line",
    dateRange: { from: new Date(2023, 0, 1), to: new Date() },
    createdAt: new Date(2023, 9, 10)
  }
]

// Create the context
const ReportsContext = createContext<ReportsContextType | undefined>(undefined)

// Provider component
export function ReportsProvider({ children }: { children: ReactNode }) {
  const [activeTab, setActiveTab] = useState<ReportType>("overview")
  const [timeframe, setTimeframe] = useState<ReportTimeframe>("month")
  const [dateRange, setDateRange] = useState<DateRange>({})
  const [filters, setFilters] = useState<ReportFilter>({})
  const [customReports, setCustomReports] = useState<CustomReportConfig[]>(sampleCustomReports)
  const [selectedCustomReport, setSelectedCustomReport] = useState<string | null>(null)
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)
  
  const { toast } = useToast()
  
  // Update a specific filter
  const updateFilter = (key: keyof ReportFilter, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }
  
  // Reset all filters
  const resetFilters = () => {
    setFilters({})
    setTimeframe("month")
    setDateRange({})
  }
  
  // Export report in specified format
  const exportReport = (format: ReportFormat) => {
    toast({
      title: "Report Exported",
      description: `Report has been exported in ${format.toUpperCase()} format.`
    })
  }
  
  // Generate a report
  const generateReport = async (type: ReportType, config?: any) => {
    setIsGeneratingReport(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      toast({
        title: "Report Generated",
        description: "Your report has been generated successfully."
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate report. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsGeneratingReport(false)
    }
  }
  
  // Save a custom report
  const saveCustomReport = (config: Omit<CustomReportConfig, 'id' | 'createdAt'>) => {
    const newReport: CustomReportConfig = {
      ...config,
      id: `cr-${Math.floor(1000 + Math.random() * 9000)}`,
      createdAt: new Date()
    }
    
    setCustomReports(prev => [...prev, newReport])
    
    toast({
      title: "Report Saved",
      description: `Custom report "${config.name}" has been saved.`
    })
  }
  
  // Delete a custom report
  const deleteCustomReport = (id: string) => {
    setCustomReports(prev => prev.filter(report => report.id !== id))
    
    if (selectedCustomReport === id) {
      setSelectedCustomReport(null)
    }
    
    toast({
      title: "Report Deleted",
      description: "Custom report has been deleted."
    })
  }
  
  // Schedule a report
  const scheduleReport = (id: string, schedule: CustomReportConfig['schedule']) => {
    setCustomReports(prev => 
      prev.map(report => 
        report.id === id ? { ...report, schedule } : report
      )
    )
    
    toast({
      title: "Report Scheduled",
      description: `Report will be generated ${schedule.frequency}.`
    })
  }
  
  // Select a custom report
  const selectCustomReport = (id: string | null) => {
    setSelectedCustomReport(id)
    
    if (id) {
      const report = customReports.find(r => r.id === id)
      if (report) {
        setFilters(report.filters)
        setDateRange(report.dateRange)
      }
    }
  }
  
  // Get available regions
  const getRegions = () => sampleRegions
  
  // Get available service centers
  const getServiceCenters = () => sampleServiceCenters
  
  // Get available transformer types
  const getTransformerTypes = () => sampleTransformerTypes
  
  // Get available manufacturers
  const getManufacturers = () => sampleManufacturers
  
  // Get available metrics
  const getMetrics = () => sampleMetrics
  
  return (
    <ReportsContext.Provider
      value={{
        activeTab,
        timeframe,
        dateRange,
        filters,
        customReports,
        selectedCustomReport,
        isGeneratingReport,
        setActiveTab,
        setTimeframe,
        setDateRange,
        updateFilter,
        resetFilters,
        exportReport,
        generateReport,
        saveCustomReport,
        deleteCustomReport,
        scheduleReport,
        selectCustomReport,
        getRegions,
        getServiceCenters,
        getTransformerTypes,
        getManufacturers,
        getMetrics
      }}
    >
      {children}
    </ReportsContext.Provider>
  )
}

// Hook to use the reports context
export function useReports() {
  const context = useContext(ReportsContext)
  
  if (context === undefined) {
    throw new Error("useReports must be used within a ReportsProvider")
  }
  
  return context
}
