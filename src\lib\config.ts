/**
 * Application Configuration
 * Centralized configuration management
 */

export const config = {
  // Database configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    name: process.env.DB_NAME || 'dtms_eeu_db',
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10'),
    acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
    timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
  },

  // API configuration
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || '',
    timeout: parseInt(process.env.API_TIMEOUT || '30000'),
    retryAttempts: parseInt(process.env.API_RETRY_ATTEMPTS || '3'),
    retryDelay: parseInt(process.env.API_RETRY_DELAY || '1000'),
  },

  // Cache configuration
  cache: {
    defaultTtl: parseInt(process.env.CACHE_DEFAULT_TTL || '30000'), // 30 seconds
    dashboardTtl: parseInt(process.env.CACHE_DASHBOARD_TTL || '30000'),
    transformersTtl: parseInt(process.env.CACHE_TRANSFORMERS_TTL || '60000'),
  },

  // Application settings
  app: {
    name: 'EEU Digital Transformer Management System',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '2.0.0',
    environment: process.env.NODE_ENV || 'development',
    debug: process.env.NODE_ENV === 'development',
  },

  // Pagination defaults
  pagination: {
    defaultLimit: 50,
    maxLimit: 100,
    defaultPage: 1,
  },

  // Feature flags
  features: {
    enableCaching: process.env.ENABLE_CACHING !== 'false',
    enableRealTimeUpdates: process.env.ENABLE_REAL_TIME !== 'false',
    enableAnalytics: process.env.ENABLE_ANALYTICS !== 'false',
    enableNotifications: process.env.ENABLE_NOTIFICATIONS !== 'false',
  },

  // Security settings
  security: {
    enableCors: process.env.ENABLE_CORS !== 'false',
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3002'],
    enableRateLimit: process.env.ENABLE_RATE_LIMIT !== 'false',
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100'),
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15 minutes
  },
}

/**
 * Validate required environment variables
 */
export function validateConfig(): void {
  const requiredEnvVars = [
    'DB_HOST',
    'DB_USER',
    'DB_NAME',
  ]

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName])

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}`
    )
  }
}

/**
 * Get configuration for specific environment
 */
export function getEnvironmentConfig() {
  const env = config.app.environment

  switch (env) {
    case 'production':
      return {
        ...config,
        cache: {
          ...config.cache,
          defaultTtl: 300000, // 5 minutes in production
          dashboardTtl: 300000,
        },
        features: {
          ...config.features,
          enableAnalytics: true,
        },
      }

    case 'staging':
      return {
        ...config,
        cache: {
          ...config.cache,
          defaultTtl: 60000, // 1 minute in staging
        },
      }

    default: // development
      return {
        ...config,
        cache: {
          ...config.cache,
          defaultTtl: 10000, // 10 seconds in development
        },
      }
  }
}

export default config
