"use client"

import React, { useState, useEffect } from 'react'
import {
  Search,
  Filter,
  X,
  MapPin,
  Zap,
  Building,
  Settings,
  ChevronDown,
  RotateCcw,
  Eye,
  EyeOff
} from 'lucide-react'
import { Input } from "@/src/components/ui/input"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Checkbox } from "@/src/components/ui/checkbox"
import { Slider } from "@/src/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/src/components/ui/collapsible"

export interface TransformerFilters {
  search: string
  status: string[]
  region: string[]
  serviceCenter: string[]
  manufacturer: string[]
  type: string[]
  voltageLevel: string[]
  capacityRange: [number, number]
  healthIndexRange: [number, number]
  installationYear: string[]
  lastMaintenanceRange: [number, number] // days ago
  showCriticalOnly: boolean
  showMaintenanceDue: boolean
}

interface TransformerMapFiltersProps {
  filters: TransformerFilters
  onFiltersChange: (filters: TransformerFilters) => void
  isVisible: boolean
  onToggleVisibility: () => void
  totalCount: number
  filteredCount: number
}

const statusOptions = [
  { value: 'operational', label: 'Operational', color: '#10b981' },
  { value: 'warning', label: 'Warning', color: '#f59e0b' },
  { value: 'maintenance', label: 'Maintenance', color: '#3b82f6' },
  { value: 'critical', label: 'Critical', color: '#ef4444' },
  { value: 'burnt', label: 'Burnt', color: '#7c2d12' },
  { value: 'offline', label: 'Offline', color: '#6b7280' }
]

const regionOptions = [
  'Addis Ababa', 'Afar', 'Amhara', 'Benishangul-Gumuz', 'Dire Dawa',
  'Gambela', 'Harari', 'Oromia', 'Sidama', 'SNNP', 'Somali', 'Tigray'
]

const manufacturerOptions = [
  'ABB', 'Siemens', 'Schneider Electric', 'General Electric', 'Toshiba',
  'Mitsubishi', 'Hyundai', 'TBEA', 'Crompton Greaves', 'Kirloskar'
]

const typeOptions = [
  'Distribution', 'Power', 'Instrument', 'Auto', 'Isolation', 'Step-up', 'Step-down'
]

const voltageLevelOptions = [
  '400V', '11kV', '15kV', '33kV', '66kV', '132kV', '230kV', '400kV'
]

export function TransformerMapFilters({
  filters,
  onFiltersChange,
  isVisible,
  onToggleVisibility,
  totalCount,
  filteredCount
}: TransformerMapFiltersProps) {
  const [expandedSections, setExpandedSections] = useState({
    basic: true,
    location: false,
    technical: false,
    performance: false,
    maintenance: false
  })

  const updateFilter = (key: keyof TransformerFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const toggleArrayFilter = (key: keyof TransformerFilters, value: string) => {
    const currentArray = filters[key] as string[]
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    updateFilter(key, newArray)
  }

  const clearAllFilters = () => {
    onFiltersChange({
      search: '',
      status: [],
      region: [],
      serviceCenter: [],
      manufacturer: [],
      type: [],
      voltageLevel: [],
      capacityRange: [0, 5000],
      healthIndexRange: [0, 100],
      installationYear: [],
      lastMaintenanceRange: [0, 365],
      showCriticalOnly: false,
      showMaintenanceDue: false
    })
  }

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.status.length > 0) count++
    if (filters.region.length > 0) count++
    if (filters.serviceCenter.length > 0) count++
    if (filters.manufacturer.length > 0) count++
    if (filters.type.length > 0) count++
    if (filters.voltageLevel.length > 0) count++
    if (filters.capacityRange[0] > 0 || filters.capacityRange[1] < 5000) count++
    if (filters.healthIndexRange[0] > 0 || filters.healthIndexRange[1] < 100) count++
    if (filters.installationYear.length > 0) count++
    if (filters.lastMaintenanceRange[0] > 0 || filters.lastMaintenanceRange[1] < 365) count++
    if (filters.showCriticalOnly) count++
    if (filters.showMaintenanceDue) count++
    return count
  }

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  if (!isVisible) {
    return (
      <div className="absolute top-4 left-4 z-[1000]">
        <Button
          onClick={onToggleVisibility}
          variant="default"
          size="sm"
          className="shadow-lg"
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
          {getActiveFilterCount() > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
              {getActiveFilterCount()}
            </Badge>
          )}
        </Button>
      </div>
    )
  }

  return (
    <div className="absolute top-4 left-4 z-[1000] w-80 max-h-[calc(100vh-2rem)] overflow-y-auto">
      <Card className="shadow-xl border-2">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Transformer Filters
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {filteredCount} of {totalCount}
              </Badge>
              <Button
                variant="ghost"
                size="icon"
                onClick={onToggleVisibility}
                className="h-8 w-8"
              >
                <EyeOff className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllFilters}
              className="text-xs h-7"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Clear All
            </Button>
            <div className="flex gap-1">
              <Button
                variant={filters.showCriticalOnly ? "default" : "outline"}
                size="sm"
                onClick={() => updateFilter('showCriticalOnly', !filters.showCriticalOnly)}
                className="text-xs h-7"
              >
                Critical Only
              </Button>
              <Button
                variant={filters.showMaintenanceDue ? "default" : "outline"}
                size="sm"
                onClick={() => updateFilter('showMaintenanceDue', !filters.showMaintenanceDue)}
                className="text-xs h-7"
              >
                Maintenance Due
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Search */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Search</label>
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by serial, name, location..."
                value={filters.search}
                onChange={(e) => updateFilter('search', e.target.value)}
                className="pl-8"
              />
              {filters.search && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => updateFilter('search', '')}
                  className="absolute right-0 top-0 h-full w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Basic Filters */}
          <Collapsible open={expandedSections.basic} onOpenChange={() => toggleSection('basic')}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                <span className="text-sm font-medium">Status & Type</span>
                <ChevronDown className={`h-4 w-4 transition-transform ${expandedSections.basic ? 'rotate-180' : ''}`} />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-3 pt-2">
              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">Status</label>
                <div className="grid grid-cols-2 gap-1">
                  {statusOptions.map((status) => (
                    <div key={status.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${status.value}`}
                        checked={filters.status.includes(status.value)}
                        onCheckedChange={() => toggleArrayFilter('status', status.value)}
                      />
                      <label
                        htmlFor={`status-${status.value}`}
                        className="text-xs flex items-center gap-1 cursor-pointer"
                      >
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: status.color }}
                        />
                        {status.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Type Filter */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">Type</label>
                <div className="grid grid-cols-2 gap-1">
                  {typeOptions.map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type}`}
                        checked={filters.type.includes(type)}
                        onCheckedChange={() => toggleArrayFilter('type', type)}
                      />
                      <label htmlFor={`type-${type}`} className="text-xs cursor-pointer">
                        {type}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Location Filters */}
          <Collapsible open={expandedSections.location} onOpenChange={() => toggleSection('location')}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                <span className="text-sm font-medium flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location
                </span>
                <ChevronDown className={`h-4 w-4 transition-transform ${expandedSections.location ? 'rotate-180' : ''}`} />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-3 pt-2">
              {/* Region Filter */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">Region</label>
                <div className="grid grid-cols-1 gap-1 max-h-32 overflow-y-auto">
                  {regionOptions.map((region) => (
                    <div key={region} className="flex items-center space-x-2">
                      <Checkbox
                        id={`region-${region}`}
                        checked={filters.region.includes(region)}
                        onCheckedChange={() => toggleArrayFilter('region', region)}
                      />
                      <label htmlFor={`region-${region}`} className="text-xs cursor-pointer">
                        {region}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Service Center Filter */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">Service Center</label>
                <Select
                  value={filters.serviceCenter.length > 0 ? filters.serviceCenter[0] : 'all'}
                  onValueChange={(value) => updateFilter('serviceCenter', value === 'all' ? [] : [value])}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="Select service center" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Service Centers</SelectItem>
                    <SelectItem value="Addis Ababa SC">Addis Ababa SC</SelectItem>
                    <SelectItem value="Bahir Dar SC">Bahir Dar SC</SelectItem>
                    <SelectItem value="Hawassa SC">Hawassa SC</SelectItem>
                    <SelectItem value="Mekelle SC">Mekelle SC</SelectItem>
                    <SelectItem value="Jimma SC">Jimma SC</SelectItem>
                    <SelectItem value="Dire Dawa SC">Dire Dawa SC</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Technical Specifications */}
          <Collapsible open={expandedSections.technical} onOpenChange={() => toggleSection('technical')}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                <span className="text-sm font-medium flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Technical Specs
                </span>
                <ChevronDown className={`h-4 w-4 transition-transform ${expandedSections.technical ? 'rotate-180' : ''}`} />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-3 pt-2">
              {/* Manufacturer Filter */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">Manufacturer</label>
                <div className="grid grid-cols-2 gap-1 max-h-24 overflow-y-auto">
                  {manufacturerOptions.map((manufacturer) => (
                    <div key={manufacturer} className="flex items-center space-x-2">
                      <Checkbox
                        id={`manufacturer-${manufacturer}`}
                        checked={filters.manufacturer.includes(manufacturer)}
                        onCheckedChange={() => toggleArrayFilter('manufacturer', manufacturer)}
                      />
                      <label htmlFor={`manufacturer-${manufacturer}`} className="text-xs cursor-pointer">
                        {manufacturer}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Voltage Level Filter */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">Voltage Level</label>
                <div className="grid grid-cols-3 gap-1">
                  {voltageLevelOptions.map((voltage) => (
                    <div key={voltage} className="flex items-center space-x-2">
                      <Checkbox
                        id={`voltage-${voltage}`}
                        checked={filters.voltageLevel.includes(voltage)}
                        onCheckedChange={() => toggleArrayFilter('voltageLevel', voltage)}
                      />
                      <label htmlFor={`voltage-${voltage}`} className="text-xs cursor-pointer">
                        {voltage}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Capacity Range */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">
                  Capacity Range (kVA): {filters.capacityRange[0]} - {filters.capacityRange[1]}
                </label>
                <Slider
                  value={filters.capacityRange}
                  onValueChange={(value) => updateFilter('capacityRange', value as [number, number])}
                  max={5000}
                  min={0}
                  step={50}
                  className="w-full"
                />
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Performance Metrics */}
          <Collapsible open={expandedSections.performance} onOpenChange={() => toggleSection('performance')}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                <span className="text-sm font-medium flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Performance
                </span>
                <ChevronDown className={`h-4 w-4 transition-transform ${expandedSections.performance ? 'rotate-180' : ''}`} />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-3 pt-2">
              {/* Health Index Range */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">
                  Health Index: {filters.healthIndexRange[0]}% - {filters.healthIndexRange[1]}%
                </label>
                <Slider
                  value={filters.healthIndexRange}
                  onValueChange={(value) => updateFilter('healthIndexRange', value as [number, number])}
                  max={100}
                  min={0}
                  step={5}
                  className="w-full"
                />
              </div>

              {/* Installation Year */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">Installation Year</label>
                <Select
                  value={filters.installationYear.length > 0 ? filters.installationYear[0] : 'all'}
                  onValueChange={(value) => updateFilter('installationYear', value === 'all' ? [] : [value])}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="Select year range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Years</SelectItem>
                    <SelectItem value="2020-2024">2020-2024 (Recent)</SelectItem>
                    <SelectItem value="2015-2019">2015-2019</SelectItem>
                    <SelectItem value="2010-2014">2010-2014</SelectItem>
                    <SelectItem value="2005-2009">2005-2009</SelectItem>
                    <SelectItem value="2000-2004">2000-2004 (Legacy)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Maintenance Filters */}
          <Collapsible open={expandedSections.maintenance} onOpenChange={() => toggleSection('maintenance')}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                <span className="text-sm font-medium flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Maintenance
                </span>
                <ChevronDown className={`h-4 w-4 transition-transform ${expandedSections.maintenance ? 'rotate-180' : ''}`} />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-3 pt-2">
              {/* Last Maintenance Range */}
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">
                  Last Maintenance: {filters.lastMaintenanceRange[0]} - {filters.lastMaintenanceRange[1]} days ago
                </label>
                <Slider
                  value={filters.lastMaintenanceRange}
                  onValueChange={(value) => updateFilter('lastMaintenanceRange', value as [number, number])}
                  max={365}
                  min={0}
                  step={30}
                  className="w-full"
                />
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>
    </div>
  )
}
