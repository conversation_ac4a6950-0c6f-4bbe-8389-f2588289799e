"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// Types
interface SidebarContextType {
  isOpen: boolean
  isMobile: boolean
  toggle: () => void
  open: () => void
  close: () => void
  setOpen: (open: boolean) => void
}

// Create context
const SidebarContext = createContext<SidebarContextType | undefined>(undefined)

// Sidebar Provider Component
export function SidebarProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
      
      // Auto-close sidebar on mobile
      if (mobile) {
        setIsOpen(false)
      } else {
        // Auto-open sidebar on desktop
        setIsOpen(true)
      }
    }

    // Check on mount
    checkMobile()

    // Listen for resize events
    window.addEventListener('resize', checkMobile)
    
    return () => {
      window.removeEventListener('resize', checkMobile)
    }
  }, [])

  const toggle = () => {
    setIsOpen(prev => !prev)
  }

  const open = () => {
    setIsOpen(true)
  }

  const close = () => {
    setIsOpen(false)
  }

  const setOpen = (open: boolean) => {
    setIsOpen(open)
  }

  const value: SidebarContextType = {
    isOpen,
    isMobile,
    toggle,
    open,
    close,
    setOpen
  }

  return (
    <SidebarContext.Provider value={value}>
      {children}
    </SidebarContext.Provider>
  )
}

// Custom hook to use sidebar context
export function useSidebar() {
  const context = useContext(SidebarContext)
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider')
  }
  return context
}

// Export types for use in other components
export type { SidebarContextType }
