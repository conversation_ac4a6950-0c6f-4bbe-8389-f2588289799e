/**
 * Shared Types Index
 * Central export for all shared type definitions
 */

// Export common types
export * from './common.types'
export * from './auth.types'

// Re-export specific types for convenience
export type {
  BaseEntity,
  PaginationParams,
  PaginationMeta,
  PaginatedResponse,
  FilterParams,
  SortParams,
  ApiResponse,
  ApiSuccessResponse,
  ApiErrorResponse,
  FormField,
  FormState,
  Status,
  Priority,
  AlertType,
  Coordinates,
  Address,
  Location,
  FileInfo,
  UploadProgress,
  Notification,
  NotificationAction,
  AuditLog,
  SearchResult,
  SearchResponse,
  ChartDataPoint,
  ChartSeries,
  ChartConfig,
  TableColumn,
  TableProps,
  ThemeMode,
  ThemeConfig,
  Language,
  LocaleConfig,
  Permission,
  Role,
  CustomEvent,
  AppError,
  LoadingState,
  AsyncState
} from './common.types'

export type {
  User,
  UserStatus,
  UserRole,
  UserPreferences,
  LoginCredentials,
  RegisterData,
  AuthTokens,
  AuthSession,
  PasswordChangeRequest,
  PasswordResetRequest,
  PasswordResetConfirm,
  PasswordPolicy,
  RoleDefinition,
  RolePermission,
  PermissionDefinition,
  PermissionCondition,
  OAuthProvider,
  OAuthProfile,
  SecurityEvent,
  SecurityEventType,
  LoginAttempt,
  AccountLockout,
  SessionInfo,
  TwoFactorAuth,
  TwoFactorSetup,
  TwoFactorVerification,
  ApiKey,
  ApiKeyRequest,
  AuthContextValue,
  UseAuthReturn,
  UsePermissionsReturn,
  AuthFormErrors,
  AuthFormState,
  AuthAction,
  AuthState
} from './auth.types'

// Export utility functions
export {
  isApiSuccessResponse,
  isApiErrorResponse,
  createAsyncState,
  createPaginationMeta
} from './common.types'

export {
  createInitialAuthState,
  isTokenExpired,
  hasPermission,
  hasRole,
  checkPermissions
} from './auth.types'

// Type guards and utility types
export const isBaseEntity = (obj: any): obj is BaseEntity => {
  return obj && typeof obj.id === 'string' && obj.createdAt && obj.updatedAt
}

export const isUser = (obj: any): obj is User => {
  return isBaseEntity(obj) && typeof obj.email === 'string' && typeof obj.firstName === 'string'
}

export const isPaginatedResponse = <T>(obj: any): obj is PaginatedResponse<T> => {
  return obj && Array.isArray(obj.data) && obj.meta && typeof obj.meta.total === 'number'
}

// Common constants
export const DEFAULT_PAGINATION: PaginationParams = {
  page: 1,
  limit: 20,
  offset: 0,
  sortBy: 'createdAt',
  sortOrder: 'desc'
}

export const ALERT_TYPES: AlertType[] = ['success', 'error', 'warning', 'info']

export const PRIORITIES: Priority[] = ['low', 'medium', 'high', 'critical']

export const USER_STATUSES: UserStatus[] = ['active', 'inactive', 'suspended', 'pending_verification']

export const LOADING_STATES: LoadingState[] = ['idle', 'loading', 'success', 'error']

// Validation schemas (basic)
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
export const PHONE_REGEX = /^\+?[\d\s\-\(\)]+$/
export const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/

// Error codes
export const ERROR_CODES = {
  // Authentication errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_SUSPENDED: 'ACCOUNT_SUSPENDED',
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
  
  // Authorization errors
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  ROLE_NOT_FOUND: 'ROLE_NOT_FOUND',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  REQUIRED_FIELD: 'REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  DUPLICATE_VALUE: 'DUPLICATE_VALUE',
  
  // Resource errors
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  RESOURCE_LOCKED: 'RESOURCE_LOCKED',
  
  // System errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  MAINTENANCE_MODE: 'MAINTENANCE_MODE'
} as const

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

// Success messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Successfully logged in',
  LOGOUT_SUCCESS: 'Successfully logged out',
  REGISTRATION_SUCCESS: 'Account created successfully',
  PASSWORD_CHANGED: 'Password changed successfully',
  PASSWORD_RESET_SENT: 'Password reset email sent',
  PASSWORD_RESET_SUCCESS: 'Password reset successfully',
  PROFILE_UPDATED: 'Profile updated successfully',
  EMAIL_VERIFIED: 'Email verified successfully',
  
  TRANSFORMER_CREATED: 'Transformer created successfully',
  TRANSFORMER_UPDATED: 'Transformer updated successfully',
  TRANSFORMER_DELETED: 'Transformer deleted successfully',
  
  MAINTENANCE_SCHEDULED: 'Maintenance scheduled successfully',
  MAINTENANCE_COMPLETED: 'Maintenance completed successfully',
  MAINTENANCE_CANCELLED: 'Maintenance cancelled successfully',
  
  REPORT_GENERATED: 'Report generated successfully',
  DATA_EXPORTED: 'Data exported successfully',
  DATA_IMPORTED: 'Data imported successfully',
  
  SETTINGS_SAVED: 'Settings saved successfully',
  NOTIFICATION_SENT: 'Notification sent successfully'
} as const

export type SuccessMessage = typeof SUCCESS_MESSAGES[keyof typeof SUCCESS_MESSAGES]

// Default values
export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: 'light',
  language: 'en',
  timezone: 'Africa/Addis_Ababa',
  dateFormat: 'MMM dd, yyyy',
  notifications: {
    email: true,
    push: true,
    sms: false,
    inApp: true
  },
  dashboard: {
    layout: 'grid',
    widgets: ['overview', 'alerts', 'maintenance', 'performance'],
    refreshInterval: 30000
  }
}

export const DEFAULT_THEME_CONFIG: ThemeConfig = {
  mode: 'light',
  primaryColor: '#10B981',
  accentColor: '#3B82F6',
  borderRadius: 8,
  fontSize: 'medium'
}

export const DEFAULT_LOCALE_CONFIG: LocaleConfig = {
  language: 'en',
  dateFormat: 'MMM dd, yyyy',
  timeFormat: 'HH:mm',
  numberFormat: 'en-US',
  currency: 'ETB'
}

// Export everything as default for convenience
export default {
  ERROR_CODES,
  SUCCESS_MESSAGES,
  DEFAULT_PAGINATION,
  ALERT_TYPES,
  PRIORITIES,
  USER_STATUSES,
  LOADING_STATES,
  EMAIL_REGEX,
  PHONE_REGEX,
  PASSWORD_REGEX,
  DEFAULT_USER_PREFERENCES,
  DEFAULT_THEME_CONFIG,
  DEFAULT_LOCALE_CONFIG
}
