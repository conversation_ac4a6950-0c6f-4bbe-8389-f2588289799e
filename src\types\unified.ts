/**
 * Unified Type Definitions for EEU-DTMS
 * Standardized interfaces and types for consistent data usage across all components
 */

// ===== ENUMS =====

export enum TransformerStatus {
  OPERATIONAL = 'operational',
  WARNING = 'warning',
  MAINTENANCE = 'maintenance',
  CRITICAL = 'critical',
  OFFLINE = 'offline',
  BURNT = 'burnt'
}

export enum MaintenanceStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  OVERDUE = 'overdue'
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum AlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  CRITICAL = 'critical'
}

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  NATIONAL_ASSET_MANAGER = 'national_asset_manager',
  NATIONAL_MAINTENANCE_MANAGER = 'national_maintenance_manager',
  REGIONAL_ADMIN = 'regional_admin',
  REGIONAL_ASSET_MANAGER = 'regional_asset_manager',
  REGIONAL_MAINTENANCE_ENGINEER = 'regional_maintenance_engineer',
  SERVICE_CENTER_MANAGER = 'service_center_manager',
  FIELD_TECHNICIAN = 'field_technician',
  CUSTOMER_SERVICE_AGENT = 'customer_service_agent',
  AUDIT_COMPLIANCE_OFFICER = 'audit_compliance_officer'
}

// ===== BASE INTERFACES =====

export interface BaseEntity {
  id: string
  createdAt: string
  updatedAt: string
}

export interface Coordinates {
  lat: number
  lng: number
}

export interface Location {
  name: string
  region: string
  serviceCenter: string
  address: string
  coordinates: Coordinates
}

export interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

export interface APIResponse<T> {
  success: boolean
  data: T
  message?: string
  error?: string
  pagination?: Pagination
  metadata?: {
    timestamp: string
    version: string
    requestId: string
  }
}

// ===== TRANSFORMER INTERFACES =====

export interface TransformerMetrics {
  loadPercentage: number      // Current load 0-100
  temperature: number         // Current temperature in Celsius
  efficiency: number          // Efficiency rating 0-100
  uptimePercentage: number    // Uptime 0-100
  healthScore: number         // Overall health 0-100
  oilLevel?: number          // Oil level 0-100
  powerFactor?: number       // Power factor
}

export interface TransformerVoltage {
  primary: number            // Primary voltage in kV
  secondary: number          // Secondary voltage in V
}

export interface TransformerMaintenance {
  lastDate: string          // ISO date string
  nextDate: string          // ISO date string
  activeAlerts: number
  totalMaintenanceHours: number
  averageDowntime: number   // Hours
}

export interface StandardTransformer extends BaseEntity {
  // Core Identity
  serialNumber: string
  name: string
  
  // Technical Specifications
  type: 'distribution' | 'power' | 'instrument' | 'auto'
  manufacturer: string
  model: string
  kvaRating: number
  voltage: TransformerVoltage
  
  // Status & Health
  status: TransformerStatus
  healthScore: number
  
  // Location
  location: Location
  
  // Performance Metrics
  metrics: TransformerMetrics
  
  // Maintenance
  maintenance: TransformerMaintenance
  
  // Installation
  installationDate: string
  yearManufactured?: number
  
  // Additional Properties
  tags?: string[]
  notes?: string
}

// ===== MAINTENANCE INTERFACES =====

export interface MaintenanceTask extends BaseEntity {
  // Core Information
  transformerId: string
  transformerName: string
  transformerLocation: string
  
  // Task Details
  type: 'preventive' | 'corrective' | 'predictive' | 'emergency'
  status: MaintenanceStatus
  priority: Priority
  title: string
  description: string
  
  // Scheduling
  scheduledDate: string
  estimatedDuration: number  // Hours
  actualDuration?: number    // Hours
  
  // Assignment
  assignedTechnicianId?: string
  assignedTechnicianName?: string
  assignedTechnicianPhone?: string
  
  // Progress
  completedDate?: string
  completionNotes?: string
  
  // Resources
  requiredParts?: Array<{
    name: string
    quantity: number
    cost?: number
  }>
  totalCost?: number
  
  // Urgency
  daysUntilDue: number
  urgencyStatus: 'normal' | 'upcoming' | 'urgent' | 'overdue'
  
  // Attachments
  attachments?: Array<{
    name: string
    url: string
    type: string
    size: number
  }>
}

// ===== ALERT INTERFACES =====

export interface Alert extends BaseEntity {
  // Core Information
  title: string
  description: string
  severity: AlertSeverity
  category: 'system' | 'maintenance' | 'performance' | 'weather' | 'security'
  
  // Status
  status: 'active' | 'acknowledged' | 'resolved' | 'ignored'
  
  // Assignment
  assignedToId?: string
  assignedToName?: string
  acknowledgedAt?: string
  acknowledgedById?: string
  resolvedAt?: string
  resolvedById?: string
  
  // Source
  transformerId?: string
  transformerName?: string
  location?: string
  
  // Priority
  priority: Priority
  
  // Metadata
  source: 'system' | 'manual' | 'sensor' | 'external'
  ageInMinutes: number
  hasWorkOrder: boolean
  
  // Resolution
  resolutionNotes?: string
  escalationLevel: number
}

// ===== USER INTERFACES =====

export interface User extends BaseEntity {
  // Personal Information
  email: string
  firstName: string
  lastName: string
  fullName: string
  phone?: string
  avatar?: string
  
  // Professional Information
  employeeId?: string
  department?: string
  position?: string
  
  // System Information
  role: UserRole
  status: 'active' | 'inactive' | 'suspended'
  emailVerified: boolean
  phoneVerified: boolean
  
  // Access Control
  region?: string
  serviceCenter?: string
  permissions: string[]
  
  // Activity
  lastLoginAt?: string
  passwordChangedAt?: string
  
  // Preferences
  preferences: {
    theme: 'light' | 'dark' | 'system'
    language: string
    notifications: boolean
    dashboardLayout?: string
  }
}

// ===== SMART METER INTERFACES =====

export interface SmartMeter extends BaseEntity {
  // Core Information
  serialNumber: string
  manufacturer: string
  model: string
  type: 'residential' | 'commercial' | 'industrial'
  
  // Status
  status: 'connected' | 'disconnected' | 'maintenance' | 'tampered'
  
  // Location
  location: Location
  
  // Technical Information
  firmwareVersion: string
  batteryLevel?: number
  signalStrength?: number
  
  // Customer Information
  customer: {
    id: string
    name: string
    accountNumber: string
    contactPhone: string
    email?: string
  }
  
  // Billing
  tariffPlan: string
  billingCycle: 'monthly' | 'quarterly'
  prepaid: boolean
  balance?: number
  
  // Readings
  lastReading: {
    timestamp: string
    value: number
    peak: boolean
  }
  
  // Communication
  lastCommunication: string
  installationDate: string
  
  // Alerts
  activeAlerts: number
  
  // Notes
  notes?: string
}

// ===== STATISTICS INTERFACES =====

export interface TransformerStatistics {
  total: number
  operational: number
  warning: number
  maintenance: number
  critical: number
  offline: number
  burnt: number
  averageLoad: number
  averageEfficiency: number
  averageHealthScore: number
}

export interface MaintenanceStatistics {
  totalTasks: number
  pendingTasks: number
  inProgressTasks: number
  completedTasks: number
  overdueTasks: number
  criticalTasks: number
  averageCompletionDays: number
  efficiency: number
}

export interface AlertStatistics {
  totalAlerts: number
  activeAlerts: number
  criticalAlerts: number
  highAlerts: number
  mediumAlerts: number
  lowAlerts: number
  resolvedAlerts: number
  resolutionRate: number
  averageResolutionTime: number
}

// ===== DASHBOARD INTERFACES =====

export interface DashboardAnalytics {
  overview: {
    totalTransformers: number
    operationalTransformers: number
    averageLoad: number
    averageEfficiency: number
    systemUptime: number
    efficiencyTrend: 'up' | 'down' | 'stable'
  }
  maintenance: MaintenanceStatistics
  alerts: AlertStatistics
  performance: {
    averageUptime: number
    averageEfficiency: number
    averageLoadFactor: number
    monitoredTransformers: number
  }
  regional: Array<{
    name: string
    code: string
    transformerCount: number
    averageLoad: number
    operationalCount: number
    operationalPercentage: number
  }>
  trends: {
    daily: Array<{
      date: string
      alerts: number
      criticalPercentage: number
    }>
  }
  summary: {
    systemHealth: number
    maintenanceBacklog: number
    criticalIssues: number
    overallEfficiency: number
  }
}

// ===== EXPORT ALL TYPES =====

export type {
  BaseEntity,
  Coordinates,
  Location,
  Pagination,
  APIResponse,
  TransformerMetrics,
  TransformerVoltage,
  TransformerMaintenance,
  StandardTransformer,
  MaintenanceTask,
  Alert,
  User,
  SmartMeter,
  TransformerStatistics,
  MaintenanceStatistics,
  AlertStatistics,
  DashboardAnalytics
}
