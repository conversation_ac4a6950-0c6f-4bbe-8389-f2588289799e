"use client"

import { useState, useEffect } from 'react'
import {
  Activity,
  Thermometer,
  Droplet,
  Percent,
  Zap,
  AlertTriangle,
  Clock,
  RefreshCw,
  Download,
  Filter,
  ChevronDown,
  Search,
  BarChart2,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  Settings
} from 'lucide-react'
import { MainLayout } from '@/src/components/layout/main-layout'
// import { TransformerLayout } from '@/components/transformer/TransformerLayout' // Commented out - using MainLayout instead
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import { Progress } from '@/src/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/src/components/ui/tabs'

export default function TransformerMonitoringDashboardPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedTransformer, setSelectedTransformer] = useState<string | null>(null)
  const [monitoringData, setMonitoringData] = useState({
    overview: {
      totalMonitored: 87,
      online: 82,
      offline: 5,
      critical: 3,
      warning: 8
    },
    parameters: {
      temperature: {
        current: 65,
        min: 45,
        max: 75,
        threshold: 85,
        unit: '°C'
      },
      loadPercentage: {
        current: 72,
        min: 45,
        max: 85,
        threshold: 95,
        unit: '%'
      },
      oilLevel: {
        current: 85,
        min: 75,
        max: 95,
        threshold: 60,
        unit: '%'
      },
      voltage: {
        primary: {
          current: 11200,
          min: 10800,
          max: 11500,
          threshold: 11800,
          unit: 'V'
        },
        secondary: {
          current: 410,
          min: 390,
          max: 420,
          threshold: 430,
          unit: 'V'
        }
      },
      current: {
        primary: {
          current: 65,
          min: 40,
          max: 75,
          threshold: 85,
          unit: 'A'
        },
        secondary: {
          current: 950,
          min: 700,
          max: 1100,
          threshold: 1200,
          unit: 'A'
        }
      },
      powerFactor: {
        current: 0.92,
        min: 0.85,
        max: 0.95,
        threshold: 0.8,
        unit: ''
      }
    },
    alerts: [
      {
        id: 'alert-001',
        transformerId: 'tr-037',
        transformerName: 'Lideta Sub-station T4',
        parameter: 'temperature',
        value: 85,
        threshold: 75,
        severity: 'critical',
        timestamp: '2023-06-14T09:30:00Z',
        status: 'active'
      },
      {
        id: 'alert-002',
        transformerId: 'tr-015',
        transformerName: 'Kirkos Sub-station T3',
        parameter: 'loadPercentage',
        value: 95,
        threshold: 90,
        severity: 'critical',
        timestamp: '2023-06-14T10:15:00Z',
        status: 'active'
      },
      {
        id: 'alert-003',
        transformerId: 'tr-042',
        transformerName: 'Arada Sub-station T2',
        parameter: 'oilLevel',
        value: 45,
        threshold: 50,
        severity: 'high',
        timestamp: '2023-06-14T08:45:00Z',
        status: 'active'
      }
    ]
  })

  // Refresh data
  const refreshData = () => {
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }

  // Format time
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get parameter icon
  const getParameterIcon = (parameter: string) => {
    switch (parameter) {
      case 'temperature':
        return <Thermometer className="h-5 w-5 text-red-500" />
      case 'loadPercentage':
        return <Percent className="h-5 w-5 text-blue-500" />
      case 'oilLevel':
        return <Droplet className="h-5 w-5 text-blue-500" />
      case 'voltage':
        return <Zap className="h-5 w-5 text-yellow-500" />
      case 'current':
        return <Activity className="h-5 w-5 text-green-500" />
      default:
        return <Activity className="h-5 w-5 text-gray-500" />
    }
  }

  // Get severity badge
  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            Critical
          </Badge>
        )
      case 'high':
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-700">
            High
          </Badge>
        )
      case 'medium':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            Medium
          </Badge>
        )
      case 'low':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            Low
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700">
            Info
          </Badge>
        )
    }
  }

  return (
    <MainLayout
      allowedRoles={[
        "super_admin",
        "national_asset_manager",
        "national_maintenance_manager",
        "regional_admin",
        "regional_asset_manager",
        "regional_maintenance_engineer",
        "service_center_manager",
        "field_technician"
      ]}
      requiredPermissions={[{ resource: "transformers", action: "read" }]}
    >
      <TransformerLayout>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Transformer Monitoring Dashboard</h1>
              <p className="text-gray-600 mt-1">Real-time monitoring of transformer parameters and status</p>
            </div>

            <div className="flex items-center space-x-2">
              <div className="relative">
                <select className="appearance-none bg-white border rounded-md px-3 py-1.5 pr-8 text-sm focus:outline-none focus:ring-1 focus:ring-green-500">
                  <option value="">All Transformers</option>
                  <option value="tr-001">Bole Sub-station T1</option>
                  <option value="tr-015">Kirkos Sub-station T3</option>
                  <option value="tr-037">Lideta Sub-station T4</option>
                  <option value="tr-042">Arada Sub-station T2</option>
                </select>
                <ChevronDown size={14} className="absolute right-2.5 top-2.5 text-gray-400" />
              </div>

              <Button variant="outline" size="sm" onClick={refreshData} disabled={isLoading}>
                {isLoading ? (
                  <RefreshCw size={14} className="mr-1 animate-spin" />
                ) : (
                  <RefreshCw size={14} className="mr-1" />
                )}
                Refresh
              </Button>

              <Button variant="outline" size="sm">
                <Download size={14} className="mr-1" />
                Export
              </Button>

              <Button variant="outline" size="sm">
                <Settings size={14} className="mr-1" />
                Settings
              </Button>
            </div>
          </div>

          {/* Stats cards */}
          <div className="grid grid-cols-5 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Monitored Transformers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-green-600">{monitoringData.overview.totalMonitored}</div>
                  <Activity size={20} className="text-green-600" />
                </div>
                <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                  <span>Online: {monitoringData.overview.online}</span>
                  <span>Offline: {monitoringData.overview.offline}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Temperature</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-amber-600">{monitoringData.parameters.temperature.current}°C</div>
                  <Thermometer size={20} className="text-amber-600" />
                </div>
                <Progress
                  value={(monitoringData.parameters.temperature.current / monitoringData.parameters.temperature.threshold) * 100}
                  className="h-1 mt-2"
                />
                <div className="flex items-center justify-between mt-1 text-xs text-gray-500">
                  <span>Min: {monitoringData.parameters.temperature.min}°C</span>
                  <span>Max: {monitoringData.parameters.temperature.max}°C</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Load</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-blue-600">{monitoringData.parameters.loadPercentage.current}%</div>
                  <Percent size={20} className="text-blue-600" />
                </div>
                <Progress
                  value={(monitoringData.parameters.loadPercentage.current / monitoringData.parameters.loadPercentage.threshold) * 100}
                  className="h-1 mt-2"
                />
                <div className="flex items-center justify-between mt-1 text-xs text-gray-500">
                  <span>Min: {monitoringData.parameters.loadPercentage.min}%</span>
                  <span>Max: {monitoringData.parameters.loadPercentage.max}%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Oil Level</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-cyan-600">{monitoringData.parameters.oilLevel.current}%</div>
                  <Droplet size={20} className="text-cyan-600" />
                </div>
                <Progress
                  value={monitoringData.parameters.oilLevel.current}
                  className="h-1 mt-2"
                />
                <div className="flex items-center justify-between mt-1 text-xs text-gray-500">
                  <span>Min: {monitoringData.parameters.oilLevel.min}%</span>
                  <span>Max: {monitoringData.parameters.oilLevel.max}%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Active Alerts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-red-600">{monitoringData.alerts.length}</div>
                  <AlertTriangle size={20} className="text-red-600" />
                </div>
                <div className="flex items-center justify-between mt-2 text-xs">
                  <span className="text-red-600">Critical: {monitoringData.overview.critical}</span>
                  <span className="text-amber-600">Warning: {monitoringData.overview.warning}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="parameters" className="mb-6">
            <TabsList>
              <TabsTrigger value="parameters">Parameters</TabsTrigger>
              <TabsTrigger value="alerts">Active Alerts</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
            </TabsList>

            <TabsContent value="parameters" className="mt-4">
              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Voltage Readings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">Primary Voltage</span>
                          <span className="text-sm font-medium">{monitoringData.parameters.voltage.primary.current} V</span>
                        </div>
                        <Progress
                          value={(monitoringData.parameters.voltage.primary.current / monitoringData.parameters.voltage.primary.threshold) * 100}
                          className="h-2"
                        />
                        <div className="flex items-center justify-between mt-1 text-xs text-gray-500">
                          <span>Min: {monitoringData.parameters.voltage.primary.min} V</span>
                          <span>Max: {monitoringData.parameters.voltage.primary.max} V</span>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">Secondary Voltage</span>
                          <span className="text-sm font-medium">{monitoringData.parameters.voltage.secondary.current} V</span>
                        </div>
                        <Progress
                          value={(monitoringData.parameters.voltage.secondary.current / monitoringData.parameters.voltage.secondary.threshold) * 100}
                          className="h-2"
                        />
                        <div className="flex items-center justify-between mt-1 text-xs text-gray-500">
                          <span>Min: {monitoringData.parameters.voltage.secondary.min} V</span>
                          <span>Max: {monitoringData.parameters.voltage.secondary.max} V</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Current Readings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">Primary Current</span>
                          <span className="text-sm font-medium">{monitoringData.parameters.current.primary.current} A</span>
                        </div>
                        <Progress
                          value={(monitoringData.parameters.current.primary.current / monitoringData.parameters.current.primary.threshold) * 100}
                          className="h-2"
                        />
                        <div className="flex items-center justify-between mt-1 text-xs text-gray-500">
                          <span>Min: {monitoringData.parameters.current.primary.min} A</span>
                          <span>Max: {monitoringData.parameters.current.primary.max} A</span>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">Secondary Current</span>
                          <span className="text-sm font-medium">{monitoringData.parameters.current.secondary.current} A</span>
                        </div>
                        <Progress
                          value={(monitoringData.parameters.current.secondary.current / monitoringData.parameters.current.secondary.threshold) * 100}
                          className="h-2"
                        />
                        <div className="flex items-center justify-between mt-1 text-xs text-gray-500">
                          <span>Min: {monitoringData.parameters.current.secondary.min} A</span>
                          <span>Max: {monitoringData.parameters.current.secondary.max} A</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="alerts" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Active Alerts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {monitoringData.alerts.map(alert => (
                      <div key={alert.id} className="border rounded-lg p-3 bg-red-50 border-red-200">
                        <div className="flex items-start">
                          <div className="mr-3 mt-1">
                            {getParameterIcon(alert.parameter)}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div className="font-medium">{alert.transformerName}</div>
                              {getSeverityBadge(alert.severity)}
                            </div>
                            <div className="text-sm text-gray-600 mt-1">
                              {alert.parameter === 'temperature' && `Temperature ${alert.value}°C exceeds threshold (${alert.threshold}°C)`}
                              {alert.parameter === 'loadPercentage' && `Load ${alert.value}% exceeds threshold (${alert.threshold}%)`}
                              {alert.parameter === 'oilLevel' && `Oil level ${alert.value}% below threshold (${alert.threshold}%)`}
                            </div>
                            <div className="flex items-center justify-between mt-2">
                              <div className="text-xs text-gray-500">
                                {new Date(alert.timestamp).toLocaleString()}
                              </div>
                              <Button variant="outline" size="sm">
                                Acknowledge
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="trends" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Parameter Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 bg-gray-50 rounded flex items-center justify-center">
                    <div className="text-center">
                      <LineChart size={48} className="mx-auto text-gray-300 mb-2" />
                      <p className="text-gray-500">Chart visualization would appear here</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </TransformerLayout>
    </MainLayout>
  )
}
