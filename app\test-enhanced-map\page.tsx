"use client"

import React from 'react'
import { MapboxMap } from '@/components/mapbox-map'
import { MapLocation } from '@/src/services/map-service'

// Sample transformer data with comprehensive information
const sampleTransformers: MapLocation[] = [
  {
    id: "transformer-001",
    title: "Addis Ababa Central T1",
    description: "Primary distribution transformer for central district",
    latitude: 9.0222,
    longitude: 38.7468,
    status: "operational",
    data: {
      id: "DT-AA-001",
      capacity: "500",
      manufacturer: "ABB",
      model: "DTCF-500",
      type: "Distribution",
      voltagePrimary: 15000,
      voltageSecondary: 400,
      healthIndex: 85,
      temperature: 65,
      loadFactor: 78,
      nextMaintenanceDate: "2024-03-15",
      location: {
        name: "Addis Ababa Central",
        region: "Addis Ababa",
        district: "Kirkos",
        serviceCenter: "Addis Ababa SC"
      }
    }
  },
  {
    id: "transformer-002",
    title: "Bahir Dar Industrial T2",
    description: "Industrial zone transformer",
    latitude: 11.5942,
    longitude: 37.3906,
    status: "warning",
    data: {
      id: "DT-BD-002",
      capacity: "1000",
      manufacturer: "Siemens",
      model: "DTCF-1000",
      type: "Power",
      voltagePrimary: 33000,
      voltageSecondary: 11000,
      healthIndex: 65,
      temperature: 85,
      loadFactor: 92,
      nextMaintenanceDate: "2024-02-20",
      location: {
        name: "Bahir Dar Industrial Zone",
        region: "Amhara",
        district: "Bahir Dar",
        serviceCenter: "Bahir Dar SC"
      }
    }
  },
  {
    id: "transformer-003",
    title: "Hawassa University T3",
    description: "University campus transformer",
    latitude: 7.0621,
    longitude: 38.4746,
    status: "maintenance",
    data: {
      id: "DT-HW-003",
      capacity: "315",
      manufacturer: "Schneider Electric",
      model: "DTCF-315",
      type: "Distribution",
      voltagePrimary: 11000,
      voltageSecondary: 400,
      healthIndex: 72,
      temperature: 70,
      loadFactor: 45,
      nextMaintenanceDate: "2024-01-30",
      location: {
        name: "Hawassa University",
        region: "Sidama",
        district: "Hawassa",
        serviceCenter: "Hawassa SC"
      }
    }
  },
  {
    id: "transformer-004",
    title: "Mekelle Hospital T4",
    description: "Critical hospital power supply",
    latitude: 13.4967,
    longitude: 39.4755,
    status: "critical",
    data: {
      id: "DT-MK-004",
      capacity: "800",
      manufacturer: "General Electric",
      model: "DTCF-800",
      type: "Power",
      voltagePrimary: 66000,
      voltageSecondary: 15000,
      healthIndex: 35,
      temperature: 95,
      loadFactor: 98,
      nextMaintenanceDate: "2024-01-15",
      location: {
        name: "Mekelle Hospital",
        region: "Tigray",
        district: "Mekelle",
        serviceCenter: "Mekelle SC"
      }
    }
  },
  {
    id: "transformer-005",
    title: "Jimma Coffee Plant T5",
    description: "Coffee processing facility transformer",
    latitude: 7.6773,
    longitude: 36.8344,
    status: "burnt",
    data: {
      id: "DT-JM-005",
      capacity: "630",
      manufacturer: "TBEA",
      model: "DTCF-630",
      type: "Industrial",
      voltagePrimary: 15000,
      voltageSecondary: 400,
      healthIndex: 15,
      temperature: 120,
      loadFactor: 0,
      nextMaintenanceDate: "2024-01-10",
      location: {
        name: "Jimma Coffee Processing Plant",
        region: "Oromia",
        district: "Jimma",
        serviceCenter: "Jimma SC"
      }
    }
  },
  {
    id: "transformer-006",
    title: "Dire Dawa Railway T6",
    description: "Railway station transformer",
    latitude: 9.5928,
    longitude: 41.8661,
    status: "offline",
    data: {
      id: "DT-DD-006",
      capacity: "400",
      manufacturer: "Hyundai",
      model: "DTCF-400",
      type: "Distribution",
      voltagePrimary: 11000,
      voltageSecondary: 400,
      healthIndex: 45,
      temperature: 0,
      loadFactor: 0,
      nextMaintenanceDate: "2024-02-01",
      location: {
        name: "Dire Dawa Railway Station",
        region: "Dire Dawa",
        district: "Dire Dawa",
        serviceCenter: "Dire Dawa SC"
      }
    }
  },
  {
    id: "transformer-007",
    title: "Gondar Castle T7",
    description: "Historical site transformer",
    latitude: 12.6116,
    longitude: 37.4669,
    status: "operational",
    data: {
      id: "DT-GD-007",
      capacity: "250",
      manufacturer: "Mitsubishi",
      model: "DTCF-250",
      type: "Distribution",
      voltagePrimary: 11000,
      voltageSecondary: 400,
      healthIndex: 90,
      temperature: 55,
      loadFactor: 35,
      nextMaintenanceDate: "2024-04-15",
      location: {
        name: "Gondar Castle Area",
        region: "Amhara",
        district: "Gondar",
        serviceCenter: "Bahir Dar SC"
      }
    }
  },
  {
    id: "transformer-008",
    title: "Adama Industrial T8",
    description: "Industrial park transformer",
    latitude: 8.5400,
    longitude: 39.2675,
    status: "warning",
    data: {
      id: "DT-AD-008",
      capacity: "1250",
      manufacturer: "Crompton Greaves",
      model: "DTCF-1250",
      type: "Power",
      voltagePrimary: 132000,
      voltageSecondary: 33000,
      healthIndex: 58,
      temperature: 88,
      loadFactor: 85,
      nextMaintenanceDate: "2024-02-10",
      location: {
        name: "Adama Industrial Park",
        region: "Oromia",
        district: "Adama",
        serviceCenter: "Addis Ababa SC"
      }
    }
  }
]

export default function TestEnhancedMapPage() {
  const handleMarkerClick = (location: MapLocation) => {
    console.log('Marker clicked:', location)
  }

  return (
    <div className="h-screen w-full">
      <div className="h-full relative">
        <MapboxMap
          locations={sampleTransformers}
          height="100vh"
          onMarkerClick={handleMarkerClick}
          mapStyle="satellite"
          clustered={true}
        />
        
        {/* Page Title Overlay */}
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-[500] bg-white/90 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg">
          <h1 className="text-lg font-bold text-gray-900">Enhanced Transformer Map</h1>
          <p className="text-sm text-gray-600">Advanced filtering, statistics, and map controls</p>
        </div>
      </div>
    </div>
  )
}
