/**
 * MySQL Notifications API Route
 * 
 * This API route handles notification data requests from MySQL database.
 */

import { NextRequest, NextResponse } from 'next/server';
import { MySQLServerService } from '@/src/lib/mysql-server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔔 API: Fetching notifications from MySQL...');
    
    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();
    
    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        { 
          error: 'MySQL connection failed',
          fallback: true 
        },
        { status: 503 }
      );
    }
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const isRead = searchParams.get('isRead');
    const priority = searchParams.get('priority');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    // Get notifications data
    const notifications = await MySQLServerService.getNotifications({
      userId,
      isRead: isRead ? isRead === 'true' : undefined,
      priority,
      limit,
      offset
    });
    
    console.log(`✅ API: ${notifications.length} notifications fetched successfully from MySQL`);
    
    return NextResponse.json({
      success: true,
      data: notifications,
      count: notifications.length,
      source: 'mysql'
    });
    
  } catch (error) {
    console.error('❌ API: Error fetching notifications:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch notifications',
        message: error instanceof Error ? error.message : 'Unknown error',
        fallback: true
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔔 API: Creating new notification...');
    
    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();
    
    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        { 
          error: 'MySQL connection failed',
          fallback: true 
        },
        { status: 503 }
      );
    }
    
    const notificationData = await request.json();
    
    // Create new notification
    const newNotification = await MySQLServerService.createNotification(notificationData);
    
    console.log('✅ API: Notification created successfully');
    
    return NextResponse.json({
      success: true,
      data: newNotification,
      source: 'mysql'
    });
    
  } catch (error) {
    console.error('❌ API: Error creating notification:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to create notification',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    console.log('🔔 API: Updating notification...');
    
    // Test MySQL connection first
    const isConnected = await MySQLServerService.testConnection();
    
    if (!isConnected) {
      console.warn('⚠️ API: MySQL connection failed');
      return NextResponse.json(
        { 
          error: 'MySQL connection failed',
          fallback: true 
        },
        { status: 503 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    const notificationId = searchParams.get('id');
    const updateData = await request.json();
    
    if (!notificationId) {
      return NextResponse.json(
        { error: 'Notification ID is required' },
        { status: 400 }
      );
    }
    
    // Update notification
    const updatedNotification = await MySQLServerService.updateNotification(notificationId, updateData);
    
    console.log('✅ API: Notification updated successfully');
    
    return NextResponse.json({
      success: true,
      data: updatedNotification,
      source: 'mysql'
    });
    
  } catch (error) {
    console.error('❌ API: Error updating notification:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to update notification',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
