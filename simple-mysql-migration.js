/**
 * Simple MySQL Migration Script
 *
 * This script creates a basic schema and migrates core data to MySQL dtms_eeu_db database.
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dtms_eeu_db'
};

let connection;

/**
 * Initialize MySQL connection
 */
async function initConnection() {
  try {
    connection = await mysql.createConnection(config);
    console.log('✅ MySQL connection established');
  } catch (error) {
    console.error('❌ Failed to connect to MySQL:', error.message);
    throw error;
  }
}

/**
 * Execute MySQL query
 */
async function executeQuery(query, params = []) {
  try {
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('❌ Query error:', error.message);
    console.error('Query:', query.substring(0, 100) + '...');
    throw error;
  }
}

/**
 * Create basic schema
 */
async function createBasicSchema() {
  console.log('🔄 Creating basic MySQL schema...');

  try {
    // Disable foreign key checks
    await executeQuery('SET FOREIGN_KEY_CHECKS = 0');

    // Drop tables if they exist
    const dropQueries = [
      'DROP TABLE IF EXISTS weather_alerts',
      'DROP TABLE IF EXISTS outages',
      'DROP TABLE IF EXISTS alerts',
      'DROP TABLE IF EXISTS maintenance_records',
      'DROP TABLE IF EXISTS transformers',
      'DROP TABLE IF EXISTS service_centers',
      'DROP TABLE IF EXISTS regions',
      'DROP TABLE IF EXISTS users'
    ];

    for (const query of dropQueries) {
      await executeQuery(query);
    }

    // Re-enable foreign key checks
    await executeQuery('SET FOREIGN_KEY_CHECKS = 1');

    // Create tables without foreign keys first
    await executeQuery(`
      CREATE TABLE users (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        salt VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        role ENUM('super_admin', 'national_asset_manager', 'regional_asset_manager', 'maintenance_manager', 'technician', 'viewer') NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        region_id VARCHAR(36) NULL,
        service_center_id VARCHAR(36) NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    await executeQuery(`
      CREATE TABLE regions (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(10) UNIQUE NOT NULL,
        description TEXT,
        coordinates_lat DECIMAL(10, 8) NOT NULL,
        coordinates_lng DECIMAL(11, 8) NOT NULL,
        population INT DEFAULT 0,
        area_km2 DECIMAL(10, 2) DEFAULT 0
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    await executeQuery(`
      CREATE TABLE service_centers (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(20) UNIQUE NOT NULL,
        region_id VARCHAR(36) NOT NULL,
        address TEXT,
        coordinates_lat DECIMAL(10, 8) NOT NULL,
        coordinates_lng DECIMAL(11, 8) NOT NULL,
        contact_phone VARCHAR(20),
        contact_email VARCHAR(255),
        manager_name VARCHAR(100),
        capacity INT DEFAULT 0
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    await executeQuery(`
      CREATE TABLE transformers (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        serial_number VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(100) NOT NULL,
        status ENUM('operational', 'warning', 'critical', 'offline', 'maintenance') NOT NULL DEFAULT 'operational',
        type VARCHAR(50) NOT NULL,
        manufacturer VARCHAR(100) NOT NULL,
        model VARCHAR(100) NOT NULL,
        manufacture_date DATE,
        installation_date DATE,
        last_maintenance_date DATE,
        next_maintenance_date DATE,
        capacity INT NOT NULL,
        voltage_primary DECIMAL(8, 2) NOT NULL,
        voltage_secondary DECIMAL(8, 2) NOT NULL,
        region_id VARCHAR(36) NOT NULL,
        service_center_id VARCHAR(36) NOT NULL,
        location_address TEXT,
        location_lat DECIMAL(10, 8) NOT NULL,
        location_lng DECIMAL(11, 8) NOT NULL,
        temperature DECIMAL(5, 2) DEFAULT 0,
        load_percentage DECIMAL(5, 2) DEFAULT 0,
        oil_level DECIMAL(5, 2) DEFAULT 0,
        health_index DECIMAL(5, 2) DEFAULT 0,
        tags JSON
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    await executeQuery(`
      CREATE TABLE maintenance_records (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        transformer_id VARCHAR(36) NOT NULL,
        type ENUM('preventive', 'corrective', 'emergency', 'inspection') NOT NULL,
        status ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'overdue') NOT NULL DEFAULT 'scheduled',
        priority ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
        scheduled_date DATE NOT NULL,
        completed_date DATE NULL,
        technician_id VARCHAR(36) NULL,
        description TEXT,
        work_performed TEXT,
        parts_used JSON,
        cost DECIMAL(10, 2) DEFAULT 0,
        notes TEXT
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    await executeQuery(`
      CREATE TABLE alerts (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        transformer_id VARCHAR(36) NULL,
        type VARCHAR(50) NOT NULL,
        severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        is_resolved BOOLEAN DEFAULT FALSE,
        resolved_at TIMESTAMP NULL,
        resolved_by VARCHAR(36) NULL,
        resolution_notes TEXT
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    await executeQuery(`
      CREATE TABLE outages (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        transformer_id VARCHAR(36) NULL,
        region_id VARCHAR(36) NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        status ENUM('active', 'resolved', 'investigating') NOT NULL DEFAULT 'active',
        severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        end_time TIMESTAMP NULL,
        affected_customers INT DEFAULT 0,
        cause VARCHAR(200),
        resolution TEXT
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    await executeQuery(`
      CREATE TABLE weather_alerts (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        region_id VARCHAR(36) NULL,
        type VARCHAR(50) NOT NULL,
        severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        end_time TIMESTAMP NULL,
        affected_areas JSON
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log('✅ Basic schema created');

  } catch (error) {
    console.error('❌ Schema creation failed:', error.message);
    throw error;
  }
}

/**
 * Generate sample data
 */
function generateSampleData() {
  console.log('📦 Generating sample data...');

  // Generate users
  const users = [
    {
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      email: '<EMAIL>',
      passwordHash: 'hashed_password_1',
      salt: 'salt_1',
      firstName: 'System',
      lastName: 'Administrator',
      role: 'super_admin',
      isActive: true,
      lastLogin: null,
      regionId: null,
      serviceCenterId: null
    },
    {
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      email: '<EMAIL>',
      passwordHash: 'hashed_password_2',
      salt: 'salt_2',
      firstName: 'Asset',
      lastName: 'Manager',
      role: 'national_asset_manager',
      isActive: true,
      lastLogin: null,
      regionId: null,
      serviceCenterId: null
    }
  ];

  // Generate regions
  const regions = [
    {
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      name: 'Addis Ababa',
      code: 'AA',
      description: 'Capital city region',
      coordinates: { lat: 9.0320, lng: 38.7469 },
      population: 3500000,
      areaKm2: 527.0
    },
    {
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      name: 'Oromia',
      code: 'OR',
      description: 'Largest region by area',
      coordinates: { lat: 8.5000, lng: 39.5000 },
      population: 35000000,
      areaKm2: 353006.0
    }
  ];

  // Generate service centers
  const serviceCenters = [
    {
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      name: 'Addis Ababa Central Service Center',
      code: 'AA-CENTRAL',
      regionId: regions[0].id,
      address: 'Bole, Addis Ababa',
      coordinates: { lat: 9.0320, lng: 38.7469 },
      contactPhone: '+251-11-123-4567',
      contactEmail: '<EMAIL>',
      managerName: 'Alemayehu Tadesse',
      capacity: 100
    },
    {
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      name: 'Oromia Regional Service Center',
      code: 'OR-REGIONAL',
      regionId: regions[1].id,
      address: 'Adama, Oromia',
      coordinates: { lat: 8.5400, lng: 39.2675 },
      contactPhone: '+251-22-123-4567',
      contactEmail: '<EMAIL>',
      managerName: 'Bekele Megersa',
      capacity: 150
    }
  ];

  // Generate transformers
  const transformers = [];
  for (let i = 0; i < 10; i++) {
    const region = regions[i % regions.length];
    const serviceCenter = serviceCenters.find(sc => sc.regionId === region.id);

    transformers.push({
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      serialNumber: `EEU-${region.code}-${1000 + i}`,
      name: `${region.name} Transformer ${i + 1}`,
      status: ['operational', 'warning', 'critical'][i % 3],
      type: 'Distribution',
      manufacturer: 'ABB',
      model: `Model-${100 + i}`,
      manufactureDate: '2020-01-01',
      installationDate: '2021-01-01',
      lastMaintenanceDate: '2024-01-01',
      nextMaintenanceDate: '2024-12-01',
      capacity: 250,
      voltage: { primary: 15, secondary: 400 },
      regionId: region.id,
      serviceCenterId: serviceCenter.id,
      location: {
        address: `${region.name}, Ethiopia`,
        coordinates: { lat: region.coordinates.lat + (Math.random() - 0.5) * 0.1, lng: region.coordinates.lng + (Math.random() - 0.5) * 0.1 }
      },
      metrics: {
        temperature: 45 + Math.random() * 20,
        loadPercentage: 60 + Math.random() * 30,
        oilLevel: 80 + Math.random() * 20,
        healthIndex: 70 + Math.random() * 30
      },
      tags: []
    });
  }

  return { users, regions, serviceCenters, transformers };
}

/**
 * Migrate data to MySQL
 */
async function migrateData(data) {
  console.log('🔄 Migrating data to MySQL...');

  // Migrate users
  for (const user of data.users) {
    await executeQuery(`
      INSERT INTO users (
        id, created_at, updated_at, email, password_hash, salt,
        first_name, last_name, role, is_active, last_login,
        region_id, service_center_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      user.id, user.createdAt, user.updatedAt, user.email,
      user.passwordHash, user.salt, user.firstName, user.lastName,
      user.role, user.isActive, user.lastLogin, user.regionId, user.serviceCenterId
    ]);
  }
  console.log(`✅ Migrated ${data.users.length} users`);

  // Migrate regions
  for (const region of data.regions) {
    await executeQuery(`
      INSERT INTO regions (
        id, created_at, updated_at, name, code, description,
        coordinates_lat, coordinates_lng, population, area_km2
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      region.id, region.createdAt, region.updatedAt, region.name,
      region.code, region.description, region.coordinates.lat, region.coordinates.lng,
      region.population, region.areaKm2
    ]);
  }
  console.log(`✅ Migrated ${data.regions.length} regions`);

  // Migrate service centers
  for (const center of data.serviceCenters) {
    await executeQuery(`
      INSERT INTO service_centers (
        id, created_at, updated_at, name, code, region_id,
        address, coordinates_lat, coordinates_lng, contact_phone,
        contact_email, manager_name, capacity
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      center.id, center.createdAt, center.updatedAt, center.name,
      center.code, center.regionId, center.address, center.coordinates.lat,
      center.coordinates.lng, center.contactPhone, center.contactEmail,
      center.managerName, center.capacity
    ]);
  }
  console.log(`✅ Migrated ${data.serviceCenters.length} service centers`);

  // Migrate transformers
  for (const transformer of data.transformers) {
    await executeQuery(`
      INSERT INTO transformers (
        id, created_at, updated_at, serial_number, name, status,
        type, manufacturer, model, manufacture_date, installation_date,
        last_maintenance_date, next_maintenance_date, capacity,
        voltage_primary, voltage_secondary, region_id, service_center_id,
        location_address, location_lat, location_lng, temperature,
        load_percentage, oil_level, health_index, tags
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      transformer.id, transformer.createdAt, transformer.updatedAt,
      transformer.serialNumber, transformer.name, transformer.status,
      transformer.type, transformer.manufacturer, transformer.model,
      transformer.manufactureDate, transformer.installationDate,
      transformer.lastMaintenanceDate, transformer.nextMaintenanceDate,
      transformer.capacity, transformer.voltage.primary, transformer.voltage.secondary,
      transformer.regionId, transformer.serviceCenterId, transformer.location.address,
      transformer.location.coordinates.lat, transformer.location.coordinates.lng,
      transformer.metrics.temperature, transformer.metrics.loadPercentage,
      transformer.metrics.oilLevel, transformer.metrics.healthIndex,
      JSON.stringify(transformer.tags)
    ]);
  }
  console.log(`✅ Migrated ${data.transformers.length} transformers`);
}

/**
 * Main migration function
 */
async function runMigration() {
  console.log('🚀 Starting Simple MySQL Migration to dtms_eeu_db');
  console.log('=================================================');

  const startTime = Date.now();

  try {
    // Step 1: Initialize connection
    await initConnection();

    // Step 2: Create schema
    await createBasicSchema();

    // Step 3: Generate sample data
    const data = generateSampleData();

    // Step 4: Migrate data
    await migrateData(data);

    // Step 5: Verify migration
    console.log('🔍 Verifying migration...');

    const counts = {
      users: (await executeQuery('SELECT COUNT(*) as count FROM users'))[0].count,
      regions: (await executeQuery('SELECT COUNT(*) as count FROM regions'))[0].count,
      service_centers: (await executeQuery('SELECT COUNT(*) as count FROM service_centers'))[0].count,
      transformers: (await executeQuery('SELECT COUNT(*) as count FROM transformers'))[0].count
    };

    console.log('📊 Migration Results:');
    console.log(`   Users: ${counts.users}`);
    console.log(`   Regions: ${counts.regions}`);
    console.log(`   Service Centers: ${counts.service_centers}`);
    console.log(`   Transformers: ${counts.transformers}`);

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`⏱️  Migration completed in ${duration.toFixed(2)} seconds`);
    console.log('🎉 Migration successful!');
    console.log('');
    console.log('✅ Database dtms_eeu_db is ready with sample data');
    console.log('✅ You can now connect your application to MySQL');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run migration
runMigration().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
