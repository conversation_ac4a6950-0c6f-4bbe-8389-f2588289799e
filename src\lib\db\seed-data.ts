/**
 * Seed data for the JSON database
 *
 * This file contains initial data to populate the database with realistic
 * sample data for development and testing purposes.
 *
 * Updated to include comprehensive transformer data migrated from mock files.
 */

import { v4 as uuidv4 } from 'uuid';
import {
  DatabaseSchema,
  User,
  Region,
  ServiceCenter,
  Transformer,
  MaintenanceRecord,
  Alert,
  Outage,
  WeatherAlert
} from './schema';

// Import mock data for migration
import { mockTransformers, mockServiceCenters, mockSwitchgearTeams } from '../../services/mock-transformers';

// Helper function to generate a random date within a range
function randomDate(start: Date, end: Date): string {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString();
}

// Helper function to generate a random number within a range
function randomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Helper function to pick a random item from an array
function randomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// Helper function to convert mock transformer to database transformer
function convertMockTransformerToDb(mockTransformer: any, regionId: string, serviceCenterId: string): Transformer {
  const now = new Date().toISOString();

  // Map status from mock format to database format
  const statusMap: Record<string, 'operational' | 'warning' | 'critical' | 'offline' | 'maintenance'> = {
    'Operational': 'operational',
    'Warning': 'warning',
    'Critical': 'critical',
    'Maintenance': 'maintenance',
    'Burnt': 'offline'
  };

  return {
    id: uuidv4(),
    createdAt: now,
    updatedAt: now,
    serialNumber: mockTransformer.serialNumber,
    name: `${mockTransformer.location.region} ${mockTransformer.type} Transformer`,
    status: statusMap[mockTransformer.status] || 'operational',
    type: mockTransformer.type,
    manufacturer: mockTransformer.manufacturer,
    model: mockTransformer.model,
    manufactureDate: mockTransformer.manufacturingYear ? `${mockTransformer.manufacturingYear}-01-01` : '2020-01-01',
    installationDate: mockTransformer.installationDate,
    lastMaintenanceDate: mockTransformer.lastMaintenanceDate,
    nextMaintenanceDate: mockTransformer.maintenanceHistory?.[0]?.nextScheduledDate,
    capacity: parseInt(mockTransformer.capacity) || 250,
    voltage: {
      primary: parseFloat(mockTransformer.primaryVoltage?.replace(' kV', '')) || 15,
      secondary: parseFloat(mockTransformer.secondaryVoltage?.replace(' kV', '')) * 1000 || 400
    },
    regionId,
    serviceCenterId,
    location: {
      address: mockTransformer.location.address,
      coordinates: {
        lat: parseFloat(mockTransformer.location.latitude),
        lng: parseFloat(mockTransformer.location.longitude)
      }
    },
    metrics: {
      temperature: randomNumber(30, 70),
      loadPercentage: randomNumber(40, 90),
      oilLevel: randomNumber(80, 100),
      healthIndex: mockTransformer.status === 'Operational' ? randomNumber(80, 100) :
                   mockTransformer.status === 'Warning' ? randomNumber(60, 79) :
                   mockTransformer.status === 'Critical' ? randomNumber(30, 59) :
                   randomNumber(10, 40)
    },
    tags: []
  };
}

// Generate seed data
export function generateSeedData(): Partial<DatabaseSchema> {
  const now = new Date();
  const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());

  // Create regions
  const regions: Region[] = [
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Addis Ababa',
      code: 'addis',
      serviceCenters: 4,
      transformers: 10,
      coordinates: { lat: 9.0222, lng: 38.7468 }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Oromia',
      code: 'oromia',
      serviceCenters: 6,
      transformers: 8,
      coordinates: { lat: 8.5547, lng: 39.2700 }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Amhara',
      code: 'amhara',
      serviceCenters: 5,
      transformers: 6,
      coordinates: { lat: 11.5000, lng: 38.0000 }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Tigray',
      code: 'tigray',
      serviceCenters: 3,
      transformers: 4,
      coordinates: { lat: 14.0000, lng: 38.8333 }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'SNNPR',
      code: 'snnpr',
      serviceCenters: 4,
      transformers: 4,
      coordinates: { lat: 7.0000, lng: 38.0000 }
    }
  ];

  // Create service centers
  const serviceCenters: ServiceCenter[] = [];

  // Addis Ababa service centers
  serviceCenters.push(
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Bole Service Center',
      code: 'bole',
      regionId: regions[0].id,
      address: 'Bole Road, Addis Ababa',
      phone: '+251-111-234567',
      email: '<EMAIL>',
      manager: 'Abebe Kebede',
      staff: 12,
      transformers: 3,
      coordinates: { lat: 8.9806, lng: 38.7578 }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Kirkos Service Center',
      code: 'kirkos',
      regionId: regions[0].id,
      address: 'Kirkos Sub-city, Addis Ababa',
      phone: '+251-111-345678',
      email: '<EMAIL>',
      manager: 'Tigist Haile',
      staff: 10,
      transformers: 2,
      coordinates: { lat: 9.0105, lng: 38.7612 }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Arada Service Center',
      code: 'arada',
      regionId: regions[0].id,
      address: 'Arada Sub-city, Addis Ababa',
      phone: '+251-111-456789',
      email: '<EMAIL>',
      manager: 'Solomon Tesfaye',
      staff: 8,
      transformers: 3,
      coordinates: { lat: 9.0300, lng: 38.7500 }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Yeka Service Center',
      code: 'yeka',
      regionId: regions[0].id,
      address: 'Yeka Sub-city, Addis Ababa',
      phone: '+251-111-567890',
      email: '<EMAIL>',
      manager: 'Hiwot Mengistu',
      staff: 9,
      transformers: 2,
      coordinates: { lat: 9.0500, lng: 38.8000 }
    }
  );

  // Oromia service centers
  serviceCenters.push(
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Adama Service Center',
      code: 'adama',
      regionId: regions[1].id,
      address: 'Adama, Oromia',
      phone: '+251-221-123456',
      email: '<EMAIL>',
      manager: 'Gemechu Tadesse',
      staff: 7,
      transformers: 2,
      coordinates: { lat: 8.5400, lng: 39.2700 }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Bishoftu Service Center',
      code: 'bishoftu',
      regionId: regions[1].id,
      address: 'Bishoftu, Oromia',
      phone: '+251-221-234567',
      email: '<EMAIL>',
      manager: 'Chaltu Negash',
      staff: 6,
      transformers: 1,
      coordinates: { lat: 8.7500, lng: 38.9800 }
    }
  );

  // Amhara service centers
  serviceCenters.push(
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Bahir Dar Service Center',
      code: 'bahirdar',
      regionId: regions[2].id,
      address: 'Bahir Dar, Amhara',
      phone: '+251-331-123456',
      email: '<EMAIL>',
      manager: 'Yohannes Alemu',
      staff: 8,
      transformers: 2,
      coordinates: { lat: 11.5936, lng: 37.3908 }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Gondar Service Center',
      code: 'gondar',
      regionId: regions[2].id,
      address: 'Gondar, Amhara',
      phone: '+251-331-234567',
      email: '<EMAIL>',
      manager: 'Meseret Ayele',
      staff: 6,
      transformers: 1,
      coordinates: { lat: 12.6030, lng: 37.4521 }
    }
  );

  // Tigray service center
  serviceCenters.push(
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Mekelle Service Center',
      code: 'mekelle',
      regionId: regions[3].id,
      address: 'Mekelle, Tigray',
      phone: '+251-441-123456',
      email: '<EMAIL>',
      manager: 'Teklay Gebre',
      staff: 7,
      transformers: 2,
      coordinates: { lat: 13.4967, lng: 39.4697 }
    }
  );

  // SNNPR service center
  serviceCenters.push(
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      name: 'Hawassa Service Center',
      code: 'hawassa',
      regionId: regions[4].id,
      address: 'Hawassa, SNNPR',
      phone: '+251-551-123456',
      email: '<EMAIL>',
      manager: 'Dawit Mulugeta',
      staff: 6,
      transformers: 2,
      coordinates: { lat: 7.0500, lng: 38.4800 }
    }
  );

  // Create users
  const users: User[] = [
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'super_admin',
      isActive: true,
      lastLogin: randomDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), now),
      preferences: {
        theme: 'system',
        language: 'en',
        notifications: true
      }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      email: '<EMAIL>',
      name: 'Bekele Tadesse',
      role: 'national_asset_manager',
      isActive: true,
      lastLogin: randomDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), now),
      preferences: {
        theme: 'light',
        language: 'en',
        notifications: true
      }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      email: '<EMAIL>',
      name: 'Hanna Girma',
      role: 'national_maintenance_manager',
      isActive: true,
      lastLogin: randomDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), now),
      preferences: {
        theme: 'dark',
        language: 'en',
        notifications: true
      }
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      email: '<EMAIL>',
      name: 'Yonas Hailu',
      role: 'regional_admin',
      region: 'addis',
      isActive: true,
      lastLogin: randomDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), now)
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      email: '<EMAIL>',
      name: 'Fatuma Ahmed',
      role: 'regional_admin',
      region: 'oromia',
      isActive: true,
      lastLogin: randomDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), now)
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      email: '<EMAIL>',
      name: 'Abebe Kebede',
      role: 'service_center_manager',
      region: 'addis',
      serviceCenter: 'bole',
      isActive: true,
      lastLogin: randomDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), now)
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      email: '<EMAIL>',
      name: 'Tadesse Bekele',
      role: 'field_technician',
      region: 'addis',
      serviceCenter: 'bole',
      isActive: true,
      lastLogin: randomDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), now)
    },
    {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      email: '<EMAIL>',
      name: 'Sara Tesfaye',
      role: 'customer_service_agent',
      isActive: true,
      lastLogin: randomDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), now)
    }
  ];

  // Create transformers from mock data
  const transformers: Transformer[] = [];

  // Convert mock transformers to database format
  for (const mockTransformer of mockTransformers) {
    // Find matching region
    const region = regions.find(r => r.name === mockTransformer.location.region);
    if (!region) continue;

    // Find matching service center
    const serviceCenter = serviceCenters.find(sc =>
      sc.regionId === region.id &&
      sc.name.toLowerCase().includes(mockTransformer.location.serviceCenter?.toLowerCase() || '')
    ) || serviceCenters.find(sc => sc.regionId === region.id);

    if (!serviceCenter) continue;

    // Convert mock transformer to database format
    const transformer = convertMockTransformerToDb(mockTransformer, region.id, serviceCenter.id);

    // Add tags based on status and metrics
    if (transformer.status === 'warning') {
      transformer.tags.push('needs-inspection');
    } else if (transformer.status === 'critical') {
      transformer.tags.push('urgent-maintenance');
    } else if (transformer.status === 'maintenance') {
      transformer.tags.push('scheduled-maintenance');
    }

    // Add additional tags
    if (transformer.metrics.loadPercentage > 80) {
      transformer.tags.push('high-load');
    }
    if (transformer.metrics.temperature > 60) {
      transformer.tags.push('high-temperature');
    }
    if (new Date(transformer.installationDate).getFullYear() < 2018) {
      transformer.tags.push('aging');
    }

    transformers.push(transformer);
  }

  // Generate additional transformers to reach 32 total
  const additionalCount = Math.max(0, 32 - transformers.length);
  for (let i = 0; i < additionalCount; i++) {
    const regionIndex = i % regions.length;
    const region = regions[regionIndex];

    // Find service centers for this region
    const regionServiceCenters = serviceCenters.filter(sc => sc.regionId === region.id);
    const serviceCenter = regionServiceCenters[i % regionServiceCenters.length];

    // Generate random coordinates near the service center
    const latOffset = (Math.random() - 0.5) * 0.1;
    const lngOffset = (Math.random() - 0.5) * 0.1;
    const coordinates = {
      lat: serviceCenter.coordinates.lat + latOffset,
      lng: serviceCenter.coordinates.lng + lngOffset
    };

    // Generate random metrics
    const healthIndex = Math.random() * 100;
    const status = healthIndex > 90 ? 'operational' :
                  healthIndex > 70 ? 'operational' :
                  healthIndex > 50 ? 'warning' :
                  healthIndex > 30 ? 'critical' :
                  Math.random() > 0.5 ? 'offline' : 'maintenance';

    const transformer: Transformer = {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      serialNumber: `EEU-${region.code.toUpperCase()}-${20000 + i}`,
      name: `${region.name} Transformer ${transformers.length + i + 1}`,
      status: status as any,
      type: randomItem(['Distribution', 'Power', 'Pad-mounted']),
      manufacturer: randomItem(['ABB', 'Siemens', 'Schneider Electric', 'General Electric']),
      model: `Model-${randomNumber(100, 999)}`,
      manufactureDate: randomDate(new Date(2010, 0, 1), new Date(2020, 0, 1)),
      installationDate: randomDate(new Date(2015, 0, 1), new Date(2022, 0, 1)),
      lastMaintenanceDate: randomDate(new Date(2022, 0, 1), now),
      nextMaintenanceDate: randomDate(now, new Date(now.getFullYear() + 1, now.getMonth(), now.getDate())),
      capacity: randomItem([100, 250, 500, 1000]),
      voltage: {
        primary: randomItem([11, 15, 33]),
        secondary: randomItem([400, 415])
      },
      regionId: region.id,
      serviceCenterId: serviceCenter.id,
      location: {
        address: `${region.name}, Ethiopia`,
        coordinates
      },
      metrics: {
        temperature: randomNumber(30, 70),
        loadPercentage: randomNumber(30, 90),
        oilLevel: randomNumber(70, 100),
        healthIndex
      },
      tags: []
    };

    // Add tags based on status
    if (transformer.status === 'warning') {
      transformer.tags.push('needs-inspection');
    } else if (transformer.status === 'critical') {
      transformer.tags.push('urgent-maintenance');
    } else if (transformer.status === 'maintenance') {
      transformer.tags.push('scheduled-maintenance');
    }

    transformers.push(transformer);
  }

  // Create maintenance records
  const maintenanceRecords: MaintenanceRecord[] = [];
  const maintenanceTypes: Array<'preventive' | 'corrective' | 'emergency' | 'inspection'> = [
    'preventive', 'corrective', 'emergency', 'inspection'
  ];
  const maintenanceStatuses: Array<'scheduled' | 'in-progress' | 'completed' | 'cancelled' | 'overdue'> = [
    'scheduled', 'in-progress', 'completed', 'cancelled', 'overdue'
  ];

  // Generate maintenance records for some transformers
  for (let i = 0; i < 20; i++) {
    const transformer = randomItem(transformers);
    const type = randomItem(maintenanceTypes);
    const status = randomItem(maintenanceStatuses);
    const scheduledDate = randomDate(new Date(now.getFullYear() - 1, now.getMonth(), now.getDate()),
                                    new Date(now.getFullYear() + 1, now.getMonth(), now.getDate()));

    const maintenanceRecord: MaintenanceRecord = {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      transformerId: transformer.id,
      type,
      status,
      title: `${type.charAt(0).toUpperCase() + type.slice(1)} maintenance for ${transformer.name}`,
      description: `${type.charAt(0).toUpperCase() + type.slice(1)} maintenance task for transformer ${transformer.serialNumber}`,
      scheduledDate,
      assignedTo: randomItem(users.filter(u => u.role === 'field_technician').map(u => u.id)),
      reportedBy: randomItem(users.filter(u => ['service_center_manager', 'regional_maintenance_engineer'].includes(u.role)).map(u => u.id)),
      priority: randomItem(['low', 'medium', 'high', 'critical']),
      estimatedDuration: randomNumber(1, 8),
      notes: 'Regular maintenance as per schedule.'
    };

    // Add completed date for completed maintenance
    if (status === 'completed') {
      maintenanceRecord.completedDate = randomDate(new Date(scheduledDate), now);
      maintenanceRecord.actualDuration = randomNumber(1, 10);
      maintenanceRecord.cost = randomNumber(1000, 5000);
    }

    maintenanceRecords.push(maintenanceRecord);
  }

  // Create alerts
  const alerts: Alert[] = [];
  const alertTypes = ['Temperature', 'Oil Level', 'Load', 'Voltage', 'Current', 'Connection', 'Physical'];
  const alertSeverities: Array<'low' | 'medium' | 'high' | 'critical'> = ['low', 'medium', 'high', 'critical'];

  // Generate alerts for some transformers
  for (let i = 0; i < 15; i++) {
    const transformer = randomItem(transformers);
    const type = randomItem(alertTypes);
    const severity = randomItem(alertSeverities);

    const alert: Alert = {
      id: uuidv4(),
      createdAt: randomDate(new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), now),
      updatedAt: randomDate(new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), now),
      transformerId: transformer.id,
      title: `${severity.charAt(0).toUpperCase() + severity.slice(1)} ${type} Alert`,
      message: `${severity.charAt(0).toUpperCase() + severity.slice(1)} ${type.toLowerCase()} alert detected for transformer ${transformer.serialNumber}`,
      severity,
      type,
      isRead: Math.random() > 0.5,
      isResolved: Math.random() > 0.7,
      metadata: {
        value: type === 'Temperature' ? randomNumber(60, 90) :
              type === 'Oil Level' ? randomNumber(30, 60) :
              type === 'Load' ? randomNumber(85, 100) :
              randomNumber(1, 100)
      }
    };

    // Add resolved info for resolved alerts
    if (alert.isResolved) {
      alert.resolvedAt = randomDate(new Date(alert.createdAt), now);
      alert.resolvedBy = randomItem(users.map(u => u.id));
    }

    alerts.push(alert);
  }

  // Create outages
  const outages: Outage[] = [];
  const outageStatuses: Array<'active' | 'scheduled' | 'resolved'> = ['active', 'scheduled', 'resolved'];
  const outageCauses = ['Equipment Failure', 'Weather', 'Planned Maintenance', 'External Damage', 'Unknown'];

  // Generate outages for some transformers
  for (let i = 0; i < 10; i++) {
    const transformer = randomItem(transformers);
    const status = randomItem(outageStatuses);
    const cause = randomItem(outageCauses);
    const startTime = randomDate(new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000), now);

    const outage: Outage = {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      transformerId: transformer.id,
      status,
      startTime,
      cause,
      affectedCustomers: randomNumber(50, 500),
      description: `${status === 'scheduled' ? 'Planned' : 'Unplanned'} outage due to ${cause.toLowerCase()}`,
      reportedBy: randomItem(users.map(u => u.id)),
      notes: status === 'scheduled' ? 'Customers have been notified in advance.' : 'Emergency response team dispatched.'
    };

    // Add end time for resolved outages
    if (status === 'resolved') {
      const durationHours = randomNumber(1, 24);
      outage.endTime = new Date(new Date(startTime).getTime() + durationHours * 60 * 60 * 1000).toISOString();
      outage.resolvedBy = randomItem(users.map(u => u.id));
    }

    outages.push(outage);
  }

  // Create weather alerts
  const weatherAlerts: WeatherAlert[] = [];
  const weatherTypes = ['Heavy Rain', 'Thunderstorm', 'High Winds', 'Flooding', 'Extreme Heat'];

  // Generate weather alerts for some regions
  for (let i = 0; i < 5; i++) {
    const region = randomItem(regions);
    const type = randomItem(weatherTypes);
    const severity = randomItem(alertSeverities);
    const startTime = randomDate(now, new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000));
    const endTime = new Date(new Date(startTime).getTime() + randomNumber(6, 48) * 60 * 60 * 1000).toISOString();

    // Find transformers in this region
    const regionTransformers = transformers.filter(t => t.regionId === region.id);
    const affectedTransformers = regionTransformers
      .slice(0, randomNumber(1, regionTransformers.length))
      .map(t => t.id);

    const weatherAlert: WeatherAlert = {
      id: uuidv4(),
      createdAt: randomDate(oneYearAgo, now),
      updatedAt: randomDate(oneYearAgo, now),
      regionId: region.id,
      type,
      severity,
      title: `${severity.charAt(0).toUpperCase() + severity.slice(1)} ${type} Warning`,
      description: `${severity.charAt(0).toUpperCase() + severity.slice(1)} ${type.toLowerCase()} expected in ${region.name} region from ${new Date(startTime).toLocaleString()} to ${new Date(endTime).toLocaleString()}`,
      startTime,
      endTime,
      affectedTransformers,
      source: 'Ethiopian Meteorological Service',
      coordinates: {
        lat: region.coordinates.lat + (Math.random() - 0.5) * 0.2,
        lng: region.coordinates.lng + (Math.random() - 0.5) * 0.2
      },
      isActive: new Date(startTime) <= now && new Date(endTime) >= now
    };

    weatherAlerts.push(weatherAlert);
  }

  return {
    users,
    regions,
    serviceCenters,
    transformers,
    maintenanceRecords,
    alerts,
    outages,
    weatherAlerts
  };
}
