"use client"

import { create<PERSON>ontext, useContext, useState, useEffect, ReactNode } from "react"
import { enhancedMaintenanceService } from "@/src/services/enhanced-maintenance-service"
import { useTransformers } from "@/src/contexts/transformer-context"
import { useToast } from "@/src/components/ui/use-toast"
import { useSearchParams } from "next/navigation"
import type { ExtendedMaintenanceRecord } from "@/src/services/maintenance-service"
import type { MapLocation } from "@/src/services/map-service"

interface MaintenanceContextType {
  // Maintenance data
  allRecords: ExtendedMaintenanceRecord[]
  upcomingRecords: ExtendedMaintenanceRecord[]
  inProgressRecords: ExtendedMaintenanceRecord[]
  completedRecords: ExtendedMaintenanceRecord[]
  filteredRecords: ExtendedMaintenanceRecord[]
  maintenanceLocations: MapLocation[]
  maintenanceStatistics: any

  // Loading states
  isLoading: boolean
  isMapLoading: boolean

  // Filters
  filters: {
    status: string[]
    type: string
    priority: string[]
    region: string[]
    assignedTo: string[]
    search: string
    dateRange: [Date | null, Date | null]
  }

  // Filter options
  filterOptions: {
    types: string[]
    priorities: string[]
    regions: string[]
    technicians: string[]
  }

  // Actions
  updateFilter: (key: string, value: any) => void
  resetFilters: () => void
  refreshData: () => void
  scheduleNewMaintenance: (data: Omit<ExtendedMaintenanceRecord, 'id'>) => Promise<ExtendedMaintenanceRecord>
  updateMaintenanceRecord: (id: string, updates: Partial<ExtendedMaintenanceRecord>) => Promise<void>
  completeMaintenanceRecord: (id: string) => Promise<void>
  cancelMaintenanceRecord: (id: string) => Promise<void>
  getMaintenanceRecordsByTransformerId: (id: string) => Promise<ExtendedMaintenanceRecord[]>
}

const defaultFilters = {
  status: [],
  type: "all",
  priority: [],
  region: [],
  assignedTo: [],
  search: "",
  dateRange: [null, null] as [Date | null, Date | null]
}

const MaintenanceContext = createContext<MaintenanceContextType | undefined>(undefined)

export function MaintenanceProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast()
  const { transformers } = useTransformers()
  const searchParams = useSearchParams()

  // State
  const [allRecords, setAllRecords] = useState<ExtendedMaintenanceRecord[]>([])
  const [upcomingRecords, setUpcomingRecords] = useState<ExtendedMaintenanceRecord[]>([])
  const [inProgressRecords, setInProgressRecords] = useState<ExtendedMaintenanceRecord[]>([])
  const [completedRecords, setCompletedRecords] = useState<ExtendedMaintenanceRecord[]>([])
  const [filteredRecords, setFilteredRecords] = useState<ExtendedMaintenanceRecord[]>([])
  const [maintenanceLocations, setMaintenanceLocations] = useState<MapLocation[]>([])
  const [maintenanceStatistics, setMaintenanceStatistics] = useState<any>({})

  // Loading states
  const [isLoading, setIsLoading] = useState(true)
  const [isMapLoading, setIsMapLoading] = useState(true)

  // Filters
  const [filters, setFilters] = useState(defaultFilters)

  // Filter options
  const [filterOptions, setFilterOptions] = useState({
    types: [] as string[],
    priorities: ["high", "medium", "low"],
    regions: [] as string[],
    technicians: [] as string[]
  })

  // Check for transformer ID in URL params
  useEffect(() => {
    const transformerId = searchParams.get('transformer')
    if (transformerId) {
      // If transformer ID is provided, filter maintenance records for this transformer
      loadMaintenanceForTransformer(transformerId)
    } else {
      // Otherwise load all maintenance records
      loadInitialData()
    }

    // Disable real-time updates for now to improve performance
    const removeListener = () => {}

    return () => {
      removeListener()
      enhancedMaintenanceService.stopRealTimeUpdates()
    }
  }, [searchParams])

  // Apply filters when they change
  useEffect(() => {
    applyFilters()
  }, [filters, allRecords])

  // Load maintenance records for a specific transformer
  const loadMaintenanceForTransformer = async (transformerId: string) => {
    setIsLoading(true)

    try {
      const records = await enhancedMaintenanceService.getMaintenanceRecordsByTransformerIdEnhanced(transformerId)
      setAllRecords(records)
      setFilteredRecords(records)

      // Set other record categories
      setUpcomingRecords(records.filter(r => r.status === "Scheduled"))
      setInProgressRecords(records.filter(r => r.status === "In Progress"))
      setCompletedRecords(records.filter(r => r.status === "Completed"))

      // Load map locations
      await loadMapLocations()

      // Load statistics
      setMaintenanceStatistics(enhancedMaintenanceService.getMaintenanceStatisticsEnhanced())

      // Extract filter options
      extractFilterOptions(records)
    } catch (error) {
      console.error("Error loading maintenance for transformer:", error)
      toast({
        title: "Error",
        description: "Failed to load maintenance records for this transformer.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Load all initial data
  const loadInitialData = async () => {
    setIsLoading(true)
    setIsMapLoading(true)

    try {
      // Load all maintenance records
      const all = await enhancedMaintenanceService.getAllMaintenanceRecordsEnhanced()
      setAllRecords(all)
      setFilteredRecords(all)

      // Load records by category
      const upcoming = await enhancedMaintenanceService.getUpcomingMaintenanceRecordsEnhanced()
      const inProgress = await enhancedMaintenanceService.getInProgressMaintenanceRecordsEnhanced()
      const completed = await enhancedMaintenanceService.getCompletedMaintenanceRecordsEnhanced()

      setUpcomingRecords(upcoming)
      setInProgressRecords(inProgress)
      setCompletedRecords(completed)

      // Load map locations
      await loadMapLocations()

      // Load statistics
      setMaintenanceStatistics(enhancedMaintenanceService.getMaintenanceStatisticsEnhanced())

      // Extract filter options
      extractFilterOptions(all)
    } catch (error) {
      console.error("Error loading maintenance data:", error)
      toast({
        title: "Error",
        description: "Failed to load maintenance data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
      setIsMapLoading(false)
    }
  }

  // Load map locations for maintenance records
  const loadMapLocations = async () => {
    setIsMapLoading(true)
    try {
      const locations = await enhancedMaintenanceService.getMaintenanceMapLocations()
      setMaintenanceLocations(locations)
    } catch (error) {
      console.error("Error loading maintenance map locations:", error)
    } finally {
      setIsMapLoading(false)
    }
  }

  // Extract filter options from maintenance records
  const extractFilterOptions = (records: ExtendedMaintenanceRecord[]) => {
    // Extract unique types
    const types = [...new Set(records.map(r => r.type))]

    // Extract unique regions
    const regions = [...new Set(records.map(r => {
      const parts = r.location.split(' - ')
      return parts[0]
    }))]

    // Extract unique technicians
    const technicians = [...new Set(records.map(r => r.assignedTo))]

    setFilterOptions({
      ...filterOptions,
      types,
      regions,
      technicians
    })
  }

  // Apply filters to maintenance records
  const applyFilters = async () => {
    if (allRecords.length === 0) return

    let filtered = [...allRecords]

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(r =>
        filters.status.includes(r.status)
      )
    }

    // Apply type filter
    if (filters.type !== "all") {
      filtered = await enhancedMaintenanceService.filterMaintenanceRecordsByTypeEnhanced(filtered, filters.type)
    }

    // Apply priority filter
    if (filters.priority.length > 0) {
      filtered = filtered.filter(r =>
        filters.priority.includes(r.priority)
      )
    }

    // Apply region filter
    if (filters.region.length > 0) {
      filtered = filtered.filter(r => {
        const region = r.location.split(' - ')[0]
        return filters.region.includes(region)
      })
    }

    // Apply assigned to filter
    if (filters.assignedTo.length > 0) {
      filtered = filtered.filter(r =>
        filters.assignedTo.includes(r.assignedTo)
      )
    }

    // Apply date range filter
    if (filters.dateRange[0] && filters.dateRange[1]) {
      filtered = filtered.filter(r => {
        const recordDate = new Date(r.scheduledDate)
        return recordDate >= filters.dateRange[0]! && recordDate <= filters.dateRange[1]!
      })
    }

    // Apply search filter
    if (filters.search) {
      filtered = await enhancedMaintenanceService.searchMaintenanceRecordsEnhanced(filtered, filters.search)
    }

    setFilteredRecords(filtered)
  }

  // Update a specific filter
  const updateFilter = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // Reset all filters
  const resetFilters = () => {
    setFilters(defaultFilters)
  }

  // Refresh all data
  const refreshData = () => {
    loadInitialData()
  }

  // Schedule new maintenance
  const scheduleNewMaintenance = async (data: Omit<ExtendedMaintenanceRecord, 'id'>) => {
    try {
      const newRecord = await enhancedMaintenanceService.scheduleNewMaintenance(data)
      refreshData()
      toast({
        title: "Maintenance Scheduled",
        description: "New maintenance record has been created successfully.",
      })
      return newRecord
    } catch (error) {
      console.error("Error scheduling maintenance:", error)
      toast({
        title: "Error",
        description: "Failed to schedule maintenance. Please try again.",
        variant: "destructive"
      })
      throw error
    }
  }

  // Update maintenance record
  const updateMaintenanceRecord = async (id: string, updates: Partial<ExtendedMaintenanceRecord>) => {
    try {
      await enhancedMaintenanceService.updateMaintenance(id, updates)
      refreshData()
      toast({
        title: "Maintenance Updated",
        description: "Maintenance record has been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating maintenance:", error)
      toast({
        title: "Error",
        description: "Failed to update maintenance record. Please try again.",
        variant: "destructive"
      })
      throw error
    }
  }

  // Complete maintenance record
  const completeMaintenanceRecord = async (id: string) => {
    try {
      await enhancedMaintenanceService.completeMaintenanceRecord(id)
      refreshData()
      toast({
        title: "Maintenance Completed",
        description: "Maintenance record has been marked as completed.",
      })
    } catch (error) {
      console.error("Error completing maintenance:", error)
      toast({
        title: "Error",
        description: "Failed to complete maintenance record. Please try again.",
        variant: "destructive"
      })
      throw error
    }
  }

  // Cancel maintenance record
  const cancelMaintenanceRecord = async (id: string) => {
    try {
      await enhancedMaintenanceService.cancelMaintenanceRecord(id)
      refreshData()
      toast({
        title: "Maintenance Cancelled",
        description: "Maintenance record has been cancelled.",
      })
    } catch (error) {
      console.error("Error cancelling maintenance:", error)
      toast({
        title: "Error",
        description: "Failed to cancel maintenance record. Please try again.",
        variant: "destructive"
      })
      throw error
    }
  }

  // Get maintenance records by transformer ID
  const getMaintenanceRecordsByTransformerId = async (id: string) => {
    try {
      return await enhancedMaintenanceService.getMaintenanceRecordsByTransformerIdEnhanced(id)
    } catch (error) {
      console.error(`Error fetching maintenance records for transformer ${id}:`, error)
      return []
    }
  }

  const value = {
    allRecords,
    upcomingRecords,
    inProgressRecords,
    completedRecords,
    filteredRecords,
    maintenanceLocations,
    maintenanceStatistics,
    isLoading,
    isMapLoading,
    filters,
    filterOptions,
    updateFilter,
    resetFilters,
    refreshData,
    scheduleNewMaintenance,
    updateMaintenanceRecord,
    completeMaintenanceRecord,
    cancelMaintenanceRecord,
    getMaintenanceRecordsByTransformerId
  }

  return (
    <MaintenanceContext.Provider value={value}>
      {children}
    </MaintenanceContext.Provider>
  )
}

export function useMaintenance() {
  const context = useContext(MaintenanceContext)
  if (context === undefined) {
    throw new Error("useMaintenance must be used within a MaintenanceProvider")
  }
  return context
}
