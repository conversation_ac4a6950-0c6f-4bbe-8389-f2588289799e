/**
 * API Configuration
 * Configuration for API endpoints, timeouts, and request handling
 */

export interface ApiConfig {
  baseUrl: string
  timeout: number
  retries: number
  retryDelay: number
  headers: Record<string, string>
  endpoints: Record<string, string>
  pagination: {
    defaultLimit: number
    maxLimit: number
    defaultOffset: number
  }
  cache: {
    enabled: boolean
    ttl: number
    maxSize: number
  }
  rateLimit: {
    enabled: boolean
    windowMs: number
    maxRequests: number
  }
}

// API endpoints configuration
export const API_ENDPOINTS = {
  // Authentication
  auth: {
    login: '/api/auth/login',
    logout: '/api/auth/logout',
    register: '/api/auth/register',
    refresh: '/api/auth/refresh',
    profile: '/api/auth/profile',
    changePassword: '/api/auth/change-password',
    resetPassword: '/api/auth/reset-password',
    verifyEmail: '/api/auth/verify-email'
  },
  
  // Users
  users: {
    list: '/api/users',
    create: '/api/users',
    get: '/api/users/:id',
    update: '/api/users/:id',
    delete: '/api/users/:id',
    roles: '/api/users/:id/roles',
    permissions: '/api/users/:id/permissions',
    activity: '/api/users/:id/activity',
    bulk: '/api/users/bulk',
    export: '/api/users/export',
    import: '/api/users/import'
  },
  
  // Roles & Permissions
  roles: {
    list: '/api/roles',
    create: '/api/roles',
    get: '/api/roles/:id',
    update: '/api/roles/:id',
    delete: '/api/roles/:id',
    permissions: '/api/roles/:id/permissions'
  },
  
  permissions: {
    list: '/api/permissions',
    create: '/api/permissions',
    get: '/api/permissions/:id',
    update: '/api/permissions/:id',
    delete: '/api/permissions/:id'
  },
  
  // Transformers
  transformers: {
    list: '/api/transformers',
    create: '/api/transformers',
    get: '/api/transformers/:id',
    update: '/api/transformers/:id',
    delete: '/api/transformers/:id',
    statistics: '/api/transformers/statistics',
    locations: '/api/transformers/locations',
    health: '/api/transformers/:id/health',
    history: '/api/transformers/:id/history',
    specifications: '/api/transformers/:id/specifications',
    documents: '/api/transformers/:id/documents',
    export: '/api/transformers/export',
    import: '/api/transformers/import',
    bulk: '/api/transformers/bulk',
    search: '/api/transformers/search'
  },
  
  // Maintenance
  maintenance: {
    records: '/api/maintenance/records',
    schedules: '/api/maintenance/schedules',
    teams: '/api/maintenance/teams',
    spareParts: '/api/maintenance/spare-parts',
    create: '/api/maintenance',
    get: '/api/maintenance/:id',
    update: '/api/maintenance/:id',
    delete: '/api/maintenance/:id',
    complete: '/api/maintenance/:id/complete',
    approve: '/api/maintenance/:id/approve',
    assign: '/api/maintenance/:id/assign',
    statistics: '/api/maintenance/statistics',
    calendar: '/api/maintenance/calendar'
  },
  
  // Monitoring & Alerts
  monitoring: {
    abnormalities: '/api/monitoring/abnormalities',
    meggerTests: '/api/monitoring/megger-tests',
    temperatures: '/api/monitoring/temperatures',
    loads: '/api/monitoring/loads',
    realTime: '/api/monitoring/real-time',
    alerts: '/api/monitoring/alerts',
    thresholds: '/api/monitoring/thresholds'
  },
  
  alerts: {
    list: '/api/alerts',
    create: '/api/alerts',
    get: '/api/alerts/:id',
    update: '/api/alerts/:id',
    delete: '/api/alerts/:id',
    acknowledge: '/api/alerts/:id/acknowledge',
    resolve: '/api/alerts/:id/resolve',
    statistics: '/api/alerts/statistics'
  },
  
  // Reports
  reports: {
    list: '/api/reports',
    create: '/api/reports',
    get: '/api/reports/:id',
    update: '/api/reports/:id',
    delete: '/api/reports/:id',
    generate: '/api/reports/generate',
    templates: '/api/reports/templates',
    schedule: '/api/reports/schedule',
    export: '/api/reports/:id/export'
  },
  
  // Analytics
  analytics: {
    dashboard: '/api/analytics/dashboard',
    performance: '/api/analytics/performance',
    trends: '/api/analytics/trends',
    predictions: '/api/analytics/predictions',
    kpis: '/api/analytics/kpis',
    comparisons: '/api/analytics/comparisons'
  },
  
  // System
  system: {
    health: '/api/system/health',
    status: '/api/system/status',
    settings: '/api/system/settings',
    logs: '/api/system/logs',
    backup: '/api/system/backup',
    restore: '/api/system/restore',
    notifications: '/api/system/notifications'
  },
  
  // File Management
  files: {
    upload: '/api/files/upload',
    download: '/api/files/:id/download',
    delete: '/api/files/:id',
    list: '/api/files',
    metadata: '/api/files/:id/metadata'
  },
  
  // External Services
  external: {
    weather: '/api/external/weather',
    maps: '/api/external/maps',
    geocoding: '/api/external/geocoding'
  }
} as const

// Default API configuration
export const API_CONFIG: ApiConfig = {
  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002/api',
  timeout: 30000, // 30 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
  
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
  
  endpoints: Object.values(API_ENDPOINTS).reduce((acc, group) => {
    return { ...acc, ...group }
  }, {}),
  
  pagination: {
    defaultLimit: 20,
    maxLimit: 100,
    defaultOffset: 0
  },
  
  cache: {
    enabled: true,
    ttl: 5 * 60 * 1000, // 5 minutes
    maxSize: 100
  },
  
  rateLimit: {
    enabled: true,
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000
  }
}

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
} as const

// API response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
  meta?: {
    total?: number
    page?: number
    limit?: number
    hasNext?: boolean
    hasPrev?: boolean
  }
}

export interface ApiError {
  code: string
  message: string
  details?: any
  timestamp: string
  path: string
}

// Request/Response interceptor configurations
export const INTERCEPTOR_CONFIG = {
  request: {
    timeout: 30000,
    withCredentials: true,
    validateStatus: (status: number) => status >= 200 && status < 300
  },
  
  response: {
    transformResponse: (data: any) => {
      try {
        return JSON.parse(data)
      } catch {
        return data
      }
    }
  }
}

// API client configuration for different environments
export const getApiConfig = (): ApiConfig => {
  const environment = process.env.NODE_ENV || 'development'
  
  const config = { ...API_CONFIG }
  
  switch (environment) {
    case 'production':
      config.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api.eeu-dtms.com'
      config.timeout = 60000 // 60 seconds
      config.cache.ttl = 10 * 60 * 1000 // 10 minutes
      config.rateLimit.maxRequests = 5000
      break
      
    case 'test':
      config.baseUrl = 'http://localhost:3002/api'
      config.timeout = 5000 // 5 seconds
      config.retries = 1
      config.cache.enabled = false
      config.rateLimit.enabled = false
      break
      
    case 'development':
    default:
      config.baseUrl = 'http://localhost:3002/api'
      config.timeout = 30000
      config.cache.ttl = 1 * 60 * 1000 // 1 minute
      break
  }
  
  return config
}

// Build URL with parameters
export const buildUrl = (endpoint: string, params?: Record<string, string | number>): string => {
  let url = endpoint
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`:${key}`, String(value))
    })
  }
  
  return url
}

// Build query string
export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, String(item)))
      } else {
        searchParams.append(key, String(value))
      }
    }
  })
  
  return searchParams.toString()
}

// Validate API configuration
export const validateApiConfig = (config: ApiConfig): boolean => {
  if (!config.baseUrl) {
    throw new Error('API base URL is required')
  }
  
  if (config.timeout < 1000) {
    throw new Error('API timeout must be at least 1000ms')
  }
  
  if (config.retries < 0) {
    throw new Error('API retries must be non-negative')
  }
  
  return true
}

// Export default configuration
export default getApiConfig()
