"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { Badge } from "@/src/components/ui/badge"
import { Input } from "@/src/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/src/components/ui/select"
import { Checkbox } from "@/src/components/ui/checkbox"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/src/components/ui/table"
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts'
import { 
  FileText, 
  Download, 
  Calendar, 
  Filter, 
  <PERSON><PERSON><PERSON>, 
  Eye,
  Share,
  Clock,
  TrendingUp,
  BarChart3,
  <PERSON><PERSON><PERSON> as <PERSON>ChartIcon,
  FileSpreadsheet,
  FilePdf,
  Mail,
  Printer,
  RefreshCw,
  Plus,
  Edit,
  Trash2
} from 'lucide-react'

interface Report {
  id: string
  name: string
  type: 'performance' | 'maintenance' | 'financial' | 'operational' | 'compliance'
  description: string
  schedule: 'manual' | 'daily' | 'weekly' | 'monthly' | 'quarterly'
  lastGenerated: string
  nextScheduled?: string
  status: 'active' | 'inactive' | 'generating'
  recipients: string[]
  format: 'pdf' | 'excel' | 'csv'
  parameters: Record<string, any>
}

interface ReportTemplate {
  id: string
  name: string
  category: string
  description: string
  fields: string[]
  charts: string[]
}

export function AdvancedReportingSystem() {
  const [reports, setReports] = useState<Report[]>([])
  const [templates, setTemplates] = useState<ReportTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('reports')
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)
  const [reportData, setReportData] = useState<any>(null)

  useEffect(() => {
    loadReports()
    loadTemplates()
  }, [])

  const loadReports = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockReports: Report[] = [
        {
          id: '1',
          name: 'Monthly Transformer Performance Report',
          type: 'performance',
          description: 'Comprehensive analysis of transformer performance metrics',
          schedule: 'monthly',
          lastGenerated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          nextScheduled: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active',
          recipients: ['<EMAIL>', '<EMAIL>'],
          format: 'pdf',
          parameters: { region: 'all', includeCharts: true }
        },
        {
          id: '2',
          name: 'Weekly Maintenance Summary',
          type: 'maintenance',
          description: 'Summary of maintenance activities and upcoming schedules',
          schedule: 'weekly',
          lastGenerated: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          nextScheduled: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active',
          recipients: ['<EMAIL>'],
          format: 'excel',
          parameters: { includePhotos: true, detailLevel: 'high' }
        },
        {
          id: '3',
          name: 'Quarterly Financial Analysis',
          type: 'financial',
          description: 'Financial impact analysis of transformer operations',
          schedule: 'quarterly',
          lastGenerated: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          nextScheduled: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active',
          recipients: ['<EMAIL>', '<EMAIL>'],
          format: 'pdf',
          parameters: { includeForecast: true, currency: 'ETB' }
        },
        {
          id: '4',
          name: 'Daily Operations Dashboard',
          type: 'operational',
          description: 'Real-time operational metrics and alerts',
          schedule: 'daily',
          lastGenerated: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          nextScheduled: new Date(Date.now() + 23 * 60 * 60 * 1000).toISOString(),
          status: 'generating',
          recipients: ['<EMAIL>', '<EMAIL>'],
          format: 'excel',
          parameters: { realTime: true, includeAlerts: true }
        }
      ]
      
      setReports(mockReports)
    } catch (error) {
      console.error('Failed to load reports:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadTemplates = async () => {
    try {
      const mockTemplates: ReportTemplate[] = [
        {
          id: '1',
          name: 'Performance Analysis Template',
          category: 'Performance',
          description: 'Standard template for transformer performance analysis',
          fields: ['efficiency', 'load_factor', 'temperature', 'voltage', 'current'],
          charts: ['efficiency_trend', 'load_distribution', 'temperature_heatmap']
        },
        {
          id: '2',
          name: 'Maintenance Report Template',
          category: 'Maintenance',
          description: 'Template for maintenance activities and schedules',
          fields: ['maintenance_type', 'completion_date', 'technician', 'cost', 'notes'],
          charts: ['maintenance_timeline', 'cost_breakdown', 'technician_workload']
        },
        {
          id: '3',
          name: 'Financial Summary Template',
          category: 'Financial',
          description: 'Financial analysis and cost reporting template',
          fields: ['operational_cost', 'maintenance_cost', 'energy_loss', 'revenue_impact'],
          charts: ['cost_trends', 'budget_variance', 'roi_analysis']
        }
      ]
      
      setTemplates(mockTemplates)
    } catch (error) {
      console.error('Failed to load templates:', error)
    }
  }

  const generateReport = async (reportId: string) => {
    const report = reports.find(r => r.id === reportId)
    if (!report) return

    // Update status to generating
    setReports(prev => prev.map(r => 
      r.id === reportId ? { ...r, status: 'generating' as const } : r
    ))

    try {
      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Mock report data
      const mockData = {
        summary: {
          totalTransformers: 150,
          operationalEfficiency: 94.5,
          maintenanceCompleted: 12,
          criticalAlerts: 3
        },
        charts: {
          performanceTrend: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            efficiency: 90 + Math.random() * 10,
            load: 60 + Math.random() * 30
          })),
          statusDistribution: [
            { name: 'Operational', value: 85, color: '#00C49F' },
            { name: 'Warning', value: 10, color: '#FFBB28' },
            { name: 'Critical', value: 3, color: '#FF8042' },
            { name: 'Maintenance', value: 2, color: '#0088FE' }
          ]
        }
      }
      
      setReportData(mockData)
      setSelectedReport(report)

      // Update status back to active
      setReports(prev => prev.map(r => 
        r.id === reportId ? { 
          ...r, 
          status: 'active' as const,
          lastGenerated: new Date().toISOString()
        } : r
      ))

    } catch (error) {
      console.error('Failed to generate report:', error)
      // Reset status on error
      setReports(prev => prev.map(r => 
        r.id === reportId ? { ...r, status: 'active' as const } : r
      ))
    }
  }

  const downloadReport = (format: 'pdf' | 'excel' | 'csv') => {
    // Simulate download
    const blob = new Blob(['Mock report data'], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `report-${Date.now()}.${format}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'generating': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'performance': return 'bg-blue-100 text-blue-800'
      case 'maintenance': return 'bg-orange-100 text-orange-800'
      case 'financial': return 'bg-green-100 text-green-800'
      case 'operational': return 'bg-purple-100 text-purple-800'
      case 'compliance': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatSchedule = (schedule: string) => {
    return schedule.charAt(0).toUpperCase() + schedule.slice(1)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Advanced Reporting System
              </CardTitle>
              <CardDescription>
                Generate, schedule, and manage comprehensive reports
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-1" />
                Settings
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-1" />
                New Report
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="reports">Reports</TabsTrigger>
              <TabsTrigger value="templates">Templates</TabsTrigger>
              <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="reports" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Reports List */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Available Reports</h3>
                    <Button variant="outline" size="sm" onClick={loadReports}>
                      <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                    </Button>
                  </div>
                  
                  <div className="space-y-3">
                    {loading ? (
                      <div className="text-center py-8">
                        <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                        Loading reports...
                      </div>
                    ) : (
                      reports.map((report) => (
                        <Card key={report.id} className="cursor-pointer hover:shadow-md transition-shadow">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1">
                                <h4 className="font-medium">{report.name}</h4>
                                <p className="text-sm text-muted-foreground mt-1">
                                  {report.description}
                                </p>
                              </div>
                              <Badge className={getStatusColor(report.status)}>
                                {report.status === 'generating' && (
                                  <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                                )}
                                {report.status}
                              </Badge>
                            </div>
                            
                            <div className="flex items-center gap-2 mb-3">
                              <Badge variant="outline" className={getTypeColor(report.type)}>
                                {report.type}
                              </Badge>
                              <Badge variant="outline">
                                {formatSchedule(report.schedule)}
                              </Badge>
                            </div>
                            
                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                Last: {new Date(report.lastGenerated).toLocaleDateString()}
                              </div>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => generateReport(report.id)}
                                  disabled={report.status === 'generating'}
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  Generate
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                </div>

                {/* Report Preview */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Report Preview</h3>
                  
                  {selectedReport && reportData ? (
                    <Card>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-lg">{selectedReport.name}</CardTitle>
                            <CardDescription>
                              Generated on {new Date().toLocaleDateString()}
                            </CardDescription>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm" onClick={() => downloadReport('pdf')}>
                              <FilePdf className="h-4 w-4 mr-1" />
                              PDF
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => downloadReport('excel')}>
                              <FileSpreadsheet className="h-4 w-4 mr-1" />
                              Excel
                            </Button>
                            <Button variant="outline" size="sm">
                              <Mail className="h-4 w-4 mr-1" />
                              Email
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {/* Summary Cards */}
                        <div className="grid grid-cols-2 gap-4 mb-6">
                          <Card>
                            <CardContent className="p-4">
                              <div className="text-2xl font-bold">{reportData.summary.totalTransformers}</div>
                              <div className="text-sm text-muted-foreground">Total Transformers</div>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardContent className="p-4">
                              <div className="text-2xl font-bold">{reportData.summary.operationalEfficiency}%</div>
                              <div className="text-sm text-muted-foreground">Efficiency</div>
                            </CardContent>
                          </Card>
                        </div>

                        {/* Charts */}
                        <div className="space-y-6">
                          <div>
                            <h4 className="font-medium mb-3">Performance Trend</h4>
                            <ResponsiveContainer width="100%" height={200}>
                              <LineChart data={reportData.charts.performanceTrend}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="date" />
                                <YAxis />
                                <Tooltip />
                                <Line type="monotone" dataKey="efficiency" stroke="#8884d8" strokeWidth={2} />
                              </LineChart>
                            </ResponsiveContainer>
                          </div>

                          <div>
                            <h4 className="font-medium mb-3">Status Distribution</h4>
                            <ResponsiveContainer width="100%" height={200}>
                              <PieChart>
                                <Pie
                                  data={reportData.charts.statusDistribution}
                                  cx="50%"
                                  cy="50%"
                                  innerRadius={40}
                                  outerRadius={80}
                                  paddingAngle={5}
                                  dataKey="value"
                                >
                                  {reportData.charts.statusDistribution.map((entry: any, index: number) => (
                                    <Cell key={`cell-${index}`} fill={entry.color} />
                                  ))}
                                </Pie>
                                <Tooltip />
                                <Legend />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card>
                      <CardContent className="p-8 text-center">
                        <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                        <h4 className="font-medium mb-2">No Report Selected</h4>
                        <p className="text-sm text-muted-foreground">
                          Generate a report to see the preview here
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="templates" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {templates.map((template) => (
                  <Card key={template.id}>
                    <CardHeader>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <CardDescription>{template.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <h5 className="text-sm font-medium mb-2">Fields</h5>
                          <div className="flex flex-wrap gap-1">
                            {template.fields.slice(0, 3).map((field) => (
                              <Badge key={field} variant="outline" className="text-xs">
                                {field.replace('_', ' ')}
                              </Badge>
                            ))}
                            {template.fields.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{template.fields.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <div>
                          <h5 className="text-sm font-medium mb-2">Charts</h5>
                          <div className="flex flex-wrap gap-1">
                            {template.charts.map((chart) => (
                              <Badge key={chart} variant="secondary" className="text-xs">
                                {chart.replace('_', ' ')}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        
                        <div className="flex gap-2 pt-2">
                          <Button variant="outline" size="sm" className="flex-1">
                            <Eye className="h-4 w-4 mr-1" />
                            Preview
                          </Button>
                          <Button size="sm" className="flex-1">
                            <Plus className="h-4 w-4 mr-1" />
                            Use Template
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="scheduled" className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Report</TableHead>
                      <TableHead>Schedule</TableHead>
                      <TableHead>Next Run</TableHead>
                      <TableHead>Recipients</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reports.filter(r => r.schedule !== 'manual').map((report) => (
                      <TableRow key={report.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{report.name}</div>
                            <Badge variant="outline" className={`${getTypeColor(report.type)} text-xs mt-1`}>
                              {report.type}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {formatSchedule(report.schedule)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {report.nextScheduled ? (
                            <div className="text-sm">
                              {new Date(report.nextScheduled).toLocaleDateString()}
                              <div className="text-xs text-muted-foreground">
                                {new Date(report.nextScheduled).toLocaleTimeString()}
                              </div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {report.recipients.length} recipient(s)
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(report.status)}>
                            {report.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Reports</p>
                        <p className="text-2xl font-bold">{reports.length}</p>
                      </div>
                      <FileText className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Active Schedules</p>
                        <p className="text-2xl font-bold">
                          {reports.filter(r => r.status === 'active' && r.schedule !== 'manual').length}
                        </p>
                      </div>
                      <Calendar className="h-4 w-4 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Generated Today</p>
                        <p className="text-2xl font-bold">
                          {reports.filter(r => {
                            const today = new Date().toDateString()
                            return new Date(r.lastGenerated).toDateString() === today
                          }).length}
                        </p>
                      </div>
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Templates</p>
                        <p className="text-2xl font-bold">{templates.length}</p>
                      </div>
                      <Settings className="h-4 w-4 text-purple-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
