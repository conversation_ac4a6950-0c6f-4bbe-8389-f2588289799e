import { Metadata } from 'next'
import { DatabaseInitializer } from '@/components/db-initializer'
import { DatabaseStatus } from '@/components/database-status'
import { DatabaseExportImport } from '@/components/database-export-import'
import { DatabaseQuery } from '@/components/database-query'
import { DatabaseReset } from '@/components/database-reset'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { DatabaseInitializerClient } from '@/components/database-initializer-client'
import { DatabaseInfoClient } from '@/components/database-info-client'

export const metadata: Metadata = {
  title: 'Database Administration',
  description: 'Manage the application database',
}

/**
 * Database Administration Page
 *
 * This page provides tools for managing the application database.
 * It's intended for administrators and developers.
 */
export default function DatabaseAdminPage() {
  return (
    <div className="container mx-auto py-10 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Database Administration</h1>
      </div>

      <DatabaseInitializerClient />

      <Card>
        <CardHeader>
          <CardTitle>Database Management</CardTitle>
          <CardDescription>
            Initialize, backup, and manage the application database
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <DatabaseStatus />
          <div className="border-t pt-4">
            <DatabaseInitializer />
          </div>
        </CardContent>
      </Card>

      <DatabaseExportImport />

      <DatabaseQuery />

      <Card>
        <CardHeader>
          <CardTitle>Database Information</CardTitle>
          <CardDescription>
            Information about the current database state
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DatabaseInfoClient />
        </CardContent>
      </Card>

      <DatabaseReset />
    </div>
  )
}
