// Create views to map original table names to workaround tables for other core entities
const mysql = require('mysql2/promise');

const config = {
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'dtms_eeu_db',
};

const views = [
  { name: 'app_alerts', sql: 'CREATE OR REPLACE VIEW app_alerts AS SELECT * FROM app_alerts2' },
  { name: 'app_maintenance_schedules', sql: 'CREATE OR REPLACE VIEW app_maintenance_schedules AS SELECT * FROM app_maintenance_schedules2' },
  { name: 'app_notifications', sql: 'CREATE OR REPLACE VIEW app_notifications AS SELECT * FROM app_notifications2' },
  { name: 'app_performance_metrics', sql: 'CREATE OR REPLACE VIEW app_performance_metrics AS SELECT * FROM app_performance_metrics2' },
  { name: 'app_weather_data', sql: 'CREATE OR REPLACE VIEW app_weather_data AS SELECT * FROM app_weather_data2' },
  { name: 'app_sensor_readings', sql: 'CREATE OR REPLACE VIEW app_sensor_readings AS SELECT * FROM app_sensor_readings2' }
];

(async () => {
  const connection = await mysql.createConnection(config);
  try {
    for (const v of views) {
      await connection.execute(v.sql);
      console.log(`✅ Created view: ${v.name}`);
    }
  } catch (err) {
    console.error('❌ Error creating views:', err);
  } finally {
    await connection.end();
  }
})();
