// Notification service with mock data

export interface Notification {
  id: string
  title: string
  description: string
  type: "info" | "success" | "warning" | "error"
  timestamp: string
  read: boolean
  actionUrl?: string
  source: "system" | "transformer" | "maintenance" | "user" | "weather"
}

// Mock notifications data
const mockNotifications: Notification[] = [
  {
    id: "notif-001",
    title: "Critical Temperature Alert",
    description: "Transformer TRF-1187 has reached critical temperature levels",
    type: "error",
    timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
    read: false,
    actionUrl: "/transformers/TRF-1187",
    source: "transformer"
  },
  {
    id: "notif-002",
    title: "Maintenance Completed",
    description: "Scheduled maintenance for TRF-0945 has been completed",
    type: "success",
    timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago
    read: false,
    actionUrl: "/maintenance/records/MNT-2345",
    source: "maintenance"
  },
  {
    id: "notif-003",
    title: "New User Registered",
    description: "<PERSON><PERSON><PERSON> has registered as a Technician",
    type: "info",
    timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
    read: true,
    actionUrl: "/users/USR-789",
    source: "user"
  },
  {
    id: "notif-004",
    title: "System Update Available",
    description: "A new system update (v2.3.1) is available for installation",
    type: "info",
    timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(), // 3 hours ago
    read: true,
    actionUrl: "/settings/updates",
    source: "system"
  },
  {
    id: "notif-005",
    title: "Severe Weather Alert",
    description: "Heavy rainfall expected in Addis Ababa region in the next 24 hours",
    type: "warning",
    timestamp: new Date(Date.now() - 1000 * 60 * 240).toISOString(), // 4 hours ago
    read: false,
    actionUrl: "/weather",
    source: "weather"
  },
  {
    id: "notif-006",
    title: "Oil Level Warning",
    description: "Transformer TRF-0562 has low oil levels",
    type: "warning",
    timestamp: new Date(Date.now() - 1000 * 60 * 300).toISOString(), // 5 hours ago
    read: true,
    actionUrl: "/transformers/TRF-0562",
    source: "transformer"
  },
  {
    id: "notif-007",
    title: "Maintenance Scheduled",
    description: "New maintenance scheduled for TRF-1024 on May 15, 2025",
    type: "info",
    timestamp: new Date(Date.now() - 1000 * 60 * 360).toISOString(), // 6 hours ago
    read: true,
    actionUrl: "/maintenance/schedule",
    source: "maintenance"
  },
  {
    id: "notif-008",
    title: "Connection Restored",
    description: "Smart meter SM-10245 connection has been restored",
    type: "success",
    timestamp: new Date(Date.now() - 1000 * 60 * 420).toISOString(), // 7 hours ago
    read: true,
    actionUrl: "/smart-meters/SM-10245",
    source: "system"
  }
];

// Notification service class
class NotificationService {
  private notifications: Notification[] = [...mockNotifications];
  
  // Get all notifications
  async getAllNotifications(): Promise<Notification[]> {
    // In a real app, this would fetch from an API
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...this.notifications].sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }
  
  // Get unread notifications
  async getUnreadNotifications(): Promise<Notification[]> {
    const allNotifications = await this.getAllNotifications();
    return allNotifications.filter(notification => !notification.read);
  }
  
  // Get notifications by source
  async getNotificationsBySource(source: Notification['source']): Promise<Notification[]> {
    const allNotifications = await this.getAllNotifications();
    return allNotifications.filter(notification => notification.source === source);
  }
  
  // Mark notification as read
  async markAsRead(id: string): Promise<void> {
    // In a real app, this would call an API
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    this.notifications = this.notifications.map(notification => 
      notification.id === id ? { ...notification, read: true } : notification
    );
  }
  
  // Mark all notifications as read
  async markAllAsRead(): Promise<void> {
    // In a real app, this would call an API
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    this.notifications = this.notifications.map(notification => 
      ({ ...notification, read: true })
    );
  }
  
  // Delete notification
  async deleteNotification(id: string): Promise<void> {
    // In a real app, this would call an API
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    this.notifications = this.notifications.filter(notification => 
      notification.id !== id
    );
  }
  
  // Clear all notifications
  async clearAllNotifications(): Promise<void> {
    // In a real app, this would call an API
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    this.notifications = [];
  }
  
  // Add a new notification
  async addNotification(notification: Omit<Notification, 'id' | 'timestamp'>): Promise<Notification> {
    // In a real app, this would call an API
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const newNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}`,
      timestamp: new Date().toISOString(),
    };
    
    this.notifications.unshift(newNotification);
    return newNotification;
  }
  
  // Get notification count
  async getNotificationCount(): Promise<{ total: number; unread: number }> {
    const allNotifications = await this.getAllNotifications();
    const unreadCount = allNotifications.filter(notification => !notification.read).length;
    
    return {
      total: allNotifications.length,
      unread: unreadCount
    };
  }
}

// Create and export a singleton instance
export const notificationService = new NotificationService();
