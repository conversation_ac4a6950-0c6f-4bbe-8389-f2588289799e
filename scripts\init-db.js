/**
 * Database Initialization Script
 *
 * This script initializes the database with seed data.
 * It can be run manually or as part of the application startup process.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Simple UUID v4 generator using crypto
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Path to the database file
const DB_PATH = path.join(__dirname, '..', 'public', 'db.json');

// Create a simple database with seed data
function createSeedData() {
  const now = new Date().toISOString();
  
  // Generate sample data
  const users = [
    {
      id: uuidv4(),
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'super_admin',
      isActive: true,
      createdAt: now,
      updatedAt: now
    }
  ];

  const regions = [
    {
      id: uuidv4(),
      name: 'Addis Ababa',
      code: 'addis',
      serviceCenters: 4,
      transformers: 10,
      coordinates: { lat: 9.0222, lng: 38.7468 },
      createdAt: now,
      updatedAt: now
    },
    {
      id: uuidv4(),
      name: 'Oromia',
      code: 'oromia',
      serviceCenters: 8,
      transformers: 25,
      coordinates: { lat: 8.9806, lng: 39.3772 },
      createdAt: now,
      updatedAt: now
    }
  ];

  // Create the database object
  const db = {
    users,
    regions,
    serviceCenters: [],
    transformers: [],
    maintenanceRecords: [],
    alerts: [],
    outages: [],
    weatherAlerts: [],
    _metadata: {
      version: '1.0.0',
      lastUpdated: now,
      recordCounts: {
        users: users.length,
        regions: regions.length,
        serviceCenters: 0,
        transformers: 0,
        maintenanceRecords: 0,
        alerts: 0,
        outages: 0,
        weatherAlerts: 0
      }
    }
  };

  return db;
}

// Initialize the database
function initializeDatabase() {
  try {
    // Check if the database file exists
    if (fs.existsSync(DB_PATH)) {
      console.log('Database already exists. To recreate it, delete the existing db.json file first.');
      return;
    }

    // Create the database directory if it doesn't exist
    const dbDir = path.dirname(DB_PATH);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    // Create and write the seed data
    const db = createSeedData();
    fs.writeFileSync(DB_PATH, JSON.stringify(db, null, 2));
    
    console.log('Database initialized successfully at', DB_PATH);
  } catch (error) {
    console.error('Error initializing database:', error);
    process.exit(1);
  }
}

// Run the initialization
initializeDatabase();
