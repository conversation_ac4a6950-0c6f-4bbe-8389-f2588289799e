"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ever<PERSON>, <PERSON><PERSON><PERSON>tatus, AlertType } from "@/contexts/alert-context"
import { transformerService } from "./transformer-service"

// Helper function to determine transformer status based on alert severity
function getTransformerStatusFromAlertSeverity(severity: AlertSeverity): "Operational" | "Maintenance" | "Critical" | "Burnt" | "Offline" {
  switch (severity) {
    case "Critical":
      return "Critical"
    case "High":
      return "Maintenance"
    case "Medium":
      return "Maintenance"
    case "Low":
    default:
      return "Operational"
  }
}

// Alert service to handle alert-related operations
export const alertService = {
  // Update transformer status based on alert
  async updateTransformerStatusFromAlert(transformerId: string, alertSeverity: AlertSeverity): Promise<void> {
    try {
      // Get the current transformer
      const transformer = await transformerService.getTransformerById(transformerId)
      if (!transformer) return

      // Determine the new status based on alert severity
      const newStatus = getTransformerStatusFromAlertSeverity(alertSeverity)
      
      // Only update if the new status is more severe than the current status
      // Priority: Critical > Maintenance > Operational
      const shouldUpdate = (
        (newStatus === "Critical") ||
        (newStatus === "Maintenance" && transformer.status !== "Critical") ||
        (transformer.status === "Operational")
      )
      
      if (shouldUpdate) {
        await transformerService.updateTransformerStatus(transformerId, newStatus)
        console.log(`Updated transformer ${transformerId} status to ${newStatus} based on alert`)
      }
    } catch (error) {
      console.error("Error updating transformer status from alert:", error)
    }
  },
  
  // Update transformer status when alert is resolved
  async updateTransformerStatusOnAlertResolution(transformerId: string): Promise<void> {
    try {
      // Get all active alerts for this transformer
      // In a real implementation, you would fetch this from a database
      // For now, we'll just set it back to Operational
      await transformerService.updateTransformerStatus(transformerId, "Operational")
      console.log(`Reset transformer ${transformerId} status to Operational after alert resolution`)
    } catch (error) {
      console.error("Error updating transformer status on alert resolution:", error)
    }
  }
}
