/**
 * Seed Operational Data for EEU DTMS
 * This script seeds transformers, maintenance, alerts, and operational data
 */

const mysql = require('mysql2/promise');

const config = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'dtms_eeu_db',
};

async function seedOperationalData() {
  let connection;
  
  try {
    console.log('⚡ SEEDING OPERATIONAL DATA FOR EEU DTMS');
    console.log('=' .repeat(60));
    
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to MySQL database');
    
    // 1. Seed Comprehensive Transformers
    console.log('\n⚡ SEEDING COMPREHENSIVE TRANSFORMERS');
    console.log('-' .repeat(40));
    
    const transformers = [
      // Addis Ababa Region (4 transformers)
      { serial_number: 'EEU-AA-001', name: 'Bole Main Distribution Transformer', type: 'distribution', capacity_kva: 1000.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'Siemens', model: 'GEAFOL Cast Resin', year_manufactured: 2020, installation_date: '2020-06-15', commissioning_date: '2020-07-01', location_name: 'Bole Road, Near EEU Headquarters', latitude: 9.02220000, longitude: 38.74680000, region_id: 1, service_center_id: 1, status: 'operational', efficiency_rating: 98.50, load_factor: 75.00, temperature: 65.00, oil_level: 95.00, health_index: 92.00, last_maintenance: '2024-09-15', next_maintenance: '2024-12-15', maintenance_interval_days: 90, criticality: 'high', customer_type: 'commercial', asset_value: 150000.00, warranty_expiry: '2025-06-15', insurance_policy: 'INS-AA-001' },
      { serial_number: 'EEU-AA-002', name: 'Megenagna Distribution Transformer', type: 'distribution', capacity_kva: 500.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2019, installation_date: '2019-08-20', commissioning_date: '2019-09-01', location_name: 'Megenagna, Addis Ababa', latitude: 9.02990000, longitude: 38.80790000, region_id: 1, service_center_id: 1, status: 'warning', efficiency_rating: 97.80, load_factor: 88.00, temperature: 72.00, oil_level: 85.00, health_index: 78.00, last_maintenance: '2024-08-10', next_maintenance: '2024-11-10', maintenance_interval_days: 90, criticality: 'medium', customer_type: 'residential', asset_value: 85000.00, warranty_expiry: '2024-08-20', insurance_policy: 'INS-AA-002' },
      { serial_number: 'EEU-AA-003', name: 'Bole Airport Transformer', type: 'distribution', capacity_kva: 1250.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2022, installation_date: '2022-01-20', commissioning_date: '2022-02-01', location_name: 'Bole International Airport', latitude: 8.98060000, longitude: 38.79920000, region_id: 1, service_center_id: 1, status: 'operational', efficiency_rating: 99.50, load_factor: 70.00, temperature: 55.00, oil_level: 98.00, health_index: 98.00, last_maintenance: '2024-10-01', next_maintenance: '2025-01-01', maintenance_interval_days: 90, criticality: 'critical', customer_type: 'commercial', asset_value: 200000.00, warranty_expiry: '2027-01-20', insurance_policy: 'INS-AA-003' },
      { serial_number: 'EEU-AA-004', name: 'Merkato Commercial Transformer', type: 'distribution', capacity_kva: 630.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2020, installation_date: '2020-03-15', commissioning_date: '2020-04-01', location_name: 'Merkato Commercial Area', latitude: 9.00840000, longitude: 38.75750000, region_id: 1, service_center_id: 1, status: 'operational', efficiency_rating: 97.50, load_factor: 78.00, temperature: 66.00, oil_level: 91.00, health_index: 88.00, last_maintenance: '2024-07-20', next_maintenance: '2024-10-20', maintenance_interval_days: 90, criticality: 'medium', customer_type: 'commercial', asset_value: 110000.00, warranty_expiry: '2025-03-15', insurance_policy: 'INS-AA-004' },
      
      // Oromia Region (4 transformers)
      { serial_number: 'EEU-OR-001', name: 'Jimma Central Distribution Transformer', type: 'distribution', capacity_kva: 500.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2019, installation_date: '2019-08-20', commissioning_date: '2019-09-01', location_name: 'Jimma City Center', latitude: 7.67810000, longitude: 36.83440000, region_id: 2, service_center_id: 2, status: 'operational', efficiency_rating: 97.80, load_factor: 68.00, temperature: 62.00, oil_level: 92.00, health_index: 85.00, last_maintenance: '2024-10-05', next_maintenance: '2025-01-05', maintenance_interval_days: 90, criticality: 'medium', customer_type: 'commercial', asset_value: 85000.00, warranty_expiry: '2024-08-20', insurance_policy: 'INS-OR-001' },
      { serial_number: 'EEU-OR-002', name: 'Adama Industrial Transformer', type: 'distribution', capacity_kva: 800.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2018, installation_date: '2018-11-12', commissioning_date: '2018-12-01', location_name: 'Adama Industrial Park', latitude: 8.54000000, longitude: 39.26750000, region_id: 2, service_center_id: 2, status: 'warning', efficiency_rating: 96.50, load_factor: 87.00, temperature: 74.00, oil_level: 88.00, health_index: 80.00, last_maintenance: '2024-08-15', next_maintenance: '2024-11-15', maintenance_interval_days: 90, criticality: 'high', customer_type: 'industrial', asset_value: 125000.00, warranty_expiry: '2023-11-12', insurance_policy: 'INS-OR-002' },
      { serial_number: 'EEU-OR-003', name: 'Nekemte Distribution Transformer', type: 'distribution', capacity_kva: 400.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'Schneider Electric', model: 'Trihal', year_manufactured: 2019, installation_date: '2019-11-20', commissioning_date: '2019-12-01', location_name: 'Nekemte City Center', latitude: 9.08330000, longitude: 36.55000000, region_id: 2, service_center_id: 2, status: 'maintenance', efficiency_rating: 96.20, load_factor: 65.00, temperature: 58.00, oil_level: 83.00, health_index: 82.00, last_maintenance: '2024-11-25', next_maintenance: '2024-12-25', maintenance_interval_days: 90, criticality: 'medium', customer_type: 'residential', asset_value: 70000.00, warranty_expiry: '2024-11-20', insurance_policy: 'INS-OR-003' },
      { serial_number: 'EEU-OR-004', name: 'Bishoftu Power Transformer', type: 'power', capacity_kva: 1500.00, voltage_primary: 132.00, voltage_secondary: 33.00, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2021, installation_date: '2021-04-10', commissioning_date: '2021-05-01', location_name: 'Bishoftu Industrial Zone', latitude: 8.75280000, longitude: 38.98330000, region_id: 2, service_center_id: 2, status: 'operational', efficiency_rating: 99.10, load_factor: 72.00, temperature: 60.00, oil_level: 96.00, health_index: 95.00, last_maintenance: '2024-09-10', next_maintenance: '2024-12-10', maintenance_interval_days: 90, criticality: 'high', customer_type: 'industrial', asset_value: 250000.00, warranty_expiry: '2026-04-10', insurance_policy: 'INS-OR-004' },
      
      // Amhara Region (3 transformers)
      { serial_number: 'EEU-AM-001', name: 'Bahir Dar Power Distribution Transformer', type: 'power', capacity_kva: 2000.00, voltage_primary: 132.00, voltage_secondary: 33.00, manufacturer: 'Schneider Electric', model: 'Trihal', year_manufactured: 2021, installation_date: '2021-03-10', commissioning_date: '2021-04-01', location_name: 'Bahir Dar Industrial Zone', latitude: 11.59590000, longitude: 37.39060000, region_id: 3, service_center_id: 3, status: 'operational', efficiency_rating: 99.20, load_factor: 82.00, temperature: 58.00, oil_level: 98.00, health_index: 96.00, last_maintenance: '2024-11-20', next_maintenance: '2024-12-20', maintenance_interval_days: 90, criticality: 'critical', customer_type: 'industrial', asset_value: 300000.00, warranty_expiry: '2026-03-10', insurance_policy: 'INS-AM-001' },
      { serial_number: 'EEU-AM-002', name: 'Gondar Distribution Transformer', type: 'distribution', capacity_kva: 315.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2019, installation_date: '2019-12-05', commissioning_date: '2020-01-01', location_name: 'Gondar City Center', latitude: 12.60900000, longitude: 37.46470000, region_id: 3, service_center_id: 3, status: 'operational', efficiency_rating: 97.00, load_factor: 72.00, temperature: 63.00, oil_level: 87.00, health_index: 82.00, last_maintenance: '2024-08-05', next_maintenance: '2024-11-05', maintenance_interval_days: 90, criticality: 'medium', customer_type: 'residential', asset_value: 60000.00, warranty_expiry: '2024-12-05', insurance_policy: 'INS-AM-002' },
      { serial_number: 'EEU-AM-003', name: 'Debre Markos Transformer', type: 'distribution', capacity_kva: 315.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'Siemens', model: 'GEAFOL', year_manufactured: 2021, installation_date: '2021-05-10', commissioning_date: '2021-06-01', location_name: 'Debre Markos Town', latitude: 10.35000000, longitude: 37.73330000, region_id: 3, service_center_id: 3, status: 'operational', efficiency_rating: 98.10, load_factor: 69.00, temperature: 61.00, oil_level: 94.00, health_index: 90.00, last_maintenance: '2024-09-10', next_maintenance: '2024-12-10', maintenance_interval_days: 90, criticality: 'medium', customer_type: 'commercial', asset_value: 65000.00, warranty_expiry: '2026-05-10', insurance_policy: 'INS-AM-003' },
      
      // Tigray Region (2 transformers)
      { serial_number: 'EEU-TI-001', name: 'Mekelle Central Transformer', type: 'distribution', capacity_kva: 630.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2017, installation_date: '2017-05-08', commissioning_date: '2017-06-01', location_name: 'Mekelle City Center', latitude: 13.49670000, longitude: 39.47530000, region_id: 4, service_center_id: 4, status: 'critical', efficiency_rating: 94.20, load_factor: 95.00, temperature: 85.00, oil_level: 65.00, health_index: 45.00, last_maintenance: '2024-07-15', next_maintenance: '2024-12-10', maintenance_interval_days: 90, criticality: 'critical', customer_type: 'residential', asset_value: 90000.00, warranty_expiry: '2022-05-08', insurance_policy: 'INS-TI-001' },
      { serial_number: 'EEU-TI-002', name: 'Adigrat Distribution Transformer', type: 'distribution', capacity_kva: 250.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'Schneider Electric', model: 'Trihal', year_manufactured: 2020, installation_date: '2020-08-15', commissioning_date: '2020-09-01', location_name: 'Adigrat Town Center', latitude: 14.27000000, longitude: 39.46000000, region_id: 4, service_center_id: 4, status: 'operational', efficiency_rating: 97.50, load_factor: 65.00, temperature: 59.00, oil_level: 89.00, health_index: 88.00, last_maintenance: '2024-10-15', next_maintenance: '2025-01-15', maintenance_interval_days: 90, criticality: 'low', customer_type: 'residential', asset_value: 45000.00, warranty_expiry: '2025-08-15', insurance_policy: 'INS-TI-002' },
      
      // SNNP Region (2 transformers)
      { serial_number: 'EEU-SN-001', name: 'Hawassa Distribution Transformer', type: 'distribution', capacity_kva: 400.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'Schneider Electric', model: 'Trihal', year_manufactured: 2020, installation_date: '2020-09-15', commissioning_date: '2020-10-01', location_name: 'Hawassa City Center', latitude: 7.06210000, longitude: 38.47760000, region_id: 5, service_center_id: 5, status: 'operational', efficiency_rating: 98.00, load_factor: 65.00, temperature: 60.00, oil_level: 90.00, health_index: 88.00, last_maintenance: '2024-09-01', next_maintenance: '2024-12-01', maintenance_interval_days: 90, criticality: 'medium', customer_type: 'commercial', asset_value: 70000.00, warranty_expiry: '2025-09-15', insurance_policy: 'INS-SN-001' },
      { serial_number: 'EEU-SN-002', name: 'Arba Minch Transformer', type: 'distribution', capacity_kva: 315.00, voltage_primary: 33.00, voltage_secondary: 0.40, manufacturer: 'ABB', model: 'UniGear ZS1', year_manufactured: 2018, installation_date: '2018-07-20', commissioning_date: '2018-08-01', location_name: 'Arba Minch Town', latitude: 6.03330000, longitude: 37.55000000, region_id: 5, service_center_id: 5, status: 'warning', efficiency_rating: 96.80, load_factor: 84.00, temperature: 71.00, oil_level: 86.00, health_index: 75.00, last_maintenance: '2024-06-20', next_maintenance: '2024-09-20', maintenance_interval_days: 90, criticality: 'medium', customer_type: 'agricultural', asset_value: 60000.00, warranty_expiry: '2023-07-20', insurance_policy: 'INS-SN-002' }
    ];

    // Insert transformers
    let transformerCount = 0;
    for (const transformer of transformers) {
      try {
        await connection.execute(`
          INSERT INTO dtms_transformers (
            serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary,
            manufacturer, model, year_manufactured, installation_date, commissioning_date, location_name,
            latitude, longitude, region_id, service_center_id, status, efficiency_rating, load_factor,
            temperature, oil_level, health_index, last_maintenance, next_maintenance, maintenance_interval_days,
            criticality, customer_type, asset_value, warranty_expiry, insurance_policy
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          name = VALUES(name), status = VALUES(status), efficiency_rating = VALUES(efficiency_rating),
          load_factor = VALUES(load_factor), temperature = VALUES(temperature), oil_level = VALUES(oil_level),
          health_index = VALUES(health_index), last_maintenance = VALUES(last_maintenance),
          next_maintenance = VALUES(next_maintenance)
        `, [
          transformer.serial_number, transformer.name, transformer.type, transformer.capacity_kva,
          transformer.voltage_primary, transformer.voltage_secondary, transformer.manufacturer,
          transformer.model, transformer.year_manufactured, transformer.installation_date,
          transformer.commissioning_date, transformer.location_name, transformer.latitude, transformer.longitude,
          transformer.region_id, transformer.service_center_id, transformer.status, transformer.efficiency_rating,
          transformer.load_factor, transformer.temperature, transformer.oil_level, transformer.health_index,
          transformer.last_maintenance, transformer.next_maintenance, transformer.maintenance_interval_days,
          transformer.criticality, transformer.customer_type, transformer.asset_value,
          transformer.warranty_expiry, transformer.insurance_policy
        ]);
        transformerCount++;
        console.log(`  ✅ Added: ${transformer.name}`);
      } catch (error) {
        console.log(`  ⚠️  Skipped: ${transformer.name} (${error.message})`);
      }
    }
    console.log(`✅ Successfully seeded ${transformerCount} transformers`);

    console.log('\n✅ Operational data seeded successfully!');
    console.log('📊 Ready for dashboard display with comprehensive data!');
    
  } catch (error) {
    console.error('❌ Error seeding operational data:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Export for use in other scripts
module.exports = { seedOperationalData };

// Run if called directly
if (require.main === module) {
  seedOperationalData();
}
