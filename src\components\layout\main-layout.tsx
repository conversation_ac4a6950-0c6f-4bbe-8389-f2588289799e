import type React from "react"
import { SidebarOptimized } from "@/src/components/layout/sidebar-optimized"
import { Header } from "@/src/components/layout/header"
import { Footer } from "@/src/components/layout/footer"
import { ProtectedRoute } from "@/src/components/layout/protected-route"
import ProvidersWrapper from "@/src/components/layout/providers-wrapper"

export function MainLayout({
  children,
  allowedRoles,
  requiredPermissions,
}: {
  children: React.ReactNode
  allowedRoles?: any[]
  requiredPermissions?: Array<{ resource: string; action: string }>
}) {
  return (
    <ProvidersWrapper>
      <ProtectedRoute allowedRoles={allowedRoles} requiredPermissions={requiredPermissions}>
        <div className="flex h-screen overflow-hidden">
          <SidebarOptimized />
          <div className="flex flex-col flex-1 overflow-hidden">
            <Header />
            <main className="flex-1 overflow-y-auto bg-slate-50 dark:bg-slate-900 p-4 md:p-6">{children}</main>
            <Footer />
          </div>
        </div>
      </ProtectedRoute>
    </ProvidersWrapper>
  )
}
