"use client"

import { Tabs<PERSON><PERSON>, TabsTrigger } from "@/src/components/ui/tabs"
import { Badge } from "@/src/components/ui/badge"
import { Users, Shield, Wrench, Activity } from "lucide-react"
import { UserCounts } from "@/src/types/user-management"

interface UserTabListProps {
  activeTab: string
  onTabChange: (value: string) => void
  userCounts: UserCounts
}

export function UserTabList({ activeTab, onTabChange, userCounts }: UserTabListProps) {
  return (
    <TabsList className="flex">
      <TabsTrigger value="all" className="flex items-center gap-2">
        <Users className="h-4 w-4" />
        All Users
        <Badge variant="outline" className="ml-1 px-1 py-0 text-xs">
          {userCounts.all}
        </Badge>
      </TabsTrigger>
      <TabsTrigger value="admins" className="flex items-center gap-2">
        <Shield className="h-4 w-4" />
        Administrators
        <Badge variant="outline" className="ml-1 px-1 py-0 text-xs">
          {userCounts.administrators}
        </Badge>
      </TabsTrigger>
      <TabsTrigger value="technicians" className="flex items-center gap-2">
        <Wrench className="h-4 w-4" />
        Technicians
        <Badge variant="outline" className="ml-1 px-1 py-0 text-xs">
          {userCounts.technicians}
        </Badge>
      </TabsTrigger>
      <TabsTrigger value="operators" className="flex items-center gap-2">
        <Activity className="h-4 w-4" />
        Operators
        <Badge variant="outline" className="ml-1 px-1 py-0 text-xs">
          {userCounts.operators}
        </Badge>
      </TabsTrigger>
    </TabsList>
  )
}
