"use client"

import { Menu, Languages } from "lucide-react"
import Image from "next/image"
import { Button } from "@/src/components/ui/button"
import { useSidebar } from "@/src/components/layout/sidebar-provider"
import { ModeToggle } from "@/components/mode-toggle"
import { UserNav } from "@/components/user-nav"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/src/components/ui/dropdown-menu"
import { NotificationCenter } from "@/components/notification-center"
import { QuickActions } from "@/components/quick-actions"
import { HelpSupport } from "@/components/help-support"
import { GlobalSearch } from "@/components/global-search"
import { useState, useEffect } from "react"
import { Badge } from "@/src/components/ui/badge"
import { useLanguage, languageNames, type Language } from "@/src/contexts/language-context"
import { useToast } from "@/src/components/ui/use-toast"

export function Header() {
  const { toggle, isMobile } = useSidebar()
  const [currentTime, setCurrentTime] = useState(new Date())
  const { language, setLanguage, t } = useLanguage()
  const { toast } = useToast()

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)

    return () => clearInterval(timer)
  }, [])

  // Format time based on current language
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString(language === 'en' ? 'en-US' : language, {
      hour: '2-digit',
      minute: '2-digit',
      hour12: language === 'en' // Use 12-hour format for English, 24-hour for others
    })
  }

  // Handle language change
  const handleLanguageChange = (lang: Language) => {
    setLanguage(lang)

    toast({
      title: t("language_changed"),
      description: `${languageNames[lang]} ${t("language_set_as_default")}`,
    })

    // Reload the page to apply language changes to all components
    // In a real app, you might want to use a more sophisticated approach
    // that doesn't require a page reload
    // window.location.reload()
  }

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-white px-4 md:px-6">
      {isMobile && (
        <Button variant="ghost" size="icon" onClick={toggle} className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      )}
      <div className="flex-1 flex items-center gap-3">
        <Image
          src="/eeu-logo.svg"
          alt="Ethiopian Electric Utility Logo"
          width={36}
          height={36}
          className="h-9 w-9 hidden md:block"
        />
        <h1 className="text-lg font-semibold">
          <span className="text-green-600">Ethiopian Electric Utility</span>
          <span className="hidden md:inline"> - </span>
          <span className="block md:inline text-sm md:text-lg text-gray-700">Transformer Management System</span>
        </h1>
      </div>

      {/* Global Search */}
      <GlobalSearch />

      <div className="flex items-center gap-2">
        {/* Current Time */}
        <div className="hidden md:flex items-center text-sm text-muted-foreground mr-2">
          <span>{formatTime(currentTime)}</span>
        </div>

        {/* Language Selector */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="relative">
              <Languages className="h-5 w-5" />
              <Badge
                className="absolute -top-1 -right-1 h-4 w-4 flex items-center justify-center p-0 text-[10px] bg-primary"
                variant="default"
              >
                {language.toUpperCase()}
              </Badge>
              <span className="sr-only">{t("language")}</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {Object.entries(languageNames).map(([code, name]) => (
              <DropdownMenuItem
                key={code}
                onClick={() => handleLanguageChange(code as Language)}
                className="flex items-center justify-between"
              >
                <span>{name}</span>
                {language === code && (
                  <Badge variant="outline" className="ml-2 px-1 py-0 h-5">
                    ✓
                  </Badge>
                )}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Quick Actions */}
        <QuickActions />

        {/* Help & Support */}
        <HelpSupport />

        {/* Notifications */}
        <NotificationCenter />

        {/* Theme Toggle */}
        <ModeToggle />

        {/* User Navigation */}
        <UserNav />
      </div>
    </header>
  )
}
