export type UserRole =
  | "super_admin"
  | "national_asset_manager"
  | "national_maintenance_manager"
  | "regional_admin"
  | "regional_asset_manager"
  | "regional_maintenance_engineer"
  | "service_center_manager"
  | "field_technician"
  | "customer_service_agent"
  | "audit_compliance_officer";

export type OrganizationalLevel = "head_office" | "regional_office" | "service_center";

export interface Permission {
  resource: string;
  action: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  organizationalLevel: OrganizationalLevel;
  regionId?: string;
  serviceCenterId?: string;
  permissions: Permission[];
  isActive: boolean;
  lastLogin: string;
  avatar?: string;
  phone?: string;
  department?: string;
  location?: string;
  bio?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
}
