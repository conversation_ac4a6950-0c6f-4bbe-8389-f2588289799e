"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Download, Search, Users, Building, MapPin, UserPlus } from "lucide-react"
import { Input } from "@/src/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/src/components/ui/tabs"
import { useAuth } from "@/src/features/auth/context/auth-context"
import { useToast } from "@/src/components/ui/use-toast"
import { UserAssignmentTable } from "@/components/user-assignment-table"
import { BulkAssignmentDialog } from "@/components/bulk-assignment-dialog"
import type { User } from "@/src/types/auth"

export function UserAssignmentContent() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isBulkDialogOpen, setIsBulkDialogOpen] = useState(false)
  const { user: currentUser } = useAuth()
  const { toast } = useToast()

  // Mock users data - in a real app, this would come from an API
  const users = [
    {
      id: "user-001",
      name: "Abebe Kebede",
      email: "<EMAIL>",
      role: "super_admin",
      organizationalLevel: "head_office",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-25T10:24:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=AK",
    },
    {
      id: "user-002",
      name: "Tigist Haile",
      email: "<EMAIL>",
      role: "national_asset_manager",
      organizationalLevel: "head_office",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-26T09:15:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=TH",
    },
    {
      id: "user-003",
      name: "Dawit Mengistu",
      email: "<EMAIL>",
      role: "regional_admin",
      organizationalLevel: "regional_office",
      regionId: "region-001",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-26T14:30:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=DM",
    },
    {
      id: "user-004",
      name: "Hiwot Tadesse",
      email: "<EMAIL>",
      role: "regional_maintenance_engineer",
      organizationalLevel: "regional_office",
      regionId: "region-001",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-25T16:45:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=HT",
    },
    {
      id: "user-005",
      name: "Solomon Bekele",
      email: "<EMAIL>",
      role: "service_center_manager",
      organizationalLevel: "service_center",
      regionId: "region-001",
      serviceCenterId: "sc-001",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-24T11:20:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=SB",
    },
    {
      id: "user-006",
      name: "Meron Alemu",
      email: "<EMAIL>",
      role: "field_technician",
      organizationalLevel: "service_center",
      regionId: "region-001",
      serviceCenterId: "sc-001",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-26T08:10:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=MA",
    },
    {
      id: "user-007",
      name: "Yonas Girma",
      email: "<EMAIL>",
      role: "customer_service_agent",
      organizationalLevel: "service_center",
      regionId: "region-001",
      serviceCenterId: "sc-001",
      permissions: [],
      isActive: false,
      lastLogin: "2023-04-20T09:30:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=YG",
    },
    {
      id: "user-008",
      name: "Bethlehem Tadesse",
      email: "<EMAIL>",
      role: "audit_compliance_officer",
      organizationalLevel: "head_office",
      permissions: [],
      isActive: true,
      lastLogin: "2023-04-25T13:15:00Z",
      avatar: "/placeholder.svg?height=40&width=40&text=BT",
    },
  ] as User[]

  // Filter users based on search query and active tab
  const filteredUsers = users.filter((user) => {
    // Search filter
    const matchesSearch =
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.role.toLowerCase().includes(searchQuery.toLowerCase())

    // Tab filter
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "unassigned" &&
        ((user.organizationalLevel === "regional_office" && !user.regionId) ||
          (user.organizationalLevel === "service_center" && !user.serviceCenterId))) ||
      (activeTab === "head_office" && user.organizationalLevel === "head_office") ||
      (activeTab === "regional_office" && user.organizationalLevel === "regional_office") ||
      (activeTab === "service_center" && user.organizationalLevel === "service_center")

    // Access control - only show users the current user has permission to manage
    let hasAccess = false
    if (currentUser?.role === "super_admin") {
      hasAccess = true
    } else if (currentUser?.role === "regional_admin" && currentUser?.regionId) {
      hasAccess = user.regionId === currentUser.regionId || !user.regionId
    } else if (currentUser?.role === "service_center_manager" && currentUser?.serviceCenterId) {
      hasAccess = user.serviceCenterId === currentUser.serviceCenterId
    }

    return matchesSearch && matchesTab && hasAccess
  })

  const handleBulkAssignment = () => {
    setIsBulkDialogOpen(true)
  }

  const handleUserAssigned = (user: User, regionId?: string, serviceCenterId?: string) => {
    // In a real app, this would call an API to update the user
    toast({
      title: "User assigned",
      description: `${user.name} has been assigned to ${
        serviceCenterId ? "service center " + serviceCenterId : regionId ? "region " + regionId : "head office"
      }.`,
    })
  }

  const handleBulkAssignmentComplete = (count: number, regionId?: string, serviceCenterId?: string) => {
    // In a real app, this would call an API to update multiple users
    toast({
      title: "Users assigned",
      description: `${count} users have been assigned to ${
        serviceCenterId ? "service center " + serviceCenterId : regionId ? "region " + regionId : "head office"
      }.`,
    })
    setIsBulkDialogOpen(false)
  }

  // Check if user has permission to assign users
  const canAssignUsers =
    currentUser?.role === "super_admin" ||
    currentUser?.role === "regional_admin" ||
    currentUser?.role === "service_center_manager"

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">User Assignments</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          {canAssignUsers && (
            <Button size="sm" onClick={handleBulkAssignment}>
              <UserPlus className="mr-2 h-4 w-4" />
              Bulk Assign
            </Button>
          )}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>User Assignments</CardTitle>
          <CardDescription>Assign users to regions and service centers based on their roles</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search users..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-4">
            <TabsList>
              <TabsTrigger value="all">
                <Users className="mr-2 h-4 w-4" />
                All Users
              </TabsTrigger>
              <TabsTrigger value="unassigned">
                <Users className="mr-2 h-4 w-4" />
                Unassigned
              </TabsTrigger>
              <TabsTrigger value="head_office">
                <Building className="mr-2 h-4 w-4" />
                Head Office
              </TabsTrigger>
              <TabsTrigger value="regional_office">
                <Building className="mr-2 h-4 w-4" />
                Regional Office
              </TabsTrigger>
              <TabsTrigger value="service_center">
                <MapPin className="mr-2 h-4 w-4" />
                Service Center
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <UserAssignmentTable
            users={filteredUsers}
            onUserAssigned={handleUserAssigned}
            currentUserRole={currentUser?.role}
            currentUserRegionId={currentUser?.regionId}
            currentUserServiceCenterId={currentUser?.serviceCenterId}
          />
        </CardContent>
      </Card>

      <BulkAssignmentDialog
        open={isBulkDialogOpen}
        onOpenChange={setIsBulkDialogOpen}
        users={users}
        onComplete={handleBulkAssignmentComplete}
        currentUserRole={currentUser?.role}
        currentUserRegionId={currentUser?.regionId}
        currentUserServiceCenterId={currentUser?.serviceCenterId}
      />
    </div>
  )
}
