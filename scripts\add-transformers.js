// This script adds transformers to the mock data
const { transformerService } = require('../services/transformer-service');

async function addTransformers() {
  try {
    // Get current transformers
    const currentTransformers = await transformerService.getAllTransformers();
    console.log(`Current number of transformers: ${currentTransformers.length}`);
    
    // Calculate how many more transformers we need
    const targetCount = 32;
    const neededCount = targetCount - currentTransformers.length;
    
    if (neededCount <= 0) {
      console.log(`Already have ${currentTransformers.length} transformers, no need to add more.`);
      return;
    }
    
    console.log(`Adding ${neededCount} more transformers...`);
    
    // Generate and add new transformers
    const newTransformers = await transformerService.generateRandomTransformers(neededCount);
    console.log(`Added ${newTransformers.length} new transformers.`);
    
    // Verify the total count
    const updatedTransformers = await transformerService.getAllTransformers();
    console.log(`Total transformers now: ${updatedTransformers.length}`);
  } catch (error) {
    console.error('Error adding transformers:', error);
  }
}

addTransformers();
