'use client'

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/src/components/ui/card'
import { Badge } from '@/src/components/ui/badge'
import { Progress } from '@/src/components/ui/progress'
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>hart3, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle
} from 'lucide-react'

interface ChartData {
  transformerStatus: Array<{
    name: string
    value: number
    color: string
  }>
  monthlyBurns: Array<{
    month: string
    burns: number
    maintenance: number
    replacements: number
  }>
  maintenanceTypes: Array<{
    type: string
    count: number
    percentage: number
  }>
  regionalPerformance: Array<{
    region: string
    operational: number
    burned: number
    maintenance: number
  }>
}

interface TransformerChartsProps {
  data: ChartData
  overview: {
    totalTransformers: number
    operationalTransformers: number
    burnedTransformers: number
    underMaintenance: number
  }
}

export function TransformerStatusPieChart({ data, overview }: TransformerChartsProps) {
  const total = overview.totalTransformers || 1
  const operational = overview.operationalTransformers || 0
  const burned = overview.burnedTransformers || 0
  const maintenance = overview.underMaintenance || 0
  const offline = total - operational - burned - maintenance

  // Calculate angles for pie chart
  const operationalAngle = (operational / total) * 360
  const burnedAngle = (burned / total) * 360
  const maintenanceAngle = (maintenance / total) * 360
  const offlineAngle = (offline / total) * 360

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <PieChart className="mr-2 h-5 w-5" />
          Transformer Status Distribution
        </CardTitle>
        <CardDescription>
          Current status breakdown of all distribution transformers
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Visual Pie Chart */}
          <div className="flex items-center justify-center">
            <div className="relative w-40 h-40">
              <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                {/* Operational */}
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke="#22c55e"
                  strokeWidth="20"
                  strokeDasharray={`${(operational / total) * 251.2} 251.2`}
                  strokeDashoffset="0"
                />
                {/* Burned */}
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke="#ef4444"
                  strokeWidth="20"
                  strokeDasharray={`${(burned / total) * 251.2} 251.2`}
                  strokeDashoffset={`-${(operational / total) * 251.2}`}
                />
                {/* Maintenance */}
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke="#f97316"
                  strokeWidth="20"
                  strokeDasharray={`${(maintenance / total) * 251.2} 251.2`}
                  strokeDashoffset={`-${((operational + burned) / total) * 251.2}`}
                />
                {/* Offline */}
                {offline > 0 && (
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    fill="none"
                    stroke="#6b7280"
                    strokeWidth="20"
                    strokeDasharray={`${(offline / total) * 251.2} 251.2`}
                    strokeDashoffset={`-${((operational + burned + maintenance) / total) * 251.2}`}
                  />
                )}
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-2xl font-bold">{total}</div>
                  <div className="text-xs text-muted-foreground">Total</div>
                </div>
              </div>
            </div>
          </div>

          {/* Legend and Statistics */}
          <div className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">Operational</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-bold">{operational}</div>
                  <div className="text-xs text-muted-foreground">
                    {((operational / total) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Flame className="h-4 w-4 text-red-500" />
                  <span className="text-sm font-medium">Burned</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-bold text-red-600">{burned}</div>
                  <div className="text-xs text-muted-foreground">
                    {((burned / total) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Wrench className="h-4 w-4 text-orange-500" />
                  <span className="text-sm font-medium">Under Maintenance</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-bold text-orange-600">{maintenance}</div>
                  <div className="text-xs text-muted-foreground">
                    {((maintenance / total) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>

              {offline > 0 && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">Offline</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-600">{offline}</div>
                    <div className="text-xs text-muted-foreground">
                      {((offline / total) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Key Insights */}
            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Key Insights</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span>Service Rate</span>
                  <Badge variant="secondary">
                    {((operational / total) * 100).toFixed(1)}%
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span>Critical Issues</span>
                  <Badge variant={burned > 0 ? "destructive" : "secondary"}>
                    {burned} units
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span>Maintenance Load</span>
                  <Badge variant="outline">
                    {maintenance} units
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function MonthlyTrendsChart({ data }: { data: ChartData }) {
  const maxValue = Math.max(
    ...data.monthlyBurns.map(item => Math.max(item.burns, item.maintenance, item.replacements))
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <BarChart3 className="mr-2 h-5 w-5" />
          Monthly Trends Analysis
        </CardTitle>
        <CardDescription>
          Transformer burns, maintenance activities, and replacements over time
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Chart Legend */}
          <div className="flex items-center justify-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span>Burns</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span>Maintenance</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span>Replacements</span>
            </div>
          </div>

          {/* Bar Chart */}
          <div className="space-y-4">
            {data.monthlyBurns.map((month) => (
              <div key={month.month} className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="w-12 font-medium">{month.month}</span>
                  <div className="flex space-x-4 text-xs">
                    <span className="text-red-600 w-8 text-right">{month.burns}</span>
                    <span className="text-orange-600 w-8 text-right">{month.maintenance}</span>
                    <span className="text-blue-600 w-8 text-right">{month.replacements}</span>
                  </div>
                </div>
                
                {/* Stacked Bar */}
                <div className="flex space-x-1 h-6">
                  <div 
                    className="bg-red-500 rounded-sm flex items-center justify-center text-white text-xs"
                    style={{ width: `${(month.burns / (maxValue * 3)) * 100}%`, minWidth: month.burns > 0 ? '20px' : '0' }}
                  >
                    {month.burns > 0 && month.burns}
                  </div>
                  <div 
                    className="bg-orange-500 rounded-sm flex items-center justify-center text-white text-xs"
                    style={{ width: `${(month.maintenance / (maxValue * 3)) * 100}%`, minWidth: month.maintenance > 0 ? '20px' : '0' }}
                  >
                    {month.maintenance > 0 && month.maintenance}
                  </div>
                  <div 
                    className="bg-blue-500 rounded-sm flex items-center justify-center text-white text-xs"
                    style={{ width: `${(month.replacements / (maxValue * 3)) * 100}%`, minWidth: month.replacements > 0 ? '20px' : '0' }}
                  >
                    {month.replacements > 0 && month.replacements}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Summary Statistics */}
          <div className="grid grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className="text-lg font-bold text-red-600">
                {data.monthlyBurns.reduce((sum, month) => sum + month.burns, 0)}
              </div>
              <div className="text-xs text-muted-foreground">Total Burns</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-orange-600">
                {data.monthlyBurns.reduce((sum, month) => sum + month.maintenance, 0)}
              </div>
              <div className="text-xs text-muted-foreground">Total Maintenance</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">
                {data.monthlyBurns.reduce((sum, month) => sum + month.replacements, 0)}
              </div>
              <div className="text-xs text-muted-foreground">Total Replacements</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function MaintenanceTypesChart({ data }: { data: ChartData }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Maintenance Types Distribution</CardTitle>
        <CardDescription>
          Breakdown of maintenance activities by type
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.maintenanceTypes.map((item) => (
            <div key={item.type} className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{item.type}</span>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-bold">{item.count}</span>
                  <span className="text-xs text-muted-foreground">
                    ({item.percentage.toFixed(1)}%)
                  </span>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className={`h-3 rounded-full transition-all duration-500 ${
                    item.type === 'Preventive' ? 'bg-green-500' :
                    item.type === 'Corrective' ? 'bg-yellow-500' :
                    item.type === 'Emergency' ? 'bg-red-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${item.percentage}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-green-600">
                {data.maintenanceTypes.find(t => t.type === 'Preventive')?.count || 0}
              </div>
              <div className="text-xs text-muted-foreground">Preventive Tasks</div>
            </div>
            <div>
              <div className="text-lg font-bold text-red-600">
                {data.maintenanceTypes.find(t => t.type === 'Emergency')?.count || 0}
              </div>
              <div className="text-xs text-muted-foreground">Emergency Tasks</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
