"use client"

import { useState, useEffect } from "react"
import { Search, Zap, Map, Wrench, AlertTriangle, Users, FileText, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter
} from "@/src/components/ui/dialog"
import { ScrollArea } from "@/src/components/ui/scroll-area"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/src/components/ui/tabs"
import { useRouter } from "next/navigation"
import { useToast } from "@/src/components/ui/use-toast"
import { Badge } from "@/src/components/ui/badge"
import { cn } from "@/src/lib/utils"
import { useLanguage } from "@/src/contexts/language-context"

// Define search result types
interface SearchResult {
  id: string
  title: string
  description: string
  type: "transformer" | "maintenance" | "alert" | "user" | "report" | "location"
  icon: React.ReactNode
  url: string
}

// Mock search results
const mockSearchResults: SearchResult[] = [
  // Transformers
  {
    id: "TRF-0945",
    title: "TRF-0945",
    description: "Distribution transformer in West District",
    type: "transformer",
    icon: <Zap className="h-4 w-4" />,
    url: "/transformers/TRF-0945"
  },
  {
    id: "TRF-1187",
    title: "TRF-1187",
    description: "Distribution transformer in Central Area",
    type: "transformer",
    icon: <Zap className="h-4 w-4" />,
    url: "/transformers/TRF-1187"
  },
  {
    id: "TRF-0562",
    title: "TRF-0562",
    description: "Distribution transformer in East Zone",
    type: "transformer",
    icon: <Zap className="h-4 w-4" />,
    url: "/transformers/TRF-0562"
  },

  // Maintenance
  {
    id: "MNT-2345",
    title: "Maintenance #MNT-2345",
    description: "Scheduled maintenance for TRF-0945",
    type: "maintenance",
    icon: <Wrench className="h-4 w-4" />,
    url: "/maintenance/records/MNT-2345"
  },
  {
    id: "MNT-2346",
    title: "Maintenance #MNT-2346",
    description: "Emergency maintenance for TRF-1187",
    type: "maintenance",
    icon: <Wrench className="h-4 w-4" />,
    url: "/maintenance/records/MNT-2346"
  },

  // Alerts
  {
    id: "ALT-1024",
    title: "Alert #ALT-1024",
    description: "Temperature exceeding normal range in TRF-0945",
    type: "alert",
    icon: <AlertTriangle className="h-4 w-4" />,
    url: "/alerts/ALT-1024"
  },
  {
    id: "ALT-1023",
    title: "Alert #ALT-1023",
    description: "Oil level critically low in TRF-1187",
    type: "alert",
    icon: <AlertTriangle className="h-4 w-4" />,
    url: "/alerts/ALT-1023"
  },

  // Users
  {
    id: "USR-001",
    title: "Abebe Kebede",
    description: "Super Admin - Head Office",
    type: "user",
    icon: <Users className="h-4 w-4" />,
    url: "/users/USR-001"
  },
  {
    id: "USR-002",
    title: "Tigist Haile",
    description: "National Asset Manager - Head Office",
    type: "user",
    icon: <Users className="h-4 w-4" />,
    url: "/users/USR-002"
  },

  // Reports
  {
    id: "RPT-001",
    title: "Monthly Transformer Status Report",
    description: "Status report for all transformers - April 2025",
    type: "report",
    icon: <FileText className="h-4 w-4" />,
    url: "/reports/RPT-001"
  },
  {
    id: "RPT-002",
    title: "Maintenance Efficiency Report",
    description: "Analysis of maintenance efficiency - Q1 2025",
    type: "report",
    icon: <FileText className="h-4 w-4" />,
    url: "/reports/RPT-002"
  },

  // Locations
  {
    id: "LOC-001",
    title: "West District",
    description: "Service area with 245 transformers",
    type: "location",
    icon: <Map className="h-4 w-4" />,
    url: "/map?region=west-district"
  },
  {
    id: "LOC-002",
    title: "Central Area",
    description: "Service area with 187 transformers",
    type: "location",
    icon: <Map className="h-4 w-4" />,
    url: "/map?region=central-area"
  }
];

export function GlobalSearch() {
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [results, setResults] = useState<SearchResult[]>([])
  const router = useRouter()
  const { toast } = useToast()
  const { t } = useLanguage()

  // Handle keyboard shortcut to open search
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "k") {
        e.preventDefault()
        setOpen(true)
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [])

  // Filter results based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    const query = searchQuery.toLowerCase()
    const filtered = mockSearchResults.filter(
      result =>
        result.title.toLowerCase().includes(query) ||
        result.description.toLowerCase().includes(query)
    )

    setResults(filtered)
  }, [searchQuery])

  // Filter results based on active tab
  const getFilteredResults = () => {
    if (activeTab === "all") return results
    return results.filter(result => result.type === activeTab)
  }

  // Handle result click
  const handleResultClick = (result: SearchResult) => {
    setOpen(false)
    toast({
      title: "Navigating",
      description: `Going to: ${result.title}`,
    })
    router.push(result.url)
  }

  // Get count by type
  const getCountByType = (type: string) => {
    if (type === "all") return results.length
    return results.filter(result => result.type === type).length
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full md:w-[200px] lg:w-[300px] justify-start text-muted-foreground hidden md:flex">
          <Search className="mr-2 h-4 w-4" />
          <span>{t("search")}</span>
          <kbd className="pointer-events-none ml-auto inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground">
            <span className="text-xs">⌘</span>K
          </kbd>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px] p-0">
        <DialogHeader className="px-4 pt-4 pb-0">
          <div className="flex items-center gap-2 border rounded-md px-3 py-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              className="flex-1 border-0 p-0 shadow-none focus-visible:ring-0"
              placeholder={t("search_transformers")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              autoFocus
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5"
                onClick={() => setSearchQuery("")}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Clear</span>
              </Button>
            )}
          </div>
        </DialogHeader>

        {results.length > 0 ? (
          <div className="px-2 pb-4">
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mt-2">
              <TabsList className="w-full px-2 justify-start">
                <TabsTrigger value="all" className="flex items-center gap-1">
                  {t("view_all")}
                  <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
                    {getCountByType("all")}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="transformer" className="flex items-center gap-1">
                  {t("transformers")}
                  <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
                    {getCountByType("transformer")}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="alert" className="flex items-center gap-1">
                  {t("alerts")}
                  <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
                    {getCountByType("alert")}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="maintenance" className="flex items-center gap-1">
                  {t("maintenance")}
                  <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
                    {getCountByType("maintenance")}
                  </Badge>
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-2">
                <ScrollArea className="h-[300px]">
                  <div className="flex flex-col gap-1 p-1">
                    {getFilteredResults().map((result) => (
                      <button
                        key={result.id}
                        className="flex items-start gap-3 rounded-md p-3 text-left transition-colors hover:bg-muted"
                        onClick={() => handleResultClick(result)}
                      >
                        <div className={cn(
                          "mt-1 flex h-6 w-6 items-center justify-center rounded-md",
                          result.type === "transformer" && "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300",
                          result.type === "maintenance" && "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300",
                          result.type === "alert" && "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300",
                          result.type === "user" && "bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300",
                          result.type === "report" && "bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300",
                          result.type === "location" && "bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300",
                        )}>
                          {result.icon}
                        </div>
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium">{result.title}</p>
                          <p className="text-xs text-muted-foreground line-clamp-1">
                            {result.description}
                          </p>
                        </div>
                      </button>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>
        ) : searchQuery ? (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <div className="rounded-full bg-muted p-3 mb-3">
              <Search className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-sm font-medium">{t("no_results_found")}</h3>
            <p className="text-xs text-muted-foreground mt-1">
              {t("no_results_for")} "{searchQuery}". {t("try_different_search")}
            </p>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <div className="rounded-full bg-muted p-3 mb-3">
              <Search className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-sm font-medium">{t("search_system")}</h3>
            <p className="text-xs text-muted-foreground mt-1">
              {t("start_typing")}
            </p>
          </div>
        )}

        <DialogFooter className="flex items-center justify-between p-4 pt-0">
          <div className="text-xs text-muted-foreground">
            Press <kbd className="rounded border bg-muted px-1 text-xs">↑</kbd> <kbd className="rounded border bg-muted px-1 text-xs">↓</kbd> to navigate
          </div>
          <div className="text-xs text-muted-foreground">
            Press <kbd className="rounded border bg-muted px-1 text-xs">Enter</kbd> to select
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
