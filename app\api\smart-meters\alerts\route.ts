import { NextResponse } from "next/server"
import { smartMeterService } from "@/src/services/smart-meter-service"

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  
  // Check if a meter ID is provided
  const meterId = searchParams.get("meterId")
  
  if (meterId) {
    // Get alerts for a specific meter
    const alerts = await smartMeterService.getAlertsByMeterId(meterId)
    return NextResponse.json(alerts)
  }
  
  // Check for filter parameters
  const severity = searchParams.get("severity")
  const type = searchParams.get("type")
  const status = searchParams.get("status")
  
  if (severity || type || status) {
    const results = await smartMeterService.filterAlerts({
      severity: severity || undefined,
      type: type || undefined,
      status: status || undefined
    })
    
    return NextResponse.json(results)
  }
  
  // Get all alerts
  const alerts = await smartMeterService.getAllAlerts()
  return NextResponse.json(alerts)
}
