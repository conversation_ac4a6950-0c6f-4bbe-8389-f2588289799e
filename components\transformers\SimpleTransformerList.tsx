'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Search, Download, Plus, Eye, RefreshCw } from 'lucide-react'
import { Button } from '@/src/components/ui/button'
import { Input } from '@/src/components/ui/input'
import { TransformerDataStandardizer } from '@/src/services/transformer-data-standardizer'

interface Transformer {
  id: string
  code: string
  name: string
  location: string
  kvaRating: number
  installation: string
  status: string
}

export default function SimpleTransformerList() {
  const router = useRouter()
  const [transformers, setTransformers] = useState<Transformer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [error, setError] = useState<string | null>(null)

  const loadTransformers = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🔄 SimpleList: Loading transformers...')
      const response = await fetch('/api/mysql/transformers')

      console.log('📊 SimpleList: Response status:', response.status)

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`)
      }

      const data = await response.json()
      console.log('📊 SimpleList: API Response:', data)

      if (!data.success) {
        throw new Error('API returned success: false')
      }

      if (!data.data || !data.data.transformers) {
        throw new Error('Invalid response structure')
      }

      // Transform the data using standardizer
      const transformedData = data.data.transformers.map((t: any) => {
        const standardized = TransformerDataStandardizer.standardize(t)
        return TransformerDataStandardizer.formatForDisplay(standardized, 'list')
      })

      console.log(`✅ SimpleList: Transformed ${transformedData.length} transformers`)
      setTransformers(transformedData)

    } catch (err) {
      console.error('❌ SimpleList: Error:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadTransformers()
  }, [])

  const filteredTransformers = transformers.filter(transformer =>
    transformer.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transformer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transformer.location.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'operational': return 'bg-green-100 text-green-800'
      case 'maintenance': return 'bg-yellow-100 text-yellow-800'
      case 'critical':
      case 'burnt': return 'bg-red-100 text-red-800'
      case 'warning': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Transformers</h1>
          <p className="text-gray-600 mt-1">Manage your transformer inventory</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={loadTransformers}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button
            className="bg-orange-500 hover:bg-orange-600"
            onClick={() => router.push('/transformers/add')}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Transformer
          </Button>
        </div>
      </div>

      {/* Status Info */}
      <div className="mb-4 p-3 bg-blue-50 rounded-lg text-sm">
        <strong>Status:</strong>
        {loading ? ' Loading...' : error ? ` Error: ${error}` : ` Loaded ${transformers.length} transformers, showing ${filteredTransformers.length} after filter`}
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative w-80">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search transformers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg shadow">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p>Loading transformers...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <div className="text-red-600 mb-4">❌ Error loading transformers</div>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={loadTransformers} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        ) : filteredTransformers.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500">
              {transformers.length === 0 ? 'No transformers found' : 'No transformers match your search'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Code</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Name</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Location</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">KVA</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Installation</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                  <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Action</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredTransformers.map((transformer) => (
                  <tr key={transformer.id} className="hover:bg-gray-50">
                    <td className="py-4 px-6 font-medium text-gray-900">{transformer.code}</td>
                    <td className="py-4 px-6 text-gray-900">{transformer.name}</td>
                    <td className="py-4 px-6 text-gray-600">{transformer.location}</td>
                    <td className="py-4 px-6 text-gray-900">{transformer.kvaRating}</td>
                    <td className="py-4 px-6 text-gray-600">{transformer.installation}</td>
                    <td className="py-4 px-6">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transformer.status)}`}>
                        {transformer.status}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/transformers/${transformer.id}`)}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}
