{"name": "eeu-transformer-management", "version": "2.0.0", "description": "Ethiopian Electric Utility - Digital Transformer Management System", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf .next out dist", "clean:deps": "rm -rf node_modules package-lock.json pnpm-lock.yaml", "clean:all": "npm run clean && npm run clean:deps", "db:test": "npx tsx -e \"import('./src/lib/database.ts').then(db => db.testConnection().then(r => console.log('DB Connection:', r ? 'OK' : 'FAILED')))\"", "db:stats": "npx tsx -e \"import('./src/lib/database.ts').then(db => db.getDatabaseStats().then(s => console.log('DB Stats:', s)))\"", "build:analyze": "ANALYZE=true npm run build", "build:production": "NODE_ENV=production npm run build"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "better-sqlite3": "^9.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "jspdf": "latest", "jspdf-autotable": "3.8.2", "leaflet": "^1.9.4", "lucide-react": "^0.454.0", "mysql2": "^3.6.5", "next": "^14.1.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.54.1", "react-leaflet": "^5.0.0", "react-leaflet-cluster": "^2.1.0", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@next/bundle-analyzer": "^15.2.4", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.0", "@types/react": "^18.2.0", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.0", "@types/uuid": "^9.0.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "uuid": "^9.0.1"}}