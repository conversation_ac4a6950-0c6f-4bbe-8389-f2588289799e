"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { MaintenanceTable } from "@/components/maintenance-table"
import { Button } from "@/src/components/ui/button"
import { Calendar as CalendarIcon, Plus, Filter, Download, Search, BarChart3 } from "lucide-react"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/src/components/ui/tabs"
import { Input } from "@/src/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { ScheduleMaintenanceDialog } from "@/components/schedule-maintenance-dialog"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/src/components/ui/dialog"
import { useToast } from "@/src/components/ui/use-toast"
import { MaintenancePerformanceChart } from "@/components/maintenance-performance-chart"
import { jsPDF } from "jspdf"
// Import jspdf-autotable directly with require to avoid bundling issues
const autoTable = require('jspdf-autotable')
import { format } from "date-fns"

import type { ExtendedMaintenanceRecord } from "@/src/services/maintenance-service"
import {
  getAllMaintenanceRecords,
  getUpcomingMaintenanceRecords,
  getInProgressMaintenanceRecords,
  getCompletedMaintenanceRecords,
  filterMaintenanceRecordsByType,
  searchMaintenanceRecords,
  getMaintenanceStatistics
} from "@/src/services/maintenance-service"

export function MaintenanceContent() {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)
  const [isCalendarViewOpen, setIsCalendarViewOpen] = useState(false)
  const [isStatsDialogOpen, setIsStatsDialogOpen] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // Filtered records for each tab
  const [upcomingRecords, setUpcomingRecords] = useState<ExtendedMaintenanceRecord[]>([])
  const [inProgressRecords, setInProgressRecords] = useState<ExtendedMaintenanceRecord[]>([])
  const [completedRecords, setCompletedRecords] = useState<ExtendedMaintenanceRecord[]>([])
  const [allRecords, setAllRecords] = useState<ExtendedMaintenanceRecord[]>([])

  // Load and filter records when search query, filter type, or refresh trigger changes
  useEffect(() => {
    // Get records for each tab
    const upcoming = getUpcomingMaintenanceRecords()
    const inProgress = getInProgressMaintenanceRecords()
    const completed = getCompletedMaintenanceRecords()
    const all = getAllMaintenanceRecords()

    // Apply type filter
    const filteredUpcoming = filterMaintenanceRecordsByType(upcoming, filterType)
    const filteredInProgress = filterMaintenanceRecordsByType(inProgress, filterType)
    const filteredCompleted = filterMaintenanceRecordsByType(completed, filterType)
    const filteredAll = filterMaintenanceRecordsByType(all, filterType)

    // Apply search filter
    setUpcomingRecords(searchMaintenanceRecords(filteredUpcoming, searchQuery))
    setInProgressRecords(searchMaintenanceRecords(filteredInProgress, searchQuery))
    setCompletedRecords(searchMaintenanceRecords(filteredCompleted, searchQuery))
    setAllRecords(searchMaintenanceRecords(filteredAll, searchQuery))
  }, [searchQuery, filterType, refreshTrigger])

  // Handle record updates (refresh data)
  const handleRecordUpdate = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  // Handle exporting maintenance records to PDF
  const handleExportToPDF = () => {
    const doc = new jsPDF()
    const pageWidth = doc.internal.pageSize.getWidth()

    // Add header
    doc.setFontSize(18)
    doc.setTextColor(0, 102, 0) // EEU green color
    doc.text("Ethiopia Electric Utility", pageWidth / 2, 15, { align: "center" })

    doc.setFontSize(14)
    doc.setTextColor(0, 0, 0)
    doc.text("Maintenance Records Report", pageWidth / 2, 25, { align: "center" })
    doc.text(`Generated on: ${format(new Date(), "PPP")}`, pageWidth / 2, 35, { align: "center" })

    // Add maintenance statistics
    const stats = getMaintenanceStatistics()
    doc.setFontSize(12)
    doc.text("Maintenance Statistics", 14, 45)

    const statsData = [
      ["Total Records", stats.total.toString()],
      ["Scheduled", stats.scheduled.toString()],
      ["In Progress", stats.inProgress.toString()],
      ["Completed", stats.completed.toString()],
      ["Cancelled", stats.cancelled.toString()],
      ["High Priority", stats.highPriority.toString()],
      ["Medium Priority", stats.mediumPriority.toString()],
      ["Low Priority", stats.lowPriority.toString()],
    ]

    autoTable(doc, {
      startY: 50,
      head: [["Metric", "Count"]],
      body: statsData,
      theme: "grid",
      headStyles: { fillColor: [0, 102, 0] },
    })

    // Add maintenance records
    const records = getAllMaintenanceRecords()

    doc.addPage()
    doc.setFontSize(14)
    doc.text("Maintenance Records", 14, 15)

    const maintenanceData = records.map((record) => [
      record.transformerSerialNumber,
      record.location,
      record.type,
      format(new Date(record.scheduledDate), "MMM d, yyyy"),
      record.status,
      record.assignedTo,
      record.priority,
    ])

    autoTable(doc, {
      startY: 20,
      head: [["Transformer ID", "Location", "Type", "Scheduled Date", "Status", "Assigned To", "Priority"]],
      body: maintenanceData,
      theme: "grid",
      headStyles: { fillColor: [0, 102, 0] },
    })

    // Save the PDF
    doc.save("maintenance-records.pdf")

    toast({
      title: "Export successful",
      description: "Maintenance records have been exported to PDF.",
    })
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Maintenance Management</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsCalendarViewOpen(true)}>
            <CalendarIcon className="mr-2 h-4 w-4" />
            Calendar View
          </Button>
          <Button variant="outline" size="sm" onClick={() => setIsStatsDialogOpen(true)}>
            <BarChart3 className="mr-2 h-4 w-4" />
            Statistics
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportToPDF}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button size="sm" onClick={() => setIsScheduleDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Schedule Maintenance
          </Button>
        </div>
      </div>

      <Tabs defaultValue="upcoming" className="space-y-4">
        <TabsList>
          <TabsTrigger value="upcoming">Upcoming ({upcomingRecords.length})</TabsTrigger>
          <TabsTrigger value="inprogress">In Progress ({inProgressRecords.length})</TabsTrigger>
          <TabsTrigger value="completed">Completed ({completedRecords.length})</TabsTrigger>
          <TabsTrigger value="all">All Records ({allRecords.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Upcoming Maintenance</CardTitle>
              <CardDescription>Scheduled maintenance activities for the next 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search maintenance records..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <div className="flex gap-2">
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="routine">Routine Inspection</SelectItem>
                      <SelectItem value="repair">Repair</SelectItem>
                      <SelectItem value="oil">Oil Sampling</SelectItem>
                      <SelectItem value="full">Full Maintenance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <MaintenanceTable extended records={upcomingRecords} onRecordUpdate={handleRecordUpdate} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inprogress" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>In Progress Maintenance</CardTitle>
              <CardDescription>Currently ongoing maintenance activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search maintenance records..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <div className="flex gap-2">
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="routine">Routine Inspection</SelectItem>
                      <SelectItem value="repair">Repair</SelectItem>
                      <SelectItem value="oil">Oil Sampling</SelectItem>
                      <SelectItem value="full">Full Maintenance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <MaintenanceTable extended records={inProgressRecords} onRecordUpdate={handleRecordUpdate} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Completed Maintenance</CardTitle>
              <CardDescription>Maintenance activities completed in the last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search maintenance records..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <div className="flex gap-2">
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="routine">Routine Inspection</SelectItem>
                      <SelectItem value="repair">Repair</SelectItem>
                      <SelectItem value="oil">Oil Sampling</SelectItem>
                      <SelectItem value="full">Full Maintenance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <MaintenanceTable extended records={completedRecords} onRecordUpdate={handleRecordUpdate} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Maintenance Records</CardTitle>
              <CardDescription>Complete history of maintenance activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 sm:flex-row mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search maintenance records..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <div className="flex gap-2">
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-[160px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="routine">Routine Inspection</SelectItem>
                      <SelectItem value="repair">Repair</SelectItem>
                      <SelectItem value="oil">Oil Sampling</SelectItem>
                      <SelectItem value="full">Full Maintenance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <MaintenanceTable extended records={allRecords} onRecordUpdate={handleRecordUpdate} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Schedule Maintenance Dialog */}
      <ScheduleMaintenanceDialog
        open={isScheduleDialogOpen}
        onOpenChange={setIsScheduleDialogOpen}
        onScheduled={handleRecordUpdate}
      />

      {/* Calendar View Dialog */}
      <Dialog open={isCalendarViewOpen} onOpenChange={setIsCalendarViewOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Maintenance Calendar</DialogTitle>
            <DialogDescription>
              View scheduled maintenance activities in calendar format
            </DialogDescription>
          </DialogHeader>
          <div className="p-4 border rounded-md">
            <div className="text-center p-8 text-muted-foreground">
              <CalendarIcon className="mx-auto h-12 w-12 mb-4" />
              <h3 className="text-lg font-medium">Calendar View</h3>
              <p className="mt-2">
                Calendar integration will be available in the next update. This will allow you to view and manage
                maintenance schedules in a calendar format.
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Statistics Dialog */}
      <Dialog open={isStatsDialogOpen} onOpenChange={setIsStatsDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Maintenance Statistics</DialogTitle>
            <DialogDescription>
              View maintenance performance metrics and statistics
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            <div className="h-[300px]">
              <MaintenancePerformanceChart />
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(getMaintenanceStatistics()).map(([key, value]) => (
                <Card key={key}>
                  <CardHeader className="p-4 pb-2">
                    <CardDescription className="capitalize">{key.replace(/([A-Z])/g, ' $1')}</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <p className="text-2xl font-bold">{value}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
