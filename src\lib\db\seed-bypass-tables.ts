import mysql from 'mysql2/promise'

// Database configuration
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: 'dtms_eeu_db',
  multipleStatements: true
}

export async function seedBypassTables() {
  let connection: mysql.Connection | null = null
  
  try {
    console.log('🔄 Connecting to database for seeding bypass tables...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // First, find the actual table names by looking for tables with timestamp suffixes
    console.log('🔍 Finding actual table names...')
    const [tables] = await connection.query('SHOW TABLES')
    const tableList = (tables as any[]).map(row => Object.values(row)[0])
    
    // Find the tables with timestamp suffixes
    const regionsTable = tableList.find(name => name.startsWith('regions_v'))
    const serviceCentersTable = tableList.find(name => name.startsWith('service_centers_v'))
    const usersTable = tableList.find(name => name.startsWith('users_v'))
    const transformersTable = tableList.find(name => name.startsWith('transformers_v'))
    const alertsTable = tableList.find(name => name.startsWith('alerts_v'))
    const maintenanceTable = tableList.find(name => name.startsWith('maintenance_schedules_v'))
    const notificationsTable = tableList.find(name => name.startsWith('notifications_v'))
    const performanceTable = tableList.find(name => name.startsWith('performance_metrics_v'))
    const weatherTable = tableList.find(name => name.startsWith('weather_data_v'))

    if (!regionsTable || !serviceCentersTable || !usersTable || !transformersTable) {
      throw new Error('Could not find required tables with timestamp suffixes')
    }

    console.log(`📋 Found tables: ${regionsTable}, ${serviceCentersTable}, ${usersTable}, ${transformersTable}`)

    // Check if data already exists
    const [existingRegions] = await connection.query(`SELECT COUNT(*) as count FROM ${regionsTable}`)
    const regionCount = (existingRegions as any)[0].count

    if (regionCount > 0) {
      console.log('⚠️ Data already exists, skipping seeding')
      return {
        success: true,
        message: 'Data already exists in bypass tables',
        existingRecords: regionCount
      }
    }

    console.log('🔄 Seeding Ethiopian regions...')
    
    // Seed Ethiopian regions
    const regions = [
      { name: 'Addis Ababa', code: 'AA', population: 3500000, area_km2: 527.0 },
      { name: 'Oromia', code: 'OR', population: 37000000, area_km2: 353006.81 },
      { name: 'Amhara', code: 'AM', population: 21000000, area_km2: 154708.96 },
      { name: 'Tigray', code: 'TI', population: 5500000, area_km2: 50078.64 },
      { name: 'SNNP', code: 'SN', population: 20000000, area_km2: 105887.18 },
      { name: 'Somali', code: 'SO', population: 5500000, area_km2: 279252.0 },
      { name: 'Afar', code: 'AF', population: 1800000, area_km2: 96707.0 },
      { name: 'Benishangul-Gumuz', code: 'BG', population: 1100000, area_km2: 50699.0 },
      { name: 'Gambela', code: 'GA', population: 435000, area_km2: 25802.0 },
      { name: 'Harari', code: 'HA', population: 250000, area_km2: 311.0 },
      { name: 'Dire Dawa', code: 'DD', population: 500000, area_km2: 1213.0 }
    ]

    for (const region of regions) {
      await connection.query(
        `INSERT INTO ${regionsTable} (name, code, population, area_km2) VALUES (?, ?, ?, ?)`,
        [region.name, region.code, region.population, region.area_km2]
      )
    }
    console.log(`✅ Seeded ${regions.length} regions`)

    console.log('🔄 Seeding service centers...')
    
    // Seed service centers
    const serviceCenters = [
      { name: 'Addis Ababa Central', code: 'AAC001', region_id: 1, address: 'Bole Road, Addis Ababa', phone: '+251-11-123-4567', email: '<EMAIL>', manager_name: 'Alemayehu Tadesse' },
      { name: 'Adama Service Center', code: 'OR001', region_id: 2, address: 'Adama Industrial Zone', phone: '+251-22-111-2222', email: '<EMAIL>', manager_name: 'Bekele Worku' },
      { name: 'Bahir Dar Service Center', code: 'AM001', region_id: 3, address: 'Bahir Dar City Center', phone: '+251-58-220-1234', email: '<EMAIL>', manager_name: 'Chala Negash' },
      { name: 'Mekelle Service Center', code: 'TI001', region_id: 4, address: 'Mekelle Industrial Area', phone: '+251-34-440-5678', email: '<EMAIL>', manager_name: 'Desta Gebru' },
      { name: 'Hawassa Service Center', code: 'SN001', region_id: 5, address: 'Hawassa Lake Side', phone: '+251-46-220-9876', email: '<EMAIL>', manager_name: 'Ephrem Tadele' },
      { name: 'Jijiga Service Center', code: 'SO001', region_id: 6, address: 'Jijiga Main Street', phone: '+251-25-775-4321', email: '<EMAIL>', manager_name: 'Farah Ahmed' },
      { name: 'Semera Service Center', code: 'AF001', region_id: 7, address: 'Semera Administrative Zone', phone: '+251-33-666-1111', email: '<EMAIL>', manager_name: 'Getachew Ali' },
      { name: 'Assosa Service Center', code: 'BG001', region_id: 8, address: 'Assosa Town Center', phone: '+251-57-775-2222', email: '<EMAIL>', manager_name: 'Hailu Berhanu' }
    ]

    for (const center of serviceCenters) {
      await connection.query(
        `INSERT INTO ${serviceCentersTable} (name, code, region_id, address, phone, email, manager_name) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [center.name, center.code, center.region_id, center.address, center.phone, center.email, center.manager_name]
      )
    }
    console.log(`✅ Seeded ${serviceCenters.length} service centers`)

    console.log('🔄 Seeding users...')
    
    // Seed users
    const users = [
      { username: 'admin', email: '<EMAIL>', first_name: 'System', last_name: 'Administrator', phone: '+251-11-000-0001', role: 'super_admin', region_id: 1, service_center_id: 1 },
      { username: 'alemayehu.t', email: '<EMAIL>', first_name: 'Alemayehu', last_name: 'Tadesse', phone: '+251-11-123-4567', role: 'service_center_manager', region_id: 1, service_center_id: 1 },
      { username: 'bekele.w', email: '<EMAIL>', first_name: 'Bekele', last_name: 'Worku', phone: '+251-22-111-2222', role: 'service_center_manager', region_id: 2, service_center_id: 2 },
      { username: 'chala.n', email: '<EMAIL>', first_name: 'Chala', last_name: 'Negash', phone: '+251-58-220-1234', role: 'service_center_manager', region_id: 3, service_center_id: 3 },
      { username: 'desta.g', email: '<EMAIL>', first_name: 'Desta', last_name: 'Gebru', phone: '+251-34-440-5678', role: 'service_center_manager', region_id: 4, service_center_id: 4 },
      { username: 'ephrem.t', email: '<EMAIL>', first_name: 'Ephrem', last_name: 'Tadele', phone: '+251-46-220-9876', role: 'service_center_manager', region_id: 5, service_center_id: 5 },
      { username: 'farah.a', email: '<EMAIL>', first_name: 'Farah', last_name: 'Ahmed', phone: '+251-25-775-4321', role: 'service_center_manager', region_id: 6, service_center_id: 6 },
      { username: 'getachew.a', email: '<EMAIL>', first_name: 'Getachew', last_name: 'Ali', phone: '+251-33-666-1111', role: 'service_center_manager', region_id: 7, service_center_id: 7 },
      { username: 'hailu.b', email: '<EMAIL>', first_name: 'Hailu', last_name: 'Berhanu', phone: '+251-57-775-2222', role: 'service_center_manager', region_id: 8, service_center_id: 8 }
    ]

    for (const user of users) {
      await connection.query(
        `INSERT INTO ${usersTable} (username, email, first_name, last_name, phone, role, region_id, service_center_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [user.username, user.email, user.first_name, user.last_name, user.phone, user.role, user.region_id, user.service_center_id]
      )
    }
    console.log(`✅ Seeded ${users.length} users`)

    console.log('🔄 Seeding transformers...')
    
    // Seed transformers
    const transformers = [
      { serial_number: 'ETH-TR-001', name: 'Addis Ababa Main Transformer', type: 'power', capacity_kva: 50000, voltage_primary: 132000, voltage_secondary: 15000, manufacturer: 'ABB', model: 'ONAN-132kV', year_manufactured: 2020, installation_date: '2020-06-15', location_name: 'Addis Ababa Substation', latitude: 9.0054, longitude: 38.7636, region_id: 1, service_center_id: 1, status: 'operational', efficiency_rating: 98.5, load_factor: 85.0, temperature: 65.0, oil_level: 95.0, last_maintenance: '2024-01-15', next_maintenance: '2024-07-15' },
      { serial_number: 'ETH-TR-002', name: 'Adama Distribution Transformer', type: 'distribution', capacity_kva: 25000, voltage_primary: 66000, voltage_secondary: 11000, manufacturer: 'Siemens', model: 'ONAN-66kV', year_manufactured: 2019, installation_date: '2019-08-20', location_name: 'Adama Industrial Zone', latitude: 8.5400, longitude: 39.2675, region_id: 2, service_center_id: 2, status: 'operational', efficiency_rating: 97.8, load_factor: 78.0, temperature: 62.0, oil_level: 92.0, last_maintenance: '2024-02-10', next_maintenance: '2024-08-10' },
      { serial_number: 'ETH-TR-003', name: 'Bahir Dar Power Transformer', type: 'power', capacity_kva: 40000, voltage_primary: 132000, voltage_secondary: 15000, manufacturer: 'GE', model: 'ONAF-132kV', year_manufactured: 2021, installation_date: '2021-03-10', location_name: 'Bahir Dar Substation', latitude: 11.5942, longitude: 37.3906, region_id: 3, service_center_id: 3, status: 'operational', efficiency_rating: 98.2, load_factor: 82.0, temperature: 58.0, oil_level: 96.0, last_maintenance: '2024-01-20', next_maintenance: '2024-07-20' },
      { serial_number: 'ETH-TR-004', name: 'Mekelle Distribution Unit', type: 'distribution', capacity_kva: 20000, voltage_primary: 66000, voltage_secondary: 11000, manufacturer: 'Schneider', model: 'ONAN-66kV', year_manufactured: 2018, installation_date: '2018-11-05', location_name: 'Mekelle Industrial Area', latitude: 13.4967, longitude: 39.4755, region_id: 4, service_center_id: 4, status: 'warning', efficiency_rating: 96.5, load_factor: 88.0, temperature: 72.0, oil_level: 88.0, last_maintenance: '2023-12-15', next_maintenance: '2024-06-15' },
      { serial_number: 'ETH-TR-005', name: 'Hawassa Regional Transformer', type: 'power', capacity_kva: 35000, voltage_primary: 132000, voltage_secondary: 15000, manufacturer: 'ABB', model: 'ONAF-132kV', year_manufactured: 2020, installation_date: '2020-09-12', location_name: 'Hawassa Substation', latitude: 7.0621, longitude: 38.4755, region_id: 5, service_center_id: 5, status: 'operational', efficiency_rating: 98.0, load_factor: 75.0, temperature: 60.0, oil_level: 94.0, last_maintenance: '2024-02-05', next_maintenance: '2024-08-05' },
      { serial_number: 'ETH-TR-006', name: 'Jijiga Distribution Center', type: 'distribution', capacity_kva: 15000, voltage_primary: 66000, voltage_secondary: 11000, manufacturer: 'Siemens', model: 'ONAN-66kV', year_manufactured: 2019, installation_date: '2019-12-18', location_name: 'Jijiga Main Station', latitude: 9.3500, longitude: 42.8000, region_id: 6, service_center_id: 6, status: 'maintenance', efficiency_rating: 97.2, load_factor: 70.0, temperature: 55.0, oil_level: 90.0, last_maintenance: '2024-03-01', next_maintenance: '2024-09-01' },
      { serial_number: 'ETH-TR-007', name: 'Semera Power Unit', type: 'power', capacity_kva: 30000, voltage_primary: 132000, voltage_secondary: 15000, manufacturer: 'GE', model: 'ONAN-132kV', year_manufactured: 2021, installation_date: '2021-05-25', location_name: 'Semera Substation', latitude: 11.7943, longitude: 41.0058, region_id: 7, service_center_id: 7, status: 'operational', efficiency_rating: 98.3, load_factor: 68.0, temperature: 63.0, oil_level: 93.0, last_maintenance: '2024-01-30', next_maintenance: '2024-07-30' },
      { serial_number: 'ETH-TR-008', name: 'Assosa Distribution Transformer', type: 'distribution', capacity_kva: 18000, voltage_primary: 66000, voltage_secondary: 11000, manufacturer: 'Schneider', model: 'ONAN-66kV', year_manufactured: 2020, installation_date: '2020-07-08', location_name: 'Assosa Industrial Zone', latitude: 10.0696, longitude: 34.5328, region_id: 8, service_center_id: 8, status: 'operational', efficiency_rating: 97.5, load_factor: 73.0, temperature: 59.0, oil_level: 91.0, last_maintenance: '2024-02-20', next_maintenance: '2024-08-20' }
    ]

    for (const transformer of transformers) {
      await connection.query(
        `INSERT INTO ${transformersTable} (serial_number, name, type, capacity_kva, voltage_primary, voltage_secondary, manufacturer, model, year_manufactured, installation_date, location_name, latitude, longitude, region_id, service_center_id, status, efficiency_rating, load_factor, temperature, oil_level, last_maintenance, next_maintenance) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [transformer.serial_number, transformer.name, transformer.type, transformer.capacity_kva, transformer.voltage_primary, transformer.voltage_secondary, transformer.manufacturer, transformer.model, transformer.year_manufactured, transformer.installation_date, transformer.location_name, transformer.latitude, transformer.longitude, transformer.region_id, transformer.service_center_id, transformer.status, transformer.efficiency_rating, transformer.load_factor, transformer.temperature, transformer.oil_level, transformer.last_maintenance, transformer.next_maintenance]
      )
    }
    console.log(`✅ Seeded ${transformers.length} transformers`)

    // Seed some alerts if alerts table exists
    if (alertsTable) {
      console.log('🔄 Seeding alerts...')
      const alerts = [
        { transformer_id: 4, title: 'High Temperature Alert', description: 'Transformer temperature exceeding normal range', severity: 'high', type: 'temperature', status: 'active', priority: 'high', created_by: 1, assigned_to: 4 },
        { transformer_id: 6, title: 'Maintenance Required', description: 'Scheduled maintenance overdue', severity: 'medium', type: 'maintenance', status: 'investigating', priority: 'medium', created_by: 1, assigned_to: 6 },
        { transformer_id: 2, title: 'Load Factor Warning', description: 'Load factor approaching maximum capacity', severity: 'medium', type: 'load', status: 'monitoring', priority: 'medium', created_by: 1, assigned_to: 2 }
      ]

      for (const alert of alerts) {
        await connection.query(
          `INSERT INTO ${alertsTable} (transformer_id, title, description, severity, type, status, priority, created_by, assigned_to) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [alert.transformer_id, alert.title, alert.description, alert.severity, alert.type, alert.status, alert.priority, alert.created_by, alert.assigned_to]
        )
      }
      console.log(`✅ Seeded ${alerts.length} alerts`)
    }

    console.log('✅ Database seeding completed successfully')

    return {
      success: true,
      message: 'Database seeded successfully with Ethiopian transformer data',
      seededData: {
        regions: regions.length,
        serviceCenters: serviceCenters.length,
        users: users.length,
        transformers: transformers.length,
        alerts: alertsTable ? 3 : 0
      }
    }

  } catch (error) {
    console.error('❌ Database seeding failed:', error)
    return {
      success: false,
      message: `Database seeding failed: ${error}`,
      error
    }
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}
