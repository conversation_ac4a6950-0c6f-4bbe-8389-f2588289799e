/**
 * Region repository
 * 
 * This class provides specialized methods for working with region entities.
 */

import { BaseRepository } from './base-repository';
import { Region } from '../schema';

export class RegionRepository extends BaseRepository<Region> {
  constructor() {
    super('regions');
  }
  
  /**
   * Find a region by code
   */
  findByCode(code: string): Region | null {
    return this.findOne({ code });
  }
  
  /**
   * Get regions with transformer counts
   */
  getRegionsWithTransformerCounts(): Region[] {
    return this.getAll();
  }
  
  /**
   * Get region by name (case-insensitive)
   */
  findByName(name: string): Region | null {
    const regions = this.find({});
    return regions.find(region => 
      region.name.toLowerCase() === name.toLowerCase()
    ) || null;
  }
  
  /**
   * Search regions by name
   */
  searchByName(term: string): Region[] {
    const regions = this.find({});
    return regions.filter(region => 
      region.name.toLowerCase().includes(term.toLowerCase())
    );
  }
}

// Export a singleton instance
export const regionRepository = new RegionRepository();
