'use client'

import { useState, useCallback, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card'
import { Button } from '@/src/components/ui/button'
import { Badge } from '@/src/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/src/components/ui/tabs'
import { Progress } from '@/src/components/ui/progress'
import { 
  RefreshCw, 
  Download, 
  Activity, 
  CheckCircle, 
  TrendingUp, 
  AlertTriangle, 
  Clock, 
  Zap, 
  Settings, 
  MapPin 
} from 'lucide-react'
import { toast } from 'sonner'
import { useOptimizedData, useDebounce } from '@/src/hooks/use-optimized-data'
import { CardSkeleton, ProgressiveLoader } from '@/src/components/optimized/lazy-components'

interface AnalyticsData {
  overview: {
    totalTransformers: number
    operationalTransformers: number
    averageLoad: number
    averageEfficiency: number
    systemUptime: number
    efficiencyTrend: 'up' | 'down' | 'stable'
  }
  maintenance: {
    totalSchedules: number
    completedTasks: number
    inProgressTasks: number
    pendingTasks: number
    efficiency: number
    averageCompletionDays: number
  }
  alerts: {
    totalAlerts: number
    activeAlerts: number
    criticalAlerts: number
    highAlerts: number
    mediumAlerts: number
    lowAlerts: number
    resolvedAlerts: number
    resolutionRate: number
    averageResolutionTime: number
  }
  performance: {
    averageUptime: number
    averageEfficiency: number
    averageLoadFactor: number
    monitoredTransformers: number
  }
  regional: Array<{
    name: string
    code: string
    transformerCount: number
    averageLoad: number
    operationalCount: number
    operationalPercentage: number
  }>
  trends: {
    daily: Array<{
      date: string
      alerts: number
      criticalPercentage: number
    }>
  }
  summary: {
    systemHealth: number
    maintenanceBacklog: number
    criticalIssues: number
    overallEfficiency: number
  }
}

export default function DashboardAnalyticsOptimized() {
  const [activeTab, setActiveTab] = useState('overview')
  const debouncedTab = useDebounce(activeTab, 100)

  // Optimized data fetching with caching
  const {
    data,
    loading,
    refreshing,
    error,
    refresh,
    isCached
  } = useOptimizedData<AnalyticsData>(
    useCallback(async () => {
      const response = await fetch('/api/dashboard/analytics')
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch analytics data')
      }
      
      return result.data
    }, []),
    {
      cacheKey: 'dashboard-analytics',
      cacheTTL: 2 * 60 * 1000, // 2 minutes cache
      enableCache: true,
      retryAttempts: 3,
      onError: (error) => {
        console.error('Analytics fetch error:', error)
        toast.error('Failed to load analytics data')
      },
      onSuccess: () => {
        if (!isCached) {
          toast.success('Analytics data loaded successfully')
        }
      }
    }
  )

  // Memoized calculations
  const systemHealthColor = useMemo(() => {
    if (!data) return 'text-muted-foreground'
    const health = data.summary.systemHealth
    if (health >= 90) return 'text-green-600'
    if (health >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }, [data?.summary.systemHealth])

  const criticalIssuesVariant = useMemo(() => {
    if (!data) return 'secondary'
    return data.summary.criticalIssues > 0 ? 'destructive' : 'secondary'
  }, [data?.summary.criticalIssues])

  // Optimized handlers
  const handleRefresh = useCallback(async () => {
    await refresh()
    toast.success('Analytics data refreshed')
  }, [refresh])

  const handleGenerateReport = useCallback(async () => {
    try {
      const response = await fetch('/api/dashboard/analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'generate_report' })
      })

      const result = await response.json()
      if (result.success) {
        toast.success('Analytics report generated successfully')
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast.error('Failed to generate report')
    }
  }, [])

  const handleRefreshMetrics = useCallback(async () => {
    try {
      const response = await fetch('/api/dashboard/analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'refresh_metrics' })
      })

      const result = await response.json()
      if (result.success) {
        toast.success('Metrics refreshed successfully')
        await refresh()
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast.error('Failed to refresh metrics')
    }
  }, [refresh])

  // Loading state with skeletons
  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="space-y-2">
            <div className="h-8 bg-muted rounded w-64 animate-pulse"></div>
            <div className="h-4 bg-muted rounded w-96 animate-pulse"></div>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-9 bg-muted rounded w-24 animate-pulse"></div>
            <div className="h-9 bg-muted rounded w-32 animate-pulse"></div>
            <div className="h-9 bg-muted rounded w-28 animate-pulse"></div>
          </div>
        </div>

        {/* Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <CardSkeleton key={i} showIcon showProgress />
          ))}
        </div>

        {/* Tabs Skeleton */}
        <div className="space-y-4">
          <div className="flex space-x-2">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-10 bg-muted rounded w-20 animate-pulse"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <CardSkeleton key={i} />
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Failed to Load Analytics
          </CardTitle>
          <CardDescription>
            An error occurred while loading the analytics dashboard.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
            <strong>Error:</strong> {error.message}
          </div>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  // No data state
  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No analytics data available</p>
        <Button onClick={handleRefresh} variant="outline" size="sm" className="mt-4">
          <RefreshCw className="mr-2 h-4 w-4" />
          Load Data
        </Button>
      </div>
    )
  }

  return (
    <ProgressiveLoader className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard Analytics</h1>
          <p className="text-muted-foreground">
            Comprehensive system performance and operational insights
            {isCached && <span className="ml-2 text-xs">(Cached)</span>}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleGenerateReport}
          >
            <Download className="mr-2 h-4 w-4" />
            Generate Report
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshMetrics}
          >
            <Activity className="mr-2 h-4 w-4" />
            Refresh Metrics
          </Button>
        </div>
      </div>

      {/* System Health Overview */}
      <ProgressiveLoader delay={100}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Health</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${systemHealthColor}`}>
                {data.summary.systemHealth}%
              </div>
              <Progress value={data.summary.systemHealth} className="mt-2" />
              <p className="text-xs text-muted-foreground mt-2">
                {data.overview.operationalTransformers} of {data.overview.totalTransformers} operational
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overall Efficiency</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.summary.overallEfficiency}%</div>
              <Progress value={data.summary.overallEfficiency} className="mt-2" />
              <p className="text-xs text-muted-foreground mt-2">
                Trend: {data.overview.efficiencyTrend}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Critical Issues</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.summary.criticalIssues}</div>
              <Badge variant={criticalIssuesVariant} className="mt-2">
                {data.summary.criticalIssues > 0 ? 'Attention Required' : 'All Clear'}
              </Badge>
              <p className="text-xs text-muted-foreground mt-2">
                {data.alerts.totalAlerts} total alerts
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Maintenance Backlog</CardTitle>
              <Clock className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.summary.maintenanceBacklog}</div>
              <Badge variant={data.summary.maintenanceBacklog > 10 ? "destructive" : "secondary"} className="mt-2">
                {data.summary.maintenanceBacklog > 10 ? 'High' : 'Normal'}
              </Badge>
              <p className="text-xs text-muted-foreground mt-2">
                {data.maintenance.efficiency}% completion rate
              </p>
            </CardContent>
          </Card>
        </div>
      </ProgressiveLoader>

      {/* Detailed Analytics Tabs */}
      <ProgressiveLoader delay={200}>
        <Tabs value={debouncedTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            <TabsTrigger value="alerts">Alerts</TabsTrigger>
            <TabsTrigger value="regional">Regional</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <ProgressiveLoader delay={50}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      Transformer Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span>Operational</span>
                      <Badge variant="secondary">{data.overview.operationalTransformers}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Average Load</span>
                      <Badge variant="outline">{data.overview.averageLoad}%</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>System Uptime</span>
                      <Badge variant="secondary">{data.overview.systemUptime}%</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Performance Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span>Average Uptime</span>
                      <Badge variant="secondary">{data.performance.averageUptime}%</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Load Factor</span>
                      <Badge variant="outline">{data.performance.averageLoadFactor}%</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Monitored Units</span>
                      <Badge variant="secondary">{data.performance.monitoredTransformers}</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      Maintenance Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span>Completed Tasks</span>
                      <Badge variant="secondary">{data.maintenance.completedTasks}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Pending Tasks</span>
                      <Badge variant="outline">{data.maintenance.pendingTasks}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg. Completion</span>
                      <Badge variant="secondary">{data.maintenance.averageCompletionDays} days</Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </ProgressiveLoader>
          </TabsContent>

          {/* Other tab contents would be similarly optimized */}
          <TabsContent value="regional" className="space-y-4">
            <ProgressiveLoader delay={50}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Regional Distribution
                  </CardTitle>
                  <CardDescription>
                    Transformer distribution and performance by region
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.regional.map((region, index) => (
                      <ProgressiveLoader key={region.code} delay={index * 50}>
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <h4 className="font-medium">{region.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              {region.transformerCount} transformers
                            </p>
                          </div>
                          <div className="text-right space-y-1">
                            <Badge variant="secondary">
                              {region.operationalPercentage}% operational
                            </Badge>
                            <p className="text-sm text-muted-foreground">
                              Avg Load: {region.averageLoad}%
                            </p>
                          </div>
                        </div>
                      </ProgressiveLoader>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </ProgressiveLoader>
          </TabsContent>
        </Tabs>
      </ProgressiveLoader>
    </ProgressiveLoader>
  )
}
