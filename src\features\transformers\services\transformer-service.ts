"use client"

import type { Transformer, TransformerFilter, TransformerStatistics, AbnormalityReport } from "@/src/types/transformer"
import { transformerCache } from "@/src/lib/cache"
import { isValidTransformer, sanitizeTransformer } from "@/src/lib/validation"
import { mockTransformers, mockSwitchgearTeams, mockServiceCenters } from "@/src/services/mock-transformers"

// Export mock transformers for use in other modules
export const originalMockTransformers = mockTransformers;

// Helper function to generate random coordinates within Ethiopia
const generateRandomEthiopianCoordinates = () => {
  // Ethiopia's approximate bounding box
  const minLat = 3.4; // Southern border
  const maxLat = 14.9; // Northern border
  const minLng = 33.0; // Western border
  const maxLng = 48.0; // Eastern border

  // Generate random coordinates within the bounding box
  const lat = minLat + Math.random() * (maxLat - minLat);
  const lng = minLng + Math.random() * (maxLng - minLng);

  return {
    latitude: lat.toFixed(4),
    longitude: lng.toFixed(4)
  };
};

// Helper function to get a random date within a range
const getRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString().split('T')[0];
};

// Helper function to get a random item from an array
const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

// Helper function to get a random capacity from the standard EEU distribution transformer sizes
const getRandomEEUCapacity = (): string => {
  // Standard EEU distribution transformer capacities (kVA)
  const standardCapacities = ["25", "50", "100", "160", "200", "250", "315", "400", "500", "630", "800", "1000", "1250"];
  return getRandomItem(standardCapacities);
};

// Helper function to get a random voltage rating for EEU transformers
const getRandomEEUVoltageRating = (): string => {
  // EEU uses primarily 15kV and 33kV for primary voltage, with 0.4kV secondary
  const primaryVoltages = ["15", "33"];
  const primaryVoltage = getRandomItem(primaryVoltages);
  return `${primaryVoltage}/0.4 kV`;
};

// Helper function to get a random EEU transformer type
const getRandomEEUTransformerType = (): string => {
  const types = ["Distribution", "Pole Mounted", "Pad Mounted", "Ground Mounted"];
  return getRandomItem(types);
};

// Helper function to get a random EEU transformer manufacturer
const getRandomEEUManufacturer = (): string => {
  const manufacturers = ["ABB", "Siemens", "Schneider Electric", "General Electric", "GETRA", "Elsewedy Electric", "Lucy Electric", "CG Power", "TBEA", "Hyundai Electric"];
  return getRandomItem(manufacturers);
};

// Import the transformer store
import { transformerStore } from "@/src/services/transformer-store"

// Transformer service class
class TransformerService {
  // Get all transformers
  async getAllTransformers(): Promise<Transformer[]> {
    try {
      // Try to get from cache first
      return await transformerCache.getOrSet('all_transformers', async () => {
        console.log('Cache miss: Loading all transformers from source');
        // Get transformers from the store
        const transformers = await transformerStore.getAllTransformers();

        // Validate and sanitize each transformer
        return transformers.map(transformer => {
          if (!isValidTransformer(transformer)) {
            return sanitizeTransformer(transformer);
          }
          return transformer;
        });
      });
    } catch (error) {
      console.error('Error getting all transformers:', error);
      // Return mock transformers as fallback
      return originalMockTransformers;
    }
  }

  // Get transformer by ID
  async getTransformerById(id: string): Promise<Transformer | null> {
    // Try to get from cache first
    return await transformerCache.getOrSet(`transformer_${id}`, async () => {
      console.log(`Cache miss: Loading transformer ${id} from source`);
      const transformer = transformerStore.getTransformerById(id);

      if (!transformer) return null;

      // Validate and sanitize the transformer
      if (!isValidTransformer(transformer)) {
        return sanitizeTransformer(transformer);
      }

      return transformer;
    });
  }

  // Update transformer status
  async updateTransformerStatus(id: string, status: "Operational" | "Maintenance" | "Critical" | "Burnt" | "Offline"): Promise<Transformer | null> {
    // Update the transformer in the store
    const updatedTransformer = transformerStore.updateTransformerStatus(id, status);

    // Clear cache
    transformerCache.clear('all_transformers');
    transformerCache.clear(`transformer_${id}`);

    return updatedTransformer;
  }

  // Search transformers by query
  async searchTransformers(query: string): Promise<Transformer[]> {
    if (!query) return transformerStore.getAllTransformers();

    const lowerQuery = query.toLowerCase();
    return transformerStore.getAllTransformers().filter(t =>
      t.serialNumber.toLowerCase().includes(lowerQuery) ||
      t.manufacturer.toLowerCase().includes(lowerQuery) ||
      t.model.toLowerCase().includes(lowerQuery) ||
      t.type.toLowerCase().includes(lowerQuery) ||
      t.location.region.toLowerCase().includes(lowerQuery) ||
      (t.location.serviceCenter && t.location.serviceCenter.toLowerCase().includes(lowerQuery))
    );
  }

  // Filter transformers by status
  async filterTransformersByStatus(status: string): Promise<Transformer[]> {
    if (status === 'all') return transformerStore.getAllTransformers();
    return transformerStore.getAllTransformers().filter(t => t.status.toLowerCase() === status.toLowerCase());
  }

  // Filter transformers by region
  async filterTransformersByRegion(region: string): Promise<Transformer[]> {
    if (region === 'all') return transformerStore.getAllTransformers();
    return transformerStore.getAllTransformers().filter(t => t.location.region.toLowerCase() === region.toLowerCase());
  }

  // Filter transformers by manufacturer
  async filterTransformersByManufacturer(manufacturer: string): Promise<Transformer[]> {
    if (manufacturer === 'all') return transformerStore.getAllTransformers();
    return transformerStore.getAllTransformers().filter(t => t.manufacturer.toLowerCase() === manufacturer.toLowerCase());
  }

  // Filter transformers by type
  async filterTransformersByType(type: string): Promise<Transformer[]> {
    if (type === 'all') return transformerStore.getAllTransformers();
    return transformerStore.getAllTransformers().filter(t => t.type.toLowerCase() === type.toLowerCase());
  }

  // Filter transformers by service center
  async filterTransformersByServiceCenter(serviceCenter: string): Promise<Transformer[]> {
    if (serviceCenter === 'all') return transformerStore.getAllTransformers();
    return transformerStore.getAllTransformers().filter(t =>
      t.location.serviceCenter &&
      t.location.serviceCenter.toLowerCase() === serviceCenter.toLowerCase()
    );
  }

  // Filter transformers by voltage level
  async filterTransformersByVoltageLevel(voltageLevel: string): Promise<Transformer[]> {
    if (voltageLevel === 'all') return transformerStore.getAllTransformers();
    return transformerStore.getAllTransformers().filter(t =>
      t.voltageRating &&
      t.voltageRating.toLowerCase().includes(voltageLevel.toLowerCase())
    );
  }

  // Filter transformers by manufacturing year
  async filterTransformersByManufacturingYear(year: string): Promise<Transformer[]> {
    if (year === 'all') return transformerStore.getAllTransformers();
    return transformerStore.getAllTransformers().filter(t =>
      t.manufacturingYear &&
      t.manufacturingYear === year
    );
  }

  // Filter transformers by multiple criteria
  async filterTransformers(filters: TransformerFilter): Promise<Transformer[]> {
    // Use the transformer store's filter method
    return transformerStore.filterTransformers(filters);
  }

  // Add a new transformer
  async addTransformer(transformer: Omit<Transformer, "id">): Promise<Transformer> {
    const allTransformers = transformerStore.getAllTransformers();
    const newId = `tr-${String(allTransformers.length + 1).padStart(3, '0')}`;
    const newTransformer = { ...transformer, id: newId } as Transformer;

    // Add to the store
    transformerStore.updateTransformer(newId, newTransformer);

    // Clear cache
    transformerCache.clear('all_transformers');

    return newTransformer;
  }

  // Update a transformer
  async updateTransformer(id: string, updates: Partial<Transformer>): Promise<Transformer | null> {
    // Update in the store
    const updatedTransformer = transformerStore.updateTransformer(id, updates);

    // Clear cache
    transformerCache.clear('all_transformers');
    transformerCache.clear(`transformer_${id}`);

    return updatedTransformer;
  }

  // Delete a transformer
  async deleteTransformer(id: string): Promise<boolean> {
    const transformer = transformerStore.getTransformerById(id);
    if (!transformer) return false;

    // Update status to "Offline" instead of actually removing
    transformerStore.updateTransformerStatus(id, "Offline");

    // Clear cache
    transformerCache.clear('all_transformers');
    transformerCache.clear(`transformer_${id}`);

    return true;
  }

  // Get transformer statistics
  async getTransformerStatistics(): Promise<TransformerStatistics> {
    const transformers = await this.getAllTransformers();

    // Initialize statistics object
    const statistics: TransformerStatistics = {
      total: transformers.length,
      byStatus: {},
      byRegion: {},
      byManufacturer: {},
      byType: {},
      byVoltageLevel: {},
      byYear: {},
      byServiceCenter: {},
      // Enhanced dashboard metrics
      operational: transformers.filter(t => t.status === 'Operational').length,
      critical: transformers.filter(t => t.status === 'Critical').length,
      maintenance: transformers.filter(t => t.status === 'Maintenance').length,
      offline: transformers.filter(t => t.status === 'Offline').length,
      burnt: transformers.filter(t => t.status === 'Burnt').length,
      // Performance metrics
      averageAge: this.calculateAverageAge(transformers),
      totalCapacity: this.calculateTotalCapacity(transformers),
      utilizationRate: this.calculateUtilizationRate(transformers),
      reliabilityScore: this.calculateReliabilityScore(transformers),
      // Maintenance metrics
      maintenanceStats: {
        scheduled: this.getScheduledMaintenanceCount(transformers),
        overdue: this.getOverdueMaintenanceCount(transformers),
        completed: this.getCompletedMaintenanceCount(transformers),
        pending: this.getPendingMaintenanceCount(transformers)
      },
      // Alert metrics
      alerts: {
        critical: transformers.filter(t => t.status === 'Critical').length,
        warning: transformers.filter(t => this.hasWarningConditions(t)).length,
        info: transformers.filter(t => this.hasInfoConditions(t)).length,
        total: transformers.filter(t => t.status === 'Critical' || this.hasWarningConditions(t) || this.hasInfoConditions(t)).length
      },
      // Regional performance
      regionalPerformance: this.calculateRegionalPerformance(transformers),
      // Trend data (last 12 months)
      trends: {
        monthly: this.calculateMonthlyTrends(transformers),
        failures: this.calculateFailureTrends(transformers),
        maintenance: this.calculateMaintenanceTrends(transformers)
      },
      abnormalityReports: {
        total: 0,
        byStatus: {},
        byPriority: {},
        byServiceCenter: {},
        byRegion: {}
      },
      meggerTests: {
        total: 0,
        byResult: {},
        byRecommendation: {},
        bySwitchgearTeam: {},
        byRegion: {}
      },
      burntTransformers: {
        total: 0,
        byRegion: {},
        byManufacturer: {},
        byType: {},
        byVoltageLevel: {},
        byYear: {},
        byServiceCenter: {},
        byFailureCause: {},
        byVerificationMethod: {},
        byWorkshopReferral: {
          referred: 0,
          notReferred: 0
        },
        byMeggerTestResult: {},
        byReplacementStatus: {
          replaced: 0,
          notReplaced: 0
        },
        byAbnormalityReportPriority: {}
      }
    };

    // Calculate statistics
    transformers.forEach(transformer => {
      // By status
      const status = transformer.status;
      statistics.byStatus[status] = (statistics.byStatus[status] || 0) + 1;

      // By region
      const region = transformer.location.region;
      statistics.byRegion[region] = (statistics.byRegion[region] || 0) + 1;

      // By manufacturer
      const manufacturer = transformer.manufacturer;
      statistics.byManufacturer[manufacturer] = (statistics.byManufacturer[manufacturer] || 0) + 1;

      // By type
      const type = transformer.type;
      statistics.byType[type] = (statistics.byType[type] || 0) + 1;

      // By voltage level
      if (transformer.voltageRating) {
        const voltageLevel = transformer.voltageRating;
        statistics.byVoltageLevel[voltageLevel] = (statistics.byVoltageLevel[voltageLevel] || 0) + 1;
      }

      // By manufacturing year
      if (transformer.manufacturingYear) {
        const year = transformer.manufacturingYear;
        statistics.byYear[year] = (statistics.byYear[year] || 0) + 1;
      }

      // By service center
      if (transformer.location.serviceCenter) {
        const serviceCenter = transformer.location.serviceCenter;
        statistics.byServiceCenter[serviceCenter] = (statistics.byServiceCenter[serviceCenter] || 0) + 1;
      }

      // Abnormality reports statistics
      if (transformer.abnormalityReports && transformer.abnormalityReports.length > 0) {
        statistics.abnormalityReports.total += transformer.abnormalityReports.length;

        transformer.abnormalityReports.forEach(report => {
          // By status
          const reportStatus = report.status;
          statistics.abnormalityReports.byStatus[reportStatus] = (statistics.abnormalityReports.byStatus[reportStatus] || 0) + 1;

          // By priority
          const priority = report.priority;
          statistics.abnormalityReports.byPriority[priority] = (statistics.abnormalityReports.byPriority[priority] || 0) + 1;

          // By service center
          const serviceCenter = mockServiceCenters.find(sc => sc.id === report.serviceCenterId)?.name || report.serviceCenterId;
          statistics.abnormalityReports.byServiceCenter[serviceCenter] = (statistics.abnormalityReports.byServiceCenter[serviceCenter] || 0) + 1;

          // By region
          const reportRegion = mockServiceCenters.find(sc => sc.id === report.serviceCenterId)?.region || region;
          statistics.abnormalityReports.byRegion[reportRegion] = (statistics.abnormalityReports.byRegion[reportRegion] || 0) + 1;
        });
      }

      // Megger tests statistics
      if (transformer.meggerTests && transformer.meggerTests.length > 0) {
        statistics.meggerTests.total += transformer.meggerTests.length;

        transformer.meggerTests.forEach(test => {
          // By result
          const result = test.result;
          statistics.meggerTests.byResult[result] = (statistics.meggerTests.byResult[result] || 0) + 1;

          // By recommendation
          const recommendation = test.recommendation;
          statistics.meggerTests.byRecommendation[recommendation] = (statistics.meggerTests.byRecommendation[recommendation] || 0) + 1;

          // By switchgear team
          const switchgearTeam = mockSwitchgearTeams.find(team => team.id === test.switchgearTeamId)?.name || test.switchgearTeamId;
          statistics.meggerTests.bySwitchgearTeam[switchgearTeam] = (statistics.meggerTests.bySwitchgearTeam[switchgearTeam] || 0) + 1;

          // By region
          const testRegion = mockSwitchgearTeams.find(team => team.id === test.switchgearTeamId)?.region || region;
          statistics.meggerTests.byRegion[testRegion] = (statistics.meggerTests.byRegion[testRegion] || 0) + 1;
        });
      }

      // Burnt transformers statistics
      if (transformer.status === 'Burnt') {
        statistics.burntTransformers.total += 1;

        // By region
        statistics.burntTransformers.byRegion[region] = (statistics.burntTransformers.byRegion[region] || 0) + 1;

        // By manufacturer
        statistics.burntTransformers.byManufacturer[manufacturer] = (statistics.burntTransformers.byManufacturer[manufacturer] || 0) + 1;

        // By type
        statistics.burntTransformers.byType[type] = (statistics.burntTransformers.byType[type] || 0) + 1;

        // By voltage level
        if (transformer.voltageRating) {
          const voltageLevel = transformer.voltageRating;
          statistics.burntTransformers.byVoltageLevel[voltageLevel] = (statistics.burntTransformers.byVoltageLevel[voltageLevel] || 0) + 1;
        }

        // By manufacturing year
        if (transformer.manufacturingYear) {
          const year = transformer.manufacturingYear;
          statistics.burntTransformers.byYear[year] = (statistics.burntTransformers.byYear[year] || 0) + 1;
        }

        // By service center
        if (transformer.location.serviceCenter) {
          const serviceCenter = transformer.location.serviceCenter;
          statistics.burntTransformers.byServiceCenter[serviceCenter] = (statistics.burntTransformers.byServiceCenter[serviceCenter] || 0) + 1;
        }

        // By failure cause
        if (transformer.failureCause) {
          const failureCause = transformer.failureCause;
          statistics.burntTransformers.byFailureCause[failureCause] = (statistics.burntTransformers.byFailureCause[failureCause] || 0) + 1;
        }

        // By verification method
        if (transformer.failureVerificationMethod) {
          const verificationMethod = transformer.failureVerificationMethod;
          statistics.burntTransformers.byVerificationMethod[verificationMethod] =
            (statistics.burntTransformers.byVerificationMethod[verificationMethod] || 0) + 1;
        }

        // By workshop referral
        if (transformer.workshopReferral !== undefined) {
          if (transformer.workshopReferral) {
            statistics.burntTransformers.byWorkshopReferral.referred += 1;
          } else {
            statistics.burntTransformers.byWorkshopReferral.notReferred += 1;
          }
        }

        // By replacement status
        if (transformer.replacementDate) {
          statistics.burntTransformers.byReplacementStatus.replaced += 1;
        } else {
          statistics.burntTransformers.byReplacementStatus.notReplaced += 1;
        }

        // By megger test result
        if (transformer.meggerTests && transformer.meggerTests.length > 0) {
          const latestTest = transformer.meggerTests[transformer.meggerTests.length - 1];
          const testResult = latestTest.result;
          statistics.burntTransformers.byMeggerTestResult[testResult] =
            (statistics.burntTransformers.byMeggerTestResult[testResult] || 0) + 1;
        }

        // By abnormality report priority
        if (transformer.abnormalityReports && transformer.abnormalityReports.length > 0) {
          const latestReport = transformer.abnormalityReports[transformer.abnormalityReports.length - 1];
          const priority = latestReport.priority;
          statistics.burntTransformers.byAbnormalityReportPriority[priority] =
            (statistics.burntTransformers.byAbnormalityReportPriority[priority] || 0) + 1;
        }
      }
    });

    return statistics;
  }

  // Get burnt transformers
  async getBurntTransformers(): Promise<Transformer[]> {
    return mockTransformers.filter(t => t.status === 'Burnt');
  }

  // Get burnt transformers by region
  async getBurntTransformersByRegion(region: string): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.location.region.toLowerCase() === region.toLowerCase()
    );
  }

  // Get burnt transformers by manufacturer
  async getBurntTransformersByManufacturer(manufacturer: string): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.manufacturer.toLowerCase() === manufacturer.toLowerCase()
    );
  }

  // Get burnt transformers by type
  async getBurntTransformersByType(type: string): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.type.toLowerCase() === type.toLowerCase()
    );
  }

  // Get burnt transformers by voltage level
  async getBurntTransformersByVoltageLevel(voltageLevel: string): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.voltageRating &&
      t.voltageRating.toLowerCase().includes(voltageLevel.toLowerCase())
    );
  }

  // Get burnt transformers by manufacturing year
  async getBurntTransformersByManufacturingYear(year: string): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.manufacturingYear &&
      t.manufacturingYear === year
    );
  }

  // Get burnt transformers by service center
  async getBurntTransformersByServiceCenter(serviceCenter: string): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.location.serviceCenter &&
      t.location.serviceCenter.toLowerCase() === serviceCenter.toLowerCase()
    );
  }

  // Get burnt transformers by failure cause
  async getBurntTransformersByFailureCause(failureCause: string): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.failureCause &&
      t.failureCause.toLowerCase().includes(failureCause.toLowerCase())
    );
  }

  // Get burnt transformers by verification method
  async getBurntTransformersByVerificationMethod(method: string): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.failureVerificationMethod &&
      t.failureVerificationMethod.toLowerCase().includes(method.toLowerCase())
    );
  }

  // Get burnt transformers by workshop referral status
  async getBurntTransformersByWorkshopReferral(referred: boolean): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.workshopReferral === referred
    );
  }

  // Get burnt transformers by replacement status
  async getBurntTransformersByReplacementStatus(replaced: boolean): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      (replaced ? !!t.replacementDate : !t.replacementDate)
    );
  }

  // Get burnt transformers by megger test result
  async getBurntTransformersByMeggerTestResult(result: string): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.meggerTests &&
      t.meggerTests.length > 0 &&
      t.meggerTests[t.meggerTests.length - 1].result.toLowerCase() === result.toLowerCase()
    );
  }

  // Get burnt transformers by abnormality report priority
  async getBurntTransformersByAbnormalityReportPriority(priority: string): Promise<Transformer[]> {
    return mockTransformers.filter(t =>
      t.status === 'Burnt' &&
      t.abnormalityReports &&
      t.abnormalityReports.length > 0 &&
      t.abnormalityReports[t.abnormalityReports.length - 1].priority.toLowerCase() === priority.toLowerCase()
    );
  }

  // Get all abnormality reports
  async getAllAbnormalityReports(): Promise<AbnormalityReport[]> {
    const reports: AbnormalityReport[] = [];
    mockTransformers.forEach(transformer => {
      if (transformer.abnormalityReports && transformer.abnormalityReports.length > 0) {
        transformer.abnormalityReports.forEach(report => {
          reports.push({
            ...report,
            // Add transformer information for context
            transformerId: transformer.id,
            transformerSerialNumber: transformer.serialNumber,
            transformerLocation: transformer.location.region
          } as any);
        });
      }
    });
    return reports;
  }

  // Get all megger tests
  async getAllMeggerTests(): Promise<any[]> {
    const tests: any[] = [];
    mockTransformers.forEach(transformer => {
      if (transformer.meggerTests && transformer.meggerTests.length > 0) {
        transformer.meggerTests.forEach(test => {
          tests.push({
            ...test,
            // Add transformer information for context
            transformerId: transformer.id,
            transformerSerialNumber: transformer.serialNumber,
            transformerLocation: transformer.location.region
          });
        });
      }
    });
    return tests;
  }

  // Get service centers
  async getServiceCenters(): Promise<any[]> {
    return [...mockServiceCenters];
  }

  // Get switchgear teams
  async getSwitchgearTeams(): Promise<any[]> {
    return [...mockSwitchgearTeams];
  }

  // Helper methods for enhanced statistics
  private calculateAverageAge(transformers: Transformer[]): number {
    const currentYear = new Date().getFullYear();
    const ages = transformers
      .filter(t => t.manufacturingYear)
      .map(t => currentYear - parseInt(t.manufacturingYear!));
    return ages.length > 0 ? Math.round(ages.reduce((sum, age) => sum + age, 0) / ages.length) : 0;
  }

  private calculateTotalCapacity(transformers: Transformer[]): number {
    return transformers.reduce((total, t) => total + parseInt(t.capacity), 0);
  }

  private calculateUtilizationRate(transformers: Transformer[]): number {
    const operational = transformers.filter(t => t.status === 'Operational').length;
    return transformers.length > 0 ? Math.round((operational / transformers.length) * 100) : 0;
  }

  private calculateReliabilityScore(transformers: Transformer[]): number {
    const operational = transformers.filter(t => t.status === 'Operational').length;
    const critical = transformers.filter(t => t.status === 'Critical').length;
    const burnt = transformers.filter(t => t.status === 'Burnt').length;

    if (transformers.length === 0) return 0;

    const score = ((operational * 1.0 + (transformers.length - operational - critical - burnt) * 0.7 - critical * 0.3 - burnt * 0.5) / transformers.length) * 100;
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private getScheduledMaintenanceCount(transformers: Transformer[]): number {
    return transformers.filter(t => t.status === 'Maintenance').length;
  }

  private getOverdueMaintenanceCount(transformers: Transformer[]): number {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    return transformers.filter(t =>
      t.lastMaintenanceDate && new Date(t.lastMaintenanceDate) < sixMonthsAgo
    ).length;
  }

  private getCompletedMaintenanceCount(transformers: Transformer[]): number {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    return transformers.filter(t =>
      t.lastMaintenanceDate && new Date(t.lastMaintenanceDate) >= oneMonthAgo
    ).length;
  }

  private getPendingMaintenanceCount(transformers: Transformer[]): number {
    return Math.max(0, this.getOverdueMaintenanceCount(transformers) - this.getScheduledMaintenanceCount(transformers));
  }

  private hasWarningConditions(transformer: Transformer): boolean {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    return (transformer.lastMaintenanceDate && new Date(transformer.lastMaintenanceDate) < sixMonthsAgo) ||
           (transformer.abnormalityReports && transformer.abnormalityReports.some(r => r.priority === 'Medium'));
  }

  private hasInfoConditions(transformer: Transformer): boolean {
    return transformer.abnormalityReports && transformer.abnormalityReports.some(r => r.priority === 'Low') || false;
  }

  private calculateRegionalPerformance(transformers: Transformer[]): Record<string, any> {
    const regions: Record<string, any> = {};

    transformers.forEach(t => {
      const region = t.location.region;
      if (!regions[region]) {
        regions[region] = {
          total: 0,
          operational: 0,
          critical: 0,
          maintenance: 0,
          reliability: 0
        };
      }

      regions[region].total++;
      if (t.status === 'Operational') regions[region].operational++;
      if (t.status === 'Critical') regions[region].critical++;
      if (t.status === 'Maintenance') regions[region].maintenance++;
    });

    // Calculate reliability for each region
    Object.keys(regions).forEach(region => {
      const data = regions[region];
      data.reliability = data.total > 0 ? Math.round((data.operational / data.total) * 100) : 0;
    });

    return regions;
  }

  private calculateMonthlyTrends(transformers: Transformer[]): any[] {
    const months = [];
    const currentDate = new Date();

    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthName = date.toLocaleString('default', { month: 'short' });

      months.push({
        month: monthName,
        operational: Math.floor(Math.random() * 20) + 80, // Mock data
        maintenance: Math.floor(Math.random() * 10) + 5,
        critical: Math.floor(Math.random() * 5) + 2
      });
    }

    return months;
  }

  private calculateFailureTrends(transformers: Transformer[]): any[] {
    const months = [];
    const currentDate = new Date();

    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthName = date.toLocaleString('default', { month: 'short' });

      months.push({
        month: monthName,
        failures: Math.floor(Math.random() * 3) + 1, // Mock data
        repairs: Math.floor(Math.random() * 5) + 2
      });
    }

    return months;
  }

  private calculateMaintenanceTrends(transformers: Transformer[]): any[] {
    const months = [];
    const currentDate = new Date();

    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthName = date.toLocaleString('default', { month: 'short' });

      months.push({
        month: monthName,
        scheduled: Math.floor(Math.random() * 15) + 10,
        completed: Math.floor(Math.random() * 12) + 8,
        overdue: Math.floor(Math.random() * 3) + 1
      });
    }

    return months;
  }

  // Generate random EEU transformers
  async generateRandomEEUTransformers(count: number = 10): Promise<Transformer[]> {
    const newTransformers: Transformer[] = [];

    for (let i = 0; i < count; i++) {
      const id = `tr-${String(mockTransformers.length + i + 1).padStart(3, '0')}`;
      const serialNumber = `ETH-TR-${10000 + mockTransformers.length + i}`;
      const manufacturer = getRandomEEUManufacturer();
      const capacity = getRandomEEUCapacity();
      const voltageRating = getRandomEEUVoltageRating();
      const type = getRandomEEUTransformerType();
      const primaryVoltage = voltageRating.split('/')[0] + ' kV';
      const coords = generateRandomEthiopianCoordinates();
      const region = getRandomItem(mockServiceCenters.map(sc => sc.region));
      const serviceCenters = mockServiceCenters.filter(sc => sc.region === region);
      const serviceCenter = serviceCenters.length > 0 ? getRandomItem(serviceCenters).name : "";

      const installationYear = 2018 + Math.floor(Math.random() * 5); // 2018-2022
      const manufacturingYear = (installationYear - 1).toString();
      const installationDate = getRandomDate(new Date(installationYear, 0, 1), new Date(installationYear, 11, 31));

      const maintenanceDate = getRandomDate(new Date(installationYear + 1, 0, 1), new Date(2023, 5, 30));

      const transformer: Transformer = {
        id,
        serialNumber,
        manufacturer,
        model: `DTCF-${capacity}`,
        type,
        capacity,
        voltageRating,
        installationDate,
        lastMaintenanceDate: maintenanceDate,
        lastInspectionDate: maintenanceDate,
        status: getRandomItem(["Operational", "Maintenance", "Critical", "Burnt", "Offline"]),
        location: {
          latitude: coords.latitude,
          longitude: coords.longitude,
          region,
          serviceCenter,
          address: `${serviceCenter}, ${region}`,
          installationSite: getRandomItem([
            "Residential Area",
            "Commercial Building",
            "Industrial Zone",
            "Hospital",
            "School",
            "University Campus",
            "Shopping Mall",
            "Office Complex"
          ])
        },
        manufacturingYear,
        primaryVoltage,
        secondaryVoltage: "0.4 kV",
        connectionType: "Dyn11",
        phaseCount: "3",
        impedance: (4 + Math.random()).toFixed(2) + "%",
        oilType: "Mineral Oil",
        coolingType: "ONAN",
        weight: (parseInt(capacity) * 3 + 200).toString() + " kg",
        dimensions: `${800 + parseInt(capacity)/2}x${600 + parseInt(capacity)/4}x${1000 + parseInt(capacity)/3} mm`,
        temperatureRise: "55°C",
        noiseLevel: (45 + parseInt(capacity)/20).toFixed(0) + " dB",
        standardCompliance: ["IEC 60076", "ES IEC 60076-1:2011", "ES IEC 60076-2:2011"],
        feederName: `${serviceCenter} Feeder ${Math.floor(Math.random() * 5) + 1}`,
        substationName: `${serviceCenter} Substation`
      };

      newTransformers.push(transformer);
      mockTransformers.push(transformer);
    }

    return newTransformers;
  }
}

// Export singleton instance
export const transformerService = new TransformerService();
