import { NextRequest, NextResponse } from 'next/server'
import { seedBypassTables } from '../../../src/lib/db/seed-bypass-tables'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API: Seeding bypass tables requested...')
    
    // Seed the bypass tables with Ethiopian data
    console.log('🔄 Seeding bypass tables with Ethiopian data...')
    const seedResult = await seedBypassTables()
    
    if (!seedResult.success) {
      console.error('❌ API: Bypass table seeding failed:', seedResult.message)
      return NextResponse.json({
        success: false,
        message: seedResult.message,
        error: seedResult.error,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    console.log('✅ API: Bypass tables seeded successfully')

    return NextResponse.json({
      success: true,
      message: 'Bypass tables seeded successfully with Ethiopian transformer data',
      seedSuccess: true,
      seededData: seedResult.seededData,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ API: Bypass table seeding failed:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Bypass table seeding failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Bypass table seeding API is ready. Use POST to seed the bypass tables.',
    timestamp: new Date().toISOString()
  })
}
