/**
 * Database service
 *
 * This service provides a high-level API for interacting with the database.
 * It uses the repositories to perform operations on the database.
 */

import { initializeDatabase } from './init-db';
import { getDbMetadata, backupDb, ensureDbExists } from './db-utils';
import { synchronizeData, getLastSyncTimestamp } from './db-sync';
import {
  userRepository,
  regionRepository,
  serviceCenterRepository,
  transformerRepository,
  maintenanceRepository,
  alertRepository,
  outageRepository,
  weatherAlertRepository
} from './repositories';

// Ensure the database exists when this module is loaded
ensureDbExists();

/**
 * Database service class
 */
export class DatabaseService {
  // Repositories
  users = userRepository;
  regions = regionRepository;
  serviceCenters = serviceCenterRepository;
  transformers = transformerRepository;
  maintenance = maintenanceRepository;
  alerts = alertRepository;
  outages = outageRepository;
  weatherAlerts = weatherAlertRepository;

  /**
   * Initialize the database
   */
  async initialize(force: boolean = false): Promise<void> {
    try {
      console.log('Initializing in-memory database...');
      await initializeDatabase(force);
      console.log('In-memory database initialized successfully');
    } catch (error) {
      console.error('Error initializing database:', error);
    }
  }

  /**
   * Get database metadata
   */
  getMetadata() {
    return getDbMetadata();
  }

  /**
   * Create a backup of the database
   */
  createBackup(): string {
    return backupDb();
  }

  /**
   * Synchronize data between client and server
   */
  async syncData(): Promise<void> {
    await synchronizeData();
  }

  /**
   * Get the last synchronization timestamp
   */
  getLastSyncTime(): string | null {
    return getLastSyncTimestamp();
  }

  /**
   * Get dashboard statistics
   */
  getDashboardStatistics(region: string = 'all') {
    // Get transformer statistics
    const transformerStats = transformerRepository.getStatistics();

    // Get alert statistics
    const alertStats = alertRepository.getStatistics();

    // Get maintenance statistics
    const maintenanceStats = maintenanceRepository.getStatistics();

    // Get outage statistics
    const outageStats = outageRepository.getStatistics();

    // Get weather alert statistics
    const weatherAlertStats = weatherAlertRepository.getStatistics();

    // Get regional statistics
    const regionalStats = regionRepository.getAll().map(regionEntity => {
      // Find transformers in this region
      const regionTransformers = transformerRepository.findByRegion(regionEntity.id);

      // Calculate percentages
      const totalTransformers = regionTransformers.length;
      const healthyCount = regionTransformers.filter(t => t.status === 'operational').length;
      const warningCount = regionTransformers.filter(t => t.status === 'warning').length;
      const criticalCount = regionTransformers.filter(t => t.status === 'critical').length;
      const offlineCount = regionTransformers.filter(t => t.status === 'offline' || t.status === 'maintenance').length;

      const healthyPercentage = totalTransformers > 0 ? Math.round((healthyCount / totalTransformers) * 100) : 0;
      const warningPercentage = totalTransformers > 0 ? Math.round((warningCount / totalTransformers) * 100) : 0;
      const criticalPercentage = totalTransformers > 0 ? Math.round((criticalCount / totalTransformers) * 100) : 0;
      const offlinePercentage = totalTransformers > 0 ? Math.round((offlineCount / totalTransformers) * 100) : 0;

      return {
        region: regionEntity.name,
        code: regionEntity.code,
        totalTransformers,
        healthyPercentage,
        warningPercentage,
        criticalPercentage,
        offlinePercentage
      };
    });

    // Filter by region if specified
    let filteredRegionalStats = regionalStats;
    let filteredTransformerStats = { ...transformerStats };
    let filteredAlertStats = { ...alertStats };
    let filteredMaintenanceStats = { ...maintenanceStats };
    let filteredOutageStats = { ...outageStats };

    if (region !== 'all') {
      // Find the region entity
      const regionEntity = regionRepository.findByCode(region) ||
                          regionRepository.findByName(region);

      if (regionEntity) {
        // Filter regional stats
        filteredRegionalStats = regionalStats.filter(stats =>
          stats.code === regionEntity.code ||
          stats.region.toLowerCase() === regionEntity.name.toLowerCase()
        );

        // Filter transformer stats
        const regionTransformers = transformerRepository.findByRegion(regionEntity.id);
        const regionTransformerIds = regionTransformers.map(t => t.id);

        // Recalculate transformer stats for this region
        const statusCounts = regionTransformers.reduce((counts, transformer) => {
          counts[transformer.status] = (counts[transformer.status] || 0) + 1;
          return counts;
        }, {} as Record<string, number>);

        filteredTransformerStats = {
          total: regionTransformers.length,
          byStatus: statusCounts,
          byRegion: { [regionEntity.id]: regionTransformers.length },
          byCapacity: transformerStats.byCapacity, // Keep original for now
          averageHealthIndex: regionTransformers.reduce(
            (sum, transformer) => sum + transformer.metrics.healthIndex,
            0
          ) / (regionTransformers.length || 1)
        };

        // Filter alerts by transformers in this region
        const regionAlerts = alertRepository.getAll().filter(alert =>
          regionTransformerIds.includes(alert.transformerId)
        );

        // Filter maintenance records by transformers in this region
        const regionMaintenance = maintenanceRepository.getAll().filter(record =>
          regionTransformerIds.includes(record.transformerId)
        );

        // Filter outages by transformers in this region
        const regionOutages = outageRepository.getAll().filter(outage =>
          regionTransformerIds.includes(outage.transformerId)
        );

        // Recalculate alert stats for this region
        filteredAlertStats.total = regionAlerts.length;
        filteredAlertStats.unresolved = regionAlerts.filter(a => !a.isResolved).length;

        // Recalculate maintenance stats for this region
        filteredMaintenanceStats.total = regionMaintenance.length;
        filteredMaintenanceStats.upcoming = regionMaintenance.filter(m =>
          m.status === 'scheduled' && new Date(m.scheduledDate) > new Date()
        ).length;

        // Recalculate outage stats for this region
        filteredOutageStats.total = regionOutages.length;
        filteredOutageStats.active = regionOutages.filter(o => o.status === 'active').length;
      }
    }

    return {
      stats: {
        totalTransformers: transformerStats.total,
        healthyPercentage: Math.round(
          (transformerStats.byStatus?.operational || 0) / (transformerStats.total || 1) * 100
        ),
        activeOutages: outageStats.active,
        criticalAlerts: alertStats.bySeverity?.critical || 0,
        newTransformers: 5, // Placeholder - would need to calculate based on installation date
        healthyChange: 3, // Placeholder - would need historical data
        outageChange: -5, // Placeholder - would need historical data
        alertChange: 2 // Placeholder - would need historical data
      },
      regionalStats: filteredRegionalStats,
      transformerStats: filteredTransformerStats,
      alertStats: filteredAlertStats,
      maintenanceStats: filteredMaintenanceStats,
      outageStats: filteredOutageStats,
      weatherAlertStats
    };
  }

  /**
   * Search across multiple entities
   */
  search(term: string) {
    const searchTerm = term.toLowerCase();

    // Search transformers
    const transformers = transformerRepository.search(searchTerm);

    // Search regions
    const regions = regionRepository.searchByName(searchTerm);

    // Search service centers
    const serviceCenters = serviceCenterRepository.search(searchTerm);

    // Search users
    const users = userRepository.search(searchTerm);

    return {
      transformers,
      regions,
      serviceCenters,
      users,
      totalResults: transformers.length + regions.length + serviceCenters.length + users.length
    };
  }
}

// Export a singleton instance
export const dbService = new DatabaseService();
