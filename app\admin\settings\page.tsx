"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Settings, Database, Users, Shield, Server, Layers } from 'lucide-react'
import MigrationPanel from "../../../components/admin/MigrationPanel"

export default function AdminSettingsPage() {
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('database')
  const router = useRouter()

  useEffect(() => {
    // Check if user is logged in and is an admin
    const storedUser = localStorage.getItem("eeu_user")
    
    if (!storedUser) {
      // Redirect to login if not logged in
      router.push("/login")
      return
    }
    
    try {
      const userData = JSON.parse(storedUser)
      
      // Check if user is an admin
      if (userData.role !== 'super_admin') {
        // Redirect to dashboard if not an admin
        router.push("/dashboard")
        return
      }
      
      setUser(userData)
    } catch (error) {
      console.error("Error parsing user data:", error)
      // Redirect to login if user data is invalid
      router.push("/login")
    } finally {
      setIsLoading(false)
    }
  }, [router])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    )
  }

  if (!user) {
    return <div>Redirecting to login...</div>
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center mb-6">
        <Settings className="text-green-600 mr-2" size={24} />
        <h1 className="text-2xl font-bold">Admin Settings</h1>
      </div>
      
      <div className="grid grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="col-span-1">
          <div className="bg-white rounded-lg border overflow-hidden">
            <div className="p-4 border-b bg-gray-50">
              <h2 className="font-medium">Settings</h2>
            </div>
            <div className="p-2">
              <button
                className={`w-full text-left px-3 py-2 rounded-md flex items-center ${
                  activeTab === 'database' ? 'bg-green-100 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('database')}
              >
                <Database size={16} className="mr-2" />
                <span>Database</span>
              </button>
              
              <button
                className={`w-full text-left px-3 py-2 rounded-md flex items-center ${
                  activeTab === 'users' ? 'bg-green-100 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('users')}
              >
                <Users size={16} className="mr-2" />
                <span>Users</span>
              </button>
              
              <button
                className={`w-full text-left px-3 py-2 rounded-md flex items-center ${
                  activeTab === 'permissions' ? 'bg-green-100 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('permissions')}
              >
                <Shield size={16} className="mr-2" />
                <span>Permissions</span>
              </button>
              
              <button
                className={`w-full text-left px-3 py-2 rounded-md flex items-center ${
                  activeTab === 'system' ? 'bg-green-100 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('system')}
              >
                <Server size={16} className="mr-2" />
                <span>System</span>
              </button>
              
              <button
                className={`w-full text-left px-3 py-2 rounded-md flex items-center ${
                  activeTab === 'features' ? 'bg-green-100 text-green-700' : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('features')}
              >
                <Layers size={16} className="mr-2" />
                <span>Features</span>
              </button>
            </div>
          </div>
        </div>
        
        {/* Main content */}
        <div className="col-span-3">
          {activeTab === 'database' && (
            <div>
              <h2 className="text-xl font-medium mb-4">Database Settings</h2>
              
              <MigrationPanel />
            </div>
          )}
          
          {activeTab === 'users' && (
            <div className="bg-white rounded-lg border p-6">
              <h2 className="text-xl font-medium mb-4">User Management</h2>
              <p className="text-gray-600">
                Manage system users, roles, and permissions.
              </p>
            </div>
          )}
          
          {activeTab === 'permissions' && (
            <div className="bg-white rounded-lg border p-6">
              <h2 className="text-xl font-medium mb-4">Permission Settings</h2>
              <p className="text-gray-600">
                Configure role-based access control and permissions.
              </p>
            </div>
          )}
          
          {activeTab === 'system' && (
            <div className="bg-white rounded-lg border p-6">
              <h2 className="text-xl font-medium mb-4">System Settings</h2>
              <p className="text-gray-600">
                Configure system-wide settings and preferences.
              </p>
            </div>
          )}
          
          {activeTab === 'features' && (
            <div className="bg-white rounded-lg border p-6">
              <h2 className="text-xl font-medium mb-4">Feature Management</h2>
              <p className="text-gray-600">
                Enable or disable system features and modules.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
